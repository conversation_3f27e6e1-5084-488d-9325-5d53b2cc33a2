#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour extraire et convertir les paramètres SQL vers GestImmob
"""

import sqlite3
import json
import os
from pathlib import Path
from datetime import datetime

class ExtracteurParametresSQL:
    def __init__(self):
        self.bureau = Path("C:/Users/<USER>/Desktop")
        self.parametres_extraits = {}
        self.bases_trouvees = []
        
    def rechercher_bases_donnees(self):
        """Recherche toutes les bases de données SQL sur le bureau"""
        print("🔍 RECHERCHE DES BASES DE DONNÉES SQL")
        print("=" * 60)
        
        # Rechercher dans les dossiers COMMUN et DATA2
        dossiers_a_explorer = [
            self.bureau / "COMMUN",
            self.bureau / "DATA2",
            self.bureau  # Bureau directement
        ]
        
        for dossier in dossiers_a_explorer:
            if dossier.exists():
                print(f"📁 Exploration: {dossier}")
                self.explorer_dossier_sql(dossier)
        
        print(f"\n📊 RÉSUMÉ: {len(self.bases_trouvees)} base(s) de données trouvée(s)")
        for base in self.bases_trouvees:
            print(f"  🗃️ {base}")
    
    def explorer_dossier_sql(self, dossier):
        """Explore un dossier à la recherche de fichiers SQL"""
        try:
            for fichier in dossier.rglob("*"):
                if fichier.is_file():
                    # Rechercher les fichiers de base de données
                    if fichier.suffix.lower() in ['.db', '.sqlite', '.sqlite3', '.sql']:
                        print(f"  ✅ Base trouvée: {fichier.name}")
                        self.bases_trouvees.append(fichier)
                    
                    # Rechercher aussi les fichiers avec des noms suspects
                    elif any(mot in fichier.name.lower() for mot in ['database', 'data', 'config', 'param']):
                        if fichier.suffix.lower() in ['.db3', '.dat', '.mdb']:
                            print(f"  🎯 Fichier suspect: {fichier.name}")
                            self.bases_trouvees.append(fichier)
        
        except PermissionError:
            print(f"  ❌ Accès refusé: {dossier}")
        except Exception as e:
            print(f"  ❌ Erreur: {e}")
    
    def extraire_parametres_base(self, fichier_db):
        """Extrait les paramètres d'une base de données"""
        print(f"\n🗃️ EXTRACTION: {fichier_db.name}")
        print("-" * 50)
        
        try:
            conn = sqlite3.connect(fichier_db)
            cursor = conn.cursor()
            
            # Lister toutes les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"📊 {len(tables)} table(s) trouvée(s):")
            
            parametres_db = {
                'nom_base': fichier_db.name,
                'chemin': str(fichier_db),
                'tables': {},
                'parametres': {},
                'secteurs': {},
                'configurations': {}
            }
            
            for table in tables:
                table_name = table[0]
                print(f"  📋 Table: {table_name}")
                
                # Analyser la structure de la table
                cursor.execute(f"PRAGMA table_info({table_name})")
                colonnes = cursor.fetchall()
                
                # Compter les enregistrements
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                nb_records = cursor.fetchone()[0]
                
                print(f"    📈 {len(colonnes)} colonnes, {nb_records} enregistrements")
                
                # Stocker la structure
                parametres_db['tables'][table_name] = {
                    'colonnes': [{'nom': col[1], 'type': col[2], 'not_null': col[3], 'default': col[4]} for col in colonnes],
                    'nb_records': nb_records
                }
                
                # Extraire les données selon le type de table
                if any(mot in table_name.lower() for mot in ['param', 'config', 'setting']):
                    print(f"    🎯 Table de paramètres détectée")
                    self.extraire_table_parametres(cursor, table_name, parametres_db)
                
                elif any(mot in table_name.lower() for mot in ['secteur', 'category', 'type']):
                    print(f"    🎯 Table de secteurs détectée")
                    self.extraire_table_secteurs(cursor, table_name, parametres_db)
                
                elif any(mot in table_name.lower() for mot in ['immob', 'bien', 'asset', 'materiel']):
                    print(f"    🎯 Table d'immobilisations détectée")
                    self.extraire_table_immobilisations(cursor, table_name, parametres_db)
                
                elif any(mot in table_name.lower() for mot in ['user', 'utilisateur', 'login']):
                    print(f"    🎯 Table d'utilisateurs détectée")
                    self.extraire_table_utilisateurs(cursor, table_name, parametres_db)
                
                else:
                    # Table générique - extraire quelques exemples
                    if nb_records > 0:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                        exemples = cursor.fetchall()
                        parametres_db['tables'][table_name]['exemples'] = exemples
            
            conn.close()
            
            # Stocker les paramètres extraits
            self.parametres_extraits[fichier_db.name] = parametres_db
            
            return parametres_db
            
        except Exception as e:
            print(f"❌ Erreur extraction {fichier_db.name}: {e}")
            return None
    
    def extraire_table_parametres(self, cursor, table_name, parametres_db):
        """Extrait une table de paramètres"""
        try:
            cursor.execute(f"SELECT * FROM {table_name}")
            records = cursor.fetchall()
            
            # Obtenir les noms des colonnes
            cursor.execute(f"PRAGMA table_info({table_name})")
            colonnes = [col[1] for col in cursor.fetchall()]
            
            parametres = {}
            for record in records:
                # Créer un dictionnaire pour chaque enregistrement
                record_dict = dict(zip(colonnes, record))
                
                # Essayer de trouver une clé et une valeur
                if 'nom' in record_dict and 'valeur' in record_dict:
                    parametres[record_dict['nom']] = record_dict['valeur']
                elif 'key' in record_dict and 'value' in record_dict:
                    parametres[record_dict['key']] = record_dict['value']
                elif 'parametre' in record_dict and 'valeur' in record_dict:
                    parametres[record_dict['parametre']] = record_dict['valeur']
                else:
                    # Utiliser le premier champ comme clé et le second comme valeur
                    if len(record) >= 2:
                        parametres[str(record[0])] = record[1]
            
            parametres_db['parametres'][table_name] = parametres
            print(f"      ✅ {len(parametres)} paramètre(s) extrait(s)")
            
        except Exception as e:
            print(f"      ❌ Erreur extraction paramètres: {e}")
    
    def extraire_table_secteurs(self, cursor, table_name, parametres_db):
        """Extrait une table de secteurs"""
        try:
            cursor.execute(f"SELECT * FROM {table_name}")
            records = cursor.fetchall()
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            colonnes = [col[1] for col in cursor.fetchall()]
            
            secteurs = []
            for record in records:
                secteur_dict = dict(zip(colonnes, record))
                secteurs.append(secteur_dict)
            
            parametres_db['secteurs'][table_name] = secteurs
            print(f"      ✅ {len(secteurs)} secteur(s) extrait(s)")
            
        except Exception as e:
            print(f"      ❌ Erreur extraction secteurs: {e}")
    
    def extraire_table_immobilisations(self, cursor, table_name, parametres_db):
        """Extrait un échantillon de la table d'immobilisations"""
        try:
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 10")
            records = cursor.fetchall()
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            colonnes = [col[1] for col in cursor.fetchall()]
            
            immobilisations = []
            for record in records:
                immob_dict = dict(zip(colonnes, record))
                immobilisations.append(immob_dict)
            
            parametres_db['configurations']['exemple_immobilisations'] = immobilisations
            print(f"      ✅ {len(immobilisations)} exemple(s) d'immobilisation extrait(s)")
            
        except Exception as e:
            print(f"      ❌ Erreur extraction immobilisations: {e}")
    
    def extraire_table_utilisateurs(self, cursor, table_name, parametres_db):
        """Extrait la configuration des utilisateurs (sans mots de passe)"""
        try:
            cursor.execute(f"PRAGMA table_info({table_name})")
            colonnes = [col[1] for col in cursor.fetchall()]
            
            # Sélectionner seulement les colonnes non sensibles
            colonnes_safe = [col for col in colonnes if 'password' not in col.lower() and 'pwd' not in col.lower()]
            
            if colonnes_safe:
                query = f"SELECT {', '.join(colonnes_safe)} FROM {table_name}"
                cursor.execute(query)
                records = cursor.fetchall()
                
                utilisateurs = []
                for record in records:
                    user_dict = dict(zip(colonnes_safe, record))
                    utilisateurs.append(user_dict)
                
                parametres_db['configurations']['utilisateurs'] = utilisateurs
                print(f"      ✅ {len(utilisateurs)} utilisateur(s) extrait(s)")
            
        except Exception as e:
            print(f"      ❌ Erreur extraction utilisateurs: {e}")
    
    def convertir_pour_gestimmob(self):
        """Convertit les paramètres extraits au format GestImmob"""
        print(f"\n🔄 CONVERSION POUR GESTIMMOB")
        print("=" * 60)
        
        config_gestimmob = {
            'version': '2.0.0',
            'date_import': datetime.now().isoformat(),
            'source_databases': list(self.parametres_extraits.keys()),
            'secteurs_personnalises': [],
            'parametres_application': {},
            'utilisateurs': [],
            'exemples_donnees': []
        }
        
        # Convertir les paramètres de chaque base
        for nom_base, donnees in self.parametres_extraits.items():
            print(f"🔄 Conversion: {nom_base}")
            
            # Convertir les paramètres
            if donnees['parametres']:
                for table, params in donnees['parametres'].items():
                    config_gestimmob['parametres_application'][f"{nom_base}_{table}"] = params
                    print(f"  ✅ Paramètres {table}: {len(params)} éléments")
            
            # Convertir les secteurs
            if donnees['secteurs']:
                for table, secteurs in donnees['secteurs'].items():
                    for secteur in secteurs:
                        secteur_gestimmob = self.convertir_secteur(secteur)
                        if secteur_gestimmob:
                            config_gestimmob['secteurs_personnalises'].append(secteur_gestimmob)
                    print(f"  ✅ Secteurs {table}: {len(secteurs)} éléments")
            
            # Convertir les configurations
            if donnees['configurations']:
                if 'utilisateurs' in donnees['configurations']:
                    config_gestimmob['utilisateurs'].extend(donnees['configurations']['utilisateurs'])
                
                if 'exemple_immobilisations' in donnees['configurations']:
                    config_gestimmob['exemples_donnees'].extend(donnees['configurations']['exemple_immobilisations'])
        
        return config_gestimmob
    
    def convertir_secteur(self, secteur_data):
        """Convertit un secteur au format GestImmob"""
        try:
            # Essayer de détecter les champs importants
            nom = secteur_data.get('nom') or secteur_data.get('name') or secteur_data.get('libelle') or str(secteur_data.get('id', 'Secteur'))
            description = secteur_data.get('description') or secteur_data.get('desc') or f"Secteur {nom}"
            couleur = secteur_data.get('couleur') or secteur_data.get('color') or '#1976d2'
            icone = secteur_data.get('icone') or secteur_data.get('icon') or '🏢'
            
            return {
                'nom': nom,
                'description': description,
                'couleur': couleur,
                'icone': icone,
                'criteres': []  # À compléter selon les besoins
            }
        except:
            return None
    
    def sauvegarder_configuration(self, config):
        """Sauvegarde la configuration convertie"""
        fichier_sortie = "configuration_gestimmob_importee.json"
        
        try:
            with open(fichier_sortie, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n💾 Configuration sauvegardée: {fichier_sortie}")
            return fichier_sortie
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            return None
    
    def generer_rapport_final(self, config):
        """Génère un rapport final de l'extraction"""
        print(f"\n📊 RAPPORT FINAL D'EXTRACTION")
        print("=" * 60)
        
        print(f"✅ Bases de données analysées: {len(self.parametres_extraits)}")
        print(f"✅ Secteurs extraits: {len(config['secteurs_personnalises'])}")
        print(f"✅ Paramètres extraits: {len(config['parametres_application'])}")
        print(f"✅ Utilisateurs extraits: {len(config['utilisateurs'])}")
        print(f"✅ Exemples de données: {len(config['exemples_donnees'])}")
        
        print(f"\n🎯 SECTEURS TROUVÉS:")
        for secteur in config['secteurs_personnalises']:
            print(f"  {secteur['icone']} {secteur['nom']}: {secteur['description']}")
        
        print(f"\n⚙️ PARAMÈTRES TROUVÉS:")
        for groupe, params in config['parametres_application'].items():
            print(f"  📋 {groupe}: {len(params)} paramètre(s)")
        
        print(f"\n🚀 PRÊT POUR INTÉGRATION DANS GESTIMMOB")

def main():
    """Fonction principale"""
    extracteur = ExtracteurParametresSQL()
    
    # Rechercher les bases de données
    extracteur.rechercher_bases_donnees()
    
    if not extracteur.bases_trouvees:
        print("❌ Aucune base de données trouvée")
        return
    
    # Extraire les paramètres de chaque base
    for base in extracteur.bases_trouvees:
        extracteur.extraire_parametres_base(base)
    
    # Convertir pour GestImmob
    config_gestimmob = extracteur.convertir_pour_gestimmob()
    
    # Sauvegarder
    fichier_config = extracteur.sauvegarder_configuration(config_gestimmob)
    
    # Rapport final
    extracteur.generer_rapport_final(config_gestimmob)
    
    if fichier_config:
        print(f"\n🎉 EXTRACTION TERMINÉE AVEC SUCCÈS !")
        print(f"📄 Fichier de configuration: {fichier_config}")
        print(f"🔄 Prêt pour intégration dans GestImmob")

if __name__ == "__main__":
    main()
