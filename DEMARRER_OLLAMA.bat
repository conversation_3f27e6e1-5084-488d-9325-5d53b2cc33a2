@echo off
title DÉMARRAGE OLLAMA POUR MASTER COMPTA
color 0A

echo.
echo ========================================================================
echo                🤖 DÉMARRAGE OLLAMA POUR MASTER COMPTA 🤖
echo                     Correction du timeout de connexion
echo ========================================================================
echo.

echo [%TIME%] Démarrage d'Ollama...

echo.
echo 🔍 VÉRIFICATION D'OLLAMA...
echo ========================================================================

echo Vérification si Ollama est installé...
ollama --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Ollama non installé
    echo.
    echo 💡 INSTALLATION REQUISE:
    echo 1. Téléchargez Ollama depuis: https://ollama.ai
    echo 2. Installez le fichier téléchargé
    echo 3. Redémarrez ce script
    echo.
    echo Appuyez sur une touche pour ouvrir le site Ollama...
    pause >nul
    start https://ollama.ai
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('ollama --version 2^>^&1') do echo ✅ Ollama installé: %%i
)

echo.
echo 🚀 DÉMARRAGE DU SERVEUR OLLAMA...
echo ========================================================================

echo Arrêt des processus Ollama existants...
taskkill /f /im ollama.exe 2>nul

echo Démarrage du serveur Ollama...
start /min "Ollama Server" ollama serve

echo Attente du démarrage du serveur...
timeout /t 5 /nobreak >nul

echo.
echo 🔍 TEST DE CONNEXION...
echo ========================================================================

echo Test de connexion au serveur Ollama...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Serveur pas encore prêt, nouvelle tentative...
    timeout /t 5 /nobreak >nul
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if errorlevel 1 (
        echo ❌ Impossible de se connecter au serveur Ollama
        echo 💡 Vérifiez que le port 11434 n'est pas bloqué
    ) else (
        echo ✅ Serveur Ollama opérationnel
    )
) else (
    echo ✅ Serveur Ollama opérationnel
)

echo.
echo 🤖 VÉRIFICATION DU MODÈLE LLAMA3.2...
echo ========================================================================

echo Vérification si llama3.2 est disponible...
ollama list | findstr "llama3.2" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Modèle llama3.2 non trouvé
    echo.
    echo 📥 TÉLÉCHARGEMENT DE LLAMA3.2...
    echo Cela peut prendre plusieurs minutes selon votre connexion...
    ollama pull llama3.2
    if errorlevel 1 (
        echo ❌ Échec du téléchargement de llama3.2
        echo 💡 Vérifiez votre connexion internet
    ) else (
        echo ✅ llama3.2 téléchargé avec succès
    )
) else (
    echo ✅ Modèle llama3.2 disponible
)

echo.
echo 🧪 TEST RAPIDE DU MODÈLE...
echo ========================================================================

echo Test rapide de llama3.2...
echo Bonjour | ollama run llama3.2 --timeout 10s >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Test du modèle échoué ou timeout
    echo 💡 Le modèle peut être lent au premier démarrage
) else (
    echo ✅ Modèle llama3.2 fonctionne
)

echo.
echo ========================================================================
echo                              RÉSULTAT FINAL
echo ========================================================================

echo [%TIME%] Configuration Ollama terminée

echo.
echo 🎯 STATUT OLLAMA:
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ❌ Serveur Ollama: NON OPÉRATIONNEL
    echo.
    echo 🔧 SOLUTIONS:
    echo 1. Redémarrez ce script en tant qu'administrateur
    echo 2. Vérifiez que le port 11434 n'est pas bloqué
    echo 3. Redémarrez votre ordinateur
    echo 4. Réinstallez Ollama
) else (
    echo ✅ Serveur Ollama: OPÉRATIONNEL
    echo ✅ Port 11434: ACCESSIBLE
    echo ✅ API: FONCTIONNELLE
    echo.
    echo 🚀 OLLAMA EST PRÊT POUR MASTER COMPTA !
    echo.
    echo 💡 ÉTAPES SUIVANTES:
    echo 1. Laissez cette fenêtre ouverte (serveur Ollama)
    echo 2. Lancez votre interface MASTER COMPTA
    echo 3. Cliquez sur le module "🤖 OLLAMA IA"
    echo 4. Testez la connexion
)

echo.
echo ⚠️ IMPORTANT: Ne fermez pas cette fenêtre !
echo Le serveur Ollama doit rester actif pour MASTER COMPTA.
echo.

echo Appuyez sur une touche pour continuer...
pause >nul

echo.
echo 🔄 MAINTIEN DU SERVEUR ACTIF...
echo Ollama reste en fonctionnement pour MASTER COMPTA
echo Fermez cette fenêtre pour arrêter Ollama
echo.

:loop
timeout /t 30 /nobreak >nul
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo [%TIME%] ⚠️ Serveur Ollama déconnecté - Redémarrage...
    start /min "Ollama Server" ollama serve
    timeout /t 5 /nobreak >nul
) else (
    echo [%TIME%] ✅ Serveur Ollama actif
)
goto loop
