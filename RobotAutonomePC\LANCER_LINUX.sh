#!/bin/bash
# 🐧 Robot IA Universel - Linux
# Compatible: Ubuntu, Debian, Fedora, CentOS, Arch, etc.

clear
echo "████████████████████████████████████████████████████████████████████████████████"
echo "██                                                                            ██"
echo "██    🐧 ROBOT IA UNIVERSEL - LINUX                                          ██"
echo "██    👤 SamNord@110577 - Compatible toutes distributions                    ██"
echo "██                                                                            ██"
echo "████████████████████████████████████████████████████████████████████████████████"
echo ""

echo "🐧 ROBOT IA UNIVERSEL POUR LINUX"
echo "================================="
echo "👤 Utilisateur: SamNord@110577"
echo "🔑 Mot de passe: NorDine@22"
echo "💻 Système: Linux"
echo ""

echo "🔄 Vérification environnement Linux..."

# Détection distribution
if [ -f /etc/os-release ]; then
    . /etc/os-release
    echo "✅ Distribution: $NAME $VERSION"
else
    echo "✅ Distribution: Linux (générique)"
fi

# Vérification architecture
echo "✅ Architecture: $(uname -m)"

# Vérification Python
if command -v python3 &> /dev/null; then
    echo "✅ Python3 détecté"
    echo "   Version: $(python3 --version)"
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    echo "✅ Python détecté"
    echo "   Version: $(python --version)"
    PYTHON_CMD="python"
else
    echo "❌ Python non détecté"
    echo "📥 Installation Python requise"
    echo ""
    echo "🔧 INSTALLATION PYTHON:"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "Fedora: sudo dnf install python3 python3-pip"
    echo "CentOS: sudo yum install python3 python3-pip"
    echo "Arch: sudo pacman -S python python-pip"
    echo ""
    read -p "Voulez-vous installer Python maintenant ? (o/N): " install
    if [[ $install =~ ^[Oo]$ ]]; then
        # Tentative installation automatique
        if command -v apt &> /dev/null; then
            sudo apt update && sudo apt install -y python3 python3-pip
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y python3 python3-pip
        elif command -v yum &> /dev/null; then
            sudo yum install -y python3 python3-pip
        elif command -v pacman &> /dev/null; then
            sudo pacman -S python python-pip
        else
            echo "❌ Installation automatique non supportée"
            echo "💡 Installez Python manuellement"
        fi
    fi
    exit 1
fi

echo ""
echo "🚀 LANCEMENT ROBOT IA UNIVERSEL..."

# Tentative exécutable Linux
if [ -f "executables/RobotIA_SamNord_Linux" ]; then
    echo "🐧 Lancement version exécutable Linux..."
    chmod +x "executables/RobotIA_SamNord_Linux"
    ./executables/RobotIA_SamNord_Linux
    exit 0
fi

# Tentative AppImage
if [ -f "executables/RobotIA_SamNord_Linux.AppImage" ]; then
    echo "📦 Lancement AppImage..."
    chmod +x "executables/RobotIA_SamNord_Linux.AppImage"
    ./executables/RobotIA_SamNord_Linux.AppImage
    exit 0
fi

# Tentative Python universel
if [ -f "robot_ia_universel.py" ]; then
    echo "🐍 Lancement version Python universelle..."
    $PYTHON_CMD robot_ia_universel.py
    exit 0
fi

# Fallback application principale
if [ -f "robot_autonome_pc.py" ]; then
    echo "🤖 Lancement application principale..."
    $PYTHON_CMD robot_autonome_pc.py
    exit 0
fi

echo "❌ Aucune version disponible !"
echo "🔧 Vérifiez l'installation"
read -p "Appuyez sur Entrée pour continuer..."
exit 1
