#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lancement immédiat de GestImmob - Version simple
"""

import os
import sys
import subprocess

def main():
    print("🚀 LANCEMENT IMMÉDIAT GESTIMMOB")
    print("=" * 50)
    
    # Vérifier Python
    print(f"Python: {sys.version}")
    
    # Chercher le fichier principal
    fichiers = [
        'src/main.py',
        'gestimmob_final.py', 
        'gestimmob_complet_final.py',
        'LANCER_GESTIMMOB_FINAL_V5.py',
        'gestimmob_enterprise_v5.py'
    ]
    
    fichier_trouve = None
    for fichier in fichiers:
        if os.path.exists(fichier):
            fichier_trouve = fichier
            print(f"✅ Fichier trouvé: {fichier}")
            break
    
    if not fichier_trouve:
        print("❌ Aucun fichier principal trouvé")
        print("Fichiers recherchés:", fichiers)
        return False
    
    # Installer dépendances critiques
    print("\n📦 Vérification dépendances...")
    try:
        import PySide6
        print("✅ PySide6: OK")
    except ImportError:
        print("📥 Installation PySide6...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'PySide6'], 
                         check=True, capture_output=True)
            print("✅ PySide6 installé")
        except:
            print("⚠️ Problème installation PySide6")
    
    # Créer répertoires
    for rep in ['data', 'logs']:
        if not os.path.exists(rep):
            os.makedirs(rep)
    
    # Lancer l'application
    print(f"\n🚀 Lancement: {fichier_trouve}")
    print("-" * 50)
    
    try:
        # Exécuter le fichier principal
        if fichier_trouve.endswith('.py'):
            # Exécuter comme module Python
            exec(open(fichier_trouve, encoding='utf-8').read())
        else:
            # Exécuter comme subprocess
            subprocess.run([sys.executable, fichier_trouve])
        
        print("\n✅ GestImmob fermé normalement")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Interruption utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
    
    input("\nAppuyez sur Entrée pour fermer...")
