<!-- Generated by SpecStory -->

# /fix Le type de « buttons » est partiellement inco (2025-06-08 11:02:49)

_**User**_

/fix Le type de « buttons » est partiellement inconnu
  Le type de "buttons" est "list[Unknown]", Le type de « append » est partiellement inconnu
  Le type de "append" est "(object: Unknown, /) -> None"

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Déclare explicitement le type de la liste `self.buttons` pour lever l'ambiguïté de type.

````python
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QTextEdit, QLabel, QMessageBox
from typing import List
# ...existing code...
class CalculatriceWidget(QWidget):
    def __init__(self):
        super().__init__()
        # ...existing code...
        grid = QHBoxLayout()
        self.buttons: List[QPushButton] = []
        for label in ["7", "8", "9", "/", "4", "5", "6", "*", "1", "2", "3", "-", "0", ".", "=", "+"]:
            btn = QPushButton(label)
            btn.clicked.connect(self.on_button_clicked)
            self.buttons.append(btn)
            grid.addWidget(btn)
        # ...existing code...
````

---

