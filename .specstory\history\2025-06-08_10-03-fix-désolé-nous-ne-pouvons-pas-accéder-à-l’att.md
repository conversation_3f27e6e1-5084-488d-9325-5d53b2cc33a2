<!-- Generated by SpecStory -->

# /fix Désolé... Nous ne pouvons pas accéder à l’att (2025-06-08 11:03:18)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « text » pour la classe « QObject »
  L’attribut « text » est inconnu, Le type de « text » est inconnu, Le type d’argument est inconnu
  L’argument correspond au paramètre « obj » dans la fonction « callable »

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Le problème vient du fait que `self.sender()` retourne un objet générique `QObject` et l'attribut `text()` n'est pas garanti. Mais dans ce contexte, tous les boutons sont des `QPushButton`, donc on peut faire un cast sûr avec `isinstance(sender, QPushButton)`.

Corrigez la vérification de l'attribut `text` en utilisant `isinstance` :

````python
    def on_button_clicked(self):
        sender = self.sender()
        text = ""
        # Correction : vérifier que le sender est bien un QPushButton
        if isinstance(sender, QPushButton):
            text = sender.text()
        else:
            return
        # ...existing code...
````

---

