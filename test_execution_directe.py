#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test d'exécution directe pour identifier les vrais problèmes
"""

import os
import sys
import subprocess
import traceback

def test_direct_execution():
    """Test d'exécution directe de l'application"""
    print("🔍 TEST D'EXÉCUTION DIRECTE DE L'APPLICATION")
    print("=" * 60)
    
    # Vérifier la structure
    if not os.path.exists('src/main.py'):
        print("❌ Fichier src/main.py non trouvé")
        return False
    
    print("✅ Fichier src/main.py trouvé")
    
    # Test 1: Import direct
    print(f"\n🔍 TEST 1: IMPORT DIRECT")
    print("-" * 30)
    
    try:
        # Ajouter le répertoire src au path
        sys.path.insert(0, 'src')
        
        # Tenter d'importer le module principal
        import main
        print("✅ Import de main.py: RÉUSSI")
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print(f"📍 Module manquant ou problème de dépendance")
        return False
    except SyntaxError as e:
        print(f"❌ Erreur de syntaxe: {e}")
        print(f"📍 Fichier: {e.filename}, Ligne: {e.lineno}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {type(e).__name__}: {e}")
        traceback.print_exc()
        return False

def test_subprocess_execution():
    """Test d'exécution via subprocess"""
    print(f"\n🔍 TEST 2: EXÉCUTION VIA SUBPROCESS")
    print("-" * 30)
    
    try:
        # Tenter d'exécuter l'application
        result = subprocess.run([
            sys.executable, 'src/main.py'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ Exécution subprocess: RÉUSSIE")
            if result.stdout:
                print(f"📤 Sortie: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ Exécution subprocess: ÉCHEC (code {result.returncode})")
            if result.stderr:
                print(f"📤 Erreur: {result.stderr[:500]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Timeout - L'application a démarré mais n'a pas terminé")
        print("✅ Cela peut être normal pour une application GUI")
        return True
    except Exception as e:
        print(f"❌ Erreur subprocess: {e}")
        return False

def test_individual_files():
    """Test des fichiers individuels"""
    print(f"\n🔍 TEST 3: FICHIERS INDIVIDUELS")
    print("-" * 30)
    
    files_to_test = [
        'src/config.py',
        'src/database/db_connection.py',
        'src/auth/user_management.py'
    ]
    
    results = []
    
    for file_path in files_to_test:
        print(f"\nTest de {file_path}...")
        
        if not os.path.exists(file_path):
            print(f"❌ Fichier non trouvé: {file_path}")
            results.append(False)
            continue
        
        try:
            # Test de compilation
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            compile(content, file_path, 'exec')
            print(f"✅ {file_path}: Compilation OK")
            results.append(True)
            
        except SyntaxError as e:
            print(f"❌ {file_path}: Erreur syntaxe ligne {e.lineno}: {e.msg}")
            results.append(False)
        except Exception as e:
            print(f"❌ {file_path}: Erreur: {e}")
            results.append(False)
    
    return all(results)

def test_dependencies():
    """Test des dépendances"""
    print(f"\n🔍 TEST 4: DÉPENDANCES")
    print("-" * 30)
    
    dependencies = [
        'PySide6',
        'sqlalchemy',
        'typing'
    ]
    
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: Disponible")
        except ImportError:
            print(f"❌ {dep}: MANQUANT")
            missing.append(dep)
    
    if missing:
        print(f"\n📦 Dépendances manquantes: {missing}")
        print("💡 Installez avec: pip install " + " ".join(missing))
        return False
    else:
        print(f"\n✅ Toutes les dépendances sont disponibles")
        return True

def main():
    """Test complet d'exécution"""
    print("🚀 TEST COMPLET D'EXÉCUTION DE L'APPLICATION")
    print("=" * 60)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Changer vers le bon répertoire si nécessaire
    if not os.path.exists('src/main.py'):
        print("⚠️ Recherche du bon répertoire...")
        possible_dirs = [
            'C:/Users/<USER>/Desktop/logiciel GestImmob/gestion-software',
            '../',
            './'
        ]
        for dir_path in possible_dirs:
            if os.path.exists(os.path.join(dir_path, 'src/main.py')):
                os.chdir(dir_path)
                print(f"✅ Répertoire trouvé: {os.getcwd()}")
                break
        else:
            print("❌ Impossible de trouver le répertoire du projet")
            return False
    
    # Exécuter tous les tests
    tests = [
        ("Dépendances", test_dependencies),
        ("Fichiers individuels", test_individual_files),
        ("Import direct", test_direct_execution),
        ("Subprocess", test_subprocess_execution)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"EXÉCUTION: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append(result)
            status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            print(f"\n❌ ERREUR lors du test {test_name}: {e}")
            results.append(False)
    
    # Résumé final
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ FINAL DES TESTS")
    print(f"{'='*60}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"Tests réussis: {success_count}/{total_count}")
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ RÉUSSI" if results[i] else "❌ ÉCHEC"
        print(f"  {status} {test_name}")
    
    if success_count == total_count:
        print(f"\n🎉 TOUS LES TESTS RÉUSSIS!")
        print(f"✅ L'application devrait fonctionner correctement")
        print(f"\n🚀 Commande de lancement:")
        print(f"   python src/main.py")
        return True
    else:
        print(f"\n🚨 {total_count - success_count} TEST(S) ÉCHOUÉ(S)")
        print(f"❌ Des problèmes empêchent l'exécution de l'application")
        
        # Suggestions de correction
        print(f"\n💡 SUGGESTIONS DE CORRECTION:")
        if not results[0]:  # Dépendances
            print("   - Installer les dépendances manquantes")
        if not results[1]:  # Fichiers individuels
            print("   - Corriger les erreurs de syntaxe dans les fichiers")
        if not results[2]:  # Import direct
            print("   - Vérifier les imports et la structure des modules")
        if not results[3]:  # Subprocess
            print("   - Vérifier l'exécution de l'application principale")
        
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat final: {'SUCCÈS' if success else 'ÉCHEC'}")
    sys.exit(0 if success else 1)
