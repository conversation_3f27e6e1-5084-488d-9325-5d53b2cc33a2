<!-- Generated by SpecStory -->

# /fix Le type « type[modules.sql_manager.SQLManager (2025-06-08 14:07:33)

_**User**_

/fix Le type « type[modules.sql_manager.SQLManager] » n’est pas assignable au type déclaré « type[modules.settings_manager.SQLManager] »
  « modules.sql_manager.SQLManager » n’est pas assignable à « modules.settings_manager.SQLManager »
  Le type « type[modules.sql_manager.SQLManager] » n’est pas assignable au type « type[modules.settings_manager.SQLManager] »

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Le problème vient d'une importation circulaire ou d'une confusion de types entre `SQLManager` défini dans ce fichier et celui importé depuis `.sql_manager`.  
Pour corriger cela, il faut :

1. **Supprimer l'import inutile** en haut du fichier, car la classe `SQLManager` est déjà définie dans ce même fichier.
2. **Corriger l'import dans le bloc `if __name__ == "__main__":`** pour utiliser la classe locale, sans réimporter.

Voici les modifications à apporter :

````python
# ...existing code...
# Supprimer cette ligne, car SQLManager est défini dans ce fichier
# from .sql_manager import SQLManager
# ...existing code...

if __name__ == "__main__":
    # Utiliser la classe SQLManager locale, pas besoin d'import
    # import sqlite3  # ou autre connecteur DB

    # Initialisation
    sql_manager = SQLManager("sql_queries")

    # Connexion à la base de données
    import sqlite3
    conn = sqlite3.connect("votre_base.db")

    # Exécution d'une requête
    resultats = sql_manager.execute_query("NUM_INV", conn)
    print(resultats)

    # Avec paramètres (si votre SQL utilise des variables)
    resultats_bureau = sql_manager.execute_query(
        "SEL-BUR", 
        conn, 
        {"cod_site": "01", "cod_bat": "01"}
    )
    print(resultats_bureau)
# ...existing code...
````

---

_**User**_

/fix L’importation « os » n’a pas été accédée

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Supprimer l'import inutile de `os` en haut du fichier, car il n'est pas utilisé dans cette section.

````python
import logging
# import os  # <- À supprimer, non utilisé ici
from pathlib import Path
# ...existing code...
````

---

