@echo off
title 🚀 ROBOT IA - LANCEMENT DIRECT
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🚀 ROBOT IA AUTONOME PC - LANCEMENT DIRECT                             ██
echo ██    👤 SamNord@110577 - SANS INSTALLATION                                  ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 LANCEMENT DIRECT ROBOT IA
echo 📁 Depuis ce dossier (pas besoin de copier)
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo.

echo 🔄 Vérification environnement...

REM Vérification Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python non détecté !
    echo.
    echo 📥 INSTALLATION PYTHON REQUISE:
    echo 1. Aller sur https://python.org
    echo 2. Télécharger Python 3.9+ 
    echo 3. Installer avec "Add to PATH" coché
    echo 4. Redémarrer ce script
    echo.
    echo 💡 OU utiliser les scripts .bat qui ne nécessitent pas Python
    echo.
    pause
    goto MENU_SANS_PYTHON
)

echo ✅ Python détecté
echo 📦 Vérification dépendances...

REM Installation dépendances si nécessaire
echo 🔄 Installation/vérification PySide6...
pip install PySide6 --quiet >nul 2>&1

echo 🔄 Installation/vérification requests...
pip install requests --quiet >nul 2>&1

echo 🔄 Installation/vérification psutil...
pip install psutil --quiet >nul 2>&1

echo ✅ Dépendances vérifiées

echo.
echo 🚀 LANCEMENT ROBOT IA AUTONOME PC...
echo 👤 Authentification automatique SamNord@110577...
echo.

REM Vérification fichier principal
if exist "robot_autonome_pc.py" (
    echo ✅ Application principale trouvée
    echo 🚀 Lancement en cours...
    echo.
    python robot_autonome_pc.py
) else (
    echo ❌ Fichier robot_autonome_pc.py non trouvé !
    echo 📁 Vérifiez que vous êtes dans le bon dossier
    pause
    goto MENU_SANS_PYTHON
)

goto END

:MENU_SANS_PYTHON
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    📊 MENU ROBOT IA - SANS PYTHON                                         ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎯 OPTIONS DISPONIBLES SANS PYTHON:
echo.
echo 1. 📊 Menu Universel Complet
echo 2. 🔐 Connexion Sécurisée SamNord
echo 3. 💾 Installation Portable
echo 4. 🔧 Maintenance Système
echo 5. 📁 Copier vers Bureau
echo 6. 🔍 Chercher Dossier Bureau
echo 7. 📋 Documentation
echo 8. ❌ Quitter
echo.
set /p choix="➤ Votre choix (1-8): "

if "%choix%"=="1" (
    if exist "LANCEMENT_FINAL_UNIVERSEL.bat" (
        call "LANCEMENT_FINAL_UNIVERSEL.bat"
    ) else (
        echo ❌ Fichier non trouvé !
    )
)
if "%choix%"=="2" (
    if exist "CONNEXION_SECURISEE.bat" (
        call "CONNEXION_SECURISEE.bat"
    ) else (
        echo ❌ Fichier non trouvé !
    )
)
if "%choix%"=="3" (
    if exist "INSTALLATION_PORTABLE.bat" (
        call "INSTALLATION_PORTABLE.bat"
    ) else (
        echo ❌ Fichier non trouvé !
    )
)
if "%choix%"=="4" (
    if exist "MAINTENANCE_ROBOT.bat" (
        call "MAINTENANCE_ROBOT.bat"
    ) else (
        echo ❌ Fichier non trouvé !
    )
)
if "%choix%"=="5" (
    if exist "COPIER_VERS_BUREAU_SIMPLE.bat" (
        call "COPIER_VERS_BUREAU_SIMPLE.bat"
    ) else (
        echo ❌ Fichier non trouvé !
    )
)
if "%choix%"=="6" (
    if exist "CHERCHER_DOSSIER_BUREAU.bat" (
        call "CHERCHER_DOSSIER_BUREAU.bat"
    ) else (
        echo ❌ Fichier non trouvé !
    )
)
if "%choix%"=="7" (
    echo 📋 Ouverture documentation...
    if exist "*.md" start "" *.md
    if exist "*.txt" start "" *.txt
)
if "%choix%"=="8" goto END

echo.
pause
goto MENU_SANS_PYTHON

:END
echo.
echo 👋 Robot IA fermé
echo 🎉 Merci d'avoir utilisé Robot IA Autonome PC !
pause
exit /b 0
