#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💾 ROBOT IA - GESTIONNAIRE SYSTÈME PORTABLE
Auto-détection, gestion multi-supports, synchronisation
Créé par Augment Agent
"""

import os
import sys
import subprocess
import json
import shutil
import threading
import time
from datetime import datetime
import psutil
import platform

class PortableSystemManager:
    """💾 Gestionnaire système portable ultra-avancé"""
    
    def __init__(self):
        self.portable_config = {
            "auto_detection": True,
            "multi_support": True,
            "sync_enabled": True,
            "backup_on_eject": True,
            "stealth_mode": True,
            "encryption_enabled": False,
            "supported_drives": ["USB", "SD", "HDD", "SSD", "Network"]
        }
        
        self.detected_installations = {}
        self.active_installations = {}
        self.sync_status = {}
        self.monitoring_active = False
        
        # Signatures pour détecter installations Robot IA
        self.robot_signatures = [
            "robot_autonome_pc.py",
            "LANCER_ROBOT_PORTABLE.bat",
            "portable_config.json",
            "modules/ultimate_intelligence.py"
        ]
        
    def start_auto_detection(self):
        """Démarre la détection automatique continue"""
        try:
            print("🔍 Démarrage auto-détection portable...")
            
            self.monitoring_active = True
            
            # Thread détection continue
            detection_thread = threading.Thread(
                target=self._detection_loop,
                daemon=True
            )
            detection_thread.start()
            
            # Thread surveillance supports
            monitoring_thread = threading.Thread(
                target=self._drive_monitoring_loop,
                daemon=True
            )
            monitoring_thread.start()
            
            # Thread synchronisation
            sync_thread = threading.Thread(
                target=self._sync_loop,
                daemon=True
            )
            sync_thread.start()
            
            print("✅ Auto-détection portable active")
            return True
            
        except Exception as e:
            print(f"❌ Erreur auto-détection: {e}")
            return False
    
    def _detection_loop(self):
        """Boucle de détection continue"""
        try:
            while self.monitoring_active:
                # Scan tous les lecteurs
                current_drives = self._get_available_drives()
                
                for drive in current_drives:
                    if self._is_robot_installation(drive):
                        if drive not in self.detected_installations:
                            print(f"🔍 Nouvelle installation détectée: {drive}")
                            self._register_installation(drive)
                
                # Nettoyage installations déconnectées
                self._cleanup_disconnected_installations()
                
                time.sleep(5)  # Vérification toutes les 5 secondes
                
        except Exception as e:
            print(f"❌ Erreur boucle détection: {e}")
    
    def _drive_monitoring_loop(self):
        """Surveillance des lecteurs"""
        try:
            previous_drives = set()
            
            while self.monitoring_active:
                current_drives = set(self._get_available_drives())
                
                # Nouveaux lecteurs
                new_drives = current_drives - previous_drives
                for drive in new_drives:
                    print(f"🔌 Nouveau lecteur connecté: {drive}")
                    self._handle_new_drive(drive)
                
                # Lecteurs déconnectés
                removed_drives = previous_drives - current_drives
                for drive in removed_drives:
                    print(f"🔌 Lecteur déconnecté: {drive}")
                    self._handle_removed_drive(drive)
                
                previous_drives = current_drives
                time.sleep(3)  # Vérification toutes les 3 secondes
                
        except Exception as e:
            print(f"❌ Erreur surveillance lecteurs: {e}")
    
    def _sync_loop(self):
        """Boucle de synchronisation"""
        try:
            while self.monitoring_active:
                if self.portable_config["sync_enabled"]:
                    self._sync_installations()
                
                time.sleep(60)  # Synchronisation toutes les minutes
                
        except Exception as e:
            print(f"❌ Erreur synchronisation: {e}")
    
    def _get_available_drives(self):
        """Récupère tous les lecteurs disponibles"""
        try:
            drives = []
            
            # Windows
            if platform.system() == "Windows":
                for partition in psutil.disk_partitions():
                    if partition.fstype and partition.mountpoint:
                        drives.append(partition.mountpoint)
            
            # Linux/Mac
            else:
                for partition in psutil.disk_partitions():
                    if partition.mountpoint.startswith('/media') or partition.mountpoint.startswith('/mnt'):
                        drives.append(partition.mountpoint)
            
            return drives
            
        except Exception as e:
            print(f"❌ Erreur récupération lecteurs: {e}")
            return []
    
    def _is_robot_installation(self, drive_path):
        """Vérifie si un lecteur contient une installation Robot IA"""
        try:
            # Recherche dossier RobotIA_Portable
            robot_dir = os.path.join(drive_path, "RobotIA_Portable")
            if not os.path.exists(robot_dir):
                return False
            
            # Vérification signatures
            signature_count = 0
            for signature in self.robot_signatures:
                signature_path = os.path.join(robot_dir, signature)
                if os.path.exists(signature_path):
                    signature_count += 1
            
            # Au moins 3 signatures sur 4 requises
            return signature_count >= 3
            
        except Exception as e:
            return False
    
    def _register_installation(self, drive_path):
        """Enregistre une nouvelle installation"""
        try:
            robot_dir = os.path.join(drive_path, "RobotIA_Portable")
            config_path = os.path.join(robot_dir, "Config", "portable_config.json")
            
            # Lecture configuration
            config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # Informations installation
            installation_info = {
                "drive_path": drive_path,
                "robot_dir": robot_dir,
                "config": config,
                "detected_at": datetime.now().isoformat(),
                "version": config.get("portable_config", {}).get("version", "unknown"),
                "last_sync": None,
                "status": "active"
            }
            
            self.detected_installations[drive_path] = installation_info
            
            # Notification
            self._notify_installation_detected(installation_info)
            
        except Exception as e:
            print(f"❌ Erreur enregistrement installation: {e}")
    
    def _handle_new_drive(self, drive_path):
        """Gère un nouveau lecteur connecté"""
        try:
            print(f"🔍 Analyse nouveau lecteur: {drive_path}")
            
            # Vérification installation Robot IA
            if self._is_robot_installation(drive_path):
                self._register_installation(drive_path)
                
                # Auto-lancement si configuré
                if self.portable_config.get("auto_launch", False):
                    self._auto_launch_installation(drive_path)
            else:
                # Proposition d'installation
                if self.portable_config.get("offer_installation", True):
                    self._offer_installation(drive_path)
            
        except Exception as e:
            print(f"❌ Erreur gestion nouveau lecteur: {e}")
    
    def _handle_removed_drive(self, drive_path):
        """Gère un lecteur déconnecté"""
        try:
            if drive_path in self.detected_installations:
                installation = self.detected_installations[drive_path]
                
                # Sauvegarde avant déconnexion
                if self.portable_config["backup_on_eject"]:
                    self._backup_before_eject(installation)
                
                # Nettoyage
                installation["status"] = "disconnected"
                installation["disconnected_at"] = datetime.now().isoformat()
                
                print(f"💾 Installation déconnectée: {drive_path}")
            
        except Exception as e:
            print(f"❌ Erreur gestion lecteur déconnecté: {e}")
    
    def _cleanup_disconnected_installations(self):
        """Nettoie les installations déconnectées"""
        try:
            to_remove = []
            
            for drive_path, installation in self.detected_installations.items():
                if not os.path.exists(drive_path):
                    if installation["status"] != "disconnected":
                        installation["status"] = "disconnected"
                        installation["disconnected_at"] = datetime.now().isoformat()
                    
                    # Suppression après 1 heure de déconnexion
                    if installation.get("disconnected_at"):
                        disconnect_time = datetime.fromisoformat(installation["disconnected_at"])
                        if (datetime.now() - disconnect_time).total_seconds() > 3600:
                            to_remove.append(drive_path)
            
            for drive_path in to_remove:
                del self.detected_installations[drive_path]
                print(f"🧹 Installation supprimée du cache: {drive_path}")
            
        except Exception as e:
            print(f"❌ Erreur nettoyage: {e}")
    
    def _sync_installations(self):
        """Synchronise les installations"""
        try:
            active_installations = [
                inst for inst in self.detected_installations.values()
                if inst["status"] == "active" and os.path.exists(inst["drive_path"])
            ]
            
            if len(active_installations) < 2:
                return  # Pas de synchronisation nécessaire
            
            print(f"🔄 Synchronisation {len(active_installations)} installations...")
            
            # Installation de référence (plus récente)
            reference = max(active_installations, 
                          key=lambda x: x.get("last_sync", x["detected_at"]))
            
            # Synchronisation des autres
            for installation in active_installations:
                if installation != reference:
                    self._sync_installation_to_reference(installation, reference)
            
        except Exception as e:
            print(f"❌ Erreur synchronisation: {e}")
    
    def _sync_installation_to_reference(self, target, reference):
        """Synchronise une installation vers la référence"""
        try:
            print(f"🔄 Sync {target['drive_path']} → {reference['drive_path']}")
            
            # Synchronisation configuration
            ref_config_dir = os.path.join(reference["robot_dir"], "Config")
            target_config_dir = os.path.join(target["robot_dir"], "Config")
            
            if os.path.exists(ref_config_dir):
                self._sync_directory(ref_config_dir, target_config_dir)
            
            # Synchronisation données
            ref_data_dir = os.path.join(reference["robot_dir"], "Data")
            target_data_dir = os.path.join(target["robot_dir"], "Data")
            
            if os.path.exists(ref_data_dir):
                self._sync_directory(ref_data_dir, target_data_dir)
            
            # Mise à jour timestamp
            target["last_sync"] = datetime.now().isoformat()
            
            print(f"✅ Synchronisation terminée: {target['drive_path']}")
            
        except Exception as e:
            print(f"❌ Erreur sync installation: {e}")
    
    def _sync_directory(self, source_dir, target_dir):
        """Synchronise un dossier"""
        try:
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
            
            for item in os.listdir(source_dir):
                source_item = os.path.join(source_dir, item)
                target_item = os.path.join(target_dir, item)
                
                if os.path.isfile(source_item):
                    # Copie si plus récent
                    if not os.path.exists(target_item) or \
                       os.path.getmtime(source_item) > os.path.getmtime(target_item):
                        shutil.copy2(source_item, target_item)
                
                elif os.path.isdir(source_item):
                    self._sync_directory(source_item, target_item)
            
        except Exception as e:
            print(f"❌ Erreur sync dossier: {e}")
    
    def _auto_launch_installation(self, drive_path):
        """Lance automatiquement une installation"""
        try:
            robot_dir = os.path.join(drive_path, "RobotIA_Portable")
            launcher = os.path.join(robot_dir, "LANCER_ROBOT_PORTABLE.bat")
            
            if os.path.exists(launcher):
                print(f"🚀 Lancement automatique: {drive_path}")
                subprocess.Popen([launcher], shell=True)
            
        except Exception as e:
            print(f"❌ Erreur lancement auto: {e}")
    
    def _offer_installation(self, drive_path):
        """Propose l'installation sur un nouveau support"""
        try:
            # Vérification espace libre
            free_space = shutil.disk_usage(drive_path).free
            required_space = 5 * 1024 * 1024 * 1024  # 5 GB
            
            if free_space < required_space:
                return  # Espace insuffisant
            
            print(f"💾 Support compatible détecté: {drive_path}")
            print(f"📦 Installation Robot IA possible ({free_space // (1024**3)} GB libres)")
            
            # Ici on pourrait proposer l'installation automatique
            
        except Exception as e:
            print(f"❌ Erreur proposition installation: {e}")
    
    def _backup_before_eject(self, installation):
        """Sauvegarde avant éjection"""
        try:
            backup_dir = os.path.join(installation["robot_dir"], "Backups")
            if not os.path.exists(backup_dir):
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"eject_backup_{timestamp}"
            backup_path = os.path.join(backup_dir, backup_name)
            
            # Sauvegarde configuration et données
            config_dir = os.path.join(installation["robot_dir"], "Config")
            data_dir = os.path.join(installation["robot_dir"], "Data")
            
            os.makedirs(backup_path, exist_ok=True)
            
            if os.path.exists(config_dir):
                shutil.copytree(config_dir, os.path.join(backup_path, "Config"))
            
            if os.path.exists(data_dir):
                shutil.copytree(data_dir, os.path.join(backup_path, "Data"))
            
            print(f"💾 Sauvegarde avant éjection: {backup_name}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde éjection: {e}")
    
    def _notify_installation_detected(self, installation):
        """Notifie la détection d'une installation"""
        try:
            drive = installation["drive_path"]
            version = installation["version"]
            
            print(f"🎉 Installation Robot IA détectée !")
            print(f"💾 Lecteur: {drive}")
            print(f"📦 Version: {version}")
            print(f"🕐 Détectée: {installation['detected_at']}")
            
            # Notification système (Windows)
            if platform.system() == "Windows":
                try:
                    import win10toast
                    toaster = win10toast.ToastNotifier()
                    toaster.show_toast(
                        "Robot IA Portable",
                        f"Installation détectée sur {drive}",
                        duration=5
                    )
                except:
                    pass  # Notification optionnelle
            
        except Exception as e:
            print(f"❌ Erreur notification: {e}")
    
    def get_installations_status(self):
        """Retourne le statut de toutes les installations"""
        try:
            status = {
                "total_installations": len(self.detected_installations),
                "active_installations": len([
                    inst for inst in self.detected_installations.values()
                    if inst["status"] == "active"
                ]),
                "installations": self.detected_installations,
                "monitoring_active": self.monitoring_active,
                "last_update": datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            print(f"❌ Erreur statut installations: {e}")
            return {}
    
    def create_portable_installation(self, target_drive, source_path=None):
        """Crée une nouvelle installation portable"""
        try:
            print(f"📦 Création installation portable sur {target_drive}...")
            
            # Vérification espace
            free_space = shutil.disk_usage(target_drive).free
            required_space = 5 * 1024 * 1024 * 1024  # 5 GB
            
            if free_space < required_space:
                print(f"❌ Espace insuffisant: {free_space // (1024**3)} GB < 5 GB")
                return False
            
            # Création structure
            robot_dir = os.path.join(target_drive, "RobotIA_Portable")
            os.makedirs(robot_dir, exist_ok=True)
            
            # Copie fichiers source
            if source_path and os.path.exists(source_path):
                shutil.copytree(source_path, robot_dir, dirs_exist_ok=True)
            
            # Configuration portable
            self._setup_portable_config(robot_dir, target_drive)
            
            print(f"✅ Installation portable créée: {target_drive}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création installation: {e}")
            return False
    
    def _setup_portable_config(self, robot_dir, drive_path):
        """Configure une installation portable"""
        try:
            config_dir = os.path.join(robot_dir, "Config")
            os.makedirs(config_dir, exist_ok=True)
            
            config = {
                "portable_config": {
                    "version": "1.0.0",
                    "installation_date": datetime.now().isoformat(),
                    "drive_letter": drive_path,
                    "portable_mode": True,
                    "auto_detection": True,
                    "sync_enabled": True,
                    "backup_on_eject": True
                }
            }
            
            config_path = os.path.join(config_dir, "portable_config.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"❌ Erreur configuration portable: {e}")

def main():
    """Fonction principale de test"""
    print("💾 GESTIONNAIRE SYSTÈME PORTABLE")
    print("=" * 50)
    
    manager = PortableSystemManager()
    
    # Démarrage auto-détection
    manager.start_auto_detection()
    
    # Affichage statut
    status = manager.get_installations_status()
    print(f"📊 Installations détectées: {status['total_installations']}")
    print(f"🔌 Installations actives: {status['active_installations']}")
    
    # Maintenir le système actif
    try:
        while True:
            time.sleep(10)
            # Affichage périodique du statut
            status = manager.get_installations_status()
            if status['active_installations'] > 0:
                print(f"💾 {status['active_installations']} installation(s) active(s)")
    except KeyboardInterrupt:
        print("\n👋 Arrêt gestionnaire portable")

if __name__ == "__main__":
    main()
