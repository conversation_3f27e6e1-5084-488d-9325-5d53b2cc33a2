#!/usr/bin/env python3
"""
Vérification simple des corrections
"""

import ast
import os

def check_file(filepath):
    """Vérifie un fichier Python"""
    print(f"Checking {filepath}...")
    
    if not os.path.exists(filepath):
        print(f"  ❌ File not found: {filepath}")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test AST
        ast.parse(content)
        print(f"  ✅ AST OK")
        
        # Test compilation
        compile(content, filepath, 'exec')
        print(f"  ✅ Compilation OK")
        
        return True
        
    except SyntaxError as e:
        print(f"  ❌ Syntax Error line {e.lineno}: {e.msg}")
        if e.text:
            print(f"     Code: {e.text.strip()}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Test principal"""
    print("=== VERIFICATION DES CORRECTIONS ===")
    
    files = [
        'src/main.py',
        'src/auth/user_management.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py'
    ]
    
    success_count = 0
    total_count = 0
    
    for filepath in files:
        total_count += 1
        if check_file(filepath):
            success_count += 1
        print()
    
    print(f"RESULTAT: {success_count}/{total_count} fichiers OK")
    
    if success_count == total_count:
        print("🎉 TOUS LES FICHIERS SONT CORRECTS !")
        return True
    else:
        print("🚨 Des corrections sont encore nécessaires")
        return False

if __name__ == "__main__":
    main()
