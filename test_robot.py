#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST ROBOT IA - DIAGNOSTIC
Test simple pour vérifier fonctionnement
"""

import sys
import os

def test_basic():
    """Test de base"""
    print("🧪 TEST ROBOT IA - DIAGNOSTIC")
    print("=" * 35)
    print("✅ Python fonctionne")
    print("✅ Imports de base OK")
    print("✅ Affichage console OK")
    
    # Test imports modules
    try:
        import json
        print("✅ JSON OK")
    except:
        print("❌ JSON erreur")
    
    try:
        import datetime
        print("✅ DateTime OK")
    except:
        print("❌ DateTime erreur")
    
    try:
        import threading
        print("✅ Threading OK")
    except:
        print("❌ Threading erreur")
    
    # Test modules Robot IA
    print("\n📦 Test modules Robot IA:")
    
    sys.path.append('RobotAutonomePC/modules')
    
    modules_test = [
        'intelligence_ia_transcendante',
        'scanner_telecommunications',
        'cybersecurite_ethique',
        'tracking_telephones',
        'traduction_temps_reel',
        'communication_multilingue'
    ]
    
    for module in modules_test:
        try:
            exec(f'import {module}')
            print(f"✅ {module}: OK")
        except ImportError:
            print(f"❌ {module}: Module non trouvé")
        except Exception as e:
            print(f"⚠️ {module}: Erreur - {str(e)[:50]}")
    
    print("\n🎯 Test terminé !")
    return True

if __name__ == "__main__":
    test_basic()
    input("\nAppuyez sur Entrée pour continuer...")
