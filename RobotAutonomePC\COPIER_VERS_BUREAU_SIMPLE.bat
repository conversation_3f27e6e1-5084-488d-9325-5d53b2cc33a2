@echo off
title 📁 COPIE SIMPLE VERS BUREAU
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    📁 COPIE SIMPLE ROBOT IA VERS BUREAU                                   ██
echo ██    🎯 MÉTHODE MANUELLE GARANTIE                                            ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 COPIE MANUELLE VERS BUREAU
echo 📁 Méthode simple et garantie
echo 🖥️ Création sur bureau utilisateur
echo.

REM Affichage des chemins
set "SOURCE=%~dp0"
set "BUREAU=%USERPROFILE%\Desktop"

echo 📍 INFORMATIONS:
echo 📂 Dossier source: %SOURCE%
echo 🖥️ Votre bureau: %BUREAU%
echo.

echo 🔄 INSTRUCTIONS MANUELLES:
echo.
echo 1️⃣ CRÉER LE DOSSIER:
echo    - Clic droit sur votre bureau
echo    - Nouveau ^> Dossier
echo    - Nommer: RobotIA_SamNord
echo    - Appuyer Entrée
echo.
echo 2️⃣ COPIER LES FICHIERS:
echo    - Sélectionner TOUS les fichiers de ce dossier
echo    - Ctrl+C ^(copier^)
echo    - Aller dans RobotIA_SamNord sur bureau
echo    - Ctrl+V ^(coller^)
echo.

pause

echo.
echo 🔄 TENTATIVE COPIE AUTOMATIQUE...
echo.

REM Création dossier bureau
echo 📁 Création dossier RobotIA_SamNord sur bureau...
mkdir "%BUREAU%\RobotIA_SamNord" 2>nul

if exist "%BUREAU%\RobotIA_SamNord" (
    echo ✅ Dossier créé: %BUREAU%\RobotIA_SamNord
) else (
    echo ❌ Impossible de créer le dossier automatiquement
    echo 💡 Créez-le manuellement comme indiqué ci-dessus
    goto MANUAL
)

echo.
echo 📋 Copie des fichiers...

REM Copie fichiers principaux
copy "%SOURCE%*.py" "%BUREAU%\RobotIA_SamNord\" >nul 2>&1
copy "%SOURCE%*.bat" "%BUREAU%\RobotIA_SamNord\" >nul 2>&1
copy "%SOURCE%*.md" "%BUREAU%\RobotIA_SamNord\" >nul 2>&1
copy "%SOURCE%*.txt" "%BUREAU%\RobotIA_SamNord\" >nul 2>&1

echo ✅ Fichiers principaux copiés

REM Copie dossier modules
if exist "%SOURCE%modules" (
    echo 📦 Copie dossier modules...
    xcopy "%SOURCE%modules" "%BUREAU%\RobotIA_SamNord\modules\" /E /I /Q >nul 2>&1
    echo ✅ Modules copiés
)

REM Création lanceur simple
echo.
echo 🚀 Création lanceur simple...
(
echo @echo off
echo title 🤖 ROBOT IA AUTONOME PC
echo color 0A
echo chcp 65001 ^>nul
echo.
echo echo 🤖 ROBOT IA AUTONOME PC - SamNord@110577
echo echo ================================
echo echo.
echo echo 👤 Utilisateur: SamNord@110577
echo echo 🔑 Mot de passe: NorDine@22
echo echo.
echo echo 🚀 Lancement Robot IA...
echo echo.
echo.
echo python --version ^>nul 2^>^&1
echo if %%errorLevel%% equ 0 ^(
echo     if exist "robot_autonome_pc.py" ^(
echo         echo ✅ Lancement version Python...
echo         python robot_autonome_pc.py
echo     ^) else ^(
echo         echo ❌ Fichier robot_autonome_pc.py non trouvé !
echo         echo 📁 Vérifiez que tous les fichiers sont copiés
echo         pause
echo     ^)
echo ^) else ^(
echo     echo ❌ Python non installé !
echo     echo 📥 Installez Python depuis python.org
echo     echo 📦 Puis tapez: pip install PySide6 requests psutil
echo     pause
echo ^)
echo.
echo pause
) > "%BUREAU%\RobotIA_SamNord\🚀 LANCER ROBOT IA.bat"

echo ✅ Lanceur créé

REM Création raccourci bureau
echo.
echo 🔗 Création raccourci bureau...
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%BUREAU%\🤖 Robot IA - SamNord.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%BUREAU%\RobotIA_SamNord\🚀 LANCER ROBOT IA.bat" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%BUREAU%\RobotIA_SamNord" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "Robot IA Autonome PC - SamNord@110577" >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"

cscript "%TEMP%\shortcut.vbs" >nul 2>&1
del "%TEMP%\shortcut.vbs" >nul 2>&1

echo ✅ Raccourci créé

echo.
echo 🎉 COPIE TERMINÉE !
echo.
echo 📁 VÉRIFIEZ VOTRE BUREAU:
echo ✅ Dossier: RobotIA_SamNord
echo ✅ Raccourci: 🤖 Robot IA - SamNord
echo.
echo 🚀 POUR LANCER:
echo 1. Double-cliquer le raccourci sur bureau
echo 2. Ou aller dans le dossier et lancer "🚀 LANCER ROBOT IA.bat"
echo.

REM Ouverture bureau
echo 📂 Ouverture bureau...
start "" explorer "%BUREAU%"

goto END

:MANUAL
echo.
echo 📋 INSTRUCTIONS DÉTAILLÉES MANUELLES:
echo.
echo 1️⃣ CRÉER DOSSIER SUR BUREAU:
echo    📁 Clic droit bureau ^> Nouveau ^> Dossier
echo    📝 Nom: RobotIA_SamNord
echo.
echo 2️⃣ COPIER FICHIERS:
echo    📂 Ouvrir ce dossier: %SOURCE%
echo    📋 Sélectionner tout: Ctrl+A
echo    📋 Copier: Ctrl+C
echo    📁 Aller dans RobotIA_SamNord sur bureau
echo    📋 Coller: Ctrl+V
echo.
echo 3️⃣ LANCER:
echo    🚀 Double-cliquer "robot_autonome_pc.py"
echo    🐍 Ou installer Python et relancer
echo.

REM Ouverture dossier source
echo 📂 Ouverture dossier source pour copie manuelle...
start "" explorer "%SOURCE%"

:END
echo.
echo 👋 Terminé ! Vérifiez votre bureau.
pause
exit /b 0
