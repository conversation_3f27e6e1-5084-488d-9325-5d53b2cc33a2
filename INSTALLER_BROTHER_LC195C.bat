@echo off
title INSTALLATION BROTHER LC195C - RÉPARATION PILOTES
color 0B

echo.
echo ========================================================================
echo              🖨️ INSTALLATION BROTHER LC195C - PILOTES 🖨️
echo                        Réparation spécifique Brother
echo ========================================================================
echo.

echo [%TIME%] Installation des pilotes Brother LC195C...

echo.
echo 📋 INFORMATIONS IMPRIMANTE:
echo    Modèle: Brother LC195C
echo    Type: Imprimante jet d'encre couleur
echo    Connexion: USB / WiFi
echo.

echo 🔍 ÉTAPE 1: DÉTECTION DE L'IMPRIMANTE...
echo ========================================================================

echo Recherche de l'imprimante Brother...
wmic printer get Name,Status 2>nul | findstr /i "brother"
if errorlevel 1 (
    echo ⚠️ Imprimante Brother non détectée
) else (
    echo ✅ Imprimante Brother détectée
)

echo.
echo 🔧 ÉTAPE 2: SUPPRESSION DES ANCIENS PILOTES...
echo ========================================================================

echo Suppression des pilotes Brother corrompus...
pnputil /delete-driver "*brother*" /uninstall /force 2>nul
rundll32 printui.dll,PrintUIEntry /dl /n "Brother*" 2>nul

echo ✅ Anciens pilotes supprimés

echo.
echo 🔧 ÉTAPE 3: NETTOYAGE DU SPOOLER D'IMPRESSION...
echo ========================================================================

echo Arrêt du spooler d'impression...
net stop spooler

echo Nettoyage des fichiers temporaires...
del /f /q "%SystemRoot%\System32\spool\PRINTERS\*.*" 2>nul

echo Redémarrage du spooler...
net start spooler

echo ✅ Spooler nettoyé et redémarré

echo.
echo 🌐 ÉTAPE 4: TÉLÉCHARGEMENT DES PILOTES BROTHER...
echo ========================================================================

echo 📥 LIENS DE TÉLÉCHARGEMENT BROTHER LC195C:
echo.
echo 1. Site officiel Brother:
echo    https://support.brother.com/g/b/downloadtop.aspx?c=fr&lang=fr&prod=mfcj6520dw_eu_as
echo.
echo 2. Pilote complet Brother:
echo    https://download.brother.com/welcome/dlf006893/mfcj6520dwpdrv_c1-inst-win10-fr.exe
echo.
echo 3. Utilitaire Brother iPrint^&Scan:
echo    https://download.brother.com/welcome/dlf103051/BrotherIPrintScan_WIN.exe
echo.

echo 💡 INSTRUCTIONS DE TÉLÉCHARGEMENT:
echo 1. Ouvrez votre navigateur
echo 2. Copiez un des liens ci-dessus
echo 3. Téléchargez le pilote complet
echo 4. Exécutez l'installateur en tant qu'administrateur

echo.
echo 🔧 ÉTAPE 5: INSTALLATION MANUELLE...
echo ========================================================================

echo Si le téléchargement automatique ne fonctionne pas:

echo.
echo 📋 INSTALLATION MANUELLE:
echo 1. Panneau de configuration → Périphériques et imprimantes
echo 2. Ajouter une imprimante
echo 3. "L'imprimante que je veux n'est pas répertoriée"
echo 4. "Ajouter une imprimante locale ou réseau"
echo 5. Utiliser un port existant: USB001
echo 6. Fabricant: Brother
echo 7. Modèle: MFC-J6520DW (ou similaire LC195C)

echo.
echo 🔧 ÉTAPE 6: CONFIGURATION USB...
echo ========================================================================

echo Configuration des ports USB pour Brother...
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Print\Monitors\USB Monitor" /v "Driver" /t REG_SZ /d "usbmon.dll" /f 2>nul

echo ✅ Configuration USB mise à jour

echo.
echo 🔧 ÉTAPE 7: REDÉMARRAGE DES SERVICES D'IMPRESSION...
echo ========================================================================

echo Redémarrage des services d'impression...
net stop spooler
net stop "Fax"
timeout /t 3 /nobreak >nul
net start spooler
net start "Fax"

echo ✅ Services d'impression redémarrés

echo.
echo 📊 ÉTAPE 8: TEST DE CONNEXION...
echo ========================================================================

echo Test de la connexion imprimante...
ping -n 1 192.168.1.100 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Test réseau échoué (normal si connexion USB uniquement)
) else (
    echo ✅ Connexion réseau détectée
)

echo.
echo ========================================================================
echo                              RÉSULTAT FINAL
echo ========================================================================

echo [%TIME%] Configuration Brother LC195C terminée

echo.
echo 🎯 ACTIONS EFFECTUÉES:
echo ✅ Détection de l'imprimante
echo ✅ Suppression des anciens pilotes
echo ✅ Nettoyage du spooler d'impression
echo ✅ Liens de téléchargement fournis
echo ✅ Instructions d'installation manuelle
echo ✅ Configuration USB mise à jour
echo ✅ Redémarrage des services d'impression

echo.
echo 💡 ÉTAPES SUIVANTES:
echo.
echo 1. 🌐 TÉLÉCHARGEZ LE PILOTE:
echo    Utilisez un des liens fournis ci-dessus
echo    Ou allez sur support.brother.com
echo.
echo 2. 🔌 CONNEXION PHYSIQUE:
echo    - Éteignez l'imprimante
echo    - Débranchez le câble USB
echo    - Attendez 30 secondes
echo    - Rebranchez le câble USB
echo    - Allumez l'imprimante
echo.
echo 3. 💿 INSTALLATION:
echo    - Exécutez le pilote téléchargé en tant qu'administrateur
echo    - Suivez l'assistant d'installation
echo    - Redémarrez l'ordinateur si demandé
echo.
echo 4. 🖨️ TEST D'IMPRESSION:
echo    - Imprimez une page de test
echo    - Vérifiez la qualité d'impression
echo.

echo ⚠️ SI LE PROBLÈME PERSISTE:
echo - Essayez un autre câble USB
echo - Testez sur un autre port USB
echo - Vérifiez que l'imprimante s'allume correctement
echo - Contactez le support Brother: 0825 383 383

echo.
echo 🖨️ CONFIGURATION BROTHER LC195C TERMINÉE
echo.

pause
