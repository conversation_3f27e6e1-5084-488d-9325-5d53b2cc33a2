{"name": "robot-ia-sam<PERSON>d", "displayName": "🤖 Robot IA SamNord", "description": "Assistant IA Transcendant - Interface comme Augment Agent", "version": "1.0.0", "publisher": "SamNord110577", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Data Science"], "keywords": ["robot", "ia", "intelligence artificielle", "samnord", "assistant"], "activationEvents": ["*"], "main": "./extension.js", "contributes": {"commands": [{"command": "robotIA.activate", "title": "🤖 Activer Robot IA", "category": "Robot IA"}, {"command": "robotIA.chat", "title": "💬 Chat Robot IA", "category": "Robot IA"}, {"command": "robotIA.modules", "title": "📦 <PERSON><PERSON><PERSON> (40+)", "category": "Robot IA"}, {"command": "robotIA.wifi", "title": "📡 WiFi Manager", "category": "Robot IA"}, {"command": "robotIA.bluetooth", "title": "📱 Bluetooth Manager", "category": "Robot IA"}], "keybindings": [{"command": "robotIA.chat", "key": "ctrl+shift+r", "mac": "cmd+shift+r"}], "viewsContainers": {"activitybar": [{"id": "robotIA", "title": "🤖 Robot IA SamNord", "icon": "$(robot)"}]}, "views": {"robotIA": [{"id": "robotIA.chat", "name": "💬 Chat Assistant", "type": "webview"}, {"id": "robotIA.modules", "name": "📦 <PERSON><PERSON><PERSON> (40+)"}, {"id": "robotIA.status", "name": "⚡ Status Système"}]}, "configuration": {"title": "Robot IA SamNord", "properties": {"robotIA.user": {"type": "string", "default": "SamNord@110577", "description": "Utilisateur Robot IA"}, "robotIA.autoStart": {"type": "boolean", "default": true, "description": "Démarrage automatique"}}}}, "scripts": {"compile": "echo Extension ready"}}