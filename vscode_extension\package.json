{"name": "robot-ia-sam<PERSON>d", "displayName": "🤖 Robot IA SamNord - Assistant Transcendant", "description": "Robot IA Transcendant intégré à VS Code - Interface identique à Augment Agent", "version": "1.0.0", "publisher": "SamNord110577", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Data Science", "Snippets", "Debuggers"], "keywords": ["robot", "ia", "intelligence artificielle", "transcendant", "automation", "cybersecurity", "telecommunications"], "activationEvents": ["onCommand:robotIA.activate", "onCommand:robotIA.dashboard", "onCommand:robotIA.modules", "onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "robotIA.activate", "title": "🤖 Activer Robot IA", "category": "Robot IA"}, {"command": "robotIA.dashboard", "title": "📊 Tableau de Bord Robot IA", "category": "Robot IA"}, {"command": "robotIA.modules", "title": "📦 Modules Robot IA (40+)", "category": "Robot IA"}, {"command": "robotIA.intelligence", "title": "🧠 Intelligence IA Transcendante", "category": "Robot IA"}, {"command": "robotIA.scanner", "title": "📡 Scanner Télécommunications", "category": "Robot IA"}, {"command": "robotIA.cybersecurity", "title": "🛡️ Cybersécurité Éthique", "category": "Robot IA"}, {"command": "robotIA.tracking", "title": "📱 Tracking <PERSON><PERSON>", "category": "Robot IA"}, {"command": "robotIA.translation", "title": "🌍 Traduction 100+ Langues", "category": "Robot IA"}, {"command": "robotIA.voice", "title": "🗣️ Communication Vocale", "category": "Robot IA"}, {"command": "robotIA.tests", "title": "🧪 Tests Automatiques 1000+", "category": "Robot IA"}, {"command": "robotIA.autoRepair", "title": "🔧 Auto-Réparation", "category": "Robot IA"}, {"command": "robotIA.evolution", "title": "🤖 Auto-Évolution", "category": "Robot IA"}, {"command": "robotIA.memory", "title": "🧠 <PERSON><PERSON><PERSON><PERSON>", "category": "Robot IA"}, {"command": "robotIA.compta", "title": "💼 Master <PERSON><PERSON><PERSON>", "category": "Robot IA"}, {"command": "robotIA.video", "title": "📱 Enregistrement Vidéo", "category": "Robot IA"}, {"command": "robotIA.integration", "title": "🌐 Intégration YouTube/PDF/Web", "category": "Robot IA"}, {"command": "robotIA.standalone", "title": "🚀 Lancer Robot IA Indépendant", "category": "Robot IA"}, {"command": "robotIA.terminal", "title": "💻 Terminal Robot IA", "category": "Robot IA"}], "menus": {"commandPalette": [{"command": "robotIA.activate", "when": "true"}], "editor/context": [{"command": "robotIA.intelligence", "group": "robotIA", "when": "editorTextFocus"}], "explorer/context": [{"command": "robotIA.modules", "group": "robotIA"}]}, "keybindings": [{"command": "robotIA.activate", "key": "ctrl+shift+r", "mac": "cmd+shift+r"}, {"command": "robotIA.dashboard", "key": "ctrl+shift+d", "mac": "cmd+shift+d"}, {"command": "robotIA.intelligence", "key": "ctrl+shift+i", "mac": "cmd+shift+i"}], "viewsContainers": {"activitybar": [{"id": "robotIA", "title": "🤖 Robot IA Transcendant", "icon": "$(robot)"}]}, "views": {"robotIA": [{"id": "robotIA.dashboard", "name": "📊 Tableau de Bord", "when": "robotIA.activated"}, {"id": "robotIA.modules", "name": "📦 <PERSON><PERSON><PERSON> (40+)", "when": "robotIA.activated"}, {"id": "robotIA.status", "name": "⚡ Status Système", "when": "robotIA.activated"}, {"id": "robotIA.logs", "name": "📋 Logs Robot IA", "when": "robotIA.activated"}]}, "configuration": {"title": "Robot IA Transcendant", "properties": {"robotIA.user": {"type": "string", "default": "SamNord@110577", "description": "Utilisateur Robot IA"}, "robotIA.autoStart": {"type": "boolean", "default": true, "description": "Démarrage automatique Robot IA"}, "robotIA.theme": {"type": "string", "default": "dark", "enum": ["dark", "light"], "description": "Thème Robot IA"}, "robotIA.modulesPath": {"type": "string", "default": "./modules", "description": "Chemin modules Robot IA"}, "robotIA.standalone": {"type": "boolean", "default": true, "description": "Mode indépendant activé"}}}, "snippets": [{"language": "python", "path": "./snippets/robot-ia-snippets.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"child_process": "^1.0.2", "fs": "^0.0.1-security", "path": "^0.12.7"}}