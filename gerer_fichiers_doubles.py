#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour gérer les fichiers en double avec demande de confirmation
"""

import os
import hashlib
import shutil
from pathlib import Path
from collections import defaultdict
from typing import Dict, List

class GestionnaireFichiersDoubles:
    def __init__(self):
        self.fichiers_doubles = defaultdict(list)
        self.suppressions_proposees = []
        self.suppressions_effectuees = []
        
    def calculer_hash_fichier(self, file_path: str) -> str:
        """Calcule le hash MD5 d'un fichier"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            print(f"❌ Erreur lecture {file_path}: {e}")
            return ""
    
    def identifier_fichiers_doubles(self):
        """Identifie tous les fichiers en double dans le projet"""
        print("🔍 IDENTIFICATION DES FICHIERS EN DOUBLE")
        print("=" * 50)
        
        hash_to_files = defaultdict(list)
        fichiers_analyses = 0
        
        # Extensions de fichiers à analyser
        extensions_a_analyser = {'.py', '.sql', '.json', '.txt', '.md', '.bat', '.ini', '.cfg'}
        
        for root, dirs, files in os.walk('.'):
            # Ignorer certains répertoires
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', 'env']]
            
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = Path(file).suffix.lower()
                
                if file_ext in extensions_a_analyser:
                    fichiers_analyses += 1
                    file_hash = self.calculer_hash_fichier(file_path)
                    if file_hash:
                        hash_to_files[file_hash].append(file_path)
        
        print(f"📁 Fichiers analysés: {fichiers_analyses}")
        
        # Identifier les doublons
        groupes_doubles = 0
        for file_hash, file_list in hash_to_files.items():
            if len(file_list) > 1:
                self.fichiers_doubles[file_hash] = file_list
                groupes_doubles += 1
        
        print(f"🔄 Groupes de doublons trouvés: {groupes_doubles}")
        
        if groupes_doubles == 0:
            print("✅ Aucun fichier en double trouvé !")
            return False
        
        return True
    
    def analyser_fichiers_doubles(self):
        """Analyse les fichiers en double et propose des suppressions"""
        print(f"\n📊 ANALYSE DES FICHIERS EN DOUBLE")
        print("=" * 50)
        
        for i, (file_hash, file_list) in enumerate(self.fichiers_doubles.items(), 1):
            print(f"\n🔄 Groupe {i} - {len(file_list)} fichiers identiques:")
            
            # Afficher les informations de chaque fichier
            for j, file_path in enumerate(file_list):
                try:
                    size = os.path.getsize(file_path)
                    mtime = os.path.getmtime(file_path)
                    print(f"  {j+1}. {file_path}")
                    print(f"     Taille: {size} bytes")
                    print(f"     Modifié: {Path(file_path).stat().st_mtime}")
                except Exception as e:
                    print(f"  {j+1}. {file_path} (erreur: {e})")
            
            # Proposer quel fichier garder
            fichier_a_garder = self.choisir_fichier_a_garder(file_list)
            fichiers_a_supprimer = [f for f in file_list if f != fichier_a_garder]
            
            print(f"  ✅ RECOMMANDATION: Garder {fichier_a_garder}")
            print(f"  🗑️ SUPPRIMER: {len(fichiers_a_supprimer)} fichier(s)")
            
            for fichier in fichiers_a_supprimer:
                self.suppressions_proposees.append({
                    'fichier': fichier,
                    'raison': f'Doublon de {fichier_a_garder}',
                    'groupe': i
                })
    
    def choisir_fichier_a_garder(self, file_list: List[str]) -> str:
        """Choisit intelligemment quel fichier garder parmi les doublons"""
        # Priorités pour choisir le fichier à garder:
        # 1. Fichier dans src/ plutôt que racine
        # 2. Fichier avec le chemin le plus court
        # 3. Fichier le plus récent
        
        # Trier par priorité
        def priorite_fichier(file_path):
            score = 0
            
            # Priorité 1: Fichier dans src/
            if file_path.startswith('src/'):
                score += 1000
            
            # Priorité 2: Chemin plus court (moins de niveaux)
            score -= file_path.count('/') * 100
            
            # Priorité 3: Plus récent
            try:
                score += os.path.getmtime(file_path)
            except:
                pass
            
            return score
        
        return max(file_list, key=priorite_fichier)
    
    def demander_confirmation_suppression(self):
        """Demande confirmation avant suppression"""
        print(f"\n❓ CONFIRMATION DE SUPPRESSION")
        print("=" * 50)
        
        if not self.suppressions_proposees:
            print("✅ Aucune suppression proposée")
            return True
        
        print(f"🗑️ {len(self.suppressions_proposees)} fichier(s) proposé(s) pour suppression:")
        
        for i, suppression in enumerate(self.suppressions_proposees, 1):
            print(f"  {i}. {suppression['fichier']}")
            print(f"     Raison: {suppression['raison']}")
        
        print(f"\n⚠️ ATTENTION: Cette action est irréversible !")
        print(f"Les fichiers seront déplacés vers un dossier de sauvegarde avant suppression.")
        
        while True:
            reponse = input(f"\nConfirmez-vous la suppression ? (o/n/d pour détails): ").lower().strip()
            
            if reponse in ['o', 'oui', 'y', 'yes']:
                return True
            elif reponse in ['n', 'non', 'no']:
                print("❌ Suppression annulée par l'utilisateur")
                return False
            elif reponse in ['d', 'details', 'detail']:
                self.afficher_details_suppressions()
            else:
                print("Répondez par 'o' (oui), 'n' (non) ou 'd' (détails)")
    
    def afficher_details_suppressions(self):
        """Affiche les détails des suppressions proposées"""
        print(f"\n📋 DÉTAILS DES SUPPRESSIONS PROPOSÉES")
        print("-" * 40)
        
        groupes = defaultdict(list)
        for suppression in self.suppressions_proposees:
            groupes[suppression['groupe']].append(suppression)
        
        for groupe_id, suppressions in groupes.items():
            print(f"\n🔄 Groupe {groupe_id}:")
            for suppression in suppressions:
                print(f"  🗑️ {suppression['fichier']}")
                print(f"     → {suppression['raison']}")
    
    def effectuer_suppressions(self):
        """Effectue les suppressions avec sauvegarde"""
        print(f"\n🗑️ SUPPRESSION DES FICHIERS EN DOUBLE")
        print("=" * 50)
        
        if not self.suppressions_proposees:
            print("✅ Aucune suppression à effectuer")
            return True
        
        # Créer un dossier de sauvegarde
        backup_dir = Path('backup_fichiers_doubles')
        backup_dir.mkdir(exist_ok=True)
        
        print(f"💾 Dossier de sauvegarde: {backup_dir}")
        
        for suppression in self.suppressions_proposees:
            fichier = suppression['fichier']
            
            try:
                # Créer la structure dans le backup
                backup_path = backup_dir / fichier
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Copier vers le backup
                shutil.copy2(fichier, backup_path)
                
                # Supprimer l'original
                os.remove(fichier)
                
                print(f"✅ Supprimé: {fichier}")
                print(f"   Sauvegardé: {backup_path}")
                
                self.suppressions_effectuees.append(fichier)
                
            except Exception as e:
                print(f"❌ Erreur suppression {fichier}: {e}")
        
        print(f"\n📊 Suppressions effectuées: {len(self.suppressions_effectuees)}")
        return True
    
    def generer_rapport_final(self):
        """Génère un rapport final de gestion des doublons"""
        print(f"\n📊 RAPPORT FINAL - GESTION DES FICHIERS EN DOUBLE")
        print("=" * 60)
        
        print(f"🔄 Groupes de doublons trouvés: {len(self.fichiers_doubles)}")
        print(f"🗑️ Fichiers proposés pour suppression: {len(self.suppressions_proposees)}")
        print(f"✅ Fichiers effectivement supprimés: {len(self.suppressions_effectuees)}")
        
        if self.suppressions_effectuees:
            print(f"\n✅ FICHIERS SUPPRIMÉS:")
            for fichier in self.suppressions_effectuees:
                print(f"  - {fichier}")
            
            print(f"\n💾 SAUVEGARDE:")
            print(f"  Les fichiers supprimés sont sauvegardés dans: backup_fichiers_doubles/")
            print(f"  Vous pouvez les restaurer si nécessaire.")
        
        if len(self.suppressions_effectuees) == len(self.suppressions_proposees):
            print(f"\n🎉 NETTOYAGE TERMINÉ AVEC SUCCÈS !")
            print(f"✅ Tous les fichiers en double ont été supprimés")
            print(f"✅ Espace disque libéré")
            return True
        else:
            print(f"\n⚠️ NETTOYAGE PARTIEL")
            return False

def main():
    """Fonction principale de gestion des fichiers en double"""
    print("🗑️ GESTION DES FICHIERS EN DOUBLE")
    print("=" * 80)
    print("Ce script identifie et supprime les fichiers en double du projet")
    print("avec votre confirmation préalable.")
    
    gestionnaire = GestionnaireFichiersDoubles()
    
    # Étape 1: Identifier les doublons
    if not gestionnaire.identifier_fichiers_doubles():
        print("\n🎉 Aucun fichier en double trouvé !")
        print("✅ Le projet est déjà optimisé")
        return True
    
    # Étape 2: Analyser et proposer des suppressions
    gestionnaire.analyser_fichiers_doubles()
    
    # Étape 3: Demander confirmation
    if not gestionnaire.demander_confirmation_suppression():
        print("\n❌ Opération annulée par l'utilisateur")
        return False
    
    # Étape 4: Effectuer les suppressions
    gestionnaire.effectuer_suppressions()
    
    # Étape 5: Rapport final
    succes = gestionnaire.generer_rapport_final()
    
    return succes

if __name__ == "__main__":
    main()
