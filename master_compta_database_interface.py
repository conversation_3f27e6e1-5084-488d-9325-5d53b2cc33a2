#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Bases de Données Révolutionnaire
Bases performantes • IA Ollama • Import/Export • Détection anomalies
"""

import sys
import os
import json
import sqlite3
import requests
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QProgressBar, QTextEdit, QComboBox,
    QFileDialog, QMessageBox, QTabWidget, QGroupBox, QFormLayout,
    QLineEdit, QListWidget, QSplitter
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont

class OllamaAIWorker(QThread):
    """Worker thread pour les requêtes IA Ollama"""
    response_ready = Signal(str)
    error_occurred = Signal(str)
    
    def __init__(self, prompt, context=""):
        super().__init__()
        self.prompt = prompt
        self.context = context
        self.ollama_url = "http://localhost:11434"
    
    def run(self):
        try:
            payload = {
                "model": "llama3",
                "prompt": f"{self.context}\n\nQuestion: {self.prompt}",
                "stream": False
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.response_ready.emit(result.get('response', 'Pas de réponse'))
            else:
                self.error_occurred.emit(f"Erreur API: {response.status_code}")
                
        except Exception as e:
            self.error_occurred.emit(f"Erreur connexion: {str(e)}")

class DatabaseInterface(QMainWindow):
    """Interface principale du système de bases de données"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.check_ollama_status()
        
        # Timer pour vérifications automatiques
        self.auto_timer = QTimer()
        self.auto_timer.timeout.connect(self.auto_check)
        self.auto_timer.start(30000)  # 30 secondes
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("🗃️ MASTER COMPTA - Bases de Données Révolutionnaire")
        self.setGeometry(200, 100, 1400, 900)
        
        # Style principal
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0a0a0a, stop:0.5 #1a1a1a, stop:1 #0a0a0a);
                color: white;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #1a1a1a);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 15px;
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 2px solid #FFD700;
                border-radius: 8px;
                background: rgba(0,0,0,0.5);
            }
            QTabBar::tab {
                background: #2d2d2d;
                color: white;
                border: 1px solid #FFD700;
                padding: 8px 15px;
                margin: 2px;
            }
            QTabBar::tab:selected {
                background: #FFD700;
                color: black;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # Titre
        title = QLabel("🗃️ SYSTÈME DE BASES DE DONNÉES RÉVOLUTIONNAIRE")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 24pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 3px solid #FFD700;
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
            }
        """)
        main_layout.addWidget(title)
        
        # Onglets principaux
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)
        
        # Créer les onglets
        self.create_import_export_tab()
        self.create_anomaly_detection_tab()
        self.create_ai_analysis_tab()
        self.create_backup_tab()
        self.create_performance_tab()
    
    def create_import_export_tab(self):
        """Crée l'onglet Import/Export"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Section Import
        import_group = QGroupBox("📥 IMPORT DE DONNÉES")
        import_group.setStyleSheet("""
            QGroupBox {
                color: #FFD700;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #FFD700;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
            }
        """)
        import_layout = QVBoxLayout(import_group)
        
        # Sélection fichier
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("Sélectionnez un fichier...")
        self.file_path_edit.setStyleSheet("""
            QLineEdit {
                background: rgba(255,255,255,0.1);
                border: 2px solid #FFD700;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
            }
        """)
        file_layout.addWidget(self.file_path_edit)
        
        browse_btn = QPushButton("📁 Parcourir")
        browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(browse_btn)
        
        import_layout.addLayout(file_layout)
        
        # Format détecté
        self.format_label = QLabel("Format: Non détecté")
        self.format_label.setStyleSheet("color: #FFA500; font-size: 12pt;")
        import_layout.addWidget(self.format_label)
        
        # Barre de progression
        self.import_progress = QProgressBar()
        self.import_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #FFD700;
                border-radius: 6px;
                background: rgba(0,0,0,0.5);
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFD700, stop:1 #FFA500);
                border-radius: 4px;
            }
        """)
        import_layout.addWidget(self.import_progress)
        
        # Bouton import
        import_btn = QPushButton("🚀 IMPORTER DONNÉES")
        import_btn.clicked.connect(self.import_data)
        import_layout.addWidget(import_btn)
        
        layout.addWidget(import_group)
        
        # Section Export
        export_group = QGroupBox("📤 EXPORT DE DONNÉES")
        export_group.setStyleSheet("""
            QGroupBox {
                color: #FFD700;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #FFD700;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
            }
        """)
        export_layout = QVBoxLayout(export_group)
        
        # Sélection format export
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("Format:"))
        
        self.export_format = QComboBox()
        self.export_format.addItems([
            "Excel (.xlsx)",
            "CSV (.csv)", 
            "JSON (.json)",
            "SQLite (.db)",
            "PDF Rapport"
        ])
        self.export_format.setStyleSheet("""
            QComboBox {
                background: rgba(255,255,255,0.1);
                border: 2px solid #FFD700;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #FFD700;
            }
        """)
        format_layout.addWidget(self.export_format)
        export_layout.addLayout(format_layout)
        
        # Bouton export
        export_btn = QPushButton("💾 EXPORTER DONNÉES")
        export_btn.clicked.connect(self.export_data)
        export_layout.addWidget(export_btn)
        
        layout.addWidget(export_group)
        
        self.tabs.addTab(tab, "📥📤 Import/Export")
    
    def create_anomaly_detection_tab(self):
        """Crée l'onglet détection d'anomalies"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Titre
        title = QLabel("🔍 DÉTECTION D'ANOMALIES AUTOMATIQUE")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # Boutons d'analyse
        buttons_layout = QHBoxLayout()
        
        scan_btn = QPushButton("🔍 SCANNER ANOMALIES")
        scan_btn.clicked.connect(self.scan_anomalies)
        buttons_layout.addWidget(scan_btn)
        
        auto_fix_btn = QPushButton("🔧 CORRECTION AUTO")
        auto_fix_btn.clicked.connect(self.auto_fix_anomalies)
        buttons_layout.addWidget(auto_fix_btn)
        
        layout.addLayout(buttons_layout)
        
        # Liste des anomalies
        self.anomalies_list = QListWidget()
        self.anomalies_list.setStyleSheet("""
            QListWidget {
                background: rgba(0,0,0,0.5);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: white;
                font-size: 11pt;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255,215,0,0.3);
            }
            QListWidget::item:selected {
                background: rgba(255,215,0,0.3);
            }
        """)
        layout.addWidget(self.anomalies_list)
        
        self.tabs.addTab(tab, "🔍 Anomalies")
    
    def create_ai_analysis_tab(self):
        """Crée l'onglet analyse IA"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Statut Ollama
        self.ollama_status = QLabel("🤖 Vérification Ollama...")
        self.ollama_status.setStyleSheet("""
            QLabel {
                color: #FFA500;
                font-size: 14pt;
                font-weight: bold;
                background: rgba(255,165,0,0.1);
                border: 2px solid #FFA500;
                border-radius: 8px;
                padding: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(self.ollama_status)
        
        # Zone de requête
        query_group = QGroupBox("💬 REQUÊTE IA")
        query_group.setStyleSheet("""
            QGroupBox {
                color: #FFD700;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #FFD700;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
            }
        """)
        query_layout = QVBoxLayout(query_group)
        
        self.ai_query_edit = QTextEdit()
        self.ai_query_edit.setPlaceholderText("Posez votre question à l'IA...")
        self.ai_query_edit.setStyleSheet("""
            QTextEdit {
                background: rgba(255,255,255,0.1);
                border: 2px solid #FFD700;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
            }
        """)
        self.ai_query_edit.setMaximumHeight(100)
        query_layout.addWidget(self.ai_query_edit)
        
        ask_btn = QPushButton("🧠 DEMANDER À L'IA")
        ask_btn.clicked.connect(self.ask_ai)
        query_layout.addWidget(ask_btn)
        
        layout.addWidget(query_group)
        
        # Réponse IA
        response_group = QGroupBox("🤖 RÉPONSE IA")
        response_group.setStyleSheet("""
            QGroupBox {
                color: #FFD700;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #FFD700;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
            }
        """)
        response_layout = QVBoxLayout(response_group)
        
        self.ai_response = QTextEdit()
        self.ai_response.setReadOnly(True)
        self.ai_response.setStyleSheet("""
            QTextEdit {
                background: rgba(0,0,0,0.5);
                border: 2px solid #FFD700;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
            }
        """)
        response_layout.addWidget(self.ai_response)
        
        layout.addWidget(response_group)
        
        self.tabs.addTab(tab, "🤖 IA Ollama")
    
    def create_backup_tab(self):
        """Crée l'onglet sauvegardes"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Titre
        title = QLabel("💾 SYSTÈME DE SAUVEGARDES AUTOMATIQUES")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # Statut sauvegardes
        self.backup_status = QLabel("💾 Dernière sauvegarde: Jamais")
        self.backup_status.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                background: rgba(0,0,0,0.3);
                border: 2px solid #FFD700;
                border-radius: 6px;
                padding: 8px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.backup_status)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        backup_now_btn = QPushButton("💾 SAUVEGARDER MAINTENANT")
        backup_now_btn.clicked.connect(self.backup_now)
        buttons_layout.addWidget(backup_now_btn)
        
        restore_btn = QPushButton("🔄 RESTAURER")
        restore_btn.clicked.connect(self.restore_backup)
        buttons_layout.addWidget(restore_btn)
        
        layout.addLayout(buttons_layout)
        
        # Liste des sauvegardes
        self.backups_list = QListWidget()
        self.backups_list.setStyleSheet("""
            QListWidget {
                background: rgba(0,0,0,0.5);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: white;
                font-size: 11pt;
                padding: 5px;
            }
        """)
        layout.addWidget(self.backups_list)
        
        self.tabs.addTab(tab, "💾 Sauvegardes")
    
    def create_performance_tab(self):
        """Crée l'onglet performance"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Titre
        title = QLabel("⚡ MONITORING PERFORMANCE")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # Statistiques
        stats_layout = QHBoxLayout()
        
        # CPU
        cpu_group = QGroupBox("🖥️ CPU")
        cpu_layout = QVBoxLayout(cpu_group)
        self.cpu_label = QLabel("0%")
        self.cpu_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cpu_label.setStyleSheet("font-size: 18pt; color: #00FF00;")
        cpu_layout.addWidget(self.cpu_label)
        stats_layout.addWidget(cpu_group)
        
        # RAM
        ram_group = QGroupBox("💾 RAM")
        ram_layout = QVBoxLayout(ram_group)
        self.ram_label = QLabel("0%")
        self.ram_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.ram_label.setStyleSheet("font-size: 18pt; color: #00FF00;")
        ram_layout.addWidget(self.ram_label)
        stats_layout.addWidget(ram_group)
        
        # Disque
        disk_group = QGroupBox("💿 DISQUE")
        disk_layout = QVBoxLayout(disk_group)
        self.disk_label = QLabel("0%")
        self.disk_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.disk_label.setStyleSheet("font-size: 18pt; color: #00FF00;")
        disk_layout.addWidget(self.disk_label)
        stats_layout.addWidget(disk_group)
        
        layout.addLayout(stats_layout)
        
        # Bouton optimisation
        optimize_btn = QPushButton("🚀 OPTIMISER MAINTENANT")
        optimize_btn.clicked.connect(self.optimize_performance)
        layout.addWidget(optimize_btn)
        
        self.tabs.addTab(tab, "⚡ Performance")
    
    def check_ollama_status(self):
        """Vérifie le statut d'Ollama"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if response.status_code == 200:
                self.ollama_status.setText("🟢 Ollama connecté et prêt")
                self.ollama_status.setStyleSheet("""
                    QLabel {
                        color: #00FF00;
                        font-size: 14pt;
                        font-weight: bold;
                        background: rgba(0,255,0,0.1);
                        border: 2px solid #00FF00;
                        border-radius: 8px;
                        padding: 10px;
                        margin: 10px;
                    }
                """)
            else:
                self.ollama_status.setText("🔴 Ollama non disponible")
        except:
            self.ollama_status.setText("🔴 Ollama non installé - Installez avec: ollama run llama3")
            self.ollama_status.setStyleSheet("""
                QLabel {
                    color: #FF0000;
                    font-size: 14pt;
                    font-weight: bold;
                    background: rgba(255,0,0,0.1);
                    border: 2px solid #FF0000;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 10px;
                }
            """)
    
    def browse_file(self):
        """Ouvre le dialogue de sélection de fichier"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner un fichier de données",
            "",
            "Tous les fichiers (*.*);;Excel (*.xlsx *.xls);;CSV (*.csv);;JSON (*.json);;SQLite (*.db)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            # Détecter le format
            extension = Path(file_path).suffix.lower()
            if extension in ['.xlsx', '.xls']:
                self.format_label.setText("Format: Excel détecté ✅")
                self.format_label.setStyleSheet("color: #00FF00; font-size: 12pt;")
            elif extension == '.csv':
                self.format_label.setText("Format: CSV détecté ✅")
                self.format_label.setStyleSheet("color: #00FF00; font-size: 12pt;")
            elif extension == '.json':
                self.format_label.setText("Format: JSON détecté ✅")
                self.format_label.setStyleSheet("color: #00FF00; font-size: 12pt;")
            elif extension in ['.db', '.sqlite']:
                self.format_label.setText("Format: SQLite détecté ✅")
                self.format_label.setStyleSheet("color: #00FF00; font-size: 12pt;")
            else:
                self.format_label.setText("Format: Non reconnu ⚠️")
                self.format_label.setStyleSheet("color: #FFA500; font-size: 12pt;")
    
    def import_data(self):
        """Importe les données"""
        file_path = self.file_path_edit.text()
        if not file_path:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fichier")
            return
        
        # Simulation d'import avec barre de progression
        self.import_progress.setValue(0)
        
        # Timer pour simuler le progrès
        self.progress_timer = QTimer()
        self.progress_value = 0
        
        def update_progress():
            self.progress_value += 10
            self.import_progress.setValue(self.progress_value)
            if self.progress_value >= 100:
                self.progress_timer.stop()
                QMessageBox.information(self, "Succès", f"Données importées avec succès depuis:\n{file_path}")
        
        self.progress_timer.timeout.connect(update_progress)
        self.progress_timer.start(200)
    
    def export_data(self):
        """Exporte les données"""
        format_text = self.export_format.currentText()
        QMessageBox.information(self, "Export", f"Export en cours au format: {format_text}")
    
    def scan_anomalies(self):
        """Scanne les anomalies"""
        self.anomalies_list.clear()
        
        # Simulation d'anomalies détectées
        anomalies = [
            "🔴 Valeurs manquantes détectées: 15 lignes",
            "🟡 Doublons trouvés: 3 entrées",
            "🟢 Format de date incohérent: 2 champs",
            "🔴 Valeurs aberrantes: 8 points",
            "🟡 Contraintes violées: 1 règle"
        ]
        
        for anomaly in anomalies:
            self.anomalies_list.addItem(anomaly)
        
        QMessageBox.information(self, "Scan terminé", f"{len(anomalies)} anomalies détectées")
    
    def auto_fix_anomalies(self):
        """Correction automatique des anomalies"""
        QMessageBox.information(self, "Correction", "Correction automatique appliquée ✅")
        self.anomalies_list.clear()
    
    def ask_ai(self):
        """Pose une question à l'IA"""
        query = self.ai_query_edit.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir une question")
            return
        
        self.ai_response.setText("🤖 L'IA réfléchit...")
        
        # Créer le worker thread
        self.ai_worker = OllamaAIWorker(query, "Tu es un expert en bases de données.")
        self.ai_worker.response_ready.connect(self.on_ai_response)
        self.ai_worker.error_occurred.connect(self.on_ai_error)
        self.ai_worker.start()
    
    def on_ai_response(self, response):
        """Traite la réponse de l'IA"""
        self.ai_response.setText(f"🤖 Réponse IA:\n\n{response}")
    
    def on_ai_error(self, error):
        """Traite les erreurs de l'IA"""
        self.ai_response.setText(f"❌ Erreur: {error}")
    
    def backup_now(self):
        """Crée une sauvegarde maintenant"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.backup_status.setText(f"💾 Dernière sauvegarde: {timestamp}")
        self.backups_list.addItem(f"Sauvegarde {timestamp}")
        QMessageBox.information(self, "Sauvegarde", "Sauvegarde créée avec succès ✅")
    
    def restore_backup(self):
        """Restaure une sauvegarde"""
        current_item = self.backups_list.currentItem()
        if current_item:
            QMessageBox.information(self, "Restauration", f"Restauration de: {current_item.text()}")
        else:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une sauvegarde à restaurer")
    
    def optimize_performance(self):
        """Optimise les performances"""
        QMessageBox.information(self, "Optimisation", "Optimisation des performances en cours... ⚡")
        
        # Simulation des stats
        self.cpu_label.setText("25%")
        self.ram_label.setText("45%")
        self.disk_label.setText("60%")
    
    def auto_check(self):
        """Vérifications automatiques"""
        # Mise à jour du statut de sauvegarde
        if not hasattr(self, 'last_auto_backup'):
            self.last_auto_backup = datetime.now()
        
        # Sauvegarde auto toutes les 30 secondes (pour demo)
        if (datetime.now() - self.last_auto_backup).seconds > 30:
            self.last_auto_backup = datetime.now()
            timestamp = self.last_auto_backup.strftime("%Y-%m-%d %H:%M:%S")
            self.backup_status.setText(f"💾 Sauvegarde auto: {timestamp}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = DatabaseInterface()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
