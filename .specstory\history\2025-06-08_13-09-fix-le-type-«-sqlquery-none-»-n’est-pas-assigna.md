<!-- Generated by SpecStory -->

# /fix Le type « SQLQuery | None » n’est pas assigna (2025-06-08 14:09:14)

_**User**_

/fix Le type « SQLQuery | None » n’est pas assignable au type de retour « SQLQuery »
  Le type « SQLQuery | None » n’est pas assignable au type « SQLQuery »
    « None » n’est pas assignable à « SQLQuery »

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Le problème vient du fait que `self.queries.get(query_name.upper())` peut retourner `None` si la clé n'existe pas, alors que la signature de la méthode indique un retour de type `SQLQuery`.  
Pour corriger cela, il faut soit :
- lever une exception si la requête n'existe pas (solution la plus sûre)
- ou changer le type de retour pour accepter `Optional[SQLQuery]` (moins strict, mais pas demandé ici)

Voici la correction en levant une exception si la requête n'est pas trouvée.

````python
# ...existing code...
    def get_query(self, query_name: str) -> SQLQuery:
        query = self.queries.get(query_name.upper())
        if query is None:
            raise ValueError(f"Query '{query_name}' not found")
        return query
# ...existing code...
````

---

