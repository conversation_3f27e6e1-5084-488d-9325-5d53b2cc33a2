#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnostic et correction complète pour main.py
Utilise tous les moyens nécessaires pour identifier et corriger TOUS les problèmes
"""

import ast
import re
import sys
import os
import subprocess
from typing import List, Dict, Any, Tuple

def run_python_syntax_check():
    """Exécute une vérification de syntaxe Python directe"""
    print("🔍 Vérification syntaxe Python directe...")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', 'src/main.py'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Compilation Python réussie")
            return True, []
        else:
            print(f"❌ Erreurs de compilation:")
            errors = result.stderr.split('\n')
            for error in errors:
                if error.strip():
                    print(f"  - {error}")
            return False, errors
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False, [str(e)]

def analyze_ast_detailed():
    """Analyse AST détaillée pour identifier tous les problèmes"""
    print("\n🔍 Analyse AST détaillée...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        issues = []
        
        class ProblemFinder(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Vérifier les annotations de retour manquantes
                if node.returns is None and node.name != '__init__':
                    issues.append(f"Ligne {node.lineno}: Fonction '{node.name}' sans annotation de retour")
                
                # Vérifier les paramètres sans annotations
                for arg in node.args.args:
                    if arg.annotation is None and arg.arg != 'self':
                        issues.append(f"Ligne {node.lineno}: Paramètre '{arg.arg}' sans annotation de type")
                
                self.generic_visit(node)
            
            def visit_Assign(self, node):
                # Vérifier les assignations sans type
                if isinstance(node.targets[0], ast.Name):
                    var_name = node.targets[0].id
                    if not var_name.startswith('_') and var_name.islower():
                        issues.append(f"Ligne {node.lineno}: Variable '{var_name}' sans annotation de type")
                self.generic_visit(node)
            
            def visit_Import(self, node):
                # Vérifier les imports problématiques
                for alias in node.names:
                    if alias.name in ['typing', 'types']:
                        issues.append(f"Ligne {node.lineno}: Import potentiellement problématique: {alias.name}")
                self.generic_visit(node)
        
        finder = ProblemFinder()
        finder.visit(tree)
        
        if issues:
            print(f"⚠️ {len(issues)} problèmes AST détectés:")
            for issue in issues[:10]:  # Limiter à 10
                print(f"  - {issue}")
        else:
            print("✅ Aucun problème AST détecté")
        
        return len(issues) == 0, issues
        
    except SyntaxError as e:
        error_msg = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
        print(f"❌ {error_msg}")
        return False, [error_msg]
    except Exception as e:
        error_msg = f"Erreur analyse AST: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_undefined_names():
    """Vérifie les noms non définis en utilisant l'analyse statique"""
    print("\n🔍 Vérification des noms non définis...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Utiliser compile() avec mode 'exec' pour détecter les erreurs
        try:
            compile(content, 'src/main.py', 'exec')
            print("✅ Aucun nom non défini détecté")
            return True, []
        except NameError as e:
            error_msg = f"Nom non défini: {e}"
            print(f"❌ {error_msg}")
            return False, [error_msg]
        except Exception as e:
            error_msg = f"Erreur de compilation: {e}"
            print(f"❌ {error_msg}")
            return False, [error_msg]
            
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_import_issues():
    """Vérifie les problèmes d'imports spécifiques"""
    print("\n🔍 Vérification des problèmes d'imports...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        in_function = False
        current_function = ""
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Détecter les fonctions/méthodes
            if stripped.startswith('def '):
                in_function = True
                current_function = stripped.split('(')[0].replace('def ', '')
            elif stripped.startswith('class ') or (not stripped.startswith(' ') and stripped and not stripped.startswith('#')):
                in_function = False
            
            # Vérifier les imports dans les fonctions
            if in_function and ('import ' in stripped) and not stripped.startswith('#'):
                if 'type: ignore' not in stripped:
                    issues.append(f"Ligne {i}: Import dans fonction '{current_function}' sans type: ignore")
            
            # Vérifier les imports circulaires potentiels
            if 'from main import' in stripped:
                issues.append(f"Ligne {i}: Import circulaire potentiel")
        
        if issues:
            print(f"⚠️ {len(issues)} problèmes d'imports:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("✅ Imports corrects")
        
        return len(issues) == 0, issues
        
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_type_annotations():
    """Vérifie la complétude des annotations de type"""
    print("\n🔍 Vérification des annotations de type...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Compter les fonctions avec et sans annotations
        function_pattern = r'def\s+(\w+)\s*\([^)]*\)\s*(->\s*[^:]+)?:'
        functions = re.findall(function_pattern, content)
        
        total_functions = len(functions)
        annotated_functions = len([f for f in functions if f[1]])
        
        # Compter les variables avec annotations
        var_pattern = r'(\w+)\s*:\s*\w+'
        typed_vars = len(re.findall(var_pattern, content))
        
        # Compter les assignations sans type
        assign_pattern = r'^\s*(\w+)\s*=\s*[^=]'
        untyped_assignments = []
        
        lines = content.splitlines()
        for i, line in enumerate(lines, 1):
            if re.match(assign_pattern, line):
                var_name = re.match(assign_pattern, line).group(1)
                if not any(x in line for x in ['self.', 'def ', 'class ', 'import', 'from', '#']):
                    if ':' not in line.split('=')[0]:
                        untyped_assignments.append(f"Ligne {i}: Variable '{var_name}' non typée")
        
        print(f"📊 Annotations de type:")
        print(f"  - Fonctions annotées: {annotated_functions}/{total_functions}")
        print(f"  - Variables typées: {typed_vars}")
        print(f"  - Assignations non typées: {len(untyped_assignments)}")
        
        if untyped_assignments:
            print(f"⚠️ Variables non typées détectées:")
            for issue in untyped_assignments[:5]:
                print(f"  - {issue}")
        
        success = (annotated_functions / total_functions >= 0.9) and (len(untyped_assignments) <= 5)
        
        if success:
            print("✅ Annotations de type satisfaisantes")
        else:
            print("⚠️ Annotations de type insuffisantes")
        
        return success, untyped_assignments
        
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def run_comprehensive_analysis():
    """Exécute une analyse complète et retourne tous les problèmes"""
    print("🚀 ANALYSE COMPLÈTE - Identification de TOUS les Problèmes")
    print("=" * 70)
    
    all_issues = []
    checks = [
        ("Syntaxe Python", run_python_syntax_check),
        ("Analyse AST détaillée", analyze_ast_detailed),
        ("Noms non définis", check_undefined_names),
        ("Problèmes d'imports", check_import_issues),
        ("Annotations de type", check_type_annotations)
    ]
    
    passed_checks = 0
    
    for check_name, check_func in checks:
        try:
            success, issues = check_func()
            if success:
                passed_checks += 1
            else:
                all_issues.extend(issues)
        except Exception as e:
            error_msg = f"Erreur dans {check_name}: {e}"
            print(f"❌ {error_msg}")
            all_issues.append(error_msg)
    
    # Résumé final
    print("\n" + "="*70)
    print("📋 RÉSUMÉ DE L'ANALYSE COMPLÈTE")
    print("="*70)
    
    print(f"✅ Vérifications réussies: {passed_checks}/{len(checks)}")
    print(f"❌ Problèmes totaux détectés: {len(all_issues)}")
    
    if all_issues:
        print("\n🔍 TOUS LES PROBLÈMES IDENTIFIÉS:")
        for i, issue in enumerate(all_issues, 1):
            print(f"{i:3d}. {issue}")
    
    if len(all_issues) == 0:
        print("\n🎉 AUCUN PROBLÈME DÉTECTÉ !")
        print("✅ Le fichier main.py est parfaitement correct")
        return True
    else:
        print(f"\n⚠️ {len(all_issues)} problèmes nécessitent une correction")
        return False

def main():
    """Fonction principale"""
    if not os.path.exists('src/main.py'):
        print("❌ Fichier src/main.py non trouvé")
        return 1
    
    success = run_comprehensive_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
