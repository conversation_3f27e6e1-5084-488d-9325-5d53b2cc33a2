#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic précis pour identifier les problèmes exacts dans main.py
"""

import ast
import re
import sys
import os
from typing import List, <PERSON><PERSON>

def check_syntax_errors():
    """Vérification des erreurs de syntaxe"""
    print("🔍 Vérification des erreurs de syntaxe...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test de compilation
        compile(content, 'src/main.py', 'exec')
        print("✅ Compilation réussie")
        
        # Test AST
        tree = ast.parse(content)
        print("✅ AST parsing réussi")
        
        return True, []
        
    except SyntaxError as e:
        error_msg = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
        print(f"❌ {error_msg}")
        return False, [error_msg]
    except Exception as e:
        error_msg = f"Erreur compilation: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_undefined_variables():
    """Vérification des variables non définies"""
    print("\n🔍 Vérification des variables non définies...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Variables utilisées mais potentiellement non définies
        issues = []
        
        # Chercher les variables utilisées dans le code
        lines = content.splitlines()
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Ignorer les commentaires et les chaînes
            if stripped.startswith('#') or '"""' in stripped or "'''" in stripped:
                continue
            
            # Chercher des patterns problématiques
            if 'undefined' in stripped.lower():
                issues.append(f"Ligne {i}: Référence à 'undefined' - {stripped}")
            
            # Chercher des variables qui pourraient ne pas être définies
            if re.search(r'\b[a-z_][a-z0-9_]*\s*\.\s*[a-z_][a-z0-9_]*', stripped):
                # Vérifier si c'est une variable locale non définie
                if not any(x in stripped for x in ['self.', 'import', 'from', 'def ', 'class ', '=', 'if ', 'for ', 'while ']):
                    # Cas potentiellement problématique
                    pass
        
        if issues:
            print(f"⚠️ {len(issues)} variables potentiellement non définies:")
            for issue in issues:
                print(f"  - {issue}")
            return False, issues
        else:
            print("✅ Aucune variable non définie détectée")
            return True, []
            
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_import_errors():
    """Vérification des erreurs d'import"""
    print("\n🔍 Vérification des erreurs d'import...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        lines = content.splitlines()
        
        # Vérifier les imports circulaires ou problématiques
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            if 'import' in stripped and not stripped.startswith('#'):
                # Vérifier les imports dans les fonctions sans type: ignore
                if '    ' in line and 'type: ignore' not in stripped:
                    # C'est un import dans une fonction/méthode
                    if not any(x in stripped for x in ['from typing', 'import typing']):
                        issues.append(f"Ligne {i}: Import dans fonction sans type: ignore - {stripped}")
        
        if issues:
            print(f"⚠️ {len(issues)} problèmes d'import:")
            for issue in issues:
                print(f"  - {issue}")
            return False, issues
        else:
            print("✅ Imports corrects")
            return True, []
            
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_indentation_errors():
    """Vérification des erreurs d'indentation"""
    print("\n🔍 Vérification des erreurs d'indentation...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        
        for i, line in enumerate(lines, 1):
            # Vérifier les mélanges tabs/espaces
            if '\t' in line and '    ' in line:
                issues.append(f"Ligne {i}: Mélange tabs/espaces")
            
            # Vérifier les indentations incorrectes
            if line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                # Ligne au niveau racine
                continue
            elif line.strip():
                # Vérifier que l'indentation est multiple de 4
                leading_spaces = len(line) - len(line.lstrip(' '))
                if leading_spaces % 4 != 0 and leading_spaces > 0:
                    issues.append(f"Ligne {i}: Indentation incorrecte ({leading_spaces} espaces)")
        
        if issues:
            print(f"⚠️ {len(issues)} problèmes d'indentation:")
            for issue in issues[:5]:  # Limiter à 5
                print(f"  - {issue}")
            return False, issues
        else:
            print("✅ Indentation correcte")
            return True, []
            
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def check_missing_imports():
    """Vérification des imports manquants"""
    print("\n🔍 Vérification des imports manquants...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Classes Qt utilisées
        qt_classes_used = set(re.findall(r'\b(Q\w+)\(', content))
        
        # Imports Qt déclarés
        qt_imports = set()
        for match in re.finditer(r'from PyQt6\.\w+ import (.+)', content):
            imports = match.group(1).split(',')
            for imp in imports:
                qt_imports.add(imp.strip())
        
        # Classes manquantes
        missing_qt = qt_classes_used - qt_imports
        
        issues = []
        for missing in missing_qt:
            if missing not in ['QApplication', 'QWidget']:  # Ignorer les imports de base
                issues.append(f"Classe Qt manquante: {missing}")
        
        if issues:
            print(f"⚠️ {len(issues)} imports manquants:")
            for issue in issues[:5]:
                print(f"  - {issue}")
            return False, issues
        else:
            print("✅ Imports complets")
            return True, []
            
    except Exception as e:
        error_msg = f"Erreur lors de la vérification: {e}"
        print(f"❌ {error_msg}")
        return False, [error_msg]

def main():
    """Fonction principale de diagnostic"""
    print("🚀 DIAGNOSTIC PRÉCIS - Identification des Problèmes Exacts")
    print("=" * 65)
    
    checks = [
        ("Erreurs de syntaxe", check_syntax_errors),
        ("Variables non définies", check_undefined_variables),
        ("Erreurs d'import", check_import_errors),
        ("Erreurs d'indentation", check_indentation_errors),
        ("Imports manquants", check_missing_imports)
    ]
    
    all_issues = []
    passed_checks = 0
    
    for check_name, check_func in checks:
        try:
            success, issues = check_func()
            if success:
                passed_checks += 1
            else:
                all_issues.extend(issues)
        except Exception as e:
            print(f"❌ Erreur dans {check_name}: {e}")
            all_issues.append(f"Erreur dans {check_name}: {e}")
    
    # Résumé final
    print("\n" + "="*65)
    print("📋 RÉSUMÉ DU DIAGNOSTIC")
    print("="*65)
    
    print(f"✅ Vérifications réussies: {passed_checks}/{len(checks)}")
    print(f"❌ Problèmes détectés: {len(all_issues)}")
    
    if all_issues:
        print("\n🔍 PROBLÈMES IDENTIFIÉS:")
        for i, issue in enumerate(all_issues[:10], 1):  # Limiter à 10
            print(f"{i:2d}. {issue}")
        
        if len(all_issues) > 10:
            print(f"... et {len(all_issues) - 10} autres problèmes")
    
    if len(all_issues) == 0:
        print("\n🎉 AUCUN PROBLÈME DÉTECTÉ !")
        print("✅ Le fichier main.py semble correct")
        return 0
    elif len(all_issues) <= 3:
        print("\n✅ Quelques problèmes mineurs détectés")
        return 0
    else:
        print("\n⚠️ Plusieurs problèmes nécessitent une correction")
        return 1

if __name__ == "__main__":
    sys.exit(main())
