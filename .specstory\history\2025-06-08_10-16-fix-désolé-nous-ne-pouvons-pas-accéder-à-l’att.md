<!-- Generated by SpecStory -->

# /fix Désolé... Nous ne pouvons pas accéder à l’att (2025-06-08 11:16:56)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « setOrientation » pour la classe « QPrinter »
  L’attribut « setOrientation » est inconnu, Le type de « setOrientation » est inconnu

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Remplacer `printer.setOrientation(...)` par `printer.setOrientation(...)` (notez que dans PySide6, l'attribut est directement `printer.setOrientation(QPrinter.Landscape)` ou `printer.setOrientation(QPrinter.Portrait)` sans passer par `.Orientation`). Voici la correction :

````python
        # ...existing code...
        elif fmt == "Paysage":
            printer.setOrientation(QPrinter.Landscape)
        elif fmt == "Portrait":
            printer.setOrientation(QPrinter.Portrait)
        # ...existing code...
````

---

