
-- Script de vérification après annulation du clonage
-- Exécutez ce script pour vérifier l'état de la base

SELECT 'Tables principales' as verification, COUNT(*) as nombre
FROM sqlite_master 
WHERE type='table' AND name IN ('immobilisations', 'fournisseurs', 'utilisateurs')

UNION ALL

SELECT 'Secteurs personnalisés' as verification, COUNT(*) as nombre
FROM secteurs_personnalises

UNION ALL

SELECT 'Tables de clonage restantes' as verification, COUNT(*) as nombre
FROM sqlite_master 
WHERE type='table' AND name LIKE '%clone%'

UNION ALL

SELECT 'Immobilisations' as verification, COUNT(*) as nombre
FROM immobilisations;
