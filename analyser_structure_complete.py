#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyse complète de la structure du projet GestImmob
"""

import os
import sys
import hashlib
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Set, Tuple

class AnalyseurStructure:
    def __init__(self):
        self.fichiers_analyses = []
        self.fichiers_doubles = defaultdict(list)
        self.structure_actuelle = {}
        self.structure_recommandee = {}
        self.outils_manquants = []
        self.dependances_manquantes = []
        
    def calculer_hash_fichier(self, file_path: str) -> str:
        """Calcule le hash MD5 d'un fichier"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return ""
    
    def analyser_fichiers_doubles(self):
        """Identifie les fichiers en double"""
        print("🔍 ANALYSE DES FICHIERS EN DOUBLE")
        print("=" * 50)
        
        hash_to_files = defaultdict(list)
        
        for root, dirs, files in os.walk('.'):
            # Ignorer certains répertoires
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith(('.py', '.sql', '.json', '.txt', '.md', '.bat')):
                    file_path = os.path.join(root, file)
                    file_hash = self.calculer_hash_fichier(file_path)
                    if file_hash:
                        hash_to_files[file_hash].append(file_path)
        
        # Identifier les doublons
        for file_hash, file_list in hash_to_files.items():
            if len(file_list) > 1:
                self.fichiers_doubles[file_hash] = file_list
                print(f"🔄 Fichiers identiques trouvés:")
                for file_path in file_list:
                    size = os.path.getsize(file_path)
                    print(f"   - {file_path} ({size} bytes)")
                print()
    
    def analyser_structure_actuelle(self):
        """Analyse la structure actuelle du projet"""
        print("📁 ANALYSE DE LA STRUCTURE ACTUELLE")
        print("=" * 50)
        
        structure = {}
        
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__']]
            
            level = root.replace('.', '').count(os.sep)
            indent = ' ' * 2 * level
            folder_name = os.path.basename(root) or '.'
            
            if level == 0:
                print(f"{indent}{folder_name}/")
                structure[folder_name] = {'dirs': dirs.copy(), 'files': []}
            elif level <= 3:  # Limiter la profondeur
                print(f"{indent}{folder_name}/")
            
            # Analyser les fichiers
            python_files = [f for f in files if f.endswith('.py')]
            sql_files = [f for f in files if f.endswith('.sql')]
            config_files = [f for f in files if f.endswith(('.json', '.ini', '.cfg', '.conf'))]
            other_files = [f for f in files if not f.endswith(('.py', '.sql', '.json', '.ini', '.cfg', '.conf', '.pyc'))]
            
            if level <= 2:
                if python_files:
                    print(f"{indent}  📄 Python: {len(python_files)} fichiers")
                    for f in python_files[:5]:  # Limiter l'affichage
                        print(f"{indent}    - {f}")
                    if len(python_files) > 5:
                        print(f"{indent}    ... et {len(python_files) - 5} autres")
                
                if sql_files:
                    print(f"{indent}  🗄️ SQL: {len(sql_files)} fichiers")
                    for f in sql_files:
                        print(f"{indent}    - {f}")
                
                if config_files:
                    print(f"{indent}  ⚙️ Config: {len(config_files)} fichiers")
                    for f in config_files:
                        print(f"{indent}    - {f}")
                
                if other_files:
                    print(f"{indent}  📋 Autres: {len(other_files)} fichiers")
        
        self.structure_actuelle = structure
    
    def definir_structure_recommandee(self):
        """Définit la structure recommandée pour le projet"""
        print("\n📋 STRUCTURE RECOMMANDÉE")
        print("=" * 50)
        
        structure_recommandee = {
            "📁 gestion-software/": {
                "📁 src/": {
                    "📄 main.py": "Point d'entrée principal",
                    "📄 config.py": "Configuration globale",
                    "📁 core/": {
                        "📄 __init__.py": "Module core",
                        "📄 app.py": "Application principale",
                        "📄 constants.py": "Constantes globales",
                        "📄 exceptions.py": "Exceptions personnalisées"
                    },
                    "📁 database/": {
                        "📄 __init__.py": "Module database",
                        "📄 db_connection.py": "Connexion base de données",
                        "📄 models.py": "Modèles ORM",
                        "📁 migrations/": {
                            "📄 init.sql": "Schema initial SQLite"
                        }
                    },
                    "📁 auth/": {
                        "📄 __init__.py": "Module authentification",
                        "📄 authentication.py": "Logique d'authentification",
                        "📄 user_management.py": "Gestion utilisateurs",
                        "📄 encryption.py": "Chiffrement"
                    },
                    "📁 views/": {
                        "📄 __init__.py": "Module vues",
                        "📄 admin_view.py": "Interface admin",
                        "📄 main_window.py": "Fenêtre principale"
                    },
                    "📁 modules/": {
                        "📄 __init__.py": "Module métier",
                        "📄 inventaire.py": "Gestion inventaire",
                        "📄 fournisseur.py": "Gestion fournisseurs",
                        "📄 document.py": "Gestion documents",
                        "📄 recherche.py": "Recherche avancée",
                        "📄 reforme.py": "Gestion réformes"
                    },
                    "📁 utils/": {
                        "📄 __init__.py": "Utilitaires",
                        "📄 config.py": "Utilitaires config",
                        "📄 helpers.py": "Fonctions d'aide",
                        "📄 validators.py": "Validateurs"
                    }
                },
                "📁 config/": {
                    "📄 settings.json": "Paramètres application",
                    "📄 database.json": "Configuration BDD",
                    "📄 logging.json": "Configuration logs"
                },
                "📁 data/": {
                    "📄 users.db": "Base de données SQLite",
                    "📁 backups/": "Sauvegardes",
                    "📁 exports/": "Exports"
                },
                "📁 docs/": {
                    "📄 README.md": "Documentation",
                    "📄 INSTALL.md": "Guide installation",
                    "📄 USER_GUIDE.md": "Guide utilisateur"
                },
                "📁 tools/": {
                    "📄 setup.py": "Installation",
                    "📄 requirements.txt": "Dépendances Python",
                    "📁 scripts/": "Scripts utilitaires"
                },
                "📄 main.py": "Lanceur principal",
                "📄 requirements.txt": "Dépendances",
                "📄 setup.py": "Installation",
                "📄 README.md": "Documentation"
            }
        }
        
        def afficher_structure(struct, indent=0):
            for key, value in struct.items():
                print("  " * indent + key)
                if isinstance(value, dict):
                    afficher_structure(value, indent + 1)
                else:
                    print("  " * (indent + 1) + f"→ {value}")
        
        afficher_structure(structure_recommandee)
        self.structure_recommandee = structure_recommandee
    
    def analyser_dependances(self):
        """Analyse les dépendances et outils manquants"""
        print("\n🔧 ANALYSE DES DÉPENDANCES ET OUTILS")
        print("=" * 50)
        
        # Dépendances Python requises
        dependances_requises = {
            'PySide6': 'Interface graphique Qt',
            'sqlalchemy': 'ORM base de données',
            'cryptography': 'Chiffrement sécurisé',
            'pillow': 'Traitement d\'images',
            'matplotlib': 'Graphiques et statistiques',
            'pandas': 'Manipulation de données',
            'openpyxl': 'Export Excel',
            'reportlab': 'Génération PDF',
            'qrcode': 'Génération QR codes',
            'python-barcode': 'Génération codes-barres'
        }
        
        print("📦 Vérification des dépendances Python:")
        for package, description in dependances_requises.items():
            try:
                __import__(package.replace('-', '_'))
                print(f"  ✅ {package}: Installé - {description}")
            except ImportError:
                print(f"  ❌ {package}: MANQUANT - {description}")
                self.dependances_manquantes.append(package)
        
        # Outils système
        outils_systeme = {
            'git': 'Contrôle de version',
            'sqlite3': 'Base de données SQLite',
            'python': 'Interpréteur Python'
        }
        
        print(f"\n🛠️ Vérification des outils système:")
        for outil, description in outils_systeme.items():
            if os.system(f"{outil} --version >nul 2>&1") == 0:
                print(f"  ✅ {outil}: Disponible - {description}")
            else:
                print(f"  ❌ {outil}: MANQUANT - {description}")
                self.outils_manquants.append(outil)
    
    def analyser_fichiers_config(self):
        """Analyse les fichiers de configuration"""
        print(f"\n⚙️ ANALYSE DES FICHIERS DE CONFIGURATION")
        print("=" * 50)
        
        configs_attendus = {
            'config.json': 'Configuration principale',
            'settings.json': 'Paramètres utilisateur',
            'database.json': 'Configuration base de données',
            'logging.json': 'Configuration logs',
            'requirements.txt': 'Dépendances Python',
            'setup.py': 'Script d\'installation'
        }
        
        for config_file, description in configs_attendus.items():
            if os.path.exists(config_file) or os.path.exists(f'config/{config_file}'):
                print(f"  ✅ {config_file}: Présent - {description}")
            else:
                print(f"  ❌ {config_file}: MANQUANT - {description}")
    
    def generer_rapport_complet(self):
        """Génère un rapport complet d'analyse"""
        print(f"\n📊 RAPPORT COMPLET D'ANALYSE")
        print("=" * 50)
        
        print(f"🔄 Fichiers en double: {len(self.fichiers_doubles)}")
        print(f"❌ Dépendances manquantes: {len(self.dependances_manquantes)}")
        print(f"🛠️ Outils manquants: {len(self.outils_manquants)}")
        
        if self.fichiers_doubles:
            print(f"\n🗑️ FICHIERS À SUPPRIMER (après confirmation):")
            for file_hash, file_list in self.fichiers_doubles.items():
                print(f"  Groupe de doublons:")
                for i, file_path in enumerate(file_list):
                    if i == 0:
                        print(f"    ✅ GARDER: {file_path}")
                    else:
                        print(f"    🗑️ SUPPRIMER: {file_path}")
        
        if self.dependances_manquantes:
            print(f"\n📦 COMMANDES D'INSTALLATION:")
            print(f"  pip install {' '.join(self.dependances_manquantes)}")
        
        return {
            'fichiers_doubles': len(self.fichiers_doubles),
            'dependances_manquantes': len(self.dependances_manquantes),
            'outils_manquants': len(self.outils_manquants)
        }

def main():
    """Fonction principale d'analyse"""
    print("🔍 ANALYSE COMPLÈTE DE LA STRUCTURE DU PROJET")
    print("=" * 80)
    print(f"Répertoire: {os.getcwd()}")
    
    analyseur = AnalyseurStructure()
    
    # Exécuter toutes les analyses
    analyseur.analyser_structure_actuelle()
    analyseur.definir_structure_recommandee()
    analyseur.analyser_fichiers_doubles()
    analyseur.analyser_dependances()
    analyseur.analyser_fichiers_config()
    
    # Générer le rapport final
    rapport = analyseur.generer_rapport_complet()
    
    print(f"\n🎯 ACTIONS RECOMMANDÉES:")
    print("1. Réorganiser la structure selon les recommandations")
    print("2. Installer les dépendances manquantes")
    print("3. Supprimer les fichiers en double (après confirmation)")
    print("4. Créer les fichiers de configuration manquants")
    print("5. Ajouter les outils système manquants")
    
    return rapport

if __name__ == "__main__":
    main()
