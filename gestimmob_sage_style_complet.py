#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GestImmob - Interface Style Sage Immobilisation Complète
Préserve TOUS vos critères existants + amélioration interface
Fond bleu turquoise + style professionnel
"""

import sys
import sqlite3
import json
import math
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QFrame,
    QScrollArea, QGridLayout, QComboBox, QLineEdit, QTextEdit,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QDateEdit,
    QCheckBox, QProgressBar, QSystemTrayIcon, QMenu, QSplitter,
    QTabWidget, QGroupBox, QSlider, QToolTip, QMenuBar, QStatusBar,
    QToolBar, QMessageBox, QFileDialog, QInputDialog
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QPropertyAnimation, QEasingCurve, QSize
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon, QAction, QPalette

class CalculatriceAvancee(QDialog):
    """Calculatrice avancée inspirée d'internet avec fonctions scientifiques"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🧮 Calculatrice Avancée GestImmob")
        self.setFixedSize(400, 600)
        self.setup_calculatrice()

    def setup_calculatrice(self):
        """Configure l'interface de la calculatrice"""
        layout = QVBoxLayout(self)

        # Style bleu turquoise pour calculatrice
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40E0D0, stop:1 #20B2AA);
                border: 2px solid #008B8B;
                border-radius: 10px;
            }
            QLineEdit {
                background-color: #000000;
                color: #00FFFF;
                font-size: 24pt;
                font-weight: bold;
                border: 2px solid #008B8B;
                border-radius: 8px;
                padding: 10px;
                text-align: right;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48D1CC, stop:1 #20B2AA);
                color: white;
                border: 2px solid #008B8B;
                border-radius: 8px;
                font-size: 14pt;
                font-weight: bold;
                min-height: 50px;
                min-width: 70px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FFFF, stop:1 #40E0D0);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #008B8B, stop:1 #006666);
            }
        """)

        # Écran d'affichage
        self.display = QLineEdit("0")
        self.display.setReadOnly(True)
        self.display.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout.addWidget(self.display)

        # Variables de calcul
        self.current_value = 0
        self.pending_value = 0
        self.pending_operation = ""
        self.wait_for_operand = True

        # Boutons de la calculatrice
        self.create_calculator_buttons(layout)

    def create_calculator_buttons(self, layout):
        """Crée les boutons de la calculatrice"""

        # Boutons scientifiques
        scientific_layout = QGridLayout()

        scientific_buttons = [
            ("sin", 0, 0), ("cos", 0, 1), ("tan", 0, 2), ("log", 0, 3),
            ("ln", 1, 0), ("√", 1, 1), ("x²", 1, 2), ("x³", 1, 3),
            ("π", 2, 0), ("e", 2, 1), ("1/x", 2, 2), ("x^y", 2, 3)
        ]

        for text, row, col in scientific_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, t=text: self.scientific_operation(t))
            scientific_layout.addWidget(btn, row, col)

        scientific_frame = QFrame()
        scientific_frame.setLayout(scientific_layout)
        layout.addWidget(scientific_frame)

        # Boutons numériques et opérations
        main_layout = QGridLayout()

        # Ligne 1: Fonctions
        buttons_row1 = [
            ("C", self.clear_all), ("CE", self.clear_entry),
            ("⌫", self.backspace), ("±", self.change_sign)
        ]

        for i, (text, func) in enumerate(buttons_row1):
            btn = QPushButton(text)
            btn.clicked.connect(func)
            main_layout.addWidget(btn, 0, i)

        # Lignes 2-5: Chiffres et opérations
        buttons = [
            [("7", lambda: self.digit_clicked("7")), ("8", lambda: self.digit_clicked("8")),
             ("9", lambda: self.digit_clicked("9")), ("÷", lambda: self.operation_clicked("÷"))],
            [("4", lambda: self.digit_clicked("4")), ("5", lambda: self.digit_clicked("5")),
             ("6", lambda: self.digit_clicked("6")), ("×", lambda: self.operation_clicked("×"))],
            [("1", lambda: self.digit_clicked("1")), ("2", lambda: self.digit_clicked("2")),
             ("3", lambda: self.digit_clicked("3")), ("-", lambda: self.operation_clicked("-"))],
            [("0", lambda: self.digit_clicked("0")), (".", lambda: self.digit_clicked(".")),
             ("=", self.calculate), ("+", lambda: self.operation_clicked("+"))]
        ]

        for row, button_row in enumerate(buttons, 1):
            for col, (text, func) in enumerate(button_row):
                btn = QPushButton(text)
                btn.clicked.connect(func)
                if text == "0":
                    main_layout.addWidget(btn, row, col, 1, 1)
                else:
                    main_layout.addWidget(btn, row, col)

        main_frame = QFrame()
        main_frame.setLayout(main_layout)
        layout.addWidget(main_frame)

        # Boutons de mémoire
        memory_layout = QHBoxLayout()
        memory_buttons = [
            ("MC", self.memory_clear), ("MR", self.memory_recall),
            ("M+", self.memory_add), ("M-", self.memory_subtract),
            ("MS", self.memory_store)
        ]

        self.memory_value = 0

        for text, func in memory_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(func)
            memory_layout.addWidget(btn)

        memory_frame = QFrame()
        memory_frame.setLayout(memory_layout)
        layout.addWidget(memory_frame)

    def digit_clicked(self, digit):
        """Gestion des clics sur les chiffres"""
        if self.wait_for_operand:
            self.display.setText(digit)
            self.wait_for_operand = False
        else:
            if self.display.text() == "0":
                self.display.setText(digit)
            else:
                self.display.setText(self.display.text() + digit)

    def operation_clicked(self, operation):
        """Gestion des opérations"""
        operand = float(self.display.text())

        if self.pending_operation and not self.wait_for_operand:
            self.calculate()
            operand = float(self.display.text())

        self.pending_value = operand
        self.pending_operation = operation
        self.wait_for_operand = True

    def calculate(self):
        """Effectue le calcul"""
        if not self.pending_operation:
            return

        operand = float(self.display.text())

        try:
            if self.pending_operation == "+":
                result = self.pending_value + operand
            elif self.pending_operation == "-":
                result = self.pending_value - operand
            elif self.pending_operation == "×":
                result = self.pending_value * operand
            elif self.pending_operation == "÷":
                if operand == 0:
                    self.display.setText("Erreur")
                    return
                result = self.pending_value / operand
            else:
                return

            self.display.setText(str(result))
            self.pending_operation = ""
            self.wait_for_operand = True

        except Exception:
            self.display.setText("Erreur")

    def scientific_operation(self, operation):
        """Gestion des opérations scientifiques"""
        try:
            value = float(self.display.text())

            if operation == "sin":
                result = math.sin(math.radians(value))
            elif operation == "cos":
                result = math.cos(math.radians(value))
            elif operation == "tan":
                result = math.tan(math.radians(value))
            elif operation == "log":
                result = math.log10(value)
            elif operation == "ln":
                result = math.log(value)
            elif operation == "√":
                result = math.sqrt(value)
            elif operation == "x²":
                result = value ** 2
            elif operation == "x³":
                result = value ** 3
            elif operation == "π":
                result = math.pi
            elif operation == "e":
                result = math.e
            elif operation == "1/x":
                if value == 0:
                    self.display.setText("Erreur")
                    return
                result = 1 / value
            else:
                return

            self.display.setText(str(result))
            self.wait_for_operand = True

        except Exception:
            self.display.setText("Erreur")

    def clear_all(self):
        """Efface tout"""
        self.display.setText("0")
        self.current_value = 0
        self.pending_value = 0
        self.pending_operation = ""
        self.wait_for_operand = True

    def clear_entry(self):
        """Efface l'entrée courante"""
        self.display.setText("0")
        self.wait_for_operand = True

    def backspace(self):
        """Supprime le dernier caractère"""
        text = self.display.text()
        if len(text) > 1:
            self.display.setText(text[:-1])
        else:
            self.display.setText("0")

    def change_sign(self):
        """Change le signe"""
        value = float(self.display.text())
        self.display.setText(str(-value))

    # Fonctions mémoire
    def memory_clear(self):
        self.memory_value = 0

    def memory_recall(self):
        self.display.setText(str(self.memory_value))
        self.wait_for_operand = True

    def memory_add(self):
        self.memory_value += float(self.display.text())

    def memory_subtract(self):
        self.memory_value -= float(self.display.text())

    def memory_store(self):
        self.memory_value = float(self.display.text())

class ParametresAdmin(QDialog):
    """Fenêtre des paramètres administrateur"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ Paramètres Administrateur")
        self.setFixedSize(800, 600)
        self.setup_parametres()

    def setup_parametres(self):
        """Configure l'interface des paramètres"""
        layout = QVBoxLayout(self)

        # Style bleu turquoise
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40E0D0, stop:1 #20B2AA);
                border: 2px solid #008B8B;
            }
            QTabWidget::pane {
                border: 2px solid #008B8B;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48D1CC, stop:1 #20B2AA);
                color: white;
                padding: 10px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FFFF, stop:1 #40E0D0);
            }
        """)

        # Onglets de paramètres
        tabs = QTabWidget()

        # Onglet Général
        general_tab = self.create_general_tab()
        tabs.addTab(general_tab, "🏢 Général")

        # Onglet Utilisateurs
        users_tab = self.create_users_tab()
        tabs.addTab(users_tab, "👥 Utilisateurs")

        # Onglet Base de données
        db_tab = self.create_database_tab()
        tabs.addTab(db_tab, "🗃️ Base de données")

        # Onglet Sécurité
        security_tab = self.create_security_tab()
        tabs.addTab(security_tab, "🔒 Sécurité")

        # Onglet Sauvegarde
        backup_tab = self.create_backup_tab()
        tabs.addTab(backup_tab, "💾 Sauvegarde")

        layout.addWidget(tabs)

        # Boutons
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 Sauvegarder")
        save_btn.clicked.connect(self.save_parameters)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        apply_btn = QPushButton("✅ Appliquer")
        apply_btn.clicked.connect(self.apply_parameters)
        buttons_layout.addWidget(apply_btn)

        layout.addLayout(buttons_layout)

    def create_general_tab(self):
        """Crée l'onglet des paramètres généraux"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # Paramètres généraux
        self.company_name = QLineEdit("Entreprise GestImmob")
        self.currency_symbol = QComboBox()
        self.currency_symbol.addItems(["€", "$", "£", "¥", "DA", "MAD", "TND"])

        self.language = QComboBox()
        self.language.addItems(["Français", "English", "العربية"])

        self.theme = QComboBox()
        self.theme.addItems(["Bleu Turquoise", "Sage Classique", "Sombre", "Clair"])

        layout.addRow("🏢 Nom de l'entreprise:", self.company_name)
        layout.addRow("💰 Symbole monétaire:", self.currency_symbol)
        layout.addRow("🌍 Langue:", self.language)
        layout.addRow("🎨 Thème:", self.theme)

        return widget

    def create_users_tab(self):
        """Crée l'onglet de gestion des utilisateurs"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Table des utilisateurs
        self.users_table = QTableWidget(0, 5)
        self.users_table.setHorizontalHeaderLabels([
            "ID", "Nom d'utilisateur", "Nom complet", "Rôle", "Actif"
        ])
        layout.addWidget(self.users_table)

        # Boutons utilisateurs
        users_buttons = QHBoxLayout()

        add_user_btn = QPushButton("➕ Ajouter utilisateur")
        add_user_btn.clicked.connect(self.add_user)
        users_buttons.addWidget(add_user_btn)

        edit_user_btn = QPushButton("✏️ Modifier utilisateur")
        edit_user_btn.clicked.connect(self.edit_user)
        users_buttons.addWidget(edit_user_btn)

        delete_user_btn = QPushButton("🗑️ Supprimer utilisateur")
        delete_user_btn.clicked.connect(self.delete_user)
        users_buttons.addWidget(delete_user_btn)

        layout.addLayout(users_buttons)

        return widget

    def create_database_tab(self):
        """Crée l'onglet de configuration de la base de données"""
        widget = QWidget()
        layout = QFormLayout(widget)

        self.db_path = QLineEdit("gestimmob_final.db")
        self.backup_interval = QComboBox()
        self.backup_interval.addItems(["Jamais", "Quotidien", "Hebdomadaire", "Mensuel"])

        self.auto_sync = QComboBox()
        self.auto_sync.addItems(["Activé", "Désactivé"])

        layout.addRow("🗃️ Chemin base de données:", self.db_path)
        layout.addRow("💾 Sauvegarde automatique:", self.backup_interval)
        layout.addRow("🔄 Synchronisation auto:", self.auto_sync)

        return widget

    def create_security_tab(self):
        """Crée l'onglet de sécurité"""
        widget = QWidget()
        layout = QFormLayout(widget)

        self.password_policy = QComboBox()
        self.password_policy.addItems(["Faible", "Moyen", "Fort", "Très Fort"])

        self.session_timeout = QComboBox()
        self.session_timeout.addItems(["15 min", "30 min", "1 heure", "2 heures", "Jamais"])

        self.audit_log = QComboBox()
        self.audit_log.addItems(["Activé", "Désactivé"])

        layout.addRow("🔒 Politique mot de passe:", self.password_policy)
        layout.addRow("⏰ Timeout session:", self.session_timeout)
        layout.addRow("📋 Journal d'audit:", self.audit_log)

        return widget

    def create_backup_tab(self):
        """Crée l'onglet de sauvegarde"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres de sauvegarde
        backup_settings = QFormLayout()

        self.backup_path = QLineEdit("./backups/")
        self.backup_retention = QComboBox()
        self.backup_retention.addItems(["7 jours", "30 jours", "90 jours", "1 an", "Illimité"])

        backup_settings.addRow("📁 Dossier sauvegarde:", self.backup_path)
        backup_settings.addRow("📅 Rétention:", self.backup_retention)

        layout.addLayout(backup_settings)

        # Boutons de sauvegarde
        backup_buttons = QHBoxLayout()

        backup_now_btn = QPushButton("💾 Sauvegarder maintenant")
        backup_now_btn.clicked.connect(self.backup_now)
        backup_buttons.addWidget(backup_now_btn)

        restore_btn = QPushButton("🔄 Restaurer")
        restore_btn.clicked.connect(self.restore_backup)
        backup_buttons.addWidget(restore_btn)

        layout.addLayout(backup_buttons)

        return widget

    def add_user(self):
        """Ajoute un nouvel utilisateur"""
        # Implémentation à ajouter
        pass

    def edit_user(self):
        """Modifie un utilisateur"""
        # Implémentation à ajouter
        pass

    def delete_user(self):
        """Supprime un utilisateur"""
        # Implémentation à ajouter
        pass

    def save_parameters(self):
        """Sauvegarde les paramètres"""
        # Implémentation à ajouter
        self.accept()

    def apply_parameters(self):
        """Applique les paramètres"""
        # Implémentation à ajouter
        pass

    def backup_now(self):
        """Lance une sauvegarde immédiate"""
        # Implémentation à ajouter
        pass

    def restore_backup(self):
        """Restaure une sauvegarde"""
        # Implémentation à ajouter
        pass

class ParcAutoComplet(QDialog):
    """Module Parc Auto avec TOUS vos critères préservés"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🚗 Parc Automobile - Gestion Complète")
        self.setFixedSize(1200, 800)
        self.setup_parc_auto()

    def setup_parc_auto(self):
        """Configure l'interface du parc auto avec tous vos critères"""
        layout = QVBoxLayout(self)

        # Style bleu turquoise
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40E0D0, stop:1 #20B2AA);
                border: 2px solid #008B8B;
            }
            QLineEdit, QComboBox {
                background-color: #87CEEB;
                color: #000000;
                border: 2px solid #008B8B;
                border-radius: 5px;
                padding: 5px;
                font-size: 12pt;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48D1CC, stop:1 #20B2AA);
                color: white;
                border: 2px solid #008B8B;
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FFFF, stop:1 #40E0D0);
            }
        """)

        # Titre
        title = QLabel("🚗 Gestion Complète du Parc Automobile")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18pt;
                font-weight: bold;
                background-color: rgba(0,0,0,0.3);
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)

        # Formulaire avec TOUS vos critères préservés
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)

        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)

        # TOUS vos critères du parc auto préservés exactement
        self.create_parc_auto_form(form_layout)

        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)

        # Boutons d'action
        self.create_parc_auto_buttons(layout)

    def create_parc_auto_form(self, form_layout):
        """Crée le formulaire avec TOUS vos critères préservés"""

        # Type de véhicule (exactement comme vos critères)
        self.type_veh = QComboBox()
        self.type_veh.addItems(["Voiture", "Camion", "Engin", "Bus", "Moto", "Utilitaire", "Autre"])
        form_layout.addRow("🚗 Type de véhicule:", self.type_veh)

        # Informations de base
        self.marque = QLineEdit()
        self.marque.setPlaceholderText("Marque du véhicule")
        form_layout.addRow("🏭 Marque:", self.marque)

        self.modele = QLineEdit()
        self.modele.setPlaceholderText("Modèle du véhicule")
        form_layout.addRow("🚙 Modèle:", self.modele)

        self.immatriculation = QLineEdit()
        self.immatriculation.setPlaceholderText("Numéro d'immatriculation")
        form_layout.addRow("🔢 Immatriculation:", self.immatriculation)

        self.annee = QLineEdit()
        self.annee.setPlaceholderText("Année de mise en circulation")
        form_layout.addRow("📅 Année:", self.annee)

        self.num_serie = QLineEdit()
        self.num_serie.setPlaceholderText("Numéro de série")
        form_layout.addRow("🔢 N° Série:", self.num_serie)

        # Catégorie (exactement vos valeurs)
        self.categorie = QComboBox()
        self.categorie.addItems(["Léger", "Lourd", "Spécial", "Transport", "Service", "Autre"])
        form_layout.addRow("📂 Catégorie:", self.categorie)

        # Kilométrage et entretien
        self.kilometrage = QLineEdit()
        self.kilometrage.setPlaceholderText("Kilométrage actuel")
        form_layout.addRow("🛣️ Kilométrage:", self.kilometrage)

        self.date_dernier_entretien = QLineEdit()
        self.date_dernier_entretien.setPlaceholderText("Date dernier entretien")
        form_layout.addRow("🔧 Date dernier entretien:", self.date_dernier_entretien)

        self.prochain_entretien = QLineEdit()
        self.prochain_entretien.setPlaceholderText("Prochain entretien")
        form_layout.addRow("📅 Prochain entretien:", self.prochain_entretien)

        # Statut (exactement vos valeurs)
        self.statut = QComboBox()
        self.statut.addItems(["Disponible", "En service", "En panne", "En maintenance", "Réformé"])
        form_layout.addRow("📊 Statut:", self.statut)

        # Personnel
        self.conducteur = QLineEdit()
        self.conducteur.setPlaceholderText("Conducteur principal")
        form_layout.addRow("👤 Conducteur:", self.conducteur)

        # Chauffeurs (exactement vos critères)
        self.chauffeur_titulaire = QComboBox()
        self.chauffeur_titulaire.addItems([
            "Aucun", "Titulaire 1", "Titulaire 2", "Titulaire 3", "Titulaire 4", "Titulaire 5", "Autre"
        ])
        form_layout.addRow("👤 Chauffeur titulaire:", self.chauffeur_titulaire)

        self.chauffeur_suppleant = QComboBox()
        self.chauffeur_suppleant.addItems([
            "Aucun", "Suppléant 1", "Suppléant 2", "Suppléant 3", "Suppléant 4", "Suppléant 5", "Autre"
        ])
        form_layout.addRow("👥 Chauffeur suppléant:", self.chauffeur_suppleant)

        # Carburant (exactement vos critères)
        self.carburant = QComboBox()
        self.carburant.addItems(["Diesel", "Essence", "GPL", "Électrique", "Hybride", "Autre"])
        form_layout.addRow("⛽ Type carburant:", self.carburant)

        # Pannes et réparations
        self.date_panne = QLineEdit()
        self.date_panne.setPlaceholderText("Date de la panne")
        form_layout.addRow("📅 Date panne:", self.date_panne)

        self.type_panne = QComboBox()
        self.type_panne.addItems(["Mécanique", "Électrique", "Carrosserie", "Pneumatique", "Autre"])
        form_layout.addRow("🔧 Type panne:", self.type_panne)

        self.desc_panne = QLineEdit()
        self.desc_panne.setPlaceholderText("Description de la panne/anomalie")
        form_layout.addRow("📝 Description panne:", self.desc_panne)

        self.date_reparation = QLineEdit()
        self.date_reparation.setPlaceholderText("Date de réparation")
        form_layout.addRow("🔧 Date réparation:", self.date_reparation)

        self.cout_entretien = QLineEdit()
        self.cout_entretien.setPlaceholderText("Coût d'entretien")
        form_layout.addRow("💰 Coût entretien:", self.cout_entretien)

        # Disponibilité (exactement vos critères)
        self.disponibilite = QComboBox()
        self.disponibilite.addItems(["Oui", "Non", "Réservé"])
        form_layout.addRow("✅ Disponibilité:", self.disponibilite)

        # Observations
        self.observation = QLineEdit()
        self.observation.setPlaceholderText("Observations générales")
        form_layout.addRow("📋 Observation:", self.observation)

        # Critères enrichis (exactement vos spécifications)
        self.rapport_detaille = QComboBox()
        self.rapport_detaille.addItems([
            "Rapport technique", "Rapport d'entretien", "Rapport d'accident", "Rapport de mission",
            "Rapport de consommation", "Rapport assurance", "Rapport GPS", "Rapport carburant", "Autre"
        ])
        form_layout.addRow("📊 Rapport détaillé:", self.rapport_detaille)

        self.amortissement = QComboBox()
        self.amortissement.addItems([
            "Amorti", "Non amorti", "En cours", "À calculer", "Calcul automatique", "Selon base de données"
        ])
        form_layout.addRow("📈 Amortissement:", self.amortissement)

        self.constat = QComboBox()
        self.constat.addItems([
            "RAS", "Anomalie détectée", "Entretien à prévoir", "Panne récurrente", "Usure pneus",
            "Fuite huile", "Freinage", "Éclairage", "Carrosserie", "Autre"
        ])
        form_layout.addRow("🔍 Constat:", self.constat)

        self.photo_format = QComboBox()
        self.photo_format.addItems([
            "Aucune", "JPG", "PNG", "JPEG", "PDF", "BMP", "TIFF", "GIF", "Autre format"
        ])
        form_layout.addRow("📷 Photo (format):", self.photo_format)

        # Kilométrage détaillé
        self.km_depart = QLineEdit()
        self.km_depart.setPlaceholderText("Kilométrage de départ")
        form_layout.addRow("🚀 Km départ:", self.km_depart)

        self.km_arrivee = QLineEdit()
        self.km_arrivee.setPlaceholderText("Kilométrage d'arrivée")
        form_layout.addRow("🏁 Km arrivée:", self.km_arrivee)

        # Bouton calcul km (exactement comme vos critères)
        self.calcul_km_btn = QPushButton("🧮 Calculer km parcourus")
        self.calcul_km_btn.clicked.connect(self.calculer_km)
        form_layout.addRow("", self.calcul_km_btn)

        self.km_result = QLineEdit()
        self.km_result.setReadOnly(True)
        self.km_result.setPlaceholderText("Résultat du calcul")
        form_layout.addRow("📊 Km parcourus:", self.km_result)

        # Entretien détaillé (exactement vos critères)
        self.vidange = QComboBox()
        self.vidange.addItems([
            "À jour", "À prévoir", "Urgent", "Non renseigné", "Vidange partielle", "Vidange totale"
        ])
        form_layout.addRow("🛢️ Vidange huile:", self.vidange)

        self.pression_pneus = QComboBox()
        self.pression_pneus.addItems([
            "Normale", "Basse", "Haute", "À contrôler", "Avant OK", "Arrière OK", "Tous à contrôler"
        ])
        form_layout.addRow("🛞 Pression pneus:", self.pression_pneus)

        # GPS complet (exactement vos critères)
        self.gps = QComboBox()
        self.gps.addItems([
            "Oui", "Non", "À installer", "Défectueux", "Localisation active", "Localisation inactive", "En maintenance"
        ])
        form_layout.addRow("📡 GPS:", self.gps)

        self.gps_fournisseur = QComboBox()
        self.gps_fournisseur.addItems([
            "Aucun", "TomTom", "Coyote", "Wialon", "Fleet Complete", "Geotab", "Autre"
        ])
        form_layout.addRow("🏢 Fournisseur GPS:", self.gps_fournisseur)

        self.gps_num_serie = QLineEdit()
        self.gps_num_serie.setPlaceholderText("Numéro de série GPS")
        form_layout.addRow("🔢 N° série GPS:", self.gps_num_serie)

        self.gps_date_install = QLineEdit()
        self.gps_date_install.setPlaceholderText("Date installation GPS")
        form_layout.addRow("📅 Date installation GPS:", self.gps_date_install)

        self.gps_derniere_position = QLineEdit()
        self.gps_derniere_position.setPlaceholderText("Dernière position GPS")
        form_layout.addRow("📍 Dernière position GPS:", self.gps_derniere_position)

        self.gps_latitude = QLineEdit()
        self.gps_latitude.setPlaceholderText("Latitude GPS")
        form_layout.addRow("🌐 Latitude GPS:", self.gps_latitude)

        self.gps_longitude = QLineEdit()
        self.gps_longitude.setPlaceholderText("Longitude GPS")
        form_layout.addRow("🌐 Longitude GPS:", self.gps_longitude)

        self.gps_precision = QComboBox()
        self.gps_precision.addItems(["Haute", "Moyenne", "Faible", "Non renseigné"])
        form_layout.addRow("🎯 Précision GPS:", self.gps_precision)

        self.gps_carte = QComboBox()
        self.gps_carte.addItems(["Google Maps", "OpenStreetMap", "Here", "Mapbox", "Autre"])
        form_layout.addRow("🗺️ Carte utilisée:", self.gps_carte)

        self.gps_zone_geo = QLineEdit()
        self.gps_zone_geo.setPlaceholderText("Zone géographique")
        form_layout.addRow("🌍 Zone géographique:", self.gps_zone_geo)

        self.gps_pays = QComboBox()
        self.gps_pays.addItems([
            "France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "Allemagne", "Belgique", "Suisse", "Autre"
        ])
        form_layout.addRow("🏳️ Pays:", self.gps_pays)

        self.gps_balise = QComboBox()
        self.gps_balise.addItems([
            "Aucune", "Teltonika", "Queclink", "Calamp", "Suntech", "Concox", "Ruptela", "Autre"
        ])
        form_layout.addRow("📡 Balise GPS:", self.gps_balise)

        # Données techniques
        self.vitesse = QLineEdit()
        self.vitesse.setPlaceholderText("Vitesse (km/h)")
        form_layout.addRow("⚡ Vitesse (km/h):", self.vitesse)

        self.temperature_moteur = QLineEdit()
        self.temperature_moteur.setPlaceholderText("Température moteur (°C)")
        form_layout.addRow("🌡️ Température moteur (°C):", self.temperature_moteur)

        self.voix_passagers = QComboBox()
        self.voix_passagers.addItems([
            "Non disponible", "Enregistrement actif", "Enregistrement désactivé", "Micro défectueux", "Disponible"
        ])
        form_layout.addRow("🎤 Voix passagers:", self.voix_passagers)

        # Assurance et documents (exactement vos critères)
        self.assurance = QComboBox()
        self.assurance.addItems(["À jour", "À renouveler", "Expirée", "En cours", "Non renseigné"])
        form_layout.addRow("🛡️ Assurance:", self.assurance)

        self.carte_grise = QComboBox()
        self.carte_grise.addItems(["Disponible", "Manquante", "En cours", "Non renseigné"])
        form_layout.addRow("📄 Carte grise:", self.carte_grise)

        # Observation complémentaire
        self.obs = QLineEdit()
        self.obs.setPlaceholderText("Observation complémentaire")
        form_layout.addRow("📝 Observation complémentaire:", self.obs)

    def calculer_km(self):
        """Calcule les kilomètres parcourus (exactement comme vos critères)"""
        try:
            depart = float(self.km_depart.text()) if self.km_depart.text() else 0
            arrivee = float(self.km_arrivee.text()) if self.km_arrivee.text() else 0
            resultat = arrivee - depart
            self.km_result.setText(str(resultat))
        except ValueError:
            self.km_result.setText("Erreur")

    def create_parc_auto_buttons(self, layout):
        """Crée les boutons d'action pour le parc auto"""
        buttons_layout = QHBoxLayout()

        # Boutons avec style bleu turquoise
        validate_btn = QPushButton("✅ Valider")
        validate_btn.clicked.connect(self.validate_parc_auto)
        buttons_layout.addWidget(validate_btn)

        modify_btn = QPushButton("✏️ Modifier")
        modify_btn.clicked.connect(self.modify_parc_auto)
        buttons_layout.addWidget(modify_btn)

        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        back_btn = QPushButton("🔙 Retour")
        back_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(back_btn)

        # Boutons spécialisés
        sync_btn = QPushButton("🔄 Synchroniser")
        sync_btn.clicked.connect(self.synchroniser_parc)
        buttons_layout.addWidget(sync_btn)

        param_btn = QPushButton("⚙️ Paramètres")
        param_btn.clicked.connect(self.ouvrir_parametres)
        buttons_layout.addWidget(param_btn)

        layout.addLayout(buttons_layout)

    def validate_parc_auto(self):
        """Valide et sauvegarde les données du parc auto"""
        print("✅ Validation des données du parc automobile")
        # Logique de sauvegarde avec tous vos critères

    def modify_parc_auto(self):
        """Modifie les données du parc auto"""
        print("✏️ Modification des données du parc automobile")

    def synchroniser_parc(self):
        """Synchronise avec la base de données"""
        print("🔄 Synchronisation du parc automobile")

    def ouvrir_parametres(self):
        """Ouvre les paramètres du logiciel"""
        print("⚙️ Ouverture des paramètres")

class GestImmobSageStyle(QMainWindow):
    """Interface principale style Sage Immobilisation avec fond bleu turquoise"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏢 GestImmob - Style Sage Immobilisation")
        self.setGeometry(100, 100, 1600, 1000)

        # Variables
        self.current_user = None
        self.currency_symbol = "€"
        self.code_counter = 1

        # Modules avec TOUS vos critères préservés
        self.modules_complets = {
            'Gestion des Biens': {'couleur': '#1976D2', 'icone': '🏠', 'classe': None},
            'Fournisseurs': {'couleur': '#388E3C', 'icone': '🏢', 'classe': None},
            'Inventaire': {'couleur': '#F57C00', 'icone': '📦', 'classe': None},
            'Parc Auto': {'couleur': '#D32F2F', 'icone': '🚗', 'classe': ParcAutoComplet},
            'Suivi des Animaux': {'couleur': '#7B1FA2', 'icone': '🐄', 'classe': None},
            'Suivi des Travaux': {'couleur': '#455A64', 'icone': '🔨', 'classe': None},
            'Outils de Travaux': {'couleur': '#5D4037', 'icone': '🔧', 'classe': None},
            'Calculatrice': {'couleur': '#00796B', 'icone': '🧮', 'classe': CalculatriceAvancee},
            'Documents': {'couleur': '#303F9F', 'icone': '📄', 'classe': None},
            'Impression': {'couleur': '#689F38', 'icone': '🖨️', 'classe': None},
            'Archive': {'couleur': '#512DA8', 'icone': '📚', 'classe': None},
            'Outillage': {'couleur': '#E64A19', 'icone': '⚒️', 'classe': None},
            'Moyens Généraux': {'couleur': '#1976D2', 'icone': '🏭', 'classe': None},
            'Hôtellerie': {'couleur': '#C2185B', 'icone': '🏨', 'classe': None},
            'Agencement': {'couleur': '#00ACC1', 'icone': '🪑', 'classe': None},
            'Bâtis': {'couleur': '#6A4C93', 'icone': '🏗️', 'classe': None}
        }

        self.setup_sage_interface()

    def setup_sage_interface(self):
        """Configure l'interface style Sage Immobilisation"""

        # Style principal bleu turquoise (comme Sage)
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40E0D0, stop:1 #20B2AA);
                border: 3px solid #008B8B;
            }
            QWidget {
                background-color: transparent;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48D1CC, stop:1 #20B2AA);
                color: white;
                font-size: 12pt;
                font-weight: bold;
                border-bottom: 2px solid #008B8B;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 5px;
            }
            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FFFF, stop:1 #40E0D0);
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20B2AA, stop:1 #008B8B);
                color: white;
                font-weight: bold;
                border-top: 2px solid #008B8B;
            }
        """)

        # Menu principal (style Sage)
        self.create_sage_menu()

        # Barre d'outils (style Sage)
        self.create_sage_toolbar()

        # Zone centrale avec modules
        self.create_sage_central_area()

        # Barre de statut (style Sage)
        self.create_sage_status_bar()

        # Afficher la fenêtre
        self.show()

    def create_sage_menu(self):
        """Crée le menu principal style Sage"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        new_action = file_menu.addAction("🆕 Nouveau")
        new_action.triggered.connect(self.nouveau_fichier)

        open_action = file_menu.addAction("📂 Ouvrir")
        open_action.triggered.connect(self.ouvrir_fichier)

        save_action = file_menu.addAction("💾 Sauvegarder")
        save_action.triggered.connect(self.sauvegarder_fichier)

        file_menu.addSeparator()

        exit_action = file_menu.addAction("🚪 Quitter")
        exit_action.triggered.connect(self.close)

        # Menu Modules
        modules_menu = menubar.addMenu("📋 Modules")

        for nom_module, config in self.modules_complets.items():
            action = modules_menu.addAction(f"{config['icone']} {nom_module}")
            action.triggered.connect(lambda checked, m=nom_module: self.ouvrir_module(m))

        # Menu Outils
        tools_menu = menubar.addMenu("🔧 Outils")

        calc_action = tools_menu.addAction("🧮 Calculatrice")
        calc_action.triggered.connect(self.ouvrir_calculatrice)

        param_action = tools_menu.addAction("⚙️ Paramètres")
        param_action.triggered.connect(self.ouvrir_parametres)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = help_menu.addAction("ℹ️ À propos")
        about_action.triggered.connect(self.afficher_apropos)

    def create_sage_toolbar(self):
        """Crée la barre d'outils style Sage"""
        toolbar = self.addToolBar("Outils principaux")
        toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48D1CC, stop:1 #20B2AA);
                border: 2px solid #008B8B;
                spacing: 5px;
                padding: 5px;
            }
            QToolButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #87CEEB, stop:1 #4682B4);
                color: white;
                border: 2px solid #008B8B;
                border-radius: 8px;
                font-size: 11pt;
                font-weight: bold;
                padding: 8px;
                margin: 2px;
                min-width: 80px;
                min-height: 40px;
            }
            QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FFFF, stop:1 #40E0D0);
            }
            QToolButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #008B8B, stop:1 #006666);
            }
        """)

        # Boutons de la barre d'outils
        toolbar_actions = [
            ("🆕 Nouveau", self.nouveau_fichier),
            ("📂 Ouvrir", self.ouvrir_fichier),
            ("💾 Sauvegarder", self.sauvegarder_fichier),
            ("🖨️ Imprimer", self.imprimer),
            ("🔍 Rechercher", self.rechercher),
            ("🧮 Calculatrice", self.ouvrir_calculatrice),
            ("⚙️ Paramètres", self.ouvrir_parametres)
        ]

        for text, func in toolbar_actions:
            action = toolbar.addAction(text)
            action.triggered.connect(func)

    def create_sage_central_area(self):
        """Crée la zone centrale style Sage avec modules"""
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F0FFFF, stop:1 #E0FFFF);
                border: 2px solid #008B8B;
                border-radius: 10px;
                margin: 5px;
            }
        """)
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # Titre principal
        title_label = QLabel("🏢 GestImmob - Gestion Immobilière Professionnelle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40E0D0, stop:1 #20B2AA);
                color: white;
                font-size: 20pt;
                font-weight: bold;
                border: 2px solid #008B8B;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)

        # Zone des modules (style Sage avec grille)
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8FFFF, stop:1 #F0FFFF);
                border: 2px solid #B0E0E6;
                border-radius: 10px;
                margin: 10px;
                padding: 15px;
            }
        """)

        modules_layout = QGridLayout(modules_frame)
        modules_layout.setSpacing(15)

        # Organiser les modules en grille (style Sage)
        row, col = 0, 0
        max_cols = 4  # 4 colonnes comme Sage

        for nom_module, config in self.modules_complets.items():
            module_btn = self.create_sage_module_button(nom_module, config)
            modules_layout.addWidget(module_btn, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        layout.addWidget(modules_frame)

        # Zone d'informations (style Sage)
        info_frame = self.create_sage_info_area()
        layout.addWidget(info_frame)

    def create_sage_module_button(self, nom_module, config):
        """Crée un bouton de module style Sage"""
        btn = QPushButton(f"{config['icone']}\n{nom_module}")

        # Style Sage professionnel
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {config['couleur']}, stop:1 {self.darken_color(config['couleur'])});
                color: white;
                border: 3px solid #008B8B;
                border-radius: 15px;
                font-size: 12pt;
                font-weight: bold;
                padding: 20px;
                min-width: 180px;
                min-height: 120px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FFFF, stop:1 #40E0D0);
                border: 3px solid #00CED1;
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #008B8B, stop:1 #006666);
                border: 3px solid #004D4D;
            }}
        """)

        # Tooltip informatif
        btn.setToolTip(f"🎯 Module: {nom_module}\n💡 Cliquez pour ouvrir\n🔧 Critères complets disponibles")

        # Connecter à l'ouverture du module
        btn.clicked.connect(lambda: self.ouvrir_module(nom_module))

        return btn

    def create_sage_info_area(self):
        """Crée la zone d'informations style Sage"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E6FFFA, stop:1 #B0E0E6);
                border: 2px solid #008B8B;
                border-radius: 10px;
                margin: 10px;
                padding: 15px;
            }
        """)
        info_frame.setFixedHeight(150)

        layout = QHBoxLayout(info_frame)

        # Informations système
        system_info = QLabel("""
        📊 <b>Informations Système</b><br>
        🗃️ Base de données: Connectée<br>
        👤 Utilisateur: Administrateur<br>
        💰 Devise: Euro (€)<br>
        🔢 Codification: INV001
        """)
        system_info.setStyleSheet("""
            QLabel {
                background-color: rgba(255,255,255,0.8);
                border: 1px solid #008B8B;
                border-radius: 8px;
                padding: 10px;
                font-size: 11pt;
            }
        """)
        layout.addWidget(system_info)

        # Statistiques
        stats_info = QLabel("""
        📈 <b>Statistiques</b><br>
        🏠 Biens gérés: 0<br>
        🚗 Véhicules: 0<br>
        📦 Articles: 0<br>
        🏢 Fournisseurs: 0
        """)
        stats_info.setStyleSheet("""
            QLabel {
                background-color: rgba(255,255,255,0.8);
                border: 1px solid #008B8B;
                border-radius: 8px;
                padding: 10px;
                font-size: 11pt;
            }
        """)
        layout.addWidget(stats_info)

        # Actions rapides
        quick_actions = QLabel("""
        ⚡ <b>Actions Rapides</b><br>
        🆕 Nouveau bien<br>
        🔍 Recherche avancée<br>
        📊 Rapports<br>
        💾 Sauvegarde
        """)
        quick_actions.setStyleSheet("""
            QLabel {
                background-color: rgba(255,255,255,0.8);
                border: 1px solid #008B8B;
                border-radius: 8px;
                padding: 10px;
                font-size: 11pt;
            }
        """)
        layout.addWidget(quick_actions)

        return info_frame

    def create_sage_status_bar(self):
        """Crée la barre de statut style Sage"""
        status_bar = self.statusBar()
        status_bar.showMessage("🎯 GestImmob prêt - Style Sage Immobilisation | 💰 Devise: € | 🔢 Codification: INV001")

    def darken_color(self, hex_color, factor=0.3):
        """Assombrit une couleur hexadécimale"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    # Méthodes des actions de menu
    def nouveau_fichier(self):
        """Crée un nouveau fichier"""
        print("🆕 Nouveau fichier")

    def ouvrir_fichier(self):
        """Ouvre un fichier"""
        print("📂 Ouvrir fichier")

    def sauvegarder_fichier(self):
        """Sauvegarde le fichier"""
        print("💾 Sauvegarder fichier")

    def imprimer(self):
        """Lance l'impression"""
        print("🖨️ Impression")

    def rechercher(self):
        """Lance la recherche"""
        print("🔍 Recherche")

    def ouvrir_calculatrice(self):
        """Ouvre la calculatrice avancée"""
        calc = CalculatriceAvancee(self)
        calc.exec()

    def ouvrir_parametres(self):
        """Ouvre les paramètres administrateur"""
        params = ParametresAdmin(self)
        params.exec()

    def afficher_apropos(self):
        """Affiche les informations À propos"""
        print("ℹ️ À propos de GestImmob")

    def ouvrir_module(self, nom_module):
        """Ouvre un module spécifique avec TOUS vos critères"""
        print(f"🎯 Ouverture du module: {nom_module}")

        config = self.modules_complets.get(nom_module)
        if config and config['classe']:
            # Ouvrir le module avec sa classe spécifique
            module_window = config['classe'](self)
            module_window.exec()
        else:
            # Module générique (à implémenter)
            print(f"📋 Module {nom_module} - Critères à implémenter")

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName("GestImmob Style Sage")
    app.setApplicationVersion("3.0.0")
    app.setOrganizationName("GestImmob Solutions")

    # Créer et afficher l'interface
    window = GestImmobSageStyle()

    return app.exec()

if __name__ == "__main__":
    main()