# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Configuration
app_name = "GestImmob"
main_script = "src/main.py"
project_root = Path.cwd()

# Données à inclure
datas = []

# Vérifier et ajouter les fichiers de données s'ils existent
data_files = [
    ('config_advanced.json', '.'),
    ('requirements.txt', '.'),
    ('README.md', '.'),
    ('config.json', '.'),
    ('src/config.json', '.'),
]

for src_file, dest_dir in data_files:
    if (project_root / src_file).exists():
        datas.append((src_file, dest_dir))

# Ajouter les modules source
if (project_root / 'src').exists():
    datas.append(('src', 'src'))
if (project_root / 'modules').exists():
    datas.append(('modules', 'modules'))

# Modules cachés (pour éviter les erreurs d'import)
# Seulement les modules qui sont réellement installés
hiddenimports = [
    # PySide6 modules
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtPrintSupport',
    'PySide6.QtSql',

    # Base de données
    'sqlite3',
    'sqlalchemy',
    'sqlalchemy.engine',
    'sqlalchemy.orm',

    # Sécurité
    'cryptography.fernet',
    'cryptography.hazmat',

    # Traitement de données
    'xlsxwriter',

    # Images
    'PIL',
    'PIL.Image',

    # Réseau
    'requests',
    'urllib3',

    # Modules système
    'json',
    'datetime',
    'pathlib',
    'logging',
    'threading',
    'multiprocessing',
    'os',
    'sys',
    'shutil',
    'subprocess',
    'hashlib',

    # Modules du projet
    'src.main',
    'src.config',
    'src.auth.authentication',
    'src.database.db_connection',
    'src.utils.config',
]

# Ajouter les modules optionnels seulement s'ils sont installés
optional_modules = [
    ('pandas', 'pandas'),
    ('reportlab', 'reportlab'),
    ('reportlab', 'reportlab.pdfgen'),
    ('reportlab', 'reportlab.lib'),
    ('cv2', 'cv2'),
    ('pyzbar', 'pyzbar'),
    ('openpyxl', 'openpyxl'),
    ('numpy', 'numpy'),
]

for package, module in optional_modules:
    try:
        __import__(package)
        hiddenimports.append(module)
    except ImportError:
        pass

# Exclusions pour réduire la taille
excludes = [
    'tkinter',
    'matplotlib',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'setuptools',
    'distutils',
]

# Binaires à inclure
binaries = []

a = Analysis(
    [main_script],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=app_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon=None,
)
