#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour analyser et extraire les configurations des dossiers COMMUN et DATA2
"""

import os
import json
import sqlite3
import configparser
import xml.etree.ElementTree as ET
from pathlib import Path

class AnalyseurConfigurations:
    def __init__(self):
        self.bureau = self.trouver_bureau()
        self.dossier_commun = None
        self.dossier_data2 = None
        self.configurations_extraites = {}
        
    def trouver_bureau(self):
        """Trouve le chemin du bureau"""
        chemins_possibles = [
            Path.home() / "Desktop",
            Path.home() / "Bureau",
            Path("C:/Users/<USER>/Desktop"),
            os.path.expanduser("~/Desktop")
        ]
        
        for chemin in chemins_possibles:
            if chemin.exists():
                return chemin
        return None
    
    def analyser_dossiers(self):
        """Analyse les dossiers COMMUN et DATA2"""
        print("🔍 ANALYSE DES DOSSIERS DE CONFIGURATION")
        print("=" * 60)
        
        if not self.bureau:
            print("❌ Bureau non trouvé")
            return False
        
        print(f"📂 Bureau: {self.bureau}")
        
        # Chercher les dossiers
        self.dossier_commun = self.bureau / "COMMUN"
        self.dossier_data2 = self.bureau / "DATA2"
        
        print(f"\n📁 Recherche des dossiers...")
        
        if self.dossier_commun.exists():
            print(f"✅ Dossier COMMUN trouvé: {self.dossier_commun}")
            self.analyser_dossier_commun()
        else:
            print(f"❌ Dossier COMMUN non trouvé: {self.dossier_commun}")
        
        if self.dossier_data2.exists():
            print(f"✅ Dossier DATA2 trouvé: {self.dossier_data2}")
            self.analyser_dossier_data2()
        else:
            print(f"❌ Dossier DATA2 non trouvé: {self.dossier_data2}")
        
        return True
    
    def analyser_dossier_commun(self):
        """Analyse le dossier COMMUN"""
        print(f"\n📁 ANALYSE DOSSIER COMMUN")
        print("-" * 40)
        
        # Analyser config.json
        config_json = self.dossier_commun / "config.json"
        if config_json.exists():
            print(f"📄 Analyse config.json...")
            self.analyser_config_json(config_json)
        else:
            print(f"❌ config.json non trouvé")
        
        # Analyser settings.ini
        settings_ini = self.dossier_commun / "settings.ini"
        if settings_ini.exists():
            print(f"📄 Analyse settings.ini...")
            self.analyser_settings_ini(settings_ini)
        else:
            print(f"❌ settings.ini non trouvé")
        
        # Analyser database.db
        database_db = self.dossier_commun / "database.db"
        if database_db.exists():
            print(f"📄 Analyse database.db...")
            self.analyser_database_db(database_db)
        else:
            print(f"❌ database.db non trouvé")
    
    def analyser_dossier_data2(self):
        """Analyse le dossier DATA2"""
        print(f"\n📁 ANALYSE DOSSIER DATA2")
        print("-" * 40)
        
        # Analyser parametres.xml
        parametres_xml = self.dossier_data2 / "parametres.xml"
        if parametres_xml.exists():
            print(f"📄 Analyse parametres.xml...")
            self.analyser_parametres_xml(parametres_xml)
        else:
            print(f"❌ parametres.xml non trouvé")
        
        # Analyser secteurs.json
        secteurs_json = self.dossier_data2 / "secteurs.json"
        if secteurs_json.exists():
            print(f"📄 Analyse secteurs.json...")
            self.analyser_secteurs_json(secteurs_json)
        else:
            print(f"❌ secteurs.json non trouvé")
    
    def analyser_config_json(self, fichier):
        """Analyse le fichier config.json"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ config.json chargé ({len(config)} clés principales)")
            
            # Afficher la structure
            for cle, valeur in config.items():
                if isinstance(valeur, dict):
                    print(f"  📋 {cle}: {len(valeur)} sous-éléments")
                elif isinstance(valeur, list):
                    print(f"  📋 {cle}: liste de {len(valeur)} éléments")
                else:
                    print(f"  📋 {cle}: {type(valeur).__name__}")
            
            self.configurations_extraites['config_json'] = config
            
            # Afficher le contenu complet pour clonage
            print(f"\n📋 CONTENU COMPLET config.json:")
            print("=" * 50)
            print(json.dumps(config, indent=2, ensure_ascii=False))
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ Erreur lecture config.json: {e}")
    
    def analyser_settings_ini(self, fichier):
        """Analyse le fichier settings.ini"""
        try:
            config = configparser.ConfigParser()
            config.read(fichier, encoding='utf-8')
            
            print(f"✅ settings.ini chargé ({len(config.sections())} sections)")
            
            # Afficher les sections
            for section in config.sections():
                print(f"  📋 [{section}]: {len(config[section])} paramètres")
                for cle, valeur in config[section].items():
                    print(f"    • {cle} = {valeur}")
            
            # Convertir en dictionnaire pour stockage
            settings_dict = {}
            for section in config.sections():
                settings_dict[section] = dict(config[section])
            
            self.configurations_extraites['settings_ini'] = settings_dict
            
            # Afficher le contenu complet
            print(f"\n📋 CONTENU COMPLET settings.ini:")
            print("=" * 50)
            with open(fichier, 'r', encoding='utf-8') as f:
                print(f.read())
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ Erreur lecture settings.ini: {e}")
    
    def analyser_database_db(self, fichier):
        """Analyse la base de données SQLite"""
        try:
            conn = sqlite3.connect(fichier)
            cursor = conn.cursor()
            
            # Lister les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"✅ database.db analysée ({len(tables)} tables)")
            
            schema_info = {}
            
            for table in tables:
                table_name = table[0]
                print(f"  📊 Table: {table_name}")
                
                # Obtenir la structure de la table
                cursor.execute(f"PRAGMA table_info({table_name})")
                colonnes = cursor.fetchall()
                
                # Compter les enregistrements
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                nb_records = cursor.fetchone()[0]
                
                print(f"    📋 {len(colonnes)} colonnes, {nb_records} enregistrements")
                
                schema_info[table_name] = {
                    'colonnes': colonnes,
                    'nb_records': nb_records
                }
                
                # Afficher les colonnes
                for col in colonnes:
                    print(f"      • {col[1]} ({col[2]})")
                
                # Afficher quelques exemples de données
                if nb_records > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    exemples = cursor.fetchall()
                    print(f"    📄 Exemples de données:")
                    for exemple in exemples:
                        print(f"      {exemple}")
            
            self.configurations_extraites['database_schema'] = schema_info
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur lecture database.db: {e}")
    
    def analyser_parametres_xml(self, fichier):
        """Analyse le fichier parametres.xml"""
        try:
            tree = ET.parse(fichier)
            root = tree.getroot()
            
            print(f"✅ parametres.xml chargé (racine: {root.tag})")
            
            # Analyser la structure XML
            def analyser_element(element, niveau=0):
                indent = "  " * niveau
                if element.text and element.text.strip():
                    print(f"{indent}📋 {element.tag}: {element.text.strip()}")
                else:
                    print(f"{indent}📋 {element.tag}")
                
                for child in element:
                    analyser_element(child, niveau + 1)
            
            analyser_element(root)
            
            # Convertir XML en dictionnaire
            def xml_to_dict(element):
                result = {}
                if element.text and element.text.strip():
                    result['text'] = element.text.strip()
                
                for child in element:
                    child_data = xml_to_dict(child)
                    if child.tag in result:
                        if not isinstance(result[child.tag], list):
                            result[child.tag] = [result[child.tag]]
                        result[child.tag].append(child_data)
                    else:
                        result[child.tag] = child_data
                
                return result
            
            xml_dict = xml_to_dict(root)
            self.configurations_extraites['parametres_xml'] = xml_dict
            
            # Afficher le contenu complet
            print(f"\n📋 CONTENU COMPLET parametres.xml:")
            print("=" * 50)
            with open(fichier, 'r', encoding='utf-8') as f:
                print(f.read())
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ Erreur lecture parametres.xml: {e}")
    
    def analyser_secteurs_json(self, fichier):
        """Analyse le fichier secteurs.json"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                secteurs = json.load(f)
            
            print(f"✅ secteurs.json chargé")
            
            if isinstance(secteurs, dict):
                print(f"  📋 {len(secteurs)} secteurs définis:")
                for nom_secteur, config_secteur in secteurs.items():
                    print(f"    🎯 {nom_secteur}")
                    if isinstance(config_secteur, dict):
                        for cle, valeur in config_secteur.items():
                            if isinstance(valeur, list):
                                print(f"      • {cle}: {len(valeur)} éléments")
                            else:
                                print(f"      • {cle}: {valeur}")
            elif isinstance(secteurs, list):
                print(f"  📋 Liste de {len(secteurs)} secteurs")
                for i, secteur in enumerate(secteurs[:3]):  # Afficher les 3 premiers
                    print(f"    🎯 Secteur {i+1}: {secteur}")
            
            self.configurations_extraites['secteurs_json'] = secteurs
            
            # Afficher le contenu complet
            print(f"\n📋 CONTENU COMPLET secteurs.json:")
            print("=" * 50)
            print(json.dumps(secteurs, indent=2, ensure_ascii=False))
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ Erreur lecture secteurs.json: {e}")
    
    def generer_rapport_clonage(self):
        """Génère un rapport pour le clonage"""
        print(f"\n🎯 RAPPORT DE CLONAGE")
        print("=" * 60)
        
        if not self.configurations_extraites:
            print("❌ Aucune configuration extraite")
            return
        
        print(f"✅ {len(self.configurations_extraites)} fichiers analysés:")
        
        for nom_config, contenu in self.configurations_extraites.items():
            print(f"  📄 {nom_config}")
        
        # Sauvegarder toutes les configurations dans un fichier JSON
        fichier_sauvegarde = "configurations_extraites.json"
        try:
            with open(fichier_sauvegarde, 'w', encoding='utf-8') as f:
                json.dump(self.configurations_extraites, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n💾 Configurations sauvegardées dans: {fichier_sauvegarde}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
        
        print(f"\n🚀 PRÊT POUR LE CLONAGE VERS GESTIMMOB")
        print("Les configurations ont été extraites et peuvent maintenant être")
        print("adaptées et intégrées dans votre GestImmob personnalisé.")

def main():
    """Fonction principale"""
    analyseur = AnalyseurConfigurations()
    
    if analyseur.analyser_dossiers():
        analyseur.generer_rapport_clonage()
    else:
        print("❌ Échec de l'analyse des dossiers")

if __name__ == "__main__":
    main()
