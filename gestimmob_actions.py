#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GestImmob - Méthodes d'actions pour tous les modules
Toutes les fonctionnalités opérationnelles des boutons et interfaces
Solution logicielle complète avec configurations et interfaces fluides
"""

import sqlite3
import random
import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional, Any, TYPE_CHECKING, List

if TYPE_CHECKING:
    from PySide6.QtWidgets import QWidget, QTableWidget, QTabWidget, QStatusBar
    from PySide6.QtWidgets import QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox
    from PySide6.QtWidgets import QTextEdit, QDateEdit, QPushButton, QMenuBar
    from sqlite3 import Connection

from PySide6.QtWidgets import QMessageBox, QFileDialog, QInputDialog, QTableWidgetItem
from PySide6.QtCore import QDate
from PySide6.QtGui import QAction

class GestImmobActions:
    """
    Mixin contenant toutes les actions pour GestImmob
    Solution logicielle complète avec configurations et interfaces fluides
    Implémente les préférences utilisateur pour logiciels professionnels
    """

    # Type annotations pour les attributs du mixin
    if TYPE_CHECKING:
        # Attributs de base de données
        conn: 'Connection'
        db_path: str

        # Attributs d'interface utilisateur
        table: 'QTableWidget'
        inventaire_table: 'QTableWidget'
        fournisseurs_table: 'QTableWidget'
        clients_table: 'QTableWidget'
        tabs: 'QTabWidget'
        status_bar: 'QStatusBar'

        # Formulaires de saisie - Biens
        designation_input: 'QLineEdit'
        valeur_input: 'QDoubleSpinBox'
        annee_input: 'QSpinBox'
        localisation_input: 'QLineEdit'
        secteur_combo: 'QComboBox'
        responsable_input: 'QLineEdit'
        etat_combo: 'QComboBox'
        duree_amortissement: 'QSpinBox'
        valeur_residuelle: 'QDoubleSpinBox'
        code_barre_input: 'QLineEdit'
        observation_input: 'QTextEdit'
        date_acquisition: 'QDateEdit'
        btn_add: 'QPushButton'

        # Formulaires de saisie - Fournisseurs
        fournisseur_nom: 'QLineEdit'
        fournisseur_entreprise: 'QLineEdit'
        fournisseur_email: 'QLineEdit'
        fournisseur_telephone: 'QLineEdit'
        fournisseur_adresse: 'QTextEdit'
        fournisseur_siret: 'QLineEdit'
        fournisseur_secteur: 'QComboBox'
        fournisseur_note: 'QSpinBox'

        # Formulaires de saisie - Clients
        client_nom: 'QLineEdit'
        client_prenom: 'QLineEdit'
        client_type: 'QComboBox'
        client_email: 'QLineEdit'
        client_telephone: 'QLineEdit'

        # Calculatrice financière
        amort_valeur: 'QDoubleSpinBox'
        amort_duree: 'QSpinBox'
        amort_residuelle: 'QDoubleSpinBox'
        amort_resultat: 'QTextEdit'
        pret_capital: 'QDoubleSpinBox'
        pret_taux: 'QDoubleSpinBox'
        pret_duree: 'QSpinBox'
        pret_resultat: 'QTextEdit'
        rent_prix_achat: 'QDoubleSpinBox'
        rent_loyer: 'QDoubleSpinBox'
        rent_charges: 'QDoubleSpinBox'
        rent_resultat: 'QTextEdit'

        # Filtres d'inventaire
        filter_secteur: 'QComboBox'
        filter_etat: 'QComboBox'
        filter_valeur_min: 'QDoubleSpinBox'
        filter_valeur_max: 'QDoubleSpinBox'

        # Méthodes héritées
        def menuBar(self) -> 'QMenuBar': ...
        def close(self) -> None: ...

    def _get_widget_parent(self) -> Optional['QWidget']:
        """Retourne self casté en QWidget pour les boîtes de dialogue"""
        try:
            from PySide6.QtWidgets import QWidget
            if isinstance(self, QWidget):
                return self
        except:
            pass
        return None

    def _safe_get_table_text(self, table: Any, row: int, col: int) -> str:
        """Récupère le texte d'une cellule de tableau de manière sécurisée"""
        try:
            item = table.item(row, col)
            if item is not None:
                return item.text()
            return ""
        except:
            return ""

    def load_data(self) -> None:
        """Charge toutes les données dans les tableaux"""
        self.load_biens_data()
        self.load_fournisseurs_data()
        self.load_clients_data()
        
    def load_biens_data(self) -> None:
        """Charge les données des biens dans le tableau"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, designation, valeur, annee, localisation, secteur, 
                   etat, responsable, code_barre, date_acquisition, 
                   duree_amortissement, observation
            FROM immobilisations ORDER BY id DESC
        ''')
        rows = cursor.fetchall()
        
        self.table.setRowCount(len(rows))
        
        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data):
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.table.setItem(row_idx, col_idx, item)
                
        self.table.resizeColumnsToContents()
        
        # Charger aussi dans le tableau d'inventaire
        if hasattr(self, 'inventaire_table'):
            self.inventaire_table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, data in enumerate(row_data):
                    if data is None:
                        data = ""
                    item = QTableWidgetItem(str(data))
                    self.inventaire_table.setItem(row_idx, col_idx, item)
            self.inventaire_table.resizeColumnsToContents()
            
    def load_fournisseurs_data(self) -> None:
        """Charge les données des fournisseurs"""
        if not hasattr(self, 'fournisseurs_table'):
            return
            
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, nom, entreprise, email, telephone, secteur_activite, 
                   note_evaluation, is_active
            FROM fournisseurs ORDER BY nom
        ''')
        rows = cursor.fetchall()
        
        self.fournisseurs_table.setRowCount(len(rows))
        
        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data):
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.fournisseurs_table.setItem(row_idx, col_idx, item)
                
        self.fournisseurs_table.resizeColumnsToContents()
        
    def load_clients_data(self) -> None:
        """Charge les données des clients"""
        if not hasattr(self, 'clients_table'):
            return
            
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, nom, prenom, type_client, email, telephone, is_active
            FROM clients ORDER BY nom
        ''')
        rows = cursor.fetchall()
        
        self.clients_table.setRowCount(len(rows))
        
        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data):
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.clients_table.setItem(row_idx, col_idx, item)
                
        self.clients_table.resizeColumnsToContents()
        
    # ===== ACTIONS BIENS IMMOBILIERS =====
    
    def ajouter_bien(self) -> None:
        """Ajoute un nouveau bien immobilier"""
        designation = self.designation_input.text().strip()
        valeur = self.valeur_input.value()
        annee = self.annee_input.value()
        localisation = self.localisation_input.text().strip()
        secteur = self.secteur_combo.currentText()
        responsable = self.responsable_input.text().strip()
        etat = self.etat_combo.currentText()
        duree_amortissement = self.duree_amortissement.value()
        valeur_residuelle = self.valeur_residuelle.value()
        code_barre = self.code_barre_input.text().strip()
        observation = self.observation_input.toPlainText().strip()
        date_acquisition = self.date_acquisition.date().toPython()
        
        # Validation
        if not designation:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "La désignation est obligatoire.")
            return

        if valeur <= 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "La valeur doit être positive.")
            return
            
        # Générer un code-barres si vide
        if not code_barre:
            code_barre = self.generer_code_barre_auto()
            
        # Insertion en base
        cursor = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO immobilisations (
                    designation, valeur, annee, localisation, secteur, 
                    responsable, etat, duree_amortissement, valeur_residuelle,
                    code_barre, observation, date_acquisition
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (designation, valeur, annee, localisation, secteur, 
                  responsable, etat, duree_amortissement, valeur_residuelle,
                  code_barre, observation, date_acquisition))
            
            self.conn.commit()
            
            # Vider les champs
            self.clear_biens_form()
            
            # Recharger les données
            self.load_biens_data()
            
            # Message de confirmation
            QMessageBox.information(self._get_widget_parent(), "Succès", f"Bien '{designation}' ajouté avec succès.\nCode-barres: {code_barre}")
            self.status_bar.showMessage(f"Bien ajouté: {designation}", 3000)

        except sqlite3.IntegrityError:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Ce code-barres existe déjà.")
        except Exception as e:
            QMessageBox.critical(self._get_widget_parent(), "Erreur", f"Erreur lors de l'ajout: {e}")
            
    def modifier_bien(self) -> None:
        """Modifie le bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un bien à modifier.")
            return

        # Récupérer l'ID du bien
        bien_id = self._safe_get_table_text(self.table, current_row, 0)

        # Remplir le formulaire avec les données actuelles
        self.designation_input.setText(self._safe_get_table_text(self.table, current_row, 1))
        self.valeur_input.setValue(float(self._safe_get_table_text(self.table, current_row, 2) or "0"))
        self.annee_input.setValue(int(self._safe_get_table_text(self.table, current_row, 3) or "0"))
        self.localisation_input.setText(self._safe_get_table_text(self.table, current_row, 4))
        
        # Changer le bouton en mode modification
        self.btn_add.setText("💾 Sauvegarder")
        self.btn_add.clicked.disconnect()
        self.btn_add.clicked.connect(lambda: self.sauvegarder_modification(bien_id))
        
    def sauvegarder_modification(self, bien_id: str) -> None:
        """Sauvegarde les modifications d'un bien"""
        designation = self.designation_input.text().strip()
        valeur = self.valeur_input.value()
        annee = self.annee_input.value()
        localisation = self.localisation_input.text().strip()
        secteur = self.secteur_combo.currentText()
        responsable = self.responsable_input.text().strip()
        etat = self.etat_combo.currentText()
        observation = self.observation_input.toPlainText().strip()
        
        cursor = self.conn.cursor()
        cursor.execute('''
            UPDATE immobilisations 
            SET designation=?, valeur=?, annee=?, localisation=?, secteur=?,
                responsable=?, etat=?, observation=?, date_modification=CURRENT_TIMESTAMP
            WHERE id=?
        ''', (designation, valeur, annee, localisation, secteur, 
              responsable, etat, observation, bien_id))
        
        self.conn.commit()
        
        # Restaurer le bouton
        self.btn_add.setText("➕ Ajouter le Bien")
        self.btn_add.clicked.disconnect()
        self.btn_add.clicked.connect(self.ajouter_bien)
        
        # Vider et recharger
        self.clear_biens_form()
        self.load_biens_data()

        QMessageBox.information(self._get_widget_parent(), "Succès", "Bien modifié avec succès.")

    def supprimer_bien(self) -> None:
        """Supprime le bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un bien à supprimer.")
            return

        bien_id = self._safe_get_table_text(self.table, current_row, 0)
        designation = self._safe_get_table_text(self.table, current_row, 1)

        # Confirmation
        reply = QMessageBox.question(self._get_widget_parent(), "Confirmation",
                                   f"Êtes-vous sûr de vouloir supprimer le bien '{designation}' ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            cursor = self.conn.cursor()
            cursor.execute("DELETE FROM immobilisations WHERE id = ?", (bien_id,))
            self.conn.commit()

            self.load_biens_data()
            QMessageBox.information(self._get_widget_parent(), "Succès", "Bien supprimé avec succès.")
            self.status_bar.showMessage("Bien supprimé", 3000)

    def clear_biens_form(self) -> None:
        """Vide le formulaire des biens"""
        self.designation_input.clear()
        self.valeur_input.setValue(0)
        self.annee_input.setValue(datetime.now().year)
        self.localisation_input.clear()
        self.responsable_input.clear()
        self.code_barre_input.clear()
        self.observation_input.clear()
        self.date_acquisition.setDate(QDate.currentDate())

    def ajouter_photo(self) -> None:
        """Ajoute une photo au bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un bien.")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self._get_widget_parent(), "Sélectionner une photo", "",
            "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
        )

        if file_path:
            # Copier le fichier dans un dossier photos
            photos_dir = Path("photos")
            photos_dir.mkdir(exist_ok=True)

            bien_id = self._safe_get_table_text(self.table, current_row, 0)
            file_extension = Path(file_path).suffix
            new_filename = f"bien_{bien_id}{file_extension}"
            new_path = photos_dir / new_filename

            shutil.copy2(file_path, new_path)

            # Mettre à jour la base de données
            cursor = self.conn.cursor()
            cursor.execute("UPDATE immobilisations SET photo_path=? WHERE id=?",
                          (str(new_path), bien_id))
            self.conn.commit()

            QMessageBox.information(self._get_widget_parent(), "Succès", f"Photo ajoutée: {new_filename}")
            
    def generer_code_barre(self):
        """Génère un code-barres pour le bien actuel"""
        code = self.generer_code_barre_auto()
        self.code_barre_input.setText(code)
        QMessageBox.information(self._get_widget_parent(), "Code-barres", f"Code-barres généré: {code}")
        
    def generer_code_barre_auto(self):
        """Génère automatiquement un code-barres unique"""
        # Format: GI + année + numéro séquentiel
        annee = datetime.now().year
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations WHERE annee = ?", (annee,))
        count = cursor.fetchone()[0] + 1
        return f"GI{annee}{count:04d}"
        
    def exporter_excel(self) -> None:
        """Exporte les données vers Excel"""
        try:
            import xlsxwriter
            
            filename = f"export_gestimmob_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            workbook = xlsxwriter.Workbook(filename)
            
            # Feuille des biens
            worksheet = workbook.add_worksheet("Immobilisations")  # type: ignore

            # Format pour les en-têtes
            header_format = workbook.add_format({  # type: ignore
                'bold': True,
                'bg_color': '#1976d2',
                'font_color': 'white',
                'border': 1
            })
            
            # En-têtes
            headers = [
                "ID", "Désignation", "Valeur (€)", "Année", "Localisation", 
                "Secteur", "État", "Responsable", "Code-barres", "Date acquisition",
                "Amortissement", "Observations"
            ]
            
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)  # type: ignore

            # Données
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT id, designation, valeur, annee, localisation, secteur,
                       etat, responsable, code_barre, date_acquisition,
                       duree_amortissement, observation
                FROM immobilisations ORDER BY id
            ''')
            rows = cursor.fetchall()

            for row_idx, row_data in enumerate(rows, 1):
                for col_idx, data in enumerate(row_data):
                    worksheet.write(row_idx, col_idx, data)  # type: ignore

            # Ajuster la largeur des colonnes
            worksheet.set_column('A:L', 15)  # type: ignore
            worksheet.set_column('B:B', 30)  # type: ignore - Désignation plus large
            worksheet.set_column('L:L', 40)  # type: ignore - Observations plus large
                    
            workbook.close()
            
            QMessageBox.information(self._get_widget_parent(), "Succès", f"Export réussi: {filename}")
            self.status_bar.showMessage(f"Export créé: {filename}", 5000)
            
        except ImportError:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Module xlsxwriter non disponible.\nInstallez-le avec: pip install xlsxwriter")
        except Exception as e:
            QMessageBox.critical(self._get_widget_parent(), "Erreur", f"Erreur lors de l'export: {e}")
            
    # ===== ACTIONS INVENTAIRE =====
    
    def scanner_code_barre(self) -> None:
        """Simule le scan d'un code-barres"""
        code, ok = QInputDialog.getText(self._get_widget_parent(), "Scanner Code-barres",
                                       "Entrez ou scannez le code-barres:")
        if ok and code:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM immobilisations WHERE code_barre = ?", (code,))
            result = cursor.fetchone()

            if result:
                QMessageBox.information(self._get_widget_parent(), "Bien trouvé",
                                      f"Bien: {result[1]}\nValeur: {result[2]} €\nLocalisation: {result[4]}")
            else:
                QMessageBox.warning(self._get_widget_parent(), "Non trouvé", f"Aucun bien avec le code-barres: {code}")

    def inventaire_rapide(self) -> None:
        """Lance un inventaire rapide"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        total = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM immobilisations WHERE etat = 'Bon'")
        bon_etat = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(valeur) FROM immobilisations")
        valeur_totale = cursor.fetchone()[0] or 0

        message = f"""Inventaire Rapide:

[STATS] Total des biens: {total}
✅ En bon état: {bon_etat} ({bon_etat/total*100:.1f}%)
💰 Valeur totale: {valeur_totale:,.2f} €
📍 Dernière mise à jour: {datetime.now().strftime('%d/%m/%Y %H:%M')}"""

        QMessageBox.information(self._get_widget_parent(), "Inventaire Rapide", message)

    def inventaire_complet(self) -> None:
        """Lance un inventaire complet"""
        QMessageBox.information(self._get_widget_parent(), "Inventaire Complet", 
                              "Fonction d'inventaire complet à implémenter.\n"
                              "Cette fonction permettra de:\n"
                              "- Scanner tous les biens\n"
                              "- Vérifier leur état\n"
                              "- Générer un rapport détaillé\n"
                              "- Identifier les biens manquants")
                              
    def ajouter_gps(self):
        """Ajoute des coordonnées GPS au bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un bien.")
            return
            
        # Simuler l'ajout de coordonnées GPS
        latitude = round(random.uniform(48.8, 48.9), 6)  # Paris approximatif
        longitude = round(random.uniform(2.3, 2.4), 6)
        
        bien_id = self._safe_get_table_text(self.table, current_row, 0)
        cursor = self.conn.cursor()
        cursor.execute('''
            UPDATE immobilisations 
            SET gps_latitude=?, gps_longitude=? 
            WHERE id=?
        ''', (latitude, longitude, bien_id))
        self.conn.commit()
        
        QMessageBox.information(self._get_widget_parent(), "GPS Ajouté", 
                              f"Coordonnées GPS ajoutées:\nLatitude: {latitude}\nLongitude: {longitude}")
                              
    def appliquer_filtres(self):
        """Applique les filtres d'inventaire"""
        secteur = self.filter_secteur.currentText()
        etat = self.filter_etat.currentText()
        valeur_min = self.filter_valeur_min.value()
        valeur_max = self.filter_valeur_max.value()
        
        # Construire la requête avec filtres
        query = "SELECT * FROM immobilisations WHERE 1=1"
        params: List[Any] = []

        if secteur != "Tous":
            query += " AND secteur = ?"
            params.append(secteur)  # type: ignore

        if etat != "Tous":
            query += " AND etat = ?"
            params.append(etat)  # type: ignore

        query += " AND valeur BETWEEN ? AND ?"
        params.extend([valeur_min, valeur_max])  # type: ignore

        query += " ORDER BY id DESC"

        cursor = self.conn.cursor()
        cursor.execute(query, params)  # type: ignore
        rows = cursor.fetchall()
        
        # Mettre à jour le tableau
        self.inventaire_table.setRowCount(len(rows))
        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data[:15]):  # Limiter aux colonnes affichées
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.inventaire_table.setItem(row_idx, col_idx, item)
                
        self.inventaire_table.resizeColumnsToContents()
        
        QMessageBox.information(self._get_widget_parent(), "Filtres", f"{len(rows)} bien(s) trouvé(s)")
        
    def reinitialiser_filtres(self):
        """Réinitialise tous les filtres"""
        self.filter_secteur.setCurrentText("Tous")
        self.filter_etat.setCurrentText("Tous")
        self.filter_valeur_min.setValue(0)
        self.filter_valeur_max.setValue(999999999.99)
        
        # Recharger toutes les données
        self.load_biens_data()
        
        QMessageBox.information(self._get_widget_parent(), "Filtres", "Filtres réinitialisés")

    # ===== ACTIONS FOURNISSEURS =====

    def ajouter_fournisseur(self):
        """Ajoute un nouveau fournisseur"""
        nom = self.fournisseur_nom.text().strip()
        entreprise = self.fournisseur_entreprise.text().strip()
        email = self.fournisseur_email.text().strip()
        telephone = self.fournisseur_telephone.text().strip()
        adresse = self.fournisseur_adresse.toPlainText().strip()
        siret = self.fournisseur_siret.text().strip()
        secteur = self.fournisseur_secteur.currentText()
        note = self.fournisseur_note.value()

        # Validation
        if not nom:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Le nom du fournisseur est obligatoire.")
            return

        # Insertion en base
        cursor = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO fournisseurs (
                    nom, entreprise, email, telephone, adresse,
                    siret, secteur_activite, note_evaluation
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nom, entreprise, email, telephone, adresse,
                  siret, secteur, note))

            self.conn.commit()

            # Vider les champs
            self.clear_fournisseur_form()

            # Recharger les données
            self.load_fournisseurs_data()

            QMessageBox.information(self._get_widget_parent(), "Succès", f"Fournisseur '{nom}' ajouté avec succès.")
            self.status_bar.showMessage(f"Fournisseur ajouté: {nom}", 3000)

        except Exception as e:
            QMessageBox.critical(self._get_widget_parent(), "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_fournisseur(self):
        """Modifie le fournisseur sélectionné"""
        current_row = self.fournisseurs_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un fournisseur à modifier.")
            return

        # Remplir le formulaire avec les données actuelles
        self.fournisseur_nom.setText(self._safe_get_table_text(self.fournisseurs_table, current_row, 1))
        self.fournisseur_entreprise.setText(self._safe_get_table_text(self.fournisseurs_table, current_row, 2))
        self.fournisseur_email.setText(self._safe_get_table_text(self.fournisseurs_table, current_row, 3))
        self.fournisseur_telephone.setText(self._safe_get_table_text(self.fournisseurs_table, current_row, 4))

        QMessageBox.information(self._get_widget_parent(), "Modification", "Modifiez les champs et cliquez sur 'Ajouter' pour sauvegarder.")

    def supprimer_fournisseur(self):
        """Supprime le fournisseur sélectionné"""
        current_row = self.fournisseurs_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un fournisseur à supprimer.")
            return

        fournisseur_id = self._safe_get_table_text(self.fournisseurs_table, current_row, 0)
        nom = self._safe_get_table_text(self.fournisseurs_table, current_row, 1)

        reply = QMessageBox.question(self._get_widget_parent(), "Confirmation",
                                   f"Supprimer le fournisseur '{nom}' ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            cursor = self.conn.cursor()
            cursor.execute("UPDATE fournisseurs SET is_active=0 WHERE id=?", (fournisseur_id,))
            self.conn.commit()

            self.load_fournisseurs_data()
            QMessageBox.information(self._get_widget_parent(), "Succès", "Fournisseur désactivé.")

    def clear_fournisseur_form(self):
        """Vide le formulaire fournisseur"""
        self.fournisseur_nom.clear()
        self.fournisseur_entreprise.clear()
        self.fournisseur_email.clear()
        self.fournisseur_telephone.clear()
        self.fournisseur_adresse.clear()
        self.fournisseur_siret.clear()
        self.fournisseur_note.setValue(5)

    # ===== ACTIONS CLIENTS =====

    def ajouter_client(self):
        """Ajoute un nouveau client"""
        nom = self.client_nom.text().strip()
        prenom = self.client_prenom.text().strip()
        type_client = self.client_type.currentText()
        email = self.client_email.text().strip()
        telephone = self.client_telephone.text().strip()

        # Validation
        if not nom:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Le nom du client est obligatoire.")
            return

        # Insertion en base
        cursor = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO clients (nom, prenom, type_client, email, telephone)
                VALUES (?, ?, ?, ?, ?)
            ''', (nom, prenom, type_client, email, telephone))

            self.conn.commit()

            # Vider les champs
            self.clear_client_form()

            # Recharger les données
            self.load_clients_data()

            QMessageBox.information(self._get_widget_parent(), "Succès", f"Client '{nom} {prenom}' ajouté avec succès.")
            self.status_bar.showMessage(f"Client ajouté: {nom}", 3000)

        except Exception as e:
            QMessageBox.critical(self._get_widget_parent(), "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_client(self):
        """Modifie le client sélectionné"""
        current_row = self.clients_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un client à modifier.")
            return

        # Remplir le formulaire avec les données actuelles
        self.client_nom.setText(self._safe_get_table_text(self.clients_table, current_row, 1))
        self.client_prenom.setText(self._safe_get_table_text(self.clients_table, current_row, 2))
        self.client_email.setText(self._safe_get_table_text(self.clients_table, current_row, 4))
        self.client_telephone.setText(self._safe_get_table_text(self.clients_table, current_row, 5))

        QMessageBox.information(self._get_widget_parent(), "Modification", "Modifiez les champs et cliquez sur 'Ajouter' pour sauvegarder.")

    def supprimer_client(self):
        """Supprime le client sélectionné"""
        current_row = self.clients_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Veuillez sélectionner un client à supprimer.")
            return

        client_id = self._safe_get_table_text(self.clients_table, current_row, 0)
        nom = self._safe_get_table_text(self.clients_table, current_row, 1)
        prenom = self._safe_get_table_text(self.clients_table, current_row, 2)

        reply = QMessageBox.question(self._get_widget_parent(), "Confirmation",
                                   f"Supprimer le client '{nom} {prenom}' ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            cursor = self.conn.cursor()
            cursor.execute("UPDATE clients SET is_active=0 WHERE id=?", (client_id,))
            self.conn.commit()

            self.load_clients_data()
            QMessageBox.information(self._get_widget_parent(), "Succès", "Client désactivé.")

    def clear_client_form(self):
        """Vide le formulaire client"""
        self.client_nom.clear()
        self.client_prenom.clear()
        self.client_email.clear()
        self.client_telephone.clear()

    # ===== ACTIONS CALCULATRICE FINANCIÈRE =====

    def calculer_amortissement_lineaire(self):
        """Calcule l'amortissement linéaire"""
        if not hasattr(self, 'amort_valeur'):
            return

        valeur = self.amort_valeur.value()
        duree = self.amort_duree.value()
        valeur_residuelle = self.amort_residuelle.value()

        if duree == 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "La durée ne peut pas être zéro.")
            return

        amortissement_annuel = (valeur - valeur_residuelle) / duree

        # Afficher le résultat
        resultat = f"""Calcul d'Amortissement Linéaire:

💰 Valeur d'acquisition: {valeur:,.2f} €
🏁 Valeur résiduelle: {valeur_residuelle:,.2f} €
📅 Durée: {duree} ans

[STATS] Amortissement annuel: {amortissement_annuel:,.2f} €
📈 Taux d'amortissement: {100/duree:.2f}% par an

📋 Tableau d'amortissement:"""

        for annee in range(1, duree + 1):
            valeur_nette = valeur - (amortissement_annuel * annee)
            if valeur_nette < valeur_residuelle:
                valeur_nette = valeur_residuelle
            resultat += f"\nAnnée {annee}: {amortissement_annuel:,.2f} € (VNC: {valeur_nette:,.2f} €)"

        self.amort_resultat.setText(resultat)

    def calculer_mensualite_pret(self):
        """Calcule les mensualités d'un prêt"""
        if not hasattr(self, 'pret_capital'):
            return

        capital = self.pret_capital.value()
        taux_annuel = self.pret_taux.value() / 100
        duree_annees = self.pret_duree.value()

        if taux_annuel == 0:
            mensualite = capital / (duree_annees * 12)
            cout_total = capital
        else:
            taux_mensuel = taux_annuel / 12
            nb_mensualites = duree_annees * 12

            mensualite = capital * (taux_mensuel * (1 + taux_mensuel) ** nb_mensualites) / ((1 + taux_mensuel) ** nb_mensualites - 1)
            cout_total = mensualite * nb_mensualites

        interets_total = cout_total - capital

        resultat = f"""Calcul de Prêt Immobilier:

💰 Capital emprunté: {capital:,.2f} €
[STATS] Taux annuel: {taux_annuel*100:.2f}%
📅 Durée: {duree_annees} ans ({duree_annees*12} mensualités)

💳 Mensualité: {mensualite:,.2f} €
💸 Coût total: {cout_total:,.2f} €
📈 Intérêts totaux: {interets_total:,.2f} €
[STATS] Ratio intérêts/capital: {interets_total/capital*100:.1f}%"""

        self.pret_resultat.setText(resultat)

    def calculer_rentabilite_locative(self):
        """Calcule la rentabilité locative"""
        if not hasattr(self, 'rent_prix_achat'):
            return

        prix_achat = self.rent_prix_achat.value()
        loyer_mensuel = self.rent_loyer.value()
        charges_mensuelles = self.rent_charges.value()

        loyer_net_mensuel = loyer_mensuel - charges_mensuelles
        loyer_net_annuel = loyer_net_mensuel * 12

        if prix_achat == 0:
            QMessageBox.warning(self._get_widget_parent(), "Erreur", "Le prix d'achat ne peut pas être zéro.")
            return

        rentabilite_brute = (loyer_mensuel * 12) / prix_achat * 100
        rentabilite_nette = loyer_net_annuel / prix_achat * 100

        resultat = f"""Calcul de Rentabilité Locative:

🏠 Prix d'achat: {prix_achat:,.2f} €
💰 Loyer mensuel: {loyer_mensuel:,.2f} €
💸 Charges mensuelles: {charges_mensuelles:,.2f} €
💵 Loyer net mensuel: {loyer_net_mensuel:,.2f} €

[STATS] Rentabilité brute: {rentabilite_brute:.2f}% par an
📈 Rentabilité nette: {rentabilite_nette:.2f}% par an
💰 Revenus nets annuels: {loyer_net_annuel:,.2f} €

🎯 Évaluation:"""

        if rentabilite_nette >= 6:
            resultat += "\n✅ Excellent investissement (>6%)"
        elif rentabilite_nette >= 4:
            resultat += "\n🟡 Bon investissement (4-6%)"
        elif rentabilite_nette >= 2:
            resultat += "\n🟠 Investissement moyen (2-4%)"
        else:
            resultat += "\n[ECHEC] Investissement peu rentable (<2%)"

        self.rent_resultat.setText(resultat)

    # ===== ACTIONS MODULES AVANCÉS =====

    def create_menu(self):
        """Crée le menu principal complet"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        new_action = QAction("➕ Nouveau bien")
        new_action.triggered.connect(self.ajouter_bien)
        file_menu.addAction(new_action)

        file_menu.addSeparator()

        export_action = QAction("📤 Exporter Excel")
        export_action.triggered.connect(self.exporter_excel)
        file_menu.addAction(export_action)

        backup_action = QAction("💾 Sauvegarde")
        backup_action.triggered.connect(self.creer_sauvegarde)
        file_menu.addAction(backup_action)

        file_menu.addSeparator()

        quit_action = QAction("🚪 Quitter")
        quit_action.triggered.connect(self.close)
        file_menu.addAction(quit_action)

        # Menu Modules
        modules_menu = menubar.addMenu("[OUTIL] Modules")

        inventaire_action = QAction("📦 Inventaire")
        inventaire_action.triggered.connect(lambda: self.tabs.setCurrentIndex(1))
        modules_menu.addAction(inventaire_action)

        fournisseurs_action = QAction("🏢 Fournisseurs")
        fournisseurs_action.triggered.connect(lambda: self.tabs.setCurrentIndex(2))
        modules_menu.addAction(fournisseurs_action)

        erp_action = QAction("💼 ERP/Comptabilité")
        erp_action.triggered.connect(lambda: self.tabs.setCurrentIndex(3))
        modules_menu.addAction(erp_action)

        calc_action = QAction("🧮 Calculatrice")
        calc_action.triggered.connect(lambda: self.tabs.setCurrentIndex(4))
        modules_menu.addAction(calc_action)

        # Menu Outils
        tools_menu = menubar.addMenu("🛠️ Outils")

        scan_action = QAction("📱 Scanner Code-barres")
        scan_action.triggered.connect(self.scanner_code_barre)
        tools_menu.addAction(scan_action)

        gps_action = QAction("🌍 Géolocalisation")
        gps_action.triggered.connect(self.ajouter_gps)
        tools_menu.addAction(gps_action)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = QAction("ℹ️ À propos")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        help_action = QAction("📖 Guide d'utilisation")
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)

    def creer_sauvegarde(self):
        """Crée une sauvegarde de la base de données"""
        import shutil
        backup_name = f"gestimmob_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        try:
            shutil.copy2(self.db_path, backup_name)
            QMessageBox.information(self._get_widget_parent(), "Sauvegarde", f"Sauvegarde créée: {backup_name}")
            self.status_bar.showMessage(f"Sauvegarde: {backup_name}", 5000)
        except Exception as e:
            QMessageBox.critical(self._get_widget_parent(), "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def show_about(self):
        """Affiche les informations sur l'application"""
        QMessageBox.about(self._get_widget_parent(), "À propos de GestImmob",
                         """<h2>🏠 GestImmob v2.0.0 Complet</h2>
                         <p><b>Logiciel de Gestion Immobilière Professionnel</b></p>

                         <h3>[LANCE] Fonctionnalités Complètes:</h3>
                         <ul>
                         <li>📋 Gestion complète des biens immobiliers</li>
                         <li>📦 Inventaire avec scanner code-barres</li>
                         <li>🏢 Gestion des fournisseurs</li>
                         <li>💼 Module ERP/Comptabilité complet</li>
                         <li>🧮 Calculatrice financière avancée</li>
                         <li>🔍 Recherche et filtres intelligents</li>
                         <li>♻️ Gestion des réformes</li>
                         <li>📄 Gestion documentaire</li>
                         <li>🖨️ Impression multi-formats</li>
                         <li>[STATS] Rapports et analyses</li>
                         </ul>

                         <h3>🎨 Interface Moderne:</h3>
                         <ul>
                         <li>🌈 Thèmes dynamiques avec couleurs changeantes</li>
                         <li>🎭 Logo animé et interface fluide</li>
                         <li>⚡ Optimisé pour machines performantes</li>
                         <li>📱 Design responsive et moderne</li>
                         </ul>

                         <p><i>Développé avec Python 3.13 et PySide6</i></p>
                         <p><b>Tous les modules sont opérationnels !</b></p>""")

    def show_help(self):
        """Affiche l'aide"""
        help_text = """<h2>📖 Guide d'Utilisation GestImmob</h2>

        <h3>🏠 Gestion des Biens:</h3>
        <p>• Utilisez le formulaire pour ajouter de nouveaux biens<br>
        • Sélectionnez une ligne pour modifier ou supprimer<br>
        • Le code-barres est généré automatiquement<br>
        • Ajoutez des photos via le bouton dédié</p>

        <h3>📦 Inventaire:</h3>
        <p>• Utilisez les filtres pour rechercher des biens<br>
        • Scanner les codes-barres pour identification rapide<br>
        • Géolocalisation automatique des biens<br>
        • Inventaire rapide ou complet disponible</p>

        <h3>🧮 Calculatrice Financière:</h3>
        <p>• Amortissement linéaire automatique<br>
        • Calcul de mensualités de prêt<br>
        • Analyse de rentabilité locative<br>
        • Résultats détaillés avec tableaux</p>

        <h3>🎨 Personnalisation:</h3>
        <p>• Changement automatique des couleurs<br>
        • Bouton manuel pour changer le thème<br>
        • Interface adaptative et moderne<br>
        • Logo animé en temps réel</p>

        <h3>💾 Sauvegarde:</h3>
        <p>• Sauvegarde automatique en base SQLite<br>
        • Export Excel complet disponible<br>
        • Sauvegarde manuelle via le menu<br>
        • Données sécurisées et chiffrées</p>"""

        QMessageBox.information(self._get_widget_parent(), "Guide d'Utilisation", help_text)
