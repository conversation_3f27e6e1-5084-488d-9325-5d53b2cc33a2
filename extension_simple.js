const vscode = require('vscode');

function activate(context) {
    console.log('Robot IA SamNord activé');
    
    // Status bar
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "🤖 Robot IA SamNord";
    statusBarItem.tooltip = "Robot IA SamNord - SamNord@110577";
    statusBarItem.command = "robotIA.activate";
    statusBarItem.show();
    
    // Commande principale
    const activateCommand = vscode.commands.registerCommand('robotIA.activate', () => {
        vscode.window.showInformationMessage('🤖 Robot IA SamNord activé ! SamNord@110577');
        
        // Lancement Robot IA externe
        const terminal = vscode.window.createTerminal('🤖 Robot IA SamNord');
        terminal.sendText('python robot_ia_universel_40_modules.py');
        terminal.show();
    });
    
    // Chat command
    const chatCommand = vscode.commands.registerCommand('robotIA.chat', () => {
        const panel = vscode.window.createWebviewPanel(
            'robotIAChat',
            '🤖 Robot IA SamNord - Chat',
            vscode.ViewColumn.One,
            { enableScripts: true }
        );
        
        panel.webview.html = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { background: #1e1e1e; color: white; font-family: 'Segoe UI'; padding: 20px; }
                .header { text-align: center; border-bottom: 2px solid #007acc; padding-bottom: 15px; }
                .message { background: #2d2d30; padding: 15px; margin: 10px 0; border-radius: 8px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 Robot IA SamNord</h1>
                <p>👤 SamNord@110577 | Interface comme Augment Agent</p>
            </div>
            
            <div class="message">
                <strong>🤖 Robot IA:</strong> Bonjour SamNord@110577 ! Je suis maintenant visible dans VS Code comme extension. 
                Mes 40+ modules sont disponibles : Intelligence IA, WiFi, Bluetooth, Cybersécurité, etc.
            </div>
            
            <div class="message">
                <strong>📦 Modules disponibles:</strong><br>
                🧠 Intelligence IA Transcendante<br>
                📡 WiFi Avancé Complet<br>
                📱 Bluetooth Complet<br>
                🛡️ Cybersécurité Éthique<br>
                🌍 Traduction Temps Réel<br>
                🗣️ Communication Multilingue<br>
                🧠 Mémoire Parfaite<br>
                📱 Tracking Téléphones<br>
                💼 Master Compta Général<br>
                📱 Enregistrement Vidéo
            </div>
            
            <div class="message">
                <strong>🚀 Utilisation:</strong><br>
                • Cliquez sur "🤖 Robot IA SamNord" dans la barre de statut<br>
                • Ctrl+Shift+R pour ce chat<br>
                • Terminal externe lancé automatiquement
            </div>
        </body>
        </html>`;
    });
    
    context.subscriptions.push(activateCommand, chatCommand, statusBarItem);
    
    // Message d'activation
    vscode.window.showInformationMessage('🤖 Robot IA SamNord prêt ! Cliquez sur la barre de statut');
}

function deactivate() {}

module.exports = { activate, deactivate };
