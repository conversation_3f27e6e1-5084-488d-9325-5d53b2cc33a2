#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST IMMÉDIAT - SANS SUBPROCESS
Teste directement tous les fichiers Python
"""

import ast
import sys
import os
from pathlib import Path

def test_file_direct(file_path):
    """Teste un fichier directement sans subprocess"""
    print(f"🔍 Test direct de {file_path.name}...")
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Compilation
        try:
            compile(content, str(file_path), 'exec')
            print(f"✅ {file_path.name}: Compilation réussie")
        except SyntaxError as e:
            print(f"❌ {file_path.name}: Erreur syntaxe ligne {e.lineno}: {e.msg}")
            if e.text:
                print(f"    Code problématique: {e.text.strip()}")
            return False
        except Exception as e:
            print(f"❌ {file_path.name}: Erreur compilation: {e}")
            return False
        
        # Test 2: AST
        try:
            ast.parse(content)
            print(f"✅ {file_path.name}: AST valide")
        except SyntaxError as e:
            print(f"❌ {file_path.name}: Erreur AST ligne {e.lineno}: {e.msg}")
            return False
        
        # Test 3: Vérifications spécifiques
        lines = content.splitlines()
        issues = []
        
        # Vérifier les problèmes connus
        for i, line in enumerate(lines, 1):
            if ': any =' in line:
                issues.append(f"Ligne {i}: 'any' devrait être 'Any'")
            
            if 'encrypt_password(' in line and 'def encrypt_password' not in content:
                issues.append(f"Ligne {i}: Fonction 'encrypt_password' non définie")
            
            if 'get_db_session(' in line and 'def get_db_session' not in content:
                issues.append(f"Ligne {i}: Fonction 'get_db_session' non définie")
        
        if issues:
            print(f"⚠️ {file_path.name}: {len(issues)} problèmes détectés")
            for issue in issues[:3]:  # Limiter l'affichage
                print(f"    {issue}")
            if len(issues) > 3:
                print(f"    ... et {len(issues) - 3} autres problèmes")
        else:
            print(f"✅ {file_path.name}: Aucun problème détecté")
        
        return True
        
    except Exception as e:
        print(f"❌ {file_path.name}: Erreur lecture: {e}")
        return False

def main():
    """Test immédiat de tous les fichiers"""
    print("🚀 TEST IMMÉDIAT DE TOUS LES FICHIERS PYTHON")
    print("=" * 60)
    
    # Informations système
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    print()
    
    # Trouver les fichiers Python
    python_files = []
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.vscode']
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    print(f"📁 Fichiers Python trouvés: {len(python_files)}")
    print()
    
    # Tester chaque fichier
    success_count = 0
    critical_errors = []
    
    for file_path in sorted(python_files):
        if test_file_direct(file_path):
            success_count += 1
        else:
            critical_errors.append(str(file_path))
    
    # Tests spécifiques des fichiers principaux
    print("\n" + "="*60)
    print("🎯 TESTS SPÉCIFIQUES DES FICHIERS PRINCIPAUX")
    print("="*60)
    
    main_files = [
        'src/main.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py',
        'src/auth/user_management.py'
    ]
    
    for file_path in main_files:
        if Path(file_path).exists():
            print(f"\n🔍 Test approfondi de {file_path}...")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Test de compilation ligne par ligne pour trouver l'erreur exacte
                lines = content.splitlines()
                error_found = False
                
                try:
                    compile(content, file_path, 'exec')
                    print(f"✅ {file_path}: PARFAIT - Aucune erreur")
                except SyntaxError as e:
                    print(f"❌ {file_path}: ERREUR ligne {e.lineno}")
                    print(f"    Message: {e.msg}")
                    if e.text:
                        print(f"    Code: {e.text.strip()}")
                    
                    # Essayer de localiser plus précisément
                    if e.lineno and e.lineno <= len(lines):
                        problem_line = lines[e.lineno - 1]
                        print(f"    Ligne problématique: {problem_line.strip()}")
                    
                    error_found = True
                
                if not error_found:
                    # Vérifier les problèmes spécifiques
                    specific_issues = []
                    
                    if ': any =' in content:
                        specific_issues.append("Type 'any' au lieu de 'Any'")
                    
                    if 'encrypt_password(' in content and 'def encrypt_password' not in content:
                        specific_issues.append("Fonction 'encrypt_password' manquante")
                    
                    if 'get_db_session(' in content and 'def get_db_session' not in content:
                        specific_issues.append("Fonction 'get_db_session' manquante")
                    
                    if specific_issues:
                        print(f"⚠️ {file_path}: Problèmes spécifiques détectés:")
                        for issue in specific_issues:
                            print(f"    - {issue}")
                    else:
                        print(f"✅ {file_path}: PARFAIT")
                
            except Exception as e:
                print(f"❌ {file_path}: Erreur lecture: {e}")
        else:
            print(f"❌ {file_path}: FICHIER MANQUANT")
    
    # Résumé final
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DU TEST IMMÉDIAT")
    print("="*60)
    
    success_rate = (success_count / len(python_files)) * 100 if python_files else 0
    
    print(f"✅ Fichiers sans erreur: {success_count}/{len(python_files)}")
    print(f"❌ Fichiers avec erreurs: {len(critical_errors)}")
    print(f"📈 Taux de réussite: {success_rate:.1f}%")
    
    if critical_errors:
        print(f"\n🚨 FICHIERS AVEC ERREURS CRITIQUES:")
        for file_path in critical_errors:
            print(f"  - {file_path}")
    
    if success_rate >= 95:
        print(f"\n🎉 TOUS LES FICHIERS PYTHON SONT PARFAITS !")
        return True
    elif success_rate >= 80:
        print(f"\n✅ La plupart des fichiers sont corrects")
        print(f"⚠️ Corrections mineures nécessaires")
        return True
    else:
        print(f"\n🚨 CORRECTIONS URGENTES NÉCESSAIRES")
        print(f"❌ {len(critical_errors)} fichiers avec erreurs critiques")
        return False

if __name__ == "__main__":
    main()
