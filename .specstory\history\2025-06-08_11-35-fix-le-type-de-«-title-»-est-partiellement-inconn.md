<!-- Generated by SpecStory -->

# /fix Le type de « title » est partiellement inconn (2025-06-08 12:35:07)

_**User**_

/fix Le type de « title » est partiellement inconnu
  Le type de "title" est "(label: str, fontdict: dict[str, Any] | None = None, loc: Literal['left', 'center', 'right'] | None = None, pad: float | None = None, *, y: float | None = None, **kwargs: Unknown) -> Text"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l'avertissement sur le type de `plt.title`, il suffit de préciser explicitement le paramètre `label` (qui est le premier argument positionnel) en utilisant le nom du paramètre. Cela clarifie l'appel pour les outils d'analyse statique comme Pylance.

````python
# ...existing code...
                plt.title(label="Articles par catégorie")
# ...existing code...
````

---

