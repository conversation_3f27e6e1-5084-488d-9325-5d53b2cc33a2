#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour identifier et corriger TOUS les problèmes Python
"""

import ast
import os
import re
import sys
from pathlib import Path

class PythonIssueFixer:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def analyze_file(self, file_path):
        """Analyse un fichier Python pour identifier les problèmes"""
        print(f"\n🔍 Analyse de {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            issues = []
            
            # Test 1: Syntaxe AST
            try:
                ast.parse(content)
                print(f"  ✅ AST: OK")
            except SyntaxError as e:
                issue = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
                issues.append(issue)
                print(f"  ❌ AST: {issue}")
                if e.text:
                    print(f"      Code: {e.text.strip()}")
            
            # Test 2: Compilation
            try:
                compile(content, str(file_path), 'exec')
                print(f"  ✅ Compilation: OK")
            except SyntaxError as e:
                issue = f"Erreur compilation ligne {e.lineno}: {e.msg}"
                issues.append(issue)
                print(f"  ❌ Compilation: {issue}")
            
            # Test 3: Problèmes spécifiques
            lines = content.splitlines()
            
            # Vérifier les imports manquants
            if 'Any' in content and 'from typing import' not in content:
                issues.append("Import 'Any' manquant")
                print(f"  ⚠️ Import 'Any' manquant")
            
            # Vérifier les fonctions appelées mais non définies
            if 'encrypt_password(' in content and 'def encrypt_password' not in content:
                issues.append("Fonction 'encrypt_password' non définie")
                print(f"  ⚠️ Fonction 'encrypt_password' non définie")
            
            if 'get_db_session(' in content and 'def get_db_session' not in content:
                issues.append("Fonction 'get_db_session' non définie")
                print(f"  ⚠️ Fonction 'get_db_session' non définie")
            
            # Vérifier les erreurs de type
            for i, line in enumerate(lines, 1):
                if ': any =' in line.lower():
                    issues.append(f"Ligne {i}: 'any' devrait être 'Any'")
                    print(f"  ⚠️ Ligne {i}: 'any' devrait être 'Any'")
            
            if issues:
                self.issues_found.append({
                    'file': str(file_path),
                    'issues': issues
                })
                return False
            else:
                print(f"  ✅ Aucun problème détecté")
                return True
                
        except Exception as e:
            issue = f"Erreur lecture: {e}"
            self.issues_found.append({
                'file': str(file_path),
                'issues': [issue]
            })
            print(f"  ❌ Erreur: {e}")
            return False
    
    def fix_file(self, file_path):
        """Corrige les problèmes dans un fichier"""
        print(f"\n🔧 Correction de {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            lines = content.splitlines()
            
            # Correction 1: Ajouter import Any si manquant
            if 'Any' in content and 'from typing import' not in content:
                # Trouver où insérer l'import
                insert_pos = 0
                for i, line in enumerate(lines):
                    if line.strip() and not line.startswith('#'):
                        insert_pos = i
                        break
                
                lines.insert(insert_pos, "from typing import Any, Optional, Dict, List, Tuple, Union")
                lines.insert(insert_pos + 1, "")
                self.fixes_applied.append(f"{file_path.name}: Import typing ajouté")
                print(f"  ✅ Import typing ajouté")
            
            # Correction 2: Corriger 'any' en 'Any'
            for i, line in enumerate(lines):
                if ': any =' in line:
                    lines[i] = line.replace(': any =', ': Any =')
                    self.fixes_applied.append(f"{file_path.name}: 'any' corrigé en 'Any' ligne {i+1}")
                    print(f"  ✅ 'any' corrigé en 'Any' ligne {i+1}")
            
            # Correction 3: Ajouter fonctions manquantes
            if 'encrypt_password(' in content and 'def encrypt_password' not in content:
                # Trouver où insérer la fonction
                insert_pos = len(lines)
                for i, line in enumerate(lines):
                    if line.startswith('class ') or line.startswith('def '):
                        insert_pos = i
                        break
                
                encrypt_function = [
                    "",
                    "def encrypt_password(password: str) -> str:",
                    '    """Chiffre un mot de passe avec SHA-256"""',
                    "    import hashlib",
                    "    return hashlib.sha256(password.encode('utf-8')).hexdigest()",
                    ""
                ]
                
                for j, func_line in enumerate(encrypt_function):
                    lines.insert(insert_pos + j, func_line)
                
                self.fixes_applied.append(f"{file_path.name}: Fonction encrypt_password ajoutée")
                print(f"  ✅ Fonction encrypt_password ajoutée")
            
            if 'get_db_session(' in content and 'def get_db_session' not in content:
                # Trouver où insérer la fonction
                insert_pos = len(lines)
                for i, line in enumerate(lines):
                    if line.startswith('class ') or line.startswith('def '):
                        insert_pos = i
                        break
                
                session_function = [
                    "",
                    "def get_db_session():",
                    '    """Retourne une session de base de données SQLAlchemy"""',
                    "    from sqlalchemy import create_engine  # type: ignore",
                    "    from sqlalchemy.orm import sessionmaker  # type: ignore",
                    "    engine = create_engine('sqlite:///users.db', echo=False)  # type: ignore",
                    "    SessionLocal = sessionmaker(bind=engine)  # type: ignore",
                    "    return SessionLocal()  # type: ignore",
                    ""
                ]
                
                for j, func_line in enumerate(session_function):
                    lines.insert(insert_pos + j, func_line)
                
                self.fixes_applied.append(f"{file_path.name}: Fonction get_db_session ajoutée")
                print(f"  ✅ Fonction get_db_session ajoutée")
            
            # Reconstruire le contenu
            new_content = '\n'.join(lines)
            
            # Valider la syntaxe
            try:
                ast.parse(new_content)
                compile(new_content, str(file_path), 'exec')
                
                # Sauvegarder si des changements ont été faits
                if new_content != original_content:
                    # Backup
                    backup_path = file_path.with_suffix('.py.backup_fix')
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(original_content)
                    
                    # Sauvegarder le fichier corrigé
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    print(f"  ✅ Fichier corrigé et sauvegardé")
                    return True
                else:
                    print(f"  ✅ Aucune correction nécessaire")
                    return True
                    
            except (SyntaxError, Exception) as e:
                print(f"  ❌ Erreur après correction: {e}")
                return False
                
        except Exception as e:
            print(f"  ❌ Erreur traitement: {e}")
            return False
    
    def run_complete_fix(self):
        """Exécute la correction complète"""
        print("🚀 CORRECTION COMPLÈTE DE TOUS LES PROBLÈMES PYTHON")
        print("=" * 70)
        
        # Trouver tous les fichiers Python
        python_files = []
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.vscode']
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        
        print(f"📁 Fichiers Python trouvés: {len(python_files)}")
        
        # Phase 1: Analyse
        print(f"\n🔍 PHASE 1: ANALYSE")
        print("-" * 40)
        
        files_with_issues = []
        for file_path in python_files:
            if not self.analyze_file(file_path):
                files_with_issues.append(file_path)
        
        print(f"\n📊 Fichiers avec problèmes: {len(files_with_issues)}")
        
        # Phase 2: Correction
        if files_with_issues:
            print(f"\n🔧 PHASE 2: CORRECTION")
            print("-" * 40)
            
            success_count = 0
            for file_path in files_with_issues:
                if self.fix_file(file_path):
                    success_count += 1
            
            print(f"\n📊 Fichiers corrigés: {success_count}/{len(files_with_issues)}")
        
        # Phase 3: Validation finale
        print(f"\n✅ PHASE 3: VALIDATION FINALE")
        print("-" * 40)
        
        final_success_count = 0
        for file_path in python_files:
            if self.analyze_file(file_path):
                final_success_count += 1
        
        # Résumé final
        print("\n" + "="*70)
        print("📋 RÉSUMÉ FINAL")
        print("="*70)
        
        success_rate = (final_success_count / len(python_files)) * 100 if python_files else 0
        
        print(f"✅ Fichiers sans problème: {final_success_count}/{len(python_files)}")
        print(f"📈 Taux de réussite: {success_rate:.1f}%")
        print(f"🔧 Corrections appliquées: {len(self.fixes_applied)}")
        
        if self.fixes_applied:
            print(f"\n🔧 CORRECTIONS APPLIQUÉES:")
            for fix in self.fixes_applied[:10]:
                print(f"  - {fix}")
            if len(self.fixes_applied) > 10:
                print(f"  ... et {len(self.fixes_applied) - 10} autres")
        
        if success_rate >= 95:
            print(f"\n🎉 TOUS LES PROBLÈMES PYTHON SONT CORRIGÉS !")
            return True
        else:
            print(f"\n⚠️ {len(python_files) - final_success_count} fichiers nécessitent encore des corrections")
            return False

def main():
    """Fonction principale"""
    fixer = PythonIssueFixer()
    success = fixer.run_complete_fix()
    
    if success:
        print("\n🏆 CORRECTION COMPLÈTE RÉUSSIE !")
        print("Tous les problèmes Python ont été résolus.")
    else:
        print("\n⚠️ Correction partielle - vérifiez les erreurs.")
    
    return success

if __name__ == "__main__":
    main()
