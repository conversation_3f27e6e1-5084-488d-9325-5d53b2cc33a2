#!/usr/bin/env python3
"""
Vérification directe de la syntaxe sans subprocess
"""

import ast
import os
import sys

def check_syntax_direct(filepath):
    """Vérifie la syntaxe d'un fichier Python directement"""
    print(f"\n🔍 Vérification de {filepath}...")
    
    if not os.path.exists(filepath):
        print(f"❌ Fichier non trouvé: {filepath}")
        return False
    
    try:
        # Lire le fichier
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ Lecture réussie ({len(content)} caractères)")
        
        # Vérifier qu'il n'est pas vide
        if not content.strip():
            print("❌ Fichier vide")
            return False
        
        # Test AST
        try:
            tree = ast.parse(content)
            print("✅ AST parsing réussi")
        except SyntaxError as e:
            print(f"❌ Erreur AST ligne {e.lineno}: {e.msg}")
            if e.text:
                print(f"   Code problématique: {e.text.strip()}")
            return False
        
        # Test compilation
        try:
            compile(content, filepath, 'exec')
            print("✅ Compilation réussie")
        except SyntaxError as e:
            print(f"❌ Erreur compilation ligne {e.lineno}: {e.msg}")
            if e.text:
                print(f"   Code problématique: {e.text.strip()}")
            return False
        
        # Statistiques
        lines = content.splitlines()
        non_empty_lines = [l for l in lines if l.strip()]
        code_lines = [l for l in lines if l.strip() and not l.strip().startswith('#')]
        
        print(f"📊 Statistiques:")
        print(f"   Lignes totales: {len(lines)}")
        print(f"   Lignes non vides: {len(non_empty_lines)}")
        print(f"   Lignes de code: {len(code_lines)}")
        print(f"   Classes: {content.count('class ')}")
        print(f"   Fonctions: {content.count('def ')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def main():
    """Fonction principale"""
    print("=" * 60)
    print("VÉRIFICATION DIRECTE DE LA SYNTAXE PYTHON")
    print("=" * 60)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Liste des fichiers à vérifier
    files_to_check = [
        'src/main.py',
        'src/auth/user_management.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py'
    ]
    
    success_count = 0
    total_count = 0
    
    for filepath in files_to_check:
        total_count += 1
        if check_syntax_direct(filepath):
            success_count += 1
    
    # Résumé
    print("\n" + "=" * 60)
    print("RÉSUMÉ DE LA VÉRIFICATION")
    print("=" * 60)
    
    success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
    
    print(f"✅ Fichiers valides: {success_count}/{total_count}")
    print(f"📈 Taux de réussite: {success_rate:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 TOUS LES FICHIERS PYTHON SONT PARFAITS !")
        print("✅ Aucune erreur de syntaxe détectée")
        print("✅ Tous les fichiers compilent correctement")
        print("✅ Le code est prêt à l'exécution")
        return True
    else:
        print(f"\n⚠️ {total_count - success_count} fichier(s) avec des problèmes")
        print("❌ Des corrections sont nécessaires")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nCode de sortie: {'0 (succès)' if success else '1 (échec)'}")
    sys.exit(0 if success else 1)
