#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Test rapide et direct de main.py"""

import ast
import sys
import os

def test_syntax():
    """Test syntaxe direct"""
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test AST
        ast.parse(content)
        print("✅ SYNTAXE PARFAITE")
        
        # Compter les améliorations
        metrics = {
            'Annotations ->': content.count('-> None:') + content.count('-> str:') + content.count('-> bool:') + content.count('-> Any'),
            'Variables typées': content.count(': str =') + content.count(': bool =') + content.count(': Any =') + content.count(': bytes ='),
            'Type ignore': content.count('# type: ignore'),
            'Try/except': content.count('try:'),
            'Logging': content.count('logging.'),
            'Fonctions': content.count('def '),
            'Classes': content.count('class ')
        }
        
        print("\n📊 MÉTRIQUES:")
        total_score = 0
        for metric, count in metrics.items():
            print(f"  {metric}: {count}")
            if count >= 10:
                total_score += 15
            elif count >= 5:
                total_score += 10
        
        print(f"\n📈 Score: {total_score}/105")
        
        if total_score >= 90:
            print("🎉 PARFAIT ! Code de qualité professionnelle")
        elif total_score >= 70:
            print("✅ EXCELLENT ! Code de très bonne qualité")
        else:
            print("⚠️ Code correct mais améliorable")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Erreur syntaxe ligne {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TEST RAPIDE DE MAIN.PY")
    print("=" * 30)
    
    if os.path.exists('src/main.py'):
        success = test_syntax()
        sys.exit(0 if success else 1)
    else:
        print("❌ Fichier src/main.py non trouvé")
        sys.exit(1)
