#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GestImmob - Interface Multilingue Complète (Français, العربية, English)
TOUS les modules avec critères détaillés + Rapports + Mail + Couleurs
Inspiré de Sage Immobilisation et Odoo
"""

import sys
import sqlite3
import json
import math
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QFrame,
    QScrollArea, QGridLayout, QComboBox, QLineEdit, QTextEdit,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QDateEdit,
    QCheckBox, QProgressBar, QSystemTrayIcon, QMenu, QSplitter,
    QTabWidget, QGroupBox, QSlider, QToolTip, QMenuBar, QStatusBar,
    QToolBar, QMessageBox, QFileDialog, QInputDialog, QColorDialog
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QPropertyAnimation, QEasingCurve, QSize, QTranslator
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon, QAction, QPalette

class MultilingueManager:
    """Gestionnaire multilingue pour Français, العربية, English"""
    
    def __init__(self):
        self.current_language = "fr"  # Par défaut français
        self.translations = self.load_translations()
    
    def load_translations(self):
        """Charge toutes les traductions"""
        return {
            # Interface principale
            "app_title": {
                "fr": "🏢 GestImmob - Gestion Immobilière Professionnelle",
                "ar": "🏢 جست إيموب - إدارة العقارات المهنية", 
                "en": "🏢 GestImmob - Professional Real Estate Management"
            },
            "menu_file": {
                "fr": "📁 Fichier",
                "ar": "📁 ملف",
                "en": "📁 File"
            },
            "menu_modules": {
                "fr": "📋 Modules", 
                "ar": "📋 وحدات",
                "en": "📋 Modules"
            },
            "menu_tools": {
                "fr": "🔧 Outils",
                "ar": "🔧 أدوات", 
                "en": "🔧 Tools"
            },
            "menu_help": {
                "fr": "❓ Aide",
                "ar": "❓ مساعدة",
                "en": "❓ Help"
            },
            
            # Modules
            "module_gestion_biens": {
                "fr": "🏠 Gestion des Biens",
                "ar": "🏠 إدارة الممتلكات",
                "en": "🏠 Asset Management"
            },
            "module_fournisseurs": {
                "fr": "🏢 Fournisseurs", 
                "ar": "🏢 الموردون",
                "en": "🏢 Suppliers"
            },
            "module_inventaire": {
                "fr": "📦 Inventaire",
                "ar": "📦 المخزون", 
                "en": "📦 Inventory"
            },
            "module_parc_auto": {
                "fr": "🚗 Parc Automobile",
                "ar": "🚗 أسطول السيارات",
                "en": "🚗 Vehicle Fleet"
            },
            "module_animaux": {
                "fr": "🐄 Suivi des Animaux",
                "ar": "🐄 متابعة الحيوانات", 
                "en": "🐄 Animal Tracking"
            },
            "module_travaux": {
                "fr": "🔨 Suivi des Travaux",
                "ar": "🔨 متابعة الأشغال",
                "en": "🔨 Work Tracking"
            },
            "module_outils": {
                "fr": "🔧 Outils de Travaux",
                "ar": "🔧 أدوات العمل",
                "en": "🔧 Work Tools"
            },
            "module_calculatrice": {
                "fr": "🧮 Calculatrice",
                "ar": "🧮 آلة حاسبة",
                "en": "🧮 Calculator"
            },
            "module_documents": {
                "fr": "📄 Documents",
                "ar": "📄 الوثائق",
                "en": "📄 Documents"
            },
            "module_impression": {
                "fr": "🖨️ Impression",
                "ar": "🖨️ الطباعة", 
                "en": "🖨️ Printing"
            },
            "module_archive": {
                "fr": "📚 Archive",
                "ar": "📚 الأرشيف",
                "en": "📚 Archive"
            },
            "module_outillage": {
                "fr": "⚒️ Outillage",
                "ar": "⚒️ الأدوات",
                "en": "⚒️ Tooling"
            },
            "module_moyens_generaux": {
                "fr": "🏭 Moyens Généraux",
                "ar": "🏭 الوسائل العامة",
                "en": "🏭 General Means"
            },
            "module_hotellerie": {
                "fr": "🏨 Hôtellerie",
                "ar": "🏨 الفندقة",
                "en": "🏨 Hospitality"
            },
            "module_agencement": {
                "fr": "🪑 Agencement",
                "ar": "🪑 التأثيث",
                "en": "🪑 Layout"
            },
            "module_batis": {
                "fr": "🏗️ Bâtis",
                "ar": "🏗️ المباني",
                "en": "🏗️ Buildings"
            },
            
            # Boutons communs
            "btn_validate": {
                "fr": "✅ Valider",
                "ar": "✅ تأكيد",
                "en": "✅ Validate"
            },
            "btn_modify": {
                "fr": "✏️ Modifier", 
                "ar": "✏️ تعديل",
                "en": "✏️ Modify"
            },
            "btn_cancel": {
                "fr": "❌ Annuler",
                "ar": "❌ إلغاء",
                "en": "❌ Cancel"
            },
            "btn_back": {
                "fr": "🔙 Retour",
                "ar": "🔙 رجوع",
                "en": "🔙 Back"
            },
            "btn_print": {
                "fr": "🖨️ Imprimer",
                "ar": "🖨️ طباعة",
                "en": "🖨️ Print"
            },
            "btn_email": {
                "fr": "📧 Envoyer par mail",
                "ar": "📧 إرسال بالبريد",
                "en": "📧 Send by email"
            },
            "btn_report": {
                "fr": "📊 Rapport",
                "ar": "📊 تقرير", 
                "en": "📊 Report"
            },
            
            # Critères communs
            "field_code": {
                "fr": "🔢 Code",
                "ar": "🔢 الرمز",
                "en": "🔢 Code"
            },
            "field_designation": {
                "fr": "📝 Désignation",
                "ar": "📝 التسمية",
                "en": "📝 Designation"
            },
            "field_value": {
                "fr": "💰 Valeur",
                "ar": "💰 القيمة",
                "en": "💰 Value"
            },
            "field_date": {
                "fr": "📅 Date",
                "ar": "📅 التاريخ",
                "en": "📅 Date"
            },
            "field_observation": {
                "fr": "📋 Observation",
                "ar": "📋 ملاحظة",
                "en": "📋 Observation"
            },
            "field_status": {
                "fr": "📊 Statut",
                "ar": "📊 الحالة",
                "en": "📊 Status"
            },
            "field_category": {
                "fr": "📂 Catégorie",
                "ar": "📂 الفئة", 
                "en": "📂 Category"
            },
            "field_location": {
                "fr": "📍 Localisation",
                "ar": "📍 الموقع",
                "en": "📍 Location"
            },
            
            # Rapports
            "report_title": {
                "fr": "📊 Rapport Détaillé",
                "ar": "📊 تقرير مفصل",
                "en": "📊 Detailed Report"
            },
            "report_signature": {
                "fr": "✍️ Signature",
                "ar": "✍️ التوقيع",
                "en": "✍️ Signature"
            },
            "report_date": {
                "fr": "📅 Date du rapport",
                "ar": "📅 تاريخ التقرير",
                "en": "📅 Report date"
            },
            "report_hierarchy": {
                "fr": "👥 Hiérarchie",
                "ar": "👥 التسلسل الهرمي",
                "en": "👥 Hierarchy"
            }
        }
    
    def get_text(self, key):
        """Récupère le texte traduit"""
        if key in self.translations:
            return self.translations[key].get(self.current_language, self.translations[key]["fr"])
        return key
    
    def set_language(self, language):
        """Change la langue"""
        if language in ["fr", "ar", "en"]:
            self.current_language = language

class ColorManager:
    """Gestionnaire de couleurs personnalisables"""
    
    def __init__(self):
        self.color_schemes = {
            "bleu_turquoise": {
                "primary": "#40E0D0",
                "secondary": "#20B2AA", 
                "accent": "#00FFFF",
                "background": "#F0FFFF",
                "text": "#000000",
                "button": "#48D1CC"
            },
            "sage_classique": {
                "primary": "#2E8B57",
                "secondary": "#228B22",
                "accent": "#32CD32", 
                "background": "#F0FFF0",
                "text": "#000000",
                "button": "#3CB371"
            },
            "odoo_style": {
                "primary": "#714B67",
                "secondary": "#875A7B",
                "accent": "#A24689",
                "background": "#F9F9F9", 
                "text": "#000000",
                "button": "#8F5A8A"
            },
            "professionnel": {
                "primary": "#1976D2",
                "secondary": "#1565C0",
                "accent": "#2196F3",
                "background": "#E3F2FD",
                "text": "#000000", 
                "button": "#1E88E5"
            },
            "personnalise": {
                "primary": "#40E0D0",
                "secondary": "#20B2AA",
                "accent": "#00FFFF",
                "background": "#FFFFFF",
                "text": "#000000",
                "button": "#48D1CC"
            }
        }
        self.current_scheme = "bleu_turquoise"
    
    def get_colors(self):
        """Retourne le schéma de couleurs actuel"""
        return self.color_schemes[self.current_scheme]
    
    def set_scheme(self, scheme_name):
        """Change le schéma de couleurs"""
        if scheme_name in self.color_schemes:
            self.current_scheme = scheme_name
    
    def customize_color(self, color_type, color_value):
        """Personnalise une couleur spécifique"""
        if color_type in self.color_schemes["personnalise"]:
            self.color_schemes["personnalise"][color_type] = color_value

class RapportManager:
    """Gestionnaire de rapports multilingues avec signatures"""
    
    def __init__(self, multilingue_manager, color_manager):
        self.ml = multilingue_manager
        self.cm = color_manager
    
    def generer_rapport(self, module_name, data, language="fr"):
        """Génère un rapport dans la langue spécifiée"""
        self.ml.set_language(language)
        
        rapport_html = f"""
        <!DOCTYPE html>
        <html dir="{'rtl' if language == 'ar' else 'ltr'}">
        <head>
            <meta charset="UTF-8">
            <title>{self.ml.get_text('report_title')} - {module_name}</title>
            <style>
                body {{
                    font-family: {'Tahoma, Arial' if language == 'ar' else 'Arial, sans-serif'};
                    background: linear-gradient(135deg, {self.cm.get_colors()['primary']}, {self.cm.get_colors()['secondary']});
                    margin: 0;
                    padding: 20px;
                    direction: {'rtl' if language == 'ar' else 'ltr'};
                }}
                .rapport-container {{
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    max-width: 800px;
                    margin: 0 auto;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid {self.cm.get_colors()['primary']};
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .title {{
                    color: {self.cm.get_colors()['primary']};
                    font-size: 24pt;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
                .subtitle {{
                    color: {self.cm.get_colors()['secondary']};
                    font-size: 16pt;
                    margin-bottom: 20px;
                }}
                .data-section {{
                    margin: 20px 0;
                    padding: 15px;
                    background: {self.cm.get_colors()['background']};
                    border-radius: 10px;
                    border-left: 5px solid {self.cm.get_colors()['accent']};
                }}
                .data-row {{
                    display: flex;
                    justify-content: space-between;
                    margin: 10px 0;
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }}
                .data-label {{
                    font-weight: bold;
                    color: {self.cm.get_colors()['primary']};
                    min-width: 200px;
                }}
                .data-value {{
                    color: {self.cm.get_colors()['text']};
                }}
                .signature-section {{
                    margin-top: 50px;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                }}
                .signature-box {{
                    text-align: center;
                    border-top: 2px solid {self.cm.get_colors()['primary']};
                    padding-top: 10px;
                    min-width: 200px;
                }}
                .date-rapport {{
                    text-align: right;
                    color: {self.cm.get_colors()['secondary']};
                    font-style: italic;
                    margin-top: 20px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    color: {self.cm.get_colors()['secondary']};
                    font-size: 12pt;
                }}
            </style>
        </head>
        <body>
            <div class="rapport-container">
                <div class="header">
                    <div class="title">{self.ml.get_text('report_title')}</div>
                    <div class="subtitle">{module_name}</div>
                </div>
                
                <div class="data-section">
                    <h3 style="color: {self.cm.get_colors()['primary']};">📊 {self.ml.get_text('field_designation')}</h3>
                    {self._generer_donnees_html(data)}
                </div>
                
                <div class="signature-section">
                    <div class="signature-box">
                        <div>{self.ml.get_text('report_signature')}</div>
                        <div style="margin-top: 40px;">_________________</div>
                        <div style="margin-top: 10px; font-size: 12pt;">{self.ml.get_text('report_hierarchy')}</div>
                    </div>
                    <div class="signature-box">
                        <div>{self.ml.get_text('report_signature')}</div>
                        <div style="margin-top: 40px;">_________________</div>
                        <div style="margin-top: 10px; font-size: 12pt;">Responsable</div>
                    </div>
                </div>
                
                <div class="date-rapport">
                    {self.ml.get_text('report_date')}: {datetime.now().strftime('%d/%m/%Y %H:%M')}
                </div>
                
                <div class="footer">
                    🏢 GestImmob - {self.ml.get_text('app_title')}
                </div>
            </div>
        </body>
        </html>
        """
        
        return rapport_html
    
    def _generer_donnees_html(self, data):
        """Génère le HTML pour les données"""
        html = ""
        for key, value in data.items():
            html += f"""
            <div class="data-row">
                <div class="data-label">{key}:</div>
                <div class="data-value">{value}</div>
            </div>
            """
        return html
    
    def sauvegarder_rapport(self, rapport_html, filename):
        """Sauvegarde le rapport en HTML"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(rapport_html)
            return True
        except Exception as e:
            print(f"Erreur sauvegarde rapport: {e}")
            return False

class EmailManager:
    """Gestionnaire d'envoi d'emails"""
    
    def __init__(self):
        self.smtp_config = {
            "server": "smtp.gmail.com",
            "port": 587,
            "username": "",
            "password": "",
            "use_tls": True
        }
    
    def configurer_smtp(self, server, port, username, password, use_tls=True):
        """Configure les paramètres SMTP"""
        self.smtp_config = {
            "server": server,
            "port": port, 
            "username": username,
            "password": password,
            "use_tls": use_tls
        }
    
    def envoyer_rapport(self, destinataire, sujet, rapport_html, fichier_joint=None):
        """Envoie un rapport par email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.smtp_config['username']
            msg['To'] = destinataire
            msg['Subject'] = sujet
            
            # Corps du message
            msg.attach(MIMEText(rapport_html, 'html', 'utf-8'))
            
            # Fichier joint si fourni
            if fichier_joint:
                with open(fichier_joint, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {fichier_joint}'
                    )
                    msg.attach(part)
            
            # Envoi
            server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
            if self.smtp_config['use_tls']:
                server.starttls()
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            print(f"Erreur envoi email: {e}")
            return False

class ModuleGestionBiens(QDialog):
    """Module Gestion des Biens avec critères détaillés inspiré Sage/Odoo"""

    def __init__(self, parent=None, ml_manager=None, color_manager=None):
        super().__init__(parent)
        self.ml = ml_manager
        self.cm = color_manager
        self.setWindowTitle(self.ml.get_text("module_gestion_biens"))
        self.setFixedSize(1200, 800)
        self.setup_gestion_biens()

    def setup_gestion_biens(self):
        """Configure le module Gestion des Biens"""
        layout = QVBoxLayout(self)

        # Style avec couleurs personnalisables
        colors = self.cm.get_colors()
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['primary']}, stop:1 {colors['secondary']});
                border: 2px solid {colors['accent']};
            }}
            QLineEdit, QComboBox, QTextEdit, QDateEdit, QSpinBox {{
                background-color: {colors['background']};
                color: {colors['text']};
                border: 2px solid {colors['accent']};
                border-radius: 5px;
                padding: 8px;
                font-size: 12pt;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['button']}, stop:1 {colors['secondary']});
                color: white;
                border: 2px solid {colors['accent']};
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['accent']}, stop:1 {colors['primary']});
            }}
            QLabel {{
                color: white;
                font-size: 14pt;
                font-weight: bold;
            }}
        """)

        # Titre
        title = QLabel(self.ml.get_text("module_gestion_biens"))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet(f"""
            QLabel {{
                background-color: rgba(0,0,0,0.3);
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
                font-size: 18pt;
            }}
        """)
        layout.addWidget(title)

        # Formulaire avec critères détaillés
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)

        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)

        # TOUS les critères détaillés pour Gestion des Biens
        self.create_gestion_biens_form(form_layout)

        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)

        # Boutons d'action avec rapports et email
        self.create_action_buttons(layout)

    def create_gestion_biens_form(self, form_layout):
        """Crée le formulaire détaillé pour Gestion des Biens"""

        # Identification du bien
        self.code_bien = QLineEdit()
        self.code_bien.setPlaceholderText("INV001 (automatique)")
        form_layout.addRow(f"🔢 {self.ml.get_text('field_code')}:", self.code_bien)

        self.designation = QLineEdit()
        self.designation.setPlaceholderText(self.ml.get_text('field_designation'))
        form_layout.addRow(f"📝 {self.ml.get_text('field_designation')}:", self.designation)

        # Catégorie détaillée (inspiré Sage/Odoo)
        self.categorie = QComboBox()
        self.categorie.addItems([
            "Immobilier", "Mobilier", "Informatique", "Véhicules", "Équipements industriels",
            "Matériel médical", "Outillage", "Électroménager", "Audiovisuel", "Sécurité",
            "Climatisation", "Éclairage", "Télécommunications", "Autre"
        ])
        form_layout.addRow(f"📂 {self.ml.get_text('field_category')}:", self.categorie)

        # Sous-catégorie
        self.sous_categorie = QComboBox()
        self.sous_categorie.addItems([
            "Bureautique", "Serveur", "Réseau", "Impression", "Stockage", "Sécurité IT",
            "Développement", "Formation", "Maintenance", "Support", "Autre"
        ])
        form_layout.addRow("📋 Sous-catégorie:", self.sous_categorie)

        # Informations financières
        self.valeur_acquisition = QSpinBox()
        self.valeur_acquisition.setRange(0, 9999999)
        self.valeur_acquisition.setSuffix(" €")
        form_layout.addRow(f"💰 {self.ml.get_text('field_value')} acquisition:", self.valeur_acquisition)

        self.valeur_actuelle = QSpinBox()
        self.valeur_actuelle.setRange(0, 9999999)
        self.valeur_actuelle.setSuffix(" €")
        form_layout.addRow("💵 Valeur actuelle:", self.valeur_actuelle)

        self.taux_amortissement = QComboBox()
        self.taux_amortissement.addItems([
            "Linéaire 5 ans", "Linéaire 10 ans", "Linéaire 20 ans", "Dégressif",
            "Exceptionnel", "Non amortissable", "Calcul automatique"
        ])
        form_layout.addRow("📈 Amortissement:", self.taux_amortissement)

        # Localisation détaillée
        self.batiment = QComboBox()
        self.batiment.addItems([
            "Bâtiment A", "Bâtiment B", "Bâtiment C", "Entrepôt", "Atelier",
            "Bureau principal", "Annexe", "Extérieur", "Autre"
        ])
        form_layout.addRow("🏢 Bâtiment:", self.batiment)

        self.etage = QComboBox()
        self.etage.addItems([
            "Rez-de-chaussée", "1er étage", "2ème étage", "3ème étage", "Sous-sol",
            "Mezzanine", "Terrasse", "Non applicable"
        ])
        form_layout.addRow("🏗️ Étage:", self.etage)

        self.bureau_salle = QLineEdit()
        self.bureau_salle.setPlaceholderText("Numéro bureau/salle")
        form_layout.addRow("🚪 Bureau/Salle:", self.bureau_salle)

        self.coordonnees_gps = QLineEdit()
        self.coordonnees_gps.setPlaceholderText("Latitude, Longitude")
        form_layout.addRow("📍 Coordonnées GPS:", self.coordonnees_gps)

        # État et maintenance
        self.etat_general = QComboBox()
        self.etat_general.addItems([
            "Neuf", "Excellent", "Très bon", "Bon", "Correct", "Usagé",
            "Défaillant", "Hors service", "À réformer"
        ])
        form_layout.addRow(f"🔧 {self.ml.get_text('field_status')}:", self.etat_general)

        self.derniere_maintenance = QDateEdit()
        self.derniere_maintenance.setCalendarPopup(True)
        form_layout.addRow("🔧 Dernière maintenance:", self.derniere_maintenance)

        self.prochaine_maintenance = QDateEdit()
        self.prochaine_maintenance.setCalendarPopup(True)
        form_layout.addRow("📅 Prochaine maintenance:", self.prochaine_maintenance)

        self.frequence_maintenance = QComboBox()
        self.frequence_maintenance.addItems([
            "Mensuelle", "Trimestrielle", "Semestrielle", "Annuelle",
            "Selon usage", "Sur panne", "Non planifiée"
        ])
        form_layout.addRow("⏰ Fréquence maintenance:", self.frequence_maintenance)

        # Responsabilités
        self.responsable_principal = QComboBox()
        self.responsable_principal.addItems([
            "Directeur", "Chef de service", "Responsable IT", "Responsable maintenance",
            "Gestionnaire", "Technicien", "Utilisateur final", "Autre"
        ])
        form_layout.addRow("👤 Responsable principal:", self.responsable_principal)

        self.utilisateur_principal = QLineEdit()
        self.utilisateur_principal.setPlaceholderText("Nom utilisateur principal")
        form_layout.addRow("👥 Utilisateur principal:", self.utilisateur_principal)

        self.service_affectation = QComboBox()
        self.service_affectation.addItems([
            "Direction", "Comptabilité", "Ressources Humaines", "Commercial",
            "Production", "Maintenance", "Informatique", "Sécurité", "Autre"
        ])
        form_layout.addRow("🏢 Service d'affectation:", self.service_affectation)

        # Fournisseur et garantie
        self.fournisseur = QLineEdit()
        self.fournisseur.setPlaceholderText("Nom du fournisseur")
        form_layout.addRow("🏭 Fournisseur:", self.fournisseur)

        self.numero_facture = QLineEdit()
        self.numero_facture.setPlaceholderText("Numéro de facture")
        form_layout.addRow("📄 N° Facture:", self.numero_facture)

        self.date_acquisition = QDateEdit()
        self.date_acquisition.setCalendarPopup(True)
        form_layout.addRow(f"📅 {self.ml.get_text('field_date')} acquisition:", self.date_acquisition)

        self.duree_garantie = QComboBox()
        self.duree_garantie.addItems([
            "6 mois", "1 an", "2 ans", "3 ans", "5 ans", "10 ans",
            "À vie", "Expirée", "Non garantie"
        ])
        form_layout.addRow("🛡️ Durée garantie:", self.duree_garantie)

        self.fin_garantie = QDateEdit()
        self.fin_garantie.setCalendarPopup(True)
        form_layout.addRow("📅 Fin de garantie:", self.fin_garantie)

        # Caractéristiques techniques
        self.marque = QLineEdit()
        self.marque.setPlaceholderText("Marque du bien")
        form_layout.addRow("🏭 Marque:", self.marque)

        self.modele = QLineEdit()
        self.modele.setPlaceholderText("Modèle du bien")
        form_layout.addRow("📱 Modèle:", self.modele)

        self.numero_serie = QLineEdit()
        self.numero_serie.setPlaceholderText("Numéro de série")
        form_layout.addRow("🔢 N° Série:", self.numero_serie)

        self.specifications = QTextEdit()
        self.specifications.setPlaceholderText("Spécifications techniques détaillées")
        self.specifications.setMaximumHeight(100)
        form_layout.addRow("⚙️ Spécifications:", self.specifications)

        # Assurance et conformité
        self.assurance = QComboBox()
        self.assurance.addItems([
            "Assurance multirisque", "Assurance spécifique", "Auto-assurance",
            "Non assuré", "En cours", "Expirée"
        ])
        form_layout.addRow("🛡️ Assurance:", self.assurance)

        self.numero_police = QLineEdit()
        self.numero_police.setPlaceholderText("Numéro de police d'assurance")
        form_layout.addRow("📄 N° Police:", self.numero_police)

        self.conformite_ce = QComboBox()
        self.conformite_ce.addItems(["Conforme CE", "Non conforme", "Non applicable", "En cours"])
        form_layout.addRow("✅ Conformité CE:", self.conformite_ce)

        self.certifications = QLineEdit()
        self.certifications.setPlaceholderText("ISO, NF, autres certifications")
        form_layout.addRow("🏆 Certifications:", self.certifications)

        # Observations et notes
        self.observation = QTextEdit()
        self.observation.setPlaceholderText(self.ml.get_text('field_observation'))
        self.observation.setMaximumHeight(80)
        form_layout.addRow(f"📋 {self.ml.get_text('field_observation')}:", self.observation)

        self.notes_internes = QTextEdit()
        self.notes_internes.setPlaceholderText("Notes internes, historique, incidents")
        self.notes_internes.setMaximumHeight(80)
        form_layout.addRow("📝 Notes internes:", self.notes_internes)

    def create_action_buttons(self, layout):
        """Crée les boutons d'action avec rapports multilingues"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # Boutons principaux
        validate_btn = QPushButton(self.ml.get_text("btn_validate"))
        validate_btn.clicked.connect(self.validate_data)
        buttons_layout.addWidget(validate_btn)

        modify_btn = QPushButton(self.ml.get_text("btn_modify"))
        modify_btn.clicked.connect(self.modify_data)
        buttons_layout.addWidget(modify_btn)

        # Boutons rapports multilingues
        report_fr_btn = QPushButton("📊 Rapport FR")
        report_fr_btn.clicked.connect(lambda: self.generate_report("fr"))
        buttons_layout.addWidget(report_fr_btn)

        report_ar_btn = QPushButton("📊 تقرير AR")
        report_ar_btn.clicked.connect(lambda: self.generate_report("ar"))
        buttons_layout.addWidget(report_ar_btn)

        report_en_btn = QPushButton("📊 Report EN")
        report_en_btn.clicked.connect(lambda: self.generate_report("en"))
        buttons_layout.addWidget(report_en_btn)

        # Bouton email
        email_btn = QPushButton(self.ml.get_text("btn_email"))
        email_btn.clicked.connect(self.send_email_report)
        buttons_layout.addWidget(email_btn)

        # Boutons navigation
        cancel_btn = QPushButton(self.ml.get_text("btn_cancel"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        back_btn = QPushButton(self.ml.get_text("btn_back"))
        back_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(back_btn)

        layout.addWidget(buttons_frame)

    def validate_data(self):
        """Valide et sauvegarde les données"""
        print("✅ Validation Gestion des Biens")
        # Logique de sauvegarde

    def modify_data(self):
        """Modifie les données"""
        print("✏️ Modification Gestion des Biens")

    def generate_report(self, language):
        """Génère un rapport dans la langue spécifiée"""
        print(f"📊 Génération rapport Gestion des Biens en {language}")

        # Collecter les données du formulaire
        data = {
            "Code": self.code_bien.text(),
            "Désignation": self.designation.text(),
            "Catégorie": self.categorie.currentText(),
            "Valeur": f"{self.valeur_acquisition.value()} €",
            "État": self.etat_general.currentText(),
            "Responsable": self.responsable_principal.currentText(),
            "Localisation": f"{self.batiment.currentText()} - {self.etage.currentText()}",
            "Fournisseur": self.fournisseur.text()
        }

        # Générer le rapport avec le RapportManager
        # (à implémenter avec l'instance du RapportManager)

    def send_email_report(self):
        """Envoie le rapport par email"""
        print("📧 Envoi rapport par email")
        # Logique d'envoi email

class GestImmobMultilingueComplet(QMainWindow):
    """Interface principale multilingue complète avec TOUS les modules"""

    def __init__(self):
        super().__init__()

        # Gestionnaires
        self.ml_manager = MultilingueManager()
        self.color_manager = ColorManager()
        self.rapport_manager = RapportManager(self.ml_manager, self.color_manager)
        self.email_manager = EmailManager()

        # Configuration initiale
        self.current_user = None
        self.currency_symbol = "€"
        self.code_counter = 1

        # TOUS les modules avec classes complètes
        self.modules_complets = {
            'module_gestion_biens': {
                'couleur': '#1976D2', 'icone': '🏠',
                'classe': ModuleGestionBiens,
                'criteres': 25
            },
            'module_fournisseurs': {
                'couleur': '#388E3C', 'icone': '🏢',
                'classe': None,  # À implémenter
                'criteres': 20
            },
            'module_inventaire': {
                'couleur': '#F57C00', 'icone': '📦',
                'classe': None,  # À implémenter
                'criteres': 18
            },
            'module_parc_auto': {
                'couleur': '#D32F2F', 'icone': '🚗',
                'classe': None,  # Déjà implémenté dans le fichier précédent
                'criteres': 50
            },
            'module_animaux': {
                'couleur': '#7B1FA2', 'icone': '🐄',
                'classe': None,  # À implémenter
                'criteres': 15
            },
            'module_travaux': {
                'couleur': '#455A64', 'icone': '🔨',
                'classe': None,  # À implémenter
                'criteres': 22
            },
            'module_outils': {
                'couleur': '#5D4037', 'icone': '🔧',
                'classe': None,  # À implémenter
                'criteres': 16
            },
            'module_calculatrice': {
                'couleur': '#00796B', 'icone': '🧮',
                'classe': None,  # Déjà implémenté
                'criteres': 0
            },
            'module_documents': {
                'couleur': '#303F9F', 'icone': '📄',
                'classe': None,  # À implémenter
                'criteres': 12
            },
            'module_impression': {
                'couleur': '#689F38', 'icone': '🖨️',
                'classe': None,  # À implémenter
                'criteres': 8
            },
            'module_archive': {
                'couleur': '#512DA8', 'icone': '📚',
                'classe': None,  # À implémenter
                'criteres': 14
            },
            'module_outillage': {
                'couleur': '#E64A19', 'icone': '⚒️',
                'classe': None,  # À implémenter
                'criteres': 18
            },
            'module_moyens_generaux': {
                'couleur': '#1976D2', 'icone': '🏭',
                'classe': None,  # À implémenter
                'criteres': 20
            },
            'module_hotellerie': {
                'couleur': '#C2185B', 'icone': '🏨',
                'classe': None,  # À implémenter
                'criteres': 24
            },
            'module_agencement': {
                'couleur': '#00ACC1', 'icone': '🪑',
                'classe': None,  # À implémenter
                'criteres': 16
            },
            'module_batis': {
                'couleur': '#6A4C93', 'icone': '🏗️',
                'classe': None,  # À implémenter
                'criteres': 28
            }
        }

        self.setup_multilingue_interface()

    def setup_multilingue_interface(self):
        """Configure l'interface multilingue complète"""

        # Titre de la fenêtre
        self.setWindowTitle(self.ml_manager.get_text("app_title"))
        self.setGeometry(100, 100, 1600, 1000)

        # Style avec couleurs personnalisables
        self.apply_color_scheme()

        # Menu multilingue
        self.create_multilingue_menu()

        # Barre d'outils multilingue
        self.create_multilingue_toolbar()

        # Zone centrale avec modules
        self.create_multilingue_central_area()

        # Barre de statut multilingue
        self.create_multilingue_status_bar()

        # Afficher la fenêtre
        self.show()

    def apply_color_scheme(self):
        """Applique le schéma de couleurs actuel"""
        colors = self.color_manager.get_colors()

        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['primary']}, stop:1 {colors['secondary']});
                border: 3px solid {colors['accent']};
            }}
            QMenuBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['button']}, stop:1 {colors['secondary']});
                color: white;
                font-size: 12pt;
                font-weight: bold;
                border-bottom: 2px solid {colors['accent']};
            }}
            QMenuBar::item {{
                background: transparent;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 5px;
            }}
            QMenuBar::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['accent']}, stop:1 {colors['primary']});
            }}
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['secondary']}, stop:1 {colors['accent']});
                color: white;
                font-weight: bold;
                border-top: 2px solid {colors['accent']};
            }}
            QToolBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['button']}, stop:1 {colors['secondary']});
                border: 2px solid {colors['accent']};
                spacing: 5px;
                padding: 5px;
            }}
            QToolButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['background']}, stop:1 {colors['button']});
                color: {colors['text']};
                border: 2px solid {colors['accent']};
                border-radius: 8px;
                font-size: 11pt;
                font-weight: bold;
                padding: 8px;
                margin: 2px;
                min-width: 80px;
                min-height: 40px;
            }}
            QToolButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['accent']}, stop:1 {colors['primary']});
                color: white;
            }}
        """)

    def create_multilingue_menu(self):
        """Crée le menu multilingue"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu(self.ml_manager.get_text("menu_file"))

        new_action = file_menu.addAction("🆕 Nouveau")
        new_action.triggered.connect(self.nouveau_fichier)

        open_action = file_menu.addAction("📂 Ouvrir")
        open_action.triggered.connect(self.ouvrir_fichier)

        save_action = file_menu.addAction("💾 Sauvegarder")
        save_action.triggered.connect(self.sauvegarder_fichier)

        file_menu.addSeparator()

        exit_action = file_menu.addAction("🚪 Quitter")
        exit_action.triggered.connect(self.close)

        # Menu Modules
        modules_menu = menubar.addMenu(self.ml_manager.get_text("menu_modules"))

        for module_key, config in self.modules_complets.items():
            module_name = self.ml_manager.get_text(module_key)
            action = modules_menu.addAction(f"{config['icone']} {module_name}")
            action.triggered.connect(lambda checked, m=module_key: self.ouvrir_module(m))

        # Menu Outils
        tools_menu = menubar.addMenu(self.ml_manager.get_text("menu_tools"))

        calc_action = tools_menu.addAction(f"🧮 {self.ml_manager.get_text('module_calculatrice')}")
        calc_action.triggered.connect(self.ouvrir_calculatrice)

        param_action = tools_menu.addAction("⚙️ Paramètres")
        param_action.triggered.connect(self.ouvrir_parametres)

        # Menu Langues
        lang_menu = menubar.addMenu("🌍 Langues / Languages / اللغات")

        fr_action = lang_menu.addAction("🇫🇷 Français")
        fr_action.triggered.connect(lambda: self.changer_langue("fr"))

        ar_action = lang_menu.addAction("🇩🇿 العربية")
        ar_action.triggered.connect(lambda: self.changer_langue("ar"))

        en_action = lang_menu.addAction("🇺🇸 English")
        en_action.triggered.connect(lambda: self.changer_langue("en"))

        # Menu Couleurs
        colors_menu = menubar.addMenu("🎨 Couleurs")

        turquoise_action = colors_menu.addAction("🌊 Bleu Turquoise")
        turquoise_action.triggered.connect(lambda: self.changer_couleurs("bleu_turquoise"))

        sage_action = colors_menu.addAction("🌿 Sage Classique")
        sage_action.triggered.connect(lambda: self.changer_couleurs("sage_classique"))

        odoo_action = colors_menu.addAction("🟣 Odoo Style")
        odoo_action.triggered.connect(lambda: self.changer_couleurs("odoo_style"))

        pro_action = colors_menu.addAction("💼 Professionnel")
        pro_action.triggered.connect(lambda: self.changer_couleurs("professionnel"))

        custom_action = colors_menu.addAction("🎨 Personnaliser")
        custom_action.triggered.connect(self.personnaliser_couleurs)

        # Menu Aide
        help_menu = menubar.addMenu(self.ml_manager.get_text("menu_help"))

        about_action = help_menu.addAction("ℹ️ À propos")
        about_action.triggered.connect(self.afficher_apropos)
