<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:41:24)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l’erreur « text n’est pas un attribut connu de None », il faut vérifier que `self.table.horizontalHeaderItem(i)` n’est pas `None` avant d’appeler `.text()`. On peut remplacer la ligne problématique par une compréhension de liste qui utilise une valeur par défaut (par exemple chaîne vide) si l’en-tête est absent.

Voici la modification à apporter dans la méthode `exporter_csv` :

````python
# ...existing code...
            with open(file, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                # Correction : éviter l'appel à .text() sur None
                writer.writerow([
                    self.table.horizontalHeaderItem(i).text() if self.table.horizontalHeaderItem(i) is not None else ""
                    for i in range(self.table.columnCount())
                ])
                for row in range(self.table.rowCount()):
                    writer.writerow([
                        self.table.item(row, col).text() if self.table.item(row, col) else ""
                        for col in range(self.table.columnCount())
                    ])
# ...existing code...
````

---

