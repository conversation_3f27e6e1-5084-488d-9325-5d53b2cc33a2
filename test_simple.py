#!/usr/bin/env python3
"""
Test simple pour vérifier le fonctionnement de base de GestImmob
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """Test de la version Python"""
    print(f"Python version: {sys.version}")
    if sys.version_info >= (3, 8):
        print("[OK] Version Python compatible")
        return True
    else:
        print("[ERREUR] Version Python trop ancienne (minimum 3.8)")
        return False

def test_imports():
    """Test des imports essentiels"""
    print("\nTest des imports...")
    
    # Test PySide6
    try:
        from PySide6.QtWidgets import QApplication, QWidget, QLabel
        from PySide6.QtCore import Qt
        print("[OK] PySide6 importé avec succès")
        pyside_ok = True
    except ImportError as e:
        print(f"[ERREUR] Erreur PySide6: {e}")
        pyside_ok = False
    
    # Test SQLite
    try:
        import sqlite3
        print("[OK] SQLite3 disponible")
        sqlite_ok = True
    except ImportError as e:
        print(f"[ERREUR] Erreur SQLite: {e}")
        sqlite_ok = False
    
    # Test autres modules essentiels
    try:
        import json
        import csv
        import datetime
        import logging
        print("[OK] Modules standards disponibles")
        std_ok = True
    except ImportError as e:
        print(f"[ERREUR] Erreur modules standards: {e}")
        std_ok = False
    
    return pyside_ok and sqlite_ok and std_ok

def test_database():
    """Test de création de base de données"""
    print("\nTest de la base de données...")
    
    try:
        import sqlite3
        
        # Créer une base de test
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        
        # Créer une table de test
        cursor.execute('''
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                value REAL
            )
        ''')
        
        # Insérer des données de test
        cursor.execute('INSERT INTO test_table (name, value) VALUES (?, ?)', ('test', 123.45))
        
        # Lire les données
        cursor.execute('SELECT * FROM test_table')
        result = cursor.fetchone()
        
        conn.close()
        
        if result and result[1] == 'test' and result[2] == 123.45:
            print("[OK] Base de données SQLite fonctionne")
            return True
        else:
            print("[ERREUR] Problème avec les données SQLite")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Erreur base de données: {e}")
        return False

def test_gui_basic():
    """Test de base de l'interface graphique"""
    print("\nTest de l'interface graphique...")
    
    try:
        from PySide6.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PySide6.QtCore import Qt
        
        # Créer une application Qt (sans l'afficher)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Créer un widget simple
        widget = QWidget()
        layout = QVBoxLayout()
        label = QLabel("Test GestImmob")
        layout.addWidget(label)
        widget.setLayout(layout)
        
        # Vérifier que le widget est créé
        if widget and label.text() == "Test GestImmob":
            print("[OK] Interface graphique de base fonctionne")
            return True
        else:
            print("[ERREUR] Problème avec l'interface graphique")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Erreur interface graphique: {e}")
        return False

def test_file_structure():
    """Test de la structure des fichiers"""
    print("\nTest de la structure des fichiers...")
    
    required_files = [
        "src/main.py",
        "config_advanced.json",
        "requirements_minimal.txt",
        "modules/settings_manager.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"[ERREUR] Fichiers manquants: {', '.join(missing_files)}")
        return False
    else:
        print("[OK] Structure des fichiers correcte")
        return True

def test_configuration():
    """Test de chargement de la configuration"""
    print("\nTest de la configuration...")
    
    try:
        import json
        
        config_file = Path("config_advanced.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Vérifier les sections principales
            required_sections = ["application", "database", "ui", "performance"]
            missing_sections = [s for s in required_sections if s not in config]
            
            if missing_sections:
                print(f"[ERREUR] Sections manquantes dans la config: {', '.join(missing_sections)}")
                return False
            else:
                print("[OK] Configuration chargée avec succès")
                return True
        else:
            print("[ERREUR] Fichier de configuration manquant")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Erreur configuration: {e}")
        return False

def test_performance_detection():
    """Test de détection des performances"""
    print("\nTest de détection des performances...")
    
    try:
        import psutil
        
        # Obtenir les informations système
        memory = psutil.virtual_memory()
        cpu_count = psutil.cpu_count()
        
        ram_gb = round(memory.total / (1024**3), 1)
        
        print(f"  RAM détectée: {ram_gb} GB")
        print(f"  CPU cores: {cpu_count}")
        
        # Déterminer le profil recommandé
        if ram_gb >= 16:
            profile = "high_end"
        elif ram_gb >= 8:
            profile = "medium"
        else:
            profile = "low_end"
            
        print(f"  Profil recommandé: {profile}")
        print("[OK] Détection des performances réussie")
        return True
        
    except ImportError:
        print("⚠ Module psutil non disponible (optionnel)")
        return True
    except Exception as e:
        print(f"[ERREUR] Erreur détection performances: {e}")
        return False

def run_all_tests():
    """Exécute tous les tests"""
    print("=== Tests de Fonctionnement GestImmob ===")
    print()
    
    tests = [
        ("Version Python", test_python_version),
        ("Imports essentiels", test_imports),
        ("Base de données", test_database),
        ("Interface graphique", test_gui_basic),
        ("Structure fichiers", test_file_structure),
        ("Configuration", test_configuration),
        ("Détection performances", test_performance_detection)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"[ERREUR] Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "="*50)
    print("RÉSUMÉ DES TESTS")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "[OK] PASS" if result else "[ERREUR] FAIL"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Tests réussis: {passed}/{total}")
    
    if passed == total:
        print("[SUCCES] Tous les tests sont passés ! GestImmob est prêt.")
        return True
    elif passed >= total * 0.7:  # 70% de réussite
        print("⚠ La plupart des tests sont passés. GestImmob devrait fonctionner.")
        return True
    else:
        print("[ECHEC] Trop de tests ont échoué. Vérifiez l'installation.")
        return False

def main():
    """Fonction principale"""
    success = run_all_tests()
    
    if success:
        print("\n[LANCE] Vous pouvez maintenant lancer GestImmob avec:")
        print("   python src/main.py")
    else:
        print("\n[OUTIL] Veuillez corriger les erreurs avant de lancer GestImmob.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
