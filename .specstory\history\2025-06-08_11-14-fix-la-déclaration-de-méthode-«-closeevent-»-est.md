<!-- Generated by SpecStory -->

# /fix La déclaration de méthode « closeEvent » est  (2025-06-08 12:14:00)

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez la duplication de la méthode `closeEvent` pour ne garder qu'une seule définition.

````python
# ...existing code...

    # --- Gardez UNE SEULE définition de closeEvent ---
    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()
    # --- Fin closeEvent ---

# ...existing code...

    # Supprimez la seconde définition de closeEvent ici (ligne vers la fin du fichier)
    # def closeEvent(self, event: QCloseEvent):
    #     if self.changements_detectes:
    #         self.sauvegarder_inventaire()
    #     event.accept()

# ...existing code...
````

---

