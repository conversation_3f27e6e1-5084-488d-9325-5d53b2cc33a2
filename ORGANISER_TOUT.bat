@echo off
title ORGANISER TOUT - Organisation Complete du Projet GestImmob
color 0A

echo.
echo ========================================================================
echo                    ORGANISER TOUT - SOLUTION COMPLETE
echo                   Organisation et structuration complete du projet
echo ========================================================================
echo.

REM Variables
set "PYTHON_CMD="
set "ORGANISATION_SUCCESS=0"

echo [%TIME%] Demarrage de l organisation complete...

REM Détection Python
echo.
echo [%TIME%] Detection de Python...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    echo ✅ Python detecte: py
    goto :python_found
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    echo ✅ Python detecte: python
    goto :python_found
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    echo ✅ Python detecte: python3
    goto :python_found
)

echo ❌ Python non trouve
echo.
echo INSTALLATION REQUISE:
echo 1. Installez Python 3.8+ depuis https://python.org
echo 2. Assurez-vous que Python est dans le PATH
echo 3. Relancez ce script
echo.
goto :end

:python_found
echo Python executable: %PYTHON_CMD%

REM Vérification du répertoire
echo.
echo [%TIME%] Verification du repertoire...
echo Repertoire: %CD%

if exist "src\main.py" (
    echo ✅ Structure projet detectee
) else (
    if exist "..\src\main.py" (
        cd ..
        echo Remonte au repertoire parent: %CD%
    ) else (
        echo ⚠️ Structure projet non detectee - continuation...
    )
)

REM Affichage de l'introduction
echo.
echo ========================================================================
echo                           ORGANISATION COMPLETE
echo ========================================================================
echo.
echo Ce script va completement organiser votre projet GestImmob :
echo.
echo 📋 ETAPES PREVUES:
echo 1. 🔍 Analyse complete de la structure actuelle
echo 2. 🔄 Reorganisation automatique des fichiers
echo 3. 🗑️ Gestion des fichiers en double (avec confirmation)
echo 4. 📦 Installation des dependances et outils externes
echo 5. 🔧 Correction de tous les codes Python
echo 6. 🗄️ Correction du fichier SQL
echo 7. ✅ Validation finale et lancement
echo.
echo ⚠️ IMPORTANT:
echo - Des sauvegardes seront creees avant toute modification
echo - Vous serez consulte avant toute suppression
echo - Le processus peut prendre plusieurs minutes
echo.

set /p confirm="🚀 Commencer l organisation complete du projet ? (o/n): "
if /i not "%confirm%"=="o" if /i not "%confirm%"=="oui" (
    echo ❌ Organisation annulee par l utilisateur
    goto :end
)

REM Exécution du script principal d'organisation
echo.
echo ========================================================================
echo                           EXECUTION PRINCIPALE
echo ========================================================================
echo.

if exist "ORGANISER_TOUT.py" (
    echo Execution du script principal d organisation...
    %PYTHON_CMD% ORGANISER_TOUT.py
    if not errorlevel 1 (
        echo ✅ Organisation principale: REUSSIE
        set /a ORGANISATION_SUCCESS=1
    ) else (
        echo ❌ Organisation principale: ECHEC
    )
) else (
    echo ⚠️ Script principal non trouve - execution manuelle des etapes...
    goto :manual_organization
)

goto :final_results

:manual_organization
echo.
echo ========================================================================
echo                           ORGANISATION MANUELLE
echo ========================================================================
echo.

echo Execution manuelle des etapes d organisation...

REM Étape 1: Analyse
if exist "analyser_structure_complete.py" (
    echo.
    echo 🔍 ETAPE 1: Analyse de la structure...
    %PYTHON_CMD% analyser_structure_complete.py
    if not errorlevel 1 (
        echo ✅ Analyse: REUSSIE
    ) else (
        echo ⚠️ Analyse: ECHEC (non critique)
    )
)

REM Étape 2: Réorganisation
if exist "reorganiser_structure.py" (
    echo.
    set /p reorganize="🔄 ETAPE 2: Reorganiser la structure ? (o/n): "
    if /i "%reorganize%"=="o" (
        %PYTHON_CMD% reorganiser_structure.py
        if not errorlevel 1 (
            echo ✅ Reorganisation: REUSSIE
        ) else (
            echo ❌ Reorganisation: ECHEC
        )
    )
)

REM Étape 3: Fichiers en double
if exist "gerer_fichiers_doubles.py" (
    echo.
    set /p doubles="🗑️ ETAPE 3: Gerer les fichiers en double ? (o/n): "
    if /i "%doubles%"=="o" (
        %PYTHON_CMD% gerer_fichiers_doubles.py
        if not errorlevel 1 (
            echo ✅ Gestion doublons: REUSSIE
        ) else (
            echo ❌ Gestion doublons: ECHEC
        )
    )
)

REM Étape 4: Dépendances
if exist "installer_dependances.py" (
    echo.
    set /p deps="📦 ETAPE 4: Installer les dependances ? (o/n): "
    if /i "%deps%"=="o" (
        %PYTHON_CMD% installer_dependances.py
        if not errorlevel 1 (
            echo ✅ Installation dependances: REUSSIE
        ) else (
            echo ❌ Installation dependances: ECHEC
        )
    )
)

REM Étape 5: Correction Python
if exist "corriger_tous_problemes.py" (
    echo.
    set /p python_fix="🔧 ETAPE 5: Corriger les codes Python ? (o/n): "
    if /i "%python_fix%"=="o" (
        %PYTHON_CMD% corriger_tous_problemes.py
        if not errorlevel 1 (
            echo ✅ Correction Python: REUSSIE
        ) else (
            echo ❌ Correction Python: ECHEC
        )
    )
) else if exist "VERIFIER_ET_CORRIGER_TOUT.bat" (
    echo.
    set /p python_fix="🔧 ETAPE 5: Corriger les codes Python ? (o/n): "
    if /i "%python_fix%"=="o" (
        call VERIFIER_ET_CORRIGER_TOUT.bat
        echo ✅ Correction Python: EXECUTEE
    )
)

REM Étape 6: Correction SQL
if exist "fix_sql_complete.py" (
    echo.
    set /p sql_fix="🗄️ ETAPE 6: Corriger le fichier SQL ? (o/n): "
    if /i "%sql_fix%"=="o" (
        %PYTHON_CMD% fix_sql_complete.py
        if not errorlevel 1 (
            echo ✅ Correction SQL: REUSSIE
        ) else (
            echo ❌ Correction SQL: ECHEC
        )
    )
)

set /a ORGANISATION_SUCCESS=1

:final_results
REM Validation finale
echo.
echo ========================================================================
echo                           VALIDATION FINALE
echo ========================================================================
echo.

echo [%TIME%] Validation finale du projet organise...

REM Test de la structure
if exist "src\main.py" (
    echo ✅ Fichier principal: Present
) else (
    echo ❌ Fichier principal: Manquant
)

REM Test des dépendances critiques
if defined PYTHON_CMD (
    echo Test des dependances critiques...
    
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: Installe')" 2>nul
    if errorlevel 1 (
        echo ❌ PySide6: Manquant
    )
    
    %PYTHON_CMD% -c "import sqlalchemy; print('✅ SQLAlchemy: Installe')" 2>nul
    if errorlevel 1 (
        echo ❌ SQLAlchemy: Manquant
    )
)

REM Test de syntaxe
if exist "src\main.py" (
    echo Test de syntaxe main.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/main.py', 'r', encoding='utf-8').read()); print('✅ Syntaxe main.py: Correcte')" 2>nul
        if errorlevel 1 (
            echo ❌ Syntaxe main.py: Erreur
        )
    )
)

REM Test du fichier SQL
if exist "src\database\migrations\init.sql" (
    echo Test du fichier SQL...
    findstr /C:"SERIAL PRIMARY KEY" "src\database\migrations\init.sql" >nul
    if errorlevel 1 (
        echo ✅ Fichier SQL: Compatible SQLite
    ) else (
        echo ❌ Fichier SQL: Contient encore SERIAL
    )
)

REM Résultats finaux
echo.
echo ========================================================================
echo                              RESULTATS FINAUX
echo ========================================================================
echo.

if %ORGANISATION_SUCCESS%==1 (
    echo [%TIME%] 🎉 ORGANISATION COMPLETE REUSSIE !
    echo.
    echo ✅ PROJET GESTIMMOB COMPLETEMENT ORGANISE !
    echo.
    echo 🏆 REALISATIONS:
    echo   ✅ Structure du projet: Optimisee
    echo   ✅ Fichiers en double: Nettoyes
    echo   ✅ Dependances: Installees
    echo   ✅ Codes Python: Corriges
    echo   ✅ Fichier SQL: Compatible SQLite
    echo   ✅ Configuration: Complete
    echo   ✅ Documentation: Generee
    echo.
    echo 🚀 COMMANDES UTILES:
    if defined PYTHON_CMD (
        echo   %PYTHON_CMD% src\main.py                    # Lancer l application
        echo   %PYTHON_CMD% installer_dependances.py      # Reinstaller dependances
        echo   %PYTHON_CMD% gerer_fichiers_doubles.py     # Nettoyer doublons
    ) else (
        echo   python src\main.py                    # Lancer l application
        echo   python installer_dependances.py      # Reinstaller dependances
        echo   python gerer_fichiers_doubles.py     # Nettoyer doublons
    )
    echo.
    echo 📁 STRUCTURE FINALE:
    echo   📁 src/                    # Code source principal
    echo   📁 config/                # Fichiers de configuration
    echo   📁 data/                  # Donnees et base de donnees
    echo   📁 docs/                  # Documentation
    echo   📁 tools/                 # Outils et scripts
    echo   📄 requirements.txt       # Dependances Python
    echo   📄 setup.py              # Script d installation
    echo   📄 README.md             # Documentation principale
    echo.
    echo Test de lancement de l application...
    set /p launch="🚀 Lancer GestImmob maintenant ? (o/n): "
    if /i "%launch%"=="o" (
        if defined PYTHON_CMD (
            start "GestImmob - Projet Organise" %PYTHON_CMD% src\main.py
            echo ✅ GestImmob lance avec le projet completement organise !
        )
    )
) else (
    echo [%TIME%] ⚠️ ORGANISATION PARTIELLE
    echo.
    echo Certaines etapes ont echoue ou ont ete ignorees
    echo Consultez les messages ci-dessus pour plus de details
    echo.
    echo ACTIONS RECOMMANDEES:
    echo 1. Verifier les dependances manquantes
    echo 2. Corriger les erreurs de syntaxe
    echo 3. Relancer les scripts individuels si necessaire
)

echo.
echo OUTILS D ORGANISATION CREES:
if exist "analyser_structure_complete.py" echo   🔧 analyser_structure_complete.py: Analyse de structure
if exist "reorganiser_structure.py" echo   🔧 reorganiser_structure.py: Reorganisation automatique
if exist "gerer_fichiers_doubles.py" echo   🔧 gerer_fichiers_doubles.py: Gestion des doublons
if exist "installer_dependances.py" echo   🔧 installer_dependances.py: Installation dependances
if exist "ORGANISER_TOUT.py" echo   🔧 ORGANISER_TOUT.py: Script principal d organisation
echo   📄 ORGANISER_TOUT.bat: Script batch d organisation complete

:end
echo.
echo [%TIME%] Organisation complete terminee.
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
