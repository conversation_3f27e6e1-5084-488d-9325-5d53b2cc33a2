#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🐧 ROBOT IA - INTÉGRATION LINUX & MACHINES VIRTUELLES
Support Kali Linux, Ubuntu, VMware, VirtualBox
Créé par Augment Agent
"""

import os
import sys
import subprocess
import json
import time
import socket
import paramiko
from datetime import datetime

class LinuxVMIntegration:
    """🐧 Intégration complète Linux et machines virtuelles"""
    
    def __init__(self):
        self.supported_distros = {
            "kali": {
                "name": "Kali Linux",
                "default_user": "kali",
                "default_tools": ["nmap", "metasploit", "burpsuite", "wireshark", "aircrack-ng"],
                "package_manager": "apt"
            },
            "ubuntu": {
                "name": "Ubuntu",
                "default_user": "ubuntu",
                "default_tools": ["curl", "wget", "git", "vim", "htop"],
                "package_manager": "apt"
            },
            "debian": {
                "name": "Debian",
                "default_user": "debian",
                "default_tools": ["curl", "wget", "git", "vim"],
                "package_manager": "apt"
            },
            "centos": {
                "name": "CentOS",
                "default_user": "centos",
                "default_tools": ["curl", "wget", "git", "vim"],
                "package_manager": "yum"
            }
        }
        
        self.vm_platforms = {
            "vmware": {
                "name": "VMware Workstation/Player",
                "executable": "vmrun.exe",
                "config_ext": ".vmx"
            },
            "virtualbox": {
                "name": "Oracle VirtualBox",
                "executable": "VBoxManage.exe",
                "config_ext": ".vbox"
            },
            "hyper-v": {
                "name": "Microsoft Hyper-V",
                "executable": "powershell.exe",
                "config_ext": ".xml"
            }
        }
        
        self.active_connections = {}
    
    def detect_vm_platforms(self):
        """Détecte les plateformes de virtualisation installées"""
        detected = {}
        
        print("🔍 Détection plateformes virtualisation...")
        
        # VMware
        vmware_paths = [
            "C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe",
            "C:\\Program Files\\VMware\\VMware Workstation\\vmrun.exe",
            "C:\\Program Files (x86)\\VMware\\VMware Player\\vmrun.exe"
        ]
        
        for path in vmware_paths:
            if os.path.exists(path):
                detected["vmware"] = {
                    "path": path,
                    "version": self._get_vmware_version(path),
                    "status": "Détecté"
                }
                break
        
        # VirtualBox
        vbox_paths = [
            "C:\\Program Files\\Oracle\\VirtualBox\\VBoxManage.exe",
            "C:\\Program Files (x86)\\Oracle\\VirtualBox\\VBoxManage.exe"
        ]
        
        for path in vbox_paths:
            if os.path.exists(path):
                detected["virtualbox"] = {
                    "path": path,
                    "version": self._get_virtualbox_version(path),
                    "status": "Détecté"
                }
                break
        
        # Hyper-V
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V"],
                capture_output=True, text=True, timeout=10
            )
            if "Enabled" in result.stdout:
                detected["hyper-v"] = {
                    "path": "powershell.exe",
                    "version": "Windows Built-in",
                    "status": "Activé"
                }
        except:
            pass
        
        return detected
    
    def _get_vmware_version(self, vmrun_path):
        """Récupère la version VMware"""
        try:
            result = subprocess.run([vmrun_path], capture_output=True, text=True, timeout=5)
            if "vmrun version" in result.stderr:
                return result.stderr.split("vmrun version")[1].split()[0]
        except:
            pass
        return "Inconnue"
    
    def _get_virtualbox_version(self, vbox_path):
        """Récupère la version VirtualBox"""
        try:
            result = subprocess.run([vbox_path, "--version"], capture_output=True, text=True, timeout=5)
            return result.stdout.strip()
        except:
            pass
        return "Inconnue"
    
    def list_virtual_machines(self, platform="auto"):
        """Liste toutes les machines virtuelles"""
        vms = {}
        
        if platform == "auto":
            detected = self.detect_vm_platforms()
            platforms = list(detected.keys())
        else:
            platforms = [platform]
        
        for platform in platforms:
            if platform == "vmware":
                vms[platform] = self._list_vmware_vms()
            elif platform == "virtualbox":
                vms[platform] = self._list_virtualbox_vms()
            elif platform == "hyper-v":
                vms[platform] = self._list_hyperv_vms()
        
        return vms
    
    def _list_vmware_vms(self):
        """Liste les VMs VMware"""
        vms = []
        try:
            vmrun_path = self._find_vmware_path()
            if vmrun_path:
                result = subprocess.run([vmrun_path, "list"], capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                
                for line in lines:
                    if line.strip():
                        vm_path = line.strip()
                        vm_name = os.path.basename(vm_path).replace('.vmx', '')
                        vms.append({
                            "name": vm_name,
                            "path": vm_path,
                            "status": self._get_vmware_vm_status(vmrun_path, vm_path),
                            "platform": "VMware"
                        })
        except Exception as e:
            print(f"❌ Erreur liste VMware: {e}")
        
        return vms
    
    def _list_virtualbox_vms(self):
        """Liste les VMs VirtualBox"""
        vms = []
        try:
            vbox_path = self._find_virtualbox_path()
            if vbox_path:
                result = subprocess.run([vbox_path, "list", "vms"], capture_output=True, text=True, timeout=10)
                
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        # Format: "VM Name" {UUID}
                        parts = line.split('" {')
                        if len(parts) == 2:
                            vm_name = parts[0].strip('"')
                            vm_uuid = parts[1].strip('}')
                            
                            # Récupérer le statut
                            status_result = subprocess.run(
                                [vbox_path, "showvminfo", vm_uuid, "--machinereadable"],
                                capture_output=True, text=True, timeout=5
                            )
                            
                            status = "Arrêtée"
                            for status_line in status_result.stdout.split('\n'):
                                if status_line.startswith('VMState='):
                                    vm_state = status_line.split('=')[1].strip('"')
                                    status = "En cours" if vm_state == "running" else "Arrêtée"
                                    break
                            
                            vms.append({
                                "name": vm_name,
                                "uuid": vm_uuid,
                                "status": status,
                                "platform": "VirtualBox"
                            })
        except Exception as e:
            print(f"❌ Erreur liste VirtualBox: {e}")
        
        return vms
    
    def _list_hyperv_vms(self):
        """Liste les VMs Hyper-V"""
        vms = []
        try:
            result = subprocess.run([
                "powershell", "-Command", 
                "Get-VM | Select-Object Name, State | ConvertTo-Json"
            ], capture_output=True, text=True, timeout=15)
            
            if result.stdout.strip():
                vm_data = json.loads(result.stdout)
                if isinstance(vm_data, list):
                    for vm in vm_data:
                        vms.append({
                            "name": vm["Name"],
                            "status": vm["State"],
                            "platform": "Hyper-V"
                        })
                elif isinstance(vm_data, dict):
                    vms.append({
                        "name": vm_data["Name"],
                        "status": vm_data["State"],
                        "platform": "Hyper-V"
                    })
        except Exception as e:
            print(f"❌ Erreur liste Hyper-V: {e}")
        
        return vms
    
    def start_vm(self, vm_name, platform):
        """Démarre une machine virtuelle"""
        try:
            print(f"🚀 Démarrage VM {vm_name} ({platform})...")
            
            if platform.lower() == "vmware":
                return self._start_vmware_vm(vm_name)
            elif platform.lower() == "virtualbox":
                return self._start_virtualbox_vm(vm_name)
            elif platform.lower() == "hyper-v":
                return self._start_hyperv_vm(vm_name)
            else:
                print(f"❌ Plateforme {platform} non supportée")
                return False
                
        except Exception as e:
            print(f"❌ Erreur démarrage VM: {e}")
            return False
    
    def _start_vmware_vm(self, vm_name):
        """Démarre une VM VMware"""
        try:
            vmrun_path = self._find_vmware_path()
            vm_path = self._find_vmware_vm_path(vm_name)
            
            if vmrun_path and vm_path:
                result = subprocess.run([vmrun_path, "start", vm_path], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    print(f"✅ VM {vm_name} démarrée")
                    return True
                else:
                    print(f"❌ Erreur démarrage: {result.stderr}")
            return False
        except Exception as e:
            print(f"❌ Erreur VMware: {e}")
            return False
    
    def _start_virtualbox_vm(self, vm_name):
        """Démarre une VM VirtualBox"""
        try:
            vbox_path = self._find_virtualbox_path()
            if vbox_path:
                result = subprocess.run([vbox_path, "startvm", vm_name, "--type", "headless"], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    print(f"✅ VM {vm_name} démarrée")
                    return True
                else:
                    print(f"❌ Erreur démarrage: {result.stderr}")
            return False
        except Exception as e:
            print(f"❌ Erreur VirtualBox: {e}")
            return False
    
    def connect_to_linux_vm(self, vm_ip, username, password=None, key_path=None, distro="ubuntu"):
        """Connexion SSH à une VM Linux"""
        try:
            print(f"🐧 Connexion à {distro} VM ({vm_ip})...")
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connexion avec clé ou mot de passe
            if key_path and os.path.exists(key_path):
                ssh.connect(vm_ip, username=username, key_filename=key_path, timeout=10)
            elif password:
                ssh.connect(vm_ip, username=username, password=password, timeout=10)
            else:
                print("❌ Aucune méthode d'authentification fournie")
                return None
            
            # Test de connexion
            stdin, stdout, stderr = ssh.exec_command("whoami")
            result = stdout.read().decode().strip()
            
            if result == username:
                print(f"✅ Connexion SSH réussie à {vm_ip}")
                self.active_connections[vm_ip] = {
                    "ssh": ssh,
                    "distro": distro,
                    "username": username,
                    "connected_at": datetime.now()
                }
                return ssh
            else:
                ssh.close()
                return None
                
        except Exception as e:
            print(f"❌ Erreur connexion SSH: {e}")
            return None
    
    def execute_linux_command(self, vm_ip, command):
        """Exécute une commande sur une VM Linux"""
        try:
            if vm_ip not in self.active_connections:
                print(f"❌ Aucune connexion active à {vm_ip}")
                return None
            
            ssh = self.active_connections[vm_ip]["ssh"]
            stdin, stdout, stderr = ssh.exec_command(command, timeout=30)
            
            output = stdout.read().decode()
            error = stderr.read().decode()
            
            return {
                "command": command,
                "output": output,
                "error": error,
                "exit_code": stdout.channel.recv_exit_status()
            }
            
        except Exception as e:
            print(f"❌ Erreur exécution commande: {e}")
            return None
    
    def install_kali_tools(self, vm_ip):
        """Installe les outils Kali Linux essentiels"""
        try:
            print(f"🛠️ Installation outils Kali sur {vm_ip}...")
            
            tools = [
                "nmap",
                "metasploit-framework", 
                "burpsuite",
                "wireshark",
                "aircrack-ng",
                "sqlmap",
                "nikto",
                "dirb",
                "gobuster",
                "hydra",
                "john",
                "hashcat",
                "maltego",
                "zaproxy"
            ]
            
            # Mise à jour des paquets
            self.execute_linux_command(vm_ip, "sudo apt update")
            
            # Installation des outils
            for tool in tools:
                print(f"📦 Installation {tool}...")
                result = self.execute_linux_command(vm_ip, f"sudo apt install -y {tool}")
                if result and result["exit_code"] == 0:
                    print(f"✅ {tool} installé")
                else:
                    print(f"⚠️ Erreur installation {tool}")
            
            print("✅ Installation outils Kali terminée")
            return True
            
        except Exception as e:
            print(f"❌ Erreur installation outils: {e}")
            return False
    
    def setup_ubuntu_dev_environment(self, vm_ip):
        """Configure un environnement de développement Ubuntu"""
        try:
            print(f"💻 Configuration environnement dev Ubuntu sur {vm_ip}...")
            
            commands = [
                "sudo apt update",
                "sudo apt upgrade -y",
                "sudo apt install -y curl wget git vim htop tree",
                "sudo apt install -y python3 python3-pip nodejs npm",
                "sudo apt install -y docker.io docker-compose",
                "sudo apt install -y code",  # VS Code
                "curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh",
                "sudo usermod -aG docker $USER"
            ]
            
            for cmd in commands:
                print(f"⚡ Exécution: {cmd}")
                result = self.execute_linux_command(vm_ip, cmd)
                if result and result["exit_code"] == 0:
                    print(f"✅ Commande réussie")
                else:
                    print(f"⚠️ Erreur commande: {cmd}")
            
            print("✅ Environnement dev Ubuntu configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur configuration Ubuntu: {e}")
            return False
    
    def create_vm_snapshot(self, vm_name, platform, snapshot_name=None):
        """Crée un snapshot d'une VM"""
        try:
            if not snapshot_name:
                snapshot_name = f"snapshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            print(f"📸 Création snapshot {snapshot_name} pour {vm_name}...")
            
            if platform.lower() == "vmware":
                vmrun_path = self._find_vmware_path()
                vm_path = self._find_vmware_vm_path(vm_name)
                if vmrun_path and vm_path:
                    result = subprocess.run([
                        vmrun_path, "snapshot", vm_path, snapshot_name
                    ], capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        print(f"✅ Snapshot {snapshot_name} créé")
                        return True
            
            elif platform.lower() == "virtualbox":
                vbox_path = self._find_virtualbox_path()
                if vbox_path:
                    result = subprocess.run([
                        vbox_path, "snapshot", vm_name, "take", snapshot_name
                    ], capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        print(f"✅ Snapshot {snapshot_name} créé")
                        return True
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur création snapshot: {e}")
            return False
    
    def _find_vmware_path(self):
        """Trouve le chemin VMware"""
        paths = [
            "C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe",
            "C:\\Program Files\\VMware\\VMware Workstation\\vmrun.exe",
            "C:\\Program Files (x86)\\VMware\\VMware Player\\vmrun.exe"
        ]
        for path in paths:
            if os.path.exists(path):
                return path
        return None
    
    def _find_virtualbox_path(self):
        """Trouve le chemin VirtualBox"""
        paths = [
            "C:\\Program Files\\Oracle\\VirtualBox\\VBoxManage.exe",
            "C:\\Program Files (x86)\\Oracle\\VirtualBox\\VBoxManage.exe"
        ]
        for path in paths:
            if os.path.exists(path):
                return path
        return None
    
    def close_all_connections(self):
        """Ferme toutes les connexions SSH"""
        for vm_ip, connection in self.active_connections.items():
            try:
                connection["ssh"].close()
                print(f"✅ Connexion {vm_ip} fermée")
            except:
                pass
        self.active_connections.clear()

def main():
    """Fonction principale de test"""
    print("🐧 INTÉGRATION LINUX & MACHINES VIRTUELLES")
    print("=" * 50)
    
    integration = LinuxVMIntegration()
    
    # Détection plateformes
    platforms = integration.detect_vm_platforms()
    print("🔍 Plateformes détectées:")
    for platform, info in platforms.items():
        print(f"  ✅ {platform}: {info['status']} - {info['version']}")
    
    # Liste des VMs
    vms = integration.list_virtual_machines()
    print("\n💻 Machines virtuelles:")
    for platform, vm_list in vms.items():
        print(f"\n📁 {platform.upper()}:")
        for vm in vm_list:
            print(f"  • {vm['name']} - {vm['status']}")
    
    print("\n🚀 Intégration Linux & VM prête !")

if __name__ == "__main__":
    main()
