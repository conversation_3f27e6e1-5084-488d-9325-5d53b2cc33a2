#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Professionnelle 3D
Mêmes tailles • Plus de contenu • Normes pro • 3D sans déformation
Formulaires complets • Date française • Bas bleu turquoise
"""

import sys
import locale
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QTableWidget, QTableWidgetItem, 
    QComboBox, QGroupBox, QGridLayout, QLineEdit, QSpinBox, 
    QDateEdit, QTextEdit, QScrollArea, QFormLayout
)
from PySide6.QtCore import Qt, QTimer, QDate
from PySide6.QtGui import QColor

# Configuration locale française
try:
    locale.setlocale(locale.LC_TIME, 'fr_FR.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_TIME, 'French_France.1252')
    except:
        pass

class FormulaireVehiculeProComplet(QWidget):
    """Formulaire véhicule professionnel complet avec fond noir"""
    
    def __init__(self):
        super().__init__()
        self.setup_formulaire_pro()
    
    def setup_formulaire_pro(self):
        self.setWindowTitle("🚗 MASTER COMPTA - FORMULAIRE VÉHICULE PROFESSIONNEL")
        self.setGeometry(100, 100, 1400, 900)
        
        # Style professionnel fond noir avec effets 3D subtils
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.2 #1a1a1a, stop:0.8 #1a1a1a, stop:1 #000000);
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11pt;
            }
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #00bcd4;
                border-radius: 10px;
                margin: 8px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0,188,212,0.15), stop:1 rgba(0,188,212,0.05));
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 3px 10px;
                background: #00bcd4;
                border-radius: 5px;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 11pt;
                font-weight: bold;
                padding: 3px;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a2a2a, stop:1 #1a1a1a);
                border: 2px solid #00bcd4;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
                min-height: 16px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
                border: 2px solid #4dd0e1;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3a3a3a, stop:1 #2a2a2a);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid white;
            }
            QComboBox QAbstractItemView {
                background: #2a2a2a;
                color: white;
                border: 2px solid #00bcd4;
                selection-background-color: #00bcd4;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00bcd4, stop:0.5 #0097a7, stop:1 #00838f);
                color: white;
                border: 2px solid #00bcd4;
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px 15px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4dd0e1, stop:0.5 #26c6da, stop:1 #00acc1);
                border: 2px solid #4dd0e1;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #006064, stop:0.5 #00838f, stop:1 #0097a7);
            }
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a2a2a, stop:1 #1a1a1a);
                border: 2px solid #00bcd4;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Titre avec logo (taille normale)
        title_layout = QHBoxLayout()
        logo_label = QLabel("🚗")
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 32pt;
                color: #00bcd4;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0,188,212,0.2), stop:1 rgba(0,188,212,0.1));
                border: 2px solid #00bcd4;
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
                min-width: 80px;
                max-width: 80px;
            }
        """)
        
        title_label = QLabel("FORMULAIRE VÉHICULE PROFESSIONNEL COMPLET")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00bcd4, stop:0.5 #4dd0e1, stop:1 #00bcd4);
                border: 2px solid #00bcd4;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        title_layout.addWidget(logo_label)
        title_layout.addWidget(title_label, 1)
        layout.addLayout(title_layout)
        
        # Zone de défilement pour le formulaire
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid #00bcd4;
                border-radius: 8px;
                background: transparent;
            }
        """)
        
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        
        # Groupes de critères professionnels
        self.create_info_generales_group(form_layout)
        self.create_caracteristiques_group(form_layout)
        self.create_financier_group(form_layout)
        self.create_maintenance_group(form_layout)
        self.create_assurance_group(form_layout)
        self.create_observations_group(form_layout)
        
        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)
        
        # Boutons d'action (taille normale)
        self.create_action_buttons_pro(layout)
    
    def create_info_generales_group(self, layout):
        """Groupe informations générales avec logos"""
        group = QGroupBox("📋 INFORMATIONS GÉNÉRALES")
        group_layout = QGridLayout(group)
        
        # Code véhicule
        group_layout.addWidget(QLabel("🏷️ Code Véhicule:"), 0, 0)
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("Ex: V001, V002...")
        group_layout.addWidget(self.code_edit, 0, 1)
        
        # Marque
        group_layout.addWidget(QLabel("🚗 Marque:"), 0, 2)
        self.marque_combo = QComboBox()
        self.marque_combo.addItems([
            "Sélectionner...", "Peugeot", "Renault", "Citroën", "Ford", 
            "Volkswagen", "BMW", "Mercedes", "Audi", "Toyota", "Nissan", 
            "Opel", "Fiat", "Hyundai", "Kia", "Mazda", "Honda", "Volvo",
            "Skoda", "Seat", "Dacia", "Suzuki", "Mitsubishi", "Subaru"
        ])
        group_layout.addWidget(self.marque_combo, 0, 3)
        
        # Modèle
        group_layout.addWidget(QLabel("🔧 Modèle:"), 1, 0)
        self.modele_edit = QLineEdit()
        self.modele_edit.setPlaceholderText("Ex: 308, Clio, Focus...")
        group_layout.addWidget(self.modele_edit, 1, 1)
        
        # Immatriculation
        group_layout.addWidget(QLabel("🔢 Immatriculation:"), 1, 2)
        self.immat_edit = QLineEdit()
        self.immat_edit.setPlaceholderText("Ex: AB-123-CD")
        group_layout.addWidget(self.immat_edit, 1, 3)
        
        # Année
        group_layout.addWidget(QLabel("📅 Année:"), 2, 0)
        self.annee_spin = QSpinBox()
        self.annee_spin.setRange(1990, 2025)
        self.annee_spin.setValue(2020)
        group_layout.addWidget(self.annee_spin, 2, 1)
        
        # Couleur
        group_layout.addWidget(QLabel("🎨 Couleur:"), 2, 2)
        self.couleur_combo = QComboBox()
        self.couleur_combo.addItems([
            "Sélectionner...", "Blanc", "Noir", "Gris", "Rouge", "Bleu", 
            "Vert", "Jaune", "Orange", "Violet", "Marron", "Beige", "Argent"
        ])
        group_layout.addWidget(self.couleur_combo, 2, 3)
        
        layout.addWidget(group)
    
    def create_caracteristiques_group(self, layout):
        """Groupe caractéristiques techniques"""
        group = QGroupBox("⚙️ CARACTÉRISTIQUES TECHNIQUES")
        group_layout = QGridLayout(group)
        
        # Carburant
        group_layout.addWidget(QLabel("⛽ Carburant:"), 0, 0)
        self.carburant_combo = QComboBox()
        self.carburant_combo.addItems([
            "Sélectionner...", "Essence", "Diesel", "Hybride", 
            "Électrique", "GPL", "Éthanol E85", "Hydrogène"
        ])
        group_layout.addWidget(self.carburant_combo, 0, 1)
        
        # Puissance
        group_layout.addWidget(QLabel("⚡ Puissance:"), 0, 2)
        self.puissance_spin = QSpinBox()
        self.puissance_spin.setRange(50, 1000)
        self.puissance_spin.setSuffix(" CV")
        group_layout.addWidget(self.puissance_spin, 0, 3)
        
        # Kilométrage
        group_layout.addWidget(QLabel("📏 Kilométrage:"), 1, 0)
        self.km_spin = QSpinBox()
        self.km_spin.setRange(0, 999999)
        self.km_spin.setSuffix(" km")
        group_layout.addWidget(self.km_spin, 1, 1)
        
        # Transmission
        group_layout.addWidget(QLabel("🔄 Transmission:"), 1, 2)
        self.transmission_combo = QComboBox()
        self.transmission_combo.addItems([
            "Sélectionner...", "Manuelle", "Automatique", 
            "Semi-automatique", "CVT", "Robotisée"
        ])
        group_layout.addWidget(self.transmission_combo, 1, 3)
        
        layout.addWidget(group)
    
    def create_financier_group(self, layout):
        """Groupe informations financières"""
        group = QGroupBox("💰 INFORMATIONS FINANCIÈRES")
        group_layout = QGridLayout(group)
        
        # Prix d'achat
        group_layout.addWidget(QLabel("💵 Prix d'achat:"), 0, 0)
        self.prix_achat_spin = QSpinBox()
        self.prix_achat_spin.setRange(0, 999999)
        self.prix_achat_spin.setSuffix(" €")
        group_layout.addWidget(self.prix_achat_spin, 0, 1)
        
        # Coût mensuel
        group_layout.addWidget(QLabel("📊 Coût mensuel:"), 0, 2)
        self.cout_mensuel_spin = QSpinBox()
        self.cout_mensuel_spin.setRange(0, 5000)
        self.cout_mensuel_spin.setSuffix(" €")
        group_layout.addWidget(self.cout_mensuel_spin, 0, 3)
        
        # Statut
        group_layout.addWidget(QLabel("📈 Statut:"), 1, 0)
        self.statut_combo = QComboBox()
        self.statut_combo.addItems([
            "Sélectionner...", "Actif", "Maintenance", "En panne", 
            "Réservé", "Vendu", "En vente", "Hors service"
        ])
        group_layout.addWidget(self.statut_combo, 1, 1)
        
        layout.addWidget(group)
    
    def create_maintenance_group(self, layout):
        """Groupe maintenance"""
        group = QGroupBox("🔧 MAINTENANCE ET SUIVI")
        group_layout = QGridLayout(group)
        
        # Dernière révision
        group_layout.addWidget(QLabel("🔧 Dernière révision:"), 0, 0)
        self.derniere_revision_edit = QDateEdit()
        self.derniere_revision_edit.setDate(QDate.currentDate())
        group_layout.addWidget(self.derniere_revision_edit, 0, 1)
        
        # Conducteur
        group_layout.addWidget(QLabel("👤 Conducteur:"), 0, 2)
        self.conducteur_combo = QComboBox()
        self.conducteur_combo.addItems([
            "Sélectionner...", "Martin Jean", "Durand Marie", "Leblanc Pierre",
            "Moreau Sophie", "Bernard Luc", "Petit Anne", "Roux Thomas"
        ])
        group_layout.addWidget(self.conducteur_combo, 0, 3)
        
        layout.addWidget(group)
    
    def create_assurance_group(self, layout):
        """Groupe assurance"""
        group = QGroupBox("🛡️ ASSURANCE")
        group_layout = QGridLayout(group)
        
        # Type assurance
        group_layout.addWidget(QLabel("🛡️ Type:"), 0, 0)
        self.assurance_combo = QComboBox()
        self.assurance_combo.addItems([
            "Sélectionner...", "Tous risques", "Tiers étendu", "Tiers"
        ])
        group_layout.addWidget(self.assurance_combo, 0, 1)
        
        # Compagnie
        group_layout.addWidget(QLabel("🏢 Compagnie:"), 0, 2)
        self.compagnie_edit = QLineEdit()
        self.compagnie_edit.setPlaceholderText("Ex: AXA, Allianz...")
        group_layout.addWidget(self.compagnie_edit, 0, 3)
        
        layout.addWidget(group)
    
    def create_observations_group(self, layout):
        """Groupe observations"""
        group = QGroupBox("📝 OBSERVATIONS")
        group_layout = QVBoxLayout(group)
        
        self.observations_edit = QTextEdit()
        self.observations_edit.setPlaceholderText("Observations, remarques, historique du véhicule...")
        self.observations_edit.setMaximumHeight(80)
        group_layout.addWidget(self.observations_edit)
        
        layout.addWidget(group)
    
    def create_action_buttons_pro(self, layout):
        """Boutons d'action professionnels"""
        buttons_layout = QHBoxLayout()
        
        buttons = [
            ("💾 ENREGISTRER", "#4caf50"),
            ("✏️ MODIFIER", "#2196f3"),
            ("🗑️ SUPPRIMER", "#f44336"),
            ("📋 DUPLIQUER", "#ff9800"),
            ("📊 RAPPORT", "#9c27b0"),
            ("❌ FERMER", "#607d8b")
        ]
        
        for text, color in buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:0.5 rgba(255,255,255,0.2), stop:1 {color});
                    color: white;
                    border: 2px solid {color};
                    border-radius: 8px;
                    font-size: 12pt;
                    font-weight: bold;
                    padding: 12px 20px;
                    min-width: 140px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.3), stop:0.5 {color}, stop:1 rgba(255,255,255,0.3));
                    border: 2px solid white;
                }}
            """)
            if text == "❌ FERMER":
                btn.clicked.connect(self.close)
            buttons_layout.addWidget(btn)
        
        layout.addLayout(buttons_layout)

class WidgetDateTimeProFrancais(QWidget):
    """Widget date/heure professionnel français avec plus de contenu"""

    def __init__(self):
        super().__init__()
        self.setup_datetime_pro()

        # Timer pour mise à jour
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)

    def setup_datetime_pro(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # Style professionnel 3D
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff5722, stop:0.2 #ff7043, stop:0.8 #ff6f00, stop:1 #ff5722);
                border: 3px solid #ff5722;
                border-radius: 15px;
                color: white;
            }
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                padding: 12px;
                margin: 4px;
                color: white;
                font-weight: bold;
            }
        """)

        # Date française
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.date_label.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                min-height: 50px;
            }
        """)
        layout.addWidget(self.date_label)

        # Heure
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                font-size: 22pt;
                font-weight: bold;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.time_label)

        # Événements du jour
        self.events_label = QLabel()
        self.events_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.events_label.setStyleSheet("""
            QLabel {
                font-size: 10pt;
                min-height: 80px;
            }
        """)
        layout.addWidget(self.events_label)

        # Informations supplémentaires
        self.extra_info = QLabel()
        self.extra_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.extra_info.setStyleSheet("""
            QLabel {
                font-size: 9pt;
                min-height: 40px;
            }
        """)
        layout.addWidget(self.extra_info)

        self.update_datetime()

    def update_datetime(self):
        """Met à jour date/heure en français avec événements"""
        now = datetime.now()

        # Date en français
        jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
        mois = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']

        jour_semaine = jours[now.weekday()]
        mois_nom = mois[now.month - 1]

        self.date_label.setText(f"📅 {jour_semaine} {now.day} {mois_nom} {now.year}")
        self.time_label.setText(f"🕐 {now.strftime('%H:%M:%S')}")

        # Événements du jour
        events = self.get_daily_events(now)
        self.events_label.setText(f"📋 ÉVÉNEMENTS:\n{events}")

        # Informations supplémentaires
        week_num = now.isocalendar()[1]
        day_of_year = now.timetuple().tm_yday
        trimestre = (now.month-1)//3 + 1
        self.extra_info.setText(f"📊 Semaine {week_num} • Jour {day_of_year}/365\n🗓️ Trimestre {trimestre} • 🌍 UTC+1")

    def get_daily_events(self, date):
        """Récupère les événements du jour"""
        events = []

        # Événements selon jour de la semaine
        if date.weekday() == 0:  # Lundi
            events.append("📊 Réunion équipe 9h")
            events.append("📋 Planning semaine")
        elif date.weekday() == 2:  # Mercredi
            events.append("🔧 Maintenance véhicules")
            events.append("📞 Suivi clients")
        elif date.weekday() == 4:  # Vendredi
            events.append("💾 Sauvegarde données")
            events.append("📊 Bilan semaine")

        # Événements mensuels
        if date.day == 1:
            events.append("📈 Rapport mensuel")
        elif date.day == 15:
            events.append("💰 Paie employés")

        if not events:
            events.append("📅 Journée normale")
            events.append("✅ Pas d'événement")

        return '\n'.join(events[:3])  # Limiter à 3 événements

class WidgetPrieresPro(QWidget):
    """Widget prières professionnel avec plus de contenu"""

    def __init__(self):
        super().__init__()
        self.setup_prieres_pro()

    def setup_prieres_pro(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # Style professionnel 3D
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4caf50, stop:0.2 #66bb6a, stop:0.8 #4caf50, stop:1 #388e3c);
                border: 3px solid #4caf50;
                border-radius: 15px;
                color: white;
            }
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 4px;
                color: white;
                font-weight: bold;
            }
        """)

        # Titre
        title = QLabel("🕌 HEURES PRIÈRE GPS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 14pt; font-weight: bold; min-height: 40px;")
        layout.addWidget(title)

        # Localisation
        location = QLabel("📍 Paris, France\n🌍 48.8566°N, 2.3522°E\n🕌 Qibla: 119° SE")
        location.setAlignment(Qt.AlignmentFlag.AlignLeft)
        location.setStyleSheet("font-size: 10pt; min-height: 60px;")
        layout.addWidget(location)

        # Heures de prière
        prieres = QLabel("🌅 Fajr: 05:45\n🌞 Dhuhr: 12:30\n🌇 Asr: 15:45\n🌅 Maghrib: 18:15\n🌙 Isha: 20:00")
        prieres.setAlignment(Qt.AlignmentFlag.AlignLeft)
        prieres.setStyleSheet("font-size: 11pt; min-height: 100px;")
        layout.addWidget(prieres)

        # Prochaine prière
        prochaine = QLabel("⏰ Prochaine: Dhuhr\n🔔 Dans 2h 15min\n📢 Notification: 4min avant")
        prochaine.setAlignment(Qt.AlignmentFlag.AlignLeft)
        prochaine.setStyleSheet("font-size: 10pt; min-height: 50px;")
        layout.addWidget(prochaine)

class WidgetMeteoPro(QWidget):
    """Widget météo professionnel avec plus de contenu"""

    def __init__(self):
        super().__init__()
        self.setup_meteo_pro()

    def setup_meteo_pro(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # Style professionnel 3D
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196f3, stop:0.2 #42a5f5, stop:0.8 #2196f3, stop:1 #1976d2);
                border: 3px solid #2196f3;
                border-radius: 15px;
                color: white;
            }
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 4px;
                color: white;
                font-weight: bold;
            }
        """)

        # Titre
        title = QLabel("🌤️ MÉTÉO 15 JOURS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 14pt; font-weight: bold; min-height: 40px;")
        layout.addWidget(title)

        # Météo actuelle
        actuelle = QLabel("🌤️ Aujourd'hui: 22°C\n💨 Vent: 15 km/h NE\n💧 Humidité: 65%\n🌡️ Ressentie: 24°C")
        actuelle.setAlignment(Qt.AlignmentFlag.AlignLeft)
        actuelle.setStyleSheet("font-size: 11pt; min-height: 80px;")
        layout.addWidget(actuelle)

        # Prévisions
        previsions = QLabel("📅 PRÉVISIONS:\n🌤️ Lun: 23°C\n☀️ Mar: 25°C\n🌧️ Mer: 18°C\n⛅ Jeu: 20°C")
        previsions.setAlignment(Qt.AlignmentFlag.AlignLeft)
        previsions.setStyleSheet("font-size: 10pt; min-height: 80px;")
        layout.addWidget(previsions)

        # Alertes
        alertes = QLabel("⚠️ ALERTES:\n🌧️ Pluie demain\n💨 Vent fort jeudi")
        alertes.setAlignment(Qt.AlignmentFlag.AlignLeft)
        alertes.setStyleSheet("font-size: 10pt; min-height: 40px;")
        layout.addWidget(alertes)

class WidgetConnexionsPro(QWidget):
    """Widget connexions professionnel"""

    def __init__(self):
        super().__init__()
        self.setup_connexions_pro()

    def setup_connexions_pro(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # Style professionnel 3D
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #9c27b0, stop:0.2 #ab47bc, stop:0.8 #9c27b0, stop:1 #7b1fa2);
                border: 3px solid #9c27b0;
                border-radius: 15px;
                color: white;
            }
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 4px;
                color: white;
                font-weight: bold;
            }
        """)

        # Titre
        title = QLabel("📡 CONNEXIONS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 14pt; font-weight: bold; min-height: 40px;")
        layout.addWidget(title)

        # WiFi
        wifi = QLabel("📶 WiFi: 🟢 Excellent\n📊 150 Mbps\n📡 Signal: -45 dBm\n🌐 IP: *************")
        wifi.setAlignment(Qt.AlignmentFlag.AlignLeft)
        wifi.setStyleSheet("font-size: 10pt; min-height: 80px;")
        layout.addWidget(wifi)

        # Cellulaire
        cellular = QLabel("📱 4G+: 🟢 85%\n📡 Orange\n🌐 50 Mbps\n📊 Latence: 25ms")
        cellular.setAlignment(Qt.AlignmentFlag.AlignLeft)
        cellular.setStyleSheet("font-size: 10pt; min-height: 80px;")
        layout.addWidget(cellular)

        # Serveurs
        serveurs = QLabel("🖥️ Serveurs: 🟢 OK\n💾 Base: Connectée\n🔄 Sync: Active")
        serveurs.setAlignment(Qt.AlignmentFlag.AlignLeft)
        serveurs.setStyleSheet("font-size: 10pt; min-height: 50px;")
        layout.addWidget(serveurs)

class WidgetMemosPro(QWidget):
    """Widget mémos professionnel"""

    def __init__(self):
        super().__init__()
        self.setup_memos_pro()

    def setup_memos_pro(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # Style professionnel 3D
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e91e63, stop:0.2 #ec407a, stop:0.8 #e91e63, stop:1 #c2185b);
                border: 3px solid #e91e63;
                border-radius: 15px;
                color: white;
            }
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
                color: white;
                font-weight: bold;
            }
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.3), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.5);
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 10pt;
            }
        """)

        # Titre
        title = QLabel("📝 MÉMOS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 14pt; font-weight: bold; min-height: 35px;")
        layout.addWidget(title)

        # Zone de saisie
        self.memo_input = QLineEdit()
        self.memo_input.setPlaceholderText("Nouveau mémo...")
        layout.addWidget(self.memo_input)

        # Mémos existants
        memos = [
            "📋 Réunion lundi 14h",
            "📞 Appeler client Martin",
            "💾 Sauvegarde vendredi",
            "📊 Rapport trimestriel",
            "🔧 Maintenance weekend"
        ]

        for memo in memos:
            memo_label = QLabel(memo)
            memo_label.setStyleSheet("font-size: 9pt; min-height: 25px;")
            layout.addWidget(memo_label)

class MasterComptaProInterface3D(QMainWindow):
    """Interface MASTER COMPTA professionnelle 3D avec normes du marché"""

    def __init__(self):
        super().__init__()
        self.setup_interface_pro_3d()

    def setup_interface_pro_3d(self):
        """Configure l'interface professionnelle 3D"""
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Professionnelle 3D")

        # Taille professionnelle standard
        screen = QApplication.primaryScreen().geometry()
        width = min(1800, int(screen.width() * 0.95))
        height = min(1200, int(screen.height() * 0.95))
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        # Style professionnel 3D avec normes du marché
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.3 #2a5298, stop:0.7 #1e3c72, stop:1 #0d47a1);
                color: white;
                font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:0.5 #1976d2, stop:1 #0d47a1);
                border-bottom: 3px solid #00bcd4;
                padding: 8px;
                font-size: 13pt;
                font-weight: bold;
                color: white;
                border-radius: 0px 0px 10px 10px;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 16px;
                border-radius: 6px;
                color: white;
                margin: 2px;
            }
            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.3), stop:1 rgba(255,255,255,0.1));
                border: 2px solid #00bcd4;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00bcd4, stop:0.3 #26c6da, stop:0.7 #4dd0e1, stop:1 #00bcd4);
                border-top: 3px solid #0097a7;
                color: white;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
                border-radius: 10px 10px 0px 0px;
                min-height: 35px;
            }
        """)

        # Menu professionnel
        self.create_menu_professionnel()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Zone supérieure avec widgets professionnels
        self.create_widgets_professionnels(main_layout)

        # Zone centrale avec modules professionnels
        self.create_modules_professionnels(main_layout)

        # Zone inférieure avec outils professionnels
        self.create_outils_professionnels(main_layout)

        # Barre de statut bleu turquoise
        self.create_statusbar_turquoise()

    def create_menu_professionnel(self):
        """Crée le menu professionnel"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 FICHIER")
        file_menu.addAction("🆕 Nouveau", self.nouveau_fichier)
        file_menu.addAction("📂 Ouvrir", self.ouvrir_fichier)
        file_menu.addAction("💾 Enregistrer", self.enregistrer_fichier)
        file_menu.addAction("🖨️ Imprimer", self.imprimer_fichier)
        file_menu.addAction("❌ Quitter", self.close)

        # Menu Modules
        modules_menu = menubar.addMenu("🏢 MODULES")
        modules_menu.addAction("🚗 Parc Auto", self.ouvrir_parc_auto)
        modules_menu.addAction("🏢 Immobilier", self.ouvrir_immobilier)
        modules_menu.addAction("👥 Personnel", self.ouvrir_personnel)
        modules_menu.addAction("📊 Comptabilité", self.ouvrir_comptabilite)

        # Menu Outils
        tools_menu = menubar.addMenu("🔧 OUTILS")
        tools_menu.addAction("🗃️ Bases Données", self.ouvrir_bases_donnees)
        tools_menu.addAction("📊 Rapports", self.generer_rapports)
        tools_menu.addAction("🔄 Synchroniser", self.synchroniser)

        # Menu Paramètres
        settings_menu = menubar.addMenu("⚙️ PARAMÈTRES")
        settings_menu.addAction("🎨 Interface", self.configurer_interface)
        settings_menu.addAction("🌍 Langues", self.configurer_langues)

        # Menu Aide
        help_menu = menubar.addMenu("❓ AIDE")
        help_menu.addAction("📖 Documentation", self.afficher_documentation)
        help_menu.addAction("ℹ️ À Propos", self.afficher_a_propos)

    def create_widgets_professionnels(self, layout):
        """Crée la zone supérieure avec widgets professionnels"""
        widgets_frame = QFrame()
        widgets_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:0.5 rgba(255,255,255,0.15), stop:1 rgba(255,255,255,0.1));
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 15px;
                margin: 10px;
                padding: 15px;
            }
        """)

        widgets_layout = QHBoxLayout(widgets_frame)
        widgets_layout.setSpacing(15)

        # Widgets professionnels avec tailles standards
        self.datetime_widget = WidgetDateTimeProFrancais()
        self.datetime_widget.setMinimumWidth(300)
        self.datetime_widget.setMaximumWidth(320)
        widgets_layout.addWidget(self.datetime_widget)

        self.prieres_widget = WidgetPrieresPro()
        self.prieres_widget.setMinimumWidth(280)
        self.prieres_widget.setMaximumWidth(300)
        widgets_layout.addWidget(self.prieres_widget)

        self.meteo_widget = WidgetMeteoPro()
        self.meteo_widget.setMinimumWidth(280)
        self.meteo_widget.setMaximumWidth(300)
        widgets_layout.addWidget(self.meteo_widget)

        self.connexions_widget = WidgetConnexionsPro()
        self.connexions_widget.setMinimumWidth(280)
        self.connexions_widget.setMaximumWidth(300)
        widgets_layout.addWidget(self.connexions_widget)

        self.memos_widget = WidgetMemosPro()
        self.memos_widget.setMinimumWidth(280)
        self.memos_widget.setMaximumWidth(300)
        widgets_layout.addWidget(self.memos_widget)

        layout.addWidget(widgets_frame)

    def create_modules_professionnels(self, layout):
        """Crée la zone centrale avec modules professionnels"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:0.5 rgba(255,255,255,0.15), stop:1 rgba(255,255,255,0.1));
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 15px;
                margin: 10px;
                padding: 15px;
            }
        """)

        modules_layout = QVBoxLayout(modules_frame)

        # Titre modules
        modules_title = QLabel("🏢 MODULES MASTER COMPTA PROFESSIONNELS")
        modules_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        modules_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b35, stop:0.5 #f7931e, stop:1 #ff6b35);
                border: 3px solid #ff6b35;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        modules_layout.addWidget(modules_title)

        # Grille des modules professionnels
        modules_grid = QGridLayout()
        modules_grid.setSpacing(20)

        # Modules avec tailles standards professionnelles
        modules = [
            ("🚗", "PARC AUTO", "Gestion véhicules", "#4caf50", self.ouvrir_parc_auto),
            ("🏢", "IMMOBILIER", "Gestion biens", "#2196f3", self.ouvrir_immobilier),
            ("👥", "PERSONNEL", "Gestion employés", "#ff9800", self.ouvrir_personnel),
            ("📊", "COMPTABILITÉ", "Compta générale", "#9c27b0", self.ouvrir_comptabilite),
            ("💰", "FINANCES", "Trésorerie", "#f44336", self.ouvrir_finances),
            ("📋", "INVENTAIRE", "Stock articles", "#00bcd4", self.ouvrir_inventaire),
            ("🏭", "PRODUCTION", "Gestion production", "#795548", self.ouvrir_production),
            ("🛒", "ACHATS", "Fournisseurs", "#607d8b", self.ouvrir_achats)
        ]

        for i, (icon, title, desc, color, callback) in enumerate(modules):
            row = i // 4
            col = i % 4

            module_btn = QPushButton()
            module_btn.setMinimumSize(280, 140)  # Taille standard professionnelle
            module_btn.setMaximumSize(300, 160)
            module_btn.clicked.connect(callback)

            module_btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}, stop:0.3 rgba(255,255,255,0.3),
                        stop:0.7 {color}, stop:1 rgba(0,0,0,0.2));
                    color: white;
                    border: 3px solid {color};
                    border-radius: 15px;
                    font-size: 16pt;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255,255,255,0.4), stop:0.3 {color},
                        stop:0.7 rgba(255,255,255,0.4), stop:1 {color});
                    border: 3px solid white;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(0,0,0,0.3), stop:0.3 {color},
                        stop:0.7 rgba(0,0,0,0.3), stop:1 {color});
                }}
            """)

            module_btn.setText(f"{icon}\n{title}\n{desc}")

            modules_grid.addWidget(module_btn, row, col)

        modules_layout.addLayout(modules_grid)
        layout.addWidget(modules_frame)

    def create_outils_professionnels(self, layout):
        """Crée la zone inférieure avec outils professionnels"""
        tools_frame = QFrame()
        tools_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:0.5 rgba(255,255,255,0.15), stop:1 rgba(255,255,255,0.1));
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 15px;
                margin: 10px;
                padding: 15px;
            }
        """)

        tools_layout = QHBoxLayout(tools_frame)
        tools_layout.setSpacing(15)

        # Outils professionnels avec tailles standards
        tools = [
            ("📋", "COPIER", "#37474f"),
            ("📄", "COLLER", "#455a64"),
            ("✂️", "COUPER", "#546e7a"),
            ("↩️", "ANNULER", "#607d8b"),
            ("🔍", "RECHERCHER", "#78909c"),
            ("💾", "SAUVEGARDER", "#90a4ae"),
            ("🖨️", "IMPRIMER", "#b0bec5"),
            ("🔄", "SYNCHRONISER", "#cfd8dc")
        ]

        for icon, text, color in tools:
            tool_btn = QPushButton(f"{icon}\n{text}")
            tool_btn.setMinimumSize(180, 80)  # Taille standard
            tool_btn.setMaximumSize(200, 90)
            tool_btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:0.5 rgba(255,255,255,0.2), stop:1 {color});
                    color: white;
                    border: 3px solid {color};
                    border-radius: 12px;
                    font-size: 12pt;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.3), stop:0.5 {color}, stop:1 rgba(255,255,255,0.3));
                    border: 3px solid white;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0,0,0,0.3), stop:0.5 {color}, stop:1 rgba(0,0,0,0.3));
                }}
            """)
            tools_layout.addWidget(tool_btn)

        layout.addWidget(tools_frame)

    def create_statusbar_turquoise(self):
        """Crée la barre de statut bleu turquoise professionnelle"""
        statusbar = self.statusBar()

        # Message principal
        statusbar.showMessage("🏢 MASTER COMPTA GÉNÉRAL - Interface Professionnelle 3D • Date Française • Formulaires Complets • Normes du Marché")

        # Widgets permanents avec style professionnel
        # Heure actuelle
        self.status_time = QLabel()
        self.status_time.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                padding: 8px;
                font-size: 12pt;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 6px;
                margin: 3px;
                min-width: 100px;
            }
        """)
        statusbar.addPermanentWidget(self.status_time)

        # Statut système
        self.status_system = QLabel("🟢 Système OK")
        self.status_system.setStyleSheet("""
            QLabel {
                color: #4caf50;
                font-weight: bold;
                padding: 8px;
                font-size: 12pt;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(76,175,80,0.2), stop:1 rgba(76,175,80,0.1));
                border: 2px solid #4caf50;
                border-radius: 6px;
                margin: 3px;
                min-width: 120px;
            }
        """)
        statusbar.addPermanentWidget(self.status_system)

        # Mode professionnel
        self.status_mode = QLabel("MODE PRO 3D")
        self.status_mode.setStyleSheet("""
            QLabel {
                color: #ff9800;
                font-weight: bold;
                padding: 8px;
                font-size: 12pt;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,152,0,0.2), stop:1 rgba(255,152,0,0.1));
                border: 2px solid #ff9800;
                border-radius: 6px;
                margin: 3px;
                min-width: 120px;
            }
        """)
        statusbar.addPermanentWidget(self.status_mode)

        # Timer pour mise à jour
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_statusbar)
        self.status_timer.start(1000)

    def update_statusbar(self):
        """Met à jour la barre de statut"""
        now = datetime.now()
        self.status_time.setText(f"🕐 {now.strftime('%H:%M:%S')}")

    # Méthodes d'ouverture des modules
    def ouvrir_parc_auto(self):
        """Ouvre le formulaire Parc Auto professionnel"""
        self.formulaire_vehicule = FormulaireVehiculeProComplet()
        self.formulaire_vehicule.show()

    # Autres méthodes (stubs pour l'instant)
    def nouveau_fichier(self): print("🆕 Nouveau fichier")
    def ouvrir_fichier(self): print("📂 Ouvrir fichier")
    def enregistrer_fichier(self): print("💾 Enregistrer fichier")
    def imprimer_fichier(self): print("🖨️ Imprimer fichier")
    def ouvrir_immobilier(self): print("🏢 Immobilier")
    def ouvrir_personnel(self): print("👥 Personnel")
    def ouvrir_comptabilite(self): print("📊 Comptabilité")
    def ouvrir_finances(self): print("💰 Finances")
    def ouvrir_inventaire(self): print("📋 Inventaire")
    def ouvrir_production(self): print("🏭 Production")
    def ouvrir_achats(self): print("🛒 Achats")
    def ouvrir_bases_donnees(self): print("🗃️ Bases données")
    def generer_rapports(self): print("📊 Rapports")
    def synchroniser(self): print("🔄 Synchroniser")
    def configurer_interface(self): print("🎨 Interface")
    def configurer_langues(self): print("🌍 Langues")
    def afficher_documentation(self): print("📖 Documentation")
    def afficher_a_propos(self): print("ℹ️ À propos")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Configuration professionnelle
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("Interface Professionnelle 3D")
    app.setOrganizationName("MASTER COMPTA")

    window = MasterComptaProInterface3D()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
