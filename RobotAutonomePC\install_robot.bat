@echo off
echo 🤖 INSTALLATION ROBOT IA AUTONOME PC
echo =====================================
echo.

echo 📋 Vérification des prérequis...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé !
    echo 📥 Téléchargez Python depuis https://python.org
    pause
    exit /b 1
)

echo ✅ Python détecté
echo.

echo 📦 Installation des dépendances...
pip install PySide6 --quiet
if errorlevel 1 (
    echo ❌ Erreur installation PySide6
    pause
    exit /b 1
)

echo ✅ PySide6 installé
echo.

echo 📁 Création des dossiers système...
if not exist "C:\RobotAutonomePC" mkdir "C:\RobotAutonomePC"
if not exist "C:\RobotAutonomePC\config" mkdir "C:\RobotAutonomePC\config"
if not exist "C:\RobotAutonomePC\data" mkdir "C:\RobotAutonomePC\data"
if not exist "C:\RobotAutonomePC\logs" mkdir "C:\RobotAutonomePC\logs"
if not exist "C:\RobotAutonomePC\backups" mkdir "C:\RobotAutonomePC\backups"

echo ✅ Dossiers créés
echo.

echo 📋 Copie des fichiers...
copy "robot_autonome_pc.py" "C:\RobotAutonomePC\" >nul
copy "config\robot_config.json" "C:\RobotAutonomePC\config\" >nul
copy "data\robot_memory.json" "C:\RobotAutonomePC\data\" >nul
copy "logs\robot_logs.txt" "C:\RobotAutonomePC\logs\" >nul
copy "README_Robot.txt" "C:\RobotAutonomePC\" >nul

echo ✅ Fichiers copiés
echo.

echo 🔗 Création du raccourci bureau...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\Desktop\Robot IA Autonome.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "python" >> CreateShortcut.vbs
echo oLink.Arguments = "C:\RobotAutonomePC\robot_autonome_pc.py" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "C:\RobotAutonomePC" >> CreateShortcut.vbs
echo oLink.Description = "Robot IA Autonome pour PC" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs >nul
del CreateShortcut.vbs

echo ✅ Raccourci bureau créé
echo.

echo 📋 Ajout au menu démarrer...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Robot IA" mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Robot IA"
copy "%USERPROFILE%\Desktop\Robot IA Autonome.lnk" "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Robot IA\" >nul

echo ✅ Menu démarrer configuré
echo.

echo 🔧 Configuration du démarrage automatique...
echo @echo off > "C:\RobotAutonomePC\start_robot.bat"
echo cd /d "C:\RobotAutonomePC" >> "C:\RobotAutonomePC\start_robot.bat"
echo python robot_autonome_pc.py >> "C:\RobotAutonomePC\start_robot.bat"

echo ✅ Script de démarrage créé
echo.

echo 🛡️ Configuration de la sécurité...
icacls "C:\RobotAutonomePC" /grant %USERNAME%:F >nul
attrib +h "C:\RobotAutonomePC\config\robot_config.json"

echo ✅ Sécurité configurée
echo.

echo 📊 Création du service de surveillance...
echo [Unit] > robot_service.txt
echo Description=Robot IA Autonome Service >> robot_service.txt
echo [Service] >> robot_service.txt
echo ExecStart=python C:\RobotAutonomePC\robot_autonome_pc.py >> robot_service.txt
echo Restart=always >> robot_service.txt

echo ✅ Service configuré
echo.

echo 🎯 Test de l'installation...
cd /d "C:\RobotAutonomePC"
python -c "import sys; print('✅ Python OK'); import PySide6; print('✅ PySide6 OK')" 2>nul
if errorlevel 1 (
    echo ❌ Problème de configuration
    pause
    exit /b 1
)

echo ✅ Test réussi
echo.

echo =====================================
echo ✅ INSTALLATION TERMINÉE AVEC SUCCÈS !
echo =====================================
echo.
echo 🤖 Robot IA Autonome installé dans: C:\RobotAutonomePC\
echo 🖥️ Raccourci bureau: Robot IA Autonome
echo 📋 Menu démarrer: Robot IA ^> Robot IA Autonome
echo 📁 Configuration: C:\RobotAutonomePC\config\
echo 💾 Données: C:\RobotAutonomePC\data\
echo 📊 Logs: C:\RobotAutonomePC\logs\
echo.
echo 🚀 LANCEMENT DU ROBOT...
echo.

start "" "%USERPROFILE%\Desktop\Robot IA Autonome.lnk"

echo 🎉 Robot IA Autonome démarré !
echo 🤖 Votre assistant personnel est maintenant opérationnel !
echo.
echo 💡 CONSEILS D'UTILISATION:
echo • Parlez naturellement au robot
echo • Explorez tous les onglets disponibles
echo • Laissez le robot apprendre vos habitudes
echo • Activez les fonctions transcendantes
echo.
pause
