#!/usr/bin/env python3
"""
[LANCE] LANCEUR GESTIMMOB v2.0.0
Script de lancement optimisé pour GestImmob
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """Lance GestImmob avec vérifications"""
    
    print("🏠 " + "="*60)
    print("[LANCE] LANCEMENT DE GESTIMMOB v2.0.0")
    print("[STATS] Logiciel de Gestion Immobilière Professionnel")
    print("="*64)
    
    # Vérifier Python
    print(f"🐍 Python: {sys.version}")
    
    # Vérifier les fichiers
    files_to_check = [
        "gestimmob_simple_operationnel.py"
    ]
    
    print("\n🔍 Vérification des fichiers...")
    for file in files_to_check:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"[ECHEC] {file} - MANQUANT")
            return
    
    # Vérifier PySide6
    print("\n📦 Vérification des dépendances...")
    try:
        import PySide6
        print(f"✅ PySide6: {PySide6.__version__}")
    except ImportError:
        print("[ECHEC] PySide6 non installé")
        print("💡 Installation: pip install PySide6")
        return
    
    print("\n🎨 Configuration détectée:")
    print("• Interface moderne avec thèmes dynamiques")
    print("• Base de données SQLite intégrée")
    print("• Tous les modules opérationnels")
    print("• Optimisé pour machines performantes")
    
    print("\n[LANCE] Lancement de GestImmob...")
    print("👤 Connexion: admin / Admin@1234")
    print("="*64)
    
    # Lancer l'application
    try:
        subprocess.run([sys.executable, "gestimmob_simple_operationnel.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 Fermeture de GestImmob...")
    except Exception as e:
        print(f"\n[ECHEC] Erreur lors du lancement: {e}")

if __name__ == "__main__":
    main()
