#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic exhaustif de TOUS les problèmes - analyse ligne par ligne
"""

import ast
import os
import sys
import traceback
import subprocess
from pathlib import Path

def test_file_exhaustive(file_path):
    """Test exhaustif d'un fichier avec analyse ligne par ligne"""
    print(f"\n{'='*80}")
    print(f"DIAGNOSTIC EXHAUSTIF: {file_path}")
    print(f"{'='*80}")
    
    if not os.path.exists(file_path):
        print(f"❌ FICHIER NON TROUVÉ: {file_path}")
        return False, [f"Fichier non trouvé: {file_path}"]
    
    errors = []
    warnings = []
    
    try:
        # Lecture du fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier lu: {len(content)} caractères, {len(content.splitlines())} lignes")
        
        if not content.strip():
            error = "Fichier vide"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        
        lines = content.splitlines()
        
        # Test 1: AST parsing avec localisation précise des erreurs
        print(f"\n🔍 TEST 1: ANALYSE AST (Abstract Syntax Tree)")
        print("-" * 50)
        try:
            tree = ast.parse(content, filename=file_path)
            print(f"✅ AST parsing: RÉUSSI")
        except SyntaxError as e:
            error = f"ERREUR AST ligne {e.lineno}, colonne {e.offset}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            
            # Afficher le contexte détaillé de l'erreur
            if e.lineno and e.lineno <= len(lines):
                print(f"\n📍 CONTEXTE DÉTAILLÉ DE L'ERREUR:")
                start = max(0, e.lineno - 5)
                end = min(len(lines), e.lineno + 5)
                for i in range(start, end):
                    marker = " >>> " if i == e.lineno - 1 else "     "
                    line_num = f"{i+1:4d}"
                    print(f"{marker}{line_num}: {lines[i]}")
                
                if e.text:
                    print(f"\n📍 Code problématique exact: {repr(e.text.strip())}")
                    print(f"📍 Position du problème: colonne {e.offset}")
            
            return False, errors
        except Exception as e:
            error = f"ERREUR AST inattendue: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
            traceback.print_exc()
            return False, errors
        
        # Test 2: Compilation Python
        print(f"\n🔍 TEST 2: COMPILATION PYTHON")
        print("-" * 50)
        try:
            compile(content, file_path, 'exec')
            print(f"✅ Compilation: RÉUSSIE")
        except SyntaxError as e:
            error = f"ERREUR COMPILATION ligne {e.lineno}, colonne {e.offset}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        except Exception as e:
            error = f"ERREUR COMPILATION inattendue: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        
        # Test 3: Analyse ligne par ligne des problèmes spécifiques
        print(f"\n🔍 TEST 3: ANALYSE LIGNE PAR LIGNE")
        print("-" * 50)
        
        specific_issues = []
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Problème 1: dict[...] au lieu de Dict[...]
            if 'dict[' in line and 'Dict[' not in line and not line_stripped.startswith('#'):
                issue = f"Ligne {i}: 'dict[...]' devrait être 'Dict[...]'"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
                print(f"    Code: {line.strip()}")
            
            # Problème 2: list[...] au lieu de List[...]
            if 'list[' in line and 'List[' not in line and not line_stripped.startswith('#'):
                issue = f"Ligne {i}: 'list[...]' devrait être 'List[...]'"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
                print(f"    Code: {line.strip()}")
            
            # Problème 3: any au lieu de Any
            if ': any =' in line.lower() and not line_stripped.startswith('#'):
                issue = f"Ligne {i}: 'any' devrait être 'Any'"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
                print(f"    Code: {line.strip()}")
            
            # Problème 4: Imports manquants ou incorrects
            if i == 1 or (i <= 20 and ('import' in line or 'from' in line)):
                if ('Dict[' in content or 'List[' in content or 'Any' in content) and 'from typing import' not in content:
                    if not any('Import typing manquant' in issue for issue in specific_issues):
                        issue = "Import typing manquant pour les annotations de type"
                        specific_issues.append(issue)
                        print(f"⚠️ {issue}")
            
            # Problème 5: Fonctions appelées mais non définies
            if 'encrypt_password(' in line and 'def encrypt_password' not in content:
                issue = f"Ligne {i}: Fonction 'encrypt_password' appelée mais non définie"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
            
            if 'get_db_session(' in line and 'def get_db_session' not in content:
                issue = f"Ligne {i}: Fonction 'get_db_session' appelée mais non définie"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
            
            # Problème 6: Imports circulaires ou problématiques
            if 'from ..' in line and 'import' in line:
                issue = f"Ligne {i}: Import relatif potentiellement problématique"
                warnings.append(issue)
                print(f"⚠️ {issue}")
        
        if specific_issues:
            errors.extend(specific_issues)
            print(f"\n📊 PROBLÈMES SPÉCIFIQUES DÉTECTÉS: {len(specific_issues)}")
        else:
            print(f"✅ Aucun problème spécifique détecté")
        
        # Test 4: Test d'import simulé
        print(f"\n🔍 TEST 4: SIMULATION D'IMPORT")
        print("-" * 50)
        try:
            # Créer un environnement temporaire pour tester l'import
            temp_dir = "temp_test"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            
            # Copier le fichier dans le répertoire temporaire
            temp_file = os.path.join(temp_dir, os.path.basename(file_path))
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Tenter d'importer (sans exécuter)
            print(f"✅ Simulation d'import: Fichier syntaxiquement correct")
            
            # Nettoyer
            os.remove(temp_file)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
                
        except Exception as e:
            warning = f"Simulation d'import: {e}"
            warnings.append(warning)
            print(f"⚠️ {warning}")
        
        # Test 5: Statistiques détaillées
        print(f"\n📊 STATISTIQUES DÉTAILLÉES:")
        print("-" * 30)
        print(f"   Classes: {content.count('class ')}")
        print(f"   Fonctions: {content.count('def ')}")
        print(f"   Imports: {content.count('import ')}")
        print(f"   Commentaires: {len([l for l in lines if l.strip().startswith('#')])}")
        print(f"   Lignes vides: {len([l for l in lines if not l.strip()])}")
        print(f"   Lignes de code: {len([l for l in lines if l.strip() and not l.strip().startswith('#')])}")
        
        # Résultat final
        total_issues = len(errors) + len(warnings)
        
        if errors:
            print(f"\n❌ FICHIER AVEC ERREURS: {len(errors)} erreur(s), {len(warnings)} avertissement(s)")
            return False, errors
        elif warnings:
            print(f"\n⚠️ FICHIER AVEC AVERTISSEMENTS: {len(warnings)} avertissement(s)")
            return True, warnings
        else:
            print(f"\n✅ FICHIER PARFAIT: Aucun problème détecté")
            return True, []
            
    except Exception as e:
        error = f"ERREUR INATTENDUE: {type(e).__name__}: {e}"
        print(f"❌ {error}")
        errors.append(error)
        traceback.print_exc()
        return False, errors

def main():
    """Diagnostic exhaustif de tous les fichiers"""
    print("🔍 DIAGNOSTIC EXHAUSTIF DE TOUS LES FICHIERS")
    print("=" * 80)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Liste exhaustive des fichiers à analyser
    files_to_analyze = [
        'src/main.py',
        'src/config.py',
        'src/database/db_connection.py',
        'src/database/models.py',
        'src/views/admin_view.py',
        'src/auth/user_management.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py',
        'src/utils/config.py',
        'src/inventaire.py',
        'src/fournisseur.py',
        'src/document.py',
        'src/recherche.py',
        'src/reforme.py'
    ]
    
    results = {}
    total_errors = []
    total_warnings = []
    
    for file_path in files_to_analyze:
        is_valid, issues = test_file_exhaustive(file_path)
        results[file_path] = {
            'valid': is_valid,
            'issues': issues
        }
        if not is_valid:
            total_errors.extend([f"{file_path}: {issue}" for issue in issues])
        else:
            total_warnings.extend([f"{file_path}: {issue}" for issue in issues])
    
    # Résumé final ultra-détaillé
    print(f"\n{'='*80}")
    print("📋 RÉSUMÉ EXHAUSTIF FINAL")
    print(f"{'='*80}")
    
    valid_files = sum(1 for result in results.values() if result['valid'])
    total_files = len(results)
    
    print(f"📁 Fichiers analysés: {total_files}")
    print(f"✅ Fichiers valides: {valid_files}")
    print(f"❌ Fichiers avec erreurs: {total_files - valid_files}")
    print(f"🚨 Total erreurs: {len(total_errors)}")
    print(f"⚠️ Total avertissements: {len(total_warnings)}")
    
    if total_errors:
        print(f"\n🚨 ERREURS CRITIQUES:")
        for i, error in enumerate(total_errors, 1):
            print(f"  {i:2d}. {error}")
    
    if total_warnings:
        print(f"\n⚠️ AVERTISSEMENTS:")
        for i, warning in enumerate(total_warnings[:10], 1):  # Limiter à 10
            print(f"  {i:2d}. {warning}")
        if len(total_warnings) > 10:
            print(f"  ... et {len(total_warnings) - 10} autres avertissements")
    
    print(f"\n📊 DÉTAIL PAR FICHIER:")
    for file_path, result in results.items():
        status = "✅ VALIDE" if result['valid'] else "❌ ERREURS"
        issue_count = len(result['issues'])
        print(f"  {status:12} {file_path} ({issue_count} problème(s))")
        
        if result['issues'] and not result['valid']:
            for issue in result['issues'][:2]:  # Limiter à 2 erreurs par fichier
                print(f"    - {issue}")
            if len(result['issues']) > 2:
                print(f"    ... et {len(result['issues']) - 2} autres problèmes")
    
    if valid_files == total_files:
        print(f"\n🎉 TOUS LES FICHIERS SONT PARFAITS!")
        print(f"✅ Aucune erreur critique détectée")
        print(f"✅ Tous les fichiers compilent correctement")
        print(f"✅ L'application peut être lancée")
        return True
    else:
        print(f"\n🚨 {total_files - valid_files} FICHIER(S) AVEC DES ERREURS CRITIQUES")
        print(f"❌ {len(total_errors)} erreur(s) au total")
        print(f"🔧 Corrections nécessaires avant le lancement")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat final: {'SUCCÈS COMPLET' if success else 'ÉCHEC - CORRECTIONS NÉCESSAIRES'}")
    sys.exit(0 if success else 1)
