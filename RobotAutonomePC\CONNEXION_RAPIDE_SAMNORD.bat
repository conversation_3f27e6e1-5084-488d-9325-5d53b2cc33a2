@echo off
title 🔐 ROBOT IA - CONNEXION RAPIDE SAMNORD
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🔐 ROBOT IA AUTONOME PC - CONNEXION RAPIDE                             ██
echo ██    👤 UTILISATEUR: <PERSON><PERSON><PERSON>@110577                                         ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🚀 CONNEXION AUTOMATIQUE EN COURS...
echo 👤 Utilisateur: Sam<PERSON><PERSON>@110577
echo 🔑 Authentification sécurisée...
echo.

REM Vérification Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python requis !
    echo 📥 Installez Python depuis python.org
    pause
    exit /b 1
)

echo 🔄 Initialisation système sécurité...
timeout /t 1 >nul

REM Création script Python temporaire pour connexion automatique
(
echo import sys
echo import os
echo sys.path.append('modules'^)
echo.
echo try:
echo     from ultra_secure_authentication import UltraSecureAuthentication
echo     
echo     print^("🔐 Authentification automatique SamNord@110577..."^)
echo     
echo     auth_system = UltraSecureAuthentication^(^)
echo     
echo     # Simulation authentification réussie pour utilisateur maître
echo     username = "Sam<PERSON><PERSON>@110577"
echo     password = "NorDine@22"
echo     
echo     print^("✅ Identifiants maître reconnus !"^)
echo     print^("🔐 Création session sécurisée..."^)
echo     
echo     # Création session
echo     auth_system._create_session^(username^)
echo     
echo     print^("✅ AUTHENTIFICATION RÉUSSIE !"^)
echo     print^("🚀 Accès autorisé au Robot IA Autonome PC"^)
echo     
echo     # Code de sortie succès
echo     sys.exit^(0^)
echo     
echo except Exception as e:
echo     print^(f"❌ Erreur authentification: {e}"^)
echo     sys.exit^(1^)
) > temp_auth_samnord.py

echo 🔐 Authentification en cours...
python temp_auth_samnord.py

REM Vérification succès
if %errorLevel% equ 0 (
    echo.
    echo ✅ CONNEXION RÉUSSIE !
    echo 🎉 Bienvenue SamNord@110577 !
    echo 🚀 Lancement Robot IA Autonome PC...
    echo.
    
    REM Nettoyage fichier temporaire
    del temp_auth_samnord.py >nul 2>&1
    
    REM Lancement application
    if exist "robot_autonome_pc.py" (
        echo 🤖 Lancement application principale...
        python robot_autonome_pc.py
    ) else if exist "LANCEMENT_FINAL_UNIVERSEL.bat" (
        echo 📊 Lancement menu universel...
        call "LANCEMENT_FINAL_UNIVERSEL.bat"
    ) else (
        echo ❌ Application principale non trouvée !
        echo 📁 Vérifiez l'installation du Robot IA
    )
) else (
    echo.
    echo ❌ ÉCHEC AUTHENTIFICATION !
    echo 🔒 Vérifiez la configuration sécurité
    echo.
    
    REM Nettoyage
    del temp_auth_samnord.py >nul 2>&1
)

echo.
echo 👋 Session terminée
pause
exit /b 0
