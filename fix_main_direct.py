#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CORRECTION DIRECTE ET CIBLÉE DE MAIN.PY
Corrige les vrais problèmes identifiés
"""

import re
import ast

def fix_main_py():
    """Corrige directement main.py"""
    print("🔧 CORRECTION DIRECTE DE MAIN.PY")
    print("=" * 40)
    
    # <PERSON><PERSON> le fichier
    with open('src/main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("✅ Fichier lu")
    
    # Backup
    with open('src/main.py.backup', 'w', encoding='utf-8') as f:
        f.write(content)
    print("✅ Backup créé")
    
    # Corrections spécifiques
    fixes = 0
    
    # 1. Ajouter l'import typing si manquant
    if 'from typing import' not in content:
        # Trouver la ligne après les imports PySide6
        lines = content.splitlines()
        insert_pos = 0
        for i, line in enumerate(lines):
            if 'from PySide6.QtCore import' in line:
                insert_pos = i + 1
                break
        
        if insert_pos > 0:
            lines.insert(insert_pos, 'from typing import Any, Optional, Dict, List, Tuple')
            content = '\n'.join(lines)
            fixes += 1
            print("✅ Import typing ajouté")
    
    # 2. Corriger les variables non typées les plus importantes
    important_fixes = [
        # Variables simples
        (r'(\s+)module = (__import__\()', r'\1module: Any = \2'),
        (r'(\s+)user = (cursor\.execute)', r'\1user: Any = \2'),
        (r'(\s+)data = (json\.load)', r'\1data: Dict[str, Any] = \2'),
        (r'(\s+)resp = (requests\.get)', r'\1resp: Any = \2'),
        (r'(\s+)config = (app_config\.config)', r'\1config: Dict[str, Any] = \2'),
        (r'(\s+)app = (QApplication)', r'\1app: QApplication = \2'),
        (r'(\s+)window = (ImmobilisationMainWindow)', r'\1window: ImmobilisationMainWindow = \2'),
        (r'(\s+)dialog = (QDialog)', r'\1dialog: QDialog = \2'),
        (r'(\s+)layout = (QVBoxLayout)', r'\1layout: QVBoxLayout = \2'),
        (r'(\s+)form = (QFormLayout)', r'\1form: QFormLayout = \2'),
        (r'(\s+)menu = (QMenu)', r'\1menu: QMenu = \2'),
        (r'(\s+)cursor = (self\.)', r'\1cursor: Any = \2'),
        (r'(\s+)reader = (csv\.reader)', r'\1reader: Any = \2'),
        (r'(\s+)writer = (csv\.writer)', r'\1writer: Any = \2'),
        (r'(\s+)item = (self\.table\.item)', r'\1item: Any = \2'),
        (r'(\s+)selected = (self\.table\.selectedItems)', r'\1selected: List[Any] = \2'),
        (r'(\s+)row = (self\.table\.rowCount)', r'\1row: int = \2'),
        (r'(\s+)row = (selected\[0\]\.row)', r'\1row: int = \2'),
        (r'(\s+)version = (platform\.version)', r'\1version: str = \2'),
        (r'(\s+)major = (int\(platform)', r'\1major: int = \2'),
        (r'(\s+)langue = (config\.get)', r'\1langue: str = \2'),
    ]
    
    for pattern, replacement in important_fixes:
        new_content = re.sub(pattern, replacement, content)
        if new_content != content:
            content = new_content
            fixes += 1
    
    print(f"✅ {fixes} corrections de variables appliquées")
    
    # 3. Test de validation
    try:
        ast.parse(content)
        print("✅ Syntaxe validée")
        
        # Sauvegarder
        with open('src/main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Fichier sauvegardé")
        
        print(f"\n🎉 CORRECTION RÉUSSIE !")
        print(f"✅ {fixes} problèmes corrigés")
        print("✅ Syntaxe validée")
        print("✅ Fichier main.py corrigé")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Erreur syntaxe après correction: {e}")
        # Restaurer le backup
        with open('src/main.py.backup', 'r', encoding='utf-8') as f:
            original = f.read()
        with open('src/main.py', 'w', encoding='utf-8') as f:
            f.write(original)
        print("✅ Backup restauré")
        return False

if __name__ == "__main__":
    success = fix_main_direct()
    if success:
        print("\n🚀 MAIN.PY EST MAINTENANT CORRIGÉ !")
    else:
        print("\n❌ Échec de la correction")
