<!-- Generated by SpecStory -->

# /fix La déclaration de méthode « closeEvent » est  (2025-06-08 11:57:21)

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Il y a deux définitions de la méthode `closeEvent` dans la classe `InventaireWidget`, ce qui masque la première par la seconde. Il faut supprimer l'une des deux (elles sont identiques), pour ne garder qu'une seule définition.

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

# ...supprimer la deuxième définition closeEvent ici...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez l'une des deux définitions de `closeEvent` pour éviter le masquage. Voici la correction à appliquer :

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez la deuxième définition closeEvent ici...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez la deuxième définition de `closeEvent` pour ne garder qu'une seule occurrence dans la classe.

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez ici la deuxième définition closeEvent...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez ici la deuxième définition closeEvent (gardez uniquement celle ci-dessus)...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez ici la deuxième définition closeEvent (gardez uniquement celle ci-dessus)...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez ici toute seconde définition de closeEvent dans la classe...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « scanner_code_barres » est masquée par une déclaration du même nom

---

_**Assistant**_

````python
# ...existing code...

    def scanner_code_barres(self):
        code, ok = QInputDialog.getText(self, "Scan code-barres", "Scannez ou saisissez le code-barres de l'article :")
        if ok and code:
            QMessageBox.information(self, "Code-barres", f"Code-barres scanné : {code}")
            selected = self.table.selectedItems()
            if selected:
                row = selected[0].row()
                self.table.setItem(row, 13, QTableWidgetItem(code))
                self.changements_detectes = True
                self.sauvegarder_inventaire()

    # ...supprimez ici toute seconde définition de scanner_code_barres dans la classe...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez ici toute seconde définition de closeEvent dans la classe...

# ...existing code...
````

---

