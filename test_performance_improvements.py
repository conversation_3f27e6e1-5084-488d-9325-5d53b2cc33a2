#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier les améliorations de performance
et les dépendances du logiciel GestImmob
"""

import sys
import os
import time
import json
import logging
from datetime import datetime
from typing import Dict, Any, List

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_imports_performance():
    """Test la performance des imports de modules"""
    print("🔍 Test de performance des imports...")
    
    modules_to_test = [
        'PySide6.QtWidgets',
        'PySide6.QtCore', 
        'PySide6.QtGui',
        'sqlite3',
        'json',
        'csv',
        'cryptography.fernet',
        'psutil'
    ]
    
    import_results = {}
    
    for module_name in modules_to_test:
        start_time = time.time()
        try:
            __import__(module_name)
            end_time = time.time()
            import_time = (end_time - start_time) * 1000  # en millisecondes
            import_results[module_name] = {
                'status': 'SUCCESS',
                'time_ms': round(import_time, 2)
            }
            print(f"✅ {module_name}: {import_time:.2f}ms")
        except ImportError as e:
            import_results[module_name] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ {module_name}: {e}")
    
    return import_results

def test_database_performance():
    """Test la performance de la base de données"""
    print("\n🗄️ Test de performance de la base de données...")
    
    import sqlite3
    
    # Test avec base temporaire
    test_db = "test_performance.db"
    
    try:
        # Connexion standard
        start_time = time.time()
        conn = sqlite3.connect(test_db)
        standard_time = (time.time() - start_time) * 1000
        
        # Connexion optimisée
        start_time = time.time()
        conn_opt = sqlite3.connect(test_db, check_same_thread=False)
        cursor = conn_opt.cursor()
        
        # Application des optimisations
        optimizations = [
            "PRAGMA journal_mode = WAL",
            "PRAGMA synchronous = NORMAL", 
            "PRAGMA cache_size = -64000",
            "PRAGMA temp_store = MEMORY",
            "PRAGMA mmap_size = 268435456"
        ]
        
        for pragma in optimizations:
            cursor.execute(pragma)
        
        conn_opt.commit()
        optimized_time = (time.time() - start_time) * 1000
        
        print(f"📊 Connexion standard: {standard_time:.2f}ms")
        print(f"🚀 Connexion optimisée: {optimized_time:.2f}ms")
        print(f"📈 Amélioration: {((standard_time - optimized_time) / standard_time * 100):.1f}%")
        
        # Test d'insertion
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                data TEXT,
                value REAL
            )
        ''')
        
        # Test d'insertion en lot
        start_time = time.time()
        test_data = [(i, f"test_data_{i}", i * 1.5) for i in range(1000)]
        cursor.executemany("INSERT INTO test_table (id, data, value) VALUES (?, ?, ?)", test_data)
        conn_opt.commit()
        insert_time = (time.time() - start_time) * 1000
        
        print(f"💾 Insertion 1000 enregistrements: {insert_time:.2f}ms")
        
        # Nettoyage
        conn.close()
        conn_opt.close()
        os.remove(test_db)
        
        return {
            'standard_connection_ms': round(standard_time, 2),
            'optimized_connection_ms': round(optimized_time, 2),
            'improvement_percent': round((standard_time - optimized_time) / standard_time * 100, 1),
            'insert_1000_records_ms': round(insert_time, 2)
        }
        
    except Exception as e:
        print(f"❌ Erreur lors du test DB: {e}")
        return {'error': str(e)}

def test_memory_usage():
    """Test l'utilisation mémoire"""
    print("\n🧠 Test d'utilisation mémoire...")
    
    try:
        import psutil
        process = psutil.Process()
        
        memory_info = {
            'memory_mb': round(process.memory_info().rss / 1024 / 1024, 2),
            'memory_percent': round(process.memory_percent(), 2),
            'cpu_percent': round(process.cpu_percent(), 2)
        }
        
        print(f"📊 Mémoire utilisée: {memory_info['memory_mb']} MB")
        print(f"📊 Pourcentage mémoire: {memory_info['memory_percent']}%")
        print(f"📊 CPU: {memory_info['cpu_percent']}%")
        
        return memory_info
        
    except ImportError:
        print("❌ psutil non disponible pour les métriques mémoire")
        return {'error': 'psutil not available'}

def test_module_manager():
    """Test le gestionnaire de modules optimisé"""
    print("\n🔧 Test du gestionnaire de modules...")
    
    # Ajouter le répertoire src au path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    
    try:
        # Import du gestionnaire depuis main.py
        from main import ModuleManager
        
        manager = ModuleManager()
        
        # Test d'import de modules
        start_time = time.time()
        test_modules = [
            ('json', None),
            ('sqlite3', None),
            ('fournisseur', 'FournisseurWidget'),
            ('inventaire', 'InventaireWidget')
        ]
        
        results = {}
        for module_name, class_name in test_modules:
            module_start = time.time()
            result = manager.import_module_safe(module_name, class_name)
            module_time = (time.time() - module_start) * 1000
            
            key = f"{module_name}.{class_name}" if class_name else module_name
            results[key] = {
                'success': result is not None,
                'time_ms': round(module_time, 2)
            }
        
        total_time = (time.time() - start_time) * 1000
        
        # Rapport du gestionnaire
        report = manager.get_import_report()
        
        print(f"📊 Temps total: {total_time:.2f}ms")
        print(f"✅ Modules chargés: {report['modules_loaded']}")
        print(f"❌ Modules échoués: {report['modules_failed']}")
        
        return {
            'total_time_ms': round(total_time, 2),
            'modules_loaded': report['modules_loaded'],
            'modules_failed': report['modules_failed'],
            'detailed_results': results
        }
        
    except Exception as e:
        print(f"❌ Erreur lors du test du gestionnaire: {e}")
        return {'error': str(e)}

def generate_performance_report():
    """Génère un rapport complet de performance"""
    print("📋 Génération du rapport de performance...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'python_version': sys.version,
        'platform': sys.platform,
        'tests': {}
    }
    
    # Exécution des tests
    report['tests']['imports'] = test_imports_performance()
    report['tests']['database'] = test_database_performance()
    report['tests']['memory'] = test_memory_usage()
    report['tests']['module_manager'] = test_module_manager()
    
    # Sauvegarde du rapport
    filename = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=4, ensure_ascii=False)
    
    print(f"\n📄 Rapport sauvegardé: {filename}")
    return report

def main():
    """Fonction principale"""
    print("🚀 Test des améliorations de performance GestImmob")
    print("=" * 60)
    
    try:
        report = generate_performance_report()
        
        print("\n✅ Tests terminés avec succès!")
        print(f"📊 Résumé:")
        print(f"   - Tests d'imports: {'✅' if 'imports' in report['tests'] else '❌'}")
        print(f"   - Tests base de données: {'✅' if 'database' in report['tests'] else '❌'}")
        print(f"   - Tests mémoire: {'✅' if 'memory' in report['tests'] else '❌'}")
        print(f"   - Tests gestionnaire modules: {'✅' if 'module_manager' in report['tests'] else '❌'}")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
