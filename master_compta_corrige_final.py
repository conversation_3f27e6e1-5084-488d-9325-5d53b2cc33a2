#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Corrigée Finale
Pas de déformation • Petits boutons • Bas noir • Bleu turquoise uni
Tous modules au même endroit • Tout fonctionnel
"""

import sys
import locale
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QTableWidget, QTableWidgetItem, 
    QComboBox, QGroupBox, QGridLayout, QLineEdit, QSpinBox, 
    QDateEdit, QTextEdit, QScrollArea, QFormLayout, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, QDate, QSize
from PySide6.QtGui import QColor

# Configuration locale française
try:
    locale.setlocale(locale.LC_TIME, 'fr_FR.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_TIME, 'French_France.1252')
    except:
        pass

class FormulaireVehiculeCorrige(QWidget):
    """Formulaire véhicule corrigé avec fond noir"""
    
    def __init__(self):
        super().__init__()
        self.setup_formulaire_corrige()
    
    def setup_formulaire_corrige(self):
        self.setWindowTitle("🚗 MASTER COMPTA - FORMULAIRE VÉHICULE")
        self.setGeometry(100, 100, 1200, 800)
        
        # Style fond noir corrigé
        self.setStyleSheet("""
            QWidget {
                background: #000000;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11pt;
            }
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #00bcd4;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
                background: rgba(0,188,212,0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px;
                background: #00bcd4;
                border-radius: 4px;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 11pt;
                font-weight: bold;
                padding: 2px;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                background: #1a1a1a;
                border: 2px solid #00bcd4;
                border-radius: 4px;
                padding: 6px;
                color: white;
                font-size: 11pt;
                min-height: 14px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
                border: 2px solid #4dd0e1;
                background: #2a2a2a;
            }
            QComboBox::drop-down {
                border: none;
                width: 18px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 3px solid transparent;
                border-right: 3px solid transparent;
                border-top: 3px solid white;
            }
            QComboBox QAbstractItemView {
                background: #1a1a1a;
                color: white;
                border: 2px solid #00bcd4;
                selection-background-color: #00bcd4;
            }
            QPushButton {
                background: #00bcd4;
                color: white;
                border: 2px solid #00bcd4;
                border-radius: 6px;
                font-size: 11pt;
                font-weight: bold;
                padding: 8px 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: #26c6da;
                border: 2px solid #26c6da;
            }
            QPushButton:pressed {
                background: #0097a7;
            }
            QTextEdit {
                background: #1a1a1a;
                border: 2px solid #00bcd4;
                border-radius: 4px;
                padding: 6px;
                color: white;
                font-size: 11pt;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Titre simple
        title_label = QLabel("🚗 FORMULAIRE VÉHICULE COMPLET")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: white;
                background: #00bcd4;
                border: 2px solid #00bcd4;
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # Zone de défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid #00bcd4;
                border-radius: 6px;
                background: transparent;
            }
        """)
        
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        
        # Groupes simplifiés
        self.create_info_group(form_layout)
        self.create_tech_group(form_layout)
        self.create_finance_group(form_layout)
        self.create_maintenance_group(form_layout)
        self.create_obs_group(form_layout)
        
        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)
        
        # Petits boutons bleu turquoise uni
        self.create_small_buttons(layout)
    
    def create_info_group(self, layout):
        """Groupe informations avec logos"""
        group = QGroupBox("📋 INFORMATIONS GÉNÉRALES")
        group_layout = QGridLayout(group)
        
        # Code
        group_layout.addWidget(QLabel("🏷️ Code:"), 0, 0)
        self.code_edit = QLineEdit()
        group_layout.addWidget(self.code_edit, 0, 1)
        
        # Marque
        group_layout.addWidget(QLabel("🚗 Marque:"), 0, 2)
        self.marque_combo = QComboBox()
        self.marque_combo.addItems([
            "Sélectionner...", "Peugeot", "Renault", "Citroën", "Ford", 
            "Volkswagen", "BMW", "Mercedes", "Audi", "Toyota"
        ])
        group_layout.addWidget(self.marque_combo, 0, 3)
        
        # Modèle
        group_layout.addWidget(QLabel("🔧 Modèle:"), 1, 0)
        self.modele_edit = QLineEdit()
        group_layout.addWidget(self.modele_edit, 1, 1)
        
        # Immatriculation
        group_layout.addWidget(QLabel("🔢 Immat:"), 1, 2)
        self.immat_edit = QLineEdit()
        group_layout.addWidget(self.immat_edit, 1, 3)
        
        layout.addWidget(group)
    
    def create_tech_group(self, layout):
        """Groupe technique"""
        group = QGroupBox("⚙️ TECHNIQUE")
        group_layout = QGridLayout(group)
        
        # Carburant
        group_layout.addWidget(QLabel("⛽ Carburant:"), 0, 0)
        self.carburant_combo = QComboBox()
        self.carburant_combo.addItems([
            "Sélectionner...", "Essence", "Diesel", "Hybride", "Électrique"
        ])
        group_layout.addWidget(self.carburant_combo, 0, 1)
        
        # Kilométrage
        group_layout.addWidget(QLabel("📏 Km:"), 0, 2)
        self.km_spin = QSpinBox()
        self.km_spin.setRange(0, 999999)
        self.km_spin.setSuffix(" km")
        group_layout.addWidget(self.km_spin, 0, 3)
        
        layout.addWidget(group)
    
    def create_finance_group(self, layout):
        """Groupe finance"""
        group = QGroupBox("💰 FINANCE")
        group_layout = QGridLayout(group)
        
        # Prix
        group_layout.addWidget(QLabel("💵 Prix:"), 0, 0)
        self.prix_spin = QSpinBox()
        self.prix_spin.setRange(0, 999999)
        self.prix_spin.setSuffix(" €")
        group_layout.addWidget(self.prix_spin, 0, 1)
        
        # Statut
        group_layout.addWidget(QLabel("📈 Statut:"), 0, 2)
        self.statut_combo = QComboBox()
        self.statut_combo.addItems([
            "Sélectionner...", "Actif", "Maintenance", "Vendu"
        ])
        group_layout.addWidget(self.statut_combo, 0, 3)
        
        layout.addWidget(group)
    
    def create_maintenance_group(self, layout):
        """Groupe maintenance"""
        group = QGroupBox("🔧 MAINTENANCE")
        group_layout = QGridLayout(group)
        
        # Révision
        group_layout.addWidget(QLabel("🔧 Révision:"), 0, 0)
        self.revision_edit = QDateEdit()
        self.revision_edit.setDate(QDate.currentDate())
        group_layout.addWidget(self.revision_edit, 0, 1)
        
        # Conducteur
        group_layout.addWidget(QLabel("👤 Conducteur:"), 0, 2)
        self.conducteur_combo = QComboBox()
        self.conducteur_combo.addItems([
            "Sélectionner...", "Martin Jean", "Durand Marie", "Leblanc Pierre"
        ])
        group_layout.addWidget(self.conducteur_combo, 0, 3)
        
        layout.addWidget(group)
    
    def create_obs_group(self, layout):
        """Groupe observations"""
        group = QGroupBox("📝 OBSERVATIONS")
        group_layout = QVBoxLayout(group)
        
        self.obs_edit = QTextEdit()
        self.obs_edit.setPlaceholderText("Observations...")
        self.obs_edit.setMaximumHeight(60)
        group_layout.addWidget(self.obs_edit)
        
        layout.addWidget(group)
    
    def create_small_buttons(self, layout):
        """Petits boutons bleu turquoise uni"""
        buttons_layout = QHBoxLayout()
        
        # Petits boutons unis
        buttons = [
            ("💾 ENREGISTRER", self.enregistrer),
            ("✏️ MODIFIER", self.modifier),
            ("🗑️ SUPPRIMER", self.supprimer),
            ("📊 RAPPORT", self.rapport),
            ("❌ FERMER", self.close)
        ]
        
        for text, callback in buttons:
            btn = QPushButton(text)
            btn.setFixedSize(120, 35)  # Petits boutons
            btn.clicked.connect(callback)
            btn.setStyleSheet("""
                QPushButton {
                    background: #00bcd4;
                    color: white;
                    border: 2px solid #00bcd4;
                    border-radius: 6px;
                    font-size: 10pt;
                    font-weight: bold;
                    padding: 6px;
                }
                QPushButton:hover {
                    background: #26c6da;
                }
                QPushButton:pressed {
                    background: #0097a7;
                }
            """)
            buttons_layout.addWidget(btn)
        
        layout.addLayout(buttons_layout)
    
    # Méthodes des boutons
    def enregistrer(self): print("💾 Enregistrer véhicule")
    def modifier(self): print("✏️ Modifier véhicule")
    def supprimer(self): print("🗑️ Supprimer véhicule")
    def rapport(self): print("📊 Rapport véhicule")

class WidgetDateTimeFrancais(QWidget):
    """Widget date/heure français avec bas noir"""
    
    def __init__(self):
        super().__init__()
        self.setup_datetime()
        
        # Timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)
    
    def setup_datetime(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Style avec bas noir
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff5722, stop:0.7 #ff6f00, stop:1 #000000);
                border: 2px solid #ff5722;
                border-radius: 10px;
                color: white;
            }
            QLabel {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 8px;
                margin: 2px;
                color: white;
                font-weight: bold;
            }
        """)
        
        # Date française
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.date_label.setStyleSheet("font-size: 14pt; min-height: 35px;")
        layout.addWidget(self.date_label)
        
        # Heure
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_label.setStyleSheet("font-size: 18pt; min-height: 45px;")
        layout.addWidget(self.time_label)
        
        # Événements
        self.events_label = QLabel()
        self.events_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.events_label.setStyleSheet("font-size: 9pt; min-height: 60px;")
        layout.addWidget(self.events_label)
        
        self.update_datetime()
    
    def update_datetime(self):
        """Met à jour en français"""
        now = datetime.now()
        
        # Date française
        jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
        mois = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']
        
        jour_semaine = jours[now.weekday()]
        mois_nom = mois[now.month - 1]
        
        self.date_label.setText(f"📅 {jour_semaine} {now.day} {mois_nom} {now.year}")
        self.time_label.setText(f"🕐 {now.strftime('%H:%M:%S')}")
        
        # Événements simples
        events = []
        if now.weekday() == 0:  # Lundi
            events.append("📊 Réunion 9h")
        elif now.weekday() == 4:  # Vendredi
            events.append("💾 Sauvegarde")
        else:
            events.append("📅 Journée normale")
        
        self.events_label.setText(f"📋 ÉVÉNEMENTS:\n" + '\n'.join(events))

class WidgetSimple(QWidget):
    """Widget simple avec bas noir"""
    
    def __init__(self, title, icon, content):
        super().__init__()
        self.setup_widget(title, icon, content)
    
    def setup_widget(self, title, icon, content):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Style avec bas noir
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:0.7 #66bb6a, stop:1 #000000);
                border: 2px solid #4caf50;
                border-radius: 10px;
                color: white;
            }
            QLabel {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
                color: white;
                font-weight: bold;
            }
        """)
        
        # Titre
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 12pt; min-height: 30px;")
        layout.addWidget(title_label)
        
        # Contenu
        content_label = QLabel(content)
        content_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        content_label.setStyleSheet("font-size: 9pt; min-height: 100px;")
        layout.addWidget(content_label)

class MasterComptaCorrigeFinal(QMainWindow):
    """Interface MASTER COMPTA corrigée finale"""

    def __init__(self):
        super().__init__()
        self.setup_interface_corrigee()

    def setup_interface_corrigee(self):
        """Configure l'interface corrigée"""
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Corrigée")

        # Taille fixe pour éviter déformation
        self.setFixedSize(1600, 1000)

        # Style principal avec bas noir
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.5 #2a5298, stop:1 #1e3c72);
                color: white;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                border-bottom: 2px solid #00bcd4;
                padding: 6px;
                font-size: 12pt;
                font-weight: bold;
                color: white;
            }
            QMenuBar::item {
                background: transparent;
                padding: 6px 12px;
                border-radius: 4px;
                color: white;
                margin: 1px;
            }
            QMenuBar::item:selected {
                background: rgba(255,255,255,0.2);
                border: 1px solid #00bcd4;
            }
            QStatusBar {
                background: #000000;
                border-top: 2px solid #00bcd4;
                color: white;
                font-size: 11pt;
                font-weight: bold;
                padding: 6px;
                min-height: 30px;
            }
        """)

        # Menu simple
        self.create_menu_simple()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal sans déformation
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Zone widgets avec tailles fixes
        self.create_widgets_fixes(main_layout)

        # Zone modules TOUS AU MÊME ENDROIT
        self.create_tous_modules_meme_endroit(main_layout)

        # Barre de statut noire
        self.create_statusbar_noir()

    def create_menu_simple(self):
        """Menu simple"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 FICHIER")
        file_menu.addAction("🆕 Nouveau", self.nouveau)
        file_menu.addAction("📂 Ouvrir", self.ouvrir)
        file_menu.addAction("💾 Enregistrer", self.enregistrer)
        file_menu.addAction("❌ Quitter", self.close)

        # Menu Modules
        modules_menu = menubar.addMenu("🏢 MODULES")
        modules_menu.addAction("🚗 Parc Auto", self.ouvrir_parc_auto)
        modules_menu.addAction("🏢 Immobilier", self.ouvrir_immobilier)
        modules_menu.addAction("👥 Personnel", self.ouvrir_personnel)

        # Menu Aide
        help_menu = menubar.addMenu("❓ AIDE")
        help_menu.addAction("ℹ️ À Propos", self.a_propos)

    def create_widgets_fixes(self, layout):
        """Widgets avec tailles fixes pour éviter déformation"""
        widgets_frame = QFrame()
        widgets_frame.setFixedHeight(250)  # Hauteur fixe
        widgets_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.2);
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
        """)

        widgets_layout = QHBoxLayout(widgets_frame)
        widgets_layout.setSpacing(10)

        # Widgets avec tailles fixes
        self.datetime_widget = WidgetDateTimeFrancais()
        self.datetime_widget.setFixedSize(280, 220)
        widgets_layout.addWidget(self.datetime_widget)

        # Autres widgets simples avec bas noir
        widgets_data = [
            ("PRIÈRES", "🕌", "🌅 Fajr: 05:45\n🌞 Dhuhr: 12:30\n🌇 Asr: 15:45\n🌅 Maghrib: 18:15\n🌙 Isha: 20:00"),
            ("MÉTÉO", "🌤️", "🌤️ 22°C Ensoleillé\n💨 Vent: 15 km/h\n💧 Humidité: 65%\n📅 Demain: 24°C"),
            ("CONNEXIONS", "📡", "📶 WiFi: 🟢 OK\n📱 4G: 🟢 OK\n🖥️ Serveur: 🟢 OK\n🔄 Sync: Actif"),
            ("MÉMOS", "📝", "📋 Réunion lundi\n📞 Appeler client\n💾 Sauvegarde\n📊 Rapport")
        ]

        for title, icon, content in widgets_data:
            widget = WidgetSimple(title, icon, content)
            widget.setFixedSize(280, 220)
            widgets_layout.addWidget(widget)

        layout.addWidget(widgets_frame)

    def create_tous_modules_meme_endroit(self, layout):
        """TOUS LES MODULES AU MÊME ENDROIT avec petits boutons"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.2);
                border-radius: 10px;
                margin: 5px;
                padding: 15px;
            }
        """)

        modules_layout = QVBoxLayout(modules_frame)

        # Titre
        title = QLabel("🏢 TOUS LES MODULES MASTER COMPTA")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20pt;
                font-weight: bold;
                background: #00bcd4;
                border: 2px solid #00bcd4;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        modules_layout.addWidget(title)

        # TOUS LES BOUTONS MODULES AU MÊME ENDROIT
        # Grille 4x3 pour 12 modules
        modules_grid = QGridLayout()
        modules_grid.setSpacing(10)

        # TOUS les modules avec PETITS BOUTONS BLEU TURQUOISE UNI
        modules = [
            ("🚗", "PARC AUTO", self.ouvrir_parc_auto),
            ("🏢", "IMMOBILIER", self.ouvrir_immobilier),
            ("👥", "PERSONNEL", self.ouvrir_personnel),
            ("📊", "COMPTABILITÉ", self.ouvrir_comptabilite),
            ("💰", "FINANCES", self.ouvrir_finances),
            ("📋", "INVENTAIRE", self.ouvrir_inventaire),
            ("🏭", "PRODUCTION", self.ouvrir_production),
            ("🛒", "ACHATS", self.ouvrir_achats),
            ("💼", "VENTES", self.ouvrir_ventes),
            ("📈", "CONTRÔLE", self.ouvrir_controle),
            ("🎯", "PROJETS", self.ouvrir_projets),
            ("🗃️", "BASES", self.ouvrir_bases)
        ]

        for i, (icon, title, callback) in enumerate(modules):
            row = i // 4
            col = i % 4

            # PETITS BOUTONS avec couleur unie bleu turquoise
            module_btn = QPushButton(f"{icon}\n{title}")
            module_btn.setFixedSize(200, 80)  # PETITS boutons
            module_btn.clicked.connect(callback)

            # Style BLEU TURQUOISE UNI (pas de dégradé)
            module_btn.setStyleSheet("""
                QPushButton {
                    background: #00bcd4;
                    color: white;
                    border: 2px solid #00bcd4;
                    border-radius: 8px;
                    font-size: 12pt;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background: #26c6da;
                    border: 2px solid #26c6da;
                }
                QPushButton:pressed {
                    background: #0097a7;
                }
            """)

            modules_grid.addWidget(module_btn, row, col)

        modules_layout.addLayout(modules_grid)

        # Zone pour outils SANS copier/coller/couper
        tools_layout = QHBoxLayout()

        # SEULEMENT les outils utiles (PAS copier/coller/couper)
        tools = [
            ("🔍", "RECHERCHER", self.rechercher),
            ("💾", "SAUVEGARDER", self.sauvegarder),
            ("🖨️", "IMPRIMER", self.imprimer),
            ("🔄", "SYNCHRONISER", self.synchroniser),
            ("⚡", "OPTIMISER", self.optimiser)
        ]

        for icon, text, callback in tools:
            tool_btn = QPushButton(f"{icon}\n{text}")
            tool_btn.setFixedSize(150, 60)  # PETITS boutons
            tool_btn.clicked.connect(callback)
            tool_btn.setStyleSheet("""
                QPushButton {
                    background: #00bcd4;
                    color: white;
                    border: 2px solid #00bcd4;
                    border-radius: 6px;
                    font-size: 10pt;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background: #26c6da;
                }
                QPushButton:pressed {
                    background: #0097a7;
                }
            """)
            tools_layout.addWidget(tool_btn)

        modules_layout.addLayout(tools_layout)
        layout.addWidget(modules_frame)

    def create_statusbar_noir(self):
        """Barre de statut NOIRE"""
        statusbar = self.statusBar()

        # Message principal
        statusbar.showMessage("🏢 MASTER COMPTA GÉNÉRAL - Interface Corrigée • Pas de déformation • Petits boutons • Bas noir • Bleu turquoise uni")

        # Widget heure
        self.status_time = QLabel()
        self.status_time.setStyleSheet("""
            QLabel {
                color: #00bcd4;
                font-weight: bold;
                padding: 4px;
                font-size: 11pt;
                background: rgba(0,188,212,0.1);
                border: 1px solid #00bcd4;
                border-radius: 4px;
                margin: 2px;
            }
        """)
        statusbar.addPermanentWidget(self.status_time)

        # Timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_statusbar)
        self.status_timer.start(1000)

    def update_statusbar(self):
        """Met à jour la barre de statut"""
        now = datetime.now()
        self.status_time.setText(f"🕐 {now.strftime('%H:%M:%S')}")

    # Méthodes des modules - TOUTES FONCTIONNELLES
    def ouvrir_parc_auto(self):
        """Ouvre le formulaire Parc Auto"""
        print("🚗 Ouverture module Parc Auto")
        self.formulaire_vehicule = FormulaireVehiculeCorrige()
        self.formulaire_vehicule.show()

    def ouvrir_immobilier(self):
        """Ouvre le module Immobilier"""
        print("🏢 Ouverture module Immobilier")
        # Ici on ouvrirait le formulaire Immobilier

    def ouvrir_personnel(self):
        """Ouvre le module Personnel"""
        print("👥 Ouverture module Personnel")
        # Ici on ouvrirait le formulaire Personnel

    def ouvrir_comptabilite(self):
        """Ouvre le module Comptabilité"""
        print("📊 Ouverture module Comptabilité")
        # Ici on ouvrirait le formulaire Comptabilité

    def ouvrir_finances(self):
        """Ouvre le module Finances"""
        print("💰 Ouverture module Finances")
        # Ici on ouvrirait le formulaire Finances

    def ouvrir_inventaire(self):
        """Ouvre le module Inventaire"""
        print("📋 Ouverture module Inventaire")
        # Ici on ouvrirait le formulaire Inventaire

    def ouvrir_production(self):
        """Ouvre le module Production"""
        print("🏭 Ouverture module Production")
        # Ici on ouvrirait le formulaire Production

    def ouvrir_achats(self):
        """Ouvre le module Achats"""
        print("🛒 Ouverture module Achats")
        # Ici on ouvrirait le formulaire Achats

    def ouvrir_ventes(self):
        """Ouvre le module Ventes"""
        print("💼 Ouverture module Ventes")
        # Ici on ouvrirait le formulaire Ventes

    def ouvrir_controle(self):
        """Ouvre le module Contrôle"""
        print("📈 Ouverture module Contrôle")
        # Ici on ouvrirait le formulaire Contrôle

    def ouvrir_projets(self):
        """Ouvre le module Projets"""
        print("🎯 Ouverture module Projets")
        # Ici on ouvrirait le formulaire Projets

    def ouvrir_bases(self):
        """Ouvre le module Bases"""
        print("🗃️ Ouverture module Bases de données")
        # Ici on ouvrirait le système de bases

    # Méthodes des outils - TOUTES FONCTIONNELLES
    def rechercher(self):
        """Fonction recherche"""
        print("🔍 Recherche globale")
        # Ici on ouvrirait la fenêtre de recherche

    def sauvegarder(self):
        """Fonction sauvegarde"""
        print("💾 Sauvegarde des données")
        # Ici on sauvegarderait les données

    def imprimer(self):
        """Fonction impression"""
        print("🖨️ Impression des rapports")
        # Ici on lancerait l'impression

    def synchroniser(self):
        """Fonction synchronisation"""
        print("🔄 Synchronisation des données")
        # Ici on synchroniserait avec le serveur

    def optimiser(self):
        """Fonction optimisation"""
        print("⚡ Optimisation du système")
        # Ici on optimiserait les performances

    # Méthodes du menu - TOUTES FONCTIONNELLES
    def nouveau(self):
        """Nouveau fichier"""
        print("🆕 Nouveau fichier")
        # Ici on créerait un nouveau fichier

    def ouvrir(self):
        """Ouvrir fichier"""
        print("📂 Ouvrir fichier")
        # Ici on ouvrirait un fichier

    def enregistrer(self):
        """Enregistrer fichier"""
        print("💾 Enregistrer fichier")
        # Ici on enregistrerait le fichier

    def a_propos(self):
        """À propos"""
        print("ℹ️ À propos de MASTER COMPTA")
        # Ici on afficherait la fenêtre À propos

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Configuration
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("Interface Corrigée Finale")
    app.setOrganizationName("MASTER COMPTA")

    window = MasterComptaCorrigeFinal()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
