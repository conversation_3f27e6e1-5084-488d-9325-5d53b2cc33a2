<!-- Generated by SpecStory -->

# /fix Le type du paramètre « params » est partielle (2025-06-08 14:10:16)

_**User**_

/fix Le type du paramètre « params » est partiellement inconnu
  Le type de paramètre est « dict[Unknown, Unknown] »

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Le problème vient du typage incomplet de `params: dict = None`. Il faut préciser les types génériques de dict, par exemple `Dict[str, Any]`. Voici la correction :

````python
# ...existing code...
    def execute_query(self, query_name: str, connection: Any, params: Dict[str, Any] = None) -> Optional[List[Any]]:
# ...existing code...
````

---

