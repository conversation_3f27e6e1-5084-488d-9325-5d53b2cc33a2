#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 ROBOT IA - SYSTÈME AUTHENTIFICATION ULTRA-SÉCURISÉ
Protection maximale, chiffrement militaire, accès personnel uniquement
Créé par Augment Agent
"""

import os
import sys
import hashlib
import hmac
import secrets
import json
import time
import base64
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import getpass

class UltraSecureAuthentication:
    """🔐 Système d'authentification ultra-sécurisé"""
    
    def __init__(self):
        self.security_config = {
            "max_login_attempts": 3,
            "lockout_duration": 1800,  # 30 minutes
            "session_timeout": 3600,   # 1 heure
            "password_min_length": 12,
            "require_special_chars": True,
            "require_numbers": True,
            "require_uppercase": True,
            "encryption_algorithm": "AES-256",
            "hash_algorithm": "SHA-512",
            "salt_length": 32,
            "iterations": 100000
        }
        
        self.auth_file = "config/secure_auth.enc"
        self.session_file = "config/active_session.enc"
        self.failed_attempts = {}
        self.active_sessions = {}
        
        # Clé maître pour chiffrement
        self.master_key = None
        self.encryption_key = None
        
        # Initialisation sécurité
        self._initialize_security()
    
    def _initialize_security(self):
        """Initialise le système de sécurité"""
        try:
            # Création dossier config sécurisé
            os.makedirs("config", exist_ok=True)
            
            # Génération clé maître si première utilisation
            if not os.path.exists(self.auth_file):
                print("🔐 Première utilisation - Configuration sécurité...")
                self._setup_initial_security()
            
            print("✅ Système sécurité initialisé")
            
        except Exception as e:
            print(f"❌ Erreur initialisation sécurité: {e}")
    
    def _setup_initial_security(self):
        """Configuration initiale de la sécurité"""
        try:
            print("🔐 CONFIGURATION SÉCURITÉ INITIALE")
            print("=" * 50)
            print("⚠️  CONFIGURATION AUTOMATIQUE AVEC IDENTIFIANTS PRÉDÉFINIS")
            print("🔒 Identifiants ultra-sécurisés configurés")
            print()

            # Identifiants prédéfinis sécurisés
            username = "SamNord@110577"
            password = "NorDine@22"

            print(f"👤 Nom d'utilisateur: {username}")
            print("🔑 Mot de passe: ************")
            print()

            # Validation automatique
            if not self._validate_password_strength(password):
                print("⚠️  Mot de passe ne respecte pas tous les critères mais accepté (prédéfini)")

            # Question secrète prédéfinie
            secret_question = "Quel est votre nom de famille préféré ?"
            secret_answer = "NorDine"

            print(f"❓ Question secrète: {secret_question}")
            print("💭 Réponse secrète: ********")
            print()

            # Génération clé de récupération
            recovery_key = self._generate_recovery_key()

            print("🔑 CLÉ DE RÉCUPÉRATION GÉNÉRÉE:")
            print("=" * 50)
            print(f"🔐 {recovery_key}")
            print("=" * 50)
            print("⚠️  IMPORTANT: Notez cette clé dans un endroit SÛR !")
            print("🔒 Elle permet de récupérer l'accès en cas d'oubli")
            print()

            # Sauvegarde automatique de la clé
            try:
                with open("config/recovery_key.txt", "w", encoding="utf-8") as f:
                    f.write(f"ROBOT IA AUTONOME PC - CLÉ DE RÉCUPÉRATION\n")
                    f.write(f"=========================================\n")
                    f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Utilisateur: {username}\n")
                    f.write(f"Clé: {recovery_key}\n")
                    f.write(f"=========================================\n")
                    f.write(f"GARDEZ CETTE CLÉ EN SÉCURITÉ !\n")
                print("💾 Clé de récupération sauvegardée dans config/recovery_key.txt")
            except:
                pass

            print("⏳ Configuration en cours...")

            # Création profil sécurisé
            self._create_secure_profile(username, password, secret_question, secret_answer, recovery_key)

            print("✅ Configuration sécurité terminée avec succès !")
            print("🔐 Votre Robot IA est maintenant ultra-sécurisé")
            print()
            print("🎯 IDENTIFIANTS DE CONNEXION:")
            print(f"👤 Utilisateur: {username}")
            print("🔑 Mot de passe: NorDine@22")
            print()

            return True

        except Exception as e:
            print(f"❌ Erreur configuration sécurité: {e}")
            return False
    
    def _validate_password_strength(self, password):
        """Valide la force du mot de passe"""
        if len(password) < self.security_config["password_min_length"]:
            return False
        
        if self.security_config["require_uppercase"] and not any(c.isupper() for c in password):
            return False
        
        if self.security_config["require_numbers"] and not any(c.isdigit() for c in password):
            return False
        
        if self.security_config["require_special_chars"]:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                return False
        
        return True
    
    def _display_password_requirements(self):
        """Affiche les exigences du mot de passe"""
        print("🔒 EXIGENCES MOT DE PASSE:")
        print(f"   • Minimum {self.security_config['password_min_length']} caractères")
        print("   • Au moins 1 majuscule")
        print("   • Au moins 1 chiffre")
        print("   • Au moins 1 caractère spécial (!@#$%^&*...)")
        print("   • Éviter les mots du dictionnaire")
    
    def _generate_recovery_key(self):
        """Génère une clé de récupération"""
        # Génération clé aléatoire sécurisée
        key_bytes = secrets.token_bytes(32)
        recovery_key = base64.b32encode(key_bytes).decode('utf-8')
        
        # Format lisible (groupes de 4)
        formatted_key = '-'.join([recovery_key[i:i+4] for i in range(0, len(recovery_key), 4)])
        
        return formatted_key
    
    def _create_secure_profile(self, username, password, question, answer, recovery_key):
        """Crée le profil sécurisé chiffré"""
        try:
            # Génération sel unique
            salt = secrets.token_bytes(self.security_config["salt_length"])
            
            # Hachage mot de passe avec PBKDF2
            password_hash = self._hash_password(password, salt)
            
            # Hachage réponse secrète
            answer_hash = self._hash_password(answer.lower(), salt)
            
            # Hachage clé récupération
            recovery_hash = self._hash_password(recovery_key, salt)
            
            # Profil utilisateur
            user_profile = {
                "username": username,
                "password_hash": password_hash.hex(),
                "salt": salt.hex(),
                "secret_question": question,
                "secret_answer_hash": answer_hash.hex(),
                "recovery_key_hash": recovery_hash.hex(),
                "created_at": datetime.now().isoformat(),
                "last_login": None,
                "login_attempts": 0,
                "locked_until": None,
                "security_level": "MAXIMUM"
            }
            
            # Chiffrement du profil
            encrypted_profile = self._encrypt_data(json.dumps(user_profile), password)
            
            # Sauvegarde sécurisée
            with open(self.auth_file, 'wb') as f:
                f.write(encrypted_profile)
            
            # Protection fichier (lecture seule)
            os.chmod(self.auth_file, 0o600)
            
        except Exception as e:
            print(f"❌ Erreur création profil: {e}")
            raise
    
    def _hash_password(self, password, salt):
        """Hache un mot de passe avec PBKDF2"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA512(),
            length=64,
            salt=salt,
            iterations=self.security_config["iterations"]
        )
        return kdf.derive(password.encode('utf-8'))
    
    def _encrypt_data(self, data, password):
        """Chiffre des données avec AES-256"""
        # Génération clé de chiffrement depuis mot de passe
        salt = secrets.token_bytes(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        
        # Chiffrement
        fernet = Fernet(key)
        encrypted_data = fernet.encrypt(data.encode('utf-8'))
        
        # Retour avec sel
        return salt + encrypted_data
    
    def _decrypt_data(self, encrypted_data, password):
        """Déchiffre des données"""
        try:
            # Extraction sel
            salt = encrypted_data[:16]
            encrypted_content = encrypted_data[16:]
            
            # Régénération clé
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            
            # Déchiffrement
            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(encrypted_content)
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            raise ValueError("Déchiffrement impossible - mot de passe incorrect")
    
    def authenticate_user(self):
        """Authentifie l'utilisateur"""
        try:
            print("🔐 AUTHENTIFICATION ROBOT IA AUTONOME PC")
            print("=" * 50)
            
            # Vérification fichier auth
            if not os.path.exists(self.auth_file):
                print("❌ Aucun profil configuré !")
                return False
            
            # Chargement profil
            with open(self.auth_file, 'rb') as f:
                encrypted_profile = f.read()
            
            # Tentatives de connexion
            max_attempts = self.security_config["max_login_attempts"]
            
            for attempt in range(max_attempts):
                print(f"\n🔑 Tentative {attempt + 1}/{max_attempts}")
                
                username = input("👤 Nom d'utilisateur: ").strip()
                password = getpass.getpass("🔑 Mot de passe: ")
                
                # Vérification authentification
                if self._verify_credentials(encrypted_profile, username, password):
                    print("✅ Authentification réussie !")
                    self._create_session(username)
                    return True
                else:
                    print("❌ Identifiants incorrects !")
                    if attempt < max_attempts - 1:
                        print("🔄 Veuillez réessayer...")
            
            # Échec authentification
            print("🚨 ACCÈS REFUSÉ - Trop de tentatives échouées")
            print("🔒 Système verrouillé pour sécurité")
            self._lock_system()
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur authentification: {e}")
            return False
    
    def _verify_credentials(self, encrypted_profile, username, password):
        """Vérifie les identifiants"""
        try:
            # Vérification directe des identifiants prédéfinis
            if username == "SamNord@110577" and password == "NorDine@22":
                print("✅ Identifiants maître reconnus !")
                return True

            # Méthode de vérification classique pour autres utilisateurs
            try:
                # Déchiffrement profil
                profile_data = self._decrypt_data(encrypted_profile, password)
                profile = json.loads(profile_data)

                # Vérification username
                if profile["username"] != username:
                    return False

                # Vérification mot de passe
                salt = bytes.fromhex(profile["salt"])
                stored_hash = bytes.fromhex(profile["password_hash"])
                password_hash = self._hash_password(password, salt)

                if not hmac.compare_digest(stored_hash, password_hash):
                    return False

                # Mise à jour dernière connexion
                profile["last_login"] = datetime.now().isoformat()
                profile["login_attempts"] = 0

                # Sauvegarde mise à jour
                updated_data = self._encrypt_data(json.dumps(profile), password)
                with open(self.auth_file, 'wb') as f:
                    f.write(updated_data)

                return True

            except Exception:
                # Si déchiffrement échoue, vérifier identifiants maître
                if username == "SamNord@110577" and password == "NorDine@22":
                    return True
                return False

        except Exception:
            return False
    
    def _create_session(self, username):
        """Crée une session utilisateur sécurisée"""
        try:
            session_id = secrets.token_urlsafe(32)
            session_key = secrets.token_urlsafe(32)
            
            session_data = {
                "session_id": session_id,
                "username": username,
                "created_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(seconds=self.security_config["session_timeout"])).isoformat(),
                "session_key": session_key,
                "ip_address": "127.0.0.1",  # Local
                "user_agent": "Robot IA Autonome PC"
            }
            
            # Sauvegarde session chiffrée
            session_json = json.dumps(session_data)
            encrypted_session = self._encrypt_data(session_json, session_key)
            
            with open(self.session_file, 'wb') as f:
                f.write(encrypted_session)
            
            # Session en mémoire
            self.active_sessions[session_id] = session_data
            
            print(f"🔐 Session créée: {session_id[:8]}...")
            
        except Exception as e:
            print(f"❌ Erreur création session: {e}")
    
    def verify_session(self):
        """Vérifie la session active"""
        try:
            if not os.path.exists(self.session_file):
                return False
            
            # Chargement session
            with open(self.session_file, 'rb') as f:
                encrypted_session = f.read()
            
            # Tentative déchiffrement avec clés connues
            for session_id, session_data in self.active_sessions.items():
                try:
                    session_json = self._decrypt_data(encrypted_session, session_data["session_key"])
                    session = json.loads(session_json)
                    
                    # Vérification expiration
                    expires_at = datetime.fromisoformat(session["expires_at"])
                    if datetime.now() > expires_at:
                        self._cleanup_session()
                        return False
                    
                    return True
                    
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur vérification session: {e}")
            return False
    
    def _cleanup_session(self):
        """Nettoie les sessions expirées"""
        try:
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
            
            self.active_sessions.clear()
            
        except Exception as e:
            print(f"❌ Erreur nettoyage session: {e}")
    
    def _lock_system(self):
        """Verrouille le système temporairement"""
        try:
            lockout_until = datetime.now() + timedelta(seconds=self.security_config["lockout_duration"])
            
            lock_data = {
                "locked_at": datetime.now().isoformat(),
                "locked_until": lockout_until.isoformat(),
                "reason": "Too many failed login attempts"
            }
            
            with open("config/system_lock.json", 'w') as f:
                json.dump(lock_data, f)
            
            print(f"🔒 Système verrouillé jusqu'à {lockout_until.strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ Erreur verrouillage: {e}")
    
    def change_password(self):
        """Change le mot de passe maître"""
        try:
            print("🔐 CHANGEMENT MOT DE PASSE MAÎTRE")
            print("=" * 40)
            
            # Authentification actuelle
            current_password = getpass.getpass("🔑 Mot de passe actuel: ")
            
            # Vérification
            with open(self.auth_file, 'rb') as f:
                encrypted_profile = f.read()
            
            try:
                profile_data = self._decrypt_data(encrypted_profile, current_password)
                profile = json.loads(profile_data)
            except:
                print("❌ Mot de passe actuel incorrect !")
                return False
            
            # Nouveau mot de passe
            while True:
                new_password = getpass.getpass("🔑 Nouveau mot de passe: ")
                if self._validate_password_strength(new_password):
                    break
                print("❌ Nouveau mot de passe trop faible !")
                self._display_password_requirements()
            
            # Confirmation
            confirm_password = getpass.getpass("🔑 Confirmez nouveau mot de passe: ")
            if new_password != confirm_password:
                print("❌ Les mots de passe ne correspondent pas !")
                return False
            
            # Mise à jour profil
            salt = secrets.token_bytes(self.security_config["salt_length"])
            new_password_hash = self._hash_password(new_password, salt)
            
            profile["password_hash"] = new_password_hash.hex()
            profile["salt"] = salt.hex()
            profile["password_changed_at"] = datetime.now().isoformat()
            
            # Sauvegarde avec nouveau mot de passe
            updated_data = self._encrypt_data(json.dumps(profile), new_password)
            with open(self.auth_file, 'wb') as f:
                f.write(updated_data)
            
            print("✅ Mot de passe changé avec succès !")
            return True
            
        except Exception as e:
            print(f"❌ Erreur changement mot de passe: {e}")
            return False
    
    def recovery_access(self):
        """Récupération d'accès avec clé de récupération"""
        try:
            print("🔐 RÉCUPÉRATION D'ACCÈS")
            print("=" * 30)
            
            recovery_key = input("🔑 Clé de récupération: ").strip()
            
            # Chargement profil
            with open(self.auth_file, 'rb') as f:
                encrypted_profile = f.read()
            
            # Tentative avec clé récupération
            # Note: En réalité, il faudrait une méthode plus complexe
            # pour récupérer sans le mot de passe original
            
            print("🔄 Vérification clé de récupération...")
            print("⚠️  Fonctionnalité de récupération en développement")
            print("💡 Contactez l'administrateur système")
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur récupération: {e}")
            return False
    
    def get_security_status(self):
        """Retourne le statut de sécurité"""
        try:
            status = {
                "authentication_enabled": os.path.exists(self.auth_file),
                "session_active": self.verify_session(),
                "security_level": "MAXIMUM",
                "encryption": "AES-256",
                "hash_algorithm": "SHA-512 + PBKDF2",
                "last_check": datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            print(f"❌ Erreur statut sécurité: {e}")
            return {}

def main():
    """Fonction principale de test"""
    print("🔐 SYSTÈME AUTHENTIFICATION ULTRA-SÉCURISÉ")
    print("=" * 50)
    
    auth_system = UltraSecureAuthentication()
    
    # Test authentification
    if auth_system.authenticate_user():
        print("✅ Accès autorisé au Robot IA !")
        
        # Vérification session
        if auth_system.verify_session():
            print("🔐 Session active et valide")
        
        # Statut sécurité
        status = auth_system.get_security_status()
        print(f"🛡️ Niveau sécurité: {status.get('security_level', 'Unknown')}")
        
    else:
        print("❌ Accès refusé !")

if __name__ == "__main__":
    main()
