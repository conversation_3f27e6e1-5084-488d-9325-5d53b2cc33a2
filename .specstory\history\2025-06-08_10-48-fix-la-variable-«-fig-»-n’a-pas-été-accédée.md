<!-- Generated by SpecStory -->

# /fix La variable « fig » n’a pas été accédée (2025-06-08 11:48:45)

_**User**_

/fix La variable « fig » n’a pas été accédée

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Le problème est que la variable `fig` est déclarée mais jamais utilisée. Pour corriger cela, il suffit de supprimer l'assignation à `fig` et d'appeler directement `plt.figure(...)` sans l'affecter à une variable, car la suite du code utilise uniquement l'API pyplot.

Voici la correction à appliquer pour les deux occurrences concernées :

````python
# ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

---

