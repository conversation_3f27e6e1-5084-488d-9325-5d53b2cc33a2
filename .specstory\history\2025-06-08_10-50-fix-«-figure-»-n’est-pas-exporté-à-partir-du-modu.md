<!-- Generated by SpecStory -->

# /fix « Figure » n’est pas exporté à partir du modu (2025-06-08 11:50:30)

_**User**_

/fix « Figure » n’est pas exporté à partir du module « matplotlib.pyplot »
  Importer à partir de « matplotlib.figure » à la place

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Corriger l'import de `Figure` : importer depuis `matplotlib.figure` au lieu de `matplotlib.pyplot`.

````python
# ...existing code...
            import matplotlib.pyplot as plt
            from matplotlib.figure import Figure  # Correction de l'import pour Figure
            from collections import defaultdict
# ...existing code...
````

---

