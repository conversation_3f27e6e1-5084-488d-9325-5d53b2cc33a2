@echo off
title LANCER GESTIMMOB MAINTENANT - Demarrage Immediat
color 0B

echo.
echo ========================================================================
echo                      LANCER GESTIMMOB MAINTENANT
echo                        Demarrage immediat du logiciel
echo ========================================================================
echo.

echo [%TIME%] Demarrage immediat de GestImmob...

REM Variables
set "PYTHON_FOUND=0"
set "PYTHON_CMD="
set "MAIN_FILE="

echo.
echo 🔍 DETECTION RAPIDE PYTHON...

REM Test rapide des commandes Python
py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    set "PYTHON_FOUND=1"
    echo ✅ Python detecte: py
    goto :find_main_file
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    set "PYTHON_FOUND=1"
    echo ✅ Python detecte: python
    goto :find_main_file
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    set "PYTHON_FOUND=1"
    echo ✅ Python detecte: python3
    goto :find_main_file
)

echo ❌ Python non detecte
echo.
echo 🚨 PROBLEME PYTHON DETECTE !
echo.
echo Solutions rapides:
echo 1. Executez SOLUTION_PYTHON_FINALE.bat
echo 2. Ou installez Python depuis https://python.org
echo.
set /p fix_python="Lancer la solution Python automatique ? (o/n): "
if /i "%fix_python%"=="o" (
    if exist "SOLUTION_PYTHON_FINALE.bat" (
        echo Lancement de la solution Python...
        call SOLUTION_PYTHON_FINALE.bat
        echo.
        echo Re-test Python...
        py --version >nul 2>&1
        if not errorlevel 1 (
            set "PYTHON_CMD=py"
            set "PYTHON_FOUND=1"
            echo ✅ Python maintenant disponible !
            goto :find_main_file
        )
    ) else (
        echo ❌ Solution Python non trouvee
    )
)

if %PYTHON_FOUND%==0 (
    echo.
    echo ❌ Impossible de continuer sans Python
    goto :end
)

:find_main_file
echo.
echo 📁 RECHERCHE FICHIER PRINCIPAL...

REM Recherche du fichier principal par ordre de priorité
if exist "src\main.py" (
    set "MAIN_FILE=src\main.py"
    echo ✅ Fichier trouve: src\main.py
    goto :launch_now
)

if exist "gestimmob_final.py" (
    set "MAIN_FILE=gestimmob_final.py"
    echo ✅ Fichier trouve: gestimmob_final.py
    goto :launch_now
)

if exist "gestimmob_complet_final.py" (
    set "MAIN_FILE=gestimmob_complet_final.py"
    echo ✅ Fichier trouve: gestimmob_complet_final.py
    goto :launch_now
)

if exist "LANCER_GESTIMMOB_FINAL_V5.py" (
    set "MAIN_FILE=LANCER_GESTIMMOB_FINAL_V5.py"
    echo ✅ Fichier trouve: LANCER_GESTIMMOB_FINAL_V5.py
    goto :launch_now
)

if exist "gestimmob_enterprise_v5.py" (
    set "MAIN_FILE=gestimmob_enterprise_v5.py"
    echo ✅ Fichier trouve: gestimmob_enterprise_v5.py
    goto :launch_now
)

if exist "gestimmob_simple.py" (
    set "MAIN_FILE=gestimmob_simple.py"
    echo ✅ Fichier trouve: gestimmob_simple.py
    goto :launch_now
)

echo ❌ Aucun fichier principal trouve
echo.
echo 📋 Fichiers recherches:
echo   - src\main.py
echo   - gestimmob_final.py
echo   - gestimmob_complet_final.py
echo   - LANCER_GESTIMMOB_FINAL_V5.py
echo   - gestimmob_enterprise_v5.py
echo   - gestimmob_simple.py
echo.
echo 💡 Verifiez que les fichiers GestImmob sont presents
goto :end

:launch_now
echo.
echo 🚀 LANCEMENT IMMEDIAT...
echo.
echo Configuration:
echo   Python: %PYTHON_CMD%
echo   Fichier: %MAIN_FILE%
echo.

REM Créer les répertoires de base rapidement
if not exist "data" mkdir data >nul 2>&1
if not exist "logs" mkdir logs >nul 2>&1

REM Installation rapide des dépendances critiques
echo 📦 Verification dependances critiques...

%PYTHON_CMD% -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo Installation PySide6...
    %PYTHON_CMD% -m pip install PySide6 --quiet --no-warn-script-location >nul 2>&1
    if not errorlevel 1 (
        echo ✅ PySide6 installe
    ) else (
        echo ⚠️ Probleme PySide6 - on continue...
    )
) else (
    echo ✅ PySide6 OK
)

echo.
echo ========================================================================
echo                           🚀 LANCEMENT GESTIMMOB 🚀
echo ========================================================================
echo.

echo [%TIME%] Demarrage de GestImmob...
echo.

REM Lancement direct de l'application
echo Lancement en cours...
%PYTHON_CMD% %MAIN_FILE%

set "EXIT_CODE=%ERRORLEVEL%"

echo.
echo ========================================================================
echo                              RESULTAT
echo ========================================================================
echo.

if %EXIT_CODE%==0 (
    echo [%TIME%] ✅ GESTIMMOB FERME NORMALEMENT
    echo.
    echo 🎉 GestImmob a fonctionne correctement !
    echo ✅ Lancement reussi avec %PYTHON_CMD% et %MAIN_FILE%
    echo.
    echo 💡 Pour relancer GestImmob, executez a nouveau ce script
) else (
    echo [%TIME%] ❌ GESTIMMOB FERME AVEC ERREUR (Code: %EXIT_CODE%)
    echo.
    echo 🔧 DIAGNOSTIC RAPIDE:
    
    REM Tests rapides
    %PYTHON_CMD% -c "print('✅ Python de base: OK')" 2>nul || echo ❌ Python de base: Probleme
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul || echo ❌ PySide6: Probleme
    
    echo.
    echo 💡 SOLUTIONS:
    echo 1. Relancez ce script
    echo 2. Executez SOLUTION_PYTHON_FINALE.bat
    echo 3. Verifiez les erreurs affichees ci-dessus
    echo 4. Installez manuellement: pip install PySide6 SQLAlchemy
)

:end
echo.
echo [%TIME%] Script termine.
echo.

REM Proposer de relancer
if %EXIT_CODE% NEQ 0 (
    set /p relancer="Relancer GestImmob ? (o/n): "
    if /i "%relancer%"=="o" (
        echo.
        echo Relancement...
        goto :launch_now
    )
)

echo Appuyez sur une touche pour fermer...
pause >nul
