<!-- Generated by SpecStory -->

# /fix Le type « Column[int] » n’est pas assignable  (2025-06-08 10:59:56)

_**User**_

/fix Le type « Column[int] » n’est pas assignable au type déclaré « int »
  « Column[int] » n’est pas assignable à « int »

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Le problème vient de l'utilisation simultanée d'annotations de type Python (`id: int`) et de la déclaration SQLAlchemy (`id = Column(Integer, ...)`). Pour les modèles SQLAlchemy, il ne faut pas annoter les attributs de colonne avec des types primitifs Python, car cela crée un conflit de typage.

**Solution :**
- Supprimez les annotations de type Python (`id: int`, `username: str`, etc.) dans la classe `User` pour les champs mappés à des colonnes SQLAlchemy.

````python
# ...existing code...

class User(Base):
    __tablename__ = "users"
    # Suppression des annotations de type ici
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    role = Column(String, default="user")

# ...existing code...
````

---

