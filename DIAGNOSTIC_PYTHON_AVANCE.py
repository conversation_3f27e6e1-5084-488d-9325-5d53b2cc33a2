#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC AVANCÉ DES PROBLÈMES PYTHON
Identifie précisément tous les problèmes Python
"""

import os
import sys
import subprocess
import platform
import json
import winreg
from pathlib import Path
from typing import Dict, List, Any

class DiagnosticPythonAvance:
    def __init__(self):
        self.systeme = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.rapport_diagnostic = {
            'systeme': self.systeme,
            'architecture': self.architecture,
            'problemes': [],
            'installations': [],
            'variables_env': {},
            'permissions': {},
            'recommandations': []
        }
        
    def executer_diagnostic_complet(self):
        """Exécute un diagnostic complet et détaillé"""
        print("🔍 DIAGNOSTIC AVANCÉ DES PROBLÈMES PYTHON")
        print("=" * 70)
        
        # 1. Informations système
        self._diagnostiquer_systeme()
        
        # 2. Installations Python
        self._diagnostiquer_installations()
        
        # 3. Variables d'environnement
        self._diagnostiquer_variables_environnement()
        
        # 4. Permissions
        self._diagnostiquer_permissions()
        
        # 5. Conflits
        self._diagnostiquer_conflits()
        
        # 6. Dépendances
        self._diagnostiquer_dependances()
        
        # 7. Génération du rapport
        self._generer_rapport_detaille()
        
        return self.rapport_diagnostic
    
    def _diagnostiquer_systeme(self):
        """Diagnostique les informations système"""
        print("\n🖥️ DIAGNOSTIC SYSTÈME")
        print("-" * 40)
        
        info_systeme = {
            'os': platform.system(),
            'version': platform.version(),
            'architecture': platform.machine(),
            'processeur': platform.processor(),
            'python_version_systeme': sys.version if 'python' in sys.executable.lower() else None
        }
        
        self.rapport_diagnostic['systeme_info'] = info_systeme
        
        print(f"OS: {info_systeme['os']}")
        print(f"Version: {info_systeme['version']}")
        print(f"Architecture: {info_systeme['architecture']}")
        
        # Vérifications spécifiques Windows
        if self.systeme == 'windows':
            self._diagnostiquer_windows_specifique()
    
    def _diagnostiquer_windows_specifique(self):
        """Diagnostic spécifique à Windows"""
        try:
            # Vérifier UAC
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            self.rapport_diagnostic['windows'] = {
                'admin_privileges': is_admin,
                'uac_enabled': True  # Supposé activé par défaut
            }
            
            print(f"Privilèges admin: {'✅ Oui' if is_admin else '❌ Non'}")
            
        except Exception as e:
            print(f"⚠️ Erreur diagnostic Windows: {e}")
    
    def _diagnostiquer_installations(self):
        """Diagnostique toutes les installations Python"""
        print("\n🐍 DIAGNOSTIC INSTALLATIONS PYTHON")
        print("-" * 40)
        
        installations = []
        
        # Test des commandes Python
        commandes_test = ['python', 'python3', 'py', 'python.exe']
        
        for cmd in commandes_test:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    # Obtenir le chemin complet
                    path_result = subprocess.run([cmd, '-c', 'import sys; print(sys.executable)'], 
                                                capture_output=True, text=True, timeout=10)
                    
                    installation = {
                        'commande': cmd,
                        'version': result.stdout.strip(),
                        'chemin': path_result.stdout.strip() if path_result.returncode == 0 else 'Inconnu',
                        'fonctionnel': True
                    }
                    
                    installations.append(installation)
                    print(f"✅ {cmd}: {installation['version']} ({installation['chemin']})")
                    
            except Exception as e:
                print(f"❌ {cmd}: Non disponible ({e})")
        
        # Recherche dans le système de fichiers
        installations_fs = self._rechercher_installations_filesystem()
        
        # Fusionner les résultats
        for install_fs in installations_fs:
            if not any(install['chemin'] == install_fs['chemin'] for install in installations):
                installations.append(install_fs)
                print(f"📁 Trouvé: {install_fs['chemin']} (non dans PATH)")
        
        self.rapport_diagnostic['installations'] = installations
        
        if not installations:
            self.rapport_diagnostic['problemes'].append({
                'type': 'aucune_installation',
                'severite': 'critique',
                'description': 'Aucune installation Python détectée'
            })
            print("❌ Aucune installation Python détectée")
        elif len(installations) > 3:
            self.rapport_diagnostic['problemes'].append({
                'type': 'trop_installations',
                'severite': 'moyen',
                'description': f'{len(installations)} installations détectées (conflits possibles)'
            })
            print(f"⚠️ {len(installations)} installations détectées (conflits possibles)")
    
    def _rechercher_installations_filesystem(self):
        """Recherche les installations Python dans le système de fichiers"""
        installations = []
        
        if self.systeme == 'windows':
            chemins_recherche = [
                r"C:\Python*",
                r"C:\Program Files\Python*",
                r"C:\Program Files (x86)\Python*",
                r"C:\Users\<USER>\AppData\Local\Programs\Python*",
                r"C:\Users\<USER>\AppData\Roaming\Python*"
            ]
            
            for pattern in chemins_recherche:
                try:
                    for chemin in Path('C:\\').glob(pattern.replace('C:\\', '')):
                        if chemin.is_dir():
                            python_exe = chemin / 'python.exe'
                            if python_exe.exists():
                                try:
                                    result = subprocess.run([str(python_exe), '--version'], 
                                                          capture_output=True, text=True, timeout=10)
                                    if result.returncode == 0:
                                        installations.append({
                                            'commande': str(python_exe),
                                            'version': result.stdout.strip(),
                                            'chemin': str(python_exe),
                                            'fonctionnel': True,
                                            'dans_path': False
                                        })
                                except:
                                    installations.append({
                                        'commande': str(python_exe),
                                        'version': 'Inconnue',
                                        'chemin': str(python_exe),
                                        'fonctionnel': False,
                                        'dans_path': False
                                    })
                except Exception:
                    continue
        
        return installations
    
    def _diagnostiquer_variables_environnement(self):
        """Diagnostique les variables d'environnement"""
        print("\n🌍 DIAGNOSTIC VARIABLES D'ENVIRONNEMENT")
        print("-" * 40)
        
        variables_importantes = ['PATH', 'PYTHONPATH', 'PYTHONHOME', 'VIRTUAL_ENV']
        
        for var in variables_importantes:
            valeur = os.environ.get(var)
            self.rapport_diagnostic['variables_env'][var] = valeur
            
            if var == 'PATH':
                self._analyser_path(valeur)
            elif var == 'PYTHONPATH' and valeur:
                print(f"PYTHONPATH: {valeur}")
                # Vérifier si les chemins existent
                for chemin in valeur.split(os.pathsep):
                    if chemin and not os.path.exists(chemin):
                        self.rapport_diagnostic['problemes'].append({
                            'type': 'pythonpath_invalide',
                            'severite': 'moyen',
                            'description': f'Chemin PYTHONPATH inexistant: {chemin}'
                        })
            elif var == 'PYTHONHOME' and valeur:
                print(f"PYTHONHOME: {valeur}")
                if not os.path.exists(valeur):
                    self.rapport_diagnostic['problemes'].append({
                        'type': 'pythonhome_invalide',
                        'severite': 'moyen',
                        'description': f'PYTHONHOME pointe vers un répertoire inexistant: {valeur}'
                    })
    
    def _analyser_path(self, path_value):
        """Analyse la variable PATH"""
        if not path_value:
            self.rapport_diagnostic['problemes'].append({
                'type': 'path_vide',
                'severite': 'critique',
                'description': 'Variable PATH vide'
            })
            return
        
        chemins_python = []
        chemins_invalides = []
        
        for chemin in path_value.split(os.pathsep):
            if chemin and 'python' in chemin.lower():
                chemins_python.append(chemin)
                if not os.path.exists(chemin):
                    chemins_invalides.append(chemin)
        
        print(f"Chemins Python dans PATH: {len(chemins_python)}")
        for chemin in chemins_python:
            status = "✅" if os.path.exists(chemin) else "❌"
            print(f"  {status} {chemin}")
        
        if chemins_invalides:
            self.rapport_diagnostic['problemes'].append({
                'type': 'path_chemins_invalides',
                'severite': 'moyen',
                'description': f'{len(chemins_invalides)} chemin(s) Python invalide(s) dans PATH'
            })
        
        if not chemins_python:
            self.rapport_diagnostic['problemes'].append({
                'type': 'python_pas_dans_path',
                'severite': 'critique',
                'description': 'Aucun chemin Python dans PATH'
            })
    
    def _diagnostiquer_permissions(self):
        """Diagnostique les permissions"""
        print("\n🔐 DIAGNOSTIC PERMISSIONS")
        print("-" * 40)
        
        tests_permissions = []
        
        # Test écriture répertoire courant
        try:
            test_file = Path('test_permissions.tmp')
            test_file.write_text('test')
            test_file.unlink()
            tests_permissions.append(('Écriture répertoire courant', True))
            print("✅ Écriture répertoire courant: OK")
        except Exception as e:
            tests_permissions.append(('Écriture répertoire courant', False))
            print(f"❌ Écriture répertoire courant: {e}")
            self.rapport_diagnostic['problemes'].append({
                'type': 'permissions_ecriture',
                'severite': 'critique',
                'description': 'Impossible d\'écrire dans le répertoire courant'
            })
        
        # Test pip (si Python disponible)
        if self.rapport_diagnostic['installations']:
            try:
                python_cmd = self.rapport_diagnostic['installations'][0]['commande']
                result = subprocess.run([python_cmd, '-m', 'pip', 'list'], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    tests_permissions.append(('Utilisation pip', True))
                    print("✅ Utilisation pip: OK")
                else:
                    tests_permissions.append(('Utilisation pip', False))
                    print(f"❌ Utilisation pip: Échec")
                    self.rapport_diagnostic['problemes'].append({
                        'type': 'permissions_pip',
                        'severite': 'moyen',
                        'description': 'Impossible d\'utiliser pip'
                    })
            except Exception as e:
                tests_permissions.append(('Utilisation pip', False))
                print(f"❌ Utilisation pip: {e}")
        
        self.rapport_diagnostic['permissions'] = dict(tests_permissions)
    
    def _diagnostiquer_conflits(self):
        """Diagnostique les conflits"""
        print("\n🔄 DIAGNOSTIC CONFLITS")
        print("-" * 40)
        
        installations = self.rapport_diagnostic['installations']
        
        if len(installations) > 1:
            print(f"⚠️ {len(installations)} installations détectées")
            
            # Analyser les versions
            versions = [install.get('version', '') for install in installations]
            versions_uniques = set(versions)
            
            if len(versions_uniques) > 1:
                self.rapport_diagnostic['problemes'].append({
                    'type': 'versions_multiples',
                    'severite': 'moyen',
                    'description': f'Versions Python multiples: {", ".join(versions_uniques)}'
                })
                print(f"⚠️ Versions multiples: {', '.join(versions_uniques)}")
            
            # Analyser les chemins
            chemins = [install.get('chemin', '') for install in installations]
            for i, chemin1 in enumerate(chemins):
                for j, chemin2 in enumerate(chemins[i+1:], i+1):
                    if chemin1 and chemin2 and Path(chemin1).parent == Path(chemin2).parent:
                        self.rapport_diagnostic['problemes'].append({
                            'type': 'installations_meme_repertoire',
                            'severite': 'faible',
                            'description': f'Installations dans le même répertoire: {Path(chemin1).parent}'
                        })
        else:
            print("✅ Pas de conflits détectés")
    
    def _diagnostiquer_dependances(self):
        """Diagnostique les dépendances"""
        print("\n📦 DIAGNOSTIC DÉPENDANCES")
        print("-" * 40)
        
        if not self.rapport_diagnostic['installations']:
            print("❌ Impossible de tester les dépendances (Python non disponible)")
            return
        
        python_cmd = self.rapport_diagnostic['installations'][0]['commande']
        
        # Test pip
        try:
            result = subprocess.run([python_cmd, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ pip: {result.stdout.strip()}")
            else:
                print("❌ pip: Non disponible")
                self.rapport_diagnostic['problemes'].append({
                    'type': 'pip_manquant',
                    'severite': 'critique',
                    'description': 'pip non disponible'
                })
        except Exception as e:
            print(f"❌ pip: Erreur ({e})")
        
        # Test modules standards
        modules_test = ['os', 'sys', 'json', 'pathlib', 'urllib', 'subprocess']
        modules_ok = 0
        
        for module in modules_test:
            try:
                result = subprocess.run([python_cmd, '-c', f'import {module}'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    modules_ok += 1
                else:
                    print(f"❌ Module {module}: Import échoué")
            except:
                print(f"❌ Module {module}: Erreur test")
        
        print(f"✅ Modules standards: {modules_ok}/{len(modules_test)} OK")
        
        if modules_ok < len(modules_test):
            self.rapport_diagnostic['problemes'].append({
                'type': 'modules_standards_manquants',
                'severite': 'moyen',
                'description': f'Certains modules standards ne s\'importent pas ({modules_ok}/{len(modules_test)})'
            })
    
    def _generer_rapport_detaille(self):
        """Génère un rapport détaillé"""
        print("\n📊 RAPPORT DÉTAILLÉ")
        print("=" * 70)
        
        # Résumé des problèmes
        problemes = self.rapport_diagnostic['problemes']
        critiques = [p for p in problemes if p['severite'] == 'critique']
        moyens = [p for p in problemes if p['severite'] == 'moyen']
        faibles = [p for p in problemes if p['severite'] == 'faible']
        
        print(f"🚨 Problèmes critiques: {len(critiques)}")
        print(f"⚠️ Problèmes moyens: {len(moyens)}")
        print(f"💡 Problèmes mineurs: {len(faibles)}")
        
        # Détail des problèmes critiques
        if critiques:
            print(f"\n🚨 PROBLÈMES CRITIQUES:")
            for probleme in critiques:
                print(f"  - {probleme['description']}")
        
        # Recommandations
        self._generer_recommandations()
        
        if self.rapport_diagnostic['recommandations']:
            print(f"\n💡 RECOMMANDATIONS:")
            for i, rec in enumerate(self.rapport_diagnostic['recommandations'], 1):
                print(f"  {i}. {rec}")
        
        # Sauvegarder le rapport
        self._sauvegarder_rapport()
    
    def _generer_recommandations(self):
        """Génère des recommandations basées sur les problèmes"""
        recommandations = []
        
        problemes = self.rapport_diagnostic['problemes']
        
        # Recommandations basées sur les types de problèmes
        types_problemes = [p['type'] for p in problemes]
        
        if 'aucune_installation' in types_problemes:
            recommandations.append("Installer Python depuis https://www.python.org/downloads/")
            recommandations.append("Cocher 'Add Python to PATH' lors de l'installation")
        
        if 'python_pas_dans_path' in types_problemes:
            recommandations.append("Ajouter Python au PATH système")
            recommandations.append("Redémarrer l'ordinateur après modification du PATH")
        
        if 'pip_manquant' in types_problemes:
            recommandations.append("Installer pip avec: python -m ensurepip --upgrade")
        
        if 'trop_installations' in types_problemes:
            recommandations.append("Désinstaller les anciennes versions de Python")
            recommandations.append("Garder seulement la version la plus récente")
        
        if 'permissions_ecriture' in types_problemes:
            recommandations.append("Exécuter en tant qu'administrateur")
            recommandations.append("Vérifier les permissions du répertoire")
        
        self.rapport_diagnostic['recommandations'] = recommandations
    
    def _sauvegarder_rapport(self):
        """Sauvegarde le rapport en JSON"""
        try:
            rapport_file = Path('diagnostic_python_rapport.json')
            with open(rapport_file, 'w', encoding='utf-8') as f:
                json.dump(self.rapport_diagnostic, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Rapport sauvegardé: {rapport_file}")
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde rapport: {e}")

def main():
    """Fonction principale de diagnostic"""
    diagnostic = DiagnosticPythonAvance()
    rapport = diagnostic.executer_diagnostic_complet()
    
    # Résumé final
    problemes_critiques = len([p for p in rapport['problemes'] if p['severite'] == 'critique'])
    
    print(f"\n{'='*70}")
    if problemes_critiques == 0:
        print("🎉 DIAGNOSTIC TERMINÉ - PYTHON FONCTIONNEL")
    else:
        print(f"⚠️ DIAGNOSTIC TERMINÉ - {problemes_critiques} PROBLÈME(S) CRITIQUE(S)")
    print(f"{'='*70}")
    
    return rapport

if __name__ == "__main__":
    main()
