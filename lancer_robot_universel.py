#!/usr/bin/env python3
import sys
import os
import platform

def main():
    print("🌍 ROBOT IA UNIVERSEL - LANCEMENT")
    print(f"💻 Système: {platform.system()}")
    print(f"👤 Utilisateur: SamNord@110577")
    
    try:
        from robot_ia_universel import UniversalRobotIA
        robot = UniversalRobotIA()
        robot.run_universal()
    except ImportError:
        print("❌ Module principal non trouvé")
        print("📁 Vérifiez l'installation")

if __name__ == "__main__":
    main()
