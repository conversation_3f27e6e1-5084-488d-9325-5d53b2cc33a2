"""
Module ERP/Comptabilité pour GestImmob
Gestion complète des aspects financiers et comptables
"""

import sqlite3
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from decimal import Decimal
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, 
    QTableWidgetItem, QPushButton, QLineEdit, QLabel, QComboBox,
    QDateEdit, QTextEdit, QMessageBox, QFormLayout, QDialog,
    QDialogButtonBox, QSpinBox, QDoubleSpinBox, QGroupBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont

@dataclass
class Facture:
    """Classe représentant une facture"""
    id: Optional[int] = None
    numero: str = ""
    date_emission: date = None
    date_echeance: date = None
    client_id: int = 0
    montant_ht: Decimal = Decimal('0.00')
    tva: Decimal = Decimal('0.00')
    montant_ttc: Decimal = Decimal('0.00')
    statut: str = "brouillon"  # brouillon, envoyee, payee, annulee
    description: str = ""
    
@dataclass
class Client:
    """Classe représentant un client"""
    id: Optional[int] = None
    nom: str = ""
    prenom: str = ""
    entreprise: str = ""
    email: str = ""
    telephone: str = ""
    adresse: str = ""
    code_postal: str = ""
    ville: str = ""
    pays: str = "France"
    
@dataclass
class Fournisseur:
    """Classe représentant un fournisseur"""
    id: Optional[int] = None
    nom: str = ""
    entreprise: str = ""
    email: str = ""
    telephone: str = ""
    adresse: str = ""
    code_postal: str = ""
    ville: str = ""
    pays: str = "France"
    siret: str = ""
    
@dataclass
class BonCommande:
    """Classe représentant un bon de commande"""
    id: Optional[int] = None
    numero: str = ""
    date_creation: date = None
    fournisseur_id: int = 0
    montant_ht: Decimal = Decimal('0.00')
    tva: Decimal = Decimal('0.00')
    montant_ttc: Decimal = Decimal('0.00')
    statut: str = "en_attente"  # en_attente, valide, livre, facture
    description: str = ""

class ERPDatabase:
    """Gestionnaire de base de données pour le module ERP"""
    
    def __init__(self, db_path: str = "erp.db"):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """Initialise les tables de la base de données ERP"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT,
                entreprise TEXT,
                email TEXT,
                telephone TEXT,
                adresse TEXT,
                code_postal TEXT,
                ville TEXT,
                pays TEXT DEFAULT 'France',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table fournisseurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                entreprise TEXT,
                email TEXT,
                telephone TEXT,
                adresse TEXT,
                code_postal TEXT,
                ville TEXT,
                pays TEXT DEFAULT 'France',
                siret TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table factures
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE NOT NULL,
                date_emission DATE NOT NULL,
                date_echeance DATE,
                client_id INTEGER,
                montant_ht DECIMAL(10,2),
                tva DECIMAL(10,2),
                montant_ttc DECIMAL(10,2),
                statut TEXT DEFAULT 'brouillon',
                description TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')
        
        # Table bons de commande
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bons_commande (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE NOT NULL,
                date_creation DATE NOT NULL,
                fournisseur_id INTEGER,
                montant_ht DECIMAL(10,2),
                tva DECIMAL(10,2),
                montant_ttc DECIMAL(10,2),
                statut TEXT DEFAULT 'en_attente',
                description TEXT,
                date_creation_record TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
            )
        ''')
        
        # Table lignes de facture
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lignes_facture (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                facture_id INTEGER,
                designation TEXT NOT NULL,
                quantite DECIMAL(10,2),
                prix_unitaire DECIMAL(10,2),
                montant DECIMAL(10,2),
                FOREIGN KEY (facture_id) REFERENCES factures (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def ajouter_client(self, client: Client) -> int:
        """Ajoute un client à la base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO clients (nom, prenom, entreprise, email, telephone, 
                               adresse, code_postal, ville, pays)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (client.nom, client.prenom, client.entreprise, client.email,
              client.telephone, client.adresse, client.code_postal,
              client.ville, client.pays))
        
        client_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return client_id
        
    def lister_clients(self) -> List[Client]:
        """Retourne la liste de tous les clients"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM clients ORDER BY nom, prenom')
        rows = cursor.fetchall()
        
        clients = []
        for row in rows:
            client = Client(
                id=row[0], nom=row[1], prenom=row[2], entreprise=row[3],
                email=row[4], telephone=row[5], adresse=row[6],
                code_postal=row[7], ville=row[8], pays=row[9]
            )
            clients.append(client)
            
        conn.close()
        return clients
        
    def ajouter_facture(self, facture: Facture) -> int:
        """Ajoute une facture à la base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO factures (numero, date_emission, date_echeance, 
                                client_id, montant_ht, tva, montant_ttc, 
                                statut, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (facture.numero, facture.date_emission, facture.date_echeance,
              facture.client_id, float(facture.montant_ht), float(facture.tva),
              float(facture.montant_ttc), facture.statut, facture.description))
        
        facture_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return facture_id

class ClientDialog(QDialog):
    """Dialog pour ajouter/modifier un client"""
    
    def __init__(self, client: Optional[Client] = None, parent=None):
        super().__init__(parent)
        self.client = client or Client()
        self.setWindowTitle("Client" if client else "Nouveau Client")
        self.setModal(True)
        self.resize(400, 500)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Formulaire
        form_layout = QFormLayout()
        
        self.nom_edit = QLineEdit(self.client.nom)
        self.prenom_edit = QLineEdit(self.client.prenom)
        self.entreprise_edit = QLineEdit(self.client.entreprise)
        self.email_edit = QLineEdit(self.client.email)
        self.telephone_edit = QLineEdit(self.client.telephone)
        self.adresse_edit = QTextEdit(self.client.adresse)
        self.adresse_edit.setMaximumHeight(80)
        self.code_postal_edit = QLineEdit(self.client.code_postal)
        self.ville_edit = QLineEdit(self.client.ville)
        self.pays_edit = QLineEdit(self.client.pays)
        
        form_layout.addRow("Nom *:", self.nom_edit)
        form_layout.addRow("Prénom:", self.prenom_edit)
        form_layout.addRow("Entreprise:", self.entreprise_edit)
        form_layout.addRow("Email:", self.email_edit)
        form_layout.addRow("Téléphone:", self.telephone_edit)
        form_layout.addRow("Adresse:", self.adresse_edit)
        form_layout.addRow("Code postal:", self.code_postal_edit)
        form_layout.addRow("Ville:", self.ville_edit)
        form_layout.addRow("Pays:", self.pays_edit)
        
        layout.addLayout(form_layout)
        
        # Boutons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_client(self) -> Client:
        """Retourne le client avec les données du formulaire"""
        self.client.nom = self.nom_edit.text().strip()
        self.client.prenom = self.prenom_edit.text().strip()
        self.client.entreprise = self.entreprise_edit.text().strip()
        self.client.email = self.email_edit.text().strip()
        self.client.telephone = self.telephone_edit.text().strip()
        self.client.adresse = self.adresse_edit.toPlainText().strip()
        self.client.code_postal = self.code_postal_edit.text().strip()
        self.client.ville = self.ville_edit.text().strip()
        self.client.pays = self.pays_edit.text().strip()
        return self.client

class ERPWidget(QWidget):
    """Widget principal du module ERP"""
    
    def __init__(self):
        super().__init__()
        self.db = ERPDatabase()
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Titre
        title = QLabel("Module ERP/Comptabilité")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Onglets
        self.tabs = QTabWidget()
        
        # Onglet Clients
        self.clients_tab = self.create_clients_tab()
        self.tabs.addTab(self.clients_tab, "Clients")
        
        # Onglet Fournisseurs
        self.fournisseurs_tab = self.create_fournisseurs_tab()
        self.tabs.addTab(self.fournisseurs_tab, "Fournisseurs")
        
        # Onglet Factures
        self.factures_tab = self.create_factures_tab()
        self.tabs.addTab(self.factures_tab, "Factures")
        
        # Onglet Bons de commande
        self.commandes_tab = self.create_commandes_tab()
        self.tabs.addTab(self.commandes_tab, "Bons de commande")
        
        layout.addWidget(self.tabs)
        
    def create_clients_tab(self) -> QWidget:
        """Crée l'onglet de gestion des clients"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        self.btn_nouveau_client = QPushButton("Nouveau Client")
        self.btn_modifier_client = QPushButton("Modifier")
        self.btn_supprimer_client = QPushButton("Supprimer")
        
        self.btn_nouveau_client.clicked.connect(self.nouveau_client)
        self.btn_modifier_client.clicked.connect(self.modifier_client)
        self.btn_supprimer_client.clicked.connect(self.supprimer_client)
        
        buttons_layout.addWidget(self.btn_nouveau_client)
        buttons_layout.addWidget(self.btn_modifier_client)
        buttons_layout.addWidget(self.btn_supprimer_client)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # Tableau des clients
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(6)
        self.clients_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Prénom", "Entreprise", "Email", "Téléphone"
        ])
        self.clients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.clients_table)
        
        return widget
        
    def create_fournisseurs_tab(self) -> QWidget:
        """Crée l'onglet de gestion des fournisseurs"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        layout.addWidget(QLabel("Gestion des fournisseurs - À implémenter"))
        
        return widget
        
    def create_factures_tab(self) -> QWidget:
        """Crée l'onglet de gestion des factures"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        layout.addWidget(QLabel("Gestion des factures - À implémenter"))
        
        return widget
        
    def create_commandes_tab(self) -> QWidget:
        """Crée l'onglet de gestion des bons de commande"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        layout.addWidget(QLabel("Gestion des bons de commande - À implémenter"))
        
        return widget
        
    def load_data(self):
        """Charge les données dans les tableaux"""
        self.load_clients()
        
    def load_clients(self):
        """Charge la liste des clients"""
        clients = self.db.lister_clients()
        
        self.clients_table.setRowCount(len(clients))
        
        for row, client in enumerate(clients):
            self.clients_table.setItem(row, 0, QTableWidgetItem(str(client.id)))
            self.clients_table.setItem(row, 1, QTableWidgetItem(client.nom))
            self.clients_table.setItem(row, 2, QTableWidgetItem(client.prenom))
            self.clients_table.setItem(row, 3, QTableWidgetItem(client.entreprise))
            self.clients_table.setItem(row, 4, QTableWidgetItem(client.email))
            self.clients_table.setItem(row, 5, QTableWidgetItem(client.telephone))
            
        self.clients_table.resizeColumnsToContents()
        
    def nouveau_client(self):
        """Ouvre le dialog pour créer un nouveau client"""
        dialog = ClientDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            client = dialog.get_client()
            if client.nom:
                try:
                    self.db.ajouter_client(client)
                    self.load_clients()
                    QMessageBox.information(self, "Succès", "Client ajouté avec succès")
                except Exception as e:
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")
            else:
                QMessageBox.warning(self, "Erreur", "Le nom est obligatoire")
                
    def modifier_client(self):
        """Modifie le client sélectionné"""
        QMessageBox.information(self, "Info", "Fonction à implémenter")
        
    def supprimer_client(self):
        """Supprime le client sélectionné"""
        QMessageBox.information(self, "Info", "Fonction à implémenter")
