#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'installation et configuration des outils nécessaires pour GestImmob
Utilise tous les moyens nécessaires pour assurer le bon fonctionnement
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class ToolsSetup:
    def __init__(self):
        self.system = platform.system()
        self.python_version = sys.version_info
        self.errors = []
        self.installed = []
        
    def check_python_version(self):
        """Vérifie la version de Python"""
        print("🔍 Vérification de la version Python...")
        
        if self.python_version >= (3, 8):
            print(f"✅ Python {sys.version} - Compatible")
            return True
        else:
            error = f"❌ Python {sys.version} - Version trop ancienne (requis: 3.8+)"
            print(error)
            self.errors.append(error)
            return False
    
    def install_dependencies(self):
        """Installe toutes les dépendances"""
        print("\n🔧 Installation des dépendances...")
        
        try:
            # Mise à jour de pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                         check=True, capture_output=True)
            print("✅ Pip mis à jour")
            
            # Installation des dépendances principales
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                         check=True, capture_output=True)
            print("✅ Dépendances principales installées")
            self.installed.append("Dépendances Python")
            
            return True
        except subprocess.CalledProcessError as e:
            error = f"❌ Erreur installation dépendances: {e}"
            print(error)
            self.errors.append(error)
            return False
    
    def setup_database(self):
        """Configure la base de données"""
        print("\n🔧 Configuration de la base de données...")
        
        try:
            db_path = Path("immobilisations.db")
            if not db_path.exists():
                # Créer la base de données
                import sqlite3
                conn = sqlite3.connect(str(db_path))
                
                # Optimisations SQLite
                cursor = conn.cursor()
                optimizations = [
                    "PRAGMA journal_mode = WAL",
                    "PRAGMA synchronous = NORMAL", 
                    "PRAGMA cache_size = -64000",
                    "PRAGMA temp_store = MEMORY",
                    "PRAGMA mmap_size = 268435456"
                ]
                
                for pragma in optimizations:
                    cursor.execute(pragma)
                
                conn.commit()
                conn.close()
                
                print("✅ Base de données créée et optimisée")
                self.installed.append("Base de données SQLite")
            else:
                print("✅ Base de données existante")
            
            return True
        except Exception as e:
            error = f"❌ Erreur configuration DB: {e}"
            print(error)
            self.errors.append(error)
            return False
    
    def create_config_files(self):
        """Crée les fichiers de configuration nécessaires"""
        print("\n🔧 Création des fichiers de configuration...")
        
        configs = {
            '.flake8': """[flake8]
max-line-length = 120
ignore = E501,W503,E203,E402
exclude = __pycache__,venv,env,.git
""",
            'pylint.rc': """[MASTER]
disable = C0114,C0115,C0116,R0903,R0913,W0613

[FORMAT]
max-line-length = 120
""",
            'mypy.ini': """[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
""",
            'pytest.ini': """[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
""",
            '.gitignore': """__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

.pytest_cache/
.coverage
htmlcov/

*.db
*.sqlite3
secret.key
config.json
logs/
"""
        }
        
        created_count = 0
        for filename, content in configs.items():
            try:
                if not Path(filename).exists():
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ {filename} créé")
                    created_count += 1
                else:
                    print(f"✅ {filename} existe déjà")
            except Exception as e:
                error = f"❌ Erreur création {filename}: {e}"
                print(error)
                self.errors.append(error)
        
        if created_count > 0:
            self.installed.append(f"{created_count} fichiers de configuration")
        
        return True
    
    def create_startup_script(self):
        """Crée un script de démarrage optimisé"""
        print("\n🔧 Création du script de démarrage...")
        
        startup_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
Script de démarrage optimisé pour GestImmob
\"\"\"

import sys
import os
import logging
from pathlib import Path

def setup_environment():
    \"\"\"Configure l'environnement d'exécution\"\"\"
    # Ajouter le répertoire src au path
    src_path = Path(__file__).parent / 'src'
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    # Configuration du logging
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'gestimmob.log'),
            logging.StreamHandler()
        ]
    )

def check_dependencies():
    \"\"\"Vérifie les dépendances critiques\"\"\"
    critical_modules = [
        'PySide6',
        'cryptography', 
        'sqlite3',
        'requests'
    ]
    
    missing = []
    for module in critical_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        print(f"❌ Modules manquants: {', '.join(missing)}")
        print("Exécutez: pip install -r requirements.txt")
        return False
    
    return True

def main():
    \"\"\"Fonction principale de démarrage\"\"\"
    print("🚀 Démarrage de GestImmob...")
    
    setup_environment()
    
    if not check_dependencies():
        sys.exit(1)
    
    try:
        from main import main as app_main
        app_main()
    except Exception as e:
        logging.error(f"Erreur lors du démarrage: {e}")
        print(f"❌ Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
        
        try:
            with open('start_gestimmob.py', 'w', encoding='utf-8') as f:
                f.write(startup_script)
            
            # Rendre exécutable sur Unix
            if self.system != 'Windows':
                os.chmod('start_gestimmob.py', 0o755)
            
            print("✅ Script de démarrage créé")
            self.installed.append("Script de démarrage optimisé")
            return True
        except Exception as e:
            error = f"❌ Erreur création script démarrage: {e}"
            print(error)
            self.errors.append(error)
            return False
    
    def create_diagnostic_tool(self):
        """Crée un outil de diagnostic système"""
        print("\n🔧 Création de l'outil de diagnostic...")
        
        diagnostic_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
Outil de diagnostic pour GestImmob
\"\"\"

import sys
import platform
import sqlite3
from pathlib import Path

def system_info():
    \"\"\"Affiche les informations système\"\"\"
    print("🖥️ INFORMATIONS SYSTÈME")
    print("=" * 40)
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print(f"Architecture: {platform.architecture()[0]}")
    
def check_files():
    \"\"\"Vérifie les fichiers nécessaires\"\"\"
    print("\\n📁 VÉRIFICATION DES FICHIERS")
    print("=" * 40)
    
    required_files = [
        'src/main.py',
        'requirements.txt',
        'immobilisations.db'
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MANQUANT")

def check_database():
    \"\"\"Vérifie la base de données\"\"\"
    print("\\n🗄️ VÉRIFICATION BASE DE DONNÉES")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('immobilisations.db')
        cursor = conn.cursor()
        
        # Vérifier les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"Tables trouvées: {len(tables)}")
        for table in tables:
            print(f"  - {table[0]}")
        
        conn.close()
        print("✅ Base de données accessible")
    except Exception as e:
        print(f"❌ Erreur DB: {e}")

def main():
    system_info()
    check_files()
    check_database()

if __name__ == "__main__":
    main()
"""
        
        try:
            with open('diagnostic.py', 'w', encoding='utf-8') as f:
                f.write(diagnostic_script)
            
            print("✅ Outil de diagnostic créé")
            self.installed.append("Outil de diagnostic")
            return True
        except Exception as e:
            error = f"❌ Erreur création diagnostic: {e}"
            print(error)
            self.errors.append(error)
            return False
    
    def run_setup(self):
        """Exécute la configuration complète"""
        print("🚀 CONFIGURATION COMPLÈTE DE GESTIMMOB")
        print("=" * 50)
        
        steps = [
            ("Version Python", self.check_python_version),
            ("Dépendances", self.install_dependencies),
            ("Base de données", self.setup_database),
            ("Fichiers config", self.create_config_files),
            ("Script démarrage", self.create_startup_script),
            ("Outil diagnostic", self.create_diagnostic_tool)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            try:
                if step_func():
                    success_count += 1
            except Exception as e:
                error = f"Erreur dans {step_name}: {e}"
                self.errors.append(error)
                print(f"❌ {error}")
        
        # Résumé final
        print("\\n" + "="*50)
        print("📋 RÉSUMÉ DE LA CONFIGURATION")
        print("="*50)
        
        print(f"✅ Étapes réussies: {success_count}/{len(steps)}")
        print(f"🔧 Outils installés: {len(self.installed)}")
        print(f"❌ Erreurs: {len(self.errors)}")
        
        if self.installed:
            print("\\n🔧 OUTILS INSTALLÉS:")
            for tool in self.installed:
                print(f"  - {tool}")
        
        if self.errors:
            print("\\n❌ ERREURS:")
            for error in self.errors:
                print(f"  - {error}")
        
        if success_count == len(steps):
            print("\\n🎉 CONFIGURATION TERMINÉE AVEC SUCCÈS !")
            print("Utilisez: python start_gestimmob.py pour démarrer")
            return True
        else:
            print("\\n⚠️ Configuration incomplète")
            return False

def main():
    setup = ToolsSetup()
    success = setup.run_setup()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
