@echo off
title 🔍 RECHERCHE DOSSIER ROBOT IA SUR BUREAU
color 0B
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🔍 RECHERCHE DOSSIER ROBOT IA SUR BUREAU                               ██
echo ██    📁 LOCALISATION + VÉRIFICATION + CRÉATION SI NÉCESSAIRE                ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🔍 RECHERCHE DOSSIER ROBOT IA...
echo 📁 Vérification emplacements possibles
echo 🖥️ Analyse bureau utilisateur
echo.

REM Variables
set "BUREAU=%USERPROFILE%\Desktop"
set "ROBOT_BUREAU=%BUREAU%\RobotIA_SamNord"
set "BUREAU_PUBLIC=%PUBLIC%\Desktop"
set "ROBOT_PUBLIC=%BUREAU_PUBLIC%\RobotIA_SamNord"

echo 📍 EMPLACEMENTS À VÉRIFIER:
echo 🖥️ Bureau utilisateur: %BUREAU%
echo 📁 Dossier Robot IA: %ROBOT_BUREAU%
echo 🌐 Bureau public: %BUREAU_PUBLIC%
echo.

echo 🔄 VÉRIFICATION 1: Bureau utilisateur principal...
if exist "%ROBOT_BUREAU%" (
    echo ✅ TROUVÉ ! Dossier Robot IA existe dans:
    echo    📁 %ROBOT_BUREAU%
    echo.
    echo 📂 Contenu du dossier:
    dir "%ROBOT_BUREAU%" /B 2>nul
    echo.
    echo 🚀 Ouverture du dossier...
    start "" explorer "%ROBOT_BUREAU%"
    goto FOUND
) else (
    echo ❌ Non trouvé dans bureau utilisateur
)

echo.
echo 🔄 VÉRIFICATION 2: Bureau public...
if exist "%ROBOT_PUBLIC%" (
    echo ✅ TROUVÉ ! Dossier Robot IA existe dans:
    echo    📁 %ROBOT_PUBLIC%
    echo.
    echo 🚀 Ouverture du dossier...
    start "" explorer "%ROBOT_PUBLIC%"
    goto FOUND
) else (
    echo ❌ Non trouvé dans bureau public
)

echo.
echo 🔄 VÉRIFICATION 3: Recherche dans tout le bureau...
echo 🔍 Recherche fichiers Robot IA...

for /f "delims=" %%i in ('dir "%BUREAU%\*Robot*" /B /AD 2^>nul') do (
    echo ✅ TROUVÉ ! Dossier similaire: %%i
    echo 📁 Emplacement: %BUREAU%\%%i
    start "" explorer "%BUREAU%\%%i"
    goto FOUND
)

echo.
echo 🔄 VÉRIFICATION 4: Recherche raccourcis Robot IA...
for /f "delims=" %%i in ('dir "%BUREAU%\*Robot*.lnk" /B 2^>nul') do (
    echo ✅ TROUVÉ ! Raccourci Robot IA: %%i
    echo 🔗 Emplacement: %BUREAU%\%%i
)

echo.
echo 🔄 VÉRIFICATION 5: Recherche dans dossiers utilisateur...
for /d %%i in ("%USERPROFILE%\*Robot*") do (
    echo ✅ TROUVÉ ! Dossier Robot IA: %%i
    start "" explorer "%%i"
    goto FOUND
)

echo.
echo ❌ DOSSIER ROBOT IA NON TROUVÉ !
echo.
echo 🛠️ SOLUTIONS:
echo.
echo 1️⃣ CRÉER LE DOSSIER MAINTENANT:
echo    Double-cliquer "CREER_DOSSIER_BUREAU_MAINTENANT.bat"
echo.
echo 2️⃣ INSTALLATION COMPLÈTE:
echo    Double-cliquer "INSTALLATION_BUREAU_EXECUTABLE.bat"
echo.
echo 3️⃣ INSTALLATION + LANCEMENT:
echo    Double-cliquer "INSTALLER_ET_LANCER.bat"
echo.
echo 🔧 CRÉATION AUTOMATIQUE DU DOSSIER...
echo.

set /p choix="Voulez-vous créer le dossier maintenant ? (O/N): "
if /i "%choix%"=="O" (
    echo 🚀 Création du dossier en cours...
    call "CREER_DOSSIER_BUREAU_MAINTENANT.bat"
    goto END
) else if /i "%choix%"=="o" (
    echo 🚀 Création du dossier en cours...
    call "CREER_DOSSIER_BUREAU_MAINTENANT.bat"
    goto END
)

echo.
echo 💡 CONSEILS POUR TROUVER LE DOSSIER:
echo.
echo 🖥️ VÉRIFIEZ VOTRE BUREAU:
echo    - Regardez s'il y a un dossier "RobotIA_SamNord"
echo    - Ou un raccourci "Robot IA - SamNord@110577"
echo.
echo 📁 AUTRES EMPLACEMENTS POSSIBLES:
echo    - %USERPROFILE%\Desktop\RobotIA_SamNord
echo    - %PUBLIC%\Desktop\RobotIA_SamNord
echo    - Documents\RobotIA_SamNord
echo.
echo 🔍 RECHERCHE MANUELLE:
echo    1. Ouvrir Explorateur Windows
echo    2. Rechercher "RobotIA" ou "Robot IA"
echo    3. Filtrer par dossiers
echo.

echo 🚀 Ouverture bureau pour vérification manuelle...
start "" explorer "%BUREAU%"

goto END

:FOUND
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         ✅ DOSSIER ROBOT IA TROUVÉ ET OUVERT !                            ██
echo ██         📁 VÉRIFIEZ LA FENÊTRE EXPLORATEUR                                ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎉 SUCCÈS ! Dossier Robot IA localisé !
echo.
echo 🚀 POUR LANCER LE ROBOT IA:
echo 1. Dans le dossier ouvert, chercher "🚀 LANCER ROBOT IA.bat"
echo 2. Ou double-cliquer raccourci bureau "🤖 Robot IA - SamNord@110577"
echo.
echo 👤 VOS IDENTIFIANTS:
echo    Utilisateur: SamNord@110577
echo    Mot de passe: NorDine@22
echo.

:END
echo.
echo 👋 Recherche terminée
pause
exit /b 0
