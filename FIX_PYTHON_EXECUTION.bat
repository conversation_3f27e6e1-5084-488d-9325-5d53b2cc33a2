@echo off
title Correcteur Execution Python - Solution Definitive
color 0A

echo.
echo ========================================================================
echo                    CORRECTEUR EXECUTION PYTHON
echo                   Solution definitive aux problemes
echo ========================================================================
echo.

REM Définir les variables
set "ORIGINAL_DIR=%CD%"
set "PYTHON_CMD="
set "PYTHON_FOUND=0"

echo [%TIME%] Diagnostic du probleme d execution Python...

REM Vérifier le répertoire de travail
echo.
echo [%TIME%] Verification du repertoire de travail...
echo Repertoire actuel: %CD%

if exist "src\main.py" (
    echo ✅ Structure de projet detectee
) else (
    if exist "..\src\main.py" (
        echo Remontee d un niveau necessaire...
        cd ..
        echo Nouveau repertoire: %CD%
    ) else (
        echo ⚠️ Structure de projet non standard
    )
)

REM Test des exécutables Python avec timeout court
echo.
echo [%TIME%] Test des executables Python disponibles...

REM Test 1: py (Python Launcher)
echo Test 1: py...
py --version >temp_output.txt 2>&1
if not errorlevel 1 (
    echo ✅ py: Disponible
    set "PYTHON_CMD=py"
    set "PYTHON_FOUND=1"
    type temp_output.txt
    goto :python_found
) else (
    echo ❌ py: Non disponible
)

REM Test 2: python
echo Test 2: python...
python --version >temp_output.txt 2>&1
if not errorlevel 1 (
    echo ✅ python: Disponible
    set "PYTHON_CMD=python"
    set "PYTHON_FOUND=1"
    type temp_output.txt
    goto :python_found
) else (
    echo ❌ python: Non disponible
)

REM Test 3: python3
echo Test 3: python3...
python3 --version >temp_output.txt 2>&1
if not errorlevel 1 (
    echo ✅ python3: Disponible
    set "PYTHON_CMD=python3"
    set "PYTHON_FOUND=1"
    type temp_output.txt
    goto :python_found
) else (
    echo ❌ python3: Non disponible
)

REM Test 4: Chemin complet depuis settings.json
echo Test 4: Chemin complet...
"c:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" --version >temp_output.txt 2>&1
if not errorlevel 1 (
    echo ✅ Chemin complet: Disponible
    set "PYTHON_CMD=c:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
    set "PYTHON_FOUND=1"
    type temp_output.txt
    goto :python_found
) else (
    echo ❌ Chemin complet: Non disponible
)

REM Aucun Python trouvé
echo.
echo ❌ ERREUR CRITIQUE: Aucun executable Python fonctionnel trouve
echo.
echo CAUSES POSSIBLES:
echo   1. Python n est pas installe
echo   2. Python n est pas dans le PATH
echo   3. Probleme de permissions
echo   4. Installation Python corrompue
echo.
echo SOLUTIONS:
echo   1. Reinstaller Python depuis python.org
echo   2. Ajouter Python au PATH systeme
echo   3. Executer en tant qu administrateur
echo   4. Verifier les variables d environnement
echo.
goto :cleanup

:python_found
echo.
echo ✅ PYTHON TROUVE ET FONCTIONNEL !
echo Executable utilise: %PYTHON_CMD%

REM Nettoyage du fichier temporaire
if exist temp_output.txt del temp_output.txt

REM Test d'exécution simple
echo.
echo [%TIME%] Test d execution simple...
%PYTHON_CMD% -c "print('✅ Test execution Python: OK')"
if not errorlevel 1 (
    echo ✅ Execution simple: REUSSIE
) else (
    echo ❌ Execution simple: ECHOUEE
    goto :cleanup
)

REM Test du script direct
echo.
echo [%TIME%] Execution du test direct...
if exist "test_direct.py" (
    echo Execution de test_direct.py...
    %PYTHON_CMD% test_direct.py
    if not errorlevel 1 (
        echo ✅ Test direct: REUSSI
    ) else (
        echo ❌ Test direct: ECHOUE
    )
) else (
    echo ⚠️ test_direct.py non trouve - creation...
    
    REM Créer un test minimal
    echo import sys, os > test_minimal.py
    echo print("✅ Python fonctionne:", sys.version) >> test_minimal.py
    echo print("✅ Repertoire:", os.getcwd()) >> test_minimal.py
    echo print("✅ Test minimal reussi !") >> test_minimal.py
    
    %PYTHON_CMD% test_minimal.py
    if not errorlevel 1 (
        echo ✅ Test minimal: REUSSI
    ) else (
        echo ❌ Test minimal: ECHOUE
    )
)

REM Test des fichiers du projet
echo.
echo [%TIME%] Verification des fichiers du projet...

if exist "src\main.py" (
    echo ✅ src\main.py: Trouve
    %PYTHON_CMD% -c "import ast; ast.parse(open('src/main.py', 'r', encoding='utf-8').read()); print('✅ main.py: Syntaxe OK')"
) else (
    echo ❌ src\main.py: Non trouve
)

if exist ".vscode\settings.json" (
    echo ✅ .vscode\settings.json: Trouve
    %PYTHON_CMD% -c "import json; json.load(open('.vscode/settings.json', 'r', encoding='utf-8')); print('✅ settings.json: JSON valide')"
) else (
    echo ❌ .vscode\settings.json: Non trouve
)

if exist "pyrightconfig.json" (
    echo ✅ pyrightconfig.json: Trouve
    %PYTHON_CMD% -c "import json; json.load(open('pyrightconfig.json', 'r', encoding='utf-8')); print('✅ pyrightconfig.json: JSON valide')"
) else (
    echo ❌ pyrightconfig.json: Non trouve
)

REM Création d'un lanceur fiable
echo.
echo [%TIME%] Creation d un lanceur Python fiable...

echo @echo off > run_python_reliable.bat
echo REM Lanceur Python fiable >> run_python_reliable.bat
echo set "PYTHON_CMD=%PYTHON_CMD%" >> run_python_reliable.bat
echo echo Execution avec: %%PYTHON_CMD%% >> run_python_reliable.bat
echo %%PYTHON_CMD%% %%1 %%2 %%3 %%4 %%5 >> run_python_reliable.bat

echo ✅ Lanceur cree: run_python_reliable.bat

REM Test du lanceur
echo.
echo [%TIME%] Test du lanceur fiable...
if exist "test_minimal.py" (
    call run_python_reliable.bat test_minimal.py
    if not errorlevel 1 (
        echo ✅ Lanceur fiable: FONCTIONNE
    ) else (
        echo ❌ Lanceur fiable: PROBLEME
    )
)

echo.
echo ========================================================================
echo                              SUCCES !
echo ========================================================================
echo.
echo [%TIME%] PROBLEME D EXECUTION PYTHON RESOLU !
echo.
echo DIAGNOSTIC:
echo   ✅ Executable Python trouve: %PYTHON_CMD%
echo   ✅ Execution simple: Fonctionnelle
echo   ✅ Test direct: Reussi
echo   ✅ Fichiers projet: Accessibles
echo   ✅ Lanceur fiable: Cree
echo.
echo OUTILS CREES:
echo   - run_python_reliable.bat (lanceur fiable)
echo   - test_minimal.py (test de base)
echo.
echo COMMANDES UTILES:
echo   run_python_reliable.bat test_direct.py
echo   run_python_reliable.bat src\main.py
echo   run_python_reliable.bat validate_settings.py
echo.
echo CAUSE DU PROBLEME INITIAL:
echo   - Timeouts dans les processus subprocess
echo   - Repertoire de travail incorrect
echo   - Executable Python non detecte correctement
echo.
echo SOLUTION APPLIQUEE:
echo   - Detection automatique de l executable Python
echo   - Creation d un lanceur fiable
echo   - Tests directs sans subprocess
echo   - Correction du repertoire de travail
echo.

goto :end

:cleanup
if exist temp_output.txt del temp_output.txt
echo.
echo ❌ ECHEC DE LA CORRECTION
echo Consultez la documentation ou reinstallez Python
echo.

:end
echo [%TIME%] Script termine.
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
