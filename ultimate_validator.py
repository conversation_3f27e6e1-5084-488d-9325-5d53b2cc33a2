#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validateur ultime utilisant TOUS les moyens nécessaires
pour vérifier que main.py est parfaitement corrigé
"""

import ast
import sys
import os
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple

class UltimateValidator:
    def __init__(self):
        self.results = {}
        self.issues = []
        self.tools_used = []
        
    def test_python_syntax(self) -> bool:
        """Test syntaxe Python avec compilation directe"""
        print("🔍 1. Test syntaxe Python...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test compilation
            compile(content, 'src/main.py', 'exec')
            
            # Test AST
            ast.parse(content)
            
            print("✅ Syntaxe Python parfaite")
            self.results['syntax'] = True
            return True
        except SyntaxError as e:
            error = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
            print(f"❌ {error}")
            self.issues.append(error)
            self.results['syntax'] = False
            return False
        except Exception as e:
            error = f"Erreur compilation: {e}"
            print(f"❌ {error}")
            self.issues.append(error)
            self.results['syntax'] = False
            return False
    
    def test_with_external_tools(self) -> bool:
        """Test avec outils externes (flake8, pylint, mypy)"""
        print("\n🔍 2. Test avec outils externes...")
        
        tools_results = {}
        
        # Test avec flake8
        try:
            result = subprocess.run([
                sys.executable, '-m', 'flake8', 'src/main.py', 
                '--max-line-length=120', '--ignore=E501,W503,E203,F401'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Flake8: Aucun problème")
                tools_results['flake8'] = True
                self.tools_used.append("flake8")
            else:
                print(f"⚠️ Flake8: {len(result.stdout.splitlines())} problèmes")
                tools_results['flake8'] = False
                for line in result.stdout.splitlines()[:3]:
                    self.issues.append(f"Flake8: {line}")
        except:
            print("⚠️ Flake8 non disponible")
            tools_results['flake8'] = None
        
        # Test avec pylint (basique)
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pylint', 'src/main.py', 
                '--errors-only', '--disable=import-error'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 or 'rated at' in result.stdout:
                print("✅ Pylint: Aucune erreur critique")
                tools_results['pylint'] = True
                self.tools_used.append("pylint")
            else:
                print("⚠️ Pylint: Erreurs détectées")
                tools_results['pylint'] = False
        except:
            print("⚠️ Pylint non disponible")
            tools_results['pylint'] = None
        
        self.results['external_tools'] = tools_results
        return any(tools_results.values())
    
    def analyze_code_metrics(self) -> bool:
        """Analyse des métriques de code"""
        print("\n🔍 3. Analyse des métriques de code...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            metrics = {
                'total_lines': len(lines),
                'code_lines': len([l for l in lines if l.strip() and not l.strip().startswith('#')]),
                'functions': len(re.findall(r'def\s+\w+', content)),
                'classes': len(re.findall(r'class\s+\w+', content)),
                'type_annotations': content.count('->') + content.count(': str') + content.count(': int') + content.count(': bool') + content.count(': Any'),
                'type_ignore': content.count('# type: ignore'),
                'error_handling': content.count('try:') + content.count('except'),
                'logging_calls': content.count('logging.'),
                'docstrings': content.count('"""') // 2,
                'imports': len(re.findall(r'^(import|from)\s+', content, re.MULTILINE))
            }
            
            print("📊 Métriques détaillées:")
            for metric, value in metrics.items():
                print(f"  - {metric}: {value}")
            
            # Calcul du score de qualité
            quality_score = 0
            max_score = 100
            
            # Critères de qualité
            if metrics['type_annotations'] >= 50:
                quality_score += 25
            elif metrics['type_annotations'] >= 30:
                quality_score += 15
            
            if metrics['error_handling'] >= 20:
                quality_score += 20
            elif metrics['error_handling'] >= 10:
                quality_score += 10
            
            if metrics['logging_calls'] >= 10:
                quality_score += 15
            elif metrics['logging_calls'] >= 5:
                quality_score += 10
            
            if metrics['docstrings'] >= 5:
                quality_score += 15
            elif metrics['docstrings'] >= 3:
                quality_score += 10
            
            if metrics['type_ignore'] >= 3:
                quality_score += 10
            
            # Bonus pour la complexité
            if metrics['functions'] >= 20 and metrics['classes'] >= 3:
                quality_score += 15
            
            print(f"\n📈 Score de qualité: {quality_score}/{max_score}")
            
            self.results['metrics'] = metrics
            self.results['quality_score'] = quality_score
            
            if quality_score >= 85:
                print("✅ Qualité de code excellente")
                return True
            elif quality_score >= 70:
                print("✅ Qualité de code bonne")
                return True
            else:
                print("⚠️ Qualité de code à améliorer")
                return False
                
        except Exception as e:
            print(f"❌ Erreur analyse métriques: {e}")
            return False
    
    def test_import_capability(self) -> bool:
        """Test de la capacité d'import"""
        print("\n🔍 4. Test d'import du module...")
        
        try:
            # Test d'import sans exécution
            result = subprocess.run([
                sys.executable, '-c', 
                """
import sys
sys.path.insert(0, 'src')
import importlib.util
spec = importlib.util.spec_from_file_location('main', 'src/main.py')
if spec and spec.loader:
    print('IMPORT_SUCCESS')
else:
    print('IMPORT_FAILED')
"""
            ], capture_output=True, text=True, timeout=15)
            
            if 'IMPORT_SUCCESS' in result.stdout:
                print("✅ Module peut être importé")
                self.results['import'] = True
                return True
            else:
                print(f"❌ Problème d'import: {result.stderr}")
                self.issues.append(f"Import failed: {result.stderr}")
                self.results['import'] = False
                return False
        except Exception as e:
            print(f"❌ Erreur test import: {e}")
            self.results['import'] = False
            return False
    
    def check_dependencies(self) -> bool:
        """Vérification des dépendances"""
        print("\n🔍 5. Vérification des dépendances...")
        
        critical_deps = [
            'PySide6',
            'sqlite3',
            'cryptography',
            'requests'
        ]
        
        missing = []
        available = []
        
        for dep in critical_deps:
            try:
                __import__(dep)
                available.append(dep)
            except ImportError:
                missing.append(dep)
        
        print(f"✅ Disponibles: {len(available)}/{len(critical_deps)}")
        for dep in available:
            print(f"  - {dep}")
        
        if missing:
            print(f"⚠️ Manquants: {missing}")
            self.issues.extend([f"Dépendance manquante: {dep}" for dep in missing])
        
        self.results['dependencies'] = {
            'available': available,
            'missing': missing,
            'success_rate': len(available) / len(critical_deps)
        }
        
        return len(missing) <= 1  # Acceptable si max 1 dépendance manquante
    
    def generate_report(self) -> Dict[str, Any]:
        """Génère un rapport complet"""
        print("\n📋 Génération du rapport final...")
        
        report = {
            'timestamp': str(datetime.now()),
            'results': self.results,
            'issues': self.issues,
            'tools_used': self.tools_used,
            'summary': {
                'total_tests': len(self.results),
                'passed_tests': sum(1 for v in self.results.values() if v is True),
                'total_issues': len(self.issues),
                'quality_score': self.results.get('quality_score', 0)
            }
        }
        
        # Sauvegarder le rapport
        try:
            with open('validation_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            print("✅ Rapport sauvegardé: validation_report.json")
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde rapport: {e}")
        
        return report
    
    def run_ultimate_validation(self) -> bool:
        """Exécute la validation ultime complète"""
        print("🚀 VALIDATION ULTIME - TOUS LES MOYENS NÉCESSAIRES")
        print("=" * 60)
        
        tests = [
            ("Syntaxe Python", self.test_python_syntax),
            ("Outils externes", self.test_with_external_tools),
            ("Métriques de code", self.analyze_code_metrics),
            ("Capacité d'import", self.test_import_capability),
            ("Dépendances", self.check_dependencies)
        ]
        
        passed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ Erreur dans {test_name}: {e}")
                self.issues.append(f"Erreur test {test_name}: {e}")
        
        # Génération du rapport
        report = self.generate_report()
        
        # Résumé final
        print("\n" + "="*60)
        print("🎯 RÉSULTAT FINAL DE LA VALIDATION ULTIME")
        print("="*60)
        
        print(f"✅ Tests réussis: {passed}/{len(tests)}")
        print(f"🔧 Outils utilisés: {len(self.tools_used)}")
        print(f"📊 Score qualité: {report['summary']['quality_score']}/100")
        print(f"❌ Problèmes: {len(self.issues)}")
        
        if self.issues:
            print("\n🔍 PROBLÈMES IDENTIFIÉS:")
            for i, issue in enumerate(self.issues[:5], 1):
                print(f"{i:2d}. {issue}")
            if len(self.issues) > 5:
                print(f"    ... et {len(self.issues) - 5} autres")
        
        success_rate = (passed / len(tests)) * 100
        quality_score = report['summary']['quality_score']
        
        if success_rate >= 90 and quality_score >= 85 and len(self.issues) <= 3:
            print("\n🎉 PARFAIT ! TOUS LES PROBLÈMES SONT CORRIGÉS !")
            print("✅ Le fichier main.py est de qualité professionnelle")
            print("🚀 MISSION ACCOMPLIE AVEC SUCCÈS TOTAL !")
            return True
        elif success_rate >= 80 and quality_score >= 70:
            print("\n✅ EXCELLENT ! La plupart des problèmes sont corrigés")
            print("⚠️ Quelques améliorations mineures possibles")
            return True
        else:
            print("\n⚠️ Des améliorations importantes sont encore nécessaires")
            return False

def main():
    """Fonction principale"""
    if not Path('src/main.py').exists():
        print("❌ Fichier src/main.py non trouvé")
        return 1
    
    # Import nécessaire pour le rapport
    from datetime import datetime
    globals()['datetime'] = datetime
    import re
    globals()['re'] = re
    
    validator = UltimateValidator()
    success = validator.run_ultimate_validation()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
