#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de déploiement automatique pour GestImmob
Déploie l'application sur différents environnements
"""

import sys
import shutil
import subprocess
import json
import zipfile
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, Any

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deploy.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DeploymentManager:
    """Gestionnaire de déploiement pour GestImmob"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.dist_dir = self.project_root / "dist"
        self.deploy_dir = self.project_root / "deploy"
        
        # Configuration de déploiement
        self.config: Dict[str, Any] = {
            "app_name": "GestImmob",
            "version": "2.0.0",
            "environments": {
                "development": {
                    "path": "./deploy/dev",
                    "database": "gestimmob_dev.db",
                    "debug": True,
                    "log_level": "DEBUG"
                },
                "staging": {
                    "path": "./deploy/staging", 
                    "database": "gestimmob_staging.db",
                    "debug": False,
                    "log_level": "INFO"
                },
                "production": {
                    "path": "./deploy/prod",
                    "database": "gestimmob_prod.db", 
                    "debug": False,
                    "log_level": "WARNING"
                }
            }
        }
        
    def create_deployment_package(self, environment: str = "production") -> Optional[Path]:
        """Crée un package de déploiement"""
        logger.info(f"Création du package de déploiement pour {environment}...")
        
        if environment not in self.config["environments"]:
            logger.error(f"Environnement '{environment}' non configuré")
            return None
            
        env_config = self.config["environments"][environment]
        
        # Créer le répertoire de déploiement
        deploy_path = Path(env_config["path"])
        deploy_path.mkdir(parents=True, exist_ok=True)
        
        # Vérifier que l'exécutable existe
        exe_path = self.dist_dir / f"{self.config['app_name']}.exe"
        if not exe_path.exists():
            logger.error(f"Exécutable non trouvé: {exe_path}")
            logger.info("Exécutez d'abord: python build_executable.py")
            return None
            
        # Copier l'exécutable
        try:
            shutil.copy2(exe_path, deploy_path)
            logger.info(f"Exécutable copié vers {deploy_path}")
        except (IOError, OSError, shutil.Error) as e:
            logger.error(f"Erreur lors de la copie de l'exécutable: {e}")
            return None
        
        # Copier les fichiers de configuration
        config_files = [
            "config_advanced.json",
            "requirements.txt",
            "README.md"
        ]
        
        for config_file in config_files:
            src = self.project_root / config_file
            if src.exists():
                try:
                    dst = deploy_path / config_file
                    shutil.copy2(src, dst)
                    logger.info(f"Fichier copié: {config_file}")
                except (IOError, OSError, shutil.Error) as e:
                    logger.warning(f"Erreur lors de la copie de {config_file}: {e}")
                
        # Créer la configuration spécifique à l'environnement
        self.create_environment_config(deploy_path, environment)
        
        # Créer les répertoires nécessaires
        self.create_directory_structure(deploy_path)
        
        # Créer les scripts de démarrage
        self.create_startup_scripts(deploy_path, environment)
        
        # Créer le package ZIP
        package_path = self.create_zip_package(deploy_path, environment)
        
        logger.info(f"Package de déploiement créé: {package_path}")
        return package_path
        
    def create_environment_config(self, deploy_path: Path, environment: str) -> None:
        """Crée la configuration spécifique à l'environnement"""
        env_config = self.config["environments"][environment]
        
        # Charger la configuration de base
        base_config_path = self.project_root / "config_advanced.json"
        config: Dict[str, Any] = {}

        if base_config_path.exists():
            try:
                with open(base_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            except (IOError, OSError, json.JSONDecodeError) as e:
                logger.warning(f"Erreur lors du chargement de la configuration de base: {e}")
                config = {}
            
        # Adapter la configuration pour l'environnement
        if "database" not in config:
            config["database"] = {}
        if "advanced" not in config:
            config["advanced"] = {}
        if "logging" not in config:
            config["logging"] = {}

        config["database"]["path"] = env_config["database"]
        config["advanced"]["debug_mode"] = env_config["debug"]
        config["logging"]["level"] = env_config["log_level"]
        config["deployment"] = {
            "environment": environment,
            "deployed_at": datetime.now().isoformat(),
            "version": self.config["version"]
        }

        # Sauvegarder la configuration
        env_config_path = deploy_path / "config.json"
        try:
            with open(env_config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except (IOError, OSError) as e:
            logger.error(f"Erreur lors de la sauvegarde de la configuration: {e}")
            raise
            
        logger.info(f"Configuration {environment} créée: {env_config_path}")
        
    def create_directory_structure(self, deploy_path: Path) -> None:
        """Crée la structure de répertoires nécessaire"""
        directories = [
            "data",
            "logs", 
            "backups",
            "temp",
            "plugins",
            "exports",
            "imports",
            "documents"
        ]
        
        for directory in directories:
            dir_path = deploy_path / directory
            dir_path.mkdir(exist_ok=True)
            
            # Créer un fichier .gitkeep pour préserver les répertoires vides
            gitkeep_path = dir_path / ".gitkeep"
            gitkeep_path.touch()
            
        logger.info("Structure de répertoires créée")
        
    def create_startup_scripts(self, deploy_path: Path, environment: str) -> None:
        """Crée les scripts de démarrage"""
        app_name = self.config["app_name"]
        
        # Script Windows (.bat)
        bat_content = f'''@echo off
title {app_name} - {environment.title()}
echo Démarrage de {app_name} ({environment})...
echo.

REM Vérifier que les répertoires existent
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups

REM Démarrer l'application
echo Lancement de l'application...
"{app_name}.exe"

REM Gestion des erreurs
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ERREUR: L'application s'est fermée avec le code d'erreur %ERRORLEVEL%
    echo Consultez les logs dans le dossier 'logs' pour plus d'informations.
    pause
)
'''
        
        bat_path = deploy_path / f"start_{app_name.lower()}.bat"
        try:
            with open(bat_path, 'w', encoding='utf-8') as f:
                f.write(bat_content)
        except (IOError, OSError) as e:
            logger.error(f"Erreur lors de la création du script BAT: {e}")
            raise
            
        # Script PowerShell (.ps1)
        ps1_content = f'''# Script de démarrage PowerShell pour {app_name}
Write-Host "Démarrage de {app_name} ({environment})" -ForegroundColor Green

# Vérifier les prérequis
$exePath = ".\\{app_name}.exe"
if (-not (Test-Path $exePath)) {{
    Write-Error "Exécutable non trouvé: $exePath"
    exit 1
}}

# Créer les répertoires nécessaires
$directories = @("data", "logs", "backups", "temp")
foreach ($dir in $directories) {{
    if (-not (Test-Path $dir)) {{
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Répertoire créé: $dir" -ForegroundColor Yellow
    }}
}}

# Démarrer l'application
try {{
    Write-Host "Lancement de l'application..." -ForegroundColor Cyan
    Start-Process -FilePath $exePath -Wait
    Write-Host "Application fermée normalement" -ForegroundColor Green
}}
catch {{
    Write-Error "Erreur lors du démarrage: $($_.Exception.Message)"
    exit 1
}}
'''
        
        ps1_path = deploy_path / f"start_{app_name.lower()}.ps1"
        try:
            with open(ps1_path, 'w', encoding='utf-8') as f:
                f.write(ps1_content)
        except (IOError, OSError) as e:
            logger.error(f"Erreur lors de la création du script PowerShell: {e}")
            raise
            
        logger.info("Scripts de démarrage créés")
        
    def create_zip_package(self, deploy_path: Path, environment: str) -> Path:
        """Crée un package ZIP du déploiement"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_name = f"{self.config['app_name']}_{environment}_{self.config['version']}_{timestamp}.zip"
        zip_path = self.deploy_dir / zip_name
        
        # Créer le répertoire de déploiement si nécessaire
        self.deploy_dir.mkdir(exist_ok=True)
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in deploy_path.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(deploy_path)
                        zipf.write(file_path, arcname)
        except (IOError, OSError, zipfile.BadZipFile) as e:
            logger.error(f"Erreur lors de la création du package ZIP: {e}")
            raise
                    
        logger.info(f"Package ZIP créé: {zip_path}")
        return zip_path
        
    def create_installer(self, environment: str = "production") -> Optional[Path]:
        """Crée un installateur pour l'environnement spécifié"""
        logger.info(f"Création de l'installateur pour {environment}...")
        
        # Créer le package de déploiement
        package_path = self.create_deployment_package(environment)
        if not package_path:
            return None
            
        # Script d'installation
        installer_content = f'''@echo off
title Installation de {self.config["app_name"]} - {environment.title()}
echo ========================================
echo Installation de {self.config["app_name"]}
echo Environnement: {environment.title()}
echo Version: {self.config["version"]}
echo ========================================
echo.

REM Demander le répertoire d'installation
set /p INSTALL_DIR="Répertoire d'installation [C:\\Program Files\\{self.config["app_name"]}]: "
if "%INSTALL_DIR%"=="" set INSTALL_DIR=C:\\Program Files\\{self.config["app_name"]}

echo.
echo Installation dans: %INSTALL_DIR%
echo.

REM Créer le répertoire d'installation
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    if %ERRORLEVEL% NEQ 0 (
        echo ERREUR: Impossible de créer le répertoire d'installation
        echo Vérifiez vos permissions ou exécutez en tant qu'administrateur
        pause
        exit /b 1
    )
)

REM Extraire les fichiers (nécessite 7-Zip ou PowerShell)
echo Extraction des fichiers...
powershell -Command "Expand-Archive -Path '{package_path.name}' -DestinationPath '%INSTALL_DIR%' -Force"

if %ERRORLEVEL% NEQ 0 (
    echo ERREUR: Échec de l'extraction
    pause
    exit /b 1
)

REM Créer un raccourci sur le bureau
echo Création du raccourci...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\{self.config["app_name"]}.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\{self.config["app_name"]}.exe'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation terminée avec succès!
echo ========================================
echo.
echo L'application a été installée dans: %INSTALL_DIR%
echo Un raccourci a été créé sur le bureau.
echo.
echo Vous pouvez maintenant lancer {self.config["app_name"]}
pause
'''
        
        installer_path = self.deploy_dir / f"install_{self.config['app_name']}_{environment}.bat"
        try:
            with open(installer_path, 'w', encoding='utf-8') as f:
                f.write(installer_content)
        except (IOError, OSError) as e:
            logger.error(f"Erreur lors de la création de l'installateur: {e}")
            raise
            
        logger.info(f"Installateur créé: {installer_path}")
        return installer_path
        
    def deploy_to_environment(self, environment: str, auto_start: bool = False) -> bool:
        """Déploie vers un environnement spécifique"""
        logger.info(f"Déploiement vers {environment}...")
        
        try:
            # Créer le package
            package_path = self.create_deployment_package(environment)
            if not package_path:
                return False
                
            # Créer l'installateur
            installer_path = self.create_installer(environment)
            if not installer_path:
                return False
                
            # Démarrage automatique si demandé
            if auto_start and environment == "development":
                self.start_application(environment)
                
            logger.info(f"Déploiement vers {environment} terminé avec succès")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors du déploiement: {e}")
            return False
            
    def start_application(self, environment: str) -> None:
        """Démarre l'application dans l'environnement spécifié"""
        if environment not in self.config["environments"]:
            logger.error(f"Environnement '{environment}' non configuré")
            return

        env_config = self.config["environments"][environment]
        deploy_path = Path(env_config["path"])
        exe_path = deploy_path / f"{self.config['app_name']}.exe"

        if exe_path.exists():
            logger.info(f"Démarrage de l'application ({environment})...")
            try:
                subprocess.Popen([str(exe_path)], cwd=str(deploy_path))
            except (OSError, subprocess.SubprocessError) as e:
                logger.error(f"Erreur lors du démarrage de l'application: {e}")
        else:
            logger.error(f"Exécutable non trouvé: {exe_path}")
            
    def get_deployment_info(self) -> Dict[str, Any]:
        """Retourne les informations de déploiement"""
        info: Dict[str, Any] = {
            "app_name": self.config["app_name"],
            "version": self.config["version"],
            "environments": {},
            "packages": []
        }
        
        # Informations sur les environnements
        for env_name, env_config in self.config["environments"].items():
            deploy_path = Path(env_config["path"])
            exe_path = deploy_path / f"{self.config['app_name']}.exe"
            
            info["environments"][env_name] = {
                "path": str(deploy_path),
                "deployed": exe_path.exists(),
                "config": env_config
            }
            
        # Liste des packages disponibles
        if self.deploy_dir.exists():
            try:
                for zip_file in self.deploy_dir.glob("*.zip"):
                    file_stat = zip_file.stat()
                    info["packages"].append({
                        "name": zip_file.name,
                        "size": file_stat.st_size,
                        "created": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                    })
            except (OSError, ValueError) as e:
                logger.warning(f"Erreur lors de la lecture des packages: {e}")
                
        return info

def main() -> None:
    """Fonction principale"""
    try:
        deployer = DeploymentManager()
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation du gestionnaire de déploiement: {e}")
        sys.exit(1)

    if len(sys.argv) < 2:
        print("Usage: python deploy.py <command> [options]")
        print("Commands:")
        print("  package <env>     - Créer un package de déploiement")
        print("  install <env>     - Créer un installateur")
        print("  deploy <env>      - Déployer vers un environnement")
        print("  start <env>       - Démarrer l'application")
        print("  info              - Afficher les informations de déploiement")
        print("Environments: development, staging, production")
        return
        
    command = sys.argv[1].lower()

    try:
        if command == "package":
            env = sys.argv[2] if len(sys.argv) > 2 else "production"
            result = deployer.create_deployment_package(env)
            if not result:
                sys.exit(1)

        elif command == "install":
            env = sys.argv[2] if len(sys.argv) > 2 else "production"
            result = deployer.create_installer(env)
            if not result:
                sys.exit(1)

        elif command == "deploy":
            env = sys.argv[2] if len(sys.argv) > 2 else "production"
            auto_start = "--start" in sys.argv
            success = deployer.deploy_to_environment(env, auto_start)
            sys.exit(0 if success else 1)

        elif command == "start":
            env = sys.argv[2] if len(sys.argv) > 2 else "development"
            deployer.start_application(env)

        elif command == "info":
            info = deployer.get_deployment_info()
            print(json.dumps(info, indent=2, ensure_ascii=False))

        else:
            print(f"Commande inconnue: {command}")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Erreur lors de l'exécution de la commande '{command}': {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()