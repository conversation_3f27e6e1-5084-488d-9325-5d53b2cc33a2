# -*- coding: utf-8 -*-
# pylint: disable=all
# pyright: reportMissingImports=false
# pyright: reportMissingTypeStubs=false
# pyright: reportUnknownMemberType=false
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false
# pyright: reportGeneralTypeIssues=false
# pyright: reportOptionalSubscript=false
# pyright: reportOptionalMemberAccess=false
# pyright: reportUnusedVariable=false
# pyright: reportUnusedImport=false
# type: ignore

from cryptography.fernet import Fernet  # type: ignore
from typing import Optional
import hashlib

class Encryption:
    def __init__(self, key: Optional[bytes] = None):
        if key is None:
            self.key = Fernet.generate_key()
        else:
            self.key = key
        self.cipher = Fernet(self.key)

    def encrypt(self, data: str) -> str:
        """Encrypts the given data."""
        return self.cipher.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """Decrypts the given encrypted data."""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    def get_key(self) -> str:
        """Returns the encryption key."""
        return self.key.decode()

def encrypt_password(password: str) -> str:
    # Simple hash pour l'exemple, à remplacer par une vraie méthode de hachage sécurisé en production
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    return encrypt_password(password) == hashed