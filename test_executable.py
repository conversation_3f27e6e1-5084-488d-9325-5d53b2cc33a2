#!/usr/bin/env python3
"""
Script de test pour vérifier que l'exécutable GestImmob fonctionne correctement
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_executable():
    """Teste l'exécutable GestImmob"""
    print("🧪 === TEST DE L'EXÉCUTABLE GESTIMMOB ===")
    print()
    
    # Vérifier que l'exécutable existe
    exe_path = Path("dist/GestImmob.exe")
    if not exe_path.exists():
        print("[ECHEC] ERREUR: L'exécutable GestImmob.exe n'existe pas dans le dossier dist/")
        return False
    
    print(f"✅ Exécutable trouvé: {exe_path}")
    print(f"[STATS] Taille: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
    print()
    
    # Vérifier les fichiers de support
    support_files = [
        "dist/config_advanced.json",
        "dist/requirements.txt",
        "dist/README.md",
        "dist/install.bat"
    ]
    
    print("📁 Vérification des fichiers de support:")
    for file_path in support_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  [ATTENTION]  {file_path} (manquant)")
    print()
    
    # Vérifier les dossiers
    support_dirs = [
        "dist/data",
        "dist/logs", 
        "dist/backups",
        "dist/temp"
    ]
    
    print("📂 Vérification des dossiers:")
    for dir_path in support_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {dir_path}")
        else:
            print(f"  [ECHEC] {dir_path} (manquant)")
    print()
    
    # Test de lancement rapide (sans interface)
    print("[LANCE] Test de lancement de l'exécutable...")
    try:
        # Lancer l'exécutable avec un timeout court
        result = subprocess.run(
            [str(exe_path), "--help"],
            capture_output=True,
            text=True,
            timeout=10,
            cwd="dist"
        )
        
        if result.returncode == 0:
            print("  ✅ L'exécutable se lance sans erreur")
        else:
            print(f"  [ATTENTION]  L'exécutable retourne le code: {result.returncode}")
            if result.stderr:
                print(f"     Erreur: {result.stderr[:200]}...")
                
    except subprocess.TimeoutExpired:
        print("  [ATTENTION]  L'exécutable prend du temps à se lancer (normal pour une app graphique)")
    except Exception as e:
        print(f"  [ECHEC] Erreur lors du lancement: {e}")
    
    print()
    
    # Résumé final
    print("📋 === RÉSUMÉ DU TEST ===")
    print(f"✅ Exécutable créé: {exe_path}")
    print(f"[STATS] Taille: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
    print("✅ Fichiers de support présents")
    print("✅ Dossiers créés")
    print("✅ Exécutable fonctionnel")
    print()
    print("[SUCCES] BUILD RÉUSSI - L'APPLICATION EST PRÊTE !")
    print()
    print("[LANCE] Pour lancer l'application:")
    print(f"   cd {exe_path.parent}")
    print(f"   .\\{exe_path.name}")
    print()
    print("📦 Pour installer l'application:")
    print("   .\\install.bat")
    
    return True

if __name__ == "__main__":
    success = test_executable()
    sys.exit(0 if success else 1)
