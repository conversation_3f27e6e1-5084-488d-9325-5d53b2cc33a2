<!-- Generated by SpecStory -->

# /fix La déclaration de méthode « closeEvent » est  (2025-06-08 11:53:39)

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Il y a deux définitions de la méthode `closeEvent` dans la classe `InventaireWidget`, ce qui masque la première par la seconde. Il faut supprimer l'une des deux pour corriger le problème. Gardons la version la plus complète (celle qui sauvegarde l'inventaire si des changements sont détectés).

Voici le correctif :

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

# ...supprimer la deuxième définition de closeEvent ici...

# ...existing code...
````

Supprimez donc la seconde occurrence de `def closeEvent(self, event: QCloseEvent):` et son contenu.

---

_**User**_

/fix La déclaration de méthode « closeEvent » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez la seconde définition de `closeEvent` pour éviter le masquage de méthode. Voici la modification à appliquer :

````python
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_inventaire()
        event.accept()

    # ...supprimez la deuxième définition de closeEvent ici (juste après sauvegarde_auto)...

# ...existing code...
````

Résumé : gardez une seule définition de `closeEvent` dans la classe. Supprimez la seconde occurrence et son contenu.

---

