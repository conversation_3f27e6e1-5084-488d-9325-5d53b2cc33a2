<!-- Generated by SpecStory -->

# /fix Le type de retour « Unknown | None » est part (2025-06-08 14:09:34)

_**User**_

/fix Le type de retour « Unknown | None » est partiellement inconnu

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Le problème vient du fait que la méthode `execute_query` n'a pas d'annotation de type de retour explicite. Il faut préciser le type de retour, qui peut être une liste (résultat de `fetchall()`) ou `None`. On utilise donc `Optional[list]` ou, pour plus de précision, `Optional[list[Any]]`.

Voici la correction :

````python
# ...existing code...
from typing import Any, Dict, Optional, List
# ...existing code...
    def execute_query(self, query_name: str, connection, params: dict = None) -> Optional[List[Any]]:
        logger.info(f"Executing query: {query_name}")
        query = self.get_query(query_name)
        if not query:
            raise ValueError(f"Query {query_name} not found")
        sql = self.preprocess_sql(query.sql_content)
        if params:
            sql = sql.format(**params)
        cursor = connection.cursor()
        cursor.execute(sql)
        if query.live_answer:
            return cursor.fetchall()
        return None
# ...existing code...
````

---

