@echo off
title 🚀 CRÉATION BUREAU IMMÉDIATE
color 0A
chcp 65001 >nul

echo 🚀 CRÉATION DOSSIER BUREAU MAINTENANT !
echo.

set "BUREAU=%USERPROFILE%\Desktop"
set "ROBOT=%BUREAU%\RobotIA_SamNord"

echo 📁 Création dossier: %ROBOT%
mkdir "%ROBOT%" 2>nul
mkdir "%ROBOT%\modules" 2>nul

echo 📋 Copie fichiers...
copy "%~dp0*.*" "%ROBOT%\" >nul 2>&1
xcopy "%~dp0modules" "%ROBOT%\modules\" /E /I /Q >nul 2>&1

echo 🚀 Création lanceur...
(
echo @echo off
echo title 🤖 ROBOT IA - SamNord@110577
echo color 0A
echo echo 🤖 ROBOT IA AUTONOME PC
echo echo 👤 SamNord@110577
echo echo 🔑 NorDine@22
echo echo.
echo python robot_autonome_pc.py 2^>nul ^|^| ^(
echo   echo ❌ Python requis - python.org
echo   pause
echo ^)
) > "%ROBOT%\🚀 LANCER.bat"

echo 🔗 Création raccourci...
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\s.vbs"
echo Set oLink = oWS.CreateShortcut("%BUREAU%\🤖 Robot IA - SamNord.lnk") >> "%TEMP%\s.vbs"
echo oLink.TargetPath = "%ROBOT%\🚀 LANCER.bat" >> "%TEMP%\s.vbs"
echo oLink.Save >> "%TEMP%\s.vbs"
cscript "%TEMP%\s.vbs" >nul 2>&1
del "%TEMP%\s.vbs" >nul 2>&1

echo ✅ TERMINÉ !
echo 📁 Dossier: %ROBOT%
echo 🔗 Raccourci: 🤖 Robot IA - SamNord

start "" explorer "%BUREAU%"

echo 🎉 VÉRIFIEZ VOTRE BUREAU !
pause
