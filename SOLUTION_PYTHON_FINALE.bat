@echo off
title SOLUTION PYTHON FINALE - Resolution Complete et Definitive
color 0E

echo.
echo ========================================================================
echo                    SOLUTION PYTHON FINALE
echo              Resolution complete et definitive du probleme Python
echo                        TOUTES LES METHODES INCLUSES
echo ========================================================================
echo.

REM Variables
set "PYTHON_RESOLU=0"
set "METHODE_UTILISEE="
set "ADMIN_PRIVILEGES=0"

echo [%TIME%] Demarrage de la solution Python finale...

REM Vérification privilèges administrateur
echo.
echo [%TIME%] Verification des privileges administrateur...

net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Privileges administrateur: DETECTES
    set /a ADMIN_PRIVILEGES=1
) else (
    echo ⚠️ Privileges administrateur: NON DETECTES
    echo    (Certaines solutions necessiteront des privileges eleves)
)

REM Affichage du menu des solutions
echo.
echo ========================================================================
echo                           SOLUTIONS DISPONIBLES
echo ========================================================================
echo.
echo Ce script propose TOUTES les solutions possibles pour resoudre
echo definitivement le probleme Python agacant:
echo.
echo 📋 METHODES DISPONIBLES:
echo.
echo 1. 🔍 DIAGNOSTIC AVANCE
echo    - Analyse complete et detaillee de tous les problemes
echo    - Rapport JSON genere avec recommandations
echo    - Identification precise des causes
echo.
echo 2. 🐍 RESOLVEUR PYTHON DEFINITIF
echo    - Resolution automatique de tous les problemes detectes
echo    - Installation automatique si necessaire
echo    - Correction PATH, pip, permissions
echo.
echo 3. 💻 SOLUTION POWERSHELL AVANCEE
echo    - Utilise PowerShell pour une resolution plus puissante
echo    - Telechargement et installation automatiques
echo    - Gestion avancee des permissions Windows
echo.
echo 4. 🔧 RESOLUTION MANUELLE GUIDEE
echo    - Guide etape par etape pour installation manuelle
echo    - Verification de chaque etape
echo    - Support pour tous les cas particuliers
echo.
echo 5. 🚀 SOLUTION COMPLETE AUTOMATIQUE
echo    - Combine toutes les methodes ci-dessus
echo    - Essaie chaque solution jusqu a resolution
echo    - Garantit la resolution du probleme
echo.

:menu_principal
echo.
set /p choix="Choisissez une methode (1-5) ou 'q' pour quitter: "

if /i "%choix%"=="q" goto :end
if "%choix%"=="1" goto :diagnostic_avance
if "%choix%"=="2" goto :resolveur_definitif
if "%choix%"=="3" goto :solution_powershell
if "%choix%"=="4" goto :resolution_manuelle
if "%choix%"=="5" goto :solution_complete
echo ❌ Choix invalide. Veuillez choisir 1-5 ou 'q'
goto :menu_principal

:diagnostic_avance
echo.
echo ========================================================================
echo                           DIAGNOSTIC AVANCE
echo ========================================================================
echo.

echo 🔍 Lancement du diagnostic avance...

if exist "DIAGNOSTIC_PYTHON_AVANCE.py" (
    REM Essayer avec différentes commandes Python
    python DIAGNOSTIC_PYTHON_AVANCE.py 2>nul
    if not errorlevel 1 (
        echo ✅ Diagnostic execute avec 'python'
        set "METHODE_UTILISEE=Diagnostic avance"
        goto :diagnostic_complete
    )
    
    python3 DIAGNOSTIC_PYTHON_AVANCE.py 2>nul
    if not errorlevel 1 (
        echo ✅ Diagnostic execute avec 'python3'
        set "METHODE_UTILISEE=Diagnostic avance"
        goto :diagnostic_complete
    )
    
    py DIAGNOSTIC_PYTHON_AVANCE.py 2>nul
    if not errorlevel 1 (
        echo ✅ Diagnostic execute avec 'py'
        set "METHODE_UTILISEE=Diagnostic avance"
        goto :diagnostic_complete
    )
    
    echo ❌ Impossible d executer le diagnostic (Python non fonctionnel)
    echo 💡 Essayez la methode 2 ou 3 pour installer Python d abord
) else (
    echo ❌ Fichier DIAGNOSTIC_PYTHON_AVANCE.py non trouve
)

goto :menu_principal

:diagnostic_complete
echo.
echo ✅ Diagnostic termine !
echo 📄 Consultez le fichier diagnostic_python_rapport.json pour les details
echo.
set /p continuer="Continuer avec une autre methode ? (o/n): "
if /i "%continuer%"=="o" goto :menu_principal
goto :end

:resolveur_definitif
echo.
echo ========================================================================
echo                           RESOLVEUR DEFINITIF
echo ========================================================================
echo.

echo 🔧 Lancement du resolveur Python definitif...

if exist "RESOUDRE_PYTHON_DEFINITIF.py" (
    REM Essayer avec différentes commandes Python
    python RESOUDRE_PYTHON_DEFINITIF.py 2>nul
    if not errorlevel 1 (
        echo ✅ Resolution executee avec 'python'
        set "METHODE_UTILISEE=Resolveur definitif"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
    
    python3 RESOUDRE_PYTHON_DEFINITIF.py 2>nul
    if not errorlevel 1 (
        echo ✅ Resolution executee avec 'python3'
        set "METHODE_UTILISEE=Resolveur definitif"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
    
    py RESOUDRE_PYTHON_DEFINITIF.py 2>nul
    if not errorlevel 1 (
        echo ✅ Resolution executee avec 'py'
        set "METHODE_UTILISEE=Resolveur definitif"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
    
    echo ❌ Impossible d executer le resolveur (Python non fonctionnel)
    echo 💡 Essayez la methode 3 (PowerShell) pour installer Python
) else (
    echo ❌ Fichier RESOUDRE_PYTHON_DEFINITIF.py non trouve
)

goto :menu_principal

:solution_powershell
echo.
echo ========================================================================
echo                           SOLUTION POWERSHELL
echo ========================================================================
echo.

echo 💻 Lancement de la solution PowerShell avancee...

if exist "RESOUDRE_PYTHON_POWERSHELL.ps1" (
    echo 🔧 Execution du script PowerShell...
    
    powershell -ExecutionPolicy Bypass -File "RESOUDRE_PYTHON_POWERSHELL.ps1"
    
    if not errorlevel 1 (
        echo ✅ Solution PowerShell executee
        set "METHODE_UTILISEE=Solution PowerShell"
        
        REM Tester si Python fonctionne maintenant
        python --version >nul 2>&1
        if not errorlevel 1 (
            echo ✅ Python maintenant fonctionnel !
            set /a PYTHON_RESOLU=1
            goto :resolution_complete
        )
        
        py --version >nul 2>&1
        if not errorlevel 1 (
            echo ✅ Python maintenant fonctionnel !
            set /a PYTHON_RESOLU=1
            goto :resolution_complete
        )
        
        echo ⚠️ Script execute mais Python pas encore fonctionnel
        echo 💡 Un redemarrage peut etre necessaire
    ) else (
        echo ❌ Echec execution PowerShell
    )
) else (
    echo ❌ Fichier RESOUDRE_PYTHON_POWERSHELL.ps1 non trouve
)

goto :menu_principal

:resolution_manuelle
echo.
echo ========================================================================
echo                           RESOLUTION MANUELLE GUIDEE
echo ========================================================================
echo.

echo 📋 GUIDE COMPLET D INSTALLATION MANUELLE PYTHON
echo.
echo 🎯 OBJECTIF: Installer Python correctement et definitivement
echo.
echo 📥 ETAPE 1: TELECHARGEMENT
echo -------------------------
echo 1. Ouvrez votre navigateur web
echo 2. Allez sur: https://www.python.org/downloads/
echo 3. Cliquez sur le gros bouton "Download Python 3.x.x"
echo 4. Attendez la fin du telechargement
echo.

set /p etape1="Etape 1 terminee ? (o/n): "
if /i not "%etape1%"=="o" (
    echo 💡 Ouvrons la page de telechargement...
    start https://www.python.org/downloads/
    echo ✅ Page ouverte dans le navigateur
    set /p etape1_bis="Telechargement termine ? (o/n): "
    if /i not "%etape1_bis%"=="o" goto :menu_principal
)

echo.
echo 🔧 ETAPE 2: INSTALLATION
echo -------------------------
echo 1. Localisez le fichier telecharge (generalement dans Telechargements)
echo 2. Double-cliquez sur le fichier python-3.x.x-amd64.exe
echo 3. ✅ TRES IMPORTANT: Cochez "Add Python to PATH"
echo 4. Cliquez "Install Now"
echo 5. Acceptez les demandes UAC si necessaire
echo 6. Attendez la fin de l installation
echo.

set /p etape2="Etape 2 terminee ? (o/n): "
if /i not "%etape2%"=="o" goto :menu_principal

echo.
echo 🔄 ETAPE 3: REDEMARRAGE
echo ------------------------
echo 1. Fermez TOUTES les fenetres d invite de commande
echo 2. Redemarrez votre ordinateur
echo 3. Apres redemarrage, ouvrez une nouvelle invite de commande
echo 4. Tapez: python --version
echo 5. Vous devriez voir la version Python
echo.

set /p etape3="Etape 3 terminee et Python fonctionne ? (o/n): "
if /i "%etape3%"=="o" (
    echo ✅ Installation manuelle reussie !
    set "METHODE_UTILISEE=Installation manuelle guidee"
    set /a PYTHON_RESOLU=1
    goto :resolution_complete
) else (
    echo ⚠️ Si Python ne fonctionne toujours pas:
    echo 1. Verifiez que vous avez bien coche "Add Python to PATH"
    echo 2. Redemarrez encore une fois
    echo 3. Essayez 'py --version' au lieu de 'python --version'
    echo 4. Relancez ce script et choisissez une autre methode
)

goto :menu_principal

:solution_complete
echo.
echo ========================================================================
echo                           SOLUTION COMPLETE AUTOMATIQUE
echo ========================================================================
echo.

echo 🚀 Lancement de la solution complete automatique...
echo Cette methode va essayer toutes les solutions jusqu a resolution !
echo.

REM Méthode 1: Diagnostic d'abord
echo 🔍 TENTATIVE 1: Diagnostic avance
if exist "DIAGNOSTIC_PYTHON_AVANCE.py" (
    python DIAGNOSTIC_PYTHON_AVANCE.py >nul 2>&1 || python3 DIAGNOSTIC_PYTHON_AVANCE.py >nul 2>&1 || py DIAGNOSTIC_PYTHON_AVANCE.py >nul 2>&1
    echo ✅ Diagnostic execute
)

REM Méthode 2: Résolveur Python
echo 🔧 TENTATIVE 2: Resolveur definitif
if exist "RESOUDRE_PYTHON_DEFINITIF.py" (
    python RESOUDRE_PYTHON_DEFINITIF.py >nul 2>&1 || python3 RESOUDRE_PYTHON_DEFINITIF.py >nul 2>&1 || py RESOUDRE_PYTHON_DEFINITIF.py >nul 2>&1
    
    REM Test si résolu
    python --version >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Python resolu avec le resolveur definitif !
        set "METHODE_UTILISEE=Resolveur definitif (automatique)"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
)

REM Méthode 3: PowerShell
echo 💻 TENTATIVE 3: Solution PowerShell
if exist "RESOUDRE_PYTHON_POWERSHELL.ps1" (
    powershell -ExecutionPolicy Bypass -File "RESOUDRE_PYTHON_POWERSHELL.ps1" >nul 2>&1
    
    REM Test si résolu
    python --version >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Python resolu avec PowerShell !
        set "METHODE_UTILISEE=Solution PowerShell (automatique)"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
    
    py --version >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Python resolu avec PowerShell !
        set "METHODE_UTILISEE=Solution PowerShell (automatique)"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
)

REM Méthode 4: Installation directe
echo 📦 TENTATIVE 4: Installation directe
echo Tentative de telechargement et installation automatique...

powershell -Command "try { $url = 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe'; $output = 'python_auto_installer.exe'; Invoke-WebRequest -Uri $url -OutFile $output; Write-Host 'Telechargement OK' } catch { Write-Host 'Echec' }" >nul 2>&1

if exist "python_auto_installer.exe" (
    echo ✅ Telechargement reussi, installation...
    python_auto_installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 >nul 2>&1
    
    REM Attendre l'installation
    timeout /t 30 /nobreak >nul
    
    REM Nettoyer
    del python_auto_installer.exe >nul 2>&1
    
    REM Test final
    python --version >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Python resolu avec installation directe !
        set "METHODE_UTILISEE=Installation directe (automatique)"
        set /a PYTHON_RESOLU=1
        goto :resolution_complete
    )
)

echo ❌ Toutes les methodes automatiques ont echoue
echo 💡 Essayez la methode 4 (Resolution manuelle guidee)
goto :menu_principal

:resolution_complete
echo.
echo ========================================================================
echo                              RESOLUTION COMPLETE
echo ========================================================================
echo.

echo [%TIME%] 🎉 PROBLEME PYTHON DEFINITIVEMENT RESOLU !
echo.
echo ✅ PYTHON MAINTENANT PARFAITEMENT FONCTIONNEL !
echo.
echo 🏆 METHODE UTILISEE: %METHODE_UTILISEE%
echo.

REM Tests finaux
echo 🧪 TESTS FINAUX:
echo.

python --version 2>nul
if not errorlevel 1 (
    echo ✅ python --version: OK
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo    Version: %%i
) else (
    py --version 2>nul
    if not errorlevel 1 (
        echo ✅ py --version: OK
        for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo    Version: %%i
    )
)

python -m pip --version 2>nul
if not errorlevel 1 (
    echo ✅ pip: OK
) else (
    echo ⚠️ pip: A installer avec 'python -m ensurepip --upgrade'
)

echo.
echo 🚀 COMMANDES PYTHON MAINTENANT DISPONIBLES:
echo   python --version
echo   python script.py
echo   python -m pip install package
echo   python -c "print('Hello World')"
echo.

echo 🎯 LE PROBLEME PYTHON AGACANT EST DEFINITIVEMENT RESOLU !
echo.

REM Test avec GestImmob si présent
if exist "src\main.py" (
    echo 🏠 PROJET GESTIMMOB DETECTE !
    echo.
    set /p test_gestimmob="Tester le lancement de GestImmob maintenant ? (o/n): "
    if /i "%test_gestimmob%"=="o" (
        echo.
        echo 🚀 Test de lancement GestImmob...
        python src\main.py 2>nul || py src\main.py 2>nul
        if not errorlevel 1 (
            echo ✅ GestImmob lance parfaitement !
        ) else (
            echo ⚠️ GestImmob necessite des dependances
            echo 💡 Installez-les avec: python -m pip install -r requirements.txt
        )
    )
)

goto :end

:end
echo.
echo ========================================================================
echo                              RESUME FINAL
echo ========================================================================
echo.

if %PYTHON_RESOLU%==1 (
    echo ✅ STATUT: PYTHON COMPLETEMENT RESOLU
    echo ✅ METHODE: %METHODE_UTILISEE%
    echo ✅ RESULTAT: PROBLEME AGACANT DEFINITIVEMENT CORRIGE
    echo.
    echo 🎉 FELICITATIONS ! Python fonctionne maintenant parfaitement.
    echo Vous pouvez utiliser Python sans aucun probleme.
) else (
    echo ⚠️ STATUT: RESOLUTION PARTIELLE OU INCOMPLETE
    echo.
    echo 📋 ACTIONS RECOMMANDEES:
    echo 1. Essayez la methode 4 (Resolution manuelle guidee)
    echo 2. Redemarrez votre ordinateur
    echo 3. Relancez ce script
    echo 4. Contactez le support si le probleme persiste
)

echo.
echo 📄 OUTILS CREES POUR RESOUDRE PYTHON:
if exist "DIAGNOSTIC_PYTHON_AVANCE.py" echo   🔧 DIAGNOSTIC_PYTHON_AVANCE.py
if exist "RESOUDRE_PYTHON_DEFINITIF.py" echo   🔧 RESOUDRE_PYTHON_DEFINITIF.py
if exist "RESOUDRE_PYTHON_POWERSHELL.ps1" echo   🔧 RESOUDRE_PYTHON_POWERSHELL.ps1
echo   📄 SOLUTION_PYTHON_FINALE.bat (ce script)

echo.
echo [%TIME%] Solution Python finale terminee.
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
