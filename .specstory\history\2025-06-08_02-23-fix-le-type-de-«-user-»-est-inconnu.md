<!-- Generated by SpecStory -->

# /fix Le type de « User » est inconnu (2025-06-08 03:23:17)

_**User**_

/fix Le type de « User » est inconnu

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Le problème vient de l'importation conditionnelle de `User` depuis `models`, alors que la classe `User` est déjà définie dans ce fichier. Il n'est donc pas nécessaire d'importer `User` depuis un autre module. Il suffit de supprimer la ligne problématique.

````python
# ...existing code...
if TYPE_CHECKING:
    pass  # Suppression de l'import inutile de User
# ...existing code...
````

---

