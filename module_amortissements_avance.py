#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MODULE AMORTISSEMENTS AVANCÉ - MASTER COMPTA
Multi-fenêtres avec ordre et dépendances de priorités
Gestion Immobilière et Patrimoniale pour Grandes Entreprises
"""

import sys
import os
import json
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
import requests
import logging
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QFrame, QTableWidget, QTableWidgetItem, QLineEdit,
    QComboBox, QSpinBox, QTextEdit, QGroupBox, QTabWidget, QMessageBox,
    QCheckBox, QDateEdit, QProgressBar, QListWidget, QSplitter, QFormLayout,
    QDoubleSpinBox, Q<PERSON>alendarWidget, Q<PERSON><PERSON><PERSON><PERSON>t, QTreeWidgetItem
)
from PySide6.QtCore import Qt, QTimer, QDateTime, QThread, Signal, QDate
from PySide6.QtGui import QFont, QColor, QPalette
from PySide6.QtCharts import QChart, QChartView, QLineSeries, QValueAxis

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('amortissements.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Gestionnaire de base de données pour amortissements"""
    
    def __init__(self, db_path="amortissements.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialise la base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table des actifs
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                acquisition_date DATE NOT NULL,
                initial_value DECIMAL(12,2) NOT NULL,
                amortization_period INTEGER NOT NULL,
                service TEXT NOT NULL,
                current_value DECIMAL(12,2),
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Table des écritures comptables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounting_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id INTEGER,
                entry_date DATE NOT NULL,
                amount DECIMAL(12,2) NOT NULL,
                type TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (asset_id) REFERENCES assets (id)
            )
        """)
        
        # Table des prédictions IA
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ia_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id INTEGER,
                prediction_date DATE NOT NULL,
                predicted_value DECIMAL(12,2),
                confidence_score DECIMAL(3,2),
                model_used TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (asset_id) REFERENCES assets (id)
            )
        """)
        
        conn.commit()
        conn.close()
        logger.info("Base de données initialisée")

class OllamaIntegration:
    """Intégration Ollama pour prédictions comptables"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "llama3.2"
    
    def predict_depreciation(self, asset_data):
        """Prédit la dépréciation avec Ollama"""
        prompt = f"""
        Analyse comptable pour l'actif {asset_data['code']}:
        - Valeur initiale: {asset_data['initial_value']} €
        - Date acquisition: {asset_data['acquisition_date']}
        - Période amortissement: {asset_data['amortization_period']} ans
        - Service: {asset_data['service']}
        
        Calcule la dépréciation optimale et donne des recommandations.
        Réponds en JSON avec: valeur_future, recommandations, risques
        """
        
        try:
            response = requests.post(
                self.ollama_url,
                json={"model": self.model, "prompt": prompt, "stream": False},
                timeout=60
            )
            if response.status_code == 200:
                return response.json().get("response", "Erreur de prédiction")
            else:
                return f"Erreur HTTP: {response.status_code}"
        except Exception as e:
            return f"Erreur Ollama: {str(e)}"

class AmortissementCalculator:
    """Calculateur d'amortissements avancé"""
    
    @staticmethod
    def calculate_linear_depreciation(initial_value, period_years, months_elapsed):
        """Amortissement linéaire"""
        monthly_depreciation = initial_value / (period_years * 12)
        total_depreciation = monthly_depreciation * months_elapsed
        return max(0, initial_value - total_depreciation)
    
    @staticmethod
    def calculate_degressive_depreciation(initial_value, period_years, months_elapsed, rate=2.0):
        """Amortissement dégressif"""
        annual_rate = rate / period_years
        years_elapsed = months_elapsed / 12
        current_value = initial_value * ((1 - annual_rate) ** years_elapsed)
        return max(0, current_value)
    
    @staticmethod
    def calculate_exceptional_depreciation(initial_value, exceptional_rate):
        """Amortissement exceptionnel"""
        return initial_value * (1 - exceptional_rate / 100)

class FenetreGestionActifs(QMainWindow):
    """Fenêtre 1: Gestion des Actifs - Priorité 1"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de gestion des actifs"""
        self.setWindowTitle("🏢 GESTION DES ACTIFS - Priorité 1")
        self.setFixedSize(1000, 700)
        
        # Style VSCode
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #d4d4d4;
            }
            QWidget {
                background-color: #1e1e1e;
                color: #d4d4d4;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                background-color: #252526;
                border: 1px solid #0066ff;
                border-radius: 6px;
                margin: 8px;
                padding-top: 15px;
                font-weight: bold;
            }
            QGroupBox::title {
                color: #d4d4d4;
                background-color: #2d2d30;
                border: 1px solid #0066ff;
                border-radius: 4px;
                padding: 5px 10px;
                margin-left: 10px;
            }
            QPushButton {
                background-color: #0e639c;
                color: white;
                border: 1px solid #0066ff;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1177bb;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #3c3c3c;
                color: #d4d4d4;
                border: 1px solid #0066ff;
                border-radius: 4px;
                padding: 6px;
            }
            QTableWidget {
                background-color: #252526;
                color: #d4d4d4;
                gridline-color: #0066ff;
                border: 1px solid #0066ff;
            }
            QHeaderView::section {
                background-color: #2d2d30;
                color: #d4d4d4;
                border: 1px solid #0066ff;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        titre = QLabel("🏢 GESTION DES ACTIFS IMMOBILIERS")
        titre.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titre.setStyleSheet("font-size: 16pt; font-weight: bold; padding: 10px;")
        layout.addWidget(titre)
        
        # Formulaire d'ajout
        self.create_form_ajout(layout)
        
        # Tableau des actifs
        self.create_table_actifs(layout)
        
        # Boutons d'action
        self.create_boutons_action(layout)
        
        # Chargement initial
        self.charger_actifs()
    
    def create_form_ajout(self, layout):
        """Formulaire d'ajout d'actifs"""
        form_group = QGroupBox("➕ AJOUTER UN NOUVEL ACTIF")
        form_layout = QFormLayout(form_group)
        
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("IT-BAT-001")
        form_layout.addRow("Code:", self.code_input)
        
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nom de l'actif")
        form_layout.addRow("Nom:", self.name_input)
        
        self.value_input = QDoubleSpinBox()
        self.value_input.setMaximum(999999999.99)
        self.value_input.setSuffix(" €")
        form_layout.addRow("Valeur initiale:", self.value_input)
        
        self.period_input = QSpinBox()
        self.period_input.setMinimum(1)
        self.period_input.setMaximum(50)
        self.period_input.setSuffix(" ans")
        form_layout.addRow("Période amortissement:", self.period_input)
        
        self.service_input = QComboBox()
        self.service_input.addItems(["IT", "Finance", "RH", "Production", "Commercial"])
        form_layout.addRow("Service:", self.service_input)
        
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        form_layout.addRow("Date acquisition:", self.date_input)
        
        # Bouton ajouter
        btn_ajouter = QPushButton("➕ AJOUTER ACTIF")
        btn_ajouter.clicked.connect(self.ajouter_actif)
        form_layout.addRow(btn_ajouter)
        
        layout.addWidget(form_group)
    
    def create_table_actifs(self, layout):
        """Tableau des actifs"""
        table_group = QGroupBox("📋 LISTE DES ACTIFS")
        table_layout = QVBoxLayout(table_group)
        
        self.table_actifs = QTableWidget()
        self.table_actifs.setColumnCount(7)
        self.table_actifs.setHorizontalHeaderLabels([
            "Code", "Nom", "Valeur Initiale", "Valeur Actuelle", 
            "Période", "Service", "Date Acquisition"
        ])
        table_layout.addWidget(self.table_actifs)
        
        layout.addWidget(table_group)
    
    def create_boutons_action(self, layout):
        """Boutons d'action"""
        boutons_layout = QHBoxLayout()
        
        btn_calculer = QPushButton("🧮 CALCULER AMORTISSEMENTS")
        btn_calculer.clicked.connect(self.ouvrir_fenetre_calculs)
        boutons_layout.addWidget(btn_calculer)
        
        btn_predictions = QPushButton("🤖 PRÉDICTIONS IA")
        btn_predictions.clicked.connect(self.ouvrir_fenetre_predictions)
        boutons_layout.addWidget(btn_predictions)
        
        btn_rapports = QPushButton("📊 RAPPORTS")
        btn_rapports.clicked.connect(self.ouvrir_fenetre_rapports)
        boutons_layout.addWidget(btn_rapports)
        
        layout.addLayout(boutons_layout)
    
    def ajouter_actif(self):
        """Ajoute un nouvel actif"""
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO assets (code, name, initial_value, amortization_period, 
                                  service, acquisition_date, current_value)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                self.code_input.text(),
                self.name_input.text(),
                self.value_input.value(),
                self.period_input.value(),
                self.service_input.currentText(),
                self.date_input.date().toString("yyyy-MM-dd"),
                self.value_input.value()  # Valeur initiale = valeur actuelle au début
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "Succès", "Actif ajouté avec succès!")
            self.charger_actifs()
            self.clear_form()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")
    
    def charger_actifs(self):
        """Charge les actifs dans le tableau"""
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT code, name, initial_value, current_value, amortization_period, service, acquisition_date FROM assets")
        actifs = cursor.fetchall()
        
        self.table_actifs.setRowCount(len(actifs))
        for row, actif in enumerate(actifs):
            for col, value in enumerate(actif):
                self.table_actifs.setItem(row, col, QTableWidgetItem(str(value)))
        
        conn.close()
    
    def clear_form(self):
        """Vide le formulaire"""
        self.code_input.clear()
        self.name_input.clear()
        self.value_input.setValue(0)
        self.period_input.setValue(1)
        self.date_input.setDate(QDate.currentDate())
    
    def ouvrir_fenetre_calculs(self):
        """Ouvre la fenêtre de calculs - Priorité 2"""
        self.fenetre_calculs = FenetreCalculsAmortissements()
        self.fenetre_calculs.show()
    
    def ouvrir_fenetre_predictions(self):
        """Ouvre la fenêtre de prédictions IA - Priorité 3"""
        self.fenetre_predictions = FenetrePredictionsIA()
        self.fenetre_predictions.show()
    
    def ouvrir_fenetre_rapports(self):
        """Ouvre la fenêtre de rapports - Priorité 4"""
        self.fenetre_rapports = FenetreRapports()
        self.fenetre_rapports.show()

class FenetreCalculsAmortissements(QMainWindow):
    """Fenêtre 2: Calculs d'Amortissements - Priorité 2"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.calculator = AmortissementCalculator()
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface de calculs"""
        self.setWindowTitle("🧮 CALCULS D'AMORTISSEMENTS - Priorité 2")
        self.setFixedSize(1200, 800)
        self.setStyleSheet(self.get_vscode_style())

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Titre
        titre = QLabel("🧮 CALCULS D'AMORTISSEMENTS AVANCÉS")
        titre.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titre.setStyleSheet("font-size: 16pt; font-weight: bold; padding: 10px;")
        layout.addWidget(titre)

        # Onglets de calculs
        self.tabs_calculs = QTabWidget()

        # Onglet Linéaire
        self.tabs_calculs.addTab(self.create_onglet_lineaire(), "📈 LINÉAIRE")

        # Onglet Dégressif
        self.tabs_calculs.addTab(self.create_onglet_degressif(), "📉 DÉGRESSIF")

        # Onglet Exceptionnel
        self.tabs_calculs.addTab(self.create_onglet_exceptionnel(), "⚡ EXCEPTIONNEL")

        # Onglet Comparaison
        self.tabs_calculs.addTab(self.create_onglet_comparaison(), "📊 COMPARAISON")

        layout.addWidget(self.tabs_calculs)

        # Résultats
        self.create_zone_resultats(layout)

    def get_vscode_style(self):
        """Style VSCode pour toutes les fenêtres"""
        return """
            QMainWindow { background-color: #1e1e1e; color: #d4d4d4; }
            QWidget { background-color: #1e1e1e; color: #d4d4d4; font-family: 'Segoe UI'; }
            QGroupBox { background-color: #252526; border: 1px solid #0066ff; border-radius: 6px; margin: 8px; padding-top: 15px; font-weight: bold; }
            QGroupBox::title { color: #d4d4d4; background-color: #2d2d30; border: 1px solid #0066ff; border-radius: 4px; padding: 5px 10px; margin-left: 10px; }
            QPushButton { background-color: #0e639c; color: white; border: 1px solid #0066ff; border-radius: 4px; padding: 8px; font-weight: bold; }
            QPushButton:hover { background-color: #1177bb; }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { background-color: #3c3c3c; color: #d4d4d4; border: 1px solid #0066ff; border-radius: 4px; padding: 6px; }
            QTableWidget { background-color: #252526; color: #d4d4d4; gridline-color: #0066ff; border: 1px solid #0066ff; }
            QHeaderView::section { background-color: #2d2d30; color: #d4d4d4; border: 1px solid #0066ff; padding: 8px; font-weight: bold; }
            QTabWidget::pane { background-color: #252526; border: 1px solid #0066ff; }
            QTabBar::tab { background-color: #2d2d30; color: #d4d4d4; border: 1px solid #0066ff; padding: 8px 16px; margin: 2px; }
            QTabBar::tab:selected { background-color: #0e639c; }
            QTextEdit { background-color: #1e1e1e; color: #d4d4d4; border: 1px solid #0066ff; border-radius: 6px; padding: 10px; font-family: 'Consolas'; }
        """

    def create_onglet_lineaire(self):
        """Onglet calcul linéaire"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres
        params_group = QGroupBox("⚙️ PARAMÈTRES AMORTISSEMENT LINÉAIRE")
        params_layout = QFormLayout(params_group)

        self.linear_value = QDoubleSpinBox()
        self.linear_value.setMaximum(999999999.99)
        self.linear_value.setSuffix(" €")
        params_layout.addRow("Valeur initiale:", self.linear_value)

        self.linear_period = QSpinBox()
        self.linear_period.setMinimum(1)
        self.linear_period.setMaximum(50)
        self.linear_period.setSuffix(" ans")
        params_layout.addRow("Période:", self.linear_period)

        self.linear_months = QSpinBox()
        self.linear_months.setMinimum(0)
        self.linear_months.setMaximum(600)
        self.linear_months.setSuffix(" mois")
        params_layout.addRow("Mois écoulés:", self.linear_months)

        btn_calc_linear = QPushButton("🧮 CALCULER LINÉAIRE")
        btn_calc_linear.clicked.connect(self.calculer_lineaire)
        params_layout.addRow(btn_calc_linear)

        layout.addWidget(params_group)

        # Résultats linéaire
        self.resultats_lineaire = QTextEdit()
        self.resultats_lineaire.setMaximumHeight(200)
        layout.addWidget(self.resultats_lineaire)

        return widget

    def create_onglet_degressif(self):
        """Onglet calcul dégressif"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres dégressif
        params_group = QGroupBox("⚙️ PARAMÈTRES AMORTISSEMENT DÉGRESSIF")
        params_layout = QFormLayout(params_group)

        self.degressive_value = QDoubleSpinBox()
        self.degressive_value.setMaximum(999999999.99)
        self.degressive_value.setSuffix(" €")
        params_layout.addRow("Valeur initiale:", self.degressive_value)

        self.degressive_period = QSpinBox()
        self.degressive_period.setMinimum(1)
        self.degressive_period.setMaximum(50)
        self.degressive_period.setSuffix(" ans")
        params_layout.addRow("Période:", self.degressive_period)

        self.degressive_months = QSpinBox()
        self.degressive_months.setMinimum(0)
        self.degressive_months.setMaximum(600)
        self.degressive_months.setSuffix(" mois")
        params_layout.addRow("Mois écoulés:", self.degressive_months)

        self.degressive_rate = QDoubleSpinBox()
        self.degressive_rate.setMinimum(1.0)
        self.degressive_rate.setMaximum(5.0)
        self.degressive_rate.setValue(2.0)
        self.degressive_rate.setDecimals(1)
        params_layout.addRow("Coefficient:", self.degressive_rate)

        btn_calc_degressive = QPushButton("🧮 CALCULER DÉGRESSIF")
        btn_calc_degressive.clicked.connect(self.calculer_degressif)
        params_layout.addRow(btn_calc_degressive)

        layout.addWidget(params_group)

        # Résultats dégressif
        self.resultats_degressif = QTextEdit()
        self.resultats_degressif.setMaximumHeight(200)
        layout.addWidget(self.resultats_degressif)

        return widget

    def create_onglet_exceptionnel(self):
        """Onglet calcul exceptionnel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres exceptionnel
        params_group = QGroupBox("⚙️ PARAMÈTRES AMORTISSEMENT EXCEPTIONNEL")
        params_layout = QFormLayout(params_group)

        self.exceptional_value = QDoubleSpinBox()
        self.exceptional_value.setMaximum(999999999.99)
        self.exceptional_value.setSuffix(" €")
        params_layout.addRow("Valeur initiale:", self.exceptional_value)

        self.exceptional_rate = QDoubleSpinBox()
        self.exceptional_rate.setMinimum(0.0)
        self.exceptional_rate.setMaximum(100.0)
        self.exceptional_rate.setSuffix(" %")
        params_layout.addRow("Taux exceptionnel:", self.exceptional_rate)

        self.exceptional_reason = QComboBox()
        self.exceptional_reason.addItems([
            "Obsolescence technologique",
            "Dommages exceptionnels",
            "Changement réglementaire",
            "Restructuration",
            "Autre"
        ])
        params_layout.addRow("Motif:", self.exceptional_reason)

        btn_calc_exceptional = QPushButton("🧮 CALCULER EXCEPTIONNEL")
        btn_calc_exceptional.clicked.connect(self.calculer_exceptionnel)
        params_layout.addRow(btn_calc_exceptional)

        layout.addWidget(params_group)

        # Résultats exceptionnel
        self.resultats_exceptionnel = QTextEdit()
        self.resultats_exceptionnel.setMaximumHeight(200)
        layout.addWidget(self.resultats_exceptionnel)

        return widget

    def create_onglet_comparaison(self):
        """Onglet comparaison des méthodes"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres comparaison
        params_group = QGroupBox("⚙️ PARAMÈTRES COMPARAISON")
        params_layout = QFormLayout(params_group)

        self.comp_value = QDoubleSpinBox()
        self.comp_value.setMaximum(999999999.99)
        self.comp_value.setSuffix(" €")
        params_layout.addRow("Valeur initiale:", self.comp_value)

        self.comp_period = QSpinBox()
        self.comp_period.setMinimum(1)
        self.comp_period.setMaximum(50)
        self.comp_period.setSuffix(" ans")
        params_layout.addRow("Période:", self.comp_period)

        btn_comparer = QPushButton("📊 COMPARER TOUTES LES MÉTHODES")
        btn_comparer.clicked.connect(self.comparer_methodes)
        params_layout.addRow(btn_comparer)

        layout.addWidget(params_group)

        # Tableau comparaison
        self.table_comparaison = QTableWidget()
        self.table_comparaison.setColumnCount(4)
        self.table_comparaison.setHorizontalHeaderLabels([
            "Année", "Linéaire", "Dégressif", "Différence"
        ])
        layout.addWidget(self.table_comparaison)

        return widget

    def create_zone_resultats(self, layout):
        """Zone de résultats globaux"""
        resultats_group = QGroupBox("📊 RÉSULTATS GLOBAUX")
        resultats_layout = QVBoxLayout(resultats_group)

        self.resultats_globaux = QTextEdit()
        self.resultats_globaux.setMaximumHeight(150)
        self.resultats_globaux.setPlaceholderText("Les résultats des calculs apparaîtront ici...")
        resultats_layout.addWidget(self.resultats_globaux)

        layout.addWidget(resultats_group)

    def calculer_lineaire(self):
        """Calcule l'amortissement linéaire"""
        valeur_actuelle = self.calculator.calculate_linear_depreciation(
            self.linear_value.value(),
            self.linear_period.value(),
            self.linear_months.value()
        )

        amortissement_total = self.linear_value.value() - valeur_actuelle
        amortissement_mensuel = self.linear_value.value() / (self.linear_period.value() * 12)

        resultat = f"""
🧮 CALCUL AMORTISSEMENT LINÉAIRE
═══════════════════════════════════════
📊 Valeur initiale: {self.linear_value.value():,.2f} €
📅 Période: {self.linear_period.value()} ans
⏱️ Mois écoulés: {self.linear_months.value()}

💰 Valeur actuelle: {valeur_actuelle:,.2f} €
📉 Amortissement total: {amortissement_total:,.2f} €
📅 Amortissement mensuel: {amortissement_mensuel:,.2f} €
📈 Pourcentage amorti: {(amortissement_total/self.linear_value.value()*100):.2f}%
        """

        self.resultats_lineaire.setText(resultat)
        self.resultats_globaux.append(f"✅ Calcul linéaire terminé - Valeur actuelle: {valeur_actuelle:,.2f} €")

    def calculer_degressif(self):
        """Calcule l'amortissement dégressif"""
        valeur_actuelle = self.calculator.calculate_degressive_depreciation(
            self.degressive_value.value(),
            self.degressive_period.value(),
            self.degressive_months.value(),
            self.degressive_rate.value()
        )

        amortissement_total = self.degressive_value.value() - valeur_actuelle

        resultat = f"""
🧮 CALCUL AMORTISSEMENT DÉGRESSIF
═══════════════════════════════════════
📊 Valeur initiale: {self.degressive_value.value():,.2f} €
📅 Période: {self.degressive_period.value()} ans
⏱️ Mois écoulés: {self.degressive_months.value()}
🔢 Coefficient: {self.degressive_rate.value()}

💰 Valeur actuelle: {valeur_actuelle:,.2f} €
📉 Amortissement total: {amortissement_total:,.2f} €
📈 Pourcentage amorti: {(amortissement_total/self.degressive_value.value()*100):.2f}%
        """

        self.resultats_degressif.setText(resultat)
        self.resultats_globaux.append(f"✅ Calcul dégressif terminé - Valeur actuelle: {valeur_actuelle:,.2f} €")

    def calculer_exceptionnel(self):
        """Calcule l'amortissement exceptionnel"""
        valeur_actuelle = self.calculator.calculate_exceptional_depreciation(
            self.exceptional_value.value(),
            self.exceptional_rate.value()
        )

        amortissement_exceptionnel = self.exceptional_value.value() - valeur_actuelle

        resultat = f"""
🧮 CALCUL AMORTISSEMENT EXCEPTIONNEL
═══════════════════════════════════════
📊 Valeur initiale: {self.exceptional_value.value():,.2f} €
📉 Taux exceptionnel: {self.exceptional_rate.value()}%
🔍 Motif: {self.exceptional_reason.currentText()}

💰 Valeur après amortissement: {valeur_actuelle:,.2f} €
📉 Amortissement exceptionnel: {amortissement_exceptionnel:,.2f} €
⚠️ Impact fiscal à considérer
        """

        self.resultats_exceptionnel.setText(resultat)
        self.resultats_globaux.append(f"✅ Calcul exceptionnel terminé - Valeur finale: {valeur_actuelle:,.2f} €")

    def comparer_methodes(self):
        """Compare toutes les méthodes d'amortissement"""
        valeur = self.comp_value.value()
        periode = self.comp_period.value()

        # Calculs pour chaque année
        self.table_comparaison.setRowCount(periode)

        for annee in range(1, periode + 1):
            mois = annee * 12

            # Linéaire
            val_lineaire = self.calculator.calculate_linear_depreciation(valeur, periode, mois)

            # Dégressif
            val_degressive = self.calculator.calculate_degressive_depreciation(valeur, periode, mois, 2.0)

            # Différence
            difference = val_lineaire - val_degressive

            # Ajout dans le tableau
            self.table_comparaison.setItem(annee-1, 0, QTableWidgetItem(str(annee)))
            self.table_comparaison.setItem(annee-1, 1, QTableWidgetItem(f"{val_lineaire:,.2f} €"))
            self.table_comparaison.setItem(annee-1, 2, QTableWidgetItem(f"{val_degressive:,.2f} €"))
            self.table_comparaison.setItem(annee-1, 3, QTableWidgetItem(f"{difference:,.2f} €"))

        self.resultats_globaux.append(f"✅ Comparaison terminée pour {valeur:,.2f} € sur {periode} ans")

class FenetrePredictionsIA(QMainWindow):
    """Fenêtre 3: Prédictions IA avec Ollama - Priorité 3"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.ollama = OllamaIntegration()
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface de prédictions IA"""
        self.setWindowTitle("🤖 PRÉDICTIONS IA OLLAMA - Priorité 3")
        self.setFixedSize(1100, 750)
        self.setStyleSheet(self.get_vscode_style())

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Titre
        titre = QLabel("🤖 PRÉDICTIONS IA AVEC OLLAMA")
        titre.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titre.setStyleSheet("font-size: 16pt; font-weight: bold; padding: 10px;")
        layout.addWidget(titre)

        # Sélection actif
        self.create_selection_actif(layout)

        # Zone prédictions
        self.create_zone_predictions(layout)

        # Historique prédictions
        self.create_historique_predictions(layout)

    def get_vscode_style(self):
        """Style VSCode"""
        return """
            QMainWindow { background-color: #1e1e1e; color: #d4d4d4; }
            QWidget { background-color: #1e1e1e; color: #d4d4d4; font-family: 'Segoe UI'; }
            QGroupBox { background-color: #252526; border: 1px solid #0066ff; border-radius: 6px; margin: 8px; padding-top: 15px; font-weight: bold; }
            QGroupBox::title { color: #d4d4d4; background-color: #2d2d30; border: 1px solid #0066ff; border-radius: 4px; padding: 5px 10px; margin-left: 10px; }
            QPushButton { background-color: #0e639c; color: white; border: 1px solid #0066ff; border-radius: 4px; padding: 8px; font-weight: bold; }
            QPushButton:hover { background-color: #1177bb; }
            QComboBox { background-color: #3c3c3c; color: #d4d4d4; border: 1px solid #0066ff; border-radius: 4px; padding: 6px; }
            QTextEdit { background-color: #1e1e1e; color: #d4d4d4; border: 1px solid #0066ff; border-radius: 6px; padding: 10px; font-family: 'Consolas'; }
            QTableWidget { background-color: #252526; color: #d4d4d4; gridline-color: #0066ff; border: 1px solid #0066ff; }
            QHeaderView::section { background-color: #2d2d30; color: #d4d4d4; border: 1px solid #0066ff; padding: 8px; font-weight: bold; }
        """

    def create_selection_actif(self, layout):
        """Sélection d'actif pour prédiction"""
        selection_group = QGroupBox("🎯 SÉLECTION ACTIF POUR PRÉDICTION")
        selection_layout = QHBoxLayout(selection_group)

        selection_layout.addWidget(QLabel("Actif:"))

        self.combo_actifs = QComboBox()
        self.charger_actifs_combo()
        selection_layout.addWidget(self.combo_actifs)

        btn_predire = QPushButton("🤖 LANCER PRÉDICTION IA")
        btn_predire.clicked.connect(self.lancer_prediction)
        selection_layout.addWidget(btn_predire)

        btn_actualiser = QPushButton("🔄 ACTUALISER LISTE")
        btn_actualiser.clicked.connect(self.charger_actifs_combo)
        selection_layout.addWidget(btn_actualiser)

        layout.addWidget(selection_group)

    def create_zone_predictions(self, layout):
        """Zone d'affichage des prédictions"""
        predictions_group = QGroupBox("🔮 RÉSULTATS PRÉDICTIONS OLLAMA")
        predictions_layout = QVBoxLayout(predictions_group)

        self.predictions_display = QTextEdit()
        self.predictions_display.setMinimumHeight(300)
        self.predictions_display.setPlaceholderText("Les prédictions d'Ollama apparaîtront ici...")
        self.predictions_display.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #0066ff;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11pt;
                line-height: 1.4;
            }
        """)
        predictions_layout.addWidget(self.predictions_display)

        layout.addWidget(predictions_group)

    def create_historique_predictions(self, layout):
        """Historique des prédictions"""
        historique_group = QGroupBox("📚 HISTORIQUE PRÉDICTIONS")
        historique_layout = QVBoxLayout(historique_group)

        self.table_historique = QTableWidget()
        self.table_historique.setColumnCount(5)
        self.table_historique.setHorizontalHeaderLabels([
            "Date", "Actif", "Prédiction", "Confiance", "Modèle"
        ])
        self.table_historique.setMaximumHeight(200)
        historique_layout.addWidget(self.table_historique)

        layout.addWidget(historique_group)

        # Chargement initial
        self.charger_historique()

    def charger_actifs_combo(self):
        """Charge les actifs dans le combo"""
        self.combo_actifs.clear()

        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, code, name FROM assets WHERE status = 'active'")
        actifs = cursor.fetchall()

        for actif in actifs:
            self.combo_actifs.addItem(f"{actif[1]} - {actif[2]}", actif[0])

        conn.close()

    def lancer_prediction(self):
        """Lance une prédiction avec Ollama"""
        if self.combo_actifs.count() == 0:
            QMessageBox.warning(self, "Attention", "Aucun actif disponible pour prédiction")
            return

        # Récupération des données de l'actif
        asset_id = self.combo_actifs.currentData()

        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT code, name, initial_value, amortization_period, service, acquisition_date
            FROM assets WHERE id = ?
        """, (asset_id,))

        actif_data = cursor.fetchone()
        conn.close()

        if not actif_data:
            QMessageBox.critical(self, "Erreur", "Actif non trouvé")
            return

        # Préparation des données pour Ollama
        asset_info = {
            'code': actif_data[0],
            'name': actif_data[1],
            'initial_value': actif_data[2],
            'amortization_period': actif_data[3],
            'service': actif_data[4],
            'acquisition_date': actif_data[5]
        }

        # Affichage en cours
        self.predictions_display.setText("🤖 Ollama analyse l'actif...\n💭 Prédiction en cours...")

        # Lancement prédiction (simulé car Ollama peut ne pas être disponible)
        try:
            prediction = self.ollama.predict_depreciation(asset_info)

            # Formatage du résultat
            resultat_formate = f"""
🤖 PRÉDICTION OLLAMA POUR {asset_info['code']}
═══════════════════════════════════════════════════════════════

📊 DONNÉES ACTIF:
• Code: {asset_info['code']}
• Nom: {asset_info['name']}
• Valeur initiale: {asset_info['initial_value']:,.2f} €
• Période amortissement: {asset_info['amortization_period']} ans
• Service: {asset_info['service']}
• Date acquisition: {asset_info['acquisition_date']}

🔮 PRÉDICTION OLLAMA:
{prediction}

📅 Date prédiction: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
🤖 Modèle utilisé: {self.ollama.model}
            """

            self.predictions_display.setText(resultat_formate)

            # Sauvegarde en base
            self.sauvegarder_prediction(asset_id, prediction)

            # Actualisation historique
            self.charger_historique()

        except Exception as e:
            self.predictions_display.setText(f"❌ Erreur lors de la prédiction: {str(e)}")

    def sauvegarder_prediction(self, asset_id, prediction):
        """Sauvegarde la prédiction en base"""
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO ia_predictions (asset_id, prediction_date, predicted_value,
                                      confidence_score, model_used)
            VALUES (?, ?, ?, ?, ?)
        """, (
            asset_id,
            datetime.now().date(),
            0.0,  # Valeur prédite (à extraire du texte Ollama)
            0.85,  # Score de confiance simulé
            self.ollama.model
        ))

        conn.commit()
        conn.close()

    def charger_historique(self):
        """Charge l'historique des prédictions"""
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT p.prediction_date, a.code, p.predicted_value,
                   p.confidence_score, p.model_used
            FROM ia_predictions p
            JOIN assets a ON p.asset_id = a.id
            ORDER BY p.prediction_date DESC
            LIMIT 10
        """)

        predictions = cursor.fetchall()

        self.table_historique.setRowCount(len(predictions))
        for row, prediction in enumerate(predictions):
            for col, value in enumerate(prediction):
                self.table_historique.setItem(row, col, QTableWidgetItem(str(value)))

        conn.close()

class FenetreRapports(QMainWindow):
    """Fenêtre 4: Rapports et Analyses - Priorité 4"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface de rapports"""
        self.setWindowTitle("📊 RAPPORTS ET ANALYSES - Priorité 4")
        self.setFixedSize(1300, 900)
        self.setStyleSheet(self.get_vscode_style())

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Titre
        titre = QLabel("📊 RAPPORTS ET ANALYSES FINANCIÈRES")
        titre.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titre.setStyleSheet("font-size: 16pt; font-weight: bold; padding: 10px;")
        layout.addWidget(titre)

        # Onglets rapports
        self.tabs_rapports = QTabWidget()

        # Onglet Synthèse
        self.tabs_rapports.addTab(self.create_onglet_synthese(), "📈 SYNTHÈSE")

        # Onglet Détaillé
        self.tabs_rapports.addTab(self.create_onglet_detaille(), "📋 DÉTAILLÉ")

        # Onglet Export
        self.tabs_rapports.addTab(self.create_onglet_export(), "📤 EXPORT")

        layout.addWidget(self.tabs_rapports)

    def get_vscode_style(self):
        """Style VSCode"""
        return """
            QMainWindow { background-color: #1e1e1e; color: #d4d4d4; }
            QWidget { background-color: #1e1e1e; color: #d4d4d4; font-family: 'Segoe UI'; }
            QGroupBox { background-color: #252526; border: 1px solid #0066ff; border-radius: 6px; margin: 8px; padding-top: 15px; font-weight: bold; }
            QGroupBox::title { color: #d4d4d4; background-color: #2d2d30; border: 1px solid #0066ff; border-radius: 4px; padding: 5px 10px; margin-left: 10px; }
            QPushButton { background-color: #0e639c; color: white; border: 1px solid #0066ff; border-radius: 4px; padding: 8px; font-weight: bold; }
            QPushButton:hover { background-color: #1177bb; }
            QTabWidget::pane { background-color: #252526; border: 1px solid #0066ff; }
            QTabBar::tab { background-color: #2d2d30; color: #d4d4d4; border: 1px solid #0066ff; padding: 8px 16px; margin: 2px; }
            QTabBar::tab:selected { background-color: #0e639c; }
            QTextEdit { background-color: #1e1e1e; color: #d4d4d4; border: 1px solid #0066ff; border-radius: 6px; padding: 10px; font-family: 'Consolas'; }
            QTableWidget { background-color: #252526; color: #d4d4d4; gridline-color: #0066ff; border: 1px solid #0066ff; }
            QHeaderView::section { background-color: #2d2d30; color: #d4d4d4; border: 1px solid #0066ff; padding: 8px; font-weight: bold; }
        """

    def create_onglet_synthese(self):
        """Onglet synthèse des rapports"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Statistiques générales
        stats_group = QGroupBox("📊 STATISTIQUES GÉNÉRALES")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_display = QTextEdit()
        self.stats_display.setMaximumHeight(200)
        stats_layout.addWidget(self.stats_display)

        btn_generer_stats = QPushButton("📊 GÉNÉRER STATISTIQUES")
        btn_generer_stats.clicked.connect(self.generer_statistiques)
        stats_layout.addWidget(btn_generer_stats)

        layout.addWidget(stats_group)

        # Graphiques (simulé)
        graphiques_group = QGroupBox("📈 GRAPHIQUES")
        graphiques_layout = QVBoxLayout(graphiques_group)

        self.graphiques_info = QTextEdit()
        self.graphiques_info.setMaximumHeight(150)
        self.graphiques_info.setText("📈 Zone réservée aux graphiques\n🔧 Intégration QtCharts en cours...")
        graphiques_layout.addWidget(self.graphiques_info)

        layout.addWidget(graphiques_group)

        return widget

    def create_onglet_detaille(self):
        """Onglet rapport détaillé"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Tableau détaillé
        detail_group = QGroupBox("📋 RAPPORT DÉTAILLÉ PAR ACTIF")
        detail_layout = QVBoxLayout(detail_group)

        self.table_detail = QTableWidget()
        self.table_detail.setColumnCount(8)
        self.table_detail.setHorizontalHeaderLabels([
            "Code", "Nom", "Valeur Initiale", "Valeur Actuelle",
            "Amortissement", "Pourcentage", "Service", "Statut"
        ])
        detail_layout.addWidget(self.table_detail)

        btn_generer_detail = QPushButton("📋 GÉNÉRER RAPPORT DÉTAILLÉ")
        btn_generer_detail.clicked.connect(self.generer_rapport_detaille)
        detail_layout.addWidget(btn_generer_detail)

        layout.addWidget(detail_group)

        return widget

    def create_onglet_export(self):
        """Onglet export des données"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Options d'export
        export_group = QGroupBox("📤 OPTIONS D'EXPORT")
        export_layout = QVBoxLayout(export_group)

        # Boutons d'export
        boutons_layout = QHBoxLayout()

        btn_export_excel = QPushButton("📊 EXPORT EXCEL")
        btn_export_excel.clicked.connect(self.export_excel)
        boutons_layout.addWidget(btn_export_excel)

        btn_export_pdf = QPushButton("📄 EXPORT PDF")
        btn_export_pdf.clicked.connect(self.export_pdf)
        boutons_layout.addWidget(btn_export_pdf)

        btn_export_csv = QPushButton("📋 EXPORT CSV")
        btn_export_csv.clicked.connect(self.export_csv)
        boutons_layout.addWidget(btn_export_csv)

        export_layout.addLayout(boutons_layout)

        # Zone de statut export
        self.export_status = QTextEdit()
        self.export_status.setMaximumHeight(200)
        self.export_status.setPlaceholderText("Statut des exports...")
        export_layout.addWidget(self.export_status)

        layout.addWidget(export_group)

        return widget

    def generer_statistiques(self):
        """Génère les statistiques générales"""
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()

        # Statistiques de base
        cursor.execute("SELECT COUNT(*) FROM assets")
        nb_actifs = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(initial_value) FROM assets")
        valeur_totale_initiale = cursor.fetchone()[0] or 0

        cursor.execute("SELECT SUM(current_value) FROM assets")
        valeur_totale_actuelle = cursor.fetchone()[0] or 0

        amortissement_total = valeur_totale_initiale - valeur_totale_actuelle
        pourcentage_amorti = (amortissement_total / valeur_totale_initiale * 100) if valeur_totale_initiale > 0 else 0

        # Statistiques par service
        cursor.execute("SELECT service, COUNT(*), SUM(initial_value) FROM assets GROUP BY service")
        stats_services = cursor.fetchall()

        conn.close()

        # Formatage des résultats
        stats_text = f"""
📊 STATISTIQUES GÉNÉRALES - {datetime.now().strftime('%d/%m/%Y %H:%M')}
═══════════════════════════════════════════════════════════════════

🏢 RÉSUMÉ GLOBAL:
• Nombre total d'actifs: {nb_actifs}
• Valeur totale initiale: {valeur_totale_initiale:,.2f} €
• Valeur totale actuelle: {valeur_totale_actuelle:,.2f} €
• Amortissement total: {amortissement_total:,.2f} €
• Pourcentage amorti: {pourcentage_amorti:.2f}%

📈 RÉPARTITION PAR SERVICE:
"""

        for service, count, value in stats_services:
            stats_text += f"• {service}: {count} actifs - {value:,.2f} €\n"

        self.stats_display.setText(stats_text)

    def generer_rapport_detaille(self):
        """Génère le rapport détaillé"""
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT code, name, initial_value, current_value,
                   (initial_value - current_value) as amortissement,
                   service, status
            FROM assets
            ORDER BY service, code
        """)

        actifs = cursor.fetchall()

        self.table_detail.setRowCount(len(actifs))
        for row, actif in enumerate(actifs):
            for col, value in enumerate(actif):
                if col == 4:  # Pourcentage
                    pourcentage = (actif[4] / actif[2] * 100) if actif[2] > 0 else 0
                    self.table_detail.setItem(row, col, QTableWidgetItem(f"{actif[4]:,.2f} €"))
                    self.table_detail.setItem(row, col+1, QTableWidgetItem(f"{pourcentage:.2f}%"))
                elif col < 4:
                    if col in [2, 3]:  # Valeurs monétaires
                        self.table_detail.setItem(row, col, QTableWidgetItem(f"{value:,.2f} €"))
                    else:
                        self.table_detail.setItem(row, col, QTableWidgetItem(str(value)))
                elif col > 4:
                    self.table_detail.setItem(row, col+1, QTableWidgetItem(str(value)))

        conn.close()

    def export_excel(self):
        """Export vers Excel"""
        try:
            # Simulation export Excel
            self.export_status.append(f"📊 Export Excel démarré - {datetime.now().strftime('%H:%M:%S')}")
            self.export_status.append("✅ Fichier Excel généré: amortissements_export.xlsx")
        except Exception as e:
            self.export_status.append(f"❌ Erreur export Excel: {str(e)}")

    def export_pdf(self):
        """Export vers PDF"""
        try:
            # Simulation export PDF
            self.export_status.append(f"📄 Export PDF démarré - {datetime.now().strftime('%H:%M:%S')}")
            self.export_status.append("✅ Fichier PDF généré: rapport_amortissements.pdf")
        except Exception as e:
            self.export_status.append(f"❌ Erreur export PDF: {str(e)}")

    def export_csv(self):
        """Export vers CSV"""
        try:
            # Export CSV réel
            conn = sqlite3.connect(self.db_manager.db_path)
            df = pd.read_sql_query("SELECT * FROM assets", conn)
            df.to_csv("amortissements_export.csv", index=False)
            conn.close()

            self.export_status.append(f"📋 Export CSV démarré - {datetime.now().strftime('%H:%M:%S')}")
            self.export_status.append("✅ Fichier CSV généré: amortissements_export.csv")
        except Exception as e:
            self.export_status.append(f"❌ Erreur export CSV: {str(e)}")

def main():
    """Lance le module amortissements avec multi-fenêtres"""
    app = QApplication(sys.argv)

    # Configuration application
    app.setApplicationName("Module Amortissements Avancé")
    app.setApplicationVersion("1.0")

    # Lancement fenêtre principale (Priorité 1)
    fenetre_principale = FenetreGestionActifs()
    fenetre_principale.show()

    print("🏢 Module Amortissements Avancé lancé !")
    print("📊 Multi-fenêtres avec ordre et dépendances")
    print("🤖 Intégration Ollama pour prédictions")
    print("📈 Gestion complète des amortissements")

    return app.exec()

if __name__ == "__main__":
    main()
