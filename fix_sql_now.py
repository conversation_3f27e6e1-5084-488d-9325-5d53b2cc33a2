#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger immédiatement le fichier SQL
"""

import re

def fix_sql_file():
    """Corrige le fichier SQL init.sql"""
    file_path = 'src/database/migrations/init.sql'
    
    print("🔧 Correction du fichier SQL...")
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Backup
        with open(file_path + '.backup', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Corrections
        original_content = content
        
        # 1. SERIAL PRIMARY KEY -> INTEGER PRIMARY KEY AUTOINCREMENT
        content = re.sub(r'\bid\s+SERIAL\s+PRIMARY\s+KEY\b', 'id INTEGER PRIMARY KEY AUTOINCREMENT', content, flags=re.IGNORECASE)
        
        # 2. BOOLEAN -> INTEGER (SQLite n'a pas de type BOOLEAN natif)
        content = re.sub(r'\bBOOLEAN\b', 'INTEGER', content, flags=re.IGNORECASE)
        
        # Compter les changements
        serial_changes = len(re.findall(r'\bid\s+SERIAL\s+PRIMARY\s+KEY\b', original_content, flags=re.IGNORECASE))
        boolean_changes = len(re.findall(r'\bBOOLEAN\b', original_content, flags=re.IGNORECASE))
        
        # Sauvegarder
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Corrections appliquées:")
        print(f"   - SERIAL PRIMARY KEY → INTEGER PRIMARY KEY AUTOINCREMENT: {serial_changes} occurrences")
        print(f"   - BOOLEAN → INTEGER: {boolean_changes} occurrences")
        print(f"✅ Fichier sauvegardé: {file_path}")
        print(f"✅ Backup créé: {file_path}.backup")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CORRECTION IMMÉDIATE DU FICHIER SQL")
    print("=" * 50)
    
    if fix_sql_file():
        print("\n🎉 CORRECTION SQL RÉUSSIE !")
        print("✅ Le fichier init.sql est maintenant compatible SQLite")
        print("✅ Toutes les occurrences de SERIAL ont été corrigées")
        print("✅ Tous les types BOOLEAN ont été adaptés")
        return True
    else:
        print("\n❌ CORRECTION SQL ÉCHOUÉE")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
