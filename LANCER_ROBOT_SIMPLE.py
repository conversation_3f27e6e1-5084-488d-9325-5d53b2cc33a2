#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 LANCEUR ROBOT IA SIMPLE
Lancement rapide pour test immédiat
Créé par Augment Agent pour SamNord@110577
"""

import os
import sys

def lancer_robot_simple():
    """Lancement simple Robot IA"""
    print("🚀 LANCEUR ROBOT IA SIMPLE")
    print("=" * 30)
    print("👤 SamNord@110577")
    print("🔑 NorDine@22")
    print()
    
    print("🤖 ROBOT IA TRANSCENDANT DISPONIBLE !")
    print()
    
    print("🎯 MODULES PRINCIPAUX:")
    print("1. 🤖 Robot IA Principal (Tous modules)")
    print("2. 🧠 Intelligence IA Transcendante")
    print("3. 📡 WiFi Avancé Complet")
    print("4. 📱 Bluetooth Complet")
    print("5. 🌐 Connexion Modem/Router")
    print("6. 🛡️ Cybersécurité Éthique")
    print("7. 📱 Tracking Téléphones")
    print("8. 🌍 Traduction Temps Réel")
    print("9. 🗣️ Communication Multilingue")
    print("10. 🧠 Mémoire Parfaite Robot")
    print()
    
    choix = input("➤ Choisir module (1-10): ").strip()
    
    if choix == "1":
        print("🚀 Lancement Robot IA Principal...")
        os.system("python robot_ia_universel_40_modules.py")
    elif choix == "2":
        print("🧠 Lancement Intelligence IA...")
        os.system("python modules/intelligence_ia_transcendante.py")
    elif choix == "3":
        print("📡 Lancement WiFi Avancé...")
        os.system("python modules/wifi_avance_complet.py")
    elif choix == "4":
        print("📱 Lancement Bluetooth...")
        os.system("python modules/bluetooth_complet.py")
    elif choix == "5":
        print("🌐 Lancement Connexion Réseau...")
        os.system("python modules/connexion_modem_router.py")
    elif choix == "6":
        print("🛡️ Lancement Cybersécurité...")
        os.system("python modules/cybersecurite_ethique.py")
    elif choix == "7":
        print("📱 Lancement Tracking...")
        os.system("python modules/tracking_telephones.py")
    elif choix == "8":
        print("🌍 Lancement Traduction...")
        os.system("python modules/traduction_temps_reel.py")
    elif choix == "9":
        print("🗣️ Lancement Communication...")
        os.system("python modules/communication_multilingue.py")
    elif choix == "10":
        print("🧠 Lancement Mémoire...")
        os.system("python modules/memoire_parfaite_robot.py")
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    lancer_robot_simple()
