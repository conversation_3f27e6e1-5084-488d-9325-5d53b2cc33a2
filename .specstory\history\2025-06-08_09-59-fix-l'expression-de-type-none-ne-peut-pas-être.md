<!-- Generated by SpecStory -->

# /fix L'expression de type "None" ne peut pas être  (2025-06-08 10:59:13)

_**User**_

/fix L'expression de type "None" ne peut pas être affectée au paramètre de type "str"
  « None » n’est pas assignable à « str »

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Le problème vient du typage des paramètres `username`, `password` et `role` dans la méthode `update_user`. Pour permettre la valeur `None`, il faut utiliser `Optional[str]` du module `typing`.

Voici le correctif :

````python
from typing import TYPE_CHECKING, List, Optional
# ...existing code...
    def update_user(self, user_id: int, username: Optional[str] = None, password: Optional[str] = None, role: Optional[str] = None) -> "User":
        # ...existing code...
````

---

