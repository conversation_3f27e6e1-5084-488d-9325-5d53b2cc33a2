<!-- Generated by SpecStory -->

# /fix Désolé... Nous ne pouvons pas accéder à l’att (2025-06-08 11:19:38)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « setOrientation » pour la classe « QPrinter »
  L’attribut « setOrientation » est inconnu, Le type de « setOrientation » est inconnu

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Remplacer l'utilisation de `printer.setOrientation(...)` (qui n'existe plus dans PySide6) par `printer.setPageOrientation(...)` avec l'enum approprié (`QPageLayout.Landscape` ou `QPageLayout.Portrait`).

````python
# ...existing code...
        elif fmt == "Paysage":
            from PySide6.QtGui import QPageLayout
            printer.setPageOrientation(QPageLayout.Orientation.Landscape)
        elif fmt == "Portrait":
            from PySide6.QtGui import QPageLayout
            printer.setPageOrientation(QPageLayout.Orientation.Portrait)
# ...existing code...
````

---

