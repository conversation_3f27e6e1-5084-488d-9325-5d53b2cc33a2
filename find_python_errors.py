#!/usr/bin/env python3
"""
Script pour identifier précisément les erreurs Python
"""

import ast
import os
import sys
import traceback

def test_python_file_detailed(filepath):
    """Test détaillé d'un fichier Python avec diagnostic précis"""
    print(f"\n{'='*60}")
    print(f"ANALYSE DÉTAILLÉE DE: {filepath}")
    print(f"{'='*60}")
    
    if not os.path.exists(filepath):
        print(f"❌ ERREUR: Fichier non trouvé: {filepath}")
        return False
    
    try:
        # Lecture du fichier
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ Fichier lu avec succès")
        print(f"   Taille: {len(content)} caractères")
        print(f"   Lignes: {len(content.splitlines())}")
        
        # Vérification du contenu
        if not content.strip():
            print("❌ ERREUR: Fichier vide")
            return False
        
        # Test AST (parsing)
        print(f"\n🔍 Test AST (Abstract Syntax Tree)...")
        try:
            tree = ast.parse(content, filename=filepath)
            print(f"✅ AST parsing réussi")
        except SyntaxError as e:
            print(f"❌ ERREUR AST - Ligne {e.lineno}, Colonne {e.offset}")
            print(f"   Message: {e.msg}")
            if e.text:
                print(f"   Code problématique: {repr(e.text.strip())}")
            
            # Essayer de localiser plus précisément
            lines = content.splitlines()
            if e.lineno and e.lineno <= len(lines):
                error_line = lines[e.lineno - 1]
                print(f"   Ligne complète: {repr(error_line)}")
                
                # Montrer le contexte
                start = max(0, e.lineno - 3)
                end = min(len(lines), e.lineno + 2)
                print(f"   Contexte (lignes {start+1}-{end}):")
                for i in range(start, end):
                    marker = " >>> " if i == e.lineno - 1 else "     "
                    print(f"   {marker}{i+1:4d}: {lines[i]}")
            
            return False
        except Exception as e:
            print(f"❌ ERREUR AST inattendue: {e}")
            print(f"   Type: {type(e).__name__}")
            traceback.print_exc()
            return False
        
        # Test de compilation
        print(f"\n🔍 Test de compilation...")
        try:
            compile(content, filepath, 'exec')
            print(f"✅ Compilation réussie")
        except SyntaxError as e:
            print(f"❌ ERREUR COMPILATION - Ligne {e.lineno}, Colonne {e.offset}")
            print(f"   Message: {e.msg}")
            if e.text:
                print(f"   Code problématique: {repr(e.text.strip())}")
            return False
        except Exception as e:
            print(f"❌ ERREUR COMPILATION inattendue: {e}")
            print(f"   Type: {type(e).__name__}")
            traceback.print_exc()
            return False
        
        # Analyse des imports
        print(f"\n🔍 Analyse des imports...")
        import_errors = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            if line_stripped.startswith('import ') or line_stripped.startswith('from '):
                if 'type: ignore' not in line:
                    # Vérifier les imports potentiellement problématiques
                    problematic_modules = ['PySide6', 'sqlalchemy', 'cryptography']
                    for module in problematic_modules:
                        if module in line:
                            import_errors.append(f"Ligne {i}: Import potentiellement problématique: {line_stripped}")
        
        if import_errors:
            print(f"⚠️ Imports potentiellement problématiques trouvés:")
            for error in import_errors:
                print(f"   {error}")
        else:
            print(f"✅ Aucun import problématique détecté")
        
        # Analyse des variables non typées
        print(f"\n🔍 Analyse des variables...")
        untyped_vars = []
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            if ' = ' in line_stripped and ':' not in line_stripped.split('=')[0]:
                if not any(keyword in line_stripped for keyword in ['def ', 'class ', 'import', 'from ', '#']):
                    if not line_stripped.startswith('if ') and not line_stripped.startswith('for '):
                        untyped_vars.append(f"Ligne {i}: Variable non typée: {line_stripped}")
        
        if untyped_vars:
            print(f"⚠️ Variables non typées trouvées: {len(untyped_vars)}")
            for var in untyped_vars[:5]:  # Montrer seulement les 5 premières
                print(f"   {var}")
            if len(untyped_vars) > 5:
                print(f"   ... et {len(untyped_vars) - 5} autres")
        else:
            print(f"✅ Toutes les variables importantes sont typées")
        
        # Statistiques finales
        print(f"\n📊 Statistiques:")
        print(f"   Classes: {content.count('class ')}")
        print(f"   Fonctions: {content.count('def ')}")
        print(f"   Imports: {content.count('import ')}")
        print(f"   Commentaires: {len([l for l in lines if l.strip().startswith('#')])}")
        
        print(f"\n✅ FICHIER VALIDE: {filepath}")
        return True
        
    except Exception as e:
        print(f"❌ ERREUR INATTENDUE lors de l'analyse: {e}")
        print(f"   Type: {type(e).__name__}")
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🔍 DÉTECTION PRÉCISE DES ERREURS PYTHON")
    print("=" * 60)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Liste des fichiers à analyser
    files_to_analyze = [
        'src/main.py',
        'src/auth/user_management.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py'
    ]
    
    results = {}
    
    for filepath in files_to_analyze:
        results[filepath] = test_python_file_detailed(filepath)
    
    # Résumé final
    print(f"\n{'='*60}")
    print("RÉSUMÉ FINAL")
    print(f"{'='*60}")
    
    valid_files = sum(1 for result in results.values() if result)
    total_files = len(results)
    
    print(f"Fichiers analysés: {total_files}")
    print(f"Fichiers valides: {valid_files}")
    print(f"Fichiers avec erreurs: {total_files - valid_files}")
    
    if valid_files == total_files:
        print(f"\n🎉 TOUS LES FICHIERS SONT PARFAITS!")
        print(f"✅ Aucune erreur de syntaxe détectée")
        print(f"✅ Tous les fichiers compilent correctement")
        return True
    else:
        print(f"\n🚨 ERREURS DÉTECTÉES DANS {total_files - valid_files} FICHIER(S)")
        print(f"❌ Consultez les détails ci-dessus pour chaque fichier")
        
        print(f"\nFichiers avec problèmes:")
        for filepath, is_valid in results.items():
            if not is_valid:
                print(f"   ❌ {filepath}")
        
        print(f"\nFichiers corrects:")
        for filepath, is_valid in results.items():
            if is_valid:
                print(f"   ✅ {filepath}")
        
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat: {'SUCCÈS' if success else 'ÉCHEC'}")
    sys.exit(0 if success else 1)
