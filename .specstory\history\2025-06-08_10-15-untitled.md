<!-- Generated by SpecStory -->

# Untitled (2025-06-08 11:15:59)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « setPaperSize » pour la classe « QPrinter »
  L’attribut « setPaperSize » est inconnu, Le type de « setPaperSize » est inconnu

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Remplacer l'utilisation de `setPaperSize(QPrinter.PaperSize.A3)` par la méthode compatible avec PySide6, qui utilise l'énumération directe `QPrinter.A3` (et similaires) dans `setPageSize()`.

````python
# ...existing code...
    def imprimer(self):
        printer = self._get_printer()
        fmt = self.combo_format.currentText()
        # Paramètres avancés : format, orientation, etc.
        if fmt == "PDF":
            # Correction : utiliser QPrinter.OutputFormat.PdfFormat
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
            file, _ = QFileDialog.getSaveFileName(self, "Enregistrer PDF", "", "PDF (*.pdf)")
            if file:
                printer.setOutputFileName(file)
        elif fmt == "A3":
            printer.setPageSize(QPrinter.PageSize.A3)
        elif fmt == "A4":
            printer.setPageSize(QPrinter.PageSize.A4)
        elif fmt == "Lettre":
            printer.setPageSize(QPrinter.PageSize.Letter)
        elif fmt == "Paysage":
            printer.setOrientation(QPrinter.Orientation.Landscape)
        elif fmt == "Portrait":
            printer.setOrientation(QPrinter.Orientation.Portrait)
        dialog = QPrintDialog(printer, self)
# ...existing code...
````

---

