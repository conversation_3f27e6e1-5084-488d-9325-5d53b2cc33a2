#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 CRÉATION APPLICATION .EXE ROBOT AUTONOME PC
Génère un fichier exécutable Windows complet
Créé par Augment Agent
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def installer_dependances():
    """Installe toutes les dépendances nécessaires"""
    print("📦 Installation des dépendances...")
    
    dependances = [
        "pyinstaller",
        "PySide6",
        "requests",
        "psutil",
        "pycryptodome",
        "pillow",
        "opencv-python",
        "speech-recognition",
        "pyttsx3",
        "beautifulsoup4",
        "selenium",
        "paramiko",
        "scapy",
        "nmap",
        "python-whois"
    ]
    
    for dep in dependances:
        try:
            print(f"📥 Installation {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {dep} installé")
        except subprocess.CalledProcessError:
            print(f"⚠️ Erreur installation {dep}")
    
    print("✅ Toutes les dépendances installées !")

def creer_fichier_spec():
    """Crée le fichier .spec pour PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

# Données à inclure
added_files = [
    ('config', 'config'),
    ('data', 'data'),
    ('logs', 'logs'),
    ('scripts', 'scripts'),
    ('templates', 'templates'),
    ('assets', 'assets'),
    ('README_Robot.txt', '.'),
    ('GUIDE_INSTALLATION_COMPLET.md', '.'),
    ('FONCTIONNALITES_FINALES_COMPLETES.md', '.')
]

# Modules cachés nécessaires
hidden_imports = [
    'PySide6.QtCore',
    'PySide6.QtWidgets', 
    'PySide6.QtGui',
    'requests',
    'json',
    'datetime',
    'threading',
    'time',
    'os',
    'sys',
    'subprocess',
    'socket',
    'urllib',
    'base64',
    'hashlib',
    'sqlite3',
    'csv',
    'xml',
    'html',
    'email',
    'smtplib',
    'ftplib',
    'telnetlib',
    'paramiko',
    'scapy',
    'psutil',
    'speech_recognition',
    'pyttsx3',
    'cv2',
    'PIL',
    'selenium',
    'beautifulsoup4'
]

a = Analysis(
    ['robot_autonome_pc.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RobotAutonomePC',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/robot_icon.ico' if os.path.exists('assets/robot_icon.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None
)
'''
    
    with open('robot_autonome.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec créé !")

def creer_version_info():
    """Crée le fichier d'informations de version"""
    version_content = '''# UTF-8
#
# Version Information for RobotAutonomePC.exe
#

VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Robot IA Autonome'),
        StringStruct(u'FileDescription', u'Robot IA Autonome pour PC'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'RobotAutonomePC'),
        StringStruct(u'LegalCopyright', u'© 2024 Robot IA Autonome'),
        StringStruct(u'OriginalFilename', u'RobotAutonomePC.exe'),
        StringStruct(u'ProductName', u'Robot IA Autonome PC'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✅ Fichier version créé !")

def creer_icone_robot():
    """Crée un fichier icône pour le robot"""
    # Créer le dossier assets s'il n'existe pas
    os.makedirs('assets', exist_ok=True)
    
    # Note: Dans un vrai projet, vous ajouteriez une vraie icône .ico
    icone_info = """
🤖 ICÔNE ROBOT AUTONOME
======================

Pour une icône professionnelle, remplacez ce fichier par:
- Un fichier .ico de 256x256 pixels
- Nom: robot_icon.ico
- Emplacement: assets/robot_icon.ico

Icône recommandée: Robot futuriste bleu/vert
    """
    
    with open('assets/robot_icon_info.txt', 'w', encoding='utf-8') as f:
        f.write(icone_info)
    
    print("✅ Dossier icône créé (ajoutez robot_icon.ico)")

def compiler_exe():
    """Compile le robot en fichier EXE"""
    print("🔨 Compilation en cours...")
    print("⏳ Cela peut prendre plusieurs minutes...")
    
    try:
        # Compilation avec PyInstaller
        cmd = [
            "pyinstaller",
            "--clean",
            "--noconfirm",
            "robot_autonome.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Compilation réussie !")
            
            # Vérification de l'EXE
            exe_path = "dist/RobotAutonomePC.exe"
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"📦 EXE créé: {exe_path}")
                print(f"📏 Taille: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Fichier EXE non trouvé")
                return False
        else:
            print("❌ Erreur de compilation:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur compilation: {e}")
        return False

def creer_installateur_exe():
    """Crée l'installateur pour l'EXE"""
    installateur_content = '''@echo off
title 🤖 INSTALLATION ROBOT IA AUTONOME PC
color 0A

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██    🤖 ROBOT IA AUTONOME PC - INSTALLATION EXE          ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo 📦 Installation Robot IA Autonome...
echo.

echo 📁 Création dossier d'installation...
if not exist "C:\\RobotAutonomePC" mkdir "C:\\RobotAutonomePC"
if not exist "C:\\RobotAutonomePC\\config" mkdir "C:\\RobotAutonomePC\\config"
if not exist "C:\\RobotAutonomePC\\data" mkdir "C:\\RobotAutonomePC\\data"
if not exist "C:\\RobotAutonomePC\\logs" mkdir "C:\\RobotAutonomePC\\logs"
if not exist "C:\\RobotAutonomePC\\scripts" mkdir "C:\\RobotAutonomePC\\scripts"
if not exist "C:\\RobotAutonomePC\\backups" mkdir "C:\\RobotAutonomePC\\backups"

echo ✅ Dossiers créés

echo 📋 Copie de l'application...
copy "RobotAutonomePC.exe" "C:\\RobotAutonomePC\\" >nul
if exist "README_Robot.txt" copy "README_Robot.txt" "C:\\RobotAutonomePC\\" >nul
if exist "GUIDE_INSTALLATION_COMPLET.md" copy "GUIDE_INSTALLATION_COMPLET.md" "C:\\RobotAutonomePC\\" >nul

echo ✅ Application copiée

echo 🔗 Création raccourci bureau...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\Robot IA Autonome.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\RobotAutonomePC\\RobotAutonomePC.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "C:\\RobotAutonomePC" >> CreateShortcut.vbs
echo oLink.Description = "Robot IA Autonome pour PC" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs >nul
del CreateShortcut.vbs

echo ✅ Raccourci créé

echo 📋 Ajout au menu démarrer...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot IA Autonome" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot IA Autonome"
copy "%USERPROFILE%\\Desktop\\Robot IA Autonome.lnk" "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot IA Autonome\\" >nul

echo ✅ Menu démarrer configuré

echo 🛡️ Configuration sécurité...
icacls "C:\\RobotAutonomePC" /grant %USERNAME%:F >nul 2>&1

echo ✅ Sécurité configurée

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██         ✅ INSTALLATION TERMINÉE AVEC SUCCÈS !         ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 🤖 Robot IA Autonome installé dans: C:\\RobotAutonomePC\\
echo 🖥️ Raccourci bureau: Robot IA Autonome
echo 📋 Menu démarrer: Robot IA Autonome
echo.
echo 🚀 LANCEMENT DU ROBOT...
echo.

start "" "C:\\RobotAutonomePC\\RobotAutonomePC.exe"

echo 🎉 Robot IA Autonome démarré !
echo 🤖 Votre assistant personnel est opérationnel !
echo.
pause
'''
    
    with open('installer_robot_exe.bat', 'w', encoding='utf-8') as f:
        f.write(installateur_content)
    
    print("✅ Installateur EXE créé !")

def creer_package_distribution():
    """Crée le package de distribution final"""
    print("📦 Création package de distribution...")
    
    # Dossier de distribution
    dist_folder = "RobotAutonomePC_EXE_Distribution"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    os.makedirs(dist_folder)
    
    # Fichiers à copier
    files_to_copy = [
        ("dist/RobotAutonomePC.exe", "RobotAutonomePC.exe"),
        ("installer_robot_exe.bat", "installer_robot_exe.bat"),
        ("README_Robot.txt", "README_Robot.txt"),
        ("GUIDE_INSTALLATION_COMPLET.md", "GUIDE_INSTALLATION_COMPLET.md"),
        ("FONCTIONNALITES_FINALES_COMPLETES.md", "FONCTIONNALITES_FINALES_COMPLETES.md")
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(dist_folder, dst))
            print(f"✅ {dst} copié")
        else:
            print(f"⚠️ {src} non trouvé")
    
    # README pour distribution
    readme_distribution = '''🤖 ROBOT IA AUTONOME PC - DISTRIBUTION EXE
==========================================

📦 CONTENU DU PACKAGE:
• RobotAutonomePC.exe - Application principale
• installer_robot_exe.bat - Installation automatique
• README_Robot.txt - Guide utilisateur
• GUIDE_INSTALLATION_COMPLET.md - Guide détaillé
• FONCTIONNALITES_FINALES_COMPLETES.md - Fonctionnalités

🚀 INSTALLATION RAPIDE:
1. Exécuter installer_robot_exe.bat en tant qu'administrateur
2. Suivre les instructions à l'écran
3. Lancer "Robot IA Autonome" depuis le bureau

✅ INSTALLATION TERMINÉE !
Votre Robot IA Autonome est prêt à utiliser !

🌟 Profitez de votre assistant IA transcendant !
'''
    
    with open(os.path.join(dist_folder, "README_DISTRIBUTION.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_distribution)
    
    print(f"✅ Package créé dans {dist_folder}/")

def main():
    """Fonction principale de création EXE"""
    print("🤖 CRÉATION APPLICATION EXE ROBOT AUTONOME PC")
    print("=" * 60)
    
    # Vérifications préliminaires
    if not os.path.exists("robot_autonome_pc.py"):
        print("❌ Fichier robot_autonome_pc.py non trouvé !")
        return
    
    # Étapes de création
    steps = [
        ("📦 Installation dépendances", installer_dependances),
        ("📄 Création fichier .spec", creer_fichier_spec),
        ("📋 Création version info", creer_version_info),
        ("🎨 Préparation icône", creer_icone_robot),
        ("🔨 Compilation EXE", compiler_exe),
        ("⚙️ Création installateur", creer_installateur_exe),
        ("📦 Package distribution", creer_package_distribution)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            result = step_func()
            if result is False:
                print(f"❌ Échec: {step_name}")
                break
        except Exception as e:
            print(f"❌ Erreur {step_name}: {e}")
            break
    
    print("\n" + "=" * 60)
    print("🚀 CRÉATION EXE TERMINÉE !")
    print("\n📁 FICHIERS CRÉÉS:")
    print("   • RobotAutonomePC.exe (Application principale)")
    print("   • installer_robot_exe.bat (Installation automatique)")
    print("   • RobotAutonomePC_EXE_Distribution/ (Package complet)")
    print("\n🎯 DISTRIBUTION:")
    print("1. Distribuez le dossier RobotAutonomePC_EXE_Distribution/")
    print("2. L'utilisateur exécute installer_robot_exe.bat")
    print("3. Le robot est installé et prêt à utiliser !")
    print("\n🤖 VOTRE ROBOT AUTONOME EXE EST PRÊT !")

if __name__ == "__main__":
    main()
