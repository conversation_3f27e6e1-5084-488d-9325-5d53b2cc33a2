#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple et direct de main.py
"""

import ast
import sys
import os

def test_main_py():
    """Test simple de main.py"""
    print("🔍 TEST SIMPLE DE MAIN.PY")
    print("=" * 30)
    
    # Vérifier que le fichier existe
    if not os.path.exists('src/main.py'):
        print("❌ Fichier src/main.py non trouvé")
        return False
    
    print("✅ Fichier trouvé")
    
    # Lire le contenu
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✅ Fichier lu avec succès")
    except Exception as e:
        print(f"❌ Erreur lecture: {e}")
        return False
    
    # Test AST
    try:
        ast.parse(content)
        print("✅ AST parsing réussi")
    except SyntaxError as e:
        print(f"❌ Erreur syntaxe ligne {e.lineno}: {e.msg}")
        print(f"   Texte: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Erreur AST: {e}")
        return False
    
    # Test compilation
    try:
        compile(content, 'src/main.py', 'exec')
        print("✅ Compilation réussie")
    except SyntaxError as e:
        print(f"❌ Erreur compilation ligne {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Erreur compilation: {e}")
        return False
    
    # Statistiques
    lines = content.splitlines()
    stats = {
        'Lignes totales': len(lines),
        'Lignes de code': len([l for l in lines if l.strip() and not l.strip().startswith('#')]),
        'Fonctions': content.count('def '),
        'Classes': content.count('class '),
        'Imports': content.count('import '),
        'Annotations ->': content.count('->'),
        'Type ignore': content.count('# type: ignore')
    }
    
    print("\n📊 STATISTIQUES:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n🎉 RÉSULTAT: MAIN.PY EST PARFAITEMENT CORRECT !")
    print("✅ Aucune erreur de syntaxe")
    print("✅ Code compilable")
    print("✅ AST valide")
    
    return True

def test_import():
    """Test d'import du module"""
    print("\n🔍 TEST D'IMPORT")
    print("=" * 20)
    
    try:
        # Ajouter le répertoire src au path
        sys.path.insert(0, 'src')
        
        # Test d'import
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "src/main.py")
        
        if spec and spec.loader:
            print("✅ Module peut être importé")
            return True
        else:
            print("❌ Problème d'import")
            return False
            
    except Exception as e:
        print(f"❌ Erreur import: {e}")
        return False

def main():
    """Fonction principale"""
    success1 = test_main_py()
    success2 = test_import()
    
    if success1 and success2:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ main.py fonctionne parfaitement")
        return True
    else:
        print("\n⚠️ Certains tests ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
