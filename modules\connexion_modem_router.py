#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 ROBOT IA - CONNEXION MODEM/ROUTER
Gestion complète modems, routeurs, configuration réseau
Créé par Augment Agent pour SamNord@110577
"""

import sys
import os
import json
import subprocess
import threading
import time
import socket
import requests
from datetime import datetime
import re

class ConnexionModemRouter:
    """🌐 Système connexion modem/router ultra-complet"""
    
    def __init__(self):
        self.user = "SamNord@110577"
        self.connexion_active = True
        self.modems_detectes = []
        self.routeurs_detectes = []
        
        # Configuration réseau
        self.config_reseau = {
            "timeout_connexion": 10,
            "scan_ports": [80, 443, 22, 23, 8080, 8443],
            "interfaces_communes": ["***********", "***********", "********", "**********"],
            "credentials_defaut": [
                ("admin", "admin"), ("admin", "password"), ("admin", ""),
                ("root", "root"), ("user", "user"), ("", "")
            ]
        }
        
        # Types équipements réseau
        self.types_equipements = {
            "modem": "📡 Modem",
            "router": "🌐 Routeur",
            "access_point": "📶 Point d'accès",
            "switch": "🔀 Switch",
            "firewall": "🛡️ Pare-feu",
            "nas": "💾 NAS",
            "printer": "🖨️ Imprimante",
            "camera": "📹 Caméra IP"
        }
        
        print(f"🌐 Connexion Modem/Router initialisée pour {self.user}")
        print("📡 Détection et configuration équipements réseau")
        print("🔧 Gestion complète infrastructure réseau")
    
    def scanner_equipements_reseau(self):
        """Scan complet équipements réseau"""
        print("🌐 SCAN ÉQUIPEMENTS RÉSEAU")
        print("=" * 35)
        print("📡 Détection modems, routeurs, équipements")
        print("🔍 Analyse ports et services")
        print("🛡️ Test sécurité et accès")
        print()
        
        # Détection réseau local
        reseau_local = self._detecter_reseau_local()
        print(f"🌐 Réseau local détecté: {reseau_local}")
        
        # Scan plage IP
        equipements = []
        
        print("🔄 Scan en cours...")
        
        # Scan interfaces communes
        for ip in self.config_reseau["interfaces_communes"]:
            equipement = self._analyser_equipement(ip)
            if equipement:
                equipements.append(equipement)
        
        # Scan réseau local
        if reseau_local:
            base_ip = ".".join(reseau_local.split(".")[:-1])
            for i in range(1, 255):
                ip = f"{base_ip}.{i}"
                if self._ping_host(ip):
                    equipement = self._analyser_equipement(ip)
                    if equipement:
                        equipements.append(equipement)
        
        self.modems_detectes = [e for e in equipements if e['type'] in ['modem', 'router']]
        self.routeurs_detectes = [e for e in equipements if e['type'] == 'router']
        
        print(f"\n🌐 {len(equipements)} équipements réseau détectés:")
        print("-" * 100)
        print("IP Address      | Hostname           | Type        | Ports    | Services      | Accès | Statut")
        print("-" * 100)
        
        for equipement in equipements:
            access_icon = "🔓" if equipement['accessible'] else "🔒"
            status_icon = "🟢" if equipement['online'] else "🔴"
            
            print(f"{equipement['ip']:<15} | {equipement['hostname']:<18} | {equipement['type']:<11} | {len(equipement['ports_ouverts']):>3} | {', '.join(equipement['services'][:2]):<13} | {access_icon} | {status_icon} {equipement['status']}")
        
        # Analyse sécurité réseau
        self._analyser_securite_reseau(equipements)
        
        return equipements
    
    def _detecter_reseau_local(self):
        """Détection réseau local"""
        try:
            # Obtenir IP locale
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip_locale = s.getsockname()[0]
            s.close()
            
            return ip_locale
            
        except Exception as e:
            print(f"⚠️ Erreur détection réseau: {e}")
            return None
    
    def _ping_host(self, ip):
        """Test ping host"""
        try:
            if os.name == 'nt':
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                      capture_output=True, text=True, timeout=2)
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                      capture_output=True, text=True, timeout=2)
            
            return result.returncode == 0
            
        except:
            return False
    
    def _analyser_equipement(self, ip):
        """Analyse équipement réseau"""
        try:
            # Test connectivité
            if not self._ping_host(ip):
                return None
            
            # Résolution hostname
            try:
                hostname = socket.gethostbyaddr(ip)[0]
            except:
                hostname = f"host-{ip.split('.')[-1]}"
            
            # Scan ports
            ports_ouverts = self._scan_ports(ip)
            
            # Détection services
            services = self._detecter_services(ip, ports_ouverts)
            
            # Détection type équipement
            type_equipement = self._detecter_type_equipement(ip, ports_ouverts, services, hostname)
            
            # Test accès web
            accessible = self._tester_acces_web(ip)
            
            equipement = {
                'ip': ip,
                'hostname': hostname,
                'type': type_equipement,
                'ports_ouverts': ports_ouverts,
                'services': services,
                'accessible': accessible,
                'online': True,
                'status': 'En ligne',
                'last_seen': datetime.now().isoformat(),
                'vendor': self._detecter_vendor(hostname, services),
                'version': 'Inconnue'
            }
            
            return equipement
            
        except Exception as e:
            return None
    
    def _scan_ports(self, ip):
        """Scan ports équipement"""
        ports_ouverts = []
        
        for port in self.config_reseau["scan_ports"]:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((ip, port))
                sock.close()
                
                if result == 0:
                    ports_ouverts.append(port)
                    
            except:
                continue
        
        return ports_ouverts
    
    def _detecter_services(self, ip, ports):
        """Détection services par ports"""
        services = []
        
        service_map = {
            80: "HTTP",
            443: "HTTPS",
            22: "SSH",
            23: "Telnet",
            21: "FTP",
            25: "SMTP",
            53: "DNS",
            110: "POP3",
            143: "IMAP",
            993: "IMAPS",
            995: "POP3S",
            8080: "HTTP-Alt",
            8443: "HTTPS-Alt"
        }
        
        for port in ports:
            if port in service_map:
                services.append(service_map[port])
        
        return services
    
    def _detecter_type_equipement(self, ip, ports, services, hostname):
        """Détection type équipement"""
        hostname_lower = hostname.lower()
        
        # Détection par hostname
        if any(keyword in hostname_lower for keyword in ['router', 'routeur', 'gateway']):
            return 'router'
        elif any(keyword in hostname_lower for keyword in ['modem', 'adsl', 'cable']):
            return 'modem'
        elif any(keyword in hostname_lower for keyword in ['ap', 'access', 'wifi']):
            return 'access_point'
        elif any(keyword in hostname_lower for keyword in ['switch', 'sw']):
            return 'switch'
        elif any(keyword in hostname_lower for keyword in ['firewall', 'fw', 'pfsense']):
            return 'firewall'
        elif any(keyword in hostname_lower for keyword in ['nas', 'storage']):
            return 'nas'
        elif any(keyword in hostname_lower for keyword in ['printer', 'print']):
            return 'printer'
        elif any(keyword in hostname_lower for keyword in ['camera', 'cam', 'ipcam']):
            return 'camera'
        
        # Détection par services/ports
        if 80 in ports or 443 in ports:
            if ip in self.config_reseau["interfaces_communes"]:
                return 'router'
            else:
                return 'access_point'
        elif 22 in ports:
            return 'router'
        elif 23 in ports:
            return 'modem'
        
        return 'unknown'
    
    def _detecter_vendor(self, hostname, services):
        """Détection fabricant"""
        hostname_lower = hostname.lower()
        
        vendors = {
            'cisco': ['cisco', 'linksys'],
            'netgear': ['netgear'],
            'tp-link': ['tp-link', 'tplink'],
            'asus': ['asus'],
            'd-link': ['dlink', 'd-link'],
            'belkin': ['belkin'],
            'buffalo': ['buffalo'],
            'ubiquiti': ['ubiquiti', 'unifi'],
            'mikrotik': ['mikrotik'],
            'huawei': ['huawei'],
            'zte': ['zte'],
            'fritz': ['fritz', 'avm']
        }
        
        for vendor, keywords in vendors.items():
            if any(keyword in hostname_lower for keyword in keywords):
                return vendor.title()
        
        return 'Inconnu'
    
    def _tester_acces_web(self, ip):
        """Test accès interface web"""
        try:
            # Test HTTP
            response = requests.get(f"http://{ip}", timeout=3)
            if response.status_code == 200:
                return True
        except:
            pass
        
        try:
            # Test HTTPS
            response = requests.get(f"https://{ip}", timeout=3, verify=False)
            if response.status_code == 200:
                return True
        except:
            pass
        
        return False
    
    def _analyser_securite_reseau(self, equipements):
        """Analyse sécurité réseau"""
        print(f"\n🛡️ ANALYSE SÉCURITÉ RÉSEAU:")
        
        vulnerabilites = []
        equipements_accessibles = []
        
        for equipement in equipements:
            # Équipements accessibles
            if equipement['accessible']:
                equipements_accessibles.append(equipement['ip'])
            
            # Détection vulnérabilités
            if 23 in equipement['ports_ouverts']:  # Telnet
                vulnerabilites.append({
                    'ip': equipement['ip'],
                    'vulnerability': 'Telnet non sécurisé',
                    'risk': 'ÉLEVÉ'
                })
            
            if 80 in equipement['ports_ouverts'] and 443 not in equipement['ports_ouverts']:
                vulnerabilites.append({
                    'ip': equipement['ip'],
                    'vulnerability': 'HTTP sans HTTPS',
                    'risk': 'MOYEN'
                })
        
        print(f"📊 Équipements détectés: {len(equipements)}")
        print(f"📊 Interfaces web accessibles: {len(equipements_accessibles)}")
        
        if equipements_accessibles:
            print(f"\n🔓 INTERFACES WEB ACCESSIBLES:")
            for ip in equipements_accessibles:
                print(f"   🌐 http://{ip}")
        
        if vulnerabilites:
            print(f"\n⚠️ VULNÉRABILITÉS DÉTECTÉES:")
            for vuln in vulnerabilites:
                risk_icon = "🔴" if vuln['risk'] == 'ÉLEVÉ' else "🟡"
                print(f"   {risk_icon} {vuln['ip']}: {vuln['vulnerability']}")
    
    def connecter_equipement(self, ip):
        """Connexion équipement réseau"""
        print(f"🔗 CONNEXION ÉQUIPEMENT: {ip}")
        print("=" * 30)
        
        # Vérification équipement
        equipement_trouve = None
        for equipement in self.modems_detectes + self.routeurs_detectes:
            if equipement['ip'] == ip:
                equipement_trouve = equipement
                break
        
        if not equipement_trouve:
            print(f"❌ Équipement {ip} non trouvé")
            print("🔍 Lancez d'abord un scan réseau")
            return False
        
        print(f"🌐 Équipement: {equipement_trouve['hostname']}")
        print(f"📡 Type: {self.types_equipements.get(equipement_trouve['type'], equipement_trouve['type'])}")
        print(f"🏭 Fabricant: {equipement_trouve['vendor']}")
        
        # Test accès web
        if equipement_trouve['accessible']:
            print(f"🌐 Interface web disponible")
            
            # Tentative connexion automatique
            success = self._tenter_connexion_automatique(ip)
            
            if success:
                print(f"✅ Connexion réussie à {ip}")
                self._afficher_informations_equipement(ip)
            else:
                print(f"🔑 Connexion manuelle requise")
                print(f"🌐 Ouvrez: http://{ip} dans votre navigateur")
                
                # Affichage credentials par défaut
                print(f"\n🔑 CREDENTIALS PAR DÉFAUT À TESTER:")
                for username, password in self.config_reseau["credentials_defaut"]:
                    user_display = username if username else "(vide)"
                    pass_display = password if password else "(vide)"
                    print(f"   👤 {user_display} / 🔑 {pass_display}")
        else:
            print(f"❌ Interface web non accessible")
            
            # Tentative autres protocoles
            if 22 in equipement_trouve['ports_ouverts']:
                print(f"🔧 SSH disponible: ssh admin@{ip}")
            if 23 in equipement_trouve['ports_ouverts']:
                print(f"⚠️ Telnet disponible: telnet {ip}")
        
        return equipement_trouve['accessible']
    
    def _tenter_connexion_automatique(self, ip):
        """Tentative connexion automatique"""
        print("🔄 Tentative connexion automatique...")
        
        for username, password in self.config_reseau["credentials_defaut"]:
            try:
                # Test connexion HTTP avec auth
                response = requests.get(f"http://{ip}", 
                                      auth=(username, password), 
                                      timeout=5)
                
                if response.status_code == 200:
                    print(f"✅ Connexion réussie avec {username}/{password}")
                    return True
                    
            except:
                continue
        
        return False
    
    def _afficher_informations_equipement(self, ip):
        """Affichage informations équipement"""
        print(f"\n📊 INFORMATIONS ÉQUIPEMENT {ip}:")
        
        try:
            # Récupération page principale
            response = requests.get(f"http://{ip}", timeout=5)
            
            if response.status_code == 200:
                content = response.text
                
                # Extraction informations basiques
                if 'router' in content.lower():
                    print("   📡 Type: Routeur")
                elif 'modem' in content.lower():
                    print("   📡 Type: Modem")
                
                # Recherche version firmware
                version_match = re.search(r'version[:\s]+([0-9\.]+)', content, re.IGNORECASE)
                if version_match:
                    print(f"   🔧 Version: {version_match.group(1)}")
                
                # Recherche modèle
                model_match = re.search(r'model[:\s]+([A-Z0-9\-]+)', content, re.IGNORECASE)
                if model_match:
                    print(f"   📱 Modèle: {model_match.group(1)}")
                
                print("   ✅ Interface web accessible")
            
        except Exception as e:
            print(f"   ⚠️ Erreur récupération infos: {e}")
    
    def configurer_equipement_reseau(self):
        """Configuration équipement réseau"""
        print("⚙️ CONFIGURATION ÉQUIPEMENT RÉSEAU")
        print("=" * 40)
        
        if not self.modems_detectes and not self.routeurs_detectes:
            print("❌ Aucun équipement détecté")
            print("🔍 Lancez d'abord un scan réseau")
            return
        
        print("🌐 Équipements disponibles:")
        equipements = self.modems_detectes + self.routeurs_detectes
        
        for i, equipement in enumerate(equipements, 1):
            access_icon = "🔓" if equipement['accessible'] else "🔒"
            print(f"   {i}. {access_icon} {equipement['ip']} - {equipement['hostname']} ({equipement['type']})")
        
        choix = input("\n➤ Choisir équipement (numéro): ").strip()
        
        if choix.isdigit() and 1 <= int(choix) <= len(equipements):
            equipement = equipements[int(choix)-1]
            
            print(f"\n⚙️ CONFIGURATION {equipement['ip']}:")
            print("1. 🌐 Ouvrir interface web")
            print("2. 🔑 Tester credentials")
            print("3. 📊 Informations détaillées")
            print("4. 🔧 Configuration WiFi")
            print("5. 🛡️ Configuration sécurité")
            
            config_choix = input("➤ Choisir action: ").strip()
            
            if config_choix == "1":
                print(f"🌐 Ouverture http://{equipement['ip']}")
                os.system(f"start http://{equipement['ip']}" if os.name == 'nt' else f"xdg-open http://{equipement['ip']}")
            elif config_choix == "2":
                self._tenter_connexion_automatique(equipement['ip'])
            elif config_choix == "3":
                self._afficher_informations_equipement(equipement['ip'])
            elif config_choix == "4":
                print("🔧 Configuration WiFi - Accès interface web requis")
                print(f"🌐 http://{equipement['ip']}")
            elif config_choix == "5":
                print("🛡️ Configuration sécurité - Accès interface web requis")
                print(f"🌐 http://{equipement['ip']}")
    
    def generer_rapport_reseau(self):
        """Rapport complet réseau"""
        print("📋 RAPPORT RÉSEAU COMPLET")
        print("=" * 25)
        
        tous_equipements = self.modems_detectes + self.routeurs_detectes
        
        rapport = {
            "operateur": self.user,
            "timestamp": datetime.now().isoformat(),
            "equipements_detectes": len(tous_equipements),
            "modems": len(self.modems_detectes),
            "routeurs": len(self.routeurs_detectes),
            "config_reseau": self.config_reseau,
            "equipements_details": tous_equipements,
            "reseau_local": self._detecter_reseau_local(),
            "passerelle_defaut": self._get_passerelle_defaut(),
            "serveurs_dns": self._get_serveurs_dns()
        }
        
        filename = f"rapport_reseau_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(rapport, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Rapport sauvegardé: {filename}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
        
        print(f"\n📊 RÉSUMÉ RÉSEAU:")
        print(f"   🌐 Équipements détectés: {len(tous_equipements)}")
        print(f"   📡 Modems: {len(self.modems_detectes)}")
        print(f"   🌐 Routeurs: {len(self.routeurs_detectes)}")
        print(f"   🔓 Accessibles: {sum(1 for e in tous_equipements if e['accessible'])}")
        
        return rapport
    
    def _get_passerelle_defaut(self):
        """Récupération passerelle par défaut"""
        try:
            if os.name == 'nt':
                result = subprocess.run(['ipconfig'], capture_output=True, text=True)
                # Parse pour passerelle
                return "***********"  # Simulation
            else:
                result = subprocess.run(['ip', 'route', 'show', 'default'], capture_output=True, text=True)
                if result.stdout:
                    return result.stdout.split()[2]
                return "Non disponible"
        except:
            return "Non disponible"
    
    def _get_serveurs_dns(self):
        """Récupération serveurs DNS"""
        try:
            if os.name == 'nt':
                result = subprocess.run(['nslookup'], capture_output=True, text=True)
                return ["*******", "*******"]  # Simulation
            else:
                with open('/etc/resolv.conf', 'r') as f:
                    dns_servers = []
                    for line in f:
                        if line.startswith('nameserver'):
                            dns_servers.append(line.split()[1])
                    return dns_servers
        except:
            return ["*******", "*******"]

def main():
    """Fonction principale"""
    print("🌐 CONNEXION MODEM/ROUTER")
    print("=" * 30)
    print(f"👤 Opérateur: SamNord@110577")
    print("📡 Gestion équipements réseau")
    print()
    
    connexion = ConnexionModemRouter()
    
    while True:
        print("\n🎯 MENU RÉSEAU:")
        print("1. 🌐 Scanner équipements réseau")
        print("2. 🔗 Connecter équipement")
        print("3. ⚙️ Configurer équipement")
        print("4. 📊 Informations réseau")
        print("5. 📋 Rapport réseau complet")
        print("6. 📊 Statistiques")
        print("0. ❌ Quitter")
        
        choix = input("\n➤ Votre choix (0-6): ").strip()
        
        if choix == "1":
            connexion.scanner_equipements_reseau()
        elif choix == "2":
            if not connexion.modems_detectes and not connexion.routeurs_detectes:
                print("⚠️ Lancez d'abord un scan réseau")
            else:
                equipements = connexion.modems_detectes + connexion.routeurs_detectes
                print("\n🌐 Équipements disponibles:")
                for i, equipement in enumerate(equipements, 1):
                    print(f"   {i}. {equipement['ip']} - {equipement['hostname']}")
                
                choix_eq = input("➤ Choisir équipement (numéro ou IP): ").strip()
                
                if choix_eq.isdigit():
                    idx = int(choix_eq) - 1
                    if 0 <= idx < len(equipements):
                        ip = equipements[idx]['ip']
                        connexion.connecter_equipement(ip)
                else:
                    connexion.connecter_equipement(choix_eq)
        elif choix == "3":
            connexion.configurer_equipement_reseau()
        elif choix == "4":
            print(f"🌐 Réseau local: {connexion._detecter_reseau_local()}")
            print(f"🚪 Passerelle: {connexion._get_passerelle_defaut()}")
            print(f"🌐 DNS: {', '.join(connexion._get_serveurs_dns())}")
        elif choix == "5":
            connexion.generer_rapport_reseau()
        elif choix == "6":
            tous_eq = connexion.modems_detectes + connexion.routeurs_detectes
            print(f"📊 Équipements: {len(tous_eq)}")
            print(f"📡 Modems: {len(connexion.modems_detectes)}")
            print(f"🌐 Routeurs: {len(connexion.routeurs_detectes)}")
        elif choix == "0":
            print("👋 Connexion fermée")
            break
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
