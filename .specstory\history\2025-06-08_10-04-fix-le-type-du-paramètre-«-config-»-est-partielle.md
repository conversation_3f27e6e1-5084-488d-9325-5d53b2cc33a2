<!-- Generated by SpecStory -->

# /fix Le type du paramètre « config » est partielle (2025-06-08 11:04:33)

_**User**_

/fix Le type du paramètre « config » est partiellement inconnu
  Le type de paramètre est « dict[Unknown, Unknown] »

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Remplacer l'annotation de type générique `dict` par `dict[str, object]` pour préciser les types de clé et de valeur du paramètre `config`.

````python
# ...existing code...
    def set_config(self, config: dict[str, object]):
# ...existing code...
````

---

