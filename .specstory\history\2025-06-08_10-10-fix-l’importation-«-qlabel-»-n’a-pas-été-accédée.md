<!-- Generated by SpecStory -->

# /fix L’importation « QLabel » n’a pas été accédée (2025-06-08 11:10:34)

_**User**_

/fix L’importation « QLabel » n’a pas été accédée

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Supprimer l'import inutile de `QLabel` car il n'est pas utilisé dans le fichier.

````python
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox
)
# ...existing code...
````

---

