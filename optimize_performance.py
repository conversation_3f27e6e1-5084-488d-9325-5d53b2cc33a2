#!/usr/bin/env python3
"""
Script d'optimisation automatique des performances pour GestImmob
Détecte les spécifications de la machine et applique le profil optimal
"""

import json
import psutil
import platform
import shutil
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """Optimiseur de performance automatique"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.profiles_file = self.project_root / "performance_profiles.json"
        self.config_file = self.project_root / "config_advanced.json"
        
        self.system_specs = self.detect_system_specs()
        self.profiles = self.load_performance_profiles()
        
    def detect_system_specs(self) -> Dict[str, Any]:
        """Détecte les spécifications du système"""
        logger.info("Détection des spécifications système...")
        
        # Mémoire RAM
        memory = psutil.virtual_memory()
        ram_gb = round(memory.total / (1024**3), 1)
        
        # Processeur
        cpu_cores = psutil.cpu_count(logical=False)  # Cœurs physiques
        cpu_threads = psutil.cpu_count(logical=True)  # Threads logiques
        cpu_freq = psutil.cpu_freq()
        cpu_freq_max = cpu_freq.max if cpu_freq else 0
        
        # Stockage
        storage_type = self.detect_storage_type()
        
        # Système d'exploitation
        os_info = {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "architecture": platform.architecture()[0]
        }
        
        specs = {
            "ram_gb": ram_gb,
            "cpu_cores": cpu_cores,
            "cpu_threads": cpu_threads,
            "cpu_freq_max": cpu_freq_max,
            "storage_type": storage_type,
            "os_info": os_info
        }
        
        logger.info(f"Spécifications détectées:")
        logger.info(f"  RAM: {ram_gb} GB")
        logger.info(f"  CPU: {cpu_cores} cœurs / {cpu_threads} threads")
        logger.info(f"  Stockage: {storage_type}")
        logger.info(f"  OS: {os_info['system']} {os_info['release']}")
        
        return specs
        
    def detect_storage_type(self) -> str:
        """Détecte le type de stockage (SSD/HDD)"""
        try:
            # Méthode simple: vérifier la vitesse de lecture
            import time
            
            test_file = Path("temp_speed_test.tmp")
            test_data = b"0" * (1024 * 1024)  # 1MB
            
            # Test d'écriture
            start_time = time.time()
            with open(test_file, 'wb') as f:
                f.write(test_data)
                f.flush()
            write_time = time.time() - start_time
            
            # Test de lecture
            start_time = time.time()
            with open(test_file, 'rb') as f:
                f.read()
            read_time = time.time() - start_time
            
            # Nettoyage
            test_file.unlink(missing_ok=True)
            
            # Estimation basée sur la vitesse
            avg_time = (write_time + read_time) / 2
            if avg_time < 0.1:  # Très rapide = SSD
                return "SSD"
            elif avg_time < 0.5:  # Rapide = SSD/HDD hybride
                return "SSD/HDD"
            else:  # Lent = HDD
                return "HDD"
                
        except Exception:
            # Fallback: vérifier les disques système
            try:
                partitions = psutil.disk_partitions()
                for partition in partitions:
                    if 'fixed' in partition.opts.lower():
                        # Heuristique simple basée sur le nom
                        if 'ssd' in partition.device.lower():
                            return "SSD"
                        elif 'nvme' in partition.device.lower():
                            return "NVMe SSD"
                return "HDD"  # Par défaut
            except Exception:
                return "Unknown"
                
    def load_performance_profiles(self) -> Dict[str, Any]:
        """Charge les profils de performance"""
        if not self.profiles_file.exists():
            logger.error(f"Fichier de profils non trouvé: {self.profiles_file}")
            return {}
            
        try:
            with open(self.profiles_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Erreur lors du chargement des profils: {e}")
            return {}
            
    def determine_optimal_profile(self) -> str:
        """Détermine le profil optimal basé sur les spécifications"""
        if not self.profiles:
            return "medium"  # Profil par défaut
            
        auto_detection = self.profiles.get("auto_detection", {})
        if not auto_detection.get("enabled", False):
            return auto_detection.get("fallback_profile", "medium")
            
        rules = auto_detection.get("rules", [])
        specs = self.system_specs
        
        for rule in rules:
            condition = rule.get("condition", "")
            try:
                # Remplacer les variables dans la condition
                condition_eval = condition
                for key, value in specs.items():
                    if isinstance(value, (int, float)):
                        condition_eval = condition_eval.replace(key, str(value))
                
                # Évaluer la condition (attention: eval peut être dangereux)
                # Ici on fait confiance au fichier de configuration
                if eval(condition_eval):
                    profile = rule.get("profile")
                    logger.info(f"Profil sélectionné: {profile} (condition: {condition})")
                    return profile
                    
            except Exception as e:
                logger.warning(f"Erreur lors de l'évaluation de la règle '{condition}': {e}")
                continue
                
        # Aucune règle ne correspond, utiliser le fallback
        fallback = auto_detection.get("fallback_profile", "medium")
        logger.info(f"Aucune règle ne correspond, utilisation du profil par défaut: {fallback}")
        return fallback
        
    def apply_profile(self, profile_name: str) -> bool:
        """Applique un profil de performance"""
        if profile_name not in self.profiles.get("profiles", {}):
            logger.error(f"Profil '{profile_name}' non trouvé")
            return False
            
        profile = self.profiles["profiles"][profile_name]
        settings = profile.get("settings", {})
        
        logger.info(f"Application du profil: {profile['name']}")
        logger.info(f"Description: {profile['description']}")
        
        # Charger la configuration actuelle
        current_config = self.load_current_config()
        
        # Fusionner avec les nouveaux paramètres
        updated_config = self.merge_configs(current_config, settings)
        
        # Ajouter les informations de profil
        updated_config["performance_profile"] = {
            "name": profile_name,
            "applied_at": self.get_current_timestamp(),
            "system_specs": self.system_specs,
            "description": profile["description"]
        }
        
        # Sauvegarder la configuration
        return self.save_config(updated_config)
        
    def load_current_config(self) -> Dict[str, Any]:
        """Charge la configuration actuelle"""
        if not self.config_file.exists():
            logger.warning("Fichier de configuration non trouvé, création d'une configuration par défaut")
            return {}
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration: {e}")
            return {}
            
    def merge_configs(self, base_config: Dict, new_settings: Dict) -> Dict:
        """Fusionne deux configurations de manière récursive"""
        result = base_config.copy()
        
        for key, value in new_settings.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def save_config(self, config: Dict[str, Any]) -> bool:
        """Sauvegarde la configuration"""
        try:
            # Créer une sauvegarde de l'ancienne configuration
            if self.config_file.exists():
                backup_file = self.config_file.with_suffix('.json.backup')
                shutil.copy2(self.config_file, backup_file)
                logger.info(f"Sauvegarde créée: {backup_file}")
                
            # Sauvegarder la nouvelle configuration
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Configuration sauvegardée: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde: {e}")
            return False
            
    def get_current_timestamp(self) -> str:
        """Retourne le timestamp actuel"""
        from datetime import datetime
        return datetime.now().isoformat()
        
    def show_optimization_tips(self, profile_name: str):
        """Affiche les conseils d'optimisation pour le profil"""
        tips = self.profiles.get("optimization_tips", {}).get(profile_name, [])
        
        if tips:
            logger.info("Conseils d'optimisation:")
            for i, tip in enumerate(tips, 1):
                logger.info(f"  {i}. {tip}")
        else:
            logger.info("Aucun conseil d'optimisation disponible pour ce profil")
            
    def show_benchmarks(self, profile_name: str):
        """Affiche les benchmarks attendus pour le profil"""
        benchmarks = self.profiles.get("benchmarks", {})
        
        if benchmarks:
            logger.info("Performances attendues:")
            for category, tests in benchmarks.items():
                logger.info(f"  {category}:")
                for test_name, results in tests.items():
                    if profile_name in results:
                        logger.info(f"    {test_name}: {results[profile_name]}")
                        
    def optimize(self, force_profile: Optional[str] = None) -> bool:
        """Optimise automatiquement les performances"""
        logger.info("=== Optimisation automatique des performances ===")
        
        # Déterminer le profil à utiliser
        if force_profile:
            if force_profile not in self.profiles.get("profiles", {}):
                logger.error(f"Profil forcé '{force_profile}' non trouvé")
                return False
            profile_name = force_profile
            logger.info(f"Utilisation du profil forcé: {profile_name}")
        else:
            profile_name = self.determine_optimal_profile()
            
        # Appliquer le profil
        if not self.apply_profile(profile_name):
            return False
            
        # Afficher les conseils et benchmarks
        self.show_optimization_tips(profile_name)
        self.show_benchmarks(profile_name)
        
        logger.info("=== Optimisation terminée ===")
        return True

def main():
    """Fonction principale"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Optimiseur de performance pour GestImmob")
    parser.add_argument("--profile", help="Force l'utilisation d'un profil spécifique")
    parser.add_argument("--list", action="store_true", help="Liste les profils disponibles")
    parser.add_argument("--specs", action="store_true", help="Affiche les spécifications système")
    
    args = parser.parse_args()
    
    optimizer = PerformanceOptimizer()
    
    if args.list:
        profiles = optimizer.profiles.get("profiles", {})
        print("Profils disponibles:")
        for name, profile in profiles.items():
            print(f"  {name}: {profile['name']}")
            print(f"    {profile['description']}")
        return
        
    if args.specs:
        specs = optimizer.system_specs
        print("Spécifications système:")
        for key, value in specs.items():
            print(f"  {key}: {value}")
        return
        
    # Optimisation
    success = optimizer.optimize(force_profile=args.profile)
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
