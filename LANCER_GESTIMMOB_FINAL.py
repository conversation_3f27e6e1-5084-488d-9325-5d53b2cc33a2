#!/usr/bin/env python3
"""
[LANCE] LANCEUR GESTIMMOB v3.0.0 FINAL
Script de lancement pour la version complète finale
"""

import sys
# import os
import subprocess
from pathlib import Path

def main():
    """Lance GestImmob v3.0.0 Final"""
    
    print("🏠 " + "="*70)
    print("[LANCE] LANCEMENT DE GESTIMMOB v3.0.0 - VERSION COMPLÈTE FINALE")
    print("[STATS] Logiciel de Gestion Immobilière Professionnel")
    print("="*74)
    
    # Vérifier Python
    print(f"🐍 Python: {sys.version}")
    
    # Vérifier les fichiers
    files_to_check = [
        "gestimmob_complet_final.py"
    ]
    
    print("\n🔍 Vérification des fichiers...")
    for file in files_to_check:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"[ECHEC] {file} - MANQUANT")
            return
    
    # Vérifier PySide6
    print("\n📦 Vérification des dépendances...")
    try:
        import PySide6
        print(f"✅ PySide6: {PySide6.__version__}")
    except ImportError:
        print("[ECHEC] PySide6 non installé")
        print("💡 Installation: pip install PySide6")
        return
    
    print("\n🎨 Configuration de la version finale:")
    print("• ✅ Interface moderne avec cartes et combos")
    print("• ✅ Thème fixe professionnel (sans couleurs dynamiques)")
    print("• ✅ Base de données SQLite complète avec 10 tables")
    print("• ✅ 13 modules intégrés et opérationnels")
    print("• ✅ Tous les problèmes de code corrigés")
    print("• ✅ Optimisé pour machines performantes")
    
    print("\n📋 Modules disponibles:")
    print("• [STATS] Tableau de Bord avec cartes d'information")
    print("• 🏠 Gestion des Biens (interface moderne)")
    print("• 📦 Inventaire (filtres avancés)")
    print("• 🏢 Fournisseurs (informations complètes)")
    print("• 👥 Clients")
    print("• 📄 Facturation")
    print("• 📋 Bons de Commande")
    print("• ♻️ Gestion Réformes")
    print("• 📁 Documents")
    print("• 🧮 Calculatrice")
    print("• [STATS] Rapports")
    print("• [OUTIL] Maintenance")
    print("• ⚙️ Paramètres")
    
    print("\n[LANCE] Lancement de GestImmob v3.0.0...")
    print("👤 Connexion: admin / Admin@1234")
    print("="*74)
    
    # Lancer l'application
    try:
        subprocess.run([sys.executable, "gestimmob_complet_final.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 Fermeture de GestImmob...")
    except Exception as e:
        print(f"\n[ECHEC] Erreur lors du lancement: {e}")

if __name__ == "__main__":
    main()

