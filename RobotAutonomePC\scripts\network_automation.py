#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 ROBOT IA - SCRIPTS RÉSEAU AUTOMATIQUES
Génération automatique de scripts pour modems et routeurs
Créé par Augment Agent
"""

import os
import json
import subprocess
import socket
import requests
from datetime import datetime

class NetworkScriptGenerator:
    """🔗 Générateur de scripts réseau automatiques"""
    
    def __init__(self):
        self.supported_devices = {
            "routers": [
                "TP-Link", "Netgear", "Linksys", "ASUS", "D-Link", 
                "Cisco", "Huawei", "ZTE", "Mikrotik", "Ubiquiti"
            ],
            "modems": [
                "Technicolor", "Sagemcom", "Huawei", "ZTE", "Motorola",
                "Arris", "Netgear", "TP-Link", "D-Link"
            ]
        }
        
    def generate_router_connection_script(self, router_brand, router_model="Generic"):
        """Génère un script de connexion pour routeur"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 SCRIPT CONNEXION ROUTEUR {router_brand.upper()}
Généré automatiquement par Robot IA Autonome
Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

import requests
import json
import base64
import hashlib
import time
from urllib.parse import urljoin

class {router_brand}RouterConnector:
    """Connecteur automatique pour routeur {router_brand}"""
    
    def __init__(self, ip_address="***********", username="admin", password="admin"):
        self.ip = ip_address
        self.username = username
        self.password = password
        self.base_url = f"http://{{self.ip}}"
        self.session = requests.Session()
        self.token = None
        
    def detect_router_info(self):
        """Détecte automatiquement les informations du routeur"""
        try:
            # Scan des ports communs
            common_ports = [80, 8080, 443, 8443]
            for port in common_ports:
                try:
                    response = requests.get(f"http://{{self.ip}}:{{port}}", timeout=3)
                    if response.status_code == 200:
                        print(f"✅ Routeur détecté sur port {{port}}")
                        return port
                except:
                    continue
            return 80
        except Exception as e:
            print(f"❌ Erreur détection: {{e}}")
            return 80
    
    def connect_to_router(self):
        """Connexion automatique au routeur"""
        try:
            print(f"🔗 Connexion au routeur {router_brand} ({{self.ip}})...")
            
            # Méthodes de connexion selon la marque
            if "{router_brand.lower()}" == "tp-link":
                return self._connect_tplink()
            elif "{router_brand.lower()}" == "netgear":
                return self._connect_netgear()
            elif "{router_brand.lower()}" == "linksys":
                return self._connect_linksys()
            elif "{router_brand.lower()}" == "asus":
                return self._connect_asus()
            elif "{router_brand.lower()}" == "d-link":
                return self._connect_dlink()
            else:
                return self._connect_generic()
                
        except Exception as e:
            print(f"❌ Erreur connexion: {{e}}")
            return False
    
    def _connect_tplink(self):
        """Connexion spécifique TP-Link"""
        try:
            # Authentification TP-Link
            auth_data = {{
                "method": "do",
                "login": {{
                    "username": self.username,
                    "password": base64.b64encode(self.password.encode()).decode()
                }}
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/cgi-bin/luci/;stok=/login",
                json=auth_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "stok" in data:
                    self.token = data["stok"]
                    print("✅ Connexion TP-Link réussie")
                    return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur TP-Link: {{e}}")
            return False
    
    def _connect_netgear(self):
        """Connexion spécifique Netgear"""
        try:
            # Authentification Netgear
            auth_string = f"{{self.username}}:{{self.password}}"
            auth_bytes = base64.b64encode(auth_string.encode()).decode()
            
            headers = {{
                "Authorization": f"Basic {{auth_bytes}}",
                "Content-Type": "application/x-www-form-urlencoded"
            }}
            
            response = self.session.get(
                f"{{self.base_url}}/setup.cgi",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Connexion Netgear réussie")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur Netgear: {{e}}")
            return False
    
    def _connect_generic(self):
        """Connexion générique pour autres marques"""
        try:
            # Tentative connexion basique
            auth_string = f"{{self.username}}:{{self.password}}"
            auth_bytes = base64.b64encode(auth_string.encode()).decode()
            
            headers = {{
                "Authorization": f"Basic {{auth_bytes}}"
            }}
            
            response = self.session.get(
                f"{{self.base_url}}/",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Connexion générique réussie")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur connexion générique: {{e}}")
            return False
    
    def get_router_status(self):
        """Récupère le statut du routeur"""
        try:
            if not self.token:
                print("❌ Non connecté au routeur")
                return None
                
            # Récupération des informations système
            status_data = {{
                "method": "get",
                "system": {{
                    "get_status": {{}}
                }}
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/cgi-bin/luci/;stok={{self.token}}/admin/system",
                json=status_data,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            return None
            
        except Exception as e:
            print(f"❌ Erreur récupération statut: {{e}}")
            return None
    
    def configure_wifi(self, ssid, password, security="WPA2"):
        """Configure le WiFi automatiquement"""
        try:
            print(f"📶 Configuration WiFi: {{ssid}}")
            
            wifi_config = {{
                "method": "set",
                "wireless": {{
                    "set_guest_wifi": {{
                        "enable": True,
                        "ssid": ssid,
                        "password": password,
                        "security": security
                    }}
                }}
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/cgi-bin/luci/;stok={{self.token}}/admin/wireless",
                json=wifi_config,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ WiFi configuré avec succès")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur configuration WiFi: {{e}}")
            return False
    
    def reboot_router(self):
        """Redémarre le routeur"""
        try:
            print("🔄 Redémarrage du routeur...")
            
            reboot_data = {{
                "method": "do",
                "system": {{
                    "reboot": {{}}
                }}
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/cgi-bin/luci/;stok={{self.token}}/admin/system",
                json=reboot_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Redémarrage initié")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur redémarrage: {{e}}")
            return False

def main():
    """Fonction principale de connexion"""
    print("🌐 SCRIPT CONNEXION ROUTEUR {router_brand.upper()}")
    print("=" * 50)
    
    # Configuration par défaut
    router_ip = input("📍 IP du routeur (***********): ") or "***********"
    username = input("👤 Nom d'utilisateur (admin): ") or "admin"
    password = input("🔐 Mot de passe (admin): ") or "admin"
    
    # Connexion au routeur
    connector = {router_brand}RouterConnector(router_ip, username, password)
    
    if connector.connect_to_router():
        print("\\n🎯 ACTIONS DISPONIBLES:")
        print("1. Afficher statut")
        print("2. Configurer WiFi")
        print("3. Redémarrer routeur")
        print("4. Quitter")
        
        while True:
            choice = input("\\n➤ Votre choix: ")
            
            if choice == "1":
                status = connector.get_router_status()
                if status:
                    print("📊 Statut routeur:", json.dumps(status, indent=2))
                    
            elif choice == "2":
                ssid = input("📶 Nom WiFi (SSID): ")
                wifi_password = input("🔐 Mot de passe WiFi: ")
                connector.configure_wifi(ssid, wifi_password)
                
            elif choice == "3":
                confirm = input("⚠️ Confirmer redémarrage (y/N): ")
                if confirm.lower() == "y":
                    connector.reboot_router()
                    
            elif choice == "4":
                print("👋 Au revoir !")
                break
                
            else:
                print("❌ Choix invalide")
    else:
        print("❌ Impossible de se connecter au routeur")

if __name__ == "__main__":
    main()
'''
        
        # Sauvegarde du script
        script_filename = f"connect_{router_brand.lower()}_router_{timestamp}.py"
        script_path = os.path.join("scripts", "routers", script_filename)
        
        os.makedirs(os.path.dirname(script_path), exist_ok=True)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✅ Script routeur {router_brand} créé: {script_path}")
        return script_path
    
    def generate_modem_connection_script(self, modem_brand, modem_model="Generic"):
        """Génère un script de connexion pour modem"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📡 SCRIPT CONNEXION MODEM {modem_brand.upper()}
Généré automatiquement par Robot IA Autonome
Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

import requests
import json
import base64
import time
import re
from urllib.parse import urljoin

class {modem_brand}ModemConnector:
    """Connecteur automatique pour modem {modem_brand}"""
    
    def __init__(self, ip_address="*************", username="admin", password="admin"):
        self.ip = ip_address
        self.username = username
        self.password = password
        self.base_url = f"http://{{self.ip}}"
        self.session = requests.Session()
        self.csrf_token = None
        
    def detect_modem_interface(self):
        """Détecte automatiquement l'interface du modem"""
        try:
            print(f"🔍 Détection interface modem {modem_brand}...")
            
            # IPs communes pour modems
            common_ips = ["*************", "***********", "***********", "********"]
            
            for ip in common_ips:
                try:
                    response = requests.get(f"http://{{ip}}", timeout=3)
                    if response.status_code == 200:
                        self.ip = ip
                        self.base_url = f"http://{{ip}}"
                        print(f"✅ Modem détecté sur {{ip}}")
                        return True
                except:
                    continue
            
            print("❌ Aucun modem détecté")
            return False
            
        except Exception as e:
            print(f"❌ Erreur détection: {{e}}")
            return False
    
    def connect_to_modem(self):
        """Connexion automatique au modem"""
        try:
            print(f"📡 Connexion au modem {modem_brand} ({{self.ip}})...")
            
            # Méthodes de connexion selon la marque
            if "{modem_brand.lower()}" == "technicolor":
                return self._connect_technicolor()
            elif "{modem_brand.lower()}" == "sagemcom":
                return self._connect_sagemcom()
            elif "{modem_brand.lower()}" == "huawei":
                return self._connect_huawei()
            elif "{modem_brand.lower()}" == "zte":
                return self._connect_zte()
            else:
                return self._connect_generic_modem()
                
        except Exception as e:
            print(f"❌ Erreur connexion: {{e}}")
            return False
    
    def _connect_technicolor(self):
        """Connexion spécifique Technicolor"""
        try:
            # Page de login Technicolor
            login_page = self.session.get(f"{{self.base_url}}/login.html")
            
            # Extraction du token CSRF
            csrf_match = re.search(r'name="CSRFtoken" value="([^"]+)"', login_page.text)
            if csrf_match:
                self.csrf_token = csrf_match.group(1)
            
            # Authentification
            login_data = {{
                "username": self.username,
                "password": self.password,
                "CSRFtoken": self.csrf_token
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/authenticate",
                data=login_data,
                timeout=10
            )
            
            if "dashboard" in response.url or response.status_code == 200:
                print("✅ Connexion Technicolor réussie")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur Technicolor: {{e}}")
            return False
    
    def _connect_sagemcom(self):
        """Connexion spécifique Sagemcom"""
        try:
            # Authentification Sagemcom
            auth_data = {{
                "username": self.username,
                "password": self.password
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/cgi-bin/login.cgi",
                data=auth_data,
                timeout=10
            )
            
            if response.status_code == 200 and "success" in response.text.lower():
                print("✅ Connexion Sagemcom réussie")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur Sagemcom: {{e}}")
            return False
    
    def get_modem_status(self):
        """Récupère le statut du modem"""
        try:
            print("📊 Récupération statut modem...")
            
            # Informations de connexion
            status_response = self.session.get(
                f"{{self.base_url}}/cgi-bin/status.cgi",
                timeout=10
            )
            
            if status_response.status_code == 200:
                return {{
                    "connection_status": "Connected",
                    "ip_address": self.ip,
                    "response_time": status_response.elapsed.total_seconds(),
                    "content_length": len(status_response.content)
                }}
            return None
            
        except Exception as e:
            print(f"❌ Erreur récupération statut: {{e}}")
            return None
    
    def restart_modem(self):
        """Redémarre le modem"""
        try:
            print("🔄 Redémarrage du modem...")
            
            restart_data = {{
                "action": "reboot",
                "CSRFtoken": self.csrf_token
            }}
            
            response = self.session.post(
                f"{{self.base_url}}/cgi-bin/reboot.cgi",
                data=restart_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Redémarrage initié")
                print("⏳ Attente 60 secondes...")
                time.sleep(60)
                return True
            return False
            
        except Exception as e:
            print(f"❌ Erreur redémarrage: {{e}}")
            return False

def main():
    """Fonction principale de connexion modem"""
    print("📡 SCRIPT CONNEXION MODEM {modem_brand.upper()}")
    print("=" * 50)
    
    # Configuration
    modem_ip = input("📍 IP du modem (*************): ") or "*************"
    username = input("👤 Nom d'utilisateur (admin): ") or "admin"
    password = input("🔐 Mot de passe (admin): ") or "admin"
    
    # Connexion au modem
    connector = {modem_brand}ModemConnector(modem_ip, username, password)
    
    if connector.detect_modem_interface() and connector.connect_to_modem():
        print("\\n🎯 ACTIONS DISPONIBLES:")
        print("1. Afficher statut")
        print("2. Redémarrer modem")
        print("3. Quitter")
        
        while True:
            choice = input("\\n➤ Votre choix: ")
            
            if choice == "1":
                status = connector.get_modem_status()
                if status:
                    print("📊 Statut modem:", json.dumps(status, indent=2))
                    
            elif choice == "2":
                confirm = input("⚠️ Confirmer redémarrage (y/N): ")
                if confirm.lower() == "y":
                    connector.restart_modem()
                    
            elif choice == "3":
                print("👋 Au revoir !")
                break
                
            else:
                print("❌ Choix invalide")
    else:
        print("❌ Impossible de se connecter au modem")

if __name__ == "__main__":
    main()
'''
        
        # Sauvegarde du script
        script_filename = f"connect_{modem_brand.lower()}_modem_{timestamp}.py"
        script_path = os.path.join("scripts", "modems", script_filename)
        
        os.makedirs(os.path.dirname(script_path), exist_ok=True)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✅ Script modem {modem_brand} créé: {script_path}")
        return script_path

def main():
    """Fonction principale du générateur"""
    print("🌐 GÉNÉRATEUR SCRIPTS RÉSEAU AUTOMATIQUES")
    print("=" * 50)
    
    generator = NetworkScriptGenerator()
    
    print("📋 ÉQUIPEMENTS SUPPORTÉS:")
    print("🔗 Routeurs:", ", ".join(generator.supported_devices["routers"]))
    print("📡 Modems:", ", ".join(generator.supported_devices["modems"]))
    
    while True:
        print("\n🎯 QUE VOULEZ-VOUS FAIRE ?")
        print("1. Générer script routeur")
        print("2. Générer script modem")
        print("3. Quitter")
        
        choice = input("\n➤ Votre choix: ")
        
        if choice == "1":
            print("\n🔗 MARQUES ROUTEURS DISPONIBLES:")
            for i, brand in enumerate(generator.supported_devices["routers"], 1):
                print(f"{i}. {brand}")
            
            try:
                brand_choice = int(input("\n➤ Choisissez une marque: ")) - 1
                if 0 <= brand_choice < len(generator.supported_devices["routers"]):
                    brand = generator.supported_devices["routers"][brand_choice]
                    script_path = generator.generate_router_connection_script(brand)
                    print(f"✅ Script créé: {script_path}")
                else:
                    print("❌ Choix invalide")
            except ValueError:
                print("❌ Veuillez entrer un nombre")
                
        elif choice == "2":
            print("\n📡 MARQUES MODEMS DISPONIBLES:")
            for i, brand in enumerate(generator.supported_devices["modems"], 1):
                print(f"{i}. {brand}")
            
            try:
                brand_choice = int(input("\n➤ Choisissez une marque: ")) - 1
                if 0 <= brand_choice < len(generator.supported_devices["modems"]):
                    brand = generator.supported_devices["modems"][brand_choice]
                    script_path = generator.generate_modem_connection_script(brand)
                    print(f"✅ Script créé: {script_path}")
                else:
                    print("❌ Choix invalide")
            except ValueError:
                print("❌ Veuillez entrer un nombre")
                
        elif choice == "3":
            print("👋 Au revoir !")
            break
            
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
