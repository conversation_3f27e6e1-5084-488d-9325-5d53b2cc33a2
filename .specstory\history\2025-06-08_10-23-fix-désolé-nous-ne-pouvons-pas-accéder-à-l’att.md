<!-- Generated by SpecStory -->

# /fix Désolé... Nous ne pouvons pas accéder à l’att (2025-06-08 11:23:57)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « print » pour la classe « QTextDocument »
  L’attribut « print » est inconnu, Le type de « print » est inconnu

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème vient du fait que la méthode correcte pour imprimer un `QTextDocument` en PySide6 est `print_` (avec un underscore), et non `print`. Il faut donc remplacer `doc.print(printer)` par `doc.print_(printer)` partout dans le fichier.

````python
# ...existing code...
        if dialog.exec() == 1:
            doc.print_(printer)
            QMessageBox.information(self, "Impression", "Impression de la sélection envoyée à l'imprimante.")
# ...existing code...
        doc = QTextDocument()
        doc.setHtml(html)
        doc.print_(printer)
# ...existing code...
````

---

