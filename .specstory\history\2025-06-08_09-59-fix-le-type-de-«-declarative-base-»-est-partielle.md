<!-- Generated by SpecStory -->

# /fix Le type de « declarative_base » est partielle (2025-06-08 10:59:28)

_**User**_

/fix Le type de « declarative_base » est partiellement inconnu
  Le type de "declarative_base" est "(...) -> Any"

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Remplacez l'import de `declarative_base` pour utiliser le chemin recommandé par SQLAlchemy 2.x, ce qui résout le problème de typage partiellement inconnu.

````python
from sqlalchemy.orm import declarative_base
# ...existing code...
````

---

