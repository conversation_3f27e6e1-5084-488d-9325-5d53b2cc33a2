<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-07 22:46:13)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [main.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/main.py)

Le problème vient du fait que `item_id` peut être `None`, donc l'appel à `item_id.text()` provoque une erreur. Il faut vérifier que `item_id` n'est pas `None` avant d'appeler `.text()`. Voici le correctif :

````python
# ...existing code...
        item_id = self.table.item(row, 0)
        if item_id is None:
            QMessageBox.warning(self, "Erreur", "Impossible de trouver l'ID du bien à supprimer.")
            return
        user_id = item_id.text() if item_id is not None else ""
# ...existing code...
````

---

