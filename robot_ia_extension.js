const vscode = require('vscode');
const { exec } = require('child_process');
const path = require('path');

/**
 * 🤖 ROBOT IA SAMNORD - EXTENSION VS CODE
 * Extension visible dans la barre latérale
 */

let chatPanel = null;
let statusBarItem = null;

function activate(context) {
    console.log('🤖 Robot IA SamNord extension activée');
    
    // Status bar
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "🤖 Robot IA";
    statusBarItem.tooltip = "Robot IA SamNord - Cliquez pour activer";
    statusBarItem.command = "robotIA.activate";
    statusBarItem.show();
    
    // Commandes
    const activateCommand = vscode.commands.registerCommand('robotIA.activate', () => {
        vscode.window.showInformationMessage('🤖 Robot IA SamNord activé ! SamNord@110577');
        showChatPanel();
    });
    
    const chatCommand = vscode.commands.registerCommand('robotIA.chat', () => {
        showChatPanel();
    });
    
    const modulesCommand = vscode.commands.registerCommand('robotIA.modules', () => {
        showModulesQuickPick();
    });
    
    const wifiCommand = vscode.commands.registerCommand('robotIA.wifi', () => {
        executeModule('wifi_avance_complet');
    });
    
    const bluetoothCommand = vscode.commands.registerCommand('robotIA.bluetooth', () => {
        executeModule('bluetooth_complet');
    });
    
    // Providers
    const chatProvider = new ChatViewProvider(context.extensionUri);
    const modulesProvider = new ModulesTreeProvider();
    const statusProvider = new StatusTreeProvider();
    
    // Enregistrement
    context.subscriptions.push(
        activateCommand,
        chatCommand,
        modulesCommand,
        wifiCommand,
        bluetoothCommand,
        statusBarItem,
        vscode.window.registerWebviewViewProvider('robotIA.chat', chatProvider),
        vscode.window.registerTreeDataProvider('robotIA.modules', modulesProvider),
        vscode.window.registerTreeDataProvider('robotIA.status', statusProvider)
    );
    
    // Message d'activation
    vscode.window.showInformationMessage('🤖 Robot IA SamNord prêt ! Ctrl+Shift+R pour chat');
}

function showChatPanel() {
    if (chatPanel) {
        chatPanel.reveal(vscode.ViewColumn.One);
        return;
    }

    chatPanel = vscode.window.createWebviewPanel(
        'robotIAChat',
        '🤖 Robot IA SamNord - Chat',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );
    
    chatPanel.webview.html = getChatHTML();
    
    chatPanel.webview.onDidReceiveMessage(message => {
        switch (message.command) {
            case 'sendMessage':
                handleChatMessage(message.text);
                break;
            case 'executeModule':
                executeModule(message.module);
                break;
        }
    });
    
    chatPanel.onDidDispose(() => {
        chatPanel = null;
    });
}

function getChatHTML() {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { 
                font-family: 'Segoe UI', sans-serif;
                background: #1e1e1e;
                color: #ffffff;
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }
            .header {
                background: #2d2d30;
                border-bottom: 2px solid #007acc;
                padding: 15px;
                text-align: center;
            }
            .chat-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            .messages {
                flex: 1;
                overflow-y: auto;
                padding: 20px;
            }
            .message {
                margin-bottom: 15px;
                padding: 12px;
                border-radius: 8px;
                max-width: 80%;
            }
            .user-message {
                background: #007acc;
                margin-left: auto;
                text-align: right;
            }
            .robot-message {
                background: #2d2d30;
                border-left: 4px solid #00d4ff;
            }
            .input-area {
                background: #2d2d30;
                border-top: 1px solid #007acc;
                padding: 15px;
                display: flex;
                gap: 10px;
            }
            .message-input {
                flex: 1;
                background: #1e1e1e;
                border: 1px solid #007acc;
                border-radius: 6px;
                padding: 12px;
                color: #ffffff;
            }
            .send-btn {
                background: #007acc;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                cursor: pointer;
            }
            .modules-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 8px;
                margin: 10px 0;
            }
            .module-btn {
                background: #2d2d30;
                border: 1px solid #007acc;
                border-radius: 4px;
                padding: 8px;
                cursor: pointer;
                text-align: center;
                font-size: 11px;
                color: #ffffff;
            }
            .module-btn:hover {
                background: #3e3e42;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>🤖 Robot IA SamNord</h2>
            <p>👤 SamNord@110577 | Interface comme Augment Agent</p>
        </div>
        
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message robot-message">
                    <strong>🤖 Robot IA:</strong> Bonjour SamNord@110577 ! Je suis votre Robot IA Transcendant, maintenant visible dans la barre latérale VS Code comme les autres extensions. Comment puis-je vous aider ?
                </div>
                
                <div class="message robot-message">
                    <strong>📦 Modules rapides:</strong>
                    <div class="modules-grid">
                        <div class="module-btn" onclick="executeModule('intelligence')">🧠 Intelligence</div>
                        <div class="module-btn" onclick="executeModule('wifi')">📡 WiFi</div>
                        <div class="module-btn" onclick="executeModule('bluetooth')">📱 Bluetooth</div>
                        <div class="module-btn" onclick="executeModule('cybersecurity')">🛡️ Sécurité</div>
                        <div class="module-btn" onclick="executeModule('translation')">🌍 Traduction</div>
                        <div class="module-btn" onclick="executeModule('voice')">🗣️ Voix</div>
                    </div>
                </div>
            </div>
            
            <div class="input-area">
                <input type="text" class="message-input" id="messageInput" placeholder="Votre message..." onkeypress="handleKeyPress(event)">
                <button class="send-btn" onclick="sendMessage()">Envoyer</button>
            </div>
        </div>
        
        <script>
            const vscode = acquireVsCodeApi();
            
            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (message) {
                    addMessage(message, 'user');
                    input.value = '';
                    processMessage(message);
                }
            }
            
            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }
            
            function addMessage(text, sender) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${sender}-message\`;
                
                if (sender === 'user') {
                    messageDiv.innerHTML = \`<strong>👤 Vous:</strong> \${text}\`;
                } else {
                    messageDiv.innerHTML = \`<strong>🤖 Robot IA:</strong> \${text}\`;
                }
                
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }
            
            function processMessage(message) {
                const msg = message.toLowerCase();
                let response = '';
                
                if (msg.includes('bonjour') || msg.includes('salut')) {
                    response = 'Bonjour SamNord@110577 ! Je suis maintenant visible dans la barre latérale VS Code. Que puis-je faire pour vous ?';
                } else if (msg.includes('modules')) {
                    response = 'J\\'ai 40+ modules : Intelligence IA, WiFi, Bluetooth, Cybersécurité, Traduction, etc. Cliquez sur les boutons ou tapez le nom du module.';
                } else if (msg.includes('wifi')) {
                    response = '📡 Module WiFi activé ! Scan des réseaux, connexions, hotspots disponibles.';
                    executeModule('wifi');
                } else if (msg.includes('bluetooth')) {
                    response = '📱 Module Bluetooth activé ! Scan appareils, appairage disponible.';
                    executeModule('bluetooth');
                } else if (msg.includes('sécurité') || msg.includes('cyber')) {
                    response = '🛡️ Cybersécurité activée ! Protection impénétrable en cours.';
                    executeModule('cybersecurity');
                } else {
                    response = 'Je suis maintenant visible dans VS Code ! Tapez "modules" pour voir toutes les fonctions.';
                }
                
                setTimeout(() => {
                    addMessage(response, 'robot');
                }, 500);
            }
            
            function executeModule(module) {
                vscode.postMessage({
                    command: 'executeModule',
                    module: module
                });
                
                addMessage(\`✅ Module \${module} lancé !\`, 'robot');
            }
        </script>
    </body>
    </html>`;
}

function showModulesQuickPick() {
    const modules = [
        '🧠 Intelligence IA Transcendante',
        '📡 WiFi Avancé Complet',
        '📱 Bluetooth Complet',
        '🛡️ Cybersécurité Éthique',
        '🌍 Traduction Temps Réel',
        '🗣️ Communication Multilingue',
        '🧠 Mémoire Parfaite',
        '📱 Tracking Téléphones',
        '💼 Master Compta Général',
        '📱 Enregistrement Vidéo'
    ];
    
    vscode.window.showQuickPick(modules, {
        placeHolder: '🤖 Choisir un module Robot IA'
    }).then(selection => {
        if (selection) {
            const moduleName = selection.split(' ')[1].toLowerCase();
            executeModule(moduleName);
        }
    });
}

function executeModule(module) {
    const moduleMap = {
        'intelligence': 'intelligence_ia_transcendante',
        'wifi': 'wifi_avance_complet',
        'bluetooth': 'bluetooth_complet',
        'cybersecurity': 'cybersecurite_ethique',
        'translation': 'traduction_temps_reel',
        'voice': 'communication_multilingue'
    };
    
    const moduleName = moduleMap[module] || module;
    const modulePath = \`modules/\${moduleName}.py\`;
    
    // Lancement module en terminal externe
    const terminal = vscode.window.createTerminal(\`🤖 \${module}\`);
    terminal.sendText(\`python \${modulePath}\`);
    terminal.show();
    
    vscode.window.showInformationMessage(\`🤖 Module \${module} lancé dans terminal externe\`);
}

function handleChatMessage(message) {
    console.log('Message reçu:', message);
}

// Providers pour les vues
class ChatViewProvider {
    constructor(extensionUri) {
        this.extensionUri = extensionUri;
    }
    
    resolveWebviewView(webviewView) {
        webviewView.webview.options = {
            enableScripts: true
        };
        webviewView.webview.html = getChatHTML();
    }
}

class ModulesTreeProvider {
    getTreeItem(element) {
        return element;
    }
    
    getChildren(element) {
        if (!element) {
            return [
                new vscode.TreeItem('🧠 Intelligence IA', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📡 WiFi Manager', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📱 Bluetooth', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🛡️ Cybersécurité', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🌍 Traduction', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🗣️ Communication', vscode.TreeItemCollapsibleState.None)
            ];
        }
        return [];
    }
}

class StatusTreeProvider {
    getTreeItem(element) {
        return element;
    }
    
    getChildren(element) {
        if (!element) {
            return [
                new vscode.TreeItem('✅ Robot IA: Actif', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('👤 SamNord@110577', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📦 40+ Modules', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('⚡ Performance: 100%', vscode.TreeItemCollapsibleState.None)
            ];
        }
        return [];
    }
}

function deactivate() {
    if (statusBarItem) {
        statusBarItem.dispose();
    }
}

module.exports = {
    activate,
    deactivate
};
