#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Paramètres Complets
TOUS les réglages possibles pour paramétrer le logiciel
Bases de données multiples + Fonctionnalités opérationnelles
Interface noire avec texte blanc
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame,
    QFormLayout, QLineEdit, QComboBox, QTextEdit, QSpinBox, QDateEdit,
    QCheckBox, QTabWidget, QGroupBox, QScrollArea, QGridLayout,
    QColorDialog, QFontDialog, QFileDialog, QMessageBox, QSlider,
    QProgressBar, QListWidget, QTableWidget, QTableWidgetItem
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor, QIcon

class ParametresCompletsMasterCompta(QDialog):
    """Paramètres complets pour MASTER COMPTA GÉNÉRAL avec interface noire"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_parametres_complets()

    def setup_parametres_complets(self):
        """Configure les paramètres complets"""

        # Configuration de la fenêtre
        self.setWindowTitle("⚙️ MASTER COMPTA GÉNÉRAL - Paramètres Complets")
        self.setFixedSize(1200, 800)

        # Style noir avec texte blanc
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.3 #1a1a1a, stop:0.7 #2d2d2d, stop:1 #000000);
                color: white;
                border: 3px solid #FFD700;
                border-radius: 15px;
            }
            QTabWidget::pane {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 10px;
                color: white;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                border: 2px solid #FFD700;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin: 2px;
                font-size: 12pt;
                font-weight: bold;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #4d4d4d);
                color: #FFD700;
            }
            QLineEdit, QComboBox, QTextEdit, QSpinBox, QDateEdit {
                background-color: #1a1a1a;
                color: white;
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 8px;
                font-size: 12pt;
                min-height: 25px;
            }
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus {
                border: 2px solid #FFFFFF;
                background-color: #2d2d2d;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 10px;
                font-size: 12pt;
                font-weight: bold;
                padding: 12px;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #4d4d4d);
                color: white;
            }
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: transparent;
            }
            QGroupBox {
                color: white;
                font-size: 13pt;
                font-weight: bold;
                border: 2px solid #FFD700;
                border-radius: 10px;
                margin: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #FFD700;
            }
            QCheckBox {
                color: white;
                font-size: 12pt;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #FFD700;
                border-radius: 4px;
                background: #1a1a1a;
            }
            QCheckBox::indicator:checked {
                background: #FFD700;
                border: 2px solid #FFA500;
            }
            QSlider::groove:horizontal {
                border: 2px solid #FFD700;
                height: 8px;
                background: #1a1a1a;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #FFD700;
                border: 2px solid #FFA500;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QListWidget, QTableWidget {
                background-color: #1a1a1a;
                color: white;
                border: 2px solid #FFD700;
                border-radius: 8px;
                font-size: 11pt;
            }
            QListWidget::item, QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #2d2d2d;
            }
            QListWidget::item:selected, QTableWidget::item:selected {
                background-color: #FFD700;
                color: black;
            }
        """)

        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Titre
        title = QLabel("⚙️ PARAMÈTRES COMPLETS MASTER COMPTA GÉNÉRAL")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 20pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)

        # Onglets de paramètres
        self.create_tabs_parametres(layout)

        # Boutons d'action
        self.create_action_buttons(layout)

    def create_tabs_parametres(self, layout):
        """Crée les onglets de paramètres"""

        self.tabs = QTabWidget()

        # Onglet 1: Général
        self.create_onglet_general()

        # Onglet 2: Interface
        self.create_onglet_interface()

        # Onglet 3: Bases de Données
        self.create_onglet_bases_donnees()

        # Onglet 4: Utilisateurs
        self.create_onglet_utilisateurs()

        # Onglet 5: Sécurité
        self.create_onglet_securite()

        # Onglet 6: Rapports
        self.create_onglet_rapports()

        # Onglet 7: Email
        self.create_onglet_email()

        # Onglet 8: Sauvegarde
        self.create_onglet_sauvegarde()

        # Onglet 9: Avancé
        self.create_onglet_avance()

        layout.addWidget(self.tabs)

    def create_onglet_general(self):
        """Crée l'onglet Général"""

        general_widget = QWidget()
        layout = QVBoxLayout(general_widget)

        # Scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QFormLayout(scroll_widget)

        # Informations entreprise
        entreprise_group = QGroupBox("🏢 Informations Entreprise")
        entreprise_layout = QFormLayout(entreprise_group)

        self.nom_entreprise = QLineEdit()
        self.nom_entreprise.setPlaceholderText("Nom de votre entreprise")
        entreprise_layout.addRow("📝 Nom entreprise:", self.nom_entreprise)

        self.secteur_activite = QComboBox()
        self.secteur_activite.addItems([
            "Immobilier", "Commerce", "Industrie", "Services", "Agriculture",
            "Transport", "Santé", "Éducation", "Finance", "Technologie",
            "Construction", "Hôtellerie", "Restauration", "Autre"
        ])
        entreprise_layout.addRow("🎯 Secteur activité:", self.secteur_activite)

        self.adresse_entreprise = QTextEdit()
        self.adresse_entreprise.setPlaceholderText("Adresse complète de l'entreprise")
        self.adresse_entreprise.setMaximumHeight(80)
        entreprise_layout.addRow("📍 Adresse:", self.adresse_entreprise)

        self.telephone_entreprise = QLineEdit()
        self.telephone_entreprise.setPlaceholderText("+33 1 23 45 67 89")
        entreprise_layout.addRow("📞 Téléphone:", self.telephone_entreprise)

        self.email_entreprise = QLineEdit()
        self.email_entreprise.setPlaceholderText("<EMAIL>")
        entreprise_layout.addRow("📧 Email:", self.email_entreprise)

        scroll_layout.addRow(entreprise_group)

        # Configuration générale
        config_group = QGroupBox("⚙️ Configuration Générale")
        config_layout = QFormLayout(config_group)

        self.devise_principale = QComboBox()
        self.devise_principale.addItems([
            "EUR - Euro", "USD - Dollar US", "GBP - Livre Sterling",
            "CHF - Franc Suisse", "JPY - Yen", "CAD - Dollar Canadien",
            "DZD - Dinar Algérien", "MAD - Dirham Marocain", "TND - Dinar Tunisien"
        ])
        config_layout.addRow("💰 Devise principale:", self.devise_principale)

        self.langue_defaut = QComboBox()
        self.langue_defaut.addItems(["Français", "العربية", "English"])
        config_layout.addRow("🌍 Langue par défaut:", self.langue_defaut)

        self.format_date = QComboBox()
        self.format_date.addItems(["DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"])
        config_layout.addRow("📅 Format date:", self.format_date)

        self.format_heure = QComboBox()
        self.format_heure.addItems(["24 heures", "12 heures (AM/PM)"])
        config_layout.addRow("🕐 Format heure:", self.format_heure)

        self.codification_auto = QCheckBox("Activer la codification automatique")
        self.codification_auto.setChecked(True)
        config_layout.addRow("🔢 Codification:", self.codification_auto)

        self.prefixe_codification = QLineEdit()
        self.prefixe_codification.setText("INV")
        self.prefixe_codification.setPlaceholderText("Préfixe pour les codes")
        config_layout.addRow("📝 Préfixe codes:", self.prefixe_codification)

        scroll_layout.addRow(config_group)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        self.tabs.addTab(general_widget, "🏢 Général")

    def create_onglet_interface(self):
        """Crée l'onglet Interface"""

        interface_widget = QWidget()
        layout = QVBoxLayout(interface_widget)

        # Scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QFormLayout(scroll_widget)

        # Apparence
        apparence_group = QGroupBox("🎨 Apparence")
        apparence_layout = QFormLayout(apparence_group)

        self.theme_interface = QComboBox()
        self.theme_interface.addItems([
            "Noir avec texte blanc (défaut)", "Bleu professionnel",
            "Sage classique", "Odoo style", "Personnalisé"
        ])
        apparence_layout.addRow("🎨 Thème:", self.theme_interface)

        # Bouton couleur personnalisée
        self.couleur_primaire_btn = QPushButton("Choisir couleur primaire")
        self.couleur_primaire_btn.clicked.connect(self.choisir_couleur_primaire)
        apparence_layout.addRow("🎨 Couleur primaire:", self.couleur_primaire_btn)

        self.couleur_secondaire_btn = QPushButton("Choisir couleur secondaire")
        self.couleur_secondaire_btn.clicked.connect(self.choisir_couleur_secondaire)
        apparence_layout.addRow("🎨 Couleur secondaire:", self.couleur_secondaire_btn)

        # Police
        self.police_btn = QPushButton("Choisir police")
        self.police_btn.clicked.connect(self.choisir_police)
        apparence_layout.addRow("🔤 Police:", self.police_btn)

        self.taille_police = QSpinBox()
        self.taille_police.setRange(8, 24)
        self.taille_police.setValue(12)
        self.taille_police.setSuffix(" pt")
        apparence_layout.addRow("📏 Taille police:", self.taille_police)

        scroll_layout.addRow(apparence_group)

        # Comportement
        comportement_group = QGroupBox("⚡ Comportement")
        comportement_layout = QFormLayout(comportement_group)

        self.animations = QCheckBox("Activer les animations")
        self.animations.setChecked(True)
        comportement_layout.addRow("🎬 Animations:", self.animations)

        self.sons = QCheckBox("Activer les sons")
        self.sons.setChecked(False)
        comportement_layout.addRow("🔊 Sons:", self.sons)

        self.tooltips = QCheckBox("Afficher les info-bulles")
        self.tooltips.setChecked(True)
        comportement_layout.addRow("💡 Info-bulles:", self.tooltips)

        self.demarrage_auto = QCheckBox("Démarrage automatique")
        self.demarrage_auto.setChecked(False)
        comportement_layout.addRow("🚀 Démarrage auto:", self.demarrage_auto)

        scroll_layout.addRow(comportement_group)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        self.tabs.addTab(interface_widget, "🎨 Interface")

    def create_onglet_bases_donnees(self):
        """Crée l'onglet Bases de Données Multiples"""

        db_widget = QWidget()
        layout = QVBoxLayout(db_widget)

        # Titre
        title = QLabel("🗃️ GESTION DES BASES DE DONNÉES MULTIPLES")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(title)

        # Configuration principale
        main_db_group = QGroupBox("🗃️ Base de Données Principale")
        main_db_layout = QFormLayout(main_db_group)

        self.chemin_db_principale = QLineEdit()
        self.chemin_db_principale.setText("./master_compta_principal.db")
        main_db_layout.addRow("📁 Chemin:", self.chemin_db_principale)

        parcourir_btn = QPushButton("📂 Parcourir")
        parcourir_btn.clicked.connect(self.parcourir_db_principale)
        main_db_layout.addRow("", parcourir_btn)

        self.type_db = QComboBox()
        self.type_db.addItems(["SQLite", "MySQL", "PostgreSQL", "SQL Server"])
        main_db_layout.addRow("🔧 Type:", self.type_db)

        layout.addWidget(main_db_group)

        # Bases de données sectorielles
        secteur_db_group = QGroupBox("🏭 Bases de Données par Secteur Économique")
        secteur_db_layout = QVBoxLayout(secteur_db_group)

        # Table des bases sectorielles
        self.table_bases_secteur = QTableWidget(0, 4)
        self.table_bases_secteur.setHorizontalHeaderLabels([
            "🏭 Secteur", "📁 Chemin Base", "📊 Statut", "🔧 Actions"
        ])
        self.table_bases_secteur.setMinimumHeight(200)

        # Ajouter des bases par défaut
        secteurs_defaut = [
            ("Immobilier", "./db/immobilier.db", "Connectée"),
            ("Commerce", "./db/commerce.db", "Connectée"),
            ("Industrie", "./db/industrie.db", "Déconnectée"),
            ("Agriculture", "./db/agriculture.db", "Connectée"),
            ("Transport", "./db/transport.db", "Connectée"),
            ("Santé", "./db/sante.db", "Déconnectée"),
            ("Éducation", "./db/education.db", "Connectée"),
            ("Finance", "./db/finance.db", "Connectée")
        ]

        for secteur, chemin, statut in secteurs_defaut:
            self.ajouter_base_secteur(secteur, chemin, statut)

        secteur_db_layout.addWidget(self.table_bases_secteur)

        # Boutons de gestion
        buttons_layout = QHBoxLayout()

        ajouter_secteur_btn = QPushButton("➕ Ajouter Secteur")
        ajouter_secteur_btn.clicked.connect(self.ajouter_nouveau_secteur)
        buttons_layout.addWidget(ajouter_secteur_btn)

        modifier_secteur_btn = QPushButton("✏️ Modifier")
        modifier_secteur_btn.clicked.connect(self.modifier_secteur)
        buttons_layout.addWidget(modifier_secteur_btn)

        supprimer_secteur_btn = QPushButton("🗑️ Supprimer")
        supprimer_secteur_btn.clicked.connect(self.supprimer_secteur)
        buttons_layout.addWidget(supprimer_secteur_btn)

        tester_connexion_btn = QPushButton("🔍 Tester Connexions")
        tester_connexion_btn.clicked.connect(self.tester_connexions)
        buttons_layout.addWidget(tester_connexion_btn)

        secteur_db_layout.addLayout(buttons_layout)

        layout.addWidget(secteur_db_group)

        # Configuration avancée
        avance_db_group = QGroupBox("🔧 Configuration Avancée")
        avance_db_layout = QFormLayout(avance_db_group)

        self.pool_connexions = QSpinBox()
        self.pool_connexions.setRange(1, 100)
        self.pool_connexions.setValue(10)
        avance_db_layout.addRow("🔗 Pool connexions:", self.pool_connexions)

        self.timeout_connexion = QSpinBox()
        self.timeout_connexion.setRange(5, 300)
        self.timeout_connexion.setValue(30)
        self.timeout_connexion.setSuffix(" sec")
        avance_db_layout.addRow("⏱️ Timeout:", self.timeout_connexion)

        self.cache_requetes = QCheckBox("Activer le cache des requêtes")
        self.cache_requetes.setChecked(True)
        avance_db_layout.addRow("💾 Cache:", self.cache_requetes)

        self.log_requetes = QCheckBox("Logger les requêtes SQL")
        self.log_requetes.setChecked(False)
        avance_db_layout.addRow("📝 Logs SQL:", self.log_requetes)

        layout.addWidget(avance_db_group)

        self.tabs.addTab(db_widget, "🗃️ Bases de Données")

    def ajouter_base_secteur(self, secteur, chemin, statut):
        """Ajoute une base de données sectorielle à la table"""
        row = self.table_bases_secteur.rowCount()
        self.table_bases_secteur.insertRow(row)

        self.table_bases_secteur.setItem(row, 0, QTableWidgetItem(secteur))
        self.table_bases_secteur.setItem(row, 1, QTableWidgetItem(chemin))

        # Statut avec couleur
        statut_item = QTableWidgetItem(statut)
        if statut == "Connectée":
            statut_item.setBackground(QColor(0, 255, 0, 50))
        else:
            statut_item.setBackground(QColor(255, 0, 0, 50))
        self.table_bases_secteur.setItem(row, 2, statut_item)

        # Bouton d'action
        action_btn = QPushButton("🔧 Configurer")
        self.table_bases_secteur.setCellWidget(row, 3, action_btn)

    def create_onglet_utilisateurs(self):
        """Crée l'onglet Utilisateurs"""

        users_widget = QWidget()
        layout = QVBoxLayout(users_widget)

        # Gestion des utilisateurs
        users_group = QGroupBox("👥 Gestion des Utilisateurs")
        users_layout = QVBoxLayout(users_group)

        # Table des utilisateurs
        self.table_utilisateurs = QTableWidget(0, 5)
        self.table_utilisateurs.setHorizontalHeaderLabels([
            "👤 Nom", "📧 Email", "🔒 Rôle", "📊 Statut", "🔧 Actions"
        ])

        # Ajouter des utilisateurs par défaut
        utilisateurs_defaut = [
            ("Administrateur", "<EMAIL>", "Admin", "Actif"),
            ("Gestionnaire", "<EMAIL>", "Gestionnaire", "Actif"),
            ("Utilisateur", "<EMAIL>", "Utilisateur", "Inactif"),
            ("Comptable", "<EMAIL>", "Comptable", "Actif")
        ]

        for nom, email, role, statut in utilisateurs_defaut:
            self.ajouter_utilisateur(nom, email, role, statut)

        users_layout.addWidget(self.table_utilisateurs)

        # Boutons de gestion utilisateurs
        users_buttons = QHBoxLayout()

        ajouter_user_btn = QPushButton("➕ Ajouter Utilisateur")
        ajouter_user_btn.clicked.connect(self.ajouter_nouveau_utilisateur)
        users_buttons.addWidget(ajouter_user_btn)

        modifier_user_btn = QPushButton("✏️ Modifier")
        modifier_user_btn.clicked.connect(self.modifier_utilisateur)
        users_buttons.addWidget(modifier_user_btn)

        supprimer_user_btn = QPushButton("🗑️ Supprimer")
        supprimer_user_btn.clicked.connect(self.supprimer_utilisateur)
        users_buttons.addWidget(supprimer_user_btn)

        users_layout.addLayout(users_buttons)
        layout.addWidget(users_group)

        # Permissions
        permissions_group = QGroupBox("🔐 Permissions par Rôle")
        permissions_layout = QFormLayout(permissions_group)

        self.role_selection = QComboBox()
        self.role_selection.addItems(["Admin", "Gestionnaire", "Comptable", "Utilisateur"])
        permissions_layout.addRow("🎭 Rôle:", self.role_selection)

        # Permissions détaillées
        self.perm_gestion_biens = QCheckBox("Gestion des Biens")
        self.perm_gestion_biens.setChecked(True)
        permissions_layout.addRow("🏠 Biens:", self.perm_gestion_biens)

        self.perm_fournisseurs = QCheckBox("Fournisseurs")
        self.perm_fournisseurs.setChecked(True)
        permissions_layout.addRow("🏢 Fournisseurs:", self.perm_fournisseurs)

        self.perm_inventaire = QCheckBox("Inventaire")
        self.perm_inventaire.setChecked(True)
        permissions_layout.addRow("📦 Inventaire:", self.perm_inventaire)

        self.perm_rapports = QCheckBox("Rapports")
        self.perm_rapports.setChecked(True)
        permissions_layout.addRow("📊 Rapports:", self.perm_rapports)

        self.perm_parametres = QCheckBox("Paramètres")
        self.perm_parametres.setChecked(False)
        permissions_layout.addRow("⚙️ Paramètres:", self.perm_parametres)

        layout.addWidget(permissions_group)

        self.tabs.addTab(users_widget, "👥 Utilisateurs")

    def create_onglet_securite(self):
        """Crée l'onglet Sécurité"""

        security_widget = QWidget()
        layout = QVBoxLayout(security_widget)

        # Politique de mot de passe
        password_group = QGroupBox("🔒 Politique de Mot de Passe")
        password_layout = QFormLayout(password_group)

        self.longueur_min_mdp = QSpinBox()
        self.longueur_min_mdp.setRange(4, 20)
        self.longueur_min_mdp.setValue(8)
        password_layout.addRow("📏 Longueur minimum:", self.longueur_min_mdp)

        self.majuscules_requises = QCheckBox("Majuscules requises")
        self.majuscules_requises.setChecked(True)
        password_layout.addRow("🔤 Majuscules:", self.majuscules_requises)

        self.chiffres_requis = QCheckBox("Chiffres requis")
        self.chiffres_requis.setChecked(True)
        password_layout.addRow("🔢 Chiffres:", self.chiffres_requis)

        self.caracteres_speciaux = QCheckBox("Caractères spéciaux requis")
        self.caracteres_speciaux.setChecked(True)
        password_layout.addRow("🔣 Spéciaux:", self.caracteres_speciaux)

        self.expiration_mdp = QSpinBox()
        self.expiration_mdp.setRange(0, 365)
        self.expiration_mdp.setValue(90)
        self.expiration_mdp.setSuffix(" jours")
        password_layout.addRow("⏰ Expiration:", self.expiration_mdp)

        layout.addWidget(password_group)

        # Sécurité des sessions
        session_group = QGroupBox("🕐 Sécurité des Sessions")
        session_layout = QFormLayout(session_group)

        self.timeout_session = QSpinBox()
        self.timeout_session.setRange(5, 480)
        self.timeout_session.setValue(30)
        self.timeout_session.setSuffix(" minutes")
        session_layout.addRow("⏱️ Timeout session:", self.timeout_session)

        self.sessions_multiples = QCheckBox("Autoriser sessions multiples")
        self.sessions_multiples.setChecked(False)
        session_layout.addRow("👥 Sessions multiples:", self.sessions_multiples)

        self.verrouillage_auto = QCheckBox("Verrouillage automatique")
        self.verrouillage_auto.setChecked(True)
        session_layout.addRow("🔒 Verrouillage auto:", self.verrouillage_auto)

        layout.addWidget(session_group)

        # Audit et logs
        audit_group = QGroupBox("📋 Audit et Logs")
        audit_layout = QFormLayout(audit_group)

        self.log_connexions = QCheckBox("Logger les connexions")
        self.log_connexions.setChecked(True)
        audit_layout.addRow("🔐 Connexions:", self.log_connexions)

        self.log_actions = QCheckBox("Logger les actions")
        self.log_actions.setChecked(True)
        audit_layout.addRow("⚡ Actions:", self.log_actions)

        self.log_erreurs = QCheckBox("Logger les erreurs")
        self.log_erreurs.setChecked(True)
        audit_layout.addRow("❌ Erreurs:", self.log_erreurs)

        self.retention_logs = QSpinBox()
        self.retention_logs.setRange(1, 365)
        self.retention_logs.setValue(30)
        self.retention_logs.setSuffix(" jours")
        audit_layout.addRow("📅 Rétention logs:", self.retention_logs)

        layout.addWidget(audit_group)

        self.tabs.addTab(security_widget, "🔒 Sécurité")

    def create_onglet_rapports(self):
        """Crée l'onglet Rapports"""

        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)

        # Configuration des rapports
        config_reports_group = QGroupBox("📊 Configuration des Rapports")
        config_reports_layout = QFormLayout(config_reports_group)

        self.format_rapport_defaut = QComboBox()
        self.format_rapport_defaut.addItems(["PDF", "HTML", "Excel", "Word", "CSV"])
        config_reports_layout.addRow("📄 Format par défaut:", self.format_rapport_defaut)

        self.langue_rapport_defaut = QComboBox()
        self.langue_rapport_defaut.addItems(["Français", "العربية", "English"])
        config_reports_layout.addRow("🌍 Langue par défaut:", self.langue_rapport_defaut)

        self.logo_entreprise_path = QLineEdit()
        self.logo_entreprise_path.setPlaceholderText("Chemin vers le logo")
        config_reports_layout.addRow("🖼️ Logo entreprise:", self.logo_entreprise_path)

        logo_btn = QPushButton("📂 Parcourir")
        logo_btn.clicked.connect(self.parcourir_logo)
        config_reports_layout.addRow("", logo_btn)

        self.signature_automatique = QCheckBox("Signature automatique")
        self.signature_automatique.setChecked(True)
        config_reports_layout.addRow("✍️ Signature auto:", self.signature_automatique)

        layout.addWidget(config_reports_group)

        # Modèles de rapports
        templates_group = QGroupBox("📋 Modèles de Rapports")
        templates_layout = QVBoxLayout(templates_group)

        self.liste_modeles = QListWidget()
        modeles_defaut = [
            "📊 Rapport Gestion des Biens",
            "🏢 Rapport Fournisseurs",
            "📦 Rapport Inventaire",
            "🚗 Rapport Parc Auto",
            "💰 Rapport Financier",
            "📈 Rapport Statistiques",
            "🔍 Rapport Audit",
            "📅 Rapport Mensuel",
            "📋 Rapport Personnalisé"
        ]

        for modele in modeles_defaut:
            self.liste_modeles.addItem(modele)

        templates_layout.addWidget(self.liste_modeles)

        # Boutons modèles
        templates_buttons = QHBoxLayout()

        nouveau_modele_btn = QPushButton("➕ Nouveau Modèle")
        nouveau_modele_btn.clicked.connect(self.nouveau_modele)
        templates_buttons.addWidget(nouveau_modele_btn)

        modifier_modele_btn = QPushButton("✏️ Modifier")
        modifier_modele_btn.clicked.connect(self.modifier_modele)
        templates_buttons.addWidget(modifier_modele_btn)

        supprimer_modele_btn = QPushButton("🗑️ Supprimer")
        supprimer_modele_btn.clicked.connect(self.supprimer_modele)
        templates_buttons.addWidget(supprimer_modele_btn)

        templates_layout.addLayout(templates_buttons)
        layout.addWidget(templates_group)

        self.tabs.addTab(reports_widget, "📊 Rapports")

    def create_onglet_email(self):
        """Crée l'onglet Email"""

        email_widget = QWidget()
        layout = QVBoxLayout(email_widget)

        # Configuration SMTP
        smtp_group = QGroupBox("📧 Configuration SMTP")
        smtp_layout = QFormLayout(smtp_group)

        self.serveur_smtp = QLineEdit()
        self.serveur_smtp.setPlaceholderText("smtp.gmail.com")
        smtp_layout.addRow("🌐 Serveur SMTP:", self.serveur_smtp)

        self.port_smtp = QSpinBox()
        self.port_smtp.setRange(1, 65535)
        self.port_smtp.setValue(587)
        smtp_layout.addRow("🔌 Port:", self.port_smtp)

        self.email_expediteur = QLineEdit()
        self.email_expediteur.setPlaceholderText("<EMAIL>")
        smtp_layout.addRow("📧 Email expéditeur:", self.email_expediteur)

        self.mot_de_passe_email = QLineEdit()
        self.mot_de_passe_email.setEchoMode(QLineEdit.EchoMode.Password)
        self.mot_de_passe_email.setPlaceholderText("Mot de passe ou token")
        smtp_layout.addRow("🔒 Mot de passe:", self.mot_de_passe_email)

        self.utiliser_tls = QCheckBox("Utiliser TLS/SSL")
        self.utiliser_tls.setChecked(True)
        smtp_layout.addRow("🔐 TLS/SSL:", self.utiliser_tls)

        # Bouton test
        test_email_btn = QPushButton("🧪 Tester Configuration")
        test_email_btn.clicked.connect(self.tester_email)
        smtp_layout.addRow("", test_email_btn)

        layout.addWidget(smtp_group)

        # Modèles d'emails
        email_templates_group = QGroupBox("📝 Modèles d'Emails")
        email_templates_layout = QFormLayout(email_templates_group)

        self.sujet_defaut = QLineEdit()
        self.sujet_defaut.setText("Rapport MASTER COMPTA GÉNÉRAL")
        email_templates_layout.addRow("📋 Sujet par défaut:", self.sujet_defaut)

        self.signature_email = QTextEdit()
        self.signature_email.setPlaceholderText("Signature automatique pour les emails")
        self.signature_email.setMaximumHeight(100)
        email_templates_layout.addRow("✍️ Signature:", self.signature_email)

        self.copie_automatique = QLineEdit()
        self.copie_automatique.setPlaceholderText("<EMAIL>")
        email_templates_layout.addRow("📧 Copie automatique:", self.copie_automatique)

        layout.addWidget(email_templates_group)

        self.tabs.addTab(email_widget, "📧 Email")

    def create_onglet_sauvegarde(self):
        """Crée l'onglet Sauvegarde"""

        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)

        # Configuration sauvegarde
        backup_config_group = QGroupBox("💾 Configuration Sauvegarde")
        backup_config_layout = QFormLayout(backup_config_group)

        self.dossier_sauvegarde = QLineEdit()
        self.dossier_sauvegarde.setText("./backups/")
        backup_config_layout.addRow("📁 Dossier:", self.dossier_sauvegarde)

        parcourir_backup_btn = QPushButton("📂 Parcourir")
        parcourir_backup_btn.clicked.connect(self.parcourir_dossier_backup)
        backup_config_layout.addRow("", parcourir_backup_btn)

        self.frequence_sauvegarde = QComboBox()
        self.frequence_sauvegarde.addItems([
            "Manuelle", "Quotidienne", "Hebdomadaire", "Mensuelle", "À chaque fermeture"
        ])
        backup_config_layout.addRow("⏰ Fréquence:", self.frequence_sauvegarde)

        self.heure_sauvegarde = QComboBox()
        heures = [f"{h:02d}:00" for h in range(24)]
        self.heure_sauvegarde.addItems(heures)
        self.heure_sauvegarde.setCurrentText("02:00")
        backup_config_layout.addRow("🕐 Heure:", self.heure_sauvegarde)

        self.compression_backup = QCheckBox("Compresser les sauvegardes")
        self.compression_backup.setChecked(True)
        backup_config_layout.addRow("🗜️ Compression:", self.compression_backup)

        self.retention_backup = QSpinBox()
        self.retention_backup.setRange(1, 365)
        self.retention_backup.setValue(30)
        self.retention_backup.setSuffix(" jours")
        backup_config_layout.addRow("📅 Rétention:", self.retention_backup)

        layout.addWidget(backup_config_group)

        # Actions de sauvegarde
        backup_actions_group = QGroupBox("⚡ Actions de Sauvegarde")
        backup_actions_layout = QVBoxLayout(backup_actions_group)

        # Boutons d'action
        actions_layout = QHBoxLayout()

        backup_now_btn = QPushButton("💾 Sauvegarder Maintenant")
        backup_now_btn.clicked.connect(self.sauvegarder_maintenant)
        actions_layout.addWidget(backup_now_btn)

        restore_btn = QPushButton("🔄 Restaurer")
        restore_btn.clicked.connect(self.restaurer_sauvegarde)
        actions_layout.addWidget(restore_btn)

        verify_btn = QPushButton("🔍 Vérifier Intégrité")
        verify_btn.clicked.connect(self.verifier_integrite)
        actions_layout.addWidget(verify_btn)

        backup_actions_layout.addLayout(actions_layout)

        # Liste des sauvegardes
        self.liste_sauvegardes = QListWidget()
        sauvegardes_exemple = [
            "💾 backup_2024_01_15_02_00.zip (15/01/2024 02:00)",
            "💾 backup_2024_01_14_02_00.zip (14/01/2024 02:00)",
            "💾 backup_2024_01_13_02_00.zip (13/01/2024 02:00)",
            "💾 backup_2024_01_12_02_00.zip (12/01/2024 02:00)"
        ]

        for backup in sauvegardes_exemple:
            self.liste_sauvegardes.addItem(backup)

        backup_actions_layout.addWidget(self.liste_sauvegardes)

        layout.addWidget(backup_actions_group)

        self.tabs.addTab(backup_widget, "💾 Sauvegarde")

    def create_onglet_avance(self):
        """Crée l'onglet Avancé"""

        advanced_widget = QWidget()
        layout = QVBoxLayout(advanced_widget)

        # Performance
        performance_group = QGroupBox("⚡ Performance")
        performance_layout = QFormLayout(performance_group)

        self.cache_taille = QSpinBox()
        self.cache_taille.setRange(10, 1000)
        self.cache_taille.setValue(100)
        self.cache_taille.setSuffix(" MB")
        performance_layout.addRow("💾 Taille cache:", self.cache_taille)

        self.threads_max = QSpinBox()
        self.threads_max.setRange(1, 16)
        self.threads_max.setValue(4)
        performance_layout.addRow("🔄 Threads max:", self.threads_max)

        self.optimisation_auto = QCheckBox("Optimisation automatique")
        self.optimisation_auto.setChecked(True)
        performance_layout.addRow("🚀 Optimisation:", self.optimisation_auto)

        layout.addWidget(performance_group)

        # Développement
        dev_group = QGroupBox("🔧 Développement")
        dev_layout = QFormLayout(dev_group)

        self.mode_debug = QCheckBox("Mode debug")
        self.mode_debug.setChecked(False)
        dev_layout.addRow("🐛 Debug:", self.mode_debug)

        self.logs_detailles = QCheckBox("Logs détaillés")
        self.logs_detailles.setChecked(False)
        dev_layout.addRow("📝 Logs détaillés:", self.logs_detailles)

        self.console_developpeur = QCheckBox("Console développeur")
        self.console_developpeur.setChecked(False)
        dev_layout.addRow("💻 Console dev:", self.console_developpeur)

        layout.addWidget(dev_group)

        # Maintenance
        maintenance_group = QGroupBox("🔧 Maintenance")
        maintenance_layout = QVBoxLayout(maintenance_group)

        maintenance_buttons = QHBoxLayout()

        nettoyer_cache_btn = QPushButton("🧹 Nettoyer Cache")
        nettoyer_cache_btn.clicked.connect(self.nettoyer_cache)
        maintenance_buttons.addWidget(nettoyer_cache_btn)

        optimiser_db_btn = QPushButton("🗃️ Optimiser BDD")
        optimiser_db_btn.clicked.connect(self.optimiser_base_donnees)
        maintenance_buttons.addWidget(optimiser_db_btn)

        reinitialiser_btn = QPushButton("🔄 Réinitialiser")
        reinitialiser_btn.clicked.connect(self.reinitialiser_parametres)
        maintenance_buttons.addWidget(reinitialiser_btn)

        maintenance_layout.addLayout(maintenance_buttons)
        layout.addWidget(maintenance_group)

        self.tabs.addTab(advanced_widget, "🔧 Avancé")

    def create_action_buttons(self, layout):
        """Crée les boutons d'action"""

        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 10px;
                margin: 10px;
                padding: 15px;
            }
        """)
        buttons_frame.setFixedHeight(80)

        buttons_layout = QHBoxLayout(buttons_frame)

        # Boutons d'action
        sauvegarder_btn = QPushButton("💾 Sauvegarder")
        sauvegarder_btn.clicked.connect(self.sauvegarder_parametres)
        buttons_layout.addWidget(sauvegarder_btn)

        appliquer_btn = QPushButton("✅ Appliquer")
        appliquer_btn.clicked.connect(self.appliquer_parametres)
        buttons_layout.addWidget(appliquer_btn)

        reinitialiser_btn = QPushButton("🔄 Réinitialiser")
        reinitialiser_btn.clicked.connect(self.reinitialiser_tous_parametres)
        buttons_layout.addWidget(reinitialiser_btn)

        importer_btn = QPushButton("📥 Importer")
        importer_btn.clicked.connect(self.importer_parametres)
        buttons_layout.addWidget(importer_btn)

        exporter_btn = QPushButton("📤 Exporter")
        exporter_btn.clicked.connect(self.exporter_parametres)
        buttons_layout.addWidget(exporter_btn)

        annuler_btn = QPushButton("❌ Annuler")
        annuler_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(annuler_btn)

        layout.addWidget(buttons_frame)

    # Méthodes pour les fonctionnalités
    def ajouter_utilisateur(self, nom, email, role, statut):
        """Ajoute un utilisateur à la table"""
        row = self.table_utilisateurs.rowCount()
        self.table_utilisateurs.insertRow(row)

        self.table_utilisateurs.setItem(row, 0, QTableWidgetItem(nom))
        self.table_utilisateurs.setItem(row, 1, QTableWidgetItem(email))
        self.table_utilisateurs.setItem(row, 2, QTableWidgetItem(role))

        # Statut avec couleur
        statut_item = QTableWidgetItem(statut)
        if statut == "Actif":
            statut_item.setBackground(QColor(0, 255, 0, 50))
        else:
            statut_item.setBackground(QColor(255, 0, 0, 50))
        self.table_utilisateurs.setItem(row, 3, statut_item)

        # Bouton d'action
        action_btn = QPushButton("🔧 Modifier")
        self.table_utilisateurs.setCellWidget(row, 4, action_btn)

    def choisir_couleur_primaire(self):
        """Choisit la couleur primaire"""
        color = QColorDialog.getColor(QColor("#FFD700"), self, "Choisir couleur primaire")
        if color.isValid():
            self.couleur_primaire_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color.name()};
                    color: {'white' if color.lightness() < 128 else 'black'};
                    border: 2px solid #FFD700;
                    border-radius: 8px;
                    padding: 8px;
                    font-weight: bold;
                }}
            """)
            print(f"🎨 Couleur primaire: {color.name()}")

    def choisir_couleur_secondaire(self):
        """Choisit la couleur secondaire"""
        color = QColorDialog.getColor(QColor("#FFA500"), self, "Choisir couleur secondaire")
        if color.isValid():
            self.couleur_secondaire_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color.name()};
                    color: {'white' if color.lightness() < 128 else 'black'};
                    border: 2px solid #FFD700;
                    border-radius: 8px;
                    padding: 8px;
                    font-weight: bold;
                }}
            """)
            print(f"🎨 Couleur secondaire: {color.name()}")

    def choisir_police(self):
        """Choisit la police"""
        font, ok = QFontDialog.getFont(QFont("Arial", 12), self, "Choisir police")
        if ok:
            self.police_btn.setText(f"{font.family()} {font.pointSize()}pt")
            print(f"🔤 Police: {font.family()} {font.pointSize()}pt")

    def parcourir_db_principale(self):
        """Parcourt pour choisir la base de données principale"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Choisir base de données", "", "Base de données (*.db *.sqlite)"
        )
        if file_path:
            self.chemin_db_principale.setText(file_path)
            print(f"🗃️ Base principale: {file_path}")

    def ajouter_nouveau_secteur(self):
        """Ajoute un nouveau secteur"""
        print("➕ Ajout nouveau secteur économique")
        # Ici on ouvrirait une boîte de dialogue pour saisir les détails

    def modifier_secteur(self):
        """Modifie un secteur sélectionné"""
        print("✏️ Modification secteur économique")

    def supprimer_secteur(self):
        """Supprime un secteur sélectionné"""
        print("🗑️ Suppression secteur économique")

    def tester_connexions(self):
        """Teste toutes les connexions aux bases de données"""
        print("🔍 Test des connexions aux bases de données multiples")
        # Simulation du test
        for row in range(self.table_bases_secteur.rowCount()):
            secteur = self.table_bases_secteur.item(row, 0).text()
            print(f"  🔗 Test connexion {secteur}...")

    def ajouter_nouveau_utilisateur(self):
        """Ajoute un nouveau utilisateur"""
        print("➕ Ajout nouvel utilisateur")

    def modifier_utilisateur(self):
        """Modifie un utilisateur"""
        print("✏️ Modification utilisateur")

    def supprimer_utilisateur(self):
        """Supprime un utilisateur"""
        print("🗑️ Suppression utilisateur")

    def nouveau_modele(self):
        """Crée un nouveau modèle de rapport"""
        print("➕ Nouveau modèle de rapport")

    def modifier_modele(self):
        """Modifie un modèle de rapport"""
        print("✏️ Modification modèle de rapport")

    def supprimer_modele(self):
        """Supprime un modèle de rapport"""
        print("🗑️ Suppression modèle de rapport")

    def parcourir_logo(self):
        """Parcourt pour choisir le logo"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Choisir logo", "", "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            self.logo_entreprise_path.setText(file_path)
            print(f"🖼️ Logo: {file_path}")

    def tester_email(self):
        """Teste la configuration email"""
        print("🧪 Test configuration email SMTP")
        # Simulation du test
        serveur = self.serveur_smtp.text()
        port = self.port_smtp.value()
        email = self.email_expediteur.text()
        print(f"  📧 Test: {email} via {serveur}:{port}")

    def parcourir_dossier_backup(self):
        """Parcourt pour choisir le dossier de sauvegarde"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "Choisir dossier de sauvegarde"
        )
        if folder_path:
            self.dossier_sauvegarde.setText(folder_path)
            print(f"📁 Dossier backup: {folder_path}")

    def sauvegarder_maintenant(self):
        """Lance une sauvegarde immédiate"""
        print("💾 Sauvegarde immédiate en cours...")
        # Simulation de la sauvegarde
        from datetime import datetime
        now = datetime.now()
        backup_name = f"backup_{now.strftime('%Y_%m_%d_%H_%M')}.zip"
        print(f"  📦 Création: {backup_name}")

    def restaurer_sauvegarde(self):
        """Restaure une sauvegarde"""
        print("🔄 Restauration sauvegarde")

    def verifier_integrite(self):
        """Vérifie l'intégrité des sauvegardes"""
        print("🔍 Vérification intégrité des sauvegardes")

    def nettoyer_cache(self):
        """Nettoie le cache"""
        print("🧹 Nettoyage du cache")

    def optimiser_base_donnees(self):
        """Optimise les bases de données"""
        print("🗃️ Optimisation des bases de données")

    def reinitialiser_parametres(self):
        """Réinitialise les paramètres avancés"""
        print("🔄 Réinitialisation paramètres avancés")

    def sauvegarder_parametres(self):
        """Sauvegarde tous les paramètres"""
        print("💾 Sauvegarde de tous les paramètres")
        # Ici on sauvegarderait dans un fichier JSON ou base de données
        parametres = {
            "entreprise": {
                "nom": self.nom_entreprise.text(),
                "secteur": self.secteur_activite.currentText(),
                "adresse": self.adresse_entreprise.toPlainText(),
                "telephone": self.telephone_entreprise.text(),
                "email": self.email_entreprise.text()
            },
            "general": {
                "devise": self.devise_principale.currentText(),
                "langue": self.langue_defaut.currentText(),
                "format_date": self.format_date.currentText(),
                "format_heure": self.format_heure.currentText(),
                "codification_auto": self.codification_auto.isChecked(),
                "prefixe": self.prefixe_codification.text()
            },
            "interface": {
                "theme": self.theme_interface.currentText(),
                "taille_police": self.taille_police.value(),
                "animations": self.animations.isChecked(),
                "sons": self.sons.isChecked(),
                "tooltips": self.tooltips.isChecked()
            },
            "securite": {
                "longueur_mdp": self.longueur_min_mdp.value(),
                "majuscules": self.majuscules_requises.isChecked(),
                "chiffres": self.chiffres_requis.isChecked(),
                "speciaux": self.caracteres_speciaux.isChecked(),
                "timeout_session": self.timeout_session.value()
            }
        }
        print("✅ Paramètres sauvegardés avec succès")

    def appliquer_parametres(self):
        """Applique les paramètres"""
        print("✅ Application des paramètres")
        self.sauvegarder_parametres()

    def reinitialiser_tous_parametres(self):
        """Réinitialise tous les paramètres"""
        reply = QMessageBox.question(
            self, "Réinitialisation",
            "Voulez-vous vraiment réinitialiser tous les paramètres ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            print("🔄 Réinitialisation de tous les paramètres")

    def importer_parametres(self):
        """Importe des paramètres depuis un fichier"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Importer paramètres", "", "Fichiers JSON (*.json)"
        )
        if file_path:
            print(f"📥 Import paramètres: {file_path}")

    def exporter_parametres(self):
        """Exporte les paramètres vers un fichier"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Exporter paramètres", "parametres_master_compta.json",
            "Fichiers JSON (*.json)"
        )
        if file_path:
            print(f"📤 Export paramètres: {file_path}")

# Script de test
if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)

    # Test des paramètres complets
    parametres = ParametresCompletsMasterCompta()
    parametres.show()

    sys.exit(app.exec())