<!-- Generated by SpecStory -->

# /fix Le type de paramètre « connection » est incon (2025-06-08 14:09:57)

_**User**_

/fix Le type de paramètre « connection » est inconnu, L'annotation de type est manquante pour le paramètre "connection"

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Ajouter une annotation de type pour le paramètre `connection`. Utilisez `sqlite3.Connection` ou `pyodbc.Connection` selon le contexte, ou `Any` si la fonction doit rester générique pour plusieurs types de connexion.

````python
from typing import Any
# ...existing code...
    def execute_query(self, query_name: str, connection: Any, params: dict = None) -> Optional[List[Any]]:
# ...existing code...
````

---

