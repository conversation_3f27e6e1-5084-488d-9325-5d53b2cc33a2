#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test final pour vérifier que tous les problèmes de main.py sont corrigés
"""

import sys
import os
import ast

def test_syntax():
    """Test la syntaxe du fichier main.py"""
    print("🔍 Test de syntaxe...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse le code pour vérifier la syntaxe
        ast.parse(source)
        print("✅ Syntaxe correcte")
        return True
        
    except SyntaxError as e:
        print(f"❌ Erreur de syntaxe: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_imports():
    """Test les imports du module"""
    print("\n🔍 Test des imports...")
    
    try:
        # Ajouter src au path
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        # Test import principal
        import main
        print("✅ Module main importé")
        
        # Test classes principales
        manager = main.ModuleManager()
        print("✅ ModuleManager créé")
        
        db = main.DatabaseConnection(":memory:")
        print("✅ DatabaseConnection créé")
        
        auth = main.AuthManager()
        print("✅ AuthManager créé")
        
        # Nettoyage
        db.close()
        auth.conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def test_code_quality():
    """Test la qualité du code"""
    print("\n🔍 Test de qualité du code...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        
        # Vérifier les lignes avec instructions multiples
        for i, line in enumerate(lines, 1):
            if '; ' in line and not line.strip().startswith('#'):
                # Ignorer les commentaires et certains cas valides
                if 'QMainWindow' not in line and 'color:' not in line:
                    issues.append(f"Ligne {i}: Instructions multiples sur une ligne")
        
        # Vérifier les imports manquants
        content = ''.join(lines)
        if 'from typing import' in content:
            if 'List' in content and 'List' not in content[:content.find('class')]:
                issues.append("Import List manquant dans typing")
        
        if issues:
            print("⚠️ Problèmes de qualité détectés:")
            for issue in issues[:5]:  # Limiter à 5 pour la lisibilité
                print(f"  - {issue}")
            return len(issues) <= 5  # Acceptable si <= 5 problèmes mineurs
        else:
            print("✅ Qualité du code acceptable")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors du test de qualité: {e}")
        return False

def test_type_annotations():
    """Test les annotations de type"""
    print("\n🔍 Test des annotations de type...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier que les principales fonctions ont des annotations
        functions_to_check = [
            'def __init__(self',
            'def import_module_safe(self',
            'def get_import_report(self',
            'def _verify_dependencies(self',
            'def get_performance_report(self'
        ]
        
        annotated_functions = 0
        for func in functions_to_check:
            if func in content and '-> ' in content[content.find(func):content.find(func) + 200]:
                annotated_functions += 1
        
        percentage = (annotated_functions / len(functions_to_check)) * 100
        print(f"📊 {annotated_functions}/{len(functions_to_check)} fonctions annotées ({percentage:.1f}%)")
        
        if percentage >= 80:
            print("✅ Annotations de type suffisantes")
            return True
        else:
            print("⚠️ Annotations de type insuffisantes")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test d'annotations: {e}")
        return False

def test_performance_features():
    """Test les fonctionnalités de performance"""
    print("\n🔍 Test des fonctionnalités de performance...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = [
            'ModuleManager',
            'performance_manager',
            'PRAGMA journal_mode = WAL',
            'cache_size',
            'get_performance_report',
            'show_performance_dialog'
        ]
        
        found_features = []
        for feature in features:
            if feature in content:
                found_features.append(feature)
        
        print(f"📊 {len(found_features)}/{len(features)} fonctionnalités de performance trouvées")
        
        for feature in found_features:
            print(f"  ✅ {feature}")
        
        if len(found_features) >= len(features) * 0.8:
            print("✅ Fonctionnalités de performance présentes")
            return True
        else:
            print("⚠️ Fonctionnalités de performance manquantes")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test de performance: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test Final des Corrections de main.py")
    print("=" * 50)
    
    tests = [
        ("Syntaxe", test_syntax),
        ("Imports", test_imports),
        ("Qualité du code", test_code_quality),
        ("Annotations de type", test_type_annotations),
        ("Fonctionnalités de performance", test_performance_features)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n📋 Résumé Final:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Score Final: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Tous les problèmes ont été corrigés avec succès!")
        print("✅ Le fichier main.py est maintenant propre et optimisé")
        return 0
    elif passed >= len(results) * 0.8:
        print("✅ La plupart des problèmes ont été corrigés")
        print("⚠️ Quelques améliorations mineures possibles")
        return 0
    else:
        print("⚠️ Des problèmes subsistent")
        return 1

if __name__ == "__main__":
    sys.exit(main())
