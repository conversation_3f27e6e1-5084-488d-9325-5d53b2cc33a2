#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic réel qui teste l'exécution effective des fichiers
"""

import ast
import os
import sys
import traceback
import subprocess
import importlib.util
from pathlib import Path

def test_real_execution(file_path):
    """Test d'exécution réelle d'un fichier Python"""
    print(f"\n{'='*80}")
    print(f"TEST RÉEL D'EXÉCUTION: {file_path}")
    print(f"{'='*80}")
    
    if not os.path.exists(file_path):
        print(f"❌ FICHIER NON TROUVÉ: {file_path}")
        return False, [f"Fichier non trouvé: {file_path}"]
    
    errors = []
    
    try:
        # Lecture du fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier lu: {len(content)} caractères, {len(content.splitlines())} lignes")
        
        # Test 1: Syntaxe AST
        print(f"\n🔍 TEST 1: SYNTAXE AST")
        print("-" * 40)
        try:
            tree = ast.parse(content, filename=file_path)
            print(f"✅ AST parsing: RÉUSSI")
        except SyntaxError as e:
            error = f"ERREUR AST ligne {e.lineno}, colonne {e.offset}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            
            # Contexte détaillé
            lines = content.splitlines()
            if e.lineno and e.lineno <= len(lines):
                print(f"\n📍 CONTEXTE DE L'ERREUR:")
                start = max(0, e.lineno - 3)
                end = min(len(lines), e.lineno + 3)
                for i in range(start, end):
                    marker = " >>> " if i == e.lineno - 1 else "     "
                    print(f"{marker}{i+1:4d}: {lines[i]}")
                
                if e.text:
                    print(f"\n📍 Code problématique: {repr(e.text.strip())}")
            
            return False, errors
        except Exception as e:
            error = f"ERREUR AST inattendue: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        
        # Test 2: Compilation
        print(f"\n🔍 TEST 2: COMPILATION")
        print("-" * 40)
        try:
            compile(content, file_path, 'exec')
            print(f"✅ Compilation: RÉUSSIE")
        except SyntaxError as e:
            error = f"ERREUR COMPILATION ligne {e.lineno}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        except Exception as e:
            error = f"ERREUR COMPILATION: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        
        # Test 3: Import réel (sans exécution)
        print(f"\n🔍 TEST 3: IMPORT RÉEL")
        print("-" * 40)
        try:
            # Créer un module temporaire
            spec = importlib.util.spec_from_file_location("test_module", file_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                # Ne pas exécuter, juste vérifier que l'import est possible
                print(f"✅ Import: Module peut être importé")
            else:
                print(f"⚠️ Import: Impossible de créer le spec")
        except Exception as e:
            error = f"ERREUR IMPORT: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
        
        # Test 4: Analyse des dépendances
        print(f"\n🔍 TEST 4: ANALYSE DES DÉPENDANCES")
        print("-" * 40)
        
        # Extraire les imports
        imports = []
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        imports.append(f"{module}.{alias.name}")
            
            print(f"📦 Imports détectés: {len(imports)}")
            
            # Vérifier les imports critiques
            critical_imports = ['PySide6', 'sqlalchemy', 'typing']
            missing_critical = []
            
            for critical in critical_imports:
                found = any(critical in imp for imp in imports)
                if found:
                    print(f"✅ {critical}: Importé")
                else:
                    print(f"⚠️ {critical}: Non trouvé dans les imports")
                    if critical == 'PySide6' and 'PySide6' in content:
                        missing_critical.append(critical)
            
            if missing_critical:
                error = f"Imports critiques manquants: {missing_critical}"
                errors.append(error)
        
        except Exception as e:
            error = f"ERREUR ANALYSE DÉPENDANCES: {e}"
            print(f"❌ {error}")
            errors.append(error)
        
        # Test 5: Vérification des types et annotations
        print(f"\n🔍 TEST 5: TYPES ET ANNOTATIONS")
        print("-" * 40)
        
        type_issues = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # Vérifier dict[...] vs Dict[...]
            if 'dict[' in line and 'Dict[' not in line and not line.strip().startswith('#'):
                type_issues.append(f"Ligne {i}: 'dict[...]' devrait être 'Dict[...]'")
            
            # Vérifier list[...] vs List[...]
            if 'list[' in line and 'List[' not in line and not line.strip().startswith('#'):
                type_issues.append(f"Ligne {i}: 'list[...]' devrait être 'List[...]'")
            
            # Vérifier Any vs any
            if ': any =' in line.lower() and not line.strip().startswith('#'):
                type_issues.append(f"Ligne {i}: 'any' devrait être 'Any'")
        
        if type_issues:
            print(f"⚠️ Problèmes de types détectés: {len(type_issues)}")
            for issue in type_issues[:3]:  # Limiter l'affichage
                print(f"   - {issue}")
            if len(type_issues) > 3:
                print(f"   ... et {len(type_issues) - 3} autres")
            errors.extend(type_issues)
        else:
            print(f"✅ Types et annotations: OK")
        
        # Test 6: Subprocess test (test d'exécution externe)
        print(f"\n🔍 TEST 6: EXÉCUTION EXTERNE")
        print("-" * 40)
        try:
            # Test avec subprocess pour voir les vraies erreurs
            result = subprocess.run([
                sys.executable, '-c', 
                f"import ast; ast.parse(open('{file_path}', 'r', encoding='utf-8').read()); print('OK')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ Exécution externe: RÉUSSIE")
            else:
                error = f"ERREUR EXÉCUTION EXTERNE: {result.stderr.strip()}"
                print(f"❌ {error}")
                errors.append(error)
        except subprocess.TimeoutExpired:
            error = "TIMEOUT lors de l'exécution externe"
            print(f"❌ {error}")
            errors.append(error)
        except Exception as e:
            error = f"ERREUR SUBPROCESS: {e}"
            print(f"❌ {error}")
            errors.append(error)
        
        # Résultat final
        if errors:
            print(f"\n❌ FICHIER AVEC ERREURS: {len(errors)} problème(s)")
            return False, errors
        else:
            print(f"\n✅ FICHIER PARFAIT: Tous les tests réussis")
            return True, []
            
    except Exception as e:
        error = f"ERREUR INATTENDUE: {type(e).__name__}: {e}"
        print(f"❌ {error}")
        traceback.print_exc()
        return False, [error]

def test_sql_file():
    """Test spécifique du fichier SQL"""
    print(f"\n{'='*80}")
    print(f"TEST SPÉCIFIQUE DU FICHIER SQL")
    print(f"{'='*80}")
    
    sql_file = 'src/database/migrations/init.sql'
    
    if not os.path.exists(sql_file):
        print(f"❌ Fichier SQL non trouvé: {sql_file}")
        return False, ["Fichier SQL non trouvé"]
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier SQL lu: {len(content)} caractères")
        
        # Compter les problèmes
        serial_count = content.count('SERIAL PRIMARY KEY')
        boolean_count = content.count('BOOLEAN')
        autoincrement_count = content.count('INTEGER PRIMARY KEY AUTOINCREMENT')
        
        print(f"\n📊 ANALYSE SQL:")
        print(f"   SERIAL PRIMARY KEY: {serial_count} occurrences")
        print(f"   BOOLEAN: {boolean_count} occurrences")
        print(f"   INTEGER PRIMARY KEY AUTOINCREMENT: {autoincrement_count} occurrences")
        
        errors = []
        
        if serial_count > 0:
            error = f"SQL: {serial_count} occurrences SERIAL PRIMARY KEY restantes"
            print(f"❌ {error}")
            errors.append(error)
        else:
            print(f"✅ SQL: Aucune occurrence SERIAL PRIMARY KEY")
        
        if boolean_count > 0:
            error = f"SQL: {boolean_count} occurrences BOOLEAN restantes"
            print(f"❌ {error}")
            errors.append(error)
        else:
            print(f"✅ SQL: Aucune occurrence BOOLEAN")
        
        if autoincrement_count == 0:
            error = "SQL: Aucune syntaxe SQLite détectée"
            print(f"❌ {error}")
            errors.append(error)
        else:
            print(f"✅ SQL: {autoincrement_count} corrections SQLite détectées")
        
        return len(errors) == 0, errors
        
    except Exception as e:
        error = f"ERREUR LECTURE SQL: {e}"
        print(f"❌ {error}")
        return False, [error]

def main():
    """Diagnostic réel complet"""
    print("🔍 DIAGNOSTIC RÉEL - TEST D'EXÉCUTION EFFECTIVE")
    print("=" * 80)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Changer vers le bon répertoire si nécessaire
    if not os.path.exists('src/main.py'):
        print("⚠️ Tentative de changement de répertoire...")
        possible_dirs = [
            'C:/Users/<USER>/Desktop/logiciel GestImmob/gestion-software',
            '../',
            './'
        ]
        for dir_path in possible_dirs:
            if os.path.exists(os.path.join(dir_path, 'src/main.py')):
                os.chdir(dir_path)
                print(f"✅ Répertoire changé vers: {os.getcwd()}")
                break
    
    # Liste des fichiers à tester
    files_to_test = [
        'src/main.py',
        'src/config.py',
        'src/database/db_connection.py',
        'src/database/models.py',
        'src/views/admin_view.py',
        'src/auth/user_management.py'
    ]
    
    results = {}
    total_errors = []
    
    # Test des fichiers Python
    for file_path in files_to_test:
        is_valid, errors = test_real_execution(file_path)
        results[file_path] = {
            'valid': is_valid,
            'errors': errors
        }
        if errors:
            total_errors.extend([f"{file_path}: {error}" for error in errors])
    
    # Test du fichier SQL
    sql_valid, sql_errors = test_sql_file()
    if sql_errors:
        total_errors.extend([f"init.sql: {error}" for error in sql_errors])
    
    # Résumé final
    print(f"\n{'='*80}")
    print("📋 RÉSUMÉ DIAGNOSTIC RÉEL")
    print(f"{'='*80}")
    
    python_valid = sum(1 for result in results.values() if result['valid'])
    python_total = len(results)
    
    print(f"📁 Fichiers Python testés: {python_total}")
    print(f"✅ Fichiers Python valides: {python_valid}")
    print(f"❌ Fichiers Python avec erreurs: {python_total - python_valid}")
    print(f"📄 Fichier SQL valide: {'✅ Oui' if sql_valid else '❌ Non'}")
    print(f"🚨 Total erreurs: {len(total_errors)}")
    
    if total_errors:
        print(f"\n🚨 ERREURS DÉTECTÉES:")
        for i, error in enumerate(total_errors, 1):
            print(f"  {i:2d}. {error}")
    
    print(f"\n📊 DÉTAIL PAR FICHIER:")
    for file_path, result in results.items():
        status = "✅ VALIDE" if result['valid'] else "❌ ERREURS"
        error_count = len(result['errors'])
        print(f"  {status:12} {file_path} ({error_count} erreur(s))")
    
    print(f"  {'✅ VALIDE' if sql_valid else '❌ ERREURS':12} init.sql ({len(sql_errors)} erreur(s))")
    
    # Conclusion
    all_valid = python_valid == python_total and sql_valid
    
    if all_valid:
        print(f"\n🎉 TOUS LES FICHIERS SONT PARFAITS!")
        print(f"✅ L'application peut être lancée")
        return True
    else:
        print(f"\n🚨 DES PROBLÈMES RÉELS ONT ÉTÉ DÉTECTÉS")
        print(f"❌ Corrections nécessaires avant le lancement")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat final: {'SUCCÈS COMPLET' if success else 'ÉCHEC - PROBLÈMES DÉTECTÉS'}")
    sys.exit(0 if success else 1)
