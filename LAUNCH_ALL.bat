@echo off
title GestImmob - Lancement Automatique Complet
color 0A

echo.
echo ========================================================================
echo                    GESTIMMOB - LANCEMENT AUTOMATIQUE COMPLET
echo                     Utilise TOUS les moyens necessaires
echo ========================================================================
echo.

echo [%TIME%] Demarrage du processus d'automatisation complete...

REM Verification de Python
echo [%TIME%] Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python non trouve!
    echo Installez Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)
echo Python detecte: OK

REM Verification des fichiers
echo [%TIME%] Verification des fichiers...
if not exist "src\main.py" (
    echo ERREUR: src\main.py non trouve!
    pause
    exit /b 1
)
if not exist "requirements.txt" (
    echo ERREUR: requirements.txt non trouve!
    pause
    exit /b 1
)
echo Fichiers principaux: OK

REM Mise a jour de pip
echo [%TIME%] Mise a jour de pip...
python -m pip install --upgrade pip --quiet

REM Installation des dependances de base
echo [%TIME%] Installation des dependances de base...
python -m pip install -r requirements.txt --quiet

REM Installation des outils de developpement
echo [%TIME%] Installation des outils de developpement...
python -m pip install flake8 pylint mypy black autopep8 bandit safety pytest --quiet

REM Creation des repertoires necessaires
echo [%TIME%] Creation des repertoires...
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "exports" mkdir exports
if not exist "external_tools" mkdir external_tools
if not exist "java_tools" mkdir java_tools
if not exist "sql_tools" mkdir sql_tools
if not exist "powershell_tools" mkdir powershell_tools
if not exist "nodejs_tools" mkdir nodejs_tools

REM Lancement de l'automatisation complete
echo.
echo ========================================================================
echo                        AUTOMATISATION COMPLETE
echo ========================================================================
echo.

echo [%TIME%] Lancement du systeme d'automatisation maitre...
python master_automation.py
if errorlevel 1 (
    echo ATTENTION: Erreur dans l'automatisation maitre
    echo Continuation avec les outils disponibles...
)

echo.
echo [%TIME%] Lancement de l'orchestrateur...
if exist "master_orchestrator.py" (
    python master_orchestrator.py
    if errorlevel 1 (
        echo ATTENTION: Erreur dans l'orchestrateur
    )
) else (
    echo ATTENTION: Orchestrateur non cree
)

echo.
echo [%TIME%] Correction automatique du code...
if exist "auto_fix_main.py" (
    python auto_fix_main.py
) else (
    echo ATTENTION: Script de correction non trouve
)

echo.
echo [%TIME%] Validation complete...
if exist "ultimate_validator.py" (
    python ultimate_validator.py
) else (
    echo ATTENTION: Validateur non trouve
)

echo.
echo [%TIME%] Optimisation de la base de donnees...
if exist "immobilisations.db" (
    if exist "sql_tools\optimize_database.sql" (
        sqlite3 immobilisations.db < sql_tools\optimize_database.sql
        echo Base de donnees optimisee
    )
) else (
    echo Base de donnees sera creee au premier demarrage
)

echo.
echo ========================================================================
echo                           LANCEMENT FINAL
echo ========================================================================
echo.

echo [%TIME%] Lancement de GestImmob...
if exist "start_gestimmob.py" (
    echo Utilisation du script de demarrage optimise...
    start "GestImmob" python start_gestimmob.py
) else (
    echo Demarrage direct...
    start "GestImmob" python src\main.py
)

echo.
echo [%TIME%] Demarrage du monitoring (si disponible)...
if exist "powershell_tools\SystemMonitor.ps1" (
    start "Monitoring" powershell -ExecutionPolicy Bypass -File powershell_tools\SystemMonitor.ps1
)

if exist "nodejs_tools\tools\monitor.js" (
    if exist "nodejs_tools\node_modules" (
        start "Node Monitor" cmd /c "cd nodejs_tools && node tools\monitor.js"
    )
)

echo.
echo ========================================================================
echo                              TERMINE
echo ========================================================================
echo.
echo [%TIME%] Processus d'automatisation complete termine!
echo.
echo OUTILS DISPONIBLES:
echo   - GestImmob: Lance automatiquement
echo   - Monitoring: Active si disponible
echo   - Logs: Repertoire logs\
echo   - Rapports: launch_report.json, validation_report.json
echo.
echo COMMANDES UTILES:
echo   python ultimate_validator.py    - Validation complete
echo   python auto_fix_main.py        - Correction automatique
echo   python diagnostic.py           - Diagnostic systeme
echo   python master_orchestrator.py  - Orchestration manuelle
echo.
echo Consultez README_TOOLS.md pour plus d'informations.
echo.

REM Attendre un peu pour voir les messages
timeout /t 5 /nobreak >nul

REM Ouvrir le navigateur sur la documentation si disponible
if exist "README_TOOLS.md" (
    echo Ouverture de la documentation...
    start README_TOOLS.md
)

echo.
echo Appuyez sur une touche pour fermer cette fenetre...
pause >nul
