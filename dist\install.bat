@echo off
echo Installation de GestImmob...
echo.

REM Créer les dossiers nécessaires
if not exist "%APPDATA%\GestImmob" mkdir "%APPDATA%\GestImmob"
if not exist "%APPDATA%\GestImmob\data" mkdir "%APPDATA%\GestImmob\data"
if not exist "%APPDATA%\GestImmob\logs" mkdir "%APPDATA%\GestImmob\logs"
if not exist "%APPDATA%\GestImmob\backups" mkdir "%APPDATA%\GestImmob\backups"

REM Copier les fichiers
copy "GestImmob.exe" "%APPDATA%\GestImmob\"
copy "config_advanced.json" "%APPDATA%\GestImmob\"

echo Installation terminée!
echo Vous pouvez lancer GestImmob depuis:
echo %APPDATA%\GestImmob\GestImmob.exe
pause
