#!/usr/bin/env python3
"""
[LANCE] LANCEUR GESTIMMOB PROFESSIONNEL v4.0.0
Script de lancement pour la version professionnelle exceptionnelle
Interface verticale moderne, codification propre, modules distincts
"""

import sys
import os
import subprocess
from pathlib import Path

def print_banner():
    """Affiche la bannière de lancement"""
    print("🏠 " + "="*80)
    print("[LANCE] GESTIMMOB PROFESSIONNEL v4.0.0 - VERSION EXCEPTIONNELLE")
    print("[STATS] Logiciel de Gestion Immobilière Inspiré des Meilleurs Logiciels Professionnels")
    print("🎨 Interface Verticale Moderne • Codification Propre • Modules Distincts")
    print("="*84)

def check_requirements():
    """Vérifie les prérequis"""
    print("\n🔍 Vérification des prérequis...")
    
    # Vérifier Python
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"🐍 Python: {python_version}")
    
    if sys.version_info < (3, 8):
        print("[ECHEC] Python 3.8+ requis")
        return False
    
    # Vérifier les fichiers
    files_to_check = [
        "gestimmob_professionnel_v4.py"
    ]
    
    print("\n📁 Vérification des fichiers...")
    for file in files_to_check:
        if Path(file).exists():
            size = Path(file).stat().st_size / 1024
            print(f"✅ {file} ({size:.1f} KB)")
        else:
            print(f"[ECHEC] {file} - MANQUANT")
            return False
    
    # Vérifier PySide6
    print("\n📦 Vérification des dépendances...")
    try:
        import PySide6
        print(f"✅ PySide6: {PySide6.__version__}")
    except ImportError:
        print("[ECHEC] PySide6 non installé")
        print("💡 Installation: pip install PySide6")
        return False
    
    return True

def show_features():
    """Affiche les fonctionnalités"""
    print("\n🎨 INTERFACE PROFESSIONNELLE EXCEPTIONNELLE:")
    print("• ✅ Sidebar verticale moderne avec navigation intuitive")
    print("• ✅ Thème professionnel inspiré de SAP/Oracle/Microsoft Dynamics")
    print("• ✅ Couleurs fixes (plus de couleurs dynamiques)")
    print("• ✅ Boutons alignés verticalement avec icônes")
    print("• ✅ Bouton 'Quitter l'Application' avec style spécial")
    print("• ✅ Design responsive et animations fluides")
    
    print("\n[STATS] MODULES AVEC CRITÈRES SPÉCIFIQUES:")
    print("• [STATS] Tableau de Bord - KPI en temps réel avec cartes")
    print("• 🏠 Gestion des Biens - 25+ champs professionnels")
    print("• 📦 Inventaire - Scanner codes-barres et filtres avancés")
    print("• 🏢 Fournisseurs - Informations légales complètes")
    print("• 👥 Clients - Base de données clientèle")
    print("• 📄 Facturation - Gestion des paiements")
    print("• 📋 Bons de Commande - Workflow complet")
    print("• ♻️ Gestion Réformes - Processus de réforme")
    print("• 📁 Documents - Gestion documentaire")
    print("• 🧮 Calculatrice - Outils financiers")
    print("• 📈 Rapports - Analyses et statistiques")
    print("• [OUTIL] Maintenance - Optimisation système")
    print("• ⚙️ Paramètres - Configuration avancée")
    
    print("\n[OUTIL] CODIFICATION PROFESSIONNELLE:")
    print("• ✅ Architecture modulaire avec classes de configuration")
    print("• ✅ Chaque module avec ses critères distincts")
    print("• ✅ Base de données SQLite complète (10 tables)")
    print("• ✅ Gestion des erreurs et validation des données")
    print("• ✅ Système de permissions et sécurité")
    
    print("\n💎 GRAPHISME EXCEPTIONNEL:")
    print("• ✅ Dégradés professionnels et ombres subtiles")
    print("• ✅ Cartes KPI avec données en temps réel")
    print("• ✅ Formulaires avec validation visuelle")
    print("• ✅ Effets hover et pressed sur les boutons")
    print("• ✅ Scrollbars personnalisées")
    print("• ✅ Tooltips informatifs")

def show_technical_specs():
    """Affiche les spécifications techniques"""
    print("\n💻 SPÉCIFICATIONS TECHNIQUES:")
    print("• 🐍 Python 3.8+ avec PySide6")
    print("• 🗄️ Base de données SQLite professionnelle")
    print("• 🎨 Interface Qt avec thème CSS avancé")
    print("• [STATS] Architecture MVC avec signaux/slots")
    print("• 🔒 Sécurité avec chiffrement SHA256")
    print("• 📱 Interface responsive et modulaire")
    
    print("\n🎯 OPTIMISATIONS:")
    print("• ⚡ Chargement paresseux des modules")
    print("• 🔄 Mise à jour en temps réel des KPI")
    print("• 💾 Sauvegarde automatique des sessions")
    print("• 🎛️ Configuration adaptative")

def main():
    """Fonction principale"""
    print_banner()
    
    if not check_requirements():
        print("\n[ECHEC] Prérequis non satisfaits. Veuillez corriger les erreurs ci-dessus.")
        input("\nAppuyez sur Entrée pour quitter...")
        return
    
    show_features()
    show_technical_specs()
    
    print("\n[LANCE] LANCEMENT DE GESTIMMOB PROFESSIONNEL v4.0.0...")
    print("👤 Connexion par défaut: admin / Admin@1234")
    print("🎯 Interface optimisée pour votre demande spécifique")
    print("="*84)
    
    # Demander confirmation
    response = input("\n🎮 Lancer l'application ? (O/n): ").strip().lower()
    if response in ['', 'o', 'oui', 'y', 'yes']:
        try:
            print("\n🔄 Démarrage en cours...")
            subprocess.run([sys.executable, "gestimmob_professionnel_v4.py"], check=True)
        except KeyboardInterrupt:
            print("\n\n👋 Fermeture de GestImmob Professionnel...")
        except FileNotFoundError:
            print("\n[ECHEC] Fichier gestimmob_professionnel_v4.py non trouvé")
        except Exception as e:
            print(f"\n[ECHEC] Erreur lors du lancement: {e}")
    else:
        print("\n👋 Lancement annulé.")
    
    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
