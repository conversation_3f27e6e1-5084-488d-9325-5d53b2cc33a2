#!/usr/bin/env python3
"""
Suite de tests automatisés pour GestImmob
Tests unitaires et d'intégration pour valider le fonctionnement
"""

import unittest
import sys
import os
import sqlite3
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import logging

# Ajouter le répertoire src au path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configuration du logging pour les tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDatabase(unittest.TestCase):
    """Tests pour la base de données"""
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
        
    def tearDown(self):
        """Nettoyage après chaque test"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
            
    def test_database_creation(self):
        """Test de création de base de données"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Créer une table de test
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_immobilisations (
                id INTEGER PRIMARY KEY,
                designation TEXT NOT NULL,
                valeur REAL NOT NULL
            )
        ''')
        
        # Vérifier que la table existe
        cursor.execute('''
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='test_immobilisations'
        ''')
        
        result = cursor.fetchone()
        self.assertIsNotNone(result)
        self.assertEqual(result[0], 'test_immobilisations')
        
        conn.close()
        
    def test_database_insert_select(self):
        """Test d'insertion et sélection"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Créer la table
        cursor.execute('''
            CREATE TABLE test_items (
                id INTEGER PRIMARY KEY,
                name TEXT,
                value REAL
            )
        ''')
        
        # Insérer des données
        test_data = [
            ('Item 1', 100.50),
            ('Item 2', 200.75),
            ('Item 3', 300.25)
        ]
        
        cursor.executemany(
            'INSERT INTO test_items (name, value) VALUES (?, ?)',
            test_data
        )
        conn.commit()
        
        # Sélectionner les données
        cursor.execute('SELECT name, value FROM test_items ORDER BY id')
        results = cursor.fetchall()
        
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0], ('Item 1', 100.50))
        self.assertEqual(results[1], ('Item 2', 200.75))
        self.assertEqual(results[2], ('Item 3', 300.25))
        
        conn.close()

class TestAuthentication(unittest.TestCase):
    """Tests pour l'authentification"""
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
        
    def tearDown(self):
        """Nettoyage après chaque test"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
            
    def test_password_hashing(self):
        """Test du hachage des mots de passe"""
        import hashlib
        
        password = "test_password_123"
        salt = b"test_salt"
        
        # Hacher le mot de passe
        hashed = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
        
        # Vérifier que le hash est reproductible
        hashed2 = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
        self.assertEqual(hashed, hashed2)
        
        # Vérifier qu'un mot de passe différent donne un hash différent
        wrong_password = "wrong_password"
        wrong_hash = hashlib.pbkdf2_hmac('sha256', wrong_password.encode(), salt, 100000)
        self.assertNotEqual(hashed, wrong_hash)

class TestConfigurationManager(unittest.TestCase):
    """Tests pour la gestion de configuration"""
    
    def setUp(self):
        """Configuration avant chaque test"""
        self.test_config_file = tempfile.NamedTemporaryFile(
            mode='w', delete=False, suffix='.json'
        )
        self.test_config_file.close()
        self.config_path = self.test_config_file.name
        
    def tearDown(self):
        """Nettoyage après chaque test"""
        if os.path.exists(self.config_path):
            os.unlink(self.config_path)
            
    def test_config_load_save(self):
        """Test de chargement et sauvegarde de configuration"""
        import json
        
        # Configuration de test
        test_config = {
            "database": {
                "path": "test.db",
                "timeout": 30
            },
            "ui": {
                "theme": "dark",
                "language": "fr"
            }
        }
        
        # Sauvegarder la configuration
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
            
        # Charger la configuration
        with open(self.config_path, 'r') as f:
            loaded_config = json.load(f)
            
        self.assertEqual(loaded_config, test_config)
        self.assertEqual(loaded_config["database"]["path"], "test.db")
        self.assertEqual(loaded_config["ui"]["theme"], "dark")

class TestCalculations(unittest.TestCase):
    """Tests pour les calculs financiers"""
    
    def test_amortissement_lineaire(self):
        """Test du calcul d'amortissement linéaire"""
        valeur_acquisition = 10000.0
        duree_annees = 5
        
        amortissement_annuel = valeur_acquisition / duree_annees
        
        self.assertEqual(amortissement_annuel, 2000.0)
        
    def test_calcul_pret(self):
        """Test du calcul de mensualités de prêt"""
        capital = 200000.0  # 200k€
        taux_annuel = 0.02  # 2%
        duree_annees = 20
        
        # Formule de calcul des mensualités
        taux_mensuel = taux_annuel / 12
        nb_mensualites = duree_annees * 12
        
        if taux_mensuel > 0:
            mensualite = capital * (
                taux_mensuel * (1 + taux_mensuel) ** nb_mensualites
            ) / (
                (1 + taux_mensuel) ** nb_mensualites - 1
            )
        else:
            mensualite = capital / nb_mensualites
            
        # Vérifier que la mensualité est dans une fourchette raisonnable
        self.assertGreater(mensualite, 800)  # Au moins 800€
        self.assertLess(mensualite, 1200)    # Moins de 1200€
        
    def test_calcul_rentabilite(self):
        """Test du calcul de rentabilité locative"""
        prix_achat = 150000.0
        loyer_mensuel = 800.0
        charges_mensuelles = 100.0
        
        loyer_net_mensuel = loyer_mensuel - charges_mensuelles
        loyer_net_annuel = loyer_net_mensuel * 12
        
        rentabilite_brute = (loyer_mensuel * 12) / prix_achat * 100
        rentabilite_nette = loyer_net_annuel / prix_achat * 100
        
        self.assertAlmostEqual(rentabilite_brute, 6.4, places=1)
        self.assertAlmostEqual(rentabilite_nette, 5.6, places=1)

class TestUIComponents(unittest.TestCase):
    """Tests pour les composants d'interface"""
    
    @patch('PySide6.QtWidgets.QApplication')
    def test_main_window_creation(self, mock_app):
        """Test de création de la fenêtre principale"""
        # Mock de l'application Qt
        mock_app.instance.return_value = Mock()
        
        try:
            # Importer et tester la création de la fenêtre
            # Note: Ce test nécessite que les modules soient importables
            pass  # Placeholder pour éviter les erreurs d'import
        except ImportError:
            self.skipTest("Modules UI non disponibles pour les tests")

class TestDataValidation(unittest.TestCase):
    """Tests pour la validation des données"""
    
    def test_validate_email(self):
        """Test de validation d'email"""
        import re
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        invalid_emails = [
            "invalid.email",
            "@domain.com",
            "user@",
            "user@domain",
            ""
        ]
        
        for email in valid_emails:
            self.assertTrue(
                re.match(email_pattern, email),
                f"Email valide rejeté: {email}"
            )
            
        for email in invalid_emails:
            self.assertFalse(
                re.match(email_pattern, email),
                f"Email invalide accepté: {email}"
            )
            
    def test_validate_phone(self):
        """Test de validation de numéro de téléphone"""
        import re
        
        # Pattern simple pour numéros français
        phone_pattern = r'^(?:\+33|0)[1-9](?:[0-9]{8})$'
        
        valid_phones = [
            "0123456789",
            "+33123456789",
            "0987654321"
        ]
        
        invalid_phones = [
            "123456789",    # Trop court
            "01234567890",  # Trop long
            "0023456789",   # Commence par 00
            "+33023456789", # Commence par 0 après +33
            ""
        ]
        
        for phone in valid_phones:
            self.assertTrue(
                re.match(phone_pattern, phone),
                f"Téléphone valide rejeté: {phone}"
            )
            
        for phone in invalid_phones:
            self.assertFalse(
                re.match(phone_pattern, phone),
                f"Téléphone invalide accepté: {phone}"
            )

class TestPerformance(unittest.TestCase):
    """Tests de performance"""
    
    def test_database_performance(self):
        """Test de performance de base de données"""
        import time
        
        # Créer une base de données temporaire
        test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        test_db.close()
        
        try:
            conn = sqlite3.connect(test_db.name)
            cursor = conn.cursor()
            
            # Créer une table
            cursor.execute('''
                CREATE TABLE performance_test (
                    id INTEGER PRIMARY KEY,
                    data TEXT
                )
            ''')
            
            # Mesurer le temps d'insertion
            start_time = time.time()
            
            # Insérer 1000 enregistrements
            for i in range(1000):
                cursor.execute(
                    'INSERT INTO performance_test (data) VALUES (?)',
                    (f'Test data {i}',)
                )
            
            conn.commit()
            insert_time = time.time() - start_time
            
            # Mesurer le temps de sélection
            start_time = time.time()
            cursor.execute('SELECT COUNT(*) FROM performance_test')
            result = cursor.fetchone()
            select_time = time.time() - start_time
            
            conn.close()
            
            # Vérifications de performance
            self.assertEqual(result[0], 1000)
            self.assertLess(insert_time, 5.0)  # Moins de 5 secondes
            self.assertLess(select_time, 1.0)  # Moins de 1 seconde
            
            logger.info(f"Performance DB - Insert: {insert_time:.3f}s, Select: {select_time:.3f}s")
            
        finally:
            if os.path.exists(test_db.name):
                os.unlink(test_db.name)

def run_all_tests():
    """Exécute tous les tests"""
    logger.info("=== Début des tests automatisés ===")
    
    # Créer la suite de tests
    test_suite = unittest.TestSuite()
    
    # Ajouter les classes de tests
    test_classes = [
        TestDatabase,
        TestAuthentication,
        TestConfigurationManager,
        TestCalculations,
        TestUIComponents,
        TestDataValidation,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Résumé
    logger.info("=== Résumé des tests ===")
    logger.info(f"Tests exécutés: {result.testsRun}")
    logger.info(f"Échecs: {len(result.failures)}")
    logger.info(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        logger.error("Échecs:")
        for test, traceback in result.failures:
            logger.error(f"  - {test}: {traceback}")
    
    if result.errors:
        logger.error("Erreurs:")
        for test, traceback in result.errors:
            logger.error(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    logger.info(f"Résultat global: {'SUCCÈS' if success else 'ÉCHEC'}")
    
    return success

def main():
    """Fonction principale"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("Usage: python run_tests.py [--help]")
        print("Exécute la suite de tests automatisés pour GestImmob")
        return
    
    success = run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
