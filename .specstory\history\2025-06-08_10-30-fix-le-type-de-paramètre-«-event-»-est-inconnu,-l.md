<!-- Generated by SpecStory -->

# /fix Le type de paramètre « event » est inconnu, L (2025-06-08 11:30:53)

_**User**_

/fix Le type de paramètre « event » est inconnu, L'annotation de type est manquante pour le paramètre "event"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Ajouter une annotation de type pour le paramètre `event` dans la méthode `closeEvent`. Pour une fenêtre QWidget sous PySide6, le type correct est `QCloseEvent` provenant de `PySide6.QtGui`.

Étapes :
1. Importer `QCloseEvent` depuis `PySide6.QtGui`.
2. Annoter le paramètre `event` avec le type `QCloseEvent`.

````python
# ...existing code...
from PySide6.QtGui import QColor
from PySide6.QtGui import QCloseEvent  # Ajout de l'import pour l'annotation de type
import logging
# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarde_auto()
        event.accept()
# ...existing code...
````

---

