<!-- Generated by SpecStory -->

# /fix Le type de « sessions » est partiellement inc (2025-06-08 03:09:51)

_**User**_

/fix Le type de « sessions » est partiellement inconnu
  Le type de "sessions" est "dict[Unknown, Unknown]"

---

_**Assistant**_

### [authentication.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/authentication.py)

Déclarez explicitement le type de `self.sessions` pour résoudre le problème de type partiellement inconnu.

````python
from typing import Any, Dict

class Authentication:
    def __init__(self, user_manager: Any):
        self.user_manager = user_manager
        self.sessions: Dict[str, dict] = {}
    # ...existing code...
````

---

