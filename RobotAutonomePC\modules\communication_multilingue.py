#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗣️ ROBOT IA - COMMUNICATION MULTILINGUE AVANCÉE
Interface vocale intelligente, conversation naturelle, 80+ langues
Créé par Augment Agent pour SamNord@110577
"""

import sys
import os
import json
import threading
import time
from datetime import datetime
import speech_recognition as sr
import pyttsx3
from googletrans import Translator
import openai
import requests

class CommunicationMultilingue:
    """🗣️ Système communication multilingue intelligent"""
    
    def __init__(self):
        self.translator = Translator()
        self.recognizer = sr.Recognizer()
        self.tts_engine = pyttsx3.init()
        
        # Configuration utilisateur
        self.user_config = {
            "username": "SamNord@110577",
            "langue_preferee": "fr",
            "voix_preferee": "feminine",
            "vitesse_parole": 150,
            "volume": 0.8,
            "mode_conversation": True
        }
        
        # Langues supportées avec codes vocaux
        self.langues_vocales = {
            "fr": {"nom": "Français", "code_vocal": "fr-FR", "voix": "french"},
            "en": {"nom": "<PERSON><PERSON><PERSON>", "code_vocal": "en-US", "voix": "english"},
            "es": {"nom": "Espagnol", "code_vocal": "es-ES", "voix": "spanish"},
            "de": {"nom": "Allemand", "code_vocal": "de-DE", "voix": "german"},
            "it": {"nom": "Italien", "code_vocal": "it-IT", "voix": "italian"},
            "pt": {"nom": "Portugais", "code_vocal": "pt-PT", "voix": "portuguese"},
            "ru": {"nom": "Russe", "code_vocal": "ru-RU", "voix": "russian"},
            "zh": {"nom": "Chinois", "code_vocal": "zh-CN", "voix": "chinese"},
            "ja": {"nom": "Japonais", "code_vocal": "ja-JP", "voix": "japanese"},
            "ko": {"nom": "Coréen", "code_vocal": "ko-KR", "voix": "korean"},
            "ar": {"nom": "Arabe", "code_vocal": "ar-SA", "voix": "arabic"},
            "hi": {"nom": "Hindi", "code_vocal": "hi-IN", "voix": "hindi"},
            "tr": {"nom": "Turc", "code_vocal": "tr-TR", "voix": "turkish"},
            "pl": {"nom": "Polonais", "code_vocal": "pl-PL", "voix": "polish"},
            "nl": {"nom": "Néerlandais", "code_vocal": "nl-NL", "voix": "dutch"},
            "sv": {"nom": "Suédois", "code_vocal": "sv-SE", "voix": "swedish"},
            "da": {"nom": "Danois", "code_vocal": "da-DK", "voix": "danish"},
            "no": {"nom": "Norvégien", "code_vocal": "no-NO", "voix": "norwegian"},
            "fi": {"nom": "Finnois", "code_vocal": "fi-FI", "voix": "finnish"}
        }
        
        # Contexte conversation
        self.contexte_conversation = {
            "historique": [],
            "langue_active": "fr",
            "mode_traduction": True,
            "personnalite": "assistant_ia_avance"
        }
        
        # Phrases prédéfinies multilingues
        self.phrases_systeme = {
            "fr": {
                "salutation": "Bonjour SamNord@110577, je suis votre assistant IA multilingue.",
                "ecoute": "Je vous écoute...",
                "traduction": "Traduction en cours...",
                "erreur": "Désolé, je n'ai pas compris.",
                "au_revoir": "Au revoir SamNord@110577, à bientôt !"
            },
            "en": {
                "salutation": "Hello SamNord@110577, I am your multilingual AI assistant.",
                "ecoute": "I'm listening...",
                "traduction": "Translating...",
                "erreur": "Sorry, I didn't understand.",
                "au_revoir": "Goodbye SamNord@110577, see you soon!"
            },
            "es": {
                "salutation": "Hola SamNord@110577, soy tu asistente IA multilingüe.",
                "ecoute": "Te escucho...",
                "traduction": "Traduciendo...",
                "erreur": "Lo siento, no entendí.",
                "au_revoir": "Adiós SamNord@110577, ¡hasta pronto!"
            }
        }
        
        self._configurer_tts()
        self._initialiser_conversation()
    
    def _configurer_tts(self):
        """Configuration synthèse vocale avancée"""
        try:
            # Configuration générale
            self.tts_engine.setProperty('rate', self.user_config["vitesse_parole"])
            self.tts_engine.setProperty('volume', self.user_config["volume"])
            
            # Sélection voix selon préférence
            voices = self.tts_engine.getProperty('voices')
            if voices:
                for voice in voices:
                    langue_pref = self.langues_vocales.get(self.user_config["langue_preferee"], {})
                    voix_keyword = langue_pref.get("voix", "french")
                    
                    if voix_keyword in voice.name.lower():
                        if self.user_config["voix_preferee"] == "feminine" and "female" in voice.name.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            break
                        elif self.user_config["voix_preferee"] == "masculine" and "male" in voice.name.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            break
            
            print("✅ Synthèse vocale configurée")
            
        except Exception as e:
            print(f"⚠️ Erreur configuration TTS: {e}")
    
    def _initialiser_conversation(self):
        """Initialisation contexte conversation"""
        self.contexte_conversation["historique"] = [
            {
                "role": "system",
                "content": f"Tu es l'assistant IA personnel de {self.user_config['username']}. Tu es intelligent, serviable et multilingue. Tu peux communiquer dans plus de 80 langues et tu adaptes ton style de communication selon la langue utilisée."
            }
        ]
        
        # Message de bienvenue
        langue = self.user_config["langue_preferee"]
        message_bienvenue = self.phrases_systeme.get(langue, self.phrases_systeme["fr"])["salutation"]
        
        print(f"🤖 {message_bienvenue}")
        self.parler(message_bienvenue, langue)
    
    def ecouter_vocal(self, langue="auto", duree=10):
        """Écoute et reconnaissance vocale multilingue"""
        try:
            print("🎤 Écoute en cours...")
            
            # Configuration microphone
            with sr.Microphone() as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                # Écoute avec timeout
                audio = self.recognizer.listen(source, timeout=duree, phrase_time_limit=duree)
            
            print("🔄 Reconnaissance vocale...")
            
            # Détermination langue pour reconnaissance
            if langue == "auto":
                # Tentative reconnaissance en français d'abord
                try:
                    texte = self.recognizer.recognize_google(audio, language="fr-FR")
                    langue_detectee = "fr"
                except:
                    # Puis anglais
                    try:
                        texte = self.recognizer.recognize_google(audio, language="en-US")
                        langue_detectee = "en"
                    except:
                        # Reconnaissance générale
                        texte = self.recognizer.recognize_google(audio)
                        langue_detectee = "auto"
            else:
                # Reconnaissance dans langue spécifiée
                code_vocal = self.langues_vocales.get(langue, {}).get("code_vocal", "fr-FR")
                texte = self.recognizer.recognize_google(audio, language=code_vocal)
                langue_detectee = langue
            
            print(f"✅ Texte reconnu ({langue_detectee}): {texte}")
            
            return {
                "texte": texte,
                "langue": langue_detectee,
                "timestamp": datetime.now().isoformat()
            }
            
        except sr.WaitTimeoutError:
            print("⏰ Timeout - Aucun son détecté")
            return None
        except sr.UnknownValueError:
            print("❌ Impossible de comprendre l'audio")
            return None
        except sr.RequestError as e:
            print(f"❌ Erreur service reconnaissance: {e}")
            return None
        except Exception as e:
            print(f"❌ Erreur écoute vocale: {e}")
            return None
    
    def parler(self, texte, langue="fr"):
        """Synthèse vocale multilingue intelligente"""
        try:
            if not texte.strip():
                return
            
            print(f"🔊 Synthèse vocale ({langue}): {texte}")
            
            # Configuration voix selon langue
            self._configurer_voix_langue(langue)
            
            # Synthèse avec gestion erreurs
            self.tts_engine.say(texte)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            print(f"❌ Erreur synthèse vocale: {e}")
    
    def _configurer_voix_langue(self, langue):
        """Configuration voix selon langue"""
        try:
            voices = self.tts_engine.getProperty('voices')
            if not voices:
                return
            
            # Recherche voix appropriée
            langue_info = self.langues_vocales.get(langue, {})
            voix_keyword = langue_info.get("voix", "french")
            
            for voice in voices:
                if voix_keyword in voice.name.lower() or langue in voice.id.lower():
                    self.tts_engine.setProperty('voice', voice.id)
                    return
            
        except Exception as e:
            print(f"⚠️ Erreur configuration voix: {e}")
    
    def traduire_intelligent(self, texte, langue_source="auto", langue_cible="fr"):
        """Traduction intelligente avec contexte"""
        try:
            # Traduction de base
            resultat = self.translator.translate(texte, src=langue_source, dest=langue_cible)
            
            # Amélioration avec contexte si disponible
            traduction_amelioree = self._ameliorer_traduction(
                texte, 
                resultat.text, 
                resultat.src, 
                langue_cible
            )
            
            return {
                "texte_original": texte,
                "langue_source": resultat.src,
                "langue_cible": langue_cible,
                "traduction_brute": resultat.text,
                "traduction_amelioree": traduction_amelioree,
                "confiance": getattr(resultat, 'confidence', 1.0),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Erreur traduction: {e}")
            return {
                "texte_original": texte,
                "traduction_amelioree": f"Erreur: {e}",
                "erreur": True
            }
    
    def _ameliorer_traduction(self, original, traduction, langue_src, langue_dest):
        """Amélioration traduction avec IA contextuelle"""
        try:
            # Pour l'instant, retourne la traduction de base
            # Peut être étendu avec OpenAI GPT ou autre modèle
            return traduction
            
        except Exception as e:
            print(f"⚠️ Erreur amélioration traduction: {e}")
            return traduction
    
    def conversation_multilingue(self):
        """Conversation interactive multilingue"""
        print("🗣️ CONVERSATION MULTILINGUE INTERACTIVE")
        print("=" * 45)
        print("🎤 Parlez dans n'importe quelle langue")
        print("🌍 Je traduis et réponds automatiquement")
        print("⏹️ Dites 'arrêter' ou 'stop' pour terminer")
        print()
        
        try:
            while True:
                # Écoute utilisateur
                entree_vocale = self.ecouter_vocal(duree=15)
                
                if not entree_vocale:
                    continue
                
                texte_utilisateur = entree_vocale["texte"]
                langue_utilisateur = entree_vocale["langue"]
                
                print(f"👤 Vous ({langue_utilisateur}): {texte_utilisateur}")
                
                # Vérification commandes d'arrêt
                if any(mot in texte_utilisateur.lower() for mot in ["arrêter", "stop", "quit", "exit", "au revoir", "goodbye"]):
                    langue_reponse = langue_utilisateur if langue_utilisateur != "auto" else "fr"
                    message_aurevoir = self.phrases_systeme.get(langue_reponse, self.phrases_systeme["fr"])["au_revoir"]
                    print(f"🤖 Assistant: {message_aurevoir}")
                    self.parler(message_aurevoir, langue_reponse)
                    break
                
                # Traduction vers français si nécessaire
                if langue_utilisateur != "fr" and langue_utilisateur != "auto":
                    traduction = self.traduire_intelligent(texte_utilisateur, langue_utilisateur, "fr")
                    texte_pour_ia = traduction["traduction_amelioree"]
                    print(f"🔄 Traduction: {texte_pour_ia}")
                else:
                    texte_pour_ia = texte_utilisateur
                
                # Génération réponse IA
                reponse_ia = self._generer_reponse_ia(texte_pour_ia)
                
                # Traduction réponse vers langue utilisateur si nécessaire
                if langue_utilisateur != "fr" and langue_utilisateur != "auto":
                    traduction_reponse = self.traduire_intelligent(reponse_ia, "fr", langue_utilisateur)
                    reponse_finale = traduction_reponse["traduction_amelioree"]
                    langue_reponse = langue_utilisateur
                else:
                    reponse_finale = reponse_ia
                    langue_reponse = "fr"
                
                # Affichage et synthèse réponse
                print(f"🤖 Assistant ({langue_reponse}): {reponse_finale}")
                self.parler(reponse_finale, langue_reponse)
                
                # Ajout à l'historique
                self.contexte_conversation["historique"].extend([
                    {"role": "user", "content": texte_pour_ia},
                    {"role": "assistant", "content": reponse_ia}
                ])
                
                print("-" * 50)
                
        except KeyboardInterrupt:
            print("\n⏹️ Conversation terminée")
    
    def _generer_reponse_ia(self, texte):
        """Génération réponse IA intelligente"""
        try:
            # Réponses prédéfinies pour démo
            reponses_contextuelles = {
                "bonjour": "Bonjour ! Comment puis-je vous aider aujourd'hui ?",
                "comment allez-vous": "Je vais très bien, merci ! Et vous ?",
                "quel temps": "Je ne peux pas vérifier la météo en temps réel, mais je peux vous aider avec d'autres informations.",
                "merci": "De rien ! C'est un plaisir de vous aider.",
                "aide": "Je suis votre assistant IA multilingue. Je peux traduire, converser et vous aider dans de nombreuses tâches.",
                "langues": "Je parle plus de 80 langues ! Français, anglais, espagnol, allemand, chinois, japonais, arabe, et bien d'autres.",
                "qui êtes-vous": f"Je suis votre assistant IA personnel, {self.user_config['username']}. Je suis là pour vous aider dans toutes vos tâches."
            }
            
            # Recherche réponse contextuelle
            texte_lower = texte.lower()
            for mot_cle, reponse in reponses_contextuelles.items():
                if mot_cle in texte_lower:
                    return reponse
            
            # Réponse générique intelligente
            return f"C'est intéressant ! Pouvez-vous me donner plus de détails sur '{texte}' ?"
            
        except Exception as e:
            print(f"❌ Erreur génération réponse: {e}")
            return "Je suis désolé, j'ai rencontré une erreur. Pouvez-vous répéter ?"
    
    def test_langues_vocales(self):
        """Test synthèse vocale multilingue"""
        print("🔊 TEST SYNTHÈSE VOCALE MULTILINGUE")
        print("=" * 40)
        
        phrases_test = {
            "fr": "Bonjour, je suis votre assistant IA en français.",
            "en": "Hello, I am your AI assistant in English.",
            "es": "Hola, soy tu asistente IA en español.",
            "de": "Hallo, ich bin Ihr KI-Assistent auf Deutsch.",
            "it": "Ciao, sono il tuo assistente IA in italiano."
        }
        
        for langue, phrase in phrases_test.items():
            nom_langue = self.langues_vocales.get(langue, {}).get("nom", langue)
            print(f"🔊 Test {nom_langue}: {phrase}")
            self.parler(phrase, langue)
            time.sleep(2)
        
        print("✅ Test synthèse vocale terminé")
    
    def sauvegarder_conversation(self):
        """Sauvegarde historique conversation"""
        try:
            filename = f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            donnees = {
                "utilisateur": self.user_config["username"],
                "timestamp": datetime.now().isoformat(),
                "configuration": self.user_config,
                "historique": self.contexte_conversation["historique"]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(donnees, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Conversation sauvegardée: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            return None

def main():
    """Fonction principale"""
    print("🗣️ COMMUNICATION MULTILINGUE AVANCÉE")
    print("=" * 45)
    print("👤 SamNord@110577")
    print("🌍 80+ langues supportées")
    print("🎤 Reconnaissance vocale intelligente")
    print("🔊 Synthèse vocale naturelle")
    print("💬 Conversation interactive")
    print()
    
    # Création instance
    comm = CommunicationMultilingue()
    
    # Menu principal
    while True:
        print("\n🎯 MENU COMMUNICATION:")
        print("1. 🗣️ Conversation multilingue interactive")
        print("2. 🎤 Test reconnaissance vocale")
        print("3. 🔊 Test synthèse vocale multilingue")
        print("4. 🌍 Traduction intelligente")
        print("5. ⚙️ Configuration")
        print("6. 💾 Sauvegarder conversation")
        print("0. ❌ Quitter")
        
        choix = input("\n➤ Votre choix (0-6): ").strip()
        
        if choix == "1":
            comm.conversation_multilingue()
        elif choix == "2":
            print("🎤 Parlez maintenant...")
            resultat = comm.ecouter_vocal()
            if resultat:
                print(f"✅ Reconnu: {resultat}")
        elif choix == "3":
            comm.test_langues_vocales()
        elif choix == "4":
            texte = input("📝 Texte à traduire: ")
            langue_cible = input("🎯 Langue cible (fr/en/es/de/it...): ") or "fr"
            if texte:
                resultat = comm.traduire_intelligent(texte, langue_cible=langue_cible)
                print(f"🌍 Traduction: {resultat['traduction_amelioree']}")
        elif choix == "5":
            print("⚙️ Configuration utilisateur actuelle:")
            for cle, valeur in comm.user_config.items():
                print(f"   {cle}: {valeur}")
        elif choix == "6":
            comm.sauvegarder_conversation()
        elif choix == "0":
            print("👋 Au revoir !")
            comm.parler("Au revoir SamNord@110577 !", "fr")
            break
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
