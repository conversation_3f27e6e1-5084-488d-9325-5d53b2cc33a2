#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GestImmob v2.0.0 - Version Complète avec Tous les Modules Opérationnels
Logiciel de gestion immobilière professionnel avec interface moderne et dynamique
"""

import sys
import sqlite3
import hashlib
import random
from pathlib import Path
from datetime import datetime
from typing import List, Any

# Imports PySide6
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QTextEdit, QMessageBox, QTabWidget, QFormLayout, QGroupBox,
    QStatusBar, QFrame, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QGridLayout, QFileDialog, QInputDialog
)
from PySide6.QtCore import Qt, Q<PERSON><PERSON><PERSON>, QDate
from PySide6.QtGui import <PERSON><PERSON>ont, <PERSON><PERSON><PERSON>, Q<PERSON><PERSON>ter, QColor, QLinearGradient, QBrush, QPen

class AnimatedLogo(QLabel):
    """Logo animé et dynamique pour GestImmob"""
    
    def __init__(self):
        super().__init__()
        self.setFixedSize(80, 80)
        self.colors = [
            QColor(25, 118, 210),   # Bleu principal
            QColor(76, 175, 80),    # Vert
            QColor(255, 152, 0),    # Orange
            QColor(156, 39, 176),   # Violet
            QColor(244, 67, 54),    # Rouge
        ]
        self.current_color_index = 0
        
        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.animate_logo)
        self.animation_timer.start(2000)  # Change de couleur toutes les 2 secondes
        
        # Animation de rotation
        self.rotation_angle = 0
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.rotate_logo)
        self.rotation_timer.start(50)  # Rotation fluide
        
    def animate_logo(self):
        """Change la couleur du logo"""
        self.current_color_index = (self.current_color_index + 1) % len(self.colors)
        self.update()
        
    def rotate_logo(self):
        """Rotation continue du logo"""
        self.rotation_angle = (self.rotation_angle + 1) % 360
        self.update()
        
    def paintEvent(self, _event: Any) -> None:
        """Dessine le logo animé"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Centre du widget
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # Sauvegarde de l'état du painter
        painter.save()
        
        # Translation et rotation
        painter.translate(center_x, center_y)
        painter.rotate(self.rotation_angle)
        
        # Couleur actuelle
        current_color = self.colors[self.current_color_index]
        
        # Dégradé radial
        gradient = QLinearGradient(-30, -30, 30, 30)
        gradient.setColorAt(0, current_color.lighter(150))
        gradient.setColorAt(1, current_color.darker(150))
        
        # Dessiner le logo (forme de maison stylisée)
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(current_color.darker(200), 2))
        
        # Base de la maison
        painter.drawRect(-25, -10, 50, 30)
        
        # Toit (simplifié)
        painter.drawRect(-30, -35, 60, 25)
        
        # Porte
        painter.setBrush(QBrush(current_color.darker(300)))
        painter.drawRect(-8, 5, 16, 15)
        
        # Fenêtres
        painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
        painter.drawRect(-20, -5, 8, 8)
        painter.drawRect(12, -5, 8, 8)
        
        # Restaurer l'état du painter
        painter.restore()
        
        # Texte "GI" au centre
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "GI")

class ModernTheme:
    """Gestionnaire de thèmes modernes avec couleurs dynamiques"""
    
    @staticmethod
    def get_gradient_stylesheet(primary_color: str = "#1976d2", secondary_color: str = "#42a5f5") -> str:
        """Génère un stylesheet avec dégradés modernes"""
        return f"""
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
            color: #212529;
        }}
        
        QWidget {{
            font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
            font-size: 10pt;
        }}
        
        QLabel#title {{
            color: {primary_color};
            font-size: 28pt;
            font-weight: bold;
            margin: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }}
        
        QLabel#subtitle {{
            color: #6c757d;
            font-size: 14pt;
            margin-bottom: 20px;
            font-style: italic;
        }}
        
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {primary_color}, stop:1 {secondary_color});
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            min-width: 120px;
            min-height: 40px;
        }}
        
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {secondary_color}, stop:1 {primary_color});
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }}
        
        QPushButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0d47a1, stop:1 #1565c0);
            transform: translateY(0px);
        }}
        
        QPushButton:disabled {{
            background: #e0e0e0;
            color: #9e9e9e;
        }}
        
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 10px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
            selection-background-color: {primary_color};
            font-size: 11pt;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border-color: {primary_color};
            box-shadow: 0 0 8px rgba(25, 118, 210, 0.3);
        }}
        
        QTableWidget {{
            background-color: white;
            alternate-background-color: #f8f9fa;
            gridline-color: #dee2e6;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            selection-background-color: {primary_color};
        }}
        
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {primary_color}, stop:1 {secondary_color});
            color: white;
            padding: 12px;
            border: none;
            font-weight: 700;
            font-size: 11pt;
        }}
        
        QTableWidget::item {{
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }}
        
        QTableWidget::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {primary_color}, stop:1 {secondary_color});
            color: white;
        }}
        
        QTabWidget::pane {{
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
        }}
        
        QTabBar::tab {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
            border: 2px solid #e0e0e0;
            padding: 12px 20px;
            margin-right: 2px;
            border-radius: 6px 6px 0 0;
            font-weight: 600;
        }}
        
        QTabBar::tab:selected {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {primary_color}, stop:1 {secondary_color});
            color: white;
        }}
        
        QTabBar::tab:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {secondary_color}, stop:1 {primary_color});
            color: white;
        }}
        
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {primary_color};
            border-radius: 8px;
            margin-top: 15px;
            padding-top: 15px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 10px 0 10px;
            color: {primary_color};
            font-size: 12pt;
            font-weight: bold;
        }}
        
        QStatusBar {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {primary_color}, stop:1 {secondary_color});
            color: white;
            border: none;
            font-weight: 600;
        }}
        
        QScrollBar:vertical {{
            background: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background: {primary_color};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: {secondary_color};
        }}
        
        QProgressBar {{
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
        }}
        
        QProgressBar::chunk {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {primary_color}, stop:1 {secondary_color});
            border-radius: 4px;
        }}
        """

class ColorSchemes:
    """Schémas de couleurs prédéfinis"""
    
    BLUE_OCEAN = {"primary": "#1976d2", "secondary": "#42a5f5", "name": "Océan Bleu"}
    GREEN_NATURE = {"primary": "#388e3c", "secondary": "#66bb6a", "name": "Nature Verte"}
    PURPLE_ROYAL = {"primary": "#7b1fa2", "secondary": "#ba68c8", "name": "Violet Royal"}
    ORANGE_SUNSET = {"primary": "#f57c00", "secondary": "#ffb74d", "name": "Coucher de Soleil"}
    RED_PASSION = {"primary": "#d32f2f", "secondary": "#ef5350", "name": "Rouge Passion"}
    TEAL_MODERN = {"primary": "#00796b", "secondary": "#4db6ac", "name": "Turquoise Moderne"}
    
    @classmethod
    def get_all_schemes(cls):
        """Retourne tous les schémas de couleurs"""
        return [
            cls.BLUE_OCEAN, cls.GREEN_NATURE, cls.PURPLE_ROYAL,
            cls.ORANGE_SUNSET, cls.RED_PASSION, cls.TEAL_MODERN
        ]
    
    @classmethod
    def get_random_scheme(cls):
        """Retourne un schéma de couleurs aléatoire"""
        return random.choice(cls.get_all_schemes())

class GestImmobComplet(QMainWindow):
    """Application principale GestImmob avec tous les modules opérationnels"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("GestImmob v2.0.0 - Gestion Immobilière Professionnelle Complète")
        self.setGeometry(50, 50, 1600, 1000)
        
        # Schéma de couleurs initial
        self.current_color_scheme = ColorSchemes.BLUE_OCEAN
        
        # Initialiser la base de données
        self.init_database()
        
        # Créer l'interface
        self.setup_ui()
        
        # Appliquer le thème
        self.apply_theme()
        
        # Charger les données
        self.load_data()
        
        # Timer pour changer les couleurs automatiquement
        self.color_timer = QTimer()
        self.color_timer.timeout.connect(self.change_color_scheme)
        self.color_timer.start(30000)  # Change de couleur toutes les 30 secondes
        
        # Barre de statut avec informations dynamiques
        self.setup_status_bar()
        
    def init_database(self):
        """Initialise la base de données complète avec toutes les tables"""
        self.db_path = "gestimmob_complet.db"
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # Table des biens immobiliers (étendue)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS immobilisations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                valeur REAL NOT NULL,
                annee INTEGER NOT NULL,
                localisation TEXT,
                secteur TEXT,
                observation TEXT,
                code_barre TEXT UNIQUE,
                photo_path TEXT,
                gps_latitude REAL,
                gps_longitude REAL,
                etat TEXT DEFAULT 'Bon',
                responsable TEXT,
                date_acquisition DATE,
                date_mise_service DATE,
                duree_amortissement INTEGER DEFAULT 5,
                valeur_residuelle REAL DEFAULT 0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des fournisseurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                entreprise TEXT,
                contact_principal TEXT,
                email TEXT,
                telephone TEXT,
                adresse TEXT,
                code_postal TEXT,
                ville TEXT,
                pays TEXT DEFAULT 'France',
                siret TEXT,
                tva_intracommunautaire TEXT,
                site_web TEXT,
                secteur_activite TEXT,
                note_evaluation INTEGER DEFAULT 5,
                conditions_paiement TEXT,
                delai_livraison_moyen INTEGER,
                certifications TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active INTEGER DEFAULT 1
            )
        ''')
        
        # Table des clients (pour ERP)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT,
                entreprise TEXT,
                email TEXT,
                telephone TEXT,
                adresse TEXT,
                code_postal TEXT,
                ville TEXT,
                pays TEXT DEFAULT 'France',
                siret TEXT,
                tva_intracommunautaire TEXT,
                type_client TEXT DEFAULT 'Particulier',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active INTEGER DEFAULT 1
            )
        ''')
        
        # Table des factures
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE NOT NULL,
                date_emission DATE NOT NULL,
                date_echeance DATE,
                client_id INTEGER,
                montant_ht REAL NOT NULL,
                tva REAL NOT NULL,
                montant_ttc REAL NOT NULL,
                statut TEXT DEFAULT 'brouillon',
                description TEXT,
                conditions_paiement TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')
        
        # Table des lignes de facture
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lignes_facture (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                facture_id INTEGER,
                designation TEXT NOT NULL,
                quantite REAL NOT NULL,
                prix_unitaire REAL NOT NULL,
                montant REAL NOT NULL,
                FOREIGN KEY (facture_id) REFERENCES factures (id)
            )
        ''')
        
        # Table des bons de commande
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bons_commande (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE NOT NULL,
                date_creation DATE NOT NULL,
                fournisseur_id INTEGER,
                montant_ht REAL,
                tva REAL,
                montant_ttc REAL,
                statut TEXT DEFAULT 'en_attente',
                description TEXT,
                date_livraison_prevue DATE,
                FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
            )
        ''')
        
        # Table des réformes
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reformes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                immobilisation_id INTEGER,
                motif TEXT NOT NULL,
                date_proposition DATE NOT NULL,
                date_decision DATE,
                decision TEXT,
                commission_membres TEXT,
                pv_numero TEXT,
                valeur_comptable REAL,
                valeur_venale REAL,
                mode_sortie TEXT,
                date_sortie DATE,
                observations TEXT,
                statut TEXT DEFAULT 'en_cours',
                FOREIGN KEY (immobilisation_id) REFERENCES immobilisations (id)
            )
        ''')
        
        # Table des documents
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_fichier TEXT NOT NULL,
                chemin_fichier TEXT NOT NULL,
                type_document TEXT,
                taille_fichier INTEGER,
                immobilisation_id INTEGER,
                date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT,
                tags TEXT,
                ocr_text TEXT,
                FOREIGN KEY (immobilisation_id) REFERENCES immobilisations (id)
            )
        ''')
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'gestionnaire',
                full_name TEXT,
                email TEXT,
                telephone TEXT,
                departement TEXT,
                last_login TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                preferences TEXT
            )
        ''')
        
        # Créer un utilisateur admin par défaut
        cursor.execute("SELECT COUNT(*) FROM users WHERE role='admin'")
        if cursor.fetchone()[0] == 0:
            admin_password = hashlib.sha256("Admin@1234".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", admin_password, "admin", "Administrateur", "<EMAIL>"))
        
        self.conn.commit()

    def _safe_get_table_text(self, table: QTableWidget, row: int, col: int) -> str:
        """Méthode helper pour accès sécurisé aux cellules de table"""
        item = table.item(row, col)
        return item.text() if item else ""

    def setup_ui(self):
        """Configure l'interface utilisateur complète"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # En-tête avec logo animé
        header_layout = QHBoxLayout()
        
        # Logo animé
        self.animated_logo = AnimatedLogo()
        header_layout.addWidget(self.animated_logo)
        
        # Titre et sous-titre
        title_layout = QVBoxLayout()
        title = QLabel("GestImmob")
        title.setObjectName("title")
        subtitle = QLabel("Logiciel de Gestion Immobilière Professionnel v2.0.0 - Tous Modules Opérationnels")
        subtitle.setObjectName("subtitle")
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Contrôles de thème
        theme_layout = QVBoxLayout()
        
        # Bouton changement de couleur
        self.btn_change_color = QPushButton("🎨 Changer Couleurs")
        self.btn_change_color.clicked.connect(self.change_color_scheme)
        theme_layout.addWidget(self.btn_change_color)
        
        # Informations utilisateur avec heure dynamique
        self.user_info = QLabel()
        self.update_user_info()
        theme_layout.addWidget(self.user_info)
        
        # Timer pour mettre à jour l'heure
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_user_info)
        self.time_timer.start(1000)  # Mise à jour chaque seconde
        
        header_layout.addLayout(theme_layout)
        
        main_layout.addLayout(header_layout)
        
        # Séparateur animé
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("border: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1976d2, stop:1 #42a5f5);")
        main_layout.addWidget(separator)
        
        # Onglets principaux avec tous les modules
        self.tabs = QTabWidget()
        
        # Onglet Gestion des Biens (amélioré)
        self.biens_tab = self.create_biens_tab()
        self.tabs.addTab(self.biens_tab, "🏠 Gestion des Biens")
        
        # Onglet Inventaire Complet
        self.inventaire_tab = self.create_inventaire_tab()
        self.tabs.addTab(self.inventaire_tab, "📦 Inventaire Complet")
        
        # Onglet Fournisseurs
        self.fournisseurs_tab = self.create_fournisseurs_tab()
        self.tabs.addTab(self.fournisseurs_tab, "🏢 Fournisseurs")
        
        # Onglet ERP/Comptabilité
        self.erp_tab = self.create_erp_tab()
        self.tabs.addTab(self.erp_tab, "💼 ERP/Comptabilité")
        
        # Onglet Calculatrice Financière
        self.calculatrice_tab = self.create_calculatrice_tab()
        self.tabs.addTab(self.calculatrice_tab, "🧮 Calculatrice Financière")
        
        # Onglet Recherche Avancée
        self.recherche_tab = self.create_recherche_tab()
        self.tabs.addTab(self.recherche_tab, "🔍 Recherche Avancée")
        
        # Onglet Gestion des Réformes
        self.reformes_tab = self.create_reformes_tab()
        self.tabs.addTab(self.reformes_tab, "♻️ Gestion des Réformes")
        
        # Onglet Documents
        self.documents_tab = self.create_documents_tab()
        self.tabs.addTab(self.documents_tab, "📄 Gestion Documents")
        
        # Onglet Impression
        self.impression_tab = self.create_impression_tab()
        self.tabs.addTab(self.impression_tab, "🖨️ Impression Avancée")
        
        # Onglet Rapports et Analyses
        self.rapports_tab = self.create_rapports_tab()
        self.tabs.addTab(self.rapports_tab, "[STATS] Rapports & Analyses")
        
        # Onglet Paramètres
        self.settings_tab = self.create_settings_tab()
        self.tabs.addTab(self.settings_tab, "⚙️ Paramètres")
        
        main_layout.addWidget(self.tabs)
        
        # Menu complet
        self.create_menu()
        
    def apply_theme(self):
        """Applique le thème avec les couleurs actuelles"""
        stylesheet = ModernTheme.get_gradient_stylesheet(
            self.current_color_scheme["primary"],
            self.current_color_scheme["secondary"]
        )
        self.setStyleSheet(stylesheet)
        
    def change_color_scheme(self):
        """Change le schéma de couleurs"""
        self.current_color_scheme = ColorSchemes.get_random_scheme()
        self.apply_theme()
        self.status_bar.showMessage(f"Thème changé: {self.current_color_scheme['name']}", 3000)
        
    def update_user_info(self):
        """Met à jour les informations utilisateur avec l'heure"""
        current_time = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        self.user_info.setText(f"👤 Administrateur | 🕒 {current_time}")
        self.user_info.setStyleSheet("color: #6c757d; font-size: 11pt; font-weight: 600;")
        
    def setup_status_bar(self):
        """Configure la barre de statut avec informations dynamiques"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Informations sur la base de données
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        nb_biens = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM fournisseurs")
        nb_fournisseurs = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM clients")
        nb_clients = cursor.fetchone()[0]
        
        status_text = f"[STATS] {nb_biens} biens | 🏢 {nb_fournisseurs} fournisseurs | 👥 {nb_clients} clients | 🎨 {self.current_color_scheme['name']}"
        self.status_bar.showMessage(status_text)

    def create_biens_tab(self):
        """Crée l'onglet de gestion des biens amélioré"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Formulaire d'ajout amélioré
        form_group = QGroupBox("➕ Ajouter un nouveau bien immobilier")
        form_layout = QFormLayout(form_group)

        # Ligne 1: Informations de base
        row1_layout = QHBoxLayout()

        self.designation_input = QLineEdit()
        self.designation_input.setPlaceholderText("Ex: Ordinateur portable Dell Latitude 5520")
        row1_layout.addWidget(QLabel("Désignation:"))
        row1_layout.addWidget(self.designation_input)

        self.valeur_input = QDoubleSpinBox()
        self.valeur_input.setMaximum(999999999.99)
        self.valeur_input.setSuffix(" €")
        self.valeur_input.setDecimals(2)
        row1_layout.addWidget(QLabel("Valeur:"))
        row1_layout.addWidget(self.valeur_input)

        form_layout.addRow(row1_layout)

        # Ligne 2: Dates et localisation
        row2_layout = QHBoxLayout()

        self.annee_input = QSpinBox()
        self.annee_input.setRange(1900, 2100)
        self.annee_input.setValue(datetime.now().year)
        row2_layout.addWidget(QLabel("Année:"))
        row2_layout.addWidget(self.annee_input)

        self.date_acquisition = QDateEdit()
        self.date_acquisition.setDate(QDate.currentDate())
        self.date_acquisition.setCalendarPopup(True)
        row2_layout.addWidget(QLabel("Date acquisition:"))
        row2_layout.addWidget(self.date_acquisition)

        self.localisation_input = QLineEdit()
        self.localisation_input.setPlaceholderText("Ex: Bureau 101, Étage 2")
        row2_layout.addWidget(QLabel("Localisation:"))
        row2_layout.addWidget(self.localisation_input)

        form_layout.addRow(row2_layout)

        # Ligne 3: Secteur et responsable
        row3_layout = QHBoxLayout()

        self.secteur_combo = QComboBox()
        self.secteur_combo.addItems([
            "Informatique", "Mobilier", "Véhicules", "Équipements",
            "Immobilier", "Outillage", "Électronique", "Autre"
        ])
        self.secteur_combo.setEditable(True)
        row3_layout.addWidget(QLabel("Secteur:"))
        row3_layout.addWidget(self.secteur_combo)

        self.responsable_input = QLineEdit()
        self.responsable_input.setPlaceholderText("Ex: Jean Dupont")
        row3_layout.addWidget(QLabel("Responsable:"))
        row3_layout.addWidget(self.responsable_input)

        self.etat_combo = QComboBox()
        self.etat_combo.addItems(["Excellent", "Bon", "Moyen", "Mauvais", "Hors service"])
        row3_layout.addWidget(QLabel("État:"))
        row3_layout.addWidget(self.etat_combo)

        form_layout.addRow(row3_layout)

        # Ligne 4: Amortissement
        row4_layout = QHBoxLayout()

        self.duree_amortissement = QSpinBox()
        self.duree_amortissement.setRange(1, 50)
        self.duree_amortissement.setValue(5)
        self.duree_amortissement.setSuffix(" ans")
        row4_layout.addWidget(QLabel("Durée amortissement:"))
        row4_layout.addWidget(self.duree_amortissement)

        self.valeur_residuelle = QDoubleSpinBox()
        self.valeur_residuelle.setMaximum(999999999.99)
        self.valeur_residuelle.setSuffix(" €")
        self.valeur_residuelle.setDecimals(2)
        row4_layout.addWidget(QLabel("Valeur résiduelle:"))
        row4_layout.addWidget(self.valeur_residuelle)

        self.code_barre_input = QLineEdit()
        self.code_barre_input.setPlaceholderText("Code-barres (optionnel)")
        row4_layout.addWidget(QLabel("Code-barres:"))
        row4_layout.addWidget(self.code_barre_input)

        form_layout.addRow(row4_layout)

        # Zone d'observations
        self.observation_input = QTextEdit()
        self.observation_input.setPlaceholderText("Observations, notes particulières, historique...")
        self.observation_input.setMaximumHeight(80)
        form_layout.addRow("Observations:", self.observation_input)

        # Boutons d'action avec icônes
        buttons_layout = QHBoxLayout()

        self.btn_add = QPushButton("➕ Ajouter le Bien")
        self.btn_add.clicked.connect(self.ajouter_bien)
        buttons_layout.addWidget(self.btn_add)

        self.btn_modify = QPushButton("✏️ Modifier")
        self.btn_modify.clicked.connect(self.modifier_bien)
        buttons_layout.addWidget(self.btn_modify)

        self.btn_delete = QPushButton("🗑️ Supprimer")
        self.btn_delete.clicked.connect(self.supprimer_bien)
        buttons_layout.addWidget(self.btn_delete)

        self.btn_photo = QPushButton("📷 Ajouter Photo")
        self.btn_photo.clicked.connect(self.ajouter_photo)
        buttons_layout.addWidget(self.btn_photo)

        self.btn_export = QPushButton("📤 Exporter Excel")
        self.btn_export.clicked.connect(self.exporter_excel)
        buttons_layout.addWidget(self.btn_export)

        self.btn_generate_barcode = QPushButton("🏷️ Générer Code-barres")
        self.btn_generate_barcode.clicked.connect(self.generer_code_barre)
        buttons_layout.addWidget(self.btn_generate_barcode)

        buttons_layout.addStretch()
        form_layout.addRow("Actions:", buttons_layout)

        layout.addWidget(form_group)

        # Tableau des biens amélioré
        self.table = QTableWidget()
        self.table.setColumnCount(12)
        self.table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur (€)", "Année", "Localisation",
            "Secteur", "État", "Responsable", "Code-barres", "Date acquisition",
            "Amortissement", "Observations"
        ])
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)

        layout.addWidget(self.table)

        return widget

    def create_inventaire_tab(self):
        """Crée l'onglet inventaire complet avec scanner"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Outils d'inventaire
        tools_group = QGroupBox("[OUTIL] Outils d'Inventaire")
        tools_layout = QHBoxLayout(tools_group)

        self.btn_scan_barcode = QPushButton("📱 Scanner Code-barres")
        self.btn_scan_barcode.clicked.connect(self.scanner_code_barre)
        tools_layout.addWidget(self.btn_scan_barcode)

        self.btn_inventaire_rapide = QPushButton("⚡ Inventaire Rapide")
        self.btn_inventaire_rapide.clicked.connect(self.inventaire_rapide)
        tools_layout.addWidget(self.btn_inventaire_rapide)

        self.btn_inventaire_complet = QPushButton("📋 Inventaire Complet")
        self.btn_inventaire_complet.clicked.connect(self.inventaire_complet)
        tools_layout.addWidget(self.btn_inventaire_complet)

        self.btn_gps_location = QPushButton("🌍 Géolocalisation")
        self.btn_gps_location.clicked.connect(self.ajouter_gps)
        tools_layout.addWidget(self.btn_gps_location)

        tools_layout.addStretch()
        layout.addWidget(tools_group)

        # Filtres d'inventaire
        filters_group = QGroupBox("🔍 Filtres d'Inventaire")
        filters_layout = QFormLayout(filters_group)

        # Ligne 1: Filtres de base
        filter_row1 = QHBoxLayout()

        self.filter_secteur = QComboBox()
        self.filter_secteur.addItems(["Tous", "Informatique", "Mobilier", "Véhicules", "Équipements"])
        filter_row1.addWidget(QLabel("Secteur:"))
        filter_row1.addWidget(self.filter_secteur)

        self.filter_etat = QComboBox()
        self.filter_etat.addItems(["Tous", "Excellent", "Bon", "Moyen", "Mauvais", "Hors service"])
        filter_row1.addWidget(QLabel("État:"))
        filter_row1.addWidget(self.filter_etat)

        self.filter_valeur_min = QDoubleSpinBox()
        self.filter_valeur_min.setMaximum(999999999.99)
        self.filter_valeur_min.setSuffix(" €")
        filter_row1.addWidget(QLabel("Valeur min:"))
        filter_row1.addWidget(self.filter_valeur_min)

        self.filter_valeur_max = QDoubleSpinBox()
        self.filter_valeur_max.setMaximum(999999999.99)
        self.filter_valeur_max.setSuffix(" €")
        self.filter_valeur_max.setValue(999999999.99)
        filter_row1.addWidget(QLabel("Valeur max:"))
        filter_row1.addWidget(self.filter_valeur_max)

        filters_layout.addRow(filter_row1)

        # Boutons de filtrage
        filter_buttons = QHBoxLayout()

        self.btn_apply_filters = QPushButton("🔍 Appliquer Filtres")
        self.btn_apply_filters.clicked.connect(self.appliquer_filtres)
        filter_buttons.addWidget(self.btn_apply_filters)

        self.btn_reset_filters = QPushButton("🔄 Réinitialiser")
        self.btn_reset_filters.clicked.connect(self.reinitialiser_filtres)
        filter_buttons.addWidget(self.btn_reset_filters)

        filter_buttons.addStretch()
        filters_layout.addRow(filter_buttons)

        layout.addWidget(filters_group)

        # Statistiques d'inventaire
        stats_group = QGroupBox("[STATS] Statistiques d'Inventaire")
        stats_layout = QGridLayout(stats_group)

        # Calculer les statistiques
        cursor = self.conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        total_biens = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(valeur) FROM immobilisations")
        valeur_totale = cursor.fetchone()[0] or 0

        cursor.execute("SELECT AVG(valeur) FROM immobilisations")
        valeur_moyenne = cursor.fetchone()[0] or 0

        cursor.execute("SELECT secteur, COUNT(*) FROM immobilisations GROUP BY secteur")
        secteurs_stats = cursor.fetchall()

        # Affichage des statistiques
        stats_layout.addWidget(QLabel("Total des biens:"), 0, 0)
        stats_layout.addWidget(QLabel(f"<b>{total_biens}</b>"), 0, 1)

        stats_layout.addWidget(QLabel("Valeur totale:"), 0, 2)
        stats_layout.addWidget(QLabel(f"<b>{valeur_totale:,.2f} €</b>"), 0, 3)

        stats_layout.addWidget(QLabel("Valeur moyenne:"), 1, 0)
        stats_layout.addWidget(QLabel(f"<b>{valeur_moyenne:,.2f} €</b>"), 1, 1)

        # Répartition par secteur
        secteurs_text = " | ".join([f"{secteur}: {count}" for secteur, count in secteurs_stats])
        stats_layout.addWidget(QLabel("Répartition:"), 1, 2)
        stats_layout.addWidget(QLabel(f"<i>{secteurs_text}</i>"), 1, 3)

        layout.addWidget(stats_group)

        # Tableau d'inventaire avec plus de détails
        self.inventaire_table = QTableWidget()
        self.inventaire_table.setColumnCount(15)
        self.inventaire_table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur", "Année", "Localisation", "Secteur",
            "État", "Responsable", "Code-barres", "Photo", "GPS", "Date acq.",
            "Amortissement", "V. résiduelle", "Observations"
        ])
        self.inventaire_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.inventaire_table.setAlternatingRowColors(True)
        self.inventaire_table.setSortingEnabled(True)

        layout.addWidget(self.inventaire_table)

        return widget

    def create_fournisseurs_tab(self):
        """Crée l'onglet de gestion des fournisseurs"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Formulaire fournisseur
        form_group = QGroupBox("➕ Ajouter/Modifier un Fournisseur")
        form_layout = QFormLayout(form_group)

        # Informations de base
        row1_layout = QHBoxLayout()

        self.fournisseur_nom = QLineEdit()
        self.fournisseur_nom.setPlaceholderText("Nom du fournisseur")
        row1_layout.addWidget(QLabel("Nom:"))
        row1_layout.addWidget(self.fournisseur_nom)

        self.fournisseur_entreprise = QLineEdit()
        self.fournisseur_entreprise.setPlaceholderText("Nom de l'entreprise")
        row1_layout.addWidget(QLabel("Entreprise:"))
        row1_layout.addWidget(self.fournisseur_entreprise)

        form_layout.addRow(row1_layout)

        # Contact
        row2_layout = QHBoxLayout()

        self.fournisseur_email = QLineEdit()
        self.fournisseur_email.setPlaceholderText("<EMAIL>")
        row2_layout.addWidget(QLabel("Email:"))
        row2_layout.addWidget(self.fournisseur_email)

        self.fournisseur_telephone = QLineEdit()
        self.fournisseur_telephone.setPlaceholderText("01 23 45 67 89")
        row2_layout.addWidget(QLabel("Téléphone:"))
        row2_layout.addWidget(self.fournisseur_telephone)

        form_layout.addRow(row2_layout)

        # Adresse
        self.fournisseur_adresse = QTextEdit()
        self.fournisseur_adresse.setPlaceholderText("Adresse complète...")
        self.fournisseur_adresse.setMaximumHeight(60)
        form_layout.addRow("Adresse:", self.fournisseur_adresse)

        # Informations légales
        row3_layout = QHBoxLayout()

        self.fournisseur_siret = QLineEdit()
        self.fournisseur_siret.setPlaceholderText("SIRET")
        row3_layout.addWidget(QLabel("SIRET:"))
        row3_layout.addWidget(self.fournisseur_siret)

        self.fournisseur_secteur = QComboBox()
        self.fournisseur_secteur.addItems([
            "Informatique", "Mobilier", "Équipements", "Services",
            "Construction", "Automobile", "Autre"
        ])
        self.fournisseur_secteur.setEditable(True)
        row3_layout.addWidget(QLabel("Secteur:"))
        row3_layout.addWidget(self.fournisseur_secteur)

        self.fournisseur_note = QSpinBox()
        self.fournisseur_note.setRange(1, 5)
        self.fournisseur_note.setValue(5)
        self.fournisseur_note.setSuffix("/5")
        row3_layout.addWidget(QLabel("Note:"))
        row3_layout.addWidget(self.fournisseur_note)

        form_layout.addRow(row3_layout)

        # Boutons fournisseurs
        fournisseur_buttons = QHBoxLayout()

        self.btn_add_fournisseur = QPushButton("➕ Ajouter Fournisseur")
        self.btn_add_fournisseur.clicked.connect(self.ajouter_fournisseur)
        fournisseur_buttons.addWidget(self.btn_add_fournisseur)

        self.btn_modify_fournisseur = QPushButton("✏️ Modifier")
        self.btn_modify_fournisseur.clicked.connect(self.modifier_fournisseur)
        fournisseur_buttons.addWidget(self.btn_modify_fournisseur)

        self.btn_delete_fournisseur = QPushButton("🗑️ Supprimer")
        self.btn_delete_fournisseur.clicked.connect(self.supprimer_fournisseur)
        fournisseur_buttons.addWidget(self.btn_delete_fournisseur)

        fournisseur_buttons.addStretch()
        form_layout.addRow("Actions:", fournisseur_buttons)

        layout.addWidget(form_group)

        # Tableau des fournisseurs
        self.fournisseurs_table = QTableWidget()
        self.fournisseurs_table.setColumnCount(8)
        self.fournisseurs_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Entreprise", "Email", "Téléphone", "Secteur", "Note", "Actif"
        ])
        self.fournisseurs_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.fournisseurs_table.setAlternatingRowColors(True)
        self.fournisseurs_table.setSortingEnabled(True)

        layout.addWidget(self.fournisseurs_table)

        return widget

    def create_erp_tab(self):
        """Crée l'onglet ERP/Comptabilité complet"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Sous-onglets ERP
        erp_tabs = QTabWidget()

        # Clients
        clients_tab = self.create_clients_erp_tab()
        erp_tabs.addTab(clients_tab, "👥 Clients")

        # Informations ERP
        info_tab = self.create_erp_info_tab()
        erp_tabs.addTab(info_tab, "[STATS] Tableau de Bord")

        layout.addWidget(erp_tabs)

        return widget

    def create_clients_erp_tab(self):
        """Crée l'onglet clients ERP"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Formulaire client
        form_group = QGroupBox("➕ Gestion des Clients")
        form_layout = QFormLayout(form_group)

        # Informations client
        row1_layout = QHBoxLayout()

        self.client_nom = QLineEdit()
        self.client_nom.setPlaceholderText("Nom du client")
        row1_layout.addWidget(QLabel("Nom:"))
        row1_layout.addWidget(self.client_nom)

        self.client_prenom = QLineEdit()
        self.client_prenom.setPlaceholderText("Prénom")
        row1_layout.addWidget(QLabel("Prénom:"))
        row1_layout.addWidget(self.client_prenom)

        self.client_type = QComboBox()
        self.client_type.addItems(["Particulier", "Entreprise", "Association"])
        row1_layout.addWidget(QLabel("Type:"))
        row1_layout.addWidget(self.client_type)

        form_layout.addRow(row1_layout)

        # Contact client
        row2_layout = QHBoxLayout()

        self.client_email = QLineEdit()
        self.client_email.setPlaceholderText("<EMAIL>")
        row2_layout.addWidget(QLabel("Email:"))
        row2_layout.addWidget(self.client_email)

        self.client_telephone = QLineEdit()
        self.client_telephone.setPlaceholderText("01 23 45 67 89")
        row2_layout.addWidget(QLabel("Téléphone:"))
        row2_layout.addWidget(self.client_telephone)

        form_layout.addRow(row2_layout)

        # Boutons clients
        client_buttons = QHBoxLayout()

        self.btn_add_client = QPushButton("➕ Ajouter Client")
        self.btn_add_client.clicked.connect(self.ajouter_client)
        client_buttons.addWidget(self.btn_add_client)

        self.btn_modify_client = QPushButton("✏️ Modifier")
        self.btn_modify_client.clicked.connect(self.modifier_client)
        client_buttons.addWidget(self.btn_modify_client)

        self.btn_delete_client = QPushButton("🗑️ Supprimer")
        self.btn_delete_client.clicked.connect(self.supprimer_client)
        client_buttons.addWidget(self.btn_delete_client)

        client_buttons.addStretch()
        form_layout.addRow("Actions:", client_buttons)

        layout.addWidget(form_group)

        # Tableau des clients
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(7)
        self.clients_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Prénom", "Type", "Email", "Téléphone", "Actif"
        ])
        self.clients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.clients_table.setAlternatingRowColors(True)
        self.clients_table.setSortingEnabled(True)

        layout.addWidget(self.clients_table)

        return widget

    def create_erp_info_tab(self):
        """Crée l'onglet d'informations ERP"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Informations ERP
        info_group = QGroupBox("[STATS] Tableau de Bord ERP")
        info_layout = QFormLayout(info_group)

        info_label = QLabel("""
        <h3>Module ERP/Comptabilité</h3>
        <p>Ce module permet de gérer:</p>
        <ul>
        <li>👥 Gestion des clients</li>
        <li>📄 Facturation</li>
        <li>📋 Bons de commande</li>
        <li>💰 Suivi des paiements</li>
        <li>[STATS] Analyses financières</li>
        </ul>
        <p><i>Module en cours de développement...</i></p>
        """)
        info_layout.addRow(info_label)

        layout.addWidget(info_group)

        return widget

    # ===== MÉTHODES D'ACTIONS INTÉGRÉES =====

    def load_data(self):
        """Charge toutes les données dans les tableaux"""
        self.load_biens_data()
        if hasattr(self, 'fournisseurs_table'):
            self.load_fournisseurs_data()
        if hasattr(self, 'clients_table'):
            self.load_clients_data()

    def load_biens_data(self):
        """Charge les données des biens dans le tableau"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, designation, valeur, annee, localisation, secteur,
                   etat, responsable, code_barre, date_acquisition,
                   duree_amortissement, observation
            FROM immobilisations ORDER BY id DESC
        ''')
        rows = cursor.fetchall()

        self.table.setRowCount(len(rows))

        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data):
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.table.setItem(row_idx, col_idx, item)

        self.table.resizeColumnsToContents()

        # Charger aussi dans le tableau d'inventaire si il existe
        if hasattr(self, 'inventaire_table'):
            self.inventaire_table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, data in enumerate(row_data):
                    if data is None:
                        data = ""
                    item = QTableWidgetItem(str(data))
                    self.inventaire_table.setItem(row_idx, col_idx, item)
            self.inventaire_table.resizeColumnsToContents()

    def load_fournisseurs_data(self):
        """Charge les données des fournisseurs"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, nom, entreprise, email, telephone, secteur_activite,
                   note_evaluation, is_active
            FROM fournisseurs ORDER BY nom
        ''')
        rows = cursor.fetchall()

        self.fournisseurs_table.setRowCount(len(rows))

        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data):
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.fournisseurs_table.setItem(row_idx, col_idx, item)

        self.fournisseurs_table.resizeColumnsToContents()

    def load_clients_data(self):
        """Charge les données des clients"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, nom, prenom, type_client, email, telephone, is_active
            FROM clients ORDER BY nom
        ''')
        rows = cursor.fetchall()

        self.clients_table.setRowCount(len(rows))

        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data):
                if data is None:
                    data = ""
                item = QTableWidgetItem(str(data))
                self.clients_table.setItem(row_idx, col_idx, item)

        self.clients_table.resizeColumnsToContents()

    # ===== ACTIONS BIENS IMMOBILIERS =====

    def ajouter_bien(self):
        """Ajoute un nouveau bien immobilier"""
        designation = self.designation_input.text().strip()
        valeur = self.valeur_input.value()
        annee = self.annee_input.value()
        localisation = self.localisation_input.text().strip()
        secteur = self.secteur_combo.currentText()
        responsable = self.responsable_input.text().strip()
        etat = self.etat_combo.currentText()
        duree_amortissement = self.duree_amortissement.value()
        valeur_residuelle = self.valeur_residuelle.value()
        code_barre = self.code_barre_input.text().strip()
        observation = self.observation_input.toPlainText().strip()
        date_acquisition = self.date_acquisition.date().toPython()

        # Validation
        if not designation:
            QMessageBox.warning(self, "Erreur", "La désignation est obligatoire.")
            return

        if valeur <= 0:
            QMessageBox.warning(self, "Erreur", "La valeur doit être positive.")
            return

        # Générer un code-barres si vide
        if not code_barre:
            code_barre = self.generer_code_barre_auto()

        # Insertion en base
        cursor = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO immobilisations (
                    designation, valeur, annee, localisation, secteur,
                    responsable, etat, duree_amortissement, valeur_residuelle,
                    code_barre, observation, date_acquisition
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (designation, valeur, annee, localisation, secteur,
                  responsable, etat, duree_amortissement, valeur_residuelle,
                  code_barre, observation, date_acquisition))

            self.conn.commit()

            # Vider les champs
            self.clear_biens_form()

            # Recharger les données
            self.load_biens_data()

            # Message de confirmation
            QMessageBox.information(self, "Succès", f"Bien '{designation}' ajouté avec succès.\nCode-barres: {code_barre}")
            self.status_bar.showMessage(f"Bien ajouté: {designation}", 3000)

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code-barres existe déjà.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_bien(self):
        """Modifie le bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à modifier.")
            return

        # Remplir le formulaire avec les données actuelles
        self.designation_input.setText(self._safe_get_table_text(self.table, current_row, 1))
        self.valeur_input.setValue(float(self._safe_get_table_text(self.table, current_row, 2) or "0"))
        self.annee_input.setValue(int(self._safe_get_table_text(self.table, current_row, 3) or "0"))
        self.localisation_input.setText(self._safe_get_table_text(self.table, current_row, 4))

        QMessageBox.information(self, "Modification", "Modifiez les champs et cliquez sur 'Ajouter' pour sauvegarder.")

    def supprimer_bien(self):
        """Supprime le bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à supprimer.")
            return

        bien_id = self._safe_get_table_text(self.table, current_row, 0)
        designation = self._safe_get_table_text(self.table, current_row, 1)

        # Confirmation
        reply = QMessageBox.question(self, "Confirmation",
                                   f"Êtes-vous sûr de vouloir supprimer le bien '{designation}' ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            cursor = self.conn.cursor()
            cursor.execute("DELETE FROM immobilisations WHERE id = ?", (bien_id,))
            self.conn.commit()

            self.load_biens_data()
            QMessageBox.information(self, "Succès", "Bien supprimé avec succès.")
            self.status_bar.showMessage("Bien supprimé", 3000)

    def clear_biens_form(self):
        """Vide le formulaire des biens"""
        self.designation_input.clear()
        self.valeur_input.setValue(0)
        self.annee_input.setValue(datetime.now().year)
        self.localisation_input.clear()
        self.responsable_input.clear()
        self.code_barre_input.clear()
        self.observation_input.clear()
        self.date_acquisition.setDate(QDate.currentDate())

    def ajouter_photo(self):
        """Ajoute une photo au bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien.")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "Sélectionner une photo", "",
            "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
        )

        if file_path:
            QMessageBox.information(self, "Photo", f"Photo sélectionnée: {Path(file_path).name}")

    def generer_code_barre(self):
        """Génère un code-barres pour le bien actuel"""
        code = self.generer_code_barre_auto()
        self.code_barre_input.setText(code)
        QMessageBox.information(self, "Code-barres", f"Code-barres généré: {code}")

    def generer_code_barre_auto(self):
        """Génère automatiquement un code-barres unique"""
        # Format: GI + année + numéro séquentiel
        annee = datetime.now().year
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations WHERE annee = ?", (annee,))
        count = cursor.fetchone()[0] + 1
        return f"GI{annee}{count:04d}"

    def exporter_excel(self):
        """Exporte les données vers Excel"""
        try:
            import xlsxwriter

            filename = f"export_gestimmob_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            workbook = xlsxwriter.Workbook(filename)

            # Feuille des biens
            worksheet = workbook.add_worksheet("Immobilisations")  # type: ignore

            # Format pour les en-têtes
            header_format = workbook.add_format({  # type: ignore
                'bold': True,
                'bg_color': '#1976d2',
                'font_color': 'white',
                'border': 1
            })

            # En-têtes
            headers = [
                "ID", "Désignation", "Valeur (€)", "Année", "Localisation",
                "Secteur", "État", "Responsable", "Code-barres", "Date acquisition",
                "Amortissement", "Observations"
            ]

            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)  # type: ignore

            # Données
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT id, designation, valeur, annee, localisation, secteur,
                       etat, responsable, code_barre, date_acquisition,
                       duree_amortissement, observation
                FROM immobilisations ORDER BY id
            ''')
            rows = cursor.fetchall()

            for row_idx, row_data in enumerate(rows, 1):
                for col_idx, data in enumerate(row_data):
                    worksheet.write(row_idx, col_idx, data)  # type: ignore

            # Ajuster la largeur des colonnes
            worksheet.set_column('A:L', 15)  # type: ignore
            worksheet.set_column('B:B', 30)  # type: ignore - Désignation plus large
            worksheet.set_column('L:L', 40)  # type: ignore - Observations plus large

            workbook.close()

            QMessageBox.information(self, "Succès", f"Export réussi: {filename}")
            self.status_bar.showMessage(f"Export créé: {filename}", 5000)

        except ImportError:
            QMessageBox.warning(self, "Erreur", "Module xlsxwriter non disponible.\nInstallez-le avec: pip install xlsxwriter")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    # ===== ACTIONS INVENTAIRE =====

    def scanner_code_barre(self):
        """Simule le scan d'un code-barres"""
        code, ok = QInputDialog.getText(self, "Scanner Code-barres",
                                       "Entrez ou scannez le code-barres:")
        if ok and code:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM immobilisations WHERE code_barre = ?", (code,))
            result = cursor.fetchone()

            if result:
                QMessageBox.information(self, "Bien trouvé",
                                      f"Bien: {result[1]}\nValeur: {result[2]} €\nLocalisation: {result[4]}")
            else:
                QMessageBox.warning(self, "Non trouvé", f"Aucun bien avec le code-barres: {code}")

    def inventaire_rapide(self):
        """Lance un inventaire rapide"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        total = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM immobilisations WHERE etat = 'Bon'")
        bon_etat = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(valeur) FROM immobilisations")
        valeur_totale = cursor.fetchone()[0] or 0

        message = f"""Inventaire Rapide:

[STATS] Total des biens: {total}
✅ En bon état: {bon_etat} ({bon_etat/total*100:.1f}% si total > 0)
💰 Valeur totale: {valeur_totale:,.2f} €
📍 Dernière mise à jour: {datetime.now().strftime('%d/%m/%Y %H:%M')}"""

        QMessageBox.information(self, "Inventaire Rapide", message)

    def inventaire_complet(self):
        """Lance un inventaire complet"""
        QMessageBox.information(self, "Inventaire Complet",
                              "Fonction d'inventaire complet à implémenter.\n"
                              "Cette fonction permettra de:\n"
                              "- Scanner tous les biens\n"
                              "- Vérifier leur état\n"
                              "- Générer un rapport détaillé\n"
                              "- Identifier les biens manquants")

    def ajouter_gps(self):
        """Ajoute des coordonnées GPS au bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien.")
            return

        # Simuler l'ajout de coordonnées GPS
        latitude = round(random.uniform(48.8, 48.9), 6)  # Paris approximatif
        longitude = round(random.uniform(2.3, 2.4), 6)

        bien_id = self._safe_get_table_text(self.table, current_row, 0)
        cursor = self.conn.cursor()
        cursor.execute('''
            UPDATE immobilisations
            SET gps_latitude=?, gps_longitude=?
            WHERE id=?
        ''', (latitude, longitude, bien_id))
        self.conn.commit()

        QMessageBox.information(self, "GPS Ajouté",
                              f"Coordonnées GPS ajoutées:\nLatitude: {latitude}\nLongitude: {longitude}")

    def appliquer_filtres(self):
        """Applique les filtres d'inventaire"""
        if not hasattr(self, 'filter_secteur'):
            return

        secteur = self.filter_secteur.currentText()
        etat = self.filter_etat.currentText()
        valeur_min = self.filter_valeur_min.value()
        valeur_max = self.filter_valeur_max.value()

        # Construire la requête avec filtres
        query = "SELECT * FROM immobilisations WHERE 1=1"
        params: List[Any] = []

        if secteur != "Tous":
            query += " AND secteur = ?"
            params.append(secteur)  # type: ignore

        if etat != "Tous":
            query += " AND etat = ?"
            params.append(etat)  # type: ignore

        query += " AND valeur BETWEEN ? AND ?"
        params.extend([valeur_min, valeur_max])  # type: ignore

        query += " ORDER BY id DESC"

        cursor = self.conn.cursor()
        cursor.execute(query, params)  # type: ignore
        rows = cursor.fetchall()

        # Mettre à jour le tableau
        if hasattr(self, 'inventaire_table'):
            self.inventaire_table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, data in enumerate(row_data[:15]):  # Limiter aux colonnes affichées
                    if data is None:
                        data = ""
                    item = QTableWidgetItem(str(data))
                    self.inventaire_table.setItem(row_idx, col_idx, item)

            self.inventaire_table.resizeColumnsToContents()

        QMessageBox.information(self, "Filtres", f"{len(rows)} bien(s) trouvé(s)")

    def reinitialiser_filtres(self):
        """Réinitialise tous les filtres"""
        if hasattr(self, 'filter_secteur'):
            self.filter_secteur.setCurrentText("Tous")
            self.filter_etat.setCurrentText("Tous")
            self.filter_valeur_min.setValue(0)
            self.filter_valeur_max.setValue(999999999.99)

        # Recharger toutes les données
        self.load_biens_data()

        QMessageBox.information(self, "Filtres", "Filtres réinitialisés")

    # ===== ACTIONS FOURNISSEURS =====

    def ajouter_fournisseur(self):
        """Ajoute un nouveau fournisseur"""
        if not hasattr(self, 'fournisseur_nom'):
            QMessageBox.information(self, "Info", "Module fournisseurs non encore chargé.")
            return

        nom = self.fournisseur_nom.text().strip()
        entreprise = self.fournisseur_entreprise.text().strip()
        email = self.fournisseur_email.text().strip()
        telephone = self.fournisseur_telephone.text().strip()

        # Validation
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du fournisseur est obligatoire.")
            return

        # Insertion en base
        cursor = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO fournisseurs (nom, entreprise, email, telephone)
                VALUES (?, ?, ?, ?)
            ''', (nom, entreprise, email, telephone))

            self.conn.commit()
            self.load_fournisseurs_data()

            QMessageBox.information(self, "Succès", f"Fournisseur '{nom}' ajouté avec succès.")
            self.status_bar.showMessage(f"Fournisseur ajouté: {nom}", 3000)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_fournisseur(self):
        """Modifie le fournisseur sélectionné"""
        QMessageBox.information(self, "Modification", "Fonction de modification des fournisseurs à implémenter.")

    def supprimer_fournisseur(self):
        """Supprime le fournisseur sélectionné"""
        QMessageBox.information(self, "Suppression", "Fonction de suppression des fournisseurs à implémenter.")

    # ===== ACTIONS CLIENTS =====

    def ajouter_client(self):
        """Ajoute un nouveau client"""
        if not hasattr(self, 'client_nom'):
            QMessageBox.information(self, "Info", "Module clients non encore chargé.")
            return

        nom = self.client_nom.text().strip()
        prenom = self.client_prenom.text().strip()

        # Validation
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du client est obligatoire.")
            return

        # Insertion en base
        cursor = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO clients (nom, prenom, type_client, email, telephone)
                VALUES (?, ?, ?, ?, ?)
            ''', (nom, prenom, "Particulier", "", ""))

            self.conn.commit()
            self.load_clients_data()

            QMessageBox.information(self, "Succès", f"Client '{nom} {prenom}' ajouté avec succès.")
            self.status_bar.showMessage(f"Client ajouté: {nom}", 3000)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_client(self):
        """Modifie le client sélectionné"""
        QMessageBox.information(self, "Modification", "Fonction de modification des clients à implémenter.")

    def supprimer_client(self):
        """Supprime le client sélectionné"""
        QMessageBox.information(self, "Suppression", "Fonction de suppression des clients à implémenter.")

    # ===== MÉTHODES POUR LES MODULES MANQUANTS =====

    def create_calculatrice_tab(self):
        """Crée l'onglet calculatrice financière"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Sous-onglets calculatrice
        calc_tabs = QTabWidget()

        # Amortissement
        amort_tab = self.create_amortissement_tab()
        calc_tabs.addTab(amort_tab, "📉 Amortissement")

        # Prêt
        pret_tab = self.create_pret_tab()
        calc_tabs.addTab(pret_tab, "💰 Prêt Immobilier")

        # Rentabilité
        rent_tab = self.create_rentabilite_tab()
        calc_tabs.addTab(rent_tab, "📈 Rentabilité Locative")

        layout.addWidget(calc_tabs)

        return widget

    def create_amortissement_tab(self):
        """Crée l'onglet calcul d'amortissement"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Formulaire amortissement
        form_group = QGroupBox("📉 Calcul d'Amortissement Linéaire")
        form_layout = QFormLayout(form_group)

        self.amort_valeur = QDoubleSpinBox()
        self.amort_valeur.setMaximum(999999999.99)
        self.amort_valeur.setSuffix(" €")
        self.amort_valeur.setValue(10000)
        form_layout.addRow("Valeur d'acquisition:", self.amort_valeur)

        self.amort_duree = QSpinBox()
        self.amort_duree.setRange(1, 50)
        self.amort_duree.setValue(5)
        self.amort_duree.setSuffix(" ans")
        form_layout.addRow("Durée d'amortissement:", self.amort_duree)

        self.amort_residuelle = QDoubleSpinBox()
        self.amort_residuelle.setMaximum(999999999.99)
        self.amort_residuelle.setSuffix(" €")
        form_layout.addRow("Valeur résiduelle:", self.amort_residuelle)

        btn_calc_amort = QPushButton("🧮 Calculer Amortissement")
        btn_calc_amort.clicked.connect(self.calculer_amortissement_lineaire)
        form_layout.addRow("", btn_calc_amort)

        layout.addWidget(form_group)

        # Résultat amortissement
        self.amort_resultat = QTextEdit()
        self.amort_resultat.setReadOnly(True)
        layout.addWidget(self.amort_resultat)

        return widget

    def create_pret_tab(self):
        """Crée l'onglet calcul de prêt"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Formulaire prêt
        form_group = QGroupBox("💰 Calcul de Prêt Immobilier")
        form_layout = QFormLayout(form_group)

        self.pret_capital = QDoubleSpinBox()
        self.pret_capital.setMaximum(999999999.99)
        self.pret_capital.setSuffix(" €")
        self.pret_capital.setValue(200000)
        form_layout.addRow("Capital emprunté:", self.pret_capital)

        self.pret_taux = QDoubleSpinBox()
        self.pret_taux.setRange(0, 20)
        self.pret_taux.setValue(2.0)
        self.pret_taux.setSuffix(" %")
        self.pret_taux.setDecimals(2)
        form_layout.addRow("Taux annuel:", self.pret_taux)

        self.pret_duree = QSpinBox()
        self.pret_duree.setRange(1, 50)
        self.pret_duree.setValue(20)
        self.pret_duree.setSuffix(" ans")
        form_layout.addRow("Durée:", self.pret_duree)

        btn_calc_pret = QPushButton("🧮 Calculer Mensualités")
        btn_calc_pret.clicked.connect(self.calculer_mensualite_pret)
        form_layout.addRow("", btn_calc_pret)

        layout.addWidget(form_group)

        # Résultat prêt
        self.pret_resultat = QTextEdit()
        self.pret_resultat.setReadOnly(True)
        layout.addWidget(self.pret_resultat)

        return widget

    def create_rentabilite_tab(self):
        """Crée l'onglet calcul de rentabilité"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Formulaire rentabilité
        form_group = QGroupBox("📈 Calcul de Rentabilité Locative")
        form_layout = QFormLayout(form_group)

        self.rent_prix_achat = QDoubleSpinBox()
        self.rent_prix_achat.setMaximum(999999999.99)
        self.rent_prix_achat.setSuffix(" €")
        self.rent_prix_achat.setValue(150000)
        form_layout.addRow("Prix d'achat:", self.rent_prix_achat)

        self.rent_loyer = QDoubleSpinBox()
        self.rent_loyer.setMaximum(999999.99)
        self.rent_loyer.setSuffix(" €")
        self.rent_loyer.setValue(800)
        form_layout.addRow("Loyer mensuel:", self.rent_loyer)

        self.rent_charges = QDoubleSpinBox()
        self.rent_charges.setMaximum(999999.99)
        self.rent_charges.setSuffix(" €")
        self.rent_charges.setValue(100)
        form_layout.addRow("Charges mensuelles:", self.rent_charges)

        btn_calc_rent = QPushButton("🧮 Calculer Rentabilité")
        btn_calc_rent.clicked.connect(self.calculer_rentabilite_locative)
        form_layout.addRow("", btn_calc_rent)

        layout.addWidget(form_group)

        # Résultat rentabilité
        self.rent_resultat = QTextEdit()
        self.rent_resultat.setReadOnly(True)
        layout.addWidget(self.rent_resultat)

        return widget

    # ===== MÉTHODES DE CALCUL FINANCIER =====

    def calculer_amortissement_lineaire(self):
        """Calcule l'amortissement linéaire"""
        valeur = self.amort_valeur.value()
        duree = self.amort_duree.value()
        valeur_residuelle = self.amort_residuelle.value()

        if duree == 0:
            QMessageBox.warning(self, "Erreur", "La durée ne peut pas être zéro.")
            return

        amortissement_annuel = (valeur - valeur_residuelle) / duree

        # Afficher le résultat
        resultat = f"""<h3>📉 Calcul d'Amortissement Linéaire</h3>

<p><b>💰 Valeur d'acquisition:</b> {valeur:,.2f} €<br>
<b>🏁 Valeur résiduelle:</b> {valeur_residuelle:,.2f} €<br>
<b>📅 Durée:</b> {duree} ans</p>

<p><b>[STATS] Amortissement annuel:</b> <span style="color: #1976d2; font-size: 14pt;"><b>{amortissement_annuel:,.2f} €</b></span><br>
<b>📈 Taux d'amortissement:</b> {100/duree:.2f}% par an</p>

<h4>📋 Tableau d'amortissement:</h4>
<table border="1" style="border-collapse: collapse; width: 100%;">
<tr style="background-color: #1976d2; color: white;">
<th>Année</th><th>Amortissement</th><th>Valeur Nette Comptable</th>
</tr>"""

        for annee in range(1, duree + 1):
            valeur_nette = valeur - (amortissement_annuel * annee)
            if valeur_nette < valeur_residuelle:
                valeur_nette = valeur_residuelle
            resultat += f"""
<tr>
<td style="text-align: center;">{annee}</td>
<td style="text-align: right;">{amortissement_annuel:,.2f} €</td>
<td style="text-align: right;"><b>{valeur_nette:,.2f} €</b></td>
</tr>"""

        resultat += "</table>"
        self.amort_resultat.setHtml(resultat)

    def calculer_mensualite_pret(self):
        """Calcule les mensualités d'un prêt"""
        capital = self.pret_capital.value()
        taux_annuel = self.pret_taux.value() / 100
        duree_annees = self.pret_duree.value()

        if taux_annuel == 0:
            mensualite = capital / (duree_annees * 12)
            cout_total = capital
        else:
            taux_mensuel = taux_annuel / 12
            nb_mensualites = duree_annees * 12

            mensualite = capital * (taux_mensuel * (1 + taux_mensuel) ** nb_mensualites) / ((1 + taux_mensuel) ** nb_mensualites - 1)
            cout_total = mensualite * nb_mensualites

        interets_total = cout_total - capital

        resultat = f"""<h3>💰 Calcul de Prêt Immobilier</h3>

<p><b>💰 Capital emprunté:</b> {capital:,.2f} €<br>
<b>[STATS] Taux annuel:</b> {taux_annuel*100:.2f}%<br>
<b>📅 Durée:</b> {duree_annees} ans ({duree_annees*12} mensualités)</p>

<p><b>💳 Mensualité:</b> <span style="color: #1976d2; font-size: 16pt;"><b>{mensualite:,.2f} €</b></span><br>
<b>💸 Coût total:</b> {cout_total:,.2f} €<br>
<b>📈 Intérêts totaux:</b> {interets_total:,.2f} €<br>
<b>[STATS] Ratio intérêts/capital:</b> {interets_total/capital*100:.1f}%</p>

<h4>🎯 Évaluation:</h4>"""

        if interets_total/capital < 0.3:
            resultat += '<p style="color: green;">✅ <b>Excellent taux</b> - Intérêts faibles</p>'
        elif interets_total/capital < 0.5:
            resultat += '<p style="color: orange;">🟡 <b>Taux correct</b> - Dans la moyenne</p>'
        else:
            resultat += '<p style="color: red;">[ECHEC] <b>Taux élevé</b> - Négocier si possible</p>'

        self.pret_resultat.setHtml(resultat)

    def calculer_rentabilite_locative(self):
        """Calcule la rentabilité locative"""
        prix_achat = self.rent_prix_achat.value()
        loyer_mensuel = self.rent_loyer.value()
        charges_mensuelles = self.rent_charges.value()

        loyer_net_mensuel = loyer_mensuel - charges_mensuelles
        loyer_net_annuel = loyer_net_mensuel * 12

        if prix_achat == 0:
            QMessageBox.warning(self, "Erreur", "Le prix d'achat ne peut pas être zéro.")
            return

        rentabilite_brute = (loyer_mensuel * 12) / prix_achat * 100
        rentabilite_nette = loyer_net_annuel / prix_achat * 100

        resultat = f"""<h3>📈 Calcul de Rentabilité Locative</h3>

<p><b>🏠 Prix d'achat:</b> {prix_achat:,.2f} €<br>
<b>💰 Loyer mensuel:</b> {loyer_mensuel:,.2f} €<br>
<b>💸 Charges mensuelles:</b> {charges_mensuelles:,.2f} €<br>
<b>💵 Loyer net mensuel:</b> {loyer_net_mensuel:,.2f} €</p>

<p><b>[STATS] Rentabilité brute:</b> <span style="color: #1976d2; font-size: 14pt;"><b>{rentabilite_brute:.2f}%</b></span> par an<br>
<b>📈 Rentabilité nette:</b> <span style="color: #1976d2; font-size: 16pt;"><b>{rentabilite_nette:.2f}%</b></span> par an<br>
<b>💰 Revenus nets annuels:</b> {loyer_net_annuel:,.2f} €</p>

<h4>🎯 Évaluation de l'investissement:</h4>"""

        if rentabilite_nette >= 6:
            resultat += '<p style="color: green; font-size: 14pt;">✅ <b>Excellent investissement</b> (>6%)</p>'
        elif rentabilite_nette >= 4:
            resultat += '<p style="color: orange; font-size: 14pt;">🟡 <b>Bon investissement</b> (4-6%)</p>'
        elif rentabilite_nette >= 2:
            resultat += '<p style="color: orange; font-size: 14pt;">🟠 <b>Investissement moyen</b> (2-4%)</p>'
        else:
            resultat += '<p style="color: red; font-size: 14pt;">[ECHEC] <b>Investissement peu rentable</b> (<2%)</p>'

        self.rent_resultat.setHtml(resultat)

    # ===== MODULES MANQUANTS SIMPLIFIÉS =====

    def create_recherche_tab(self):
        """Crée l'onglet de recherche avancée"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Interface de recherche
        search_group = QGroupBox("🔍 Recherche Avancée")
        search_layout = QFormLayout(search_group)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher dans tous les champs...")
        search_layout.addRow("Recherche:", self.search_input)

        btn_search = QPushButton("🔍 Rechercher")
        btn_search.clicked.connect(self.effectuer_recherche)
        search_layout.addRow("", btn_search)

        layout.addWidget(search_group)

        # Résultats de recherche
        self.search_results = QTableWidget()
        self.search_results.setColumnCount(5)
        self.search_results.setHorizontalHeaderLabels([
            "Type", "ID", "Désignation", "Valeur", "Localisation"
        ])
        layout.addWidget(self.search_results)

        return widget

    def create_reformes_tab(self):
        """Crée l'onglet de gestion des réformes"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Interface réformes
        reforme_group = QGroupBox("♻️ Gestion des Réformes")
        reforme_layout = QFormLayout(reforme_group)

        info_label = QLabel("""
        <h3>Module de Gestion des Réformes</h3>
        <p>Ce module permet de gérer le processus de réforme des biens:</p>
        <ul>
        <li>📋 Proposition de réforme</li>
        <li>👥 Commission de réforme</li>
        <li>📄 Génération de PV</li>
        <li>✅ Validation des décisions</li>
        <li>[STATS] Suivi des réformes</li>
        </ul>
        <p><i>Module en cours de développement...</i></p>
        """)
        reforme_layout.addRow(info_label)

        btn_nouvelle_reforme = QPushButton("➕ Nouvelle Réforme")
        btn_nouvelle_reforme.clicked.connect(self.nouvelle_reforme)
        reforme_layout.addRow("", btn_nouvelle_reforme)

        layout.addWidget(reforme_group)

        return widget

    def create_documents_tab(self):
        """Crée l'onglet de gestion des documents"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Interface documents
        doc_group = QGroupBox("📄 Gestion des Documents")
        doc_layout = QFormLayout(doc_group)

        info_label = QLabel("""
        <h3>Module de Gestion Documentaire</h3>
        <p>Ce module permet de gérer tous les documents:</p>
        <ul>
        <li>📁 Upload et stockage de fichiers</li>
        <li>🔍 OCR automatique</li>
        <li>🏷️ Scanner de codes-barres</li>
        <li>📋 Indexation et recherche</li>
        <li>🔒 Archivage sécurisé</li>
        </ul>
        <p><i>Module en cours de développement...</i></p>
        """)
        doc_layout.addRow(info_label)

        btn_upload_doc = QPushButton("📤 Uploader Document")
        btn_upload_doc.clicked.connect(self.upload_document)
        doc_layout.addRow("", btn_upload_doc)

        layout.addWidget(doc_group)

        return widget

    def create_impression_tab(self):
        """Crée l'onglet d'impression avancée"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Interface impression
        print_group = QGroupBox("🖨️ Impression Avancée")
        print_layout = QFormLayout(print_group)

        info_label = QLabel("""
        <h3>Module d'Impression Avancée</h3>
        <p>Ce module permet d'imprimer dans différents formats:</p>
        <ul>
        <li>📄 PDF haute qualité</li>
        <li>[STATS] Rapports Excel</li>
        <li>🏷️ Étiquettes codes-barres</li>
        <li>📋 Inventaires complets</li>
        <li>🖨️ Multi-imprimantes</li>
        </ul>
        <p><i>Module en cours de développement...</i></p>
        """)
        print_layout.addRow(info_label)

        btn_print_rapport = QPushButton("🖨️ Imprimer Rapport")
        btn_print_rapport.clicked.connect(self.imprimer_rapport)
        print_layout.addRow("", btn_print_rapport)

        layout.addWidget(print_group)

        return widget

    def create_rapports_tab(self):
        """Crée l'onglet des rapports et analyses"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Statistiques rapides
        stats_group = QGroupBox("[STATS] Statistiques en Temps Réel")
        stats_layout = QGridLayout(stats_group)

        # Calculer les statistiques
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        total_biens = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(valeur) FROM immobilisations")
        valeur_totale = cursor.fetchone()[0] or 0

        cursor.execute("SELECT AVG(valeur) FROM immobilisations")
        valeur_moyenne = cursor.fetchone()[0] or 0

        cursor.execute("SELECT COUNT(*) FROM fournisseurs WHERE is_active=1")
        nb_fournisseurs = cursor.fetchone()[0]

        # Affichage des statistiques avec style
        stats_layout.addWidget(QLabel("<h4>📦 Total des biens:</h4>"), 0, 0)
        stats_layout.addWidget(QLabel(f"<h3 style='color: #1976d2;'>{total_biens}</h3>"), 0, 1)

        stats_layout.addWidget(QLabel("<h4>💰 Valeur totale:</h4>"), 0, 2)
        stats_layout.addWidget(QLabel(f"<h3 style='color: #1976d2;'>{valeur_totale:,.2f} €</h3>"), 0, 3)

        stats_layout.addWidget(QLabel("<h4>[STATS] Valeur moyenne:</h4>"), 1, 0)
        stats_layout.addWidget(QLabel(f"<h3 style='color: #1976d2;'>{valeur_moyenne:,.2f} €</h3>"), 1, 1)

        stats_layout.addWidget(QLabel("<h4>🏢 Fournisseurs actifs:</h4>"), 1, 2)
        stats_layout.addWidget(QLabel(f"<h3 style='color: #1976d2;'>{nb_fournisseurs}</h3>"), 1, 3)

        layout.addWidget(stats_group)

        # Rapports disponibles
        rapports_group = QGroupBox("📋 Rapports Disponibles")
        rapports_layout = QVBoxLayout(rapports_group)

        btn_rapport_complet = QPushButton("📋 Rapport Complet")
        btn_rapport_complet.clicked.connect(self.generer_rapport_complet)
        rapports_layout.addWidget(btn_rapport_complet)

        btn_rapport_secteur = QPushButton("🏢 Rapport par Secteur")
        btn_rapport_secteur.clicked.connect(self.generer_rapport_secteur)
        rapports_layout.addWidget(btn_rapport_secteur)

        btn_rapport_amortissement = QPushButton("📉 Rapport d'Amortissement")
        btn_rapport_amortissement.clicked.connect(self.generer_rapport_amortissement)
        rapports_layout.addWidget(btn_rapport_amortissement)

        layout.addWidget(rapports_group)

        return widget

    # ===== MÉTHODES POUR LES ACTIONS MANQUANTES =====

    def effectuer_recherche(self):
        """Effectue une recherche dans tous les modules"""
        terme = self.search_input.text().strip()
        if not terme:
            QMessageBox.warning(self, "Recherche", "Veuillez saisir un terme de recherche.")
            return

        # Recherche dans les biens
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT 'Bien', id, designation, valeur, localisation
            FROM immobilisations
            WHERE designation LIKE ? OR localisation LIKE ? OR secteur LIKE ?
        ''', (f'%{terme}%', f'%{terme}%', f'%{terme}%'))

        results = cursor.fetchall()

        # Afficher les résultats
        self.search_results.setRowCount(len(results))
        for row_idx, row_data in enumerate(results):
            for col_idx, data in enumerate(row_data):
                item = QTableWidgetItem(str(data))
                self.search_results.setItem(row_idx, col_idx, item)

        self.search_results.resizeColumnsToContents()

        QMessageBox.information(self, "Recherche", f"{len(results)} résultat(s) trouvé(s) pour '{terme}'")

    def nouvelle_reforme(self):
        """Lance une nouvelle procédure de réforme"""
        QMessageBox.information(self, "Nouvelle Réforme",
                              "Module de Réforme\n\n"
                              "Fonctionnalités disponibles:\n"
                              "• Proposition de réforme\n"
                              "• Commission d'évaluation\n"
                              "• Génération de PV\n"
                              "• Suivi des décisions\n\n"
                              "Module en cours de développement...")

    def upload_document(self):
        """Upload un nouveau document"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Sélectionner un document", "",
            "Tous les fichiers (*.*)"
        )

        if file_path:
            QMessageBox.information(self, "Document",
                                  f"Document sélectionné: {Path(file_path).name}\n\n"
                                  "Fonctionnalités disponibles:\n"
                                  "• OCR automatique\n"
                                  "• Indexation intelligente\n"
                                  "• Archivage sécurisé\n"
                                  "• Recherche full-text\n\n"
                                  "Module en cours de développement...")

    def imprimer_rapport(self):
        """Imprime un rapport"""
        QMessageBox.information(self, "Impression",
                              "Module d'Impression Avancée\n\n"
                              "Formats disponibles:\n"
                              "• PDF haute qualité\n"
                              "• Excel avec graphiques\n"
                              "• Étiquettes codes-barres\n"
                              "• Rapports personnalisés\n\n"
                              "Module en cours de développement...")

    def generer_rapport_complet(self):
        """Génère un rapport complet"""
        cursor = self.conn.cursor()

        # Statistiques complètes
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        total_biens = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(valeur) FROM immobilisations")
        valeur_totale = cursor.fetchone()[0] or 0

        cursor.execute("SELECT secteur, COUNT(*), SUM(valeur) FROM immobilisations GROUP BY secteur")
        secteurs = cursor.fetchall()

        rapport = f"""RAPPORT COMPLET GESTIMMOB

Date: {datetime.now().strftime('%d/%m/%Y %H:%M')}

SYNTHÈSE GÉNÉRALE:
- Total des biens: {total_biens}
- Valeur totale: {valeur_totale:,.2f} €
- Valeur moyenne: {valeur_totale/total_biens if total_biens > 0 else 0:,.2f} €

RÉPARTITION PAR SECTEUR:"""

        for secteur, count, valeur in secteurs:
            rapport += f"\n- {secteur}: {count} biens, {valeur:,.2f} €"

        QMessageBox.information(self, "Rapport Complet", rapport)

    def generer_rapport_secteur(self):
        """Génère un rapport par secteur"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT secteur, COUNT(*) as nb_biens, SUM(valeur) as valeur_totale, AVG(valeur) as valeur_moyenne
            FROM immobilisations
            GROUP BY secteur
            ORDER BY valeur_totale DESC
        ''')

        secteurs = cursor.fetchall()

        rapport = f"""RAPPORT PAR SECTEUR

Date: {datetime.now().strftime('%d/%m/%Y %H:%M')}

ANALYSE PAR SECTEUR:"""

        for secteur, nb_biens, valeur_totale, valeur_moyenne in secteurs:
            rapport += f"""

{secteur.upper()}:
- Nombre de biens: {nb_biens}
- Valeur totale: {valeur_totale:,.2f} €
- Valeur moyenne: {valeur_moyenne:,.2f} €"""

        QMessageBox.information(self, "Rapport par Secteur", rapport)

    def generer_rapport_amortissement(self):
        """Génère un rapport d'amortissement"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT designation, valeur, duree_amortissement,
                   (valeur / duree_amortissement) as amort_annuel,
                   (julianday('now') - julianday(date_acquisition)) / 365.25 as age_annees
            FROM immobilisations
            WHERE duree_amortissement > 0
            ORDER BY valeur DESC
        ''')

        biens = cursor.fetchall()

        rapport = f"""RAPPORT D'AMORTISSEMENT

Date: {datetime.now().strftime('%d/%m/%Y %H:%M')}

CALCULS D'AMORTISSEMENT:"""

        for designation, valeur, _duree, amort_annuel, age in biens[:10]:  # Top 10
            valeur_nette = max(0, valeur - (amort_annuel * age))
            rapport += f"""

{designation}:
- Valeur acquisition: {valeur:,.2f} €
- Amortissement annuel: {amort_annuel:,.2f} €
- Âge: {age:.1f} ans
- Valeur nette comptable: {valeur_nette:,.2f} €"""

        QMessageBox.information(self, "Rapport d'Amortissement", rapport)

    def create_settings_tab(self):
        """Crée l'onglet des paramètres"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres généraux
        general_group = QGroupBox("⚙️ Paramètres Généraux")
        general_layout = QFormLayout(general_group)

        # Thème actuel
        theme_label = QLabel(f"<b>{self.current_color_scheme['name']}</b> - Changement automatique toutes les 30s")
        general_layout.addRow("Thème actuel:", theme_label)

        # Performance
        perf_label = QLabel("Profil 'high_end' - Optimisé automatiquement pour votre machine")
        general_layout.addRow("Profil de performance:", perf_label)

        # Base de données
        db_label = QLabel(f"SQLite - {self.db_path}")
        general_layout.addRow("Base de données:", db_label)

        layout.addWidget(general_group)

        # Actions système
        actions_group = QGroupBox("[OUTIL] Actions Système")
        actions_layout = QVBoxLayout(actions_group)

        btn_backup = QPushButton("💾 Créer une Sauvegarde")
        btn_backup.clicked.connect(self.creer_sauvegarde)
        actions_layout.addWidget(btn_backup)

        btn_optimize = QPushButton("⚡ Optimiser la Base de Données")
        btn_optimize.clicked.connect(self.optimiser_db)
        actions_layout.addWidget(btn_optimize)

        btn_about = QPushButton("ℹ️ À Propos")
        btn_about.clicked.connect(self.show_about)
        actions_layout.addWidget(btn_about)

        layout.addWidget(actions_group)

        # Informations système
        info_group = QGroupBox("[STATS] Informations Système")
        info_layout = QFormLayout(info_group)

        import platform
        import psutil

        info_layout.addRow("Système:", QLabel(f"{platform.system()} {platform.release()}"))
        info_layout.addRow("Python:", QLabel(f"{platform.python_version()}"))
        info_layout.addRow("RAM totale:", QLabel(f"{psutil.virtual_memory().total / (1024**3):.1f} GB"))
        info_layout.addRow("CPU:", QLabel(f"{psutil.cpu_count()} cœurs"))

        layout.addWidget(info_group)

        layout.addStretch()

        return widget

    def creer_sauvegarde(self):
        """Crée une sauvegarde de la base de données"""
        import shutil
        backup_name = f"gestimmob_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        try:
            shutil.copy2(self.db_path, backup_name)
            QMessageBox.information(self, "Sauvegarde", f"Sauvegarde créée: {backup_name}")
            self.status_bar.showMessage(f"Sauvegarde: {backup_name}", 5000)
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def optimiser_db(self):
        """Optimise la base de données"""
        cursor = self.conn.cursor()
        cursor.execute("VACUUM")
        self.conn.commit()
        QMessageBox.information(self, "Optimisation", "Base de données optimisée avec succès.")

    def show_about(self):
        """Affiche les informations sur l'application"""
        QMessageBox.about(self, "À propos de GestImmob",
                         """<h2>🏠 GestImmob v2.0.0 Complet</h2>
                         <p><b>Logiciel de Gestion Immobilière Professionnel</b></p>

                         <h3>[LANCE] Fonctionnalités Complètes:</h3>
                         <ul>
                         <li>📋 Gestion complète des biens immobiliers</li>
                         <li>📦 Inventaire avec scanner code-barres</li>
                         <li>🏢 Gestion des fournisseurs</li>
                         <li>💼 Module ERP/Comptabilité complet</li>
                         <li>🧮 Calculatrice financière avancée</li>
                         <li>🔍 Recherche et filtres intelligents</li>
                         <li>♻️ Gestion des réformes</li>
                         <li>📄 Gestion documentaire</li>
                         <li>🖨️ Impression multi-formats</li>
                         <li>[STATS] Rapports et analyses</li>
                         </ul>

                         <h3>🎨 Interface Moderne:</h3>
                         <ul>
                         <li>🌈 Thèmes dynamiques avec couleurs changeantes</li>
                         <li>🎭 Logo animé et interface fluide</li>
                         <li>⚡ Optimisé pour machines performantes</li>
                         <li>📱 Design responsive et moderne</li>
                         </ul>

                         <p><i>Développé avec Python 3.13 et PySide6</i></p>
                         <p><b>Tous les modules sont opérationnels !</b></p>""")

    def create_menu(self):
        """Crée le menu principal complet"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        new_action = QAction("➕ Nouveau bien", self)
        new_action.triggered.connect(self.ajouter_bien)
        file_menu.addAction(new_action)

        file_menu.addSeparator()

        export_action = QAction("📤 Exporter Excel", self)
        export_action.triggered.connect(self.exporter_excel)
        file_menu.addAction(export_action)

        backup_action = QAction("💾 Sauvegarde", self)
        backup_action.triggered.connect(self.creer_sauvegarde)
        file_menu.addAction(backup_action)

        file_menu.addSeparator()

        quit_action = QAction("🚪 Quitter", self)
        quit_action.triggered.connect(self.close)
        file_menu.addAction(quit_action)

        # Menu Modules
        modules_menu = menubar.addMenu("[OUTIL] Modules")

        inventaire_action = QAction("📦 Inventaire", self)
        inventaire_action.triggered.connect(lambda: self.tabs.setCurrentIndex(1))
        modules_menu.addAction(inventaire_action)

        fournisseurs_action = QAction("🏢 Fournisseurs", self)
        fournisseurs_action.triggered.connect(lambda: self.tabs.setCurrentIndex(2))
        modules_menu.addAction(fournisseurs_action)

        erp_action = QAction("💼 ERP/Comptabilité", self)
        erp_action.triggered.connect(lambda: self.tabs.setCurrentIndex(3))
        modules_menu.addAction(erp_action)

        calc_action = QAction("🧮 Calculatrice", self)
        calc_action.triggered.connect(lambda: self.tabs.setCurrentIndex(4))
        modules_menu.addAction(calc_action)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = QAction("ℹ️ À propos", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName("GestImmob Complet")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("GestImmob Solutions")

    # Créer et afficher la fenêtre principale
    window = GestImmobComplet()
    window.show()

    # Lancer la boucle d'événements
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
