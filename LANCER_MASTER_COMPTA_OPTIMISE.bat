@echo off
title MASTER COMPTA GÉNÉRAL - Interface Optimisée
color 0F

echo.
echo ========================================================================
echo                🏢 MASTER COMPTA GÉNÉRAL OPTIMISÉ 🏢
echo           Interface avec Boutons Verticaux et Météo
echo      Boutons Minimisés • Date/Heure Agrandies • Stickers • Mémos
echo        Zone Photo Logiciel • Météo 15 Jours • Pas de Fenêtres
echo ========================================================================
echo.

echo [%TIME%] Lancement de MASTER COMPTA GÉNÉRAL Optimisé...

REM Variables
set "PYTHON_CMD="
set "INTERFACE_PRETE=0"

echo.
echo 🔍 DETECTION PYTHON...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo ✅ Python detecté: %%i
    goto :verifier_interface
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python detecté: %%i
    goto :verifier_interface
)

echo ❌ Python non detecté
goto :end

:verifier_interface
echo.
echo 🔍 VERIFICATION DE L'INTERFACE OPTIMISÉE...

if exist "master_compta_interface_entree.py" (
    echo ✅ Interface optimisée trouvée
    set /a INTERFACE_PRETE=1
) else (
    echo ❌ Interface optimisée non trouvée
    goto :end
)

echo.
echo 📦 VERIFICATION DEPENDANCES...

%PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul
if errorlevel 1 (
    echo ❌ PySide6 manquant - Installation...
    %PYTHON_CMD% -m pip install PySide6 --quiet
    if not errorlevel 1 (
        echo ✅ PySide6 installé
    )
) else (
    echo ✅ PySide6 OK
)

echo.
echo ========================================================================
echo              🚀 LANCEMENT MASTER COMPTA GÉNÉRAL OPTIMISÉ 🚀
echo ========================================================================
echo.

echo [%TIME%] Démarrage de l'interface optimisée...
echo.

echo 🏢 MASTER COMPTA GÉNÉRAL - INTERFACE OPTIMISÉE:
echo.

echo 🔘 BOUTONS VERTICAUX MINIMISÉS À GAUCHE:
echo   🚀 LANCER MASTER COMPTA
echo   ⚙️ PARAMÈTRES COMPLETS
echo   🗃️ BASES DE DONNÉES
echo   🎨 PERSONNALISATION
echo   🌍 LANGUES
echo   📊 RAPPORTS
echo   🔒 SÉCURITÉ
echo   💾 SAUVEGARDE
echo   ❌ QUITTER
echo.

echo 📅 DATE/HEURE AGRANDIES AVEC CARACTÈRES GRAS:
echo   📅 Date: Format complet avec jour de la semaine
echo   🕐 Heure: Affichage 28pt en gras
echo   🎨 Style: Fond doré avec texte noir
echo   ⏰ Mise à jour: Temps réel chaque seconde
echo.

echo 🌤️ MÉTÉO 15 JOURS INTÉGRÉE:
echo   📅 Prévisions: 15 jours complets
echo   🌡️ Températures: Affichage par jour
echo   ☀️ Icônes: Météo visuelle
echo   📱 Scroll: Navigation horizontale
echo.

echo 📝 STICKERS DE RAPPEL:
echo   🔔 Rappels: Réunions et tâches
echo   📋 Notifications: Importantes
echo   🎨 Style: Rouge avec texte blanc
echo   ⚡ Mise à jour: Dynamique
echo.

echo 📋 MÉMOS RAPIDES:
echo   💡 Notes: Idées et informations
echo   🔧 Maintenance: Alertes système
echo   📊 Statistiques: Données importantes
echo   🎨 Style: Vert avec texte blanc
echo.

echo 🖼️ ZONE PHOTO LOGICIEL ADAPTÉE:
echo   📸 Taille: 350x300 pixels
echo   🏢 Contenu: Photo du logiciel
echo   🎨 Style: Bordures dorées
echo   ℹ️ Informations: Fonctionnalités du logiciel
echo.

echo 🚫 PAS DE FENÊTRES SUPPLÉMENTAIRES:
echo   ✅ Boutons: Affichent des messages dans la console
echo   🖥️ Interface: Une seule fenêtre principale
echo   📱 Navigation: Fluide et simple
echo   ⚡ Performance: Optimisée
echo.

echo Configuration:
echo   Python: %PYTHON_CMD%
echo   Interface: master_compta_interface_entree.py
echo   Style: Boutons verticaux minimisés
echo   Date/Heure: Agrandies avec caractères gras
echo   Météo: 15 jours intégrée
echo   Stickers: Rappels et mémos
echo   Photo: Zone adaptée au logiciel
echo.

echo 🎯 Lancement en cours...
echo.

REM Lancement de l'interface optimisée
%PYTHON_CMD% master_compta_interface_entree.py

set "EXIT_CODE=%ERRORLEVEL%"

echo.
echo ========================================================================
echo                              RESULTAT FINAL
echo ========================================================================
echo.

if %EXIT_CODE%==0 (
    echo [%TIME%] ✅ MASTER COMPTA GÉNÉRAL FERMÉ NORMALEMENT
    echo.
    echo 🎉 LANCEMENT RÉUSSI !
    echo ✅ Interface optimisée opérationnelle
    echo ✅ Boutons verticaux minimisés à gauche
    echo ✅ Date et heure agrandies avec caractères gras
    echo ✅ Météo 15 jours intégrée
    echo ✅ Stickers de rappel fonctionnels
    echo ✅ Mémos rapides intégrés
    echo ✅ Zone photo logiciel adaptée
    echo ✅ Pas de fenêtres supplémentaires
    echo.
    echo 🎯 FONCTIONNALITÉS TESTÉES:
    echo   ✅ Boutons: Verticaux avec appellations agrandies
    echo   ✅ Date/Heure: 24pt et 28pt en gras
    echo   ✅ Météo: 15 jours avec icônes
    echo   ✅ Stickers: Rappels en rouge
    echo   ✅ Mémos: Notes en vert
    echo   ✅ Photo: Zone 350x300 pour le logiciel
    echo   ✅ Navigation: Une seule interface
    echo.
    echo 🔘 BOUTONS OPTIMISÉS:
    echo   ✅ Taille: Minimisés (60px hauteur)
    echo   ✅ Position: Verticaux à gauche
    echo   ✅ Appellations: Agrandies et visibles
    echo   ✅ Style: Noir avec hover doré
    echo   ✅ Fonctions: Messages console (pas de fenêtres)
    echo.
    echo 📅 DATE/HEURE AMÉLIORÉES:
    echo   ✅ Date: Format complet avec jour
    echo   ✅ Heure: 28pt en caractères gras
    echo   ✅ Style: Fond doré avec texte noir
    echo   ✅ Position: Zone centrale visible
    echo.
    echo 🌤️ MÉTÉO ET WIDGETS:
    echo   ✅ Météo: 15 jours avec scroll horizontal
    echo   ✅ Stickers: Zone rouge pour rappels
    echo   ✅ Mémos: Zone verte pour notes
    echo   ✅ Mise à jour: Temps réel
    echo.
    echo 🖼️ ZONE PHOTO LOGICIEL:
    echo   ✅ Taille: 350x300 pixels optimale
    echo   ✅ Position: Colonne droite
    echo   ✅ Contenu: Adapté au logiciel
    echo   ✅ Style: Bordures dorées élégantes
    echo.
    echo 💡 Pour relancer: exécutez à nouveau ce script
    echo    Ou tapez: %PYTHON_CMD% master_compta_interface_entree.py
    echo.
) else (
    echo [%TIME%] ❌ INTERFACE FERMÉE AVEC ERREUR (Code: %EXIT_CODE%)
    echo.
    echo 🔧 DIAGNOSTIC:
    
    %PYTHON_CMD% -c "print('✅ Python de base: OK')" 2>nul || echo ❌ Python de base: Problème
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul || echo ❌ PySide6: Problème
    
    echo.
    echo 💡 SOLUTIONS:
    echo 1. Relancez ce script
    echo 2. Installez PySide6: pip install PySide6
    echo 3. Vérifiez les erreurs ci-dessus
)

:end
echo.
echo ========================================================================
echo                              RESUME
echo ========================================================================
echo.

echo [%TIME%] Script de lancement terminé

if %EXIT_CODE%==0 (
    echo.
    echo 🏆 SUCCÈS TOTAL !
    echo ✅ MASTER COMPTA GÉNÉRAL interface optimisée lancée parfaitement
    echo ✅ Boutons verticaux minimisés avec appellations agrandies
    echo ✅ Date et heure agrandies avec caractères gras
    echo ✅ Météo 15 jours intégrée avec icônes
    echo ✅ Stickers de rappel et mémos fonctionnels
    echo ✅ Zone photo logiciel adaptée 350x300
    echo ✅ Interface unique sans fenêtres supplémentaires
    echo ✅ Navigation fluide et optimisée
    echo.
    echo 🎯 MASTER COMPTA GÉNÉRAL INTERFACE OPTIMISÉE OPÉRATIONNELLE !
    echo.
    echo 🔘 OPTIMISATIONS RÉUSSIES:
    echo   ✅ Boutons: Minimisés et verticaux à gauche
    echo   ✅ Appellations: Agrandies et lisibles
    echo   ✅ Date/Heure: 24pt/28pt en gras
    echo   ✅ Météo: 15 jours avec scroll
    echo   ✅ Widgets: Stickers et mémos intégrés
    echo   ✅ Photo: Zone adaptée au logiciel
    echo   ✅ Interface: Une seule fenêtre
    echo.
    echo 📱 DESIGN MODERNE:
    echo   ✅ Layout: 3 colonnes optimisées
    echo   ✅ Couleurs: Noir avec dorés
    echo   ✅ Typographie: Caractères gras
    echo   ✅ Widgets: Météo et mémos
    echo   ✅ Navigation: Intuitive
    echo.
) else (
    echo.
    echo ⚠️ PROBLÈME DE LANCEMENT
    echo.
    echo 📋 VÉRIFICATIONS EFFECTUÉES:
    echo   - Python: %PYTHON_CMD%
    echo   - Interface: %INTERFACE_PRETE%
    echo   - Dépendances: Vérifiées
    echo.
    echo 🔧 ACTIONS RECOMMANDÉES:
    echo 1. Relancez ce script
    echo 2. Installez manuellement: pip install PySide6
    echo 3. Vérifiez les messages d'erreur
)

echo.
echo 🏢 MASTER COMPTA GÉNÉRAL OPTIMISÉ
echo Interface avec boutons verticaux • Date/heure agrandies • Météo 15 jours
echo Stickers rappels • Mémos • Zone photo logiciel • Navigation optimisée
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
