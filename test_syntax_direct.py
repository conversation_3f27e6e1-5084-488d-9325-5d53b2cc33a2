#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test direct de la syntaxe des fichiers Python
"""

import ast
import os

def test_file_syntax(file_path):
    """Test direct de la syntaxe d'un fichier"""
    print(f"\n🔍 Test de {file_path}")
    print("-" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier non trouvé: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier lu: {len(content)} caractères")
        
        # Test AST
        try:
            ast.parse(content, filename=file_path)
            print(f"✅ AST: OK")
        except SyntaxError as e:
            print(f"❌ AST ERREUR ligne {e.lineno}: {e.msg}")
            if e.text:
                print(f"   Code: {e.text.strip()}")
            return False
        
        # Test compilation
        try:
            compile(content, file_path, 'exec')
            print(f"✅ Compilation: OK")
        except SyntaxError as e:
            print(f"❌ COMPILATION ERREUR ligne {e.lineno}: {e.msg}")
            return False
        
        print(f"✅ FICHIER PARFAIT")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Test de tous les fichiers critiques"""
    print("🔍 TEST DIRECT DE LA SYNTAXE")
    print("=" * 60)
    
    files = [
        'src/main.py',
        'src/config.py',
        'src/database/db_connection.py',
        'src/database/models.py',
        'src/views/admin_view.py',
        'src/auth/user_management.py'
    ]
    
    results = {}
    
    for file_path in files:
        results[file_path] = test_file_syntax(file_path)
    
    # Résumé
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ")
    print(f"{'='*60}")
    
    valid_count = sum(results.values())
    total_count = len(results)
    
    print(f"✅ Fichiers valides: {valid_count}/{total_count}")
    print(f"❌ Fichiers avec erreurs: {total_count - valid_count}")
    
    for file_path, is_valid in results.items():
        status = "✅ VALIDE" if is_valid else "❌ ERREUR"
        print(f"  {status} {file_path}")
    
    if valid_count == total_count:
        print(f"\n🎉 TOUS LES FICHIERS SONT SYNTAXIQUEMENT CORRECTS!")
        return True
    else:
        print(f"\n🚨 {total_count - valid_count} FICHIER(S) AVEC DES ERREURS")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat: {'SUCCÈS' if success else 'ÉCHEC'}")
