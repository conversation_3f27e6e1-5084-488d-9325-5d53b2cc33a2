# -*- coding: utf-8 -*-
"""
Gestion du Parc Automobile - Module de gestion des véhicules et engins
Encodage: UTF-8
Auteur: Système de Gestion Immobilière
Version: 1.0.0
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QLabel, QFileDialog, QInputDialog
)
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QColor
import logging
import json
import csv
import os
from datetime import datetime

class ParcAutoWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Gestion du Parc Automobile et Engins")
        self.resize(1600, 900)
        layout = QVBoxLayout(self)
        # Table enrichie pour véhicules, camions, engins, flotte
        self.table = QTableWidget(0, 20)
        self.table.setHorizontalHeaderLabels([
            "ID", "Type", "Marque", "Modèle", "Immatriculation", "Année", "N° Série", "Catégorie",
            "Kilométrage", "Date dernier entretien", "Prochain entretien", "Statut", "Conducteur",
            "Date panne", "Type panne", "Description panne/anomalie", "Date réparation", "Coût entretien",
            "Disponibilité", "Observation"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter Véhicule/Engin")
        self.edit_btn = QPushButton("Modifier")
        self.delete_btn = QPushButton("Supprimer")
        self.maintenance_btn = QPushButton("Planifier Entretien")
        self.panne_btn = QPushButton("Signaler Panne/Anomalie")
        self.sync_btn = QPushButton("Synchroniser")
        self.chart_btn = QPushButton("Statistiques Parc")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.maintenance_btn)
        btn_layout.addWidget(self.panne_btn)
        btn_layout.addWidget(self.sync_btn)
        btn_layout.addWidget(self.chart_btn)
        layout.addLayout(btn_layout)

        self.add_btn.clicked.connect(self.ajouter_vehicule)
        self.edit_btn.clicked.connect(self.modifier_vehicule)
        self.delete_btn.clicked.connect(self.supprimer_vehicule)
        self.maintenance_btn.clicked.connect(self.planifier_entretien)
        self.panne_btn.clicked.connect(self.signaler_panne)
        self.sync_btn.clicked.connect(self.synchroniser)
        self.chart_btn.clicked.connect(self.afficher_stats)

        self.changements_detectes = False
        self.timer_sync = QTimer()
        self.timer_sync.timeout.connect(self.sauvegarde_auto)
        self.timer_sync.start(180000)  # 3 min

        # Chargement initial des données
        self.charger_donnees()

    def ajouter_vehicule(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter un véhicule/engin")
        form = QFormLayout()
        type_veh = QComboBox(); type_veh.addItems(["Voiture", "Camion", "Engin", "Bus", "Moto", "Utilitaire", "Autre"])
        marque = QLineEdit()
        modele = QLineEdit()
        immatriculation = QLineEdit()
        annee = QLineEdit()
        num_serie = QLineEdit()
        categorie = QComboBox(); categorie.addItems(["Léger", "Lourd", "Spécial", "Transport", "Service", "Autre"])
        kilometrage = QLineEdit()
        date_dernier_entretien = QLineEdit()
        prochain_entretien = QLineEdit()
        statut = QComboBox(); statut.addItems(["Disponible", "En service", "En panne", "En maintenance", "Réformé"])
        conducteur = QLineEdit()
        date_panne = QLineEdit()
        type_panne = QComboBox(); type_panne.addItems(["Mécanique", "Électrique", "Carrosserie", "Pneumatique", "Autre"])
        desc_panne = QLineEdit()
        date_reparation = QLineEdit()
        cout_entretien = QLineEdit()
        disponibilite = QComboBox(); disponibilite.addItems(["Oui", "Non", "Réservé"])
        observation = QLineEdit()
        # Critères enrichis
        rapport_detaille = QComboBox(); rapport_detaille.addItems([
            "Rapport technique", "Rapport d'entretien", "Rapport d'accident", "Rapport de mission", "Rapport de consommation", "Rapport assurance", "Rapport GPS", "Rapport carburant", "Autre"
        ])
        amortissement = QComboBox(); amortissement.addItems([
            "Amorti", "Non amorti", "En cours", "À calculer", "Calcul automatique", "Selon base de données"
        ])
        constat = QComboBox(); constat.addItems([
            "RAS", "Anomalie détectée", "Entretien à prévoir", "Panne récurrente", "Usure pneus", "Fuite huile", "Freinage", "Éclairage", "Carrosserie", "Autre"
        ])
        photo_format = QComboBox(); photo_format.addItems([
            "Aucune", "JPG", "PNG", "JPEG", "PDF", "BMP", "TIFF", "GIF", "Autre format"
        ])
        obs = QLineEdit()
        km_depart = QLineEdit()
        km_arrivee = QLineEdit()
        calcul_km_btn = QPushButton("Calculer km parcourus")
        km_result = QLineEdit(); km_result.setReadOnly(True)
        vidange = QComboBox(); vidange.addItems([
            "À jour", "À prévoir", "Urgent", "Non renseigné", "Vidange partielle", "Vidange totale"
        ])
        pression_pneus = QComboBox(); pression_pneus.addItems([
            "Normale", "Basse", "Haute", "À contrôler", "Avant OK", "Arrière OK", "Tous à contrôler"
        ])
        chauffeur_titulaire = QComboBox(); chauffeur_titulaire.addItems([
            "Aucun", "Titulaire 1", "Titulaire 2", "Titulaire 3", "Titulaire 4", "Titulaire 5", "Autre"
        ])
        chauffeur_suppleant = QComboBox(); chauffeur_suppleant.addItems([
            "Aucun", "Suppléant 1", "Suppléant 2", "Suppléant 3", "Suppléant 4", "Suppléant 5", "Autre"
        ])
        carburant = QComboBox(); carburant.addItems([
            "Diesel", "Essence", "GPL", "Électrique", "Hybride", "Autre"
        ])
        form.addRow("Type carburant:", carburant)
        # ...existing code...
        gps = QComboBox(); gps.addItems([
            "Oui", "Non", "À installer", "Défectueux", "Localisation active", "Localisation inactive", "En maintenance"
        ])
        gps_fournisseur = QComboBox(); gps_fournisseur.addItems([
            "Aucun", "TomTom", "Coyote", "Wialon", "Fleet Complete", "Geotab", "Autre"
        ])
        gps_num_serie = QLineEdit()
        gps_date_install = QLineEdit()
        gps_derniere_position = QLineEdit()
        gps_latitude = QLineEdit()
        gps_longitude = QLineEdit()
        gps_precision = QComboBox(); gps_precision.addItems([
            "Haute", "Moyenne", "Faible", "Non renseigné"
        ])
        gps_carte = QComboBox(); gps_carte.addItems([
            "Google Maps", "OpenStreetMap", "Here", "Mapbox", "Autre"
        ])
        gps_zone_geo = QLineEdit()
        gps_pays = QComboBox(); gps_pays.addItems([
            "France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "Allemagne", "Belgique", "Suisse", "Autre"
        ])
        gps_balise = QComboBox(); gps_balise.addItems([
            "Aucune", "Teltonika", "Queclink", "Calamp", "Suntech", "Concox", "Ruptela", "Autre"
        ])
        vitesse = QLineEdit()
        temperature_moteur = QLineEdit()
        voix_passagers = QComboBox(); voix_passagers.addItems([
            "Non disponible", "Enregistrement actif", "Enregistrement désactivé", "Micro défectueux", "Disponible"
        ])
        # Assurance et carte grise
        assurance = QComboBox(); assurance.addItems([
            "À jour", "À renouveler", "Expirée", "En cours", "Non renseigné"
        ])
        carte_grise = QComboBox(); carte_grise.addItems([
            "Disponible", "Manquante", "En cours", "Non renseigné"
        ])
        # Synchronisation avec la base de données et paramètres logiciel
        btn_sync_db = QPushButton("Synchroniser avec la base de données")
        btn_param_logiciel = QPushButton("Paramètres logiciel")
        def sync_db():
            # À adapter selon votre backend (exemple : SQLite, MySQL, API REST, etc.)
            QMessageBox.information(dialog, "Synchronisation", "Synchronisation avec la base de données effectuée (à implémenter selon votre backend).")
        def ouvrir_param():
            # À adapter pour ouvrir une fenêtre de configuration des paramètres
            QMessageBox.information(dialog, "Paramètres", "Ouverture des paramètres du logiciel (à implémenter).")
        btn_sync_db.clicked.connect(sync_db)
        btn_param_logiciel.clicked.connect(ouvrir_param)
        form.addRow("Type:", type_veh)
        form.addRow("Marque:", marque)
        form.addRow("Modèle:", modele)
        form.addRow("Immatriculation:", immatriculation)
        form.addRow("Année:", annee)
        form.addRow("N° Série:", num_serie)
        form.addRow("Catégorie:", categorie)
        form.addRow("Kilométrage:", kilometrage)
        form.addRow("Date dernier entretien:", date_dernier_entretien)
        form.addRow("Prochain entretien:", prochain_entretien)
        form.addRow("Statut:", statut)
        form.addRow("Conducteur:", conducteur)
        form.addRow("Date panne:", date_panne)
        form.addRow("Type panne:", type_panne)
        form.addRow("Description panne/anomalie:", desc_panne)
        form.addRow("Date réparation:", date_reparation)
        form.addRow("Coût entretien:", cout_entretien)
        form.addRow("Disponibilité:", disponibilite)
        form.addRow("Observation:", observation)
        form.addRow("Rapport détaillé:", rapport_detaille)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Constat:", constat)
        form.addRow("Photo (format):", photo_format)
        form.addRow("Observation complémentaire:", obs)
        form.addRow("Km départ:", km_depart)
        form.addRow("Km arrivée:", km_arrivee)
        form.addRow("", calcul_km_btn)
        form.addRow("Km parcourus:", km_result)
        form.addRow("Vidange huile:", vidange)
        form.addRow("Pression pneus:", pression_pneus)
        form.addRow("Chauffeur titulaire:", chauffeur_titulaire)
        form.addRow("Chauffeur suppléant:", chauffeur_suppleant)
        form.addRow("Type carburant:", carburant)
        form.addRow("GPS:", gps)
        form.addRow("Fournisseur GPS:", gps_fournisseur)
        form.addRow("N° série GPS:", gps_num_serie)
        form.addRow("Date installation GPS:", gps_date_install)
        form.addRow("Dernière position GPS:", gps_derniere_position)
        form.addRow("Latitude GPS:", gps_latitude)
        form.addRow("Longitude GPS:", gps_longitude)
        form.addRow("Précision GPS:", gps_precision)
        form.addRow("Carte utilisée:", gps_carte)
        form.addRow("Zone géographique:", gps_zone_geo)
        form.addRow("Pays:", gps_pays)
        form.addRow("Balise GPS:", gps_balise)
        form.addRow("Vitesse (km/h):", vitesse)
        form.addRow("Température moteur (°C):", temperature_moteur)
        form.addRow("Voix passagers:", voix_passagers)
        form.addRow("Assurance:", assurance)
        form.addRow("Carte grise:", carte_grise)
        # Synchronisation avec la base de données et paramètres logiciel
        form.addRow("", btn_sync_db)
        form.addRow("", btn_param_logiciel)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        def calculer_km():
            try:
                d = float(km_depart.text())
                a = float(km_arrivee.text())
                km_result.setText(str(a - d))
            except Exception:
                km_result.setText("Erreur")
        calcul_km_btn.clicked.connect(calculer_km)
        if dialog.exec() == 1:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
            self.table.setItem(row, 1, QTableWidgetItem(type_veh.currentText()))
            self.table.setItem(row, 2, QTableWidgetItem(marque.text()))
            self.table.setItem(row, 3, QTableWidgetItem(modele.text()))
            self.table.setItem(row, 4, QTableWidgetItem(immatriculation.text()))
            self.table.setItem(row, 5, QTableWidgetItem(annee.text()))
            self.table.setItem(row, 6, QTableWidgetItem(num_serie.text()))
            self.table.setItem(row, 7, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 8, QTableWidgetItem(kilometrage.text()))
            self.table.setItem(row, 9, QTableWidgetItem(date_dernier_entretien.text()))
            self.table.setItem(row, 10, QTableWidgetItem(prochain_entretien.text()))
            self.table.setItem(row, 11, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 12, QTableWidgetItem(conducteur.text()))
            self.table.setItem(row, 13, QTableWidgetItem(date_panne.text()))
            self.table.setItem(row, 14, QTableWidgetItem(type_panne.currentText()))
            self.table.setItem(row, 15, QTableWidgetItem(desc_panne.text()))
            self.table.setItem(row, 16, QTableWidgetItem(date_reparation.text()))
            self.table.setItem(row, 17, QTableWidgetItem(cout_entretien.text()))
            self.table.setItem(row, 18, QTableWidgetItem(disponibilite.currentText()))
            self.table.setItem(row, 19, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 20, QTableWidgetItem(rapport_detaille.currentText()))
            self.table.setItem(row, 21, QTableWidgetItem(amortissement.currentText()))
            self.table.setItem(row, 22, QTableWidgetItem(constat.currentText()))
            self.table.setItem(row, 23, QTableWidgetItem(photo_format.currentText()))
            self.table.setItem(row, 24, QTableWidgetItem(obs.text()))
            self.table.setItem(row, 25, QTableWidgetItem(km_depart.text()))
            self.table.setItem(row, 26, QTableWidgetItem(km_arrivee.text()))
            self.table.setItem(row, 27, QTableWidgetItem(km_result.text()))
            self.table.setItem(row, 28, QTableWidgetItem(vidange.currentText()))
            self.table.setItem(row, 29, QTableWidgetItem(pression_pneus.currentText()))
            self.table.setItem(row, 30, QTableWidgetItem(chauffeur_titulaire.currentText()))
            self.table.setItem(row, 31, QTableWidgetItem(chauffeur_suppleant.currentText()))
            self.table.setItem(row, 32, QTableWidgetItem(carburant.currentText()))
            self.table.setItem(row, 33, QTableWidgetItem(gps.currentText()))
            self.table.setItem(row, 34, QTableWidgetItem(assurance.currentText()))
            self.table.setItem(row, 35, QTableWidgetItem(carte_grise.currentText()))
            self.table.setItem(row, 36, QTableWidgetItem(gps_fournisseur.currentText()))
            self.table.setItem(row, 37, QTableWidgetItem(gps_num_serie.text()))
            self.table.setItem(row, 38, QTableWidgetItem(gps_date_install.text()))
            self.table.setItem(row, 39, QTableWidgetItem(gps_derniere_position.text()))
            self.table.setItem(row, 40, QTableWidgetItem(gps_latitude.text()))
            self.table.setItem(row, 41, QTableWidgetItem(gps_longitude.text()))
            self.table.setItem(row, 42, QTableWidgetItem(gps_precision.currentText()))
            self.table.setItem(row, 43, QTableWidgetItem(gps_carte.currentText()))
            self.table.setItem(row, 44, QTableWidgetItem(gps_zone_geo.text()))
            self.table.setItem(row, 45, QTableWidgetItem(gps_pays.currentText()))
            self.table.setItem(row, 46, QTableWidgetItem(gps_balise.currentText()))
            self.table.setItem(row, 47, QTableWidgetItem(vitesse.text()))
            self.table.setItem(row, 48, QTableWidgetItem(temperature_moteur.text()))
            self.table.setItem(row, 49, QTableWidgetItem(voix_passagers.currentText()))
            col = self.table.columnCount()
            if col < 50:
                self.table.setColumnCount(50)
                self.table.setHorizontalHeaderLabels([
                    "ID", "Type", "Marque", "Modèle", "Immatriculation", "Année", "N° Série", "Catégorie",
                    "Kilométrage", "Date dernier entretien", "Prochain entretien", "Statut", "Conducteur",
                    "Date panne", "Type panne", "Description panne/anomalie", "Date réparation", "Coût entretien",
                    "Disponibilité", "Observation",
                    "Rapport détaillé", "Amortissement", "Constat", "Photo (format)", "Observation 2",
                    "Km départ", "Km arrivée", "Km parcourus", "Vidange huile", "Pression pneus",
                    "Chauffeur titulaire", "Chauffeur suppléant", "Type carburant", "GPS", "Assurance", "Carte grise",
                    "Fournisseur GPS", "N° série GPS", "Date installation GPS", "Dernière position GPS",
                    "Latitude GPS", "Longitude GPS", "Précision GPS", "Carte utilisée", "Zone géographique", "Pays",
                    "Balise GPS", "Vitesse (km/h)", "Température moteur (°C)", "Voix passagers"
                ])
            self.changements_detectes = True

    def modifier_vehicule(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier véhicule/engin")
        form = QFormLayout()
        def safe_text(item):
            if item is not None:
                text_attr = getattr(item, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            return str(v)
                    except Exception:
                        return ""
            return ""
        type_veh = QComboBox(); type_veh.addItems(["Voiture", "Camion", "Engin", "Bus", "Moto", "Utilitaire", "Autre"])
        type_veh.setCurrentText(str(safe_text(self.table.item(row, 1))))
        marque = QLineEdit(safe_text(self.table.item(row, 2)))
        modele = QLineEdit(safe_text(self.table.item(row, 3)))
        immatriculation = QLineEdit(safe_text(self.table.item(row, 4)))
        annee = QLineEdit(safe_text(self.table.item(row, 5)))
        num_serie = QLineEdit(safe_text(self.table.item(row, 6)))
        categorie = QComboBox(); categorie.addItems(["Léger", "Lourd", "Spécial", "Transport", "Service", "Autre"])
        categorie.setCurrentText(str(safe_text(self.table.item(row, 7))))
        kilometrage = QLineEdit(safe_text(self.table.item(row, 8)))
        date_dernier_entretien = QLineEdit(safe_text(self.table.item(row, 9)))
        prochain_entretien = QLineEdit(safe_text(self.table.item(row, 10)))
        statut = QComboBox(); statut.addItems(["Disponible", "En service", "En panne", "En maintenance", "Réformé"])
        statut.setCurrentText(str(safe_text(self.table.item(row, 11))))
        conducteur = QLineEdit(safe_text(self.table.item(row, 12)))
        date_panne = QLineEdit(safe_text(self.table.item(row, 13)))
        type_panne = QComboBox(); type_panne.addItems(["Mécanique", "Électrique", "Carrosserie", "Pneumatique", "Autre"])
        type_panne.setCurrentText(str(safe_text(self.table.item(row, 14))))
        desc_panne = QLineEdit(safe_text(self.table.item(row, 15)))
        date_reparation = QLineEdit(safe_text(self.table.item(row, 16)))
        cout_entretien = QLineEdit(safe_text(self.table.item(row, 17)))
        disponibilite = QComboBox(); disponibilite.addItems(["Oui", "Non", "Réservé"])
        disponibilite.setCurrentText(str(safe_text(self.table.item(row, 18))))
        observation = QLineEdit(safe_text(self.table.item(row, 19)))
        rapport_detaille = QComboBox(); rapport_detaille.addItems([
            "Rapport technique", "Rapport d'entretien", "Rapport d'accident", "Rapport de mission", "Rapport de consommation", "Rapport assurance", "Rapport GPS", "Rapport carburant", "Autre"
        ])
        rapport_detaille.setCurrentText(str(safe_text(self.table.item(row, 20))))
        amortissement = QComboBox(); amortissement.addItems([
            "Amorti", "Non amorti", "En cours", "À calculer", "Calcul automatique", "Selon base de données"
        ])
        amortissement.setCurrentText(str(safe_text(self.table.item(row, 21))))
        constat = QComboBox(); constat.addItems([
            "RAS", "Anomalie détectée", "Entretien à prévoir", "Panne récurrente", "Usure pneus", "Fuite huile", "Freinage", "Éclairage", "Carrosserie", "Autre"
        ])
        constat.setCurrentText(str(safe_text(self.table.item(row, 22))))
        photo_format = QComboBox(); photo_format.addItems([
            "Aucune", "JPG", "PNG", "JPEG", "PDF", "BMP", "TIFF", "GIF", "Autre format"
        ])
        photo_format.setCurrentText(str(safe_text(self.table.item(row, 23))))
        obs = QLineEdit(safe_text(self.table.item(row, 24)))
        km_parcourus = QLineEdit(safe_text(self.table.item(row, 25)))
        vidange = QComboBox(); vidange.addItems([
            "À jour", "À prévoir", "Urgent", "Non renseigné", "Vidange partielle", "Vidange totale"
        ])
        vidange.setCurrentText(str(safe_text(self.table.item(row, 26))))
        pression_pneus = QComboBox(); pression_pneus.addItems([
            "Normale", "Basse", "Haute", "À contrôler", "Avant OK", "Arrière OK", "Tous à contrôler"
        ])
        pression_pneus.setCurrentText(str(safe_text(self.table.item(row, 27))))
        chauffeur_titulaire = QComboBox(); chauffeur_titulaire.addItems([
            "Aucun", "Titulaire 1", "Titulaire 2", "Titulaire 3", "Titulaire 4", "Titulaire 5", "Autre"
        ])
        chauffeur_titulaire.setCurrentText(str(safe_text(self.table.item(row, 28))))
        chauffeur_suppleant = QComboBox(); chauffeur_suppleant.addItems([
            "Aucun", "Suppléant 1", "Suppléant 2", "Suppléant 3", "Suppléant 4", "Suppléant 5", "Autre"
        ])
        chauffeur_suppleant.setCurrentText(str(safe_text(self.table.item(row, 29))))

        # Carburant
        carburant = QComboBox(); carburant.addItems([
            "Diesel", "Essence", "GPL", "Électrique", "Hybride", "Autre"
        ])
        carburant.setCurrentText(str(safe_text(self.table.item(row, 30))))

        # Assurance et carte grise
        assurance = QComboBox(); assurance.addItems([
            "À jour", "À renouveler", "Expirée", "En cours", "Non renseigné"
        ])
        assurance.setCurrentText(str(safe_text(self.table.item(row, 32))))

        carte_grise = QComboBox(); carte_grise.addItems([
            "Disponible", "Manquante", "En cours", "Non renseigné"
        ])
        carte_grise.setCurrentText(str(safe_text(self.table.item(row, 33))))

        # GPS
        gps = QComboBox(); gps.addItems([
            "Oui", "Non", "À installer", "Défectueux", "Localisation active", "Localisation inactive", "En maintenance"
        ])
        gps.setCurrentText(str(safe_text(self.table.item(row, 33))))
        gps_fournisseur = QComboBox(); gps_fournisseur.addItems([
            "Aucun", "TomTom", "Coyote", "Wialon", "Fleet Complete", "Geotab", "Autre"
        ])
        gps_fournisseur.setCurrentText(str(safe_text(self.table.item(row, 34))))
        gps_num_serie = QLineEdit(safe_text(self.table.item(row, 35)))
        gps_date_install = QLineEdit(safe_text(self.table.item(row, 36)))
        gps_derniere_position = QLineEdit(safe_text(self.table.item(row, 37)))
        gps_latitude = QLineEdit(safe_text(self.table.item(row, 38)))
        gps_longitude = QLineEdit(safe_text(self.table.item(row, 39)))
        gps_precision = QComboBox(); gps_precision.addItems([
            "Haute", "Moyenne", "Faible", "Non renseigné"
        ])
        gps_precision.setCurrentText(str(safe_text(self.table.item(row, 40))))
        gps_carte = QComboBox(); gps_carte.addItems([
            "Google Maps", "OpenStreetMap", "Here", "Mapbox", "Autre"
        ])
        gps_carte.setCurrentText(str(safe_text(self.table.item(row, 41))))
        gps_zone_geo = QLineEdit(safe_text(self.table.item(row, 42)))
        gps_pays = QComboBox(); gps_pays.addItems([
            "France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "Allemagne", "Belgique", "Suisse", "Autre"
        ])
        gps_pays.setCurrentText(str(safe_text(self.table.item(row, 43))))
        # Balise GPS, Vitesse, Température moteur, Voix passagers
        gps_balise = QComboBox(); gps_balise.addItems([
            "Aucune", "Teltonika", "Queclink", "Calamp", "Suntech", "Concox", "Ruptela", "Autre"
        ])
        vitesse = QLineEdit(safe_text(self.table.item(row, 46)))
        temperature_moteur = QLineEdit(safe_text(self.table.item(row, 47)))
        voix_passagers = QComboBox(); voix_passagers.addItems([
            "Non disponible", "Enregistrement actif", "Enregistrement désactivé", "Micro défectueux", "Disponible"
        ])
        voix_passagers.setCurrentText(str(safe_text(self.table.item(row, 48))))
        form.addRow("Type:", type_veh)
        form.addRow("Marque:", marque)
        form.addRow("Modèle:", modele)
        form.addRow("Immatriculation:", immatriculation)
        form.addRow("Année:", annee)
        form.addRow("N° Série:", num_serie)
        form.addRow("Catégorie:", categorie)
        form.addRow("Kilométrage:", kilometrage)
        form.addRow("Date dernier entretien:", date_dernier_entretien)
        form.addRow("Prochain entretien:", prochain_entretien)
        form.addRow("Statut:", statut)
        form.addRow("Conducteur:", conducteur)
        form.addRow("Date panne:", date_panne)
        form.addRow("Type panne:", type_panne)
        form.addRow("Description panne/anomalie:", desc_panne)
        form.addRow("Date réparation:", date_reparation)
        form.addRow("Coût entretien:", cout_entretien)
        form.addRow("Disponibilité:", disponibilite)
        form.addRow("Observation:", observation)
        form.addRow("Rapport détaillé:", rapport_detaille)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Constat:", constat)
        form.addRow("Photo (format):", photo_format)
        form.addRow("Observation:", obs)
        form.addRow("Km parcourus:", km_parcourus)
        form.addRow("Vidange huile:", vidange)
        form.addRow("Pression pneus:", pression_pneus)
        form.addRow("Chauffeur titulaire:", chauffeur_titulaire)
        form.addRow("Chauffeur suppléant:", chauffeur_suppleant)
        form.addRow("Type carburant:", carburant)
        form.addRow("GPS:", gps)
        form.addRow("Fournisseur GPS:", gps_fournisseur)
        form.addRow("N° série GPS:", gps_num_serie)
        form.addRow("Date installation GPS:", gps_date_install)
        form.addRow("Dernière position GPS:", gps_derniere_position)
        form.addRow("Latitude GPS:", gps_latitude)
        form.addRow("Longitude GPS:", gps_longitude)
        form.addRow("Précision GPS:", gps_precision)
        form.addRow("Carte utilisée:", gps_carte)
        form.addRow("Zone géographique:", gps_zone_geo)
        form.addRow("Pays:", gps_pays)
        form.addRow("Balise GPS:", gps_balise)
        form.addRow("Vitesse (km/h):", vitesse)
        form.addRow("Température moteur (°C):", temperature_moteur)
        form.addRow("Voix passagers:", voix_passagers)
        form.addRow("Assurance:", assurance)
        form.addRow("Carte grise:", carte_grise)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            self.table.setItem(row, 1, QTableWidgetItem(type_veh.currentText()))
            self.table.setItem(row, 2, QTableWidgetItem(marque.text()))
            self.table.setItem(row, 3, QTableWidgetItem(modele.text()))
            self.table.setItem(row, 4, QTableWidgetItem(immatriculation.text()))
            self.table.setItem(row, 5, QTableWidgetItem(annee.text()))
            self.table.setItem(row, 6, QTableWidgetItem(num_serie.text()))
            self.table.setItem(row, 7, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 8, QTableWidgetItem(kilometrage.text()))
            self.table.setItem(row, 9, QTableWidgetItem(date_dernier_entretien.text()))
            self.table.setItem(row, 10, QTableWidgetItem(prochain_entretien.text()))
            self.table.setItem(row, 11, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 12, QTableWidgetItem(conducteur.text()))
            self.table.setItem(row, 13, QTableWidgetItem(date_panne.text()))
            self.table.setItem(row, 14, QTableWidgetItem(type_panne.currentText()))
            self.table.setItem(row, 15, QTableWidgetItem(desc_panne.text()))
            self.table.setItem(row, 16, QTableWidgetItem(date_reparation.text()))
            self.table.setItem(row, 17, QTableWidgetItem(cout_entretien.text()))
            self.table.setItem(row, 18, QTableWidgetItem(disponibilite.currentText()))
            self.table.setItem(row, 19, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 20, QTableWidgetItem(rapport_detaille.currentText()))
            self.table.setItem(row, 21, QTableWidgetItem(amortissement.currentText()))
            self.table.setItem(row, 22, QTableWidgetItem(constat.currentText()))
            self.table.setItem(row, 23, QTableWidgetItem(photo_format.currentText()))
            self.table.setItem(row, 24, QTableWidgetItem(obs.text()))
            self.table.setItem(row, 25, QTableWidgetItem(km_parcourus.text()))
            self.table.setItem(row, 26, QTableWidgetItem(vidange.currentText()))
            self.table.setItem(row, 27, QTableWidgetItem(pression_pneus.currentText()))
            self.table.setItem(row, 28, QTableWidgetItem(chauffeur_titulaire.currentText()))
            self.table.setItem(row, 29, QTableWidgetItem(chauffeur_suppleant.currentText()))
            self.table.setItem(row, 30, QTableWidgetItem(carburant.currentText()))
            self.table.setItem(row, 31, QTableWidgetItem(gps.currentText()))
            self.table.setItem(row, 32, QTableWidgetItem(assurance.currentText()))
            self.table.setItem(row, 33, QTableWidgetItem(carte_grise.currentText()))
            self.table.setItem(row, 34, QTableWidgetItem(gps_fournisseur.currentText()))
            self.table.setItem(row, 35, QTableWidgetItem(gps_num_serie.text()))
            self.table.setItem(row, 36, QTableWidgetItem(gps_date_install.text()))
            self.table.setItem(row, 37, QTableWidgetItem(gps_derniere_position.text()))
            self.table.setItem(row, 38, QTableWidgetItem(gps_latitude.text()))
            self.table.setItem(row, 39, QTableWidgetItem(gps_longitude.text()))
            self.table.setItem(row, 40, QTableWidgetItem(gps_precision.currentText()))
            self.table.setItem(row, 41, QTableWidgetItem(gps_carte.currentText()))
            self.table.setItem(row, 42, QTableWidgetItem(gps_zone_geo.text()))
            self.table.setItem(row, 43, QTableWidgetItem(gps_pays.currentText()))
            self.table.setItem(row, 44, QTableWidgetItem(gps_balise.currentText()))
            self.table.setItem(row, 45, QTableWidgetItem(vitesse.text()))
            self.table.setItem(row, 46, QTableWidgetItem(temperature_moteur.text()))
            self.table.setItem(row, 47, QTableWidgetItem(voix_passagers.currentText()))
            self.changements_detectes = True

    def supprimer_vehicule(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à supprimer.")
            return
        row = selected[0].row()
        self.table.removeRow(row)
        self.changements_detectes = True

    def planifier_entretien(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Entretien", "Sélectionnez un véhicule à planifier.")
            return
        row = selected[0].row()
        prochain = QInputDialog.getText(self, "Planifier entretien", "Date du prochain entretien (AAAA-MM-JJ) :")
        if prochain[1]:
            self.table.setItem(row, 10, QTableWidgetItem(prochain[0]))
            self.table.setItem(row, 11, QTableWidgetItem("En maintenance"))
            self.changements_detectes = True

    def signaler_panne(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Panne", "Sélectionnez un véhicule à signaler en panne.")
            return
        row = selected[0].row()
        date_panne, ok = QInputDialog.getText(self, "Date panne", "Date de la panne (AAAA-MM-JJ) :")
        type_panne, ok2 = QInputDialog.getItem(self, "Type panne", "Type de panne :", ["Mécanique", "Électrique", "Carrosserie", "Pneumatique", "Autre"], 0, False)
        desc_panne, ok3 = QInputDialog.getText(self, "Description panne", "Description de la panne/anomalie :")
        if ok and ok2 and ok3:
            self.table.setItem(row, 13, QTableWidgetItem(date_panne))
            self.table.setItem(row, 14, QTableWidgetItem(type_panne))
            self.table.setItem(row, 15, QTableWidgetItem(desc_panne))
            self.table.setItem(row, 11, QTableWidgetItem("En panne"))
            self.changements_detectes = True

    def afficher_stats(self):
        try:
            import matplotlib.pyplot as plt
            from collections import defaultdict
            stats_type = defaultdict(int)
            stats_statut = defaultdict(int)
            stats_panne = defaultdict(int)
            for row in range(self.table.rowCount()):
                type_veh = self.table.item(row, 1).text() if self.table.item(row, 1) else ""
                statut = self.table.item(row, 11).text() if self.table.item(row, 11) else ""
                panne = self.table.item(row, 14).text() if self.table.item(row, 14) else ""
                stats_type[type_veh] += 1
                stats_statut[statut] += 1
                stats_panne[panne] += 1
            if stats_type:
                plt.figure(figsize=(8, 4))
                plt.bar(list(stats_type.keys()), list(stats_type.values()))
                plt.title("Répartition par type de véhicule/engin")
                plt.xlabel("Type")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Statut des véhicules/engins")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_panne:
                plt.figure(figsize=(8, 4))
                plt.bar(list(stats_panne.keys()), list(stats_panne.values()))
                plt.title("Types de pannes/anomalies")
                plt.xlabel("Type de panne")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
        except Exception as e:
            QMessageBox.warning(self, "Erreur graphique", f"Erreur lors de l'affichage des statistiques : {e}")

    def synchroniser(self):
        # ...logique de synchronisation multi-base ou API...
        QMessageBox.information(self, "Synchronisation", "Synchronisation du parc auto effectuée avec succès.")
        self.changements_detectes = False

    def sauvegarde_auto(self):
        """Sauvegarde automatique des données avec encodage UTF-8"""
        if self.changements_detectes:
            try:
                # Sauvegarde en JSON avec encodage UTF-8
                data = []
                for row in range(self.table.rowCount()):
                    row_data = {}
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        header = self.table.horizontalHeaderItem(col)
                        if header and item:
                            row_data[header.text()] = item.text()
                    data.append(row_data)

                filename = f"parc_auto_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                logging.info(f"Sauvegarde automatique du parc auto: {filename}")
                self.changements_detectes = False
            except Exception as e:
                logging.error(f"Erreur lors de la sauvegarde: {e}")

    def exporter_csv(self):
        """Exporte les données en CSV avec encodage UTF-8"""
        try:
            filename = f"parc_auto_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # En-têtes
                headers = []
                for col in range(self.table.columnCount()):
                    header = self.table.horizontalHeaderItem(col)
                    if header:
                        headers.append(header.text())
                writer.writerow(headers)

                # Données
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

            QMessageBox.information(self, "Export", f"Données exportées vers: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    def closeEvent(self, event):
        """Gestion de la fermeture de la fenêtre avec sauvegarde automatique"""
        if self.changements_detectes:
            self.sauvegarde_auto()
        event.accept()

    def charger_donnees(self):
        """Charge les données depuis un fichier JSON avec encodage UTF-8"""
        try:
            filename = "parc_auto_data.json"
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.table.setRowCount(len(data))
                for row_idx, row_data in enumerate(data):
                    for col_idx in range(self.table.columnCount()):
                        header = self.table.horizontalHeaderItem(col_idx)
                        if header and header.text() in row_data:
                            item = QTableWidgetItem(str(row_data[header.text()]))
                            self.table.setItem(row_idx, col_idx, item)

                logging.info(f"Données chargées depuis: {filename}")
        except Exception as e:
            logging.error(f"Erreur lors du chargement: {e}")
            QMessageBox.warning(self, "Erreur", f"Erreur lors du chargement des données: {e}")
