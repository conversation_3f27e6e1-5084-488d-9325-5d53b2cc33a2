const vscode = require('vscode');

function activate(context) {
    console.log('🤖 Extension Robot IA SamNord activée');
    
    // Status bar
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "🤖 Robot IA SamNord";
    statusBarItem.tooltip = "Robot IA SamNord - SamNord@110577";
    statusBarItem.command = "robotIA.activate";
    statusBarItem.show();
    
    // Commande activation
    const activateCommand = vscode.commands.registerCommand('robotIA.activate', () => {
        vscode.window.showInformationMessage('🤖 Robot IA SamNord activé ! SamNord@110577');
        showRobotPanel();
    });
    
    // Tree provider
    const treeProvider = new RobotTreeProvider();
    vscode.window.registerTreeDataProvider('robotIA.main', treeProvider);
    
    context.subscriptions.push(activateCommand, statusBarItem);
    
    vscode.window.showInformationMessage('🤖 Extension Robot IA SamNord prête !');
}

function showRobotPanel() {
    const panel = vscode.window.createWebviewPanel(
        'robotIA',
        '🤖 Robot IA SamNord',
        vscode.ViewColumn.One,
        { enableScripts: true }
    );
    
    panel.webview.html = `
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { background: #1e1e1e; color: white; font-family: 'Segoe UI'; padding: 20px; }
            .header { text-align: center; border-bottom: 2px solid #007acc; padding-bottom: 15px; }
            .section { background: #2d2d30; padding: 15px; margin: 10px 0; border-radius: 8px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 Robot IA SamNord</h1>
            <p>👤 SamNord@110577 | 🔑 NorDine@22</p>
        </div>
        
        <div class="section">
            <h2>⚡ Extension VS Code Active</h2>
            <p>✅ Robot IA SamNord opérationnel dans VS Code</p>
            <p>✅ 57 modules disponibles</p>
            <p>✅ Interface comme Augment Agent</p>
        </div>
        
        <div class="section">
            <h2>📦 Modules Principaux</h2>
            <p>🧠 Intelligence IA Transcendante</p>
            <p>📡 WiFi Manager Avancé</p>
            <p>📱 Bluetooth Manager</p>
            <p>🛡️ Cybersécurité Éthique</p>
            <p>💼 Master Compta Général</p>
        </div>
    </body>
    </html>`;
}

class RobotTreeProvider {
    getTreeItem(element) {
        return element;
    }
    
    getChildren(element) {
        if (!element) {
            return [
                new vscode.TreeItem('🤖 Robot IA SamNord', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('👤 SamNord@110577', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📦 57 Modules', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('⚡ Status: Actif', vscode.TreeItemCollapsibleState.None)
            ];
        }
        return [];
    }
}

function deactivate() {}

module.exports = { activate, deactivate };
