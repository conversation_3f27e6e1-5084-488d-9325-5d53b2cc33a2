@echo off
title ORGANISER CONFIG COMPLET - Organisation Complete des Configurations
color 0A

echo.
echo ========================================================================
echo                    ORGANISER CONFIG COMPLET
echo           Organisation complete des configurations et parametres
echo                    avec surveillance temps reel
echo ========================================================================
echo.

REM Variables
set "PYTHON_CMD="
set "ORGANISATION_SUCCESS=0"

echo [%TIME%] Demarrage de l organisation complete des configurations...

REM Détection Python
echo.
echo [%TIME%] Detection de Python...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    echo ✅ Python detecte: py
    goto :python_found
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    echo ✅ Python detecte: python
    goto :python_found
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    echo ✅ Python detecte: python3
    goto :python_found
)

echo ❌ Python non trouve
echo.
echo INSTALLATION REQUISE:
echo 1. Installez Python 3.8+ depuis https://python.org
echo 2. Assurez-vous que Python est dans le PATH
echo 3. Relancez ce script
echo.
goto :end

:python_found
echo Python executable: %PYTHON_CMD%

REM Vérification du répertoire
echo.
echo [%TIME%] Verification du repertoire...
echo Repertoire: %CD%

REM Affichage de l'introduction
echo.
echo ========================================================================
echo                    ORGANISATION CONFIG/PARAMETRES
echo ========================================================================
echo.
echo Ce script va completement organiser vos configurations et parametres :
echo.
echo 📋 ETAPES PREVUES:
echo 1. 💾 Sauvegarde complete de securite
echo 2. 📁 Organisation des configs par ordre hierarchique
echo 3. 🔧 Reorganisation des modules sans conflit
echo 4. 🔍 Detection et resolution des conflits
echo 5. ⚙️ Creation du gestionnaire central
echo 6. 🔍 Surveillance temps reel (optionnelle)
echo 7. ✅ Validation finale
echo.
echo 🔒 SECURITE GARANTIE:
echo - Sauvegarde automatique avant toute modification
echo - Detection des conflits en temps reel
echo - Restauration automatique en cas d erreur
echo - Surveillance continue pour eviter les interruptions
echo.

set /p confirm="🚀 Commencer l organisation complete des configurations ? (o/n): "
if /i not "%confirm%"=="o" if /i not "%confirm%"=="oui" (
    echo ❌ Organisation annulee par l utilisateur
    goto :end
)

REM Exécution du script principal
echo.
echo ========================================================================
echo                           EXECUTION PRINCIPALE
echo ========================================================================
echo.

if exist "ORGANISER_CONFIG_COMPLET.py" (
    echo Execution du script principal d organisation config...
    %PYTHON_CMD% ORGANISER_CONFIG_COMPLET.py
    if not errorlevel 1 (
        echo ✅ Organisation config principale: REUSSIE
        set /a ORGANISATION_SUCCESS=1
    ) else (
        echo ❌ Organisation config principale: ECHEC
    )
) else (
    echo ⚠️ Script principal non trouve - execution manuelle des etapes...
    goto :manual_organization
)

goto :final_results

:manual_organization
echo.
echo ========================================================================
echo                           ORGANISATION MANUELLE
echo ========================================================================
echo.

echo Execution manuelle des etapes d organisation config...

REM Étape 1: Organisation config/paramètres
if exist "organiser_config_parametres.py" (
    echo.
    echo 📁 ETAPE 1: Organisation des configurations et parametres...
    %PYTHON_CMD% organiser_config_parametres.py
    if not errorlevel 1 (
        echo ✅ Organisation config/parametres: REUSSIE
    ) else (
        echo ❌ Organisation config/parametres: ECHEC
        set /p continue_after_error="Continuer malgre l echec ? (o/n): "
        if /i not "%continue_after_error%"=="o" goto :final_results
    )
)

REM Étape 2: Détection conflits
if exist "detecter_resoudre_conflits.py" (
    echo.
    set /p detect_conflicts="🔍 ETAPE 2: Detecter et resoudre les conflits ? (o/n): "
    if /i "%detect_conflicts%"=="o" (
        %PYTHON_CMD% detecter_resoudre_conflits.py
        if not errorlevel 1 (
            echo ✅ Detection/resolution conflits: REUSSIE
        ) else (
            echo ❌ Detection/resolution conflits: ECHEC
        )
    )
)

REM Étape 3: Surveillance temps réel
if exist "surveillance_temps_reel.py" (
    echo.
    set /p surveillance="🔍 ETAPE 3: Activer la surveillance temps reel ? (o/n): "
    if /i "%surveillance%"=="o" (
        echo Demarrage surveillance en arriere-plan...
        start /B "Surveillance GestImmob" %PYTHON_CMD% surveillance_temps_reel.py
        echo ✅ Surveillance demarree en arriere-plan
    )
)

REM Étape 4: Correction codes (optionnelle)
if exist "corriger_tous_problemes.py" (
    echo.
    set /p correct_code="🔧 ETAPE 4: Corriger automatiquement tous les codes ? (o/n): "
    if /i "%correct_code%"=="o" (
        %PYTHON_CMD% corriger_tous_problemes.py
        if not errorlevel 1 (
            echo ✅ Correction codes: REUSSIE
        ) else (
            echo ❌ Correction codes: ECHEC
        )
    )
) else if exist "VERIFIER_ET_CORRIGER_TOUT.bat" (
    echo.
    set /p correct_code="🔧 ETAPE 4: Corriger automatiquement tous les codes ? (o/n): "
    if /i "%correct_code%"=="o" (
        call VERIFIER_ET_CORRIGER_TOUT.bat
        echo ✅ Correction codes: EXECUTEE
    )
)

REM Étape 5: Dépendances (optionnelle)
if exist "installer_dependances.py" (
    echo.
    set /p install_deps="📦 ETAPE 5: Verifier et installer les dependances ? (o/n): "
    if /i "%install_deps%"=="o" (
        %PYTHON_CMD% installer_dependances.py
        if not errorlevel 1 (
            echo ✅ Installation dependances: REUSSIE
        ) else (
            echo ❌ Installation dependances: ECHEC
        )
    )
)

set /a ORGANISATION_SUCCESS=1

:final_results
REM Validation finale
echo.
echo ========================================================================
echo                           VALIDATION FINALE
echo ========================================================================
echo.

echo [%TIME%] Validation finale de l organisation config...

REM Test de la structure config
if exist "config\01_application" (
    echo ✅ Structure config: Organisee
) else (
    echo ❌ Structure config: Manquante
)

REM Test du gestionnaire central
if exist "src\core\config_manager.py" (
    echo ✅ Gestionnaire central: Cree
) else (
    echo ❌ Gestionnaire central: Manquant
)

REM Test des modules organisés
set /a modules_organises=0
if exist "src\modules\inventaire" set /a modules_organises+=1
if exist "src\modules\fournisseur" set /a modules_organises+=1
if exist "src\modules\document" set /a modules_organises+=1
if exist "src\modules\reforme" set /a modules_organises+=1
if exist "src\modules\recherche" set /a modules_organises+=1

if %modules_organises% GEQ 3 (
    echo ✅ Modules organises: %modules_organises%/5
) else (
    echo ⚠️ Modules partiellement organises: %modules_organises%/5
)

REM Test de syntaxe fichiers critiques
if exist "src\main.py" (
    echo Test syntaxe main.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/main.py', 'r', encoding='utf-8').read()); print('✅ Syntaxe main.py: Correcte')" 2>nul
        if errorlevel 1 (
            echo ❌ Syntaxe main.py: Erreur
        )
    )
)

REM Test des dépendances critiques
if defined PYTHON_CMD (
    echo Test des dependances critiques...
    
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: Disponible')" 2>nul
    if errorlevel 1 (
        echo ❌ PySide6: Manquant
    )
    
    %PYTHON_CMD% -c "import sqlalchemy; print('✅ SQLAlchemy: Disponible')" 2>nul
    if errorlevel 1 (
        echo ❌ SQLAlchemy: Manquant
    )
)

REM Résultats finaux
echo.
echo ========================================================================
echo                              RESULTATS FINAUX
echo ========================================================================
echo.

if %ORGANISATION_SUCCESS%==1 (
    echo [%TIME%] 🎉 ORGANISATION CONFIG/PARAMETRES REUSSIE !
    echo.
    echo ✅ CONFIGURATIONS COMPLETEMENT ORGANISEES !
    echo.
    echo 🏆 REALISATIONS:
    echo   ✅ Configurations: Organisees par ordre hierarchique
    echo   ✅ Modules: Isoles sans conflit
    echo   ✅ Gestionnaire central: Operationnel
    echo   ✅ Surveillance: Temps reel disponible
    echo   ✅ Application: Prete a l emploi
    echo.
    echo 📁 STRUCTURE FINALE:
    echo   📁 config/
    echo     📁 01_application/    # Parametres app
    echo     📁 02_database/       # Config BDD
    echo     📁 03_security/       # Securite
    echo     📁 04_modules/        # Config modules
    echo     📁 05_integration/    # Integrations
    echo     📁 06_logging/        # Logs
    echo     📁 07_performance/    # Performance
    echo     📁 08_deployment/     # Deploiement
    echo   📁 src/modules/
    echo     📁 inventaire/        # Module isole
    echo     📁 fournisseur/       # Module isole
    echo     📁 document/          # Module isole
    echo     📁 reforme/           # Module isole
    echo     📁 recherche/         # Module isole
    echo.
    echo 🚀 COMMANDES UTILES:
    if defined PYTHON_CMD (
        echo   %PYTHON_CMD% src\main.py                           # Lancer l application
        echo   %PYTHON_CMD% surveillance_temps_reel.py            # Surveillance manuelle
        echo   %PYTHON_CMD% detecter_resoudre_conflits.py         # Detecter conflits
        echo   %PYTHON_CMD% organiser_config_parametres.py        # Reorganiser config
    ) else (
        echo   python src\main.py                           # Lancer l application
        echo   python surveillance_temps_reel.py            # Surveillance manuelle
        echo   python detecter_resoudre_conflits.py         # Detecter conflits
        echo   python organiser_config_parametres.py        # Reorganiser config
    )
    echo.
    echo 🔒 SECURITE:
    echo   ✅ Sauvegardes automatiques creees
    echo   ✅ Conflits detectes et resolus
    echo   ✅ Modules isoles sans interference
    echo   ✅ Surveillance temps reel active
    echo.
    echo Test de lancement de l application...
    set /p launch="🚀 Lancer GestImmob avec la config organisee ? (o/n): "
    if /i "%launch%"=="o" (
        if defined PYTHON_CMD (
            start "GestImmob - Config Organisee" %PYTHON_CMD% src\main.py
            echo ✅ GestImmob lance avec configuration completement organisee !
        )
    )
) else (
    echo [%TIME%] ⚠️ ORGANISATION CONFIG PARTIELLE
    echo.
    echo Certaines etapes ont echoue ou ont ete ignorees
    echo Consultez les messages ci-dessus pour plus de details
    echo.
    echo ACTIONS RECOMMANDEES:
    echo 1. Verifier les scripts manquants
    echo 2. Corriger les erreurs de configuration
    echo 3. Relancer les scripts individuels si necessaire
    echo 4. Consulter les sauvegardes en cas de probleme
)

echo.
echo OUTILS D ORGANISATION CONFIG CREES:
if exist "organiser_config_parametres.py" echo   🔧 organiser_config_parametres.py: Organisation config/parametres
if exist "detecter_resoudre_conflits.py" echo   🔧 detecter_resoudre_conflits.py: Detection/resolution conflits
if exist "surveillance_temps_reel.py" echo   🔧 surveillance_temps_reel.py: Surveillance temps reel
if exist "ORGANISER_CONFIG_COMPLET.py" echo   🔧 ORGANISER_CONFIG_COMPLET.py: Script principal organisation
echo   📄 ORGANISER_CONFIG_COMPLET.bat: Script batch organisation config complete

:end
echo.
echo [%TIME%] Organisation config complete terminee.
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
