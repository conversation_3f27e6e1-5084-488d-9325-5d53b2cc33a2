<!-- Generated by SpecStory -->

# /fix Le type de retour « dict[str, Unknown] » est  (2025-06-08 11:06:13)

_**User**_

/fix Le type de retour « dict[str, Unknown] » est partiellement inconnu

---

_**Assistant**_

### [db_connection.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/database/db_connection.py)

Corrigez l'annotation de type de retour de la méthode `get_status` pour qu'elle soit explicite et conforme à l'utilisation de `Dict[str, Any]`.

````python
    # ...existing code...
    def get_status(self) -> Dict[str, Any]:
        """
        Retourne l'état de la connexion, synchronisation, IA, cloud, performance.
        """
        return {
            "connected": self.engine is not None,
            "last_sync": self.last_sync,
            "lang": self.lang,
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "performance_mode": self.config.get("performance_mode"),
            "bigdata_mode": self.config.get("bigdata_mode"),
            "ia_modele": self.config.get("ia_modele"),
            "cloud_sync": self.config.get("cloud_sync"),
            "cloud_provider": self.config.get("cloud_provider"),
            "cloud_bucket": self.config.get("cloud_bucket")
        }
    # ...existing code...
````

---

