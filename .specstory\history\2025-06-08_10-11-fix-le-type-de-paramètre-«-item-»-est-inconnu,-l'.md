<!-- Generated by SpecStory -->

# /fix Le type de paramètre « item » est inconnu, L' (2025-06-08 11:11:30)

_**User**_

/fix Le type de paramètre « item » est inconnu, L'annotation de type est manquante pour le paramètre "item"

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Ajouter une annotation de type pour le paramètre `item` dans la fonction `safe_text`. Ici, il s'agit d'un objet potentiellement de type `QTableWidgetItem | None`.

````python
# ...existing code...
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox
)
# ...existing code...

    def modifier_fournisseur(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        # Pré-remplir le formulaire
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier fournisseur")
        form = QFormLayout()
        from typing import Optional
        def safe_text(item: Optional[QTableWidgetItem]) -> str:
            if item is not None:
                text_attr = getattr(item, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            return str(v)
                    except Exception:
                        return ""
            return ""
        # ...existing code...
````

---

