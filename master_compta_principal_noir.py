#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Principale avec Fenêtres Noires
Fond noir avec texte blanc + Logos + Cadres photos agrandis
Champs observation étendus + Interface optimisée
"""

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QDialog, QFormLayout, QLineEdit,
    QComboBox, QTextEdit, QSpinBox, QDateEdit, QCheckBox, QTabWidget,
    QGroupBox, QScrollArea, QGridLayout, QProgressBar, QCalendarWidget
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPixmap, Q<PERSON><PERSON>ter, QColor, QIcon

class MasterComptaPrincipalNoir(QMainWindow):
    """Interface principale MASTER COMPTA GÉNÉRAL avec style noir et blanc"""
    
    def __init__(self):
        super().__init__()
        self.setup_interface_principale_noir()
    
    def setup_interface_principale_noir(self):
        """Configure l'interface principale avec style noir"""
        
        # Configuration de la fenêtre
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Principale")
        self.setGeometry(100, 50, 1600, 1000)
        
        # Style noir avec texte blanc
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.3 #1a1a1a, stop:0.7 #2d2d2d, stop:1 #000000);
                color: white;
                border: 3px solid #FFD700;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                font-size: 13pt;
                font-weight: bold;
                border-bottom: 2px solid #FFD700;
                padding: 8px;
            }
            QMenuBar::item {
                background: transparent;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 8px;
                color: white;
            }
            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #000000, stop:0.5 #2d2d2d, stop:1 #000000);
                color: white;
                font-weight: bold;
                font-size: 12pt;
                border-top: 2px solid #FFD700;
                padding: 8px;
            }
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a1a1a, stop:1 #2d2d2d);
                border: 2px solid #FFD700;
                spacing: 8px;
                padding: 8px;
            }
            QToolButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 10px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                margin: 3px;
                min-width: 100px;
                min-height: 45px;
            }
            QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
                transform: scale(1.05);
            }
        """)
        
        # Menu noir
        self.create_menu_noir()
        
        # Barre d'outils noire
        self.create_toolbar_noir()
        
        # Zone centrale noire
        self.create_central_area_noir()
        
        # Barre de statut noire
        self.create_status_bar_noir()
        
        # Timer pour date/heure
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)
        
        # Afficher la fenêtre
        self.show()
    
    def create_menu_noir(self):
        """Crée le menu avec style noir"""
        menubar = self.menuBar()
        
        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")
        file_menu.addAction("🆕 Nouveau").triggered.connect(self.nouveau_fichier)
        file_menu.addAction("📂 Ouvrir").triggered.connect(self.ouvrir_fichier)
        file_menu.addAction("💾 Sauvegarder").triggered.connect(self.sauvegarder_fichier)
        file_menu.addSeparator()
        file_menu.addAction("🚪 Quitter").triggered.connect(self.close)
        
        # Menu Modules
        modules_menu = menubar.addMenu("📋 Modules")
        modules_list = [
            ("🏠 Gestion des Biens", self.ouvrir_gestion_biens),
            ("🏢 Fournisseurs", self.ouvrir_fournisseurs),
            ("📦 Inventaire", self.ouvrir_inventaire),
            ("🚗 Parc Auto", self.ouvrir_parc_auto),
            ("🐄 Suivi Animaux", self.ouvrir_suivi_animaux),
            ("🔨 Suivi Travaux", self.ouvrir_suivi_travaux),
            ("🔧 Outils Travaux", self.ouvrir_outils_travaux),
            ("📄 Documents", self.ouvrir_documents),
            ("🖨️ Impression", self.ouvrir_impression),
            ("📚 Archive", self.ouvrir_archive),
            ("⚒️ Outillage", self.ouvrir_outillage),
            ("🏭 Moyens Généraux", self.ouvrir_moyens_generaux),
            ("🏨 Hôtellerie", self.ouvrir_hotellerie),
            ("🪑 Agencement", self.ouvrir_agencement),
            ("🏗️ Bâtis", self.ouvrir_batis),
            ("🧮 Calculatrice", self.ouvrir_calculatrice)
        ]
        
        for text, func in modules_list:
            modules_menu.addAction(text).triggered.connect(func)
        
        # Menu Langues
        lang_menu = menubar.addMenu("🌍 Langues")
        lang_menu.addAction("🇫🇷 Français").triggered.connect(lambda: self.changer_langue("fr"))
        lang_menu.addAction("🇩🇿 العربية").triggered.connect(lambda: self.changer_langue("ar"))
        lang_menu.addAction("🇺🇸 English").triggered.connect(lambda: self.changer_langue("en"))
        
        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")
        help_menu.addAction("ℹ️ À propos").triggered.connect(self.afficher_apropos)
    
    def create_toolbar_noir(self):
        """Crée la barre d'outils noire"""
        toolbar = self.addToolBar("Outils MASTER COMPTA")
        
        toolbar_actions = [
            ("🆕 Nouveau", self.nouveau_fichier),
            ("📂 Ouvrir", self.ouvrir_fichier),
            ("💾 Sauvegarder", self.sauvegarder_fichier),
            ("🖨️ Imprimer", self.imprimer),
            ("🔍 Rechercher", self.rechercher),
            ("🧮 Calculatrice", self.ouvrir_calculatrice),
            ("⚙️ Paramètres", self.ouvrir_parametres),
            ("📊 Rapports", self.generer_rapports)
        ]
        
        for text, func in toolbar_actions:
            action = toolbar.addAction(text)
            action.triggered.connect(func)
    
    def create_central_area_noir(self):
        """Crée la zone centrale avec style noir"""
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 15px;
                margin: 8px;
            }
        """)
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # En-tête avec logos et date/heure
        self.create_header_noir(layout)
        
        # Zone des modules
        self.create_modules_grid_noir(layout)
        
        # Zone d'informations
        self.create_info_area_noir(layout)
    
    def create_header_noir(self, layout):
        """Crée l'en-tête noir avec logos"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #2d2d2d, stop:1 #000000);
                border: 3px solid #FFD700;
                border-radius: 20px;
                padding: 25px;
                margin: 15px;
            }
        """)
        header_frame.setFixedHeight(180)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Logo gauche
        logo_left = QLabel("🏢")
        logo_left.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 60pt;
                font-weight: bold;
                background: transparent;
                padding: 15px;
            }
        """)
        logo_left.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(logo_left)
        
        # Zone centrale avec titre et date/heure
        center_layout = QVBoxLayout()
        
        # Titre
        title_label = QLabel("MASTER COMPTA GÉNÉRAL")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 28pt;
                font-weight: bold;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(255,215,0,0.5);
            }
        """)
        center_layout.addWidget(title_label)
        
        # Date et heure
        self.datetime_main = QLabel()
        self.datetime_main.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.datetime_main.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 18pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        center_layout.addWidget(self.datetime_main)
        
        header_layout.addLayout(center_layout)
        
        # Logo droit avec calendrier
        right_layout = QVBoxLayout()
        
        logo_right = QLabel("💼")
        logo_right.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 60pt;
                font-weight: bold;
                background: transparent;
                padding: 15px;
            }
        """)
        logo_right.setAlignment(Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(logo_right)
        
        # Bouton calendrier
        calendar_btn = QPushButton("📅 Calendrier")
        calendar_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 10px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
            }
        """)
        calendar_btn.clicked.connect(self.show_calendar_noir)
        right_layout.addWidget(calendar_btn)
        
        header_layout.addLayout(right_layout)
        
        layout.addWidget(header_frame)
    
    def create_modules_grid_noir(self, layout):
        """Crée la grille des modules avec style noir"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a1a, stop:0.5 #000000, stop:1 #1a1a1a);
                border: 2px solid #FFD700;
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
            }
        """)
        
        modules_layout = QGridLayout(modules_frame)
        modules_layout.setSpacing(15)
        
        # Configuration des modules avec couleurs
        modules_config = [
            ("🏠 Gestion des Biens", "#FF6B6B", self.ouvrir_gestion_biens),
            ("🏢 Fournisseurs", "#4ECDC4", self.ouvrir_fournisseurs),
            ("📦 Inventaire", "#45B7D1", self.ouvrir_inventaire),
            ("🚗 Parc Auto", "#96CEB4", self.ouvrir_parc_auto),
            ("🐄 Suivi Animaux", "#FFEAA7", self.ouvrir_suivi_animaux),
            ("🔨 Suivi Travaux", "#DDA0DD", self.ouvrir_suivi_travaux),
            ("🔧 Outils Travaux", "#98D8C8", self.ouvrir_outils_travaux),
            ("📄 Documents", "#F7DC6F", self.ouvrir_documents),
            ("🖨️ Impression", "#BB8FCE", self.ouvrir_impression),
            ("📚 Archive", "#85C1E9", self.ouvrir_archive),
            ("⚒️ Outillage", "#F8C471", self.ouvrir_outillage),
            ("🏭 Moyens Généraux", "#82E0AA", self.ouvrir_moyens_generaux),
            ("🏨 Hôtellerie", "#F1948A", self.ouvrir_hotellerie),
            ("🪑 Agencement", "#85C1E9", self.ouvrir_agencement),
            ("🏗️ Bâtis", "#D7BDE2", self.ouvrir_batis),
            ("🧮 Calculatrice", "#A3E4D7", self.ouvrir_calculatrice)
        ]
        
        # Organiser en grille 4x4
        row, col = 0, 0
        max_cols = 4
        
        for text, color, func in modules_config:
            btn = self.create_module_button_noir(text, color, func)
            modules_layout.addWidget(btn, row, col)
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        layout.addWidget(modules_frame)
    
    def create_module_button_noir(self, text, color, func):
        """Crée un bouton de module avec style noir"""
        btn = QPushButton(text)
        btn.clicked.connect(func)
        
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:0.5 {color}, stop:1 #000000);
                color: white;
                border: 3px solid #FFD700;
                border-radius: 15px;
                font-size: 12pt;
                font-weight: bold;
                padding: 20px;
                min-width: 200px;
                min-height: 120px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:0.5 #FFD700, stop:1 {color});
                color: black;
                border: 3px solid white;
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:0.5 #4d4d4d, stop:1 #2d2d2d);
                color: white;
                border: 3px solid #FFD700;
            }}
        """)
        
        return btn
    
    def create_info_area_noir(self, layout):
        """Crée la zone d'informations avec style noir"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
            }
        """)
        info_frame.setFixedHeight(120)
        
        layout_info = QHBoxLayout(info_frame)
        
        # Informations système
        system_info = QLabel("""
        📊 <b style="color: #FFD700;">MASTER COMPTA GÉNÉRAL</b><br>
        <span style="color: white;">🗃️ Base de données: Connectée</span><br>
        <span style="color: white;">👤 Utilisateur: Administrateur</span><br>
        <span style="color: white;">💰 Devise: Euro (€)</span><br>
        <span style="color: white;">🔢 Codification: INV001</span>
        """)
        system_info.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 215, 0, 0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 15px;
                font-size: 12pt;
                color: white;
            }
        """)
        layout_info.addWidget(system_info)
        
        # Statistiques
        stats_info = QLabel("""
        📈 <b style="color: #FFD700;">Statistiques</b><br>
        <span style="color: white;">🏠 Biens gérés: 0</span><br>
        <span style="color: white;">🚗 Véhicules: 0</span><br>
        <span style="color: white;">📦 Articles: 0</span><br>
        <span style="color: white;">🏢 Fournisseurs: 0</span>
        """)
        stats_info.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 215, 0, 0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 15px;
                font-size: 12pt;
                color: white;
            }
        """)
        layout_info.addWidget(stats_info)
        
        # Actions rapides
        quick_actions = QLabel("""
        ⚡ <b style="color: #FFD700;">Actions Rapides</b><br>
        <span style="color: white;">🆕 Nouveau bien</span><br>
        <span style="color: white;">🔍 Recherche avancée</span><br>
        <span style="color: white;">📊 Rapports multilingues</span><br>
        <span style="color: white;">💾 Sauvegarde auto</span>
        """)
        quick_actions.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 215, 0, 0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 15px;
                font-size: 12pt;
                color: white;
            }
        """)
        layout_info.addWidget(quick_actions)
        
        layout.addWidget(info_frame)
    
    def create_status_bar_noir(self):
        """Crée la barre de statut noire"""
        status_bar = self.statusBar()
        status_bar.showMessage("🎯 MASTER COMPTA GÉNÉRAL prêt • Interface noire avec texte blanc • 💰 Devise: € • 🔢 Codification: INV001 • 📋 16 modules complets")
    
    def update_datetime(self):
        """Met à jour la date et l'heure"""
        now = datetime.now()
        datetime_str = f"📅 {now.strftime('%d/%m/%Y')} • 🕐 {now.strftime('%H:%M:%S')}"
        if hasattr(self, 'datetime_main'):
            self.datetime_main.setText(datetime_str)
    
    def show_calendar_noir(self):
        """Affiche le calendrier avec style noir"""
        calendar_dialog = QDialog(self)
        calendar_dialog.setWindowTitle("📅 Calendrier MASTER COMPTA")
        calendar_dialog.setFixedSize(500, 450)
        calendar_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                border: 3px solid #FFD700;
                border-radius: 15px;
            }
        """)
        
        layout = QVBoxLayout(calendar_dialog)
        
        calendar = QCalendarWidget()
        calendar.setStyleSheet("""
            QCalendarWidget {
                background-color: #1a1a1a;
                border: 2px solid #FFD700;
                border-radius: 10px;
                font-size: 12pt;
                color: white;
            }
            QCalendarWidget QToolButton {
                background-color: #000000;
                color: white;
                border: 1px solid #FFD700;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QCalendarWidget QToolButton:hover {
                background-color: #FFD700;
                color: black;
            }
            QCalendarWidget QMenu {
                background-color: #1a1a1a;
                border: 1px solid #FFD700;
                color: white;
            }
            QCalendarWidget QSpinBox {
                background-color: #1a1a1a;
                border: 1px solid #FFD700;
                border-radius: 5px;
                padding: 5px;
                color: white;
            }
            QCalendarWidget QAbstractItemView:enabled {
                background-color: #1a1a1a;
                color: white;
                selection-background-color: #FFD700;
                selection-color: black;
            }
        """)
        layout.addWidget(calendar)
        
        close_btn = QPushButton("✅ Fermer")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
            }
        """)
        close_btn.clicked.connect(calendar_dialog.accept)
        layout.addWidget(close_btn)
        
        calendar_dialog.exec()
    
    # Méthodes des modules (à implémenter)
    def ouvrir_gestion_biens(self):
        print("🏠 Ouverture Gestion des Biens avec interface noire")
    
    def ouvrir_fournisseurs(self):
        print("🏢 Ouverture Fournisseurs avec interface noire")
    
    def ouvrir_inventaire(self):
        print("📦 Ouverture Inventaire avec interface noire")
    
    def ouvrir_parc_auto(self):
        print("🚗 Ouverture Parc Auto avec interface noire")
    
    def ouvrir_suivi_animaux(self):
        print("🐄 Ouverture Suivi Animaux avec interface noire")
    
    def ouvrir_suivi_travaux(self):
        print("🔨 Ouverture Suivi Travaux avec interface noire")
    
    def ouvrir_outils_travaux(self):
        print("🔧 Ouverture Outils Travaux avec interface noire")
    
    def ouvrir_documents(self):
        print("📄 Ouverture Documents avec interface noire")
    
    def ouvrir_impression(self):
        print("🖨️ Ouverture Impression avec interface noire")
    
    def ouvrir_archive(self):
        print("📚 Ouverture Archive avec interface noire")
    
    def ouvrir_outillage(self):
        print("⚒️ Ouverture Outillage avec interface noire")
    
    def ouvrir_moyens_generaux(self):
        print("🏭 Ouverture Moyens Généraux avec interface noire")
    
    def ouvrir_hotellerie(self):
        print("🏨 Ouverture Hôtellerie avec interface noire")
    
    def ouvrir_agencement(self):
        print("🪑 Ouverture Agencement avec interface noire")
    
    def ouvrir_batis(self):
        print("🏗️ Ouverture Bâtis avec interface noire")
    
    def ouvrir_calculatrice(self):
        print("🧮 Ouverture Calculatrice avec interface noire")
    
    # Méthodes d'action
    def nouveau_fichier(self):
        print("🆕 Nouveau fichier")
    
    def ouvrir_fichier(self):
        print("📂 Ouvrir fichier")
    
    def sauvegarder_fichier(self):
        print("💾 Sauvegarder fichier")
    
    def imprimer(self):
        print("🖨️ Impression")
    
    def rechercher(self):
        print("🔍 Recherche")
    
    def ouvrir_parametres(self):
        print("⚙️ Paramètres")
    
    def generer_rapports(self):
        print("📊 Génération rapports")
    
    def changer_langue(self, language):
        print(f"🌍 Changement langue: {language}")
    
    def afficher_apropos(self):
        print("ℹ️ À propos de MASTER COMPTA GÉNÉRAL")

# Script de lancement
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Master Compta Solutions")
    
    # Lancement de l'interface principale noire
    window = MasterComptaPrincipalNoir()
    
    # Exécution de l'application
    sys.exit(app.exec())
