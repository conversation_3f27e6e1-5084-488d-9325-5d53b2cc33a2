#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST IMMÉDIAT DE TOUS LES FICHIERS PYTHON
Teste la syntaxe et identifie les problèmes précis
"""

import ast
import sys
import os
from pathlib import Path

def test_python_file(file_path):
    """Teste un fichier Python spécifique"""
    print(f"🔍 Test de {file_path.name}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test de compilation
        try:
            compile(content, str(file_path), 'exec')
            print(f"✅ {file_path.name}: Compilation OK")
            
            # Test AST
            ast.parse(content)
            print(f"✅ {file_path.name}: AST OK")
            
            return True, None
            
        except SyntaxError as e:
            error_msg = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
            print(f"❌ {file_path.name}: {error_msg}")
            if e.text:
                print(f"    Code: {e.text.strip()}")
            return False, error_msg
            
        except Exception as e:
            error_msg = f"Erreur: {e}"
            print(f"❌ {file_path.name}: {error_msg}")
            return False, error_msg
            
    except Exception as e:
        error_msg = f"Erreur lecture: {e}"
        print(f"❌ {file_path.name}: {error_msg}")
        return False, error_msg

def main():
    """Test immédiat de tous les fichiers Python"""
    print("🚀 TEST IMMÉDIAT DE TOUS LES FICHIERS PYTHON")
    print("=" * 60)
    
    # Trouver tous les fichiers Python
    python_files = []
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.vscode']
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    print(f"📁 Fichiers Python trouvés: {len(python_files)}")
    print()
    
    # Tester chaque fichier
    success_count = 0
    errors = []
    
    for file_path in sorted(python_files):
        success, error = test_python_file(file_path)
        if success:
            success_count += 1
        else:
            errors.append(f"{file_path.name}: {error}")
    
    # Résumé
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DU TEST")
    print("="*60)
    
    success_rate = (success_count / len(python_files)) * 100 if python_files else 0
    
    print(f"✅ Fichiers valides: {success_count}/{len(python_files)}")
    print(f"📈 Taux de réussite: {success_rate:.1f}%")
    
    if errors:
        print(f"\n❌ ERREURS TROUVÉES ({len(errors)}):")
        for error in errors:
            print(f"  - {error}")
    
    # Tests spécifiques des fichiers principaux
    print(f"\n🎯 TESTS SPÉCIFIQUES:")
    
    main_files = [
        'src/main.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py',
        'src/auth/user_management.py'
    ]
    
    for file_path in main_files:
        if Path(file_path).exists():
            success, error = test_python_file(Path(file_path))
            if not success:
                print(f"🚨 PROBLÈME CRITIQUE dans {file_path}: {error}")
    
    if success_rate >= 95:
        print(f"\n🎉 TOUS LES FICHIERS PYTHON SONT PARFAITS !")
        return True
    elif success_rate >= 80:
        print(f"\n✅ La plupart des fichiers sont corrects")
        print(f"⚠️ {len(errors)} fichiers nécessitent des corrections")
        return True
    else:
        print(f"\n❌ CORRECTIONS URGENTES NÉCESSAIRES")
        print(f"🚨 {len(errors)} fichiers avec des erreurs critiques")
        return False

if __name__ == "__main__":
    main()
