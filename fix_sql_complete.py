#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger COMPLÈTEMENT le fichier SQL
"""

import re
import os

def fix_sql_complete():
    """Corrige complètement le fichier SQL init.sql"""
    file_path = 'src/database/migrations/init.sql'
    
    print("🔧 CORRECTION COMPLÈTE DU FICHIER SQL")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier non trouvé: {file_path}")
        return False
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier lu: {len(content)} caractères")
        
        # Backup complet
        backup_path = file_path + '.backup_complete'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 Backup créé: {backup_path}")
        
        # Compter les occurrences avant correction
        serial_count_before = len(re.findall(r'\bSERIAL\s+PRIMARY\s+KEY\b', content, re.IGNORECASE))
        boolean_count_before = len(re.findall(r'\bBOOLEAN\b', content, re.IGNORECASE))
        
        print(f"📊 Avant correction:")
        print(f"   SERIAL PRIMARY KEY: {serial_count_before} occurrences")
        print(f"   BOOLEAN: {boolean_count_before} occurrences")
        
        # Corrections complètes
        original_content = content
        
        # 1. Corriger TOUTES les occurrences de SERIAL PRIMARY KEY
        content = re.sub(
            r'\bid\s+SERIAL\s+PRIMARY\s+KEY\b',
            'id INTEGER PRIMARY KEY AUTOINCREMENT',
            content,
            flags=re.IGNORECASE
        )
        
        # 2. Corriger TOUTES les occurrences de BOOLEAN
        content = re.sub(
            r'\bBOOLEAN\b',
            'INTEGER',
            content,
            flags=re.IGNORECASE
        )
        
        # 3. Corriger d'autres types PostgreSQL spécifiques si présents
        content = re.sub(
            r'\bBIGSERIAL\b',
            'INTEGER',
            content,
            flags=re.IGNORECASE
        )
        
        content = re.sub(
            r'\bSMALLSERIAL\b',
            'INTEGER',
            content,
            flags=re.IGNORECASE
        )
        
        # Compter les occurrences après correction
        serial_count_after = len(re.findall(r'\bSERIAL\s+PRIMARY\s+KEY\b', content, re.IGNORECASE))
        boolean_count_after = len(re.findall(r'\bBOOLEAN\b', content, re.IGNORECASE))
        
        print(f"\n📊 Après correction:")
        print(f"   SERIAL PRIMARY KEY: {serial_count_after} occurrences")
        print(f"   BOOLEAN: {boolean_count_after} occurrences")
        
        # Calculer les corrections appliquées
        serial_fixed = serial_count_before - serial_count_after
        boolean_fixed = boolean_count_before - boolean_count_after
        
        print(f"\n✅ Corrections appliquées:")
        print(f"   SERIAL → INTEGER PRIMARY KEY AUTOINCREMENT: {serial_fixed} corrections")
        print(f"   BOOLEAN → INTEGER: {boolean_fixed} corrections")
        
        # Sauvegarder le fichier corrigé
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"\n💾 Fichier corrigé sauvegardé: {file_path}")
        
        # Validation finale
        if serial_count_after == 0 and boolean_count_after == 0:
            print(f"\n🎉 CORRECTION COMPLÈTE RÉUSSIE !")
            print(f"✅ Toutes les occurrences PostgreSQL ont été corrigées")
            print(f"✅ Le fichier est maintenant 100% compatible SQLite")
            return True
        else:
            print(f"\n⚠️ CORRECTION PARTIELLE")
            if serial_count_after > 0:
                print(f"❌ {serial_count_after} occurrences SERIAL restantes")
            if boolean_count_after > 0:
                print(f"❌ {boolean_count_after} occurrences BOOLEAN restantes")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def validate_sql_syntax():
    """Valide la syntaxe SQL après correction"""
    file_path = 'src/database/migrations/init.sql'
    
    print(f"\n🔍 VALIDATION DE LA SYNTAXE SQL")
    print("-" * 40)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifications de base
        issues = []
        
        # Vérifier qu'il n'y a plus de syntaxe PostgreSQL
        if re.search(r'\bSERIAL\b', content, re.IGNORECASE):
            issues.append("Syntaxe PostgreSQL SERIAL détectée")
        
        if re.search(r'\bBOOLEAN\b', content, re.IGNORECASE):
            issues.append("Type BOOLEAN PostgreSQL détecté")
        
        # Vérifier la présence de syntaxe SQLite
        autoincrement_count = len(re.findall(r'\bAUTOINCREMENT\b', content, re.IGNORECASE))
        integer_pk_count = len(re.findall(r'\bINTEGER\s+PRIMARY\s+KEY\b', content, re.IGNORECASE))
        
        print(f"📊 Syntaxe SQLite détectée:")
        print(f"   INTEGER PRIMARY KEY: {integer_pk_count} occurrences")
        print(f"   AUTOINCREMENT: {autoincrement_count} occurrences")
        
        if issues:
            print(f"\n❌ PROBLÈMES DÉTECTÉS:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print(f"\n✅ SYNTAXE SQL PARFAITE")
            print(f"✅ 100% compatible SQLite")
            print(f"✅ Aucune syntaxe PostgreSQL restante")
            return True
            
    except Exception as e:
        print(f"❌ Erreur validation: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CORRECTION COMPLÈTE DU FICHIER SQL")
    print("=" * 60)
    
    # Étape 1: Correction complète
    if fix_sql_complete():
        print(f"\n" + "="*60)
        
        # Étape 2: Validation
        if validate_sql_syntax():
            print(f"\n🏆 MISSION ACCOMPLIE !")
            print(f"✅ Le fichier init.sql est maintenant parfaitement compatible SQLite")
            print(f"✅ Toutes les occurrences PostgreSQL ont été corrigées")
            print(f"✅ La base de données peut être créée sans erreur")
            return True
        else:
            print(f"\n⚠️ Correction réussie mais validation échouée")
            return False
    else:
        print(f"\n❌ Échec de la correction")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat: {'SUCCÈS COMPLET' if success else 'ÉCHEC'}")
    exit(0 if success else 1)
