-- Scripts SQL de synchronisation GestImmob
-- Création des tables de synchronisation

-- Table de log de synchronisation
CREATE TABLE IF NOT EXISTS sync_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    component TEXT NOT NULL,
    action TEXT NOT NULL,
    status TEXT NOT NULL,
    details TEXT,
    error_message TEXT
);

-- Table de configuration de synchronisation
CREATE TABLE IF NOT EXISTS sync_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insertion de la configuration par défaut
INSERT OR REPLACE INTO sync_config (config_key, config_value) VALUES
('sync_interval', '30'),
('auto_sync_enabled', 'true'),
('last_sync_timestamp', datetime('now')),
('sync_components', '["python", "javascript", "powershell", "sql"]');

-- Vue pour les statistiques de synchronisation
CREATE VIEW IF NOT EXISTS sync_stats AS
SELECT
    component,
    COUNT(*) as total_syncs,
    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_syncs,
    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed_syncs,
    MAX(timestamp) as last_sync
FROM sync_log
GROUP BY component;

-- Procédure de nettoyage des logs anciens (simulée avec DELETE)
-- Garder seulement les 1000 derniers logs
DELETE FROM sync_log
WHERE id NOT IN (
    SELECT id FROM sync_log
    ORDER BY timestamp DESC
    LIMIT 1000
);

-- Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_sync_log_timestamp ON sync_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_sync_log_component ON sync_log(component);
CREATE INDEX IF NOT EXISTS idx_sync_log_status ON sync_log(status);
