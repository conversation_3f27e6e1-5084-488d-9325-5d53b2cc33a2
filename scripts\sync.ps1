# Script PowerShell de synchronisation GestImmob
param([string]$Action = "sync")

Write-Host "=== Synchronisation PowerShell GestImmob ===" -ForegroundColor Green

switch ($Action) {
    "sync" {
        Write-Host "Synchronisation en cours..." -ForegroundColor Yellow
        python sync_module.py
        Write-Host "Synchronisation terminée" -ForegroundColor Green
    }
    "check" {
        Write-Host "Vérification des fichiers..." -ForegroundColor Yellow
        Get-ChildItem -Path "." -Filter "*.py" | ForEach-Object {
            Write-Host "Fichier Python: $($_.Name)" -ForegroundColor Cyan
        }
    }
    "install" {
        Write-Host "Installation des dépendances..." -ForegroundColor Yellow
        pip install -r requirements.txt
        Write-Host "Installation terminée" -ForegroundColor Green
    }
}
