"""
Gestionnaire de thèmes pour GestImmob
Fournit des thèmes modernes et fluides pour l'interface utilisateur
"""

from typing import Dict, Any
import json
from pathlib import Path
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>, QColor

class ThemeManager(QObject):
    """Gestionnaire de thèmes avec support des thèmes personnalisés"""
    
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "modern_light"
        self.themes = self._load_themes()
        
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """Charge les définitions de thèmes"""
        return {
            "modern_light": {
                "name": "Moderne Clair",
                "colors": {
                    "primary": "#1976D2",
                    "primary_dark": "#1565C0", 
                    "primary_light": "#BBDEFB",
                    "secondary": "#424242",
                    "accent": "#FF5722",
                    "background": "#FAFAFA",
                    "surface": "#FFFFFF",
                    "error": "#F44336",
                    "warning": "#FF9800",
                    "success": "#4CAF50",
                    "info": "#2196F3",
                    "text_primary": "#212121",
                    "text_secondary": "#757575",
                    "text_disabled": "#BDBDBD",
                    "divider": "#E0E0E0",
                    "border": "#E0E0E0"
                },
                "stylesheet": self._get_modern_light_stylesheet()
            },
            "modern_dark": {
                "name": "Moderne Sombre",
                "colors": {
                    "primary": "#2196F3",
                    "primary_dark": "#1976D2",
                    "primary_light": "#64B5F6",
                    "secondary": "#37474F",
                    "accent": "#FF5722",
                    "background": "#121212",
                    "surface": "#1E1E1E",
                    "error": "#CF6679",
                    "warning": "#FFB74D",
                    "success": "#81C784",
                    "info": "#64B5F6",
                    "text_primary": "#FFFFFF",
                    "text_secondary": "#B0BEC5",
                    "text_disabled": "#616161",
                    "divider": "#37474F",
                    "border": "#37474F"
                },
                "stylesheet": self._get_modern_dark_stylesheet()
            },
            "professional": {
                "name": "Professionnel",
                "colors": {
                    "primary": "#0D47A1",
                    "primary_dark": "#002171",
                    "primary_light": "#5472D3",
                    "secondary": "#263238",
                    "accent": "#FF6F00",
                    "background": "#F5F5F5",
                    "surface": "#FFFFFF",
                    "error": "#D32F2F",
                    "warning": "#F57C00",
                    "success": "#388E3C",
                    "info": "#1976D2",
                    "text_primary": "#212121",
                    "text_secondary": "#616161",
                    "text_disabled": "#9E9E9E",
                    "divider": "#BDBDBD",
                    "border": "#BDBDBD"
                },
                "stylesheet": self._get_professional_stylesheet()
            }
        }
    
    def _get_modern_light_stylesheet(self) -> str:
        """Retourne le stylesheet pour le thème moderne clair"""
        return """
        QMainWindow {
            background-color: #FAFAFA;
            color: #212121;
        }
        
        QWidget {
            background-color: #FAFAFA;
            color: #212121;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }
        
        QPushButton {
            background-color: #1976D2;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #1565C0;
        }
        
        QPushButton:pressed {
            background-color: #0D47A1;
        }
        
        QPushButton:disabled {
            background-color: #BDBDBD;
            color: #757575;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: white;
            border: 2px solid #E0E0E0;
            border-radius: 4px;
            padding: 8px;
            selection-background-color: #BBDEFB;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #1976D2;
        }
        
        QTableWidget {
            background-color: white;
            alternate-background-color: #F5F5F5;
            gridline-color: #E0E0E0;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
        }
        
        QHeaderView::section {
            background-color: #1976D2;
            color: white;
            padding: 8px;
            border: none;
            font-weight: 600;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #E0E0E0;
        }
        
        QTableWidget::item:selected {
            background-color: #BBDEFB;
            color: #212121;
        }
        
        QMenuBar {
            background-color: white;
            border-bottom: 1px solid #E0E0E0;
        }
        
        QMenuBar::item {
            padding: 8px 12px;
            background-color: transparent;
        }
        
        QMenuBar::item:selected {
            background-color: #E3F2FD;
        }
        
        QMenu {
            background-color: white;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #E3F2FD;
        }
        
        QScrollBar:vertical {
            background-color: #F5F5F5;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #BDBDBD;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #9E9E9E;
        }
        
        QStatusBar {
            background-color: white;
            border-top: 1px solid #E0E0E0;
        }
        
        QLabel#HeaderTitle {
            color: #1976D2;
            font-size: 24pt;
            font-weight: 700;
        }
        
        QLabel#HeaderSubtitle {
            color: #757575;
            font-size: 12pt;
        }
        """
    
    def _get_modern_dark_stylesheet(self) -> str:
        """Retourne le stylesheet pour le thème moderne sombre"""
        return """
        QMainWindow {
            background-color: #121212;
            color: #FFFFFF;
        }
        
        QWidget {
            background-color: #121212;
            color: #FFFFFF;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }
        
        QPushButton {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #1976D2;
        }
        
        QPushButton:pressed {
            background-color: #0D47A1;
        }
        
        QPushButton:disabled {
            background-color: #37474F;
            color: #616161;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: #1E1E1E;
            border: 2px solid #37474F;
            border-radius: 4px;
            padding: 8px;
            color: #FFFFFF;
            selection-background-color: #1976D2;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #2196F3;
        }
        
        QTableWidget {
            background-color: #1E1E1E;
            alternate-background-color: #2C2C2C;
            gridline-color: #37474F;
            border: 1px solid #37474F;
            border-radius: 4px;
            color: #FFFFFF;
        }
        
        QHeaderView::section {
            background-color: #2196F3;
            color: white;
            padding: 8px;
            border: none;
            font-weight: 600;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #37474F;
        }
        
        QTableWidget::item:selected {
            background-color: #1976D2;
            color: white;
        }
        
        QMenuBar {
            background-color: #1E1E1E;
            border-bottom: 1px solid #37474F;
            color: #FFFFFF;
        }
        
        QMenuBar::item {
            padding: 8px 12px;
            background-color: transparent;
        }
        
        QMenuBar::item:selected {
            background-color: #37474F;
        }
        
        QMenu {
            background-color: #1E1E1E;
            border: 1px solid #37474F;
            border-radius: 4px;
            color: #FFFFFF;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #37474F;
        }
        
        QScrollBar:vertical {
            background-color: #2C2C2C;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #616161;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #757575;
        }
        
        QStatusBar {
            background-color: #1E1E1E;
            border-top: 1px solid #37474F;
            color: #FFFFFF;
        }
        
        QLabel#HeaderTitle {
            color: #2196F3;
            font-size: 24pt;
            font-weight: 700;
        }
        
        QLabel#HeaderSubtitle {
            color: #B0BEC5;
            font-size: 12pt;
        }
        """
    
    def _get_professional_stylesheet(self) -> str:
        """Retourne le stylesheet pour le thème professionnel"""
        return """
        QMainWindow {
            background-color: #F5F5F5;
            color: #212121;
        }
        
        QWidget {
            background-color: #F5F5F5;
            color: #212121;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 9pt;
        }
        
        QPushButton {
            background-color: #0D47A1;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 6px 12px;
            font-weight: 500;
            min-width: 70px;
        }
        
        QPushButton:hover {
            background-color: #002171;
        }
        
        QPushButton:pressed {
            background-color: #001040;
        }
        
        QPushButton:disabled {
            background-color: #9E9E9E;
            color: #616161;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            background-color: white;
            border: 1px solid #BDBDBD;
            border-radius: 2px;
            padding: 6px;
            selection-background-color: #E3F2FD;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #0D47A1;
        }
        
        QTableWidget {
            background-color: white;
            alternate-background-color: #FAFAFA;
            gridline-color: #BDBDBD;
            border: 1px solid #BDBDBD;
        }
        
        QHeaderView::section {
            background-color: #0D47A1;
            color: white;
            padding: 6px;
            border: none;
            font-weight: 600;
        }
        
        QTableWidget::item {
            padding: 6px;
            border-bottom: 1px solid #E0E0E0;
        }
        
        QTableWidget::item:selected {
            background-color: #E3F2FD;
            color: #212121;
        }
        
        QLabel#HeaderTitle {
            color: #0D47A1;
            font-size: 20pt;
            font-weight: 700;
        }
        
        QLabel#HeaderSubtitle {
            color: #616161;
            font-size: 11pt;
        }
        """
    
    def apply_theme(self, theme_name: str):
        """Applique un thème à l'application"""
        if theme_name not in self.themes:
            raise ValueError(f"Thème '{theme_name}' non trouvé")
        
        theme = self.themes[theme_name]
        app = QApplication.instance()
        
        if app:
            app.setStyleSheet(theme["stylesheet"])
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
    
    def get_color(self, color_name: str) -> str:
        """Retourne une couleur du thème actuel"""
        theme = self.themes.get(self.current_theme, {})
        colors = theme.get("colors", {})
        return colors.get(color_name, "#000000")
    
    def get_available_themes(self) -> Dict[str, str]:
        """Retourne la liste des thèmes disponibles"""
        return {name: theme["name"] for name, theme in self.themes.items()}
    
    def save_theme_preference(self, theme_name: str):
        """Sauvegarde la préférence de thème"""
        config_path = Path("config_advanced.json")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            config["ui"]["theme"] = theme_name
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
    
    def load_theme_preference(self) -> str:
        """Charge la préférence de thème sauvegardée"""
        config_path = Path("config_advanced.json")
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config.get("ui", {}).get("theme", "modern_light")
            except:
                pass
        return "modern_light"
