#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Synchronisation des Modules GestImmob
Maintient tous les modules et critères synchronisés
"""

import sqlite3
import json
import os
from datetime import datetime

class SynchronisateurModules:
    """Synchronise tous les modules et critères de GestImmob"""
    
    def __init__(self):
        self.db_path = "gestimmob_final.db"
        self.config_file = "modules_synchronises.json"
        self.modules_existants = {}
        self.nouveaux_modules = {}
        self.load_configuration()
    
    def load_configuration(self):
        """Charge la configuration des modules"""
        # Modules existants (basés sur vos références)
        self.modules_existants = {
            'Informatique & Technologies': {
                'couleur': '#2196F3', 
                'icone': '💻', 
                'criteres': 15,
                'actif': True,
                'source': 'existant'
            },
            'Véhicules & Transport': {
                'couleur': '#FF9800', 
                'icone': '🚗', 
                'criteres': 16,
                'actif': True,
                'source': 'existant'
            },
            'Mobilier & Aménagement': {
                'couleur': '#8BC34A', 
                'icone': '🪑', 
                'criteres': 12,
                'actif': True,
                'source': 'existant'
            },
            'Équipements Industriels': {
                'couleur': '#F44336', 
                'icone': '⚙️', 
                'criteres': 15,
                'actif': True,
                'source': 'existant'
            }
        }
        
        # Nouveaux modules demandés
        self.nouveaux_modules = {
            'Gestion des Biens': {'couleur': '#1976D2', 'icone': '🏠', 'actif': True},
            'Fournisseurs': {'couleur': '#388E3C', 'icone': '🏢', 'actif': True},
            'Inventaire': {'couleur': '#F57C00', 'icone': '📦', 'actif': True},
            'Parc Auto': {'couleur': '#D32F2F', 'icone': '🚗', 'actif': True},
            'Suivi des Animaux': {'couleur': '#7B1FA2', 'icone': '🐄', 'actif': True},
            'Suivi des Travaux': {'couleur': '#455A64', 'icone': '🔨', 'actif': True},
            'Outils de Travaux': {'couleur': '#5D4037', 'icone': '🔧', 'actif': True},
            'Calculatrice': {'couleur': '#00796B', 'icone': '🧮', 'actif': True},
            'Documents': {'couleur': '#303F9F', 'icone': '📄', 'actif': True},
            'Impression': {'couleur': '#689F38', 'icone': '🖨️', 'actif': True},
            'Archive': {'couleur': '#512DA8', 'icone': '📚', 'actif': True},
            'Outillage': {'couleur': '#E64A19', 'icone': '⚒️', 'actif': True},
            'Moyens Généraux': {'couleur': '#1976D2', 'icone': '🏭', 'actif': True},
            'Hôtellerie': {'couleur': '#C2185B', 'icone': '🏨', 'actif': True},
            'Agencement': {'couleur': '#00ACC1', 'icone': '🪑', 'actif': True},
            'Bâtis': {'couleur': '#6A4C93', 'icone': '🏗️', 'actif': True}
        }
    
    def synchroniser_tous_modules(self):
        """Synchronise tous les modules avec la base de données"""
        print("🔄 SYNCHRONISATION DE TOUS LES MODULES")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Créer les tables si nécessaire
            self.creer_tables_synchronisation(cursor)
            
            # Synchroniser modules existants
            self.synchroniser_modules_existants(cursor)
            
            # Synchroniser nouveaux modules
            self.synchroniser_nouveaux_modules(cursor)
            
            # Synchroniser critères
            self.synchroniser_criteres_modules(cursor)
            
            # Mettre à jour configuration
            self.sauvegarder_configuration()
            
            conn.commit()
            conn.close()
            
            print(f"\n✅ SYNCHRONISATION TERMINÉE AVEC SUCCÈS !")
            return True
            
        except Exception as e:
            print(f"❌ Erreur synchronisation: {e}")
            return False
    
    def creer_tables_synchronisation(self, cursor):
        """Crée les tables nécessaires pour la synchronisation"""
        print(f"\n🗃️ CRÉATION DES TABLES DE SYNCHRONISATION")
        print("-" * 40)
        
        # Table des modules synchronisés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modules_synchronises (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT UNIQUE NOT NULL,
                couleur TEXT NOT NULL,
                icone TEXT NOT NULL,
                actif INTEGER DEFAULT 1,
                source TEXT DEFAULT 'nouveau',
                nb_criteres INTEGER DEFAULT 0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des critères synchronisés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS criteres_synchronises (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                module_nom TEXT NOT NULL,
                critere_nom TEXT NOT NULL,
                critere_type TEXT NOT NULL,
                critere_icone TEXT,
                critere_valeurs TEXT,
                obligatoire INTEGER DEFAULT 0,
                ordre INTEGER DEFAULT 0,
                tooltip TEXT,
                actif INTEGER DEFAULT 1,
                FOREIGN KEY (module_nom) REFERENCES modules_synchronises(nom)
            )
        ''')
        
        # Table de configuration générale
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS configuration_sync (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cle TEXT UNIQUE NOT NULL,
                valeur TEXT,
                description TEXT,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        print("✅ Tables de synchronisation créées")
    
    def synchroniser_modules_existants(self, cursor):
        """Synchronise vos modules existants"""
        print(f"\n🎯 SYNCHRONISATION MODULES EXISTANTS")
        print("-" * 40)
        
        modules_sync = 0
        
        for nom_module, config in self.modules_existants.items():
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO modules_synchronises
                    (nom, couleur, icone, actif, source, nb_criteres)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    nom_module,
                    config['couleur'],
                    config['icone'],
                    1 if config['actif'] else 0,
                    'existant',
                    config.get('criteres', 0)
                ))
                
                modules_sync += 1
                print(f"✅ Module existant synchronisé: {nom_module}")
                
            except Exception as e:
                print(f"❌ Erreur module {nom_module}: {e}")
        
        print(f"📊 {modules_sync} module(s) existant(s) synchronisé(s)")
    
    def synchroniser_nouveaux_modules(self, cursor):
        """Synchronise les nouveaux modules demandés"""
        print(f"\n🆕 SYNCHRONISATION NOUVEAUX MODULES")
        print("-" * 40)
        
        modules_sync = 0
        
        for nom_module, config in self.nouveaux_modules.items():
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO modules_synchronises
                    (nom, couleur, icone, actif, source, nb_criteres)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    nom_module,
                    config['couleur'],
                    config['icone'],
                    1 if config['actif'] else 0,
                    'nouveau',
                    0  # Critères à définir
                ))
                
                modules_sync += 1
                print(f"✅ Nouveau module synchronisé: {nom_module}")
                
            except Exception as e:
                print(f"❌ Erreur module {nom_module}: {e}")
        
        print(f"📊 {modules_sync} nouveau(x) module(s) synchronisé(s)")
    
    def synchroniser_criteres_modules(self, cursor):
        """Synchronise les critères pour tous les modules"""
        print(f"\n📋 SYNCHRONISATION DES CRITÈRES")
        print("-" * 40)
        
        # Critères pour modules spécifiques
        criteres_modules = {
            'Gestion des Biens': [
                {'nom': 'Code Bien', 'type': 'text', 'icone': '🔢', 'tooltip': 'Code automatique INV001'},
                {'nom': 'Désignation', 'type': 'text', 'icone': '📝', 'tooltip': 'Nom descriptif du bien'},
                {'nom': 'Catégorie', 'type': 'combo', 'icone': '📂', 'valeurs': ['Mobilier', 'Informatique', 'Véhicule', 'Équipement']},
                {'nom': 'Valeur', 'type': 'number', 'icone': '💰', 'tooltip': 'Valeur monétaire'},
                {'nom': 'État', 'type': 'combo', 'icone': '🔧', 'valeurs': ['Neuf', 'Bon', 'Moyen', 'Mauvais', 'Hors service']},
                {'nom': 'Localisation', 'type': 'combo', 'icone': '📍', 'valeurs': ['Bureau', 'Entrepôt', 'Atelier', 'Extérieur']},
                {'nom': 'Responsable', 'type': 'combo', 'icone': '👤', 'valeurs': ['Admin', 'Gestionnaire', 'Technicien']},
                {'nom': 'Date Acquisition', 'type': 'date', 'icone': '📅', 'tooltip': 'Date d\'acquisition'},
                {'nom': 'Observation', 'type': 'textarea', 'icone': '📋', 'tooltip': 'Remarques et observations'}
            ],
            'Fournisseurs': [
                {'nom': 'Code Fournisseur', 'type': 'text', 'icone': '🏢', 'tooltip': 'Code fournisseur'},
                {'nom': 'Raison Sociale', 'type': 'text', 'icone': '🏭', 'tooltip': 'Nom officiel'},
                {'nom': 'Type', 'type': 'combo', 'icone': '📂', 'valeurs': ['Local', 'National', 'International']},
                {'nom': 'Secteur Activité', 'type': 'combo', 'icone': '🎯', 'valeurs': ['Informatique', 'Mobilier', 'Services', 'Équipements']},
                {'nom': 'Téléphone', 'type': 'text', 'icone': '📞', 'tooltip': 'Numéro de téléphone'},
                {'nom': 'Email', 'type': 'text', 'icone': '📧', 'tooltip': 'Adresse email'},
                {'nom': 'Adresse', 'type': 'textarea', 'icone': '📍', 'tooltip': 'Adresse complète'},
                {'nom': 'Note Qualité', 'type': 'combo', 'icone': '⭐', 'valeurs': ['Excellent', 'Très Bon', 'Bon', 'Moyen', 'Faible']}
            ],
            'Inventaire': [
                {'nom': 'Code Article', 'type': 'text', 'icone': '📦', 'tooltip': 'Code avec code-barre'},
                {'nom': 'Désignation', 'type': 'text', 'icone': '📝', 'tooltip': 'Description article'},
                {'nom': 'Quantité Stock', 'type': 'number', 'icone': '📊', 'tooltip': 'Quantité en stock'},
                {'nom': 'Seuil Alerte', 'type': 'number', 'icone': '🚨', 'tooltip': 'Seuil d\'alerte'},
                {'nom': 'Prix Unitaire', 'type': 'number', 'icone': '💰', 'tooltip': 'Prix unitaire'},
                {'nom': 'Unité', 'type': 'combo', 'icone': '📏', 'valeurs': ['Pièce', 'Kg', 'Litre', 'Mètre', 'Boîte']},
                {'nom': 'Emplacement', 'type': 'combo', 'icone': '📍', 'valeurs': ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']},
                {'nom': 'Fournisseur', 'type': 'combo', 'icone': '🏢', 'valeurs': ['Fournisseur 1', 'Fournisseur 2']}
            ],
            'Parc Auto': [
                {'nom': 'Immatriculation', 'type': 'text', 'icone': '🚗', 'tooltip': 'Numéro d\'immatriculation'},
                {'nom': 'Marque', 'type': 'combo', 'icone': '🏭', 'valeurs': ['Renault', 'Peugeot', 'Citroën', 'Ford', 'Volkswagen']},
                {'nom': 'Modèle', 'type': 'text', 'icone': '🚙', 'tooltip': 'Modèle exact'},
                {'nom': 'Année', 'type': 'number', 'icone': '📅', 'tooltip': 'Année de mise en circulation'},
                {'nom': 'Kilométrage', 'type': 'number', 'icone': '🛣️', 'tooltip': 'Kilométrage actuel'},
                {'nom': 'Carburant', 'type': 'combo', 'icone': '⛽', 'valeurs': ['Essence', 'Diesel', 'Électrique', 'Hybride']},
                {'nom': 'Assurance', 'type': 'text', 'icone': '🛡️', 'tooltip': 'Compagnie d\'assurance'},
                {'nom': 'Contrôle Technique', 'type': 'date', 'icone': '🔧', 'tooltip': 'Date prochain contrôle'}
            ]
        }
        
        criteres_sync = 0
        
        for module_nom, criteres in criteres_modules.items():
            for i, critere in enumerate(criteres):
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO criteres_synchronises
                        (module_nom, critere_nom, critere_type, critere_icone, 
                         critere_valeurs, obligatoire, ordre, tooltip, actif)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        module_nom,
                        critere['nom'],
                        critere['type'],
                        critere['icone'],
                        json.dumps(critere.get('valeurs', [])) if critere.get('valeurs') else None,
                        critere.get('obligatoire', 0),
                        i,
                        critere.get('tooltip', ''),
                        1
                    ))
                    
                    criteres_sync += 1
                    
                except Exception as e:
                    print(f"❌ Erreur critère {critere['nom']}: {e}")
        
        print(f"📊 {criteres_sync} critère(s) synchronisé(s)")
    
    def sauvegarder_configuration(self):
        """Sauvegarde la configuration synchronisée"""
        print(f"\n💾 SAUVEGARDE CONFIGURATION")
        print("-" * 40)
        
        config_complete = {
            'modules_existants': self.modules_existants,
            'nouveaux_modules': self.nouveaux_modules,
            'date_synchronisation': datetime.now().isoformat(),
            'version': '2.0.0',
            'style_interface': 'noir_avec_fond_blanc',
            'codification': 'INV001',
            'symbole_monetaire': '€'
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_complete, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Configuration sauvegardée: {self.config_file}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
    
    def generer_rapport_synchronisation(self):
        """Génère un rapport de synchronisation"""
        print(f"\n📊 RAPPORT DE SYNCHRONISATION")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Compter les modules
            cursor.execute("SELECT COUNT(*) FROM modules_synchronises WHERE actif = 1")
            nb_modules = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM modules_synchronises WHERE source = 'existant'")
            nb_existants = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM modules_synchronises WHERE source = 'nouveau'")
            nb_nouveaux = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM criteres_synchronises WHERE actif = 1")
            nb_criteres = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ Modules synchronisés: {nb_modules}")
            print(f"  📁 Modules existants: {nb_existants}")
            print(f"  🆕 Nouveaux modules: {nb_nouveaux}")
            print(f"✅ Critères synchronisés: {nb_criteres}")
            print(f"✅ Configuration sauvegardée")
            print(f"✅ Base de données mise à jour")
            
            print(f"\n🎯 SYNCHRONISATION RÉUSSIE !")
            print(f"Tous vos modules et critères sont opérationnels")
            
        except Exception as e:
            print(f"❌ Erreur rapport: {e}")

def main():
    """Fonction principale de synchronisation"""
    print("🔄 SYNCHRONISATION COMPLÈTE DES MODULES GESTIMMOB")
    print("=" * 80)
    
    synchronisateur = SynchronisateurModules()
    
    # Exécuter la synchronisation
    if synchronisateur.synchroniser_tous_modules():
        synchronisateur.generer_rapport_synchronisation()
        
        print(f"\n🎉 SYNCHRONISATION TERMINÉE AVEC SUCCÈS !")
        print(f"Tous vos modules et critères sont maintenant synchronisés")
        print(f"L'interface complète est prête à être utilisée")
    else:
        print(f"\n❌ ERREUR LORS DE LA SYNCHRONISATION")

if __name__ == "__main__":
    main()
