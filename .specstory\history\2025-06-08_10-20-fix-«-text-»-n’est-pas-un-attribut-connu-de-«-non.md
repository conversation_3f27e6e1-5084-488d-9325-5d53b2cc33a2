<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:20:58)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème vient du fait que `self.table.horizontalHeaderItem(i)` peut retourner `None` si aucun en-tête n'est défini pour la colonne `i`. Il faut donc vérifier que l'objet n'est pas `None` avant d'accéder à `.text()`.

Corrigez la ligne concernée en ajoutant une vérification conditionnelle.

````python
# ...existing code...
if self.chk_entete.isChecked():
    f.write(
        "\t".join([
            self.table.horizontalHeaderItem(i).text() if self.table.horizontalHeaderItem(i) is not None else ""
            for i in range(self.table.columnCount())
        ]) + "\n"
    )
# ...existing code...
````

---

