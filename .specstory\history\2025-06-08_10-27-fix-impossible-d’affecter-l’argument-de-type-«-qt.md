<!-- Generated by SpecStory -->

# /fix Impossible d’affecter l’argument de type « QT (2025-06-08 11:27:54)

_**User**_

/fix Impossible d’affecter l’argument de type « QTableWidgetItem | None » au paramètre « item » de type « QTableWidgetItem » dans la fonction « safe_text »
  Le type « QTableWidgetItem | None » n’est pas assignable au type « QTableWidgetItem »
    « None » n’est pas assignable à « QTableWidgetItem »

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l’erreur de typage, il faut modifier la fonction `safe_text` pour accepter un argument de type `QTableWidgetItem | None` (donc optionnel), et gérer explicitement le cas où l’item est `None`. Cela évite l’erreur de type lors de l’appel avec `self.table.item(row, 1)` qui peut retourner `None`.

Voici la modification à apporter :

````python
# ...existing code...
        def safe_text(item) -> str:
            if item is None:
                return ""
            text_attr = getattr(item, "text", None)
            if callable(text_attr):
                try:
                    v = text_attr()
                    if v is not None:
                        return str(v)
                except Exception:
                    return ""
            return ""
        designation = QLineEdit(safe_text(self.table.item(row, 1)))
# ...existing code...
````

---

