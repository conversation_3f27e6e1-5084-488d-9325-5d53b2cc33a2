<!-- Generated by SpecStory -->

# /fix La condition prend toujours la valeur True, c (2025-06-08 11:27:00)

_**User**_

/fix La condition prend toujours la valeur True, car les types « QTableWidgetItem » et « None » ne se chevauchent pas

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Le problème vient du fait que `item` est typé comme `QTableWidgetItem` et ne peut donc jamais être `None selon l'analyseur statique. Pour corriger cela, il faut vérifier explicitement si `item` est une instance de `QTableWidgetItem` avant d'utiliser ses méthodes, ce qui est plus sûr et conforme à l'intention.

Voici la correction à appliquer dans la fonction concernée (par exemple dans `safe_text` ou tout autre endroit similaire) :

````python
# ...existing code...
def safe_text(item: 'QTableWidgetItem') -> str:
    if isinstance(item, QTableWidgetItem):
        text_attr = getattr(item, "text", None)
        if callable(text_attr):
            try:
                v = text_attr()
                if v is not None:
                    return str(v)
            except Exception:
                return ""
    return ""
# ...existing code...
````

Remplacez toute condition du type `if item is not None:` par `if isinstance(item, QTableWidgetItem):` dans ce contexte. Cela évite l'avertissement et rend le code plus robuste.

---

