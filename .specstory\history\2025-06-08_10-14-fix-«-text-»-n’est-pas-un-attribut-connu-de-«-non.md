<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:14:22)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Le problème vient du fait que `self.table.horizontalHeaderItem(i)` peut retourner `None`, et dans ce cas, il ne faut pas appeler `.text()` dessus. La correction consiste à vérifier que l'objet n'est pas `None` avant d'appeler `.text()`. Le code sélectionné est déjà correct, mais pour plus de robustesse et de lisibilité, on peut stocker l'objet dans une variable temporaire.

````python
# ...existing code...
def exporter_csv(self):
    path = "fournisseurs_export.csv"
    with open(path, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        # Correction ici : éviter d'appeler .text() sur None
        writer.writerow([
            (header.text() if header is not None else "")
            for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
        ])
        for row in range(self.table.rowCount()):
            writer.writerow([
                self.table.item(row, col).text() if self.table.item(row, col) else ""
                for col in range(self.table.columnCount())
            ])
    QMessageBox.information(self, "Export CSV", f"Export effectué : {path}")
# ...existing code...
````

---

