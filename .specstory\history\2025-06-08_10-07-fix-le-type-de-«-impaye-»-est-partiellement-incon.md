<!-- Generated by SpecStory -->

# /fix Le type de « impaye » est partiellement incon (2025-06-08 11:07:57)

_**User**_

/fix Le type de « impaye » est partiellement inconnu
  Le type de "impaye" est "Column[Unknown]"

---

_**Assistant**_

### [models.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/database/models.py)

Corrigez l'annotation de type pour le champ `impaye` afin d'utiliser `Column` au lieu de `float`. Cela évite l'erreur de type partiellement inconnu.

````python
# ...existing code...
    # historique_factures = Column(Text)
    # Correction du type d'annotation pour impaye
    impaye = Column(DECIMAL(18,2), nullable=True)
    date_creation = Column(DateTime, default=lambda: datetime.now(timezone.utc))
# ...existing code...
````

---

