# Création d'un exécutable Windows pour le logiciel de gestion immobilière

1. Ouvrez l'invite de commandes Windows (cmd).
2. Placez-vous dans le dossier du projet :
   cd C:\Users\<USER>\Desktop\logiciel GestImmob\gestion-software
3. Activez votre environnement virtuel si besoin :
   .venv\Scripts\activate
4. Installez PyInstaller si ce n'est pas déjà fait :
   pip install pyinstaller
5. Générez l'exécutable avec la commande :
   pyinstaller --onefile --name=GestImmob src/main.py
6. L'exécutable sera créé dans le dossier `dist` sous le nom `GestImmob.exe`.
7. Double-cliquez sur `dist\GestImmob.exe` pour lancer le logiciel.

**Remarque :**  
Si votre application utilise des fichiers de configuration ou des ressources externes, copiez-les dans le même dossier que l'exécutable ou adaptez leur chemin dans le code.
