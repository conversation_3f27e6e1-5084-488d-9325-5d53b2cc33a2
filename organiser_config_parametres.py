#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Organisation complète des configurations et paramètres avec sauvegarde automatique
"""

import os
import json
import shutil
import datetime
from pathlib import Path
from typing import Dict, List, Any

class OrganisateurConfigParametres:
    def __init__(self):
        self.sauvegarde_effectuee = False
        self.modifications_appliquees = []
        self.erreurs_rencontrees = []
        
    def creer_sauvegarde_complete(self):
        """Crée une sauvegarde complète avant toute modification"""
        print("💾 CRÉATION DE SAUVEGARDE COMPLÈTE")
        print("=" * 50)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = Path(f"backup_avant_organisation_{timestamp}")
        
        try:
            # Créer le répertoire de sauvegarde
            backup_dir.mkdir(exist_ok=True)
            
            # Sauvegarder tous les fichiers importants
            fichiers_a_sauvegarder = [
                'src/',
                'config/',
                'data/',
                '*.py',
                '*.json',
                '*.sql',
                '*.md',
                '*.txt'
            ]
            
            print(f"📁 Répertoire de sauvegarde: {backup_dir}")
            
            # Copier les répertoires
            for item in ['src', 'config', 'data', 'docs', 'tools']:
                if os.path.exists(item):
                    if os.path.isdir(item):
                        shutil.copytree(item, backup_dir / item, dirs_exist_ok=True)
                        print(f"✅ Sauvegardé: {item}/")
                    else:
                        shutil.copy2(item, backup_dir / item)
                        print(f"✅ Sauvegardé: {item}")
            
            # Copier les fichiers racine
            for file_path in Path('.').glob('*.py'):
                shutil.copy2(file_path, backup_dir / file_path.name)
                print(f"✅ Sauvegardé: {file_path}")
            
            for file_path in Path('.').glob('*.json'):
                shutil.copy2(file_path, backup_dir / file_path.name)
                print(f"✅ Sauvegardé: {file_path}")
            
            self.sauvegarde_effectuee = True
            print(f"\n🎉 SAUVEGARDE COMPLÈTE RÉUSSIE !")
            print(f"📁 Emplacement: {backup_dir}")
            
            return str(backup_dir)
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            self.erreurs_rencontrees.append(f"Erreur sauvegarde: {e}")
            return None
    
    def creer_structure_config_organisee(self):
        """Crée une structure organisée pour les configurations"""
        print(f"\n📁 CRÉATION DE LA STRUCTURE CONFIG ORGANISÉE")
        print("=" * 50)
        
        # Structure hiérarchique des configurations
        structure_config = {
            'config/': {
                '01_application/': {
                    'app_settings.json': 'Paramètres généraux de l\'application',
                    'ui_config.json': 'Configuration interface utilisateur',
                    'themes.json': 'Thèmes et apparence',
                    'languages.json': 'Configuration multilingue'
                },
                '02_database/': {
                    'db_config.json': 'Configuration base de données',
                    'connection_pools.json': 'Pools de connexions',
                    'migration_config.json': 'Configuration migrations',
                    'backup_config.json': 'Configuration sauvegardes BDD'
                },
                '03_security/': {
                    'auth_config.json': 'Configuration authentification',
                    'encryption_config.json': 'Configuration chiffrement',
                    'permissions.json': 'Gestion des permissions',
                    'security_policies.json': 'Politiques de sécurité'
                },
                '04_modules/': {
                    'inventaire_config.json': 'Configuration module inventaire',
                    'fournisseur_config.json': 'Configuration module fournisseurs',
                    'document_config.json': 'Configuration module documents',
                    'reforme_config.json': 'Configuration module réformes',
                    'recherche_config.json': 'Configuration module recherche'
                },
                '05_integration/': {
                    'api_config.json': 'Configuration APIs externes',
                    'export_config.json': 'Configuration exports',
                    'import_config.json': 'Configuration imports',
                    'sync_config.json': 'Configuration synchronisation'
                },
                '06_logging/': {
                    'logging_config.json': 'Configuration logs',
                    'audit_config.json': 'Configuration audit',
                    'monitoring_config.json': 'Configuration monitoring',
                    'alerts_config.json': 'Configuration alertes'
                },
                '07_performance/': {
                    'cache_config.json': 'Configuration cache',
                    'optimization_config.json': 'Configuration optimisations',
                    'memory_config.json': 'Configuration mémoire',
                    'threading_config.json': 'Configuration threads'
                },
                '08_deployment/': {
                    'environment_config.json': 'Configuration environnements',
                    'deployment_config.json': 'Configuration déploiement',
                    'maintenance_config.json': 'Configuration maintenance',
                    'update_config.json': 'Configuration mises à jour'
                }
            }
        }
        
        # Créer la structure
        for repertoire, contenu in structure_config.items():
            self._creer_repertoire_recursif(repertoire, contenu)
        
        print(f"\n✅ Structure config organisée créée !")
        self.modifications_appliquees.append("Structure config organisée créée")
    
    def _creer_repertoire_recursif(self, chemin: str, contenu: Dict):
        """Crée récursivement la structure de répertoires"""
        Path(chemin).mkdir(parents=True, exist_ok=True)
        print(f"📁 Créé: {chemin}")
        
        for nom, description in contenu.items():
            chemin_complet = Path(chemin) / nom
            
            if nom.endswith('/'):
                # C'est un répertoire
                if isinstance(description, dict):
                    self._creer_repertoire_recursif(str(chemin_complet), description)
            else:
                # C'est un fichier
                if not chemin_complet.exists():
                    self._creer_fichier_config(chemin_complet, description)
    
    def _creer_fichier_config(self, chemin: Path, description: str):
        """Crée un fichier de configuration avec contenu par défaut"""
        try:
            # Contenu par défaut basé sur le type de fichier
            contenu_defaut = self._generer_contenu_config(chemin.name, description)
            
            with open(chemin, 'w', encoding='utf-8') as f:
                json.dump(contenu_defaut, f, indent=2, ensure_ascii=False)
            
            print(f"📄 Créé: {chemin}")
            
        except Exception as e:
            print(f"❌ Erreur création {chemin}: {e}")
            self.erreurs_rencontrees.append(f"Erreur création {chemin}: {e}")
    
    def _generer_contenu_config(self, nom_fichier: str, description: str) -> Dict[str, Any]:
        """Génère le contenu par défaut pour un fichier de configuration"""
        
        # Configuration de base commune
        config_base = {
            "_metadata": {
                "description": description,
                "version": "1.0.0",
                "created": datetime.datetime.now().isoformat(),
                "last_modified": datetime.datetime.now().isoformat()
            }
        }
        
        # Configurations spécifiques par type
        if 'app_settings' in nom_fichier:
            config_base.update({
                "application": {
                    "name": "GestImmob",
                    "version": "1.0.0",
                    "debug": False,
                    "auto_save": True,
                    "auto_save_interval": 300
                },
                "startup": {
                    "show_splash": True,
                    "check_updates": True,
                    "restore_session": True
                }
            })
        
        elif 'ui_config' in nom_fichier:
            config_base.update({
                "window": {
                    "width": 1200,
                    "height": 800,
                    "maximized": False,
                    "remember_size": True
                },
                "interface": {
                    "show_toolbar": True,
                    "show_statusbar": True,
                    "show_sidebar": True
                }
            })
        
        elif 'db_config' in nom_fichier:
            config_base.update({
                "database": {
                    "type": "sqlite",
                    "path": "data/gestimmob.db",
                    "timeout": 30,
                    "pool_size": 5
                },
                "backup": {
                    "auto_backup": True,
                    "backup_interval": 24,
                    "max_backups": 10
                }
            })
        
        elif 'auth_config' in nom_fichier:
            config_base.update({
                "authentication": {
                    "method": "local",
                    "session_timeout": 3600,
                    "max_attempts": 3,
                    "lockout_duration": 300
                },
                "password_policy": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_numbers": True,
                    "require_special": True
                }
            })
        
        elif 'logging_config' in nom_fichier:
            config_base.update({
                "logging": {
                    "level": "INFO",
                    "file_path": "data/logs/gestimmob.log",
                    "max_size": "10MB",
                    "backup_count": 5
                },
                "audit": {
                    "enabled": True,
                    "log_user_actions": True,
                    "log_data_changes": True
                }
            })
        
        elif any(module in nom_fichier for module in ['inventaire', 'fournisseur', 'document', 'reforme', 'recherche']):
            module_name = nom_fichier.split('_')[0]
            config_base.update({
                "module": {
                    "enabled": True,
                    "auto_load": True,
                    "cache_enabled": True
                },
                "features": {
                    "export_enabled": True,
                    "import_enabled": True,
                    "search_enabled": True
                },
                "validation": {
                    "strict_mode": False,
                    "auto_correct": True
                }
            })
        
        return config_base
    
    def reorganiser_modules_sans_conflit(self):
        """Réorganise les modules pour éviter les conflits"""
        print(f"\n🔧 RÉORGANISATION DES MODULES SANS CONFLIT")
        print("=" * 50)
        
        # Mapping des modules avec isolation
        modules_mapping = {
            'src/modules/': {
                'inventaire/': {
                    '__init__.py': 'Module inventaire',
                    'inventaire_core.py': 'Logique métier inventaire',
                    'inventaire_ui.py': 'Interface inventaire',
                    'inventaire_config.py': 'Configuration inventaire',
                    'inventaire_utils.py': 'Utilitaires inventaire'
                },
                'fournisseur/': {
                    '__init__.py': 'Module fournisseur',
                    'fournisseur_core.py': 'Logique métier fournisseur',
                    'fournisseur_ui.py': 'Interface fournisseur',
                    'fournisseur_config.py': 'Configuration fournisseur',
                    'fournisseur_utils.py': 'Utilitaires fournisseur'
                },
                'document/': {
                    '__init__.py': 'Module document',
                    'document_core.py': 'Logique métier document',
                    'document_ui.py': 'Interface document',
                    'document_config.py': 'Configuration document',
                    'document_utils.py': 'Utilitaires document'
                },
                'reforme/': {
                    '__init__.py': 'Module réforme',
                    'reforme_core.py': 'Logique métier réforme',
                    'reforme_ui.py': 'Interface réforme',
                    'reforme_config.py': 'Configuration réforme',
                    'reforme_utils.py': 'Utilitaires réforme'
                },
                'recherche/': {
                    '__init__.py': 'Module recherche',
                    'recherche_core.py': 'Logique métier recherche',
                    'recherche_ui.py': 'Interface recherche',
                    'recherche_config.py': 'Configuration recherche',
                    'recherche_utils.py': 'Utilitaires recherche'
                }
            }
        }
        
        # Créer la structure modulaire
        for repertoire, contenu in modules_mapping.items():
            self._creer_structure_modulaire(repertoire, contenu)
        
        # Déplacer les fichiers existants
        self._deplacer_modules_existants()
        
        print(f"\n✅ Modules réorganisés sans conflit !")
        self.modifications_appliquees.append("Modules réorganisés sans conflit")
    
    def _creer_structure_modulaire(self, chemin: str, contenu: Dict):
        """Crée la structure modulaire"""
        for nom, description in contenu.items():
            chemin_complet = Path(chemin) / nom
            
            if nom.endswith('/'):
                # Répertoire de module
                chemin_complet.mkdir(parents=True, exist_ok=True)
                print(f"📁 Module: {chemin_complet}")
                
                if isinstance(description, dict):
                    self._creer_structure_modulaire(str(chemin_complet), description)
            else:
                # Fichier de module
                if not chemin_complet.exists():
                    self._creer_fichier_module(chemin_complet, description)
    
    def _creer_fichier_module(self, chemin: Path, description: str):
        """Crée un fichier de module avec structure appropriée"""
        try:
            contenu = self._generer_contenu_module(chemin.name, description)
            
            with open(chemin, 'w', encoding='utf-8') as f:
                f.write(contenu)
            
            print(f"📄 Module: {chemin}")
            
        except Exception as e:
            print(f"❌ Erreur création module {chemin}: {e}")
            self.erreurs_rencontrees.append(f"Erreur création module {chemin}: {e}")
    
    def _generer_contenu_module(self, nom_fichier: str, description: str) -> str:
        """Génère le contenu pour un fichier de module"""
        
        if nom_fichier == '__init__.py':
            return f'''# -*- coding: utf-8 -*-
"""
{description}
"""

__version__ = "1.0.0"
__author__ = "GestImmob Team"

# Imports du module
from .{nom_fichier.replace("__init__.py", "").rstrip("/")}core import *
from .{nom_fichier.replace("__init__.py", "").rstrip("/")}ui import *
from .{nom_fichier.replace("__init__.py", "").rstrip("/")}config import *
'''
        
        elif '_core.py' in nom_fichier:
            module_name = nom_fichier.replace('_core.py', '')
            return f'''# -*- coding: utf-8 -*-
"""
{description}
Logique métier du module {module_name}
"""

from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class {module_name.capitalize()}Core:
    """Classe principale pour la logique métier {module_name}"""
    
    def __init__(self):
        self.config = None
        self.data = {{}}
        logger.info(f"Module {module_name} initialisé")
    
    def initialize(self) -> bool:
        """Initialise le module"""
        try:
            # Logique d'initialisation
            return True
        except Exception as e:
            logger.error(f"Erreur initialisation {module_name}: {{e}}")
            return False
    
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Traite les données du module"""
        try:
            # Logique de traitement
            return {{"status": "success", "data": data}}
        except Exception as e:
            logger.error(f"Erreur traitement {module_name}: {{e}}")
            return {{"status": "error", "message": str(e)}}
'''
        
        elif '_config.py' in nom_fichier:
            module_name = nom_fichier.replace('_config.py', '')
            return f'''# -*- coding: utf-8 -*-
"""
{description}
Configuration du module {module_name}
"""

import json
from pathlib import Path
from typing import Dict, Any

class {module_name.capitalize()}Config:
    """Gestionnaire de configuration pour le module {module_name}"""
    
    def __init__(self):
        self.config_path = Path("config/04_modules/{module_name}_config.json")
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Charge la configuration du module"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return self.get_default_config()
        except Exception as e:
            print(f"Erreur chargement config {module_name}: {{e}}")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Retourne la configuration par défaut"""
        return {{
            "module": {{
                "enabled": True,
                "auto_load": True,
                "cache_enabled": True
            }},
            "features": {{
                "export_enabled": True,
                "import_enabled": True,
                "search_enabled": True
            }}
        }}
    
    def save_config(self) -> bool:
        """Sauvegarde la configuration"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Erreur sauvegarde config {module_name}: {{e}}")
            return False
'''
        
        else:
            # Fichier générique
            return f'''# -*- coding: utf-8 -*-
"""
{description}
"""

from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

# Code du module ici
'''
    
    def _deplacer_modules_existants(self):
        """Déplace les modules existants vers la nouvelle structure"""
        print(f"\n🔄 Déplacement des modules existants...")
        
        modules_existants = {
            'src/inventaire.py': 'src/modules/inventaire/inventaire_ui.py',
            'src/fournisseur.py': 'src/modules/fournisseur/fournisseur_ui.py',
            'src/document.py': 'src/modules/document/document_ui.py',
            'src/reforme.py': 'src/modules/reforme/reforme_ui.py',
            'src/recherche.py': 'src/modules/recherche/recherche_ui.py'
        }
        
        for source, destination in modules_existants.items():
            if os.path.exists(source):
                try:
                    # Créer le répertoire de destination
                    Path(destination).parent.mkdir(parents=True, exist_ok=True)
                    
                    # Déplacer le fichier
                    shutil.move(source, destination)
                    print(f"✅ Déplacé: {source} → {destination}")
                    self.modifications_appliquees.append(f"Module déplacé: {source} → {destination}")
                    
                except Exception as e:
                    print(f"❌ Erreur déplacement {source}: {e}")
                    self.erreurs_rencontrees.append(f"Erreur déplacement {source}: {e}")
    
    def creer_gestionnaire_config_central(self):
        """Crée un gestionnaire central de configuration"""
        print(f"\n⚙️ CRÉATION DU GESTIONNAIRE CONFIG CENTRAL")
        print("-" * 50)
        
        gestionnaire_content = '''# -*- coding: utf-8 -*-
"""
Gestionnaire central de configuration
Évite les conflits et centralise la gestion des paramètres
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """Gestionnaire central de toutes les configurations"""
    
    _instance = None
    _configs = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config_root = Path("config")
            self.initialized = True
            self.load_all_configs()
    
    def load_all_configs(self):
        """Charge toutes les configurations"""
        try:
            config_dirs = [
                "01_application",
                "02_database", 
                "03_security",
                "04_modules",
                "05_integration",
                "06_logging",
                "07_performance",
                "08_deployment"
            ]
            
            for config_dir in config_dirs:
                self.load_config_directory(config_dir)
                
            logger.info("Toutes les configurations chargées")
            
        except Exception as e:
            logger.error(f"Erreur chargement configurations: {e}")
    
    def load_config_directory(self, directory: str):
        """Charge les configurations d'un répertoire"""
        dir_path = self.config_root / directory
        if dir_path.exists():
            for config_file in dir_path.glob("*.json"):
                self.load_config_file(directory, config_file)
    
    def load_config_file(self, category: str, file_path: Path):
        """Charge un fichier de configuration"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            config_name = file_path.stem
            self._configs[f"{category}.{config_name}"] = config_data
            
        except Exception as e:
            logger.error(f"Erreur chargement {file_path}: {e}")
    
    def get_config(self, config_path: str, default: Any = None) -> Any:
        """Récupère une configuration"""
        try:
            keys = config_path.split('.')
            config = self._configs
            
            for key in keys:
                if isinstance(config, dict) and key in config:
                    config = config[key]
                else:
                    return default
            
            return config
            
        except Exception as e:
            logger.error(f"Erreur récupération config {config_path}: {e}")
            return default
    
    def set_config(self, config_path: str, value: Any) -> bool:
        """Définit une configuration"""
        try:
            keys = config_path.split('.')
            config = self._configs
            
            # Naviguer jusqu'au dernier niveau
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # Définir la valeur
            config[keys[-1]] = value
            
            # Sauvegarder
            return self.save_config(keys[0])
            
        except Exception as e:
            logger.error(f"Erreur définition config {config_path}: {e}")
            return False
    
    def save_config(self, category: str) -> bool:
        """Sauvegarde une catégorie de configuration"""
        try:
            # Logique de sauvegarde
            return True
        except Exception as e:
            logger.error(f"Erreur sauvegarde config {category}: {e}")
            return False

# Instance globale
config_manager = ConfigManager()
'''
        
        try:
            gestionnaire_path = Path('src/core/config_manager.py')
            gestionnaire_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(gestionnaire_path, 'w', encoding='utf-8') as f:
                f.write(gestionnaire_content)
            
            print(f"✅ Gestionnaire central créé: {gestionnaire_path}")
            self.modifications_appliquees.append("Gestionnaire central de configuration créé")
            
        except Exception as e:
            print(f"❌ Erreur création gestionnaire: {e}")
            self.erreurs_rencontrees.append(f"Erreur création gestionnaire: {e}")
    
    def generer_rapport_organisation(self):
        """Génère un rapport complet de l'organisation"""
        print(f"\n📊 RAPPORT D'ORGANISATION CONFIG/PARAMÈTRES")
        print("=" * 60)
        
        print(f"💾 Sauvegarde effectuée: {'✅ Oui' if self.sauvegarde_effectuee else '❌ Non'}")
        print(f"✅ Modifications appliquées: {len(self.modifications_appliquees)}")
        print(f"❌ Erreurs rencontrées: {len(self.erreurs_rencontrees)}")
        
        if self.modifications_appliquees:
            print(f"\n✅ MODIFICATIONS APPLIQUÉES:")
            for modif in self.modifications_appliquees:
                print(f"  - {modif}")
        
        if self.erreurs_rencontrees:
            print(f"\n❌ ERREURS RENCONTRÉES:")
            for erreur in self.erreurs_rencontrees:
                print(f"  - {erreur}")
        
        succes = len(self.erreurs_rencontrees) == 0
        
        if succes:
            print(f"\n🎉 ORGANISATION RÉUSSIE !")
            print(f"✅ Configurations organisées par ordre")
            print(f"✅ Modules isolés sans conflit")
            print(f"✅ Gestionnaire central créé")
            print(f"✅ Sauvegarde de sécurité effectuée")
        else:
            print(f"\n⚠️ ORGANISATION PARTIELLE")
            print(f"Consultez les erreurs ci-dessus")
        
        return succes

def main():
    """Fonction principale d'organisation"""
    print("⚙️ ORGANISATION COMPLÈTE CONFIG/PARAMÈTRES")
    print("=" * 80)
    print("Organisation des configurations et paramètres avec sauvegarde")
    
    organisateur = OrganisateurConfigParametres()
    
    try:
        # Étape 1: Sauvegarde complète
        backup_path = organisateur.creer_sauvegarde_complete()
        if not backup_path:
            print("❌ Impossible de créer la sauvegarde - Arrêt")
            return False
        
        # Étape 2: Structure config organisée
        organisateur.creer_structure_config_organisee()
        
        # Étape 3: Réorganisation modules
        organisateur.reorganiser_modules_sans_conflit()
        
        # Étape 4: Gestionnaire central
        organisateur.creer_gestionnaire_config_central()
        
        # Étape 5: Rapport final
        succes = organisateur.generer_rapport_organisation()
        
        if succes:
            print(f"\n🏆 ORGANISATION TERMINÉE AVEC SUCCÈS !")
            print(f"💾 Sauvegarde disponible: {backup_path}")
        else:
            print(f"\n⚠️ ORGANISATION PARTIELLE")
            print(f"💾 Restauration possible depuis: {backup_path}")
        
        return succes
        
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        if organisateur.sauvegarde_effectuee:
            print(f"💾 Restauration possible depuis la sauvegarde")
        return False

if __name__ == "__main__":
    main()
