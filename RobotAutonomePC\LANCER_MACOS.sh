#!/bin/bash
# 🍎 Robot IA Universel - macOS
# Compatible: macOS 10.14+ (Mojave, Catalina, Big Sur, Monterey, Ventura, Sonoma)

clear
echo "████████████████████████████████████████████████████████████████████████████████"
echo "██                                                                            ██"
echo "██    🍎 ROBOT IA UNIVERSEL - MACOS                                          ██"
echo "██    👤 SamNord@110577 - Compatible macOS 10.14+                           ██"
echo "██                                                                            ██"
echo "████████████████████████████████████████████████████████████████████████████████"
echo ""

echo "🍎 ROBOT IA UNIVERSEL POUR MACOS"
echo "================================="
echo "👤 Utilisateur: SamNord@110577"
echo "🔑 Mot de passe: NorDine@22"
echo "💻 Système: macOS"
echo ""

echo "🔄 Vérification environnement macOS..."

# Vérification version macOS
macos_version=$(sw_vers -productVersion)
echo "✅ Version macOS: $macos_version"

# Vérification architecture (Intel vs Apple Silicon)
arch=$(uname -m)
if [[ "$arch" == "arm64" ]]; then
    echo "✅ Architecture: Apple Silicon (M1/M2)"
elif [[ "$arch" == "x86_64" ]]; then
    echo "✅ Architecture: Intel x64"
else
    echo "✅ Architecture: $arch"
fi

# Vérification Python
if command -v python3 &> /dev/null; then
    echo "✅ Python3 détecté"
    echo "   Version: $(python3 --version)"
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    echo "✅ Python détecté"
    echo "   Version: $(python --version)"
    PYTHON_CMD="python"
else
    echo "❌ Python non détecté"
    echo "📥 Installation Python requise"
    echo ""
    echo "🔧 INSTALLATION PYTHON SUR MACOS:"
    echo "1. Homebrew: brew install python3"
    echo "2. Site officiel: https://python.org/downloads/macos/"
    echo "3. Xcode Command Line Tools: xcode-select --install"
    echo ""
    read -p "Voulez-vous installer Python avec Homebrew ? (o/N): " install
    if [[ $install =~ ^[Oo]$ ]]; then
        # Vérification Homebrew
        if command -v brew &> /dev/null; then
            echo "🍺 Installation Python via Homebrew..."
            brew install python3
        else
            echo "❌ Homebrew non installé"
            echo "🍺 Installation Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            brew install python3
        fi
    else
        echo "💡 Installez Python manuellement depuis python.org"
        open "https://python.org/downloads/macos/"
    fi
    exit 1
fi

# Vérification Homebrew (optionnel)
if command -v brew &> /dev/null; then
    echo "✅ Homebrew disponible"
else
    echo "⚠️ Homebrew non installé (optionnel)"
fi

echo ""
echo "🚀 LANCEMENT ROBOT IA UNIVERSEL..."

# Tentative application macOS (.app)
if [ -d "executables/RobotIA_SamNord_macOS.app" ]; then
    echo "🍎 Lancement application macOS..."
    open "executables/RobotIA_SamNord_macOS.app"
    exit 0
fi

# Tentative exécutable macOS
if [ -f "executables/RobotIA_SamNord_macOS" ]; then
    echo "🍎 Lancement exécutable macOS..."
    chmod +x "executables/RobotIA_SamNord_macOS"
    ./executables/RobotIA_SamNord_macOS
    exit 0
fi

# Tentative Python universel
if [ -f "robot_ia_universel.py" ]; then
    echo "🐍 Lancement version Python universelle..."
    $PYTHON_CMD robot_ia_universel.py
    exit 0
fi

# Fallback application principale
if [ -f "robot_autonome_pc.py" ]; then
    echo "🤖 Lancement application principale..."
    $PYTHON_CMD robot_autonome_pc.py
    exit 0
fi

echo "❌ Aucune version disponible !"
echo "🔧 Vérifiez l'installation"
read -p "Appuyez sur Entrée pour continuer..."
exit 1
