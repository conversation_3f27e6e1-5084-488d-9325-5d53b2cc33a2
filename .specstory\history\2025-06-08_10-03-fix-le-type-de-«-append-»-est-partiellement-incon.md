<!-- Generated by SpecStory -->

# /fix Le type de « append » est partiellement incon (2025-06-08 11:03:54)

_**User**_

/fix Le type de « append » est partiellement inconnu
  Le type de "append" est "(object: Unknown, /) -> None"

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Le problème vient du typage de la variable `details`. Il faut préciser à l'avance que c'est une liste de chaînes de caractères pour que l'éditeur/l'outil de typage comprenne le type de `append`. Ajoutez une annotation de type explicite pour `details`.

````python
# ...existing code...
elif expr.lower().startswith("amortdeg(") and expr.endswith(")"):
    # Syntaxe : amortdeg(valeur, taux, duree_mois)
    params = expr[9:-1].split(",")
    if len(params) == 3:
        valeur = float(params[0])
        taux = float(params[1]) / 100
        duree = int(params[2])
        montant = valeur
        details: list[str] = []  # Correction : annotation explicite du type
        for mois in range(1, duree + 1):
            amort = montant * taux
            montant -= amort
            details.append(f"Mois {mois}: {amort:.2f}")
        msg = " | ".join(details)
        self.calculs.append(expr + " = " + msg)
        self.display.setText(str(amort))
        self._update_history()
        return
# ...existing code...
````

---

