@echo off
title 🔍 RECHERCHE DOSSIER RobotAutonomePC
color 0B
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🔍 RECHERCHE DOSSIER RobotAutonomePC                                    ██
echo ██    📁 LOCALISATION AUTOMATIQUE DU DOSSIER PRINCIPAL                       ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🔍 RECHERCHE DU DOSSIER RobotAutonomePC...
echo 📁 Scan de tous les emplacements possibles
echo 🖥️ Vérification système complet
echo.

echo 📍 EMPLACEMENTS À VÉRIFIER:
echo 🖥️ Bureau
echo 📁 Documents  
echo 💾 Téléchargements
echo 🗂️ Dossiers utilisateur
echo 💿 Disques locaux
echo.

echo 🔄 RECHERCHE EN COURS...
echo.

REM Variables
set "TROUVE=0"
set "EMPLACEMENTS="

echo 🔄 1. Vérification Bureau...
for /d %%i in ("%USERPROFILE%\Desktop\*Robot*") do (
    echo ✅ TROUVÉ sur Bureau: %%i
    set "EMPLACEMENTS=%EMPLACEMENTS%%%i;"
    set "TROUVE=1"
)

echo 🔄 2. Vérification Documents...
for /d %%i in ("%USERPROFILE%\Documents\*Robot*") do (
    echo ✅ TROUVÉ dans Documents: %%i
    set "EMPLACEMENTS=%EMPLACEMENTS%%%i;"
    set "TROUVE=1"
)

echo 🔄 3. Vérification Téléchargements...
for /d %%i in ("%USERPROFILE%\Downloads\*Robot*") do (
    echo ✅ TROUVÉ dans Téléchargements: %%i
    set "EMPLACEMENTS=%EMPLACEMENTS%%%i;"
    set "TROUVE=1"
)

echo 🔄 4. Recherche dans profil utilisateur...
for /d %%i in ("%USERPROFILE%\*Robot*") do (
    echo ✅ TROUVÉ dans profil: %%i
    set "EMPLACEMENTS=%EMPLACEMENTS%%%i;"
    set "TROUVE=1"
)

echo 🔄 5. Recherche disque C:...
for /d %%i in ("C:\*Robot*") do (
    echo ✅ TROUVÉ sur C:: %%i
    set "EMPLACEMENTS=%EMPLACEMENTS%%%i;"
    set "TROUVE=1"
)

echo 🔄 6. Recherche approfondie...
echo    (Cela peut prendre quelques secondes...)

REM Recherche avec dir
for /f "delims=" %%i in ('dir /s /b /ad "%USERPROFILE%\*Robot*" 2^>nul') do (
    echo ✅ TROUVÉ: %%i
    set "EMPLACEMENTS=%EMPLACEMENTS%%%i;"
    set "TROUVE=1"
)

echo.
if "%TROUVE%"=="1" (
    echo ████████████████████████████████████████████████████████████████████████████████
    echo ██                                                                            ██
    echo ██         ✅ DOSSIER(S) RobotAutonomePC TROUVÉ(S) !                         ██
    echo ██                                                                            ██
    echo ████████████████████████████████████████████████████████████████████████████████
    echo.
    echo 🎉 SUCCÈS ! Dossier(s) Robot IA trouvé(s) !
    echo.
    echo 📁 EMPLACEMENTS TROUVÉS:
    
    REM Affichage et ouverture des dossiers trouvés
    for %%i in (%EMPLACEMENTS%) do (
        if not "%%i"=="" (
            echo    📂 %%i
            start "" explorer "%%i"
        )
    )
    
    echo.
    echo 🚀 OUVERTURE AUTOMATIQUE DES DOSSIERS...
    echo 👀 Vérifiez les fenêtres Explorateur qui s'ouvrent
    
) else (
    echo ❌ AUCUN DOSSIER RobotAutonomePC TROUVÉ !
    echo.
    echo 🛠️ SOLUTIONS:
    echo.
    echo 1️⃣ CRÉER LE DOSSIER MAINTENANT:
    echo    - Clic droit bureau ^> Nouveau ^> Dossier
    echo    - Nom: RobotAutonomePC
    echo    - Copier tous les fichiers dedans
    echo.
    echo 2️⃣ RECHERCHE MANUELLE:
    echo    - Ouvrir Explorateur Windows
    echo    - Rechercher "robot" ou "RobotAutonomePC"
    echo    - Vérifier Corbeille si supprimé
    echo.
    echo 3️⃣ RECRÉER LE PROJET:
    echo    - Créer nouveau dossier RobotAutonomePC
    echo    - Relancer la création des fichiers
    echo.
    
    echo 🔄 CRÉATION AUTOMATIQUE DU DOSSIER...
    set /p choix="Voulez-vous créer le dossier RobotAutonomePC sur le bureau ? (O/N): "
    
    if /i "%choix%"=="O" (
        echo 📁 Création dossier RobotAutonomePC sur bureau...
        mkdir "%USERPROFILE%\Desktop\RobotAutonomePC" 2>nul
        mkdir "%USERPROFILE%\Desktop\RobotAutonomePC\modules" 2>nul
        mkdir "%USERPROFILE%\Desktop\RobotAutonomePC\config" 2>nul
        
        if exist "%USERPROFILE%\Desktop\RobotAutonomePC" (
            echo ✅ Dossier créé: %USERPROFILE%\Desktop\RobotAutonomePC
            echo 📂 Ouverture du dossier...
            start "" explorer "%USERPROFILE%\Desktop\RobotAutonomePC"
            
            echo.
            echo 📋 PROCHAINES ÉTAPES:
            echo 1. Copier tous les fichiers Robot IA dans ce dossier
            echo 2. Ou relancer la création des fichiers
            echo 3. Utiliser les scripts de lancement
            
        ) else (
            echo ❌ Impossible de créer le dossier
        )
    ) else if /i "%choix%"=="o" (
        echo 📁 Création dossier RobotAutonomePC sur bureau...
        mkdir "%USERPROFILE%\Desktop\RobotAutonomePC" 2>nul
        mkdir "%USERPROFILE%\Desktop\RobotAutonomePC\modules" 2>nul
        mkdir "%USERPROFILE%\Desktop\RobotAutonomePC\config" 2>nul
        
        if exist "%USERPROFILE%\Desktop\RobotAutonomePC" (
            echo ✅ Dossier créé: %USERPROFILE%\Desktop\RobotAutonomePC
            echo 📂 Ouverture du dossier...
            start "" explorer "%USERPROFILE%\Desktop\RobotAutonomePC"
        )
    )
)

echo.
echo 💡 CONSEILS POUR RETROUVER LE DOSSIER:
echo.
echo 🔍 RECHERCHE WINDOWS:
echo    1. Windows + S
echo    2. Taper "RobotAutonomePC" ou "robot"
echo    3. Chercher dans fichiers et dossiers
echo.
echo 📁 EMPLACEMENTS COMMUNS:
echo    - Bureau: %USERPROFILE%\Desktop\
echo    - Documents: %USERPROFILE%\Documents\
echo    - Téléchargements: %USERPROFILE%\Downloads\
echo    - Racine C: C:\
echo.
echo 🗂️ VÉRIFICATION MANUELLE:
echo    - Ouvrir Explorateur Windows
echo    - Naviguer dans chaque dossier
echo    - Chercher dossier contenant robot_autonome_pc.py
echo.

echo 🚀 Ouverture Explorateur pour recherche manuelle...
start "" explorer "%USERPROFILE%"

echo.
echo 👋 Recherche terminée
pause
exit /b 0
