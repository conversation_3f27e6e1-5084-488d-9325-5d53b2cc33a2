#!/usr/bin/env python3
"""Test universel qui fonctionne depuis n'importe quel répertoire"""

import os
import sys
import ast

print("=== TEST UNIVERSEL PYTHON ===")
print(f"Répertoire courant: {os.getcwd()}")
print(f"Python: {sys.version}")

# Trouver le bon répertoire
main_paths = [
    'src/main.py',
    '../src/main.py',
    '../../src/main.py',
    './main.py'
]

main_file = None
for path in main_paths:
    if os.path.exists(path):
        main_file = path
        print(f"✅ Fichier main trouvé: {path}")
        break

if not main_file:
    print("❌ Fichier main.py non trouvé")
    print("Fichiers disponibles:")
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                print(f"  {os.path.join(root, file)}")
    sys.exit(1)

# Test du fichier main
try:
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    print("✅ Lecture main.py: OK")
    
    # Test AST
    ast.parse(content)
    print("✅ AST main.py: OK")
    
    # Test compilation
    compile(content, main_file, 'exec')
    print("✅ Compilation main.py: OK")
    
except SyntaxError as e:
    print(f"❌ Erreur syntaxe main.py ligne {e.lineno}: {e.msg}")
    if e.text:
        print(f"   Code: {e.text.strip()}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Erreur main.py: {e}")
    sys.exit(1)

# Trouver user_management.py
user_paths = [
    'src/auth/user_management.py',
    '../src/auth/user_management.py',
    '../../src/auth/user_management.py',
    'auth/user_management.py'
]

user_file = None
for path in user_paths:
    if os.path.exists(path):
        user_file = path
        print(f"✅ Fichier user_management trouvé: {path}")
        break

if user_file:
    try:
        with open(user_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print("✅ Lecture user_management.py: OK")
        
        # Test AST
        ast.parse(content)
        print("✅ AST user_management.py: OK")
        
        # Test compilation
        compile(content, user_file, 'exec')
        print("✅ Compilation user_management.py: OK")
        
    except SyntaxError as e:
        print(f"❌ Erreur syntaxe user_management.py ligne {e.lineno}: {e.msg}")
        if e.text:
            print(f"   Code: {e.text.strip()}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Erreur user_management.py: {e}")
        sys.exit(1)
else:
    print("⚠️ Fichier user_management.py non trouvé")

print("\n🎉 TOUS LES TESTS RÉUSSIS !")
print("✅ Aucun problème de syntaxe Python détecté")
print("✅ Tous les fichiers testés compilent correctement")
print("\nLes fichiers Python sont parfaitement corrects !")
