#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Office 2024 COMPLÈTE
Tous widgets • <PERSON><PERSON> prière • Météo • Modules fonctionnels
Fond bleu • <PERSON><PERSON> enrichi • Paramètres • Logos complets
"""

import sys
import os
import json
import requests
import threading
import time
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QTabWidget, QSplitter, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox, QGridLayout, QMessageBox,
    QMenuBar, QStatusBar, QProgressBar, QTextEdit, QLineEdit, QCheckBox
)
from PySide6.QtCore import Qt, <PERSON><PERSON><PERSON><PERSON>, QThread, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QPainter

class WeatherWidget(QWidget):
    """Widget météo avec prévisions 15 jours"""

    def __init__(self):
        super().__init__()
        self.setup_weather_widget()
        self.update_weather()

    def setup_weather_widget(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Titre météo
        title = QLabel("🌤️ MÉTÉO 15 JOURS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(0,123,255,0.8);
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(title)

        # Météo actuelle
        self.current_weather = QLabel("🌤️ 22°C - Ensoleillé\n💨 Vent: 15 km/h\n💧 Humidité: 65%")
        self.current_weather.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_weather.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(0,123,255,0.6);
                border: 1px solid #007bff;
                border-radius: 6px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.current_weather)

        # Prévisions 15 jours (résumé)
        self.forecast = QLabel("📅 PRÉVISIONS:\n🌤️ Lun: 23°C\n☀️ Mar: 25°C\n🌧️ Mer: 18°C\n⛅ Jeu: 20°C\n☀️ Ven: 26°C")
        self.forecast.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.forecast.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 9pt;
                background: rgba(0,123,255,0.4);
                border: 1px solid #007bff;
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.forecast)

    def update_weather(self):
        """Met à jour la météo (simulation)"""
        # Simulation - en réel, utiliser une API météo
        import random
        temp = random.randint(15, 30)
        conditions = ["☀️ Ensoleillé", "⛅ Nuageux", "🌤️ Partiellement nuageux", "🌧️ Pluvieux"]
        condition = random.choice(conditions)

        self.current_weather.setText(f"{condition}\n🌡️ {temp}°C\n💨 Vent: {random.randint(5, 25)} km/h\n💧 Humidité: {random.randint(40, 80)}%")

class PrayerTimesWidget(QWidget):
    """Widget heures de prière GPS avec notifications"""

    def __init__(self):
        super().__init__()
        self.setup_prayer_widget()
        self.update_prayer_times()

        # Timer pour notifications 4 minutes avant
        self.prayer_timer = QTimer()
        self.prayer_timer.timeout.connect(self.check_prayer_notifications)
        self.prayer_timer.start(30000)  # Vérification toutes les 30 secondes

    def setup_prayer_widget(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Titre prières
        title = QLabel("🕌 HEURES PRIÈRE GPS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(76,175,80,0.8);
                border: 2px solid #4caf50;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(title)

        # Localisation GPS
        self.location = QLabel("📍 Paris, France\n🌍 48.8566°N, 2.3522°E")
        self.location.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.location.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 9pt;
                background: rgba(76,175,80,0.6);
                border: 1px solid #4caf50;
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.location)

        # Heures de prière
        self.prayer_times = QLabel("🌅 Fajr: 05:45\n🌞 Dhuhr: 12:30\n🌇 Asr: 15:45\n🌅 Maghrib: 18:15\n🌙 Isha: 20:00")
        self.prayer_times.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.prayer_times.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(76,175,80,0.4);
                border: 1px solid #4caf50;
                border-radius: 6px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.prayer_times)

        # Prochaine prière
        self.next_prayer = QLabel("🕌 Prochaine: Dhuhr dans 2h 15min")
        self.next_prayer.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.next_prayer.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                font-weight: bold;
                background: rgba(255,193,7,0.8);
                border: 2px solid #ffc107;
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.next_prayer)

    def update_prayer_times(self):
        """Met à jour les heures de prière selon GPS"""
        # Simulation - en réel, utiliser une API de géolocalisation et calcul des heures
        now = datetime.now()
        prayers = {
            'Fajr': now.replace(hour=5, minute=45),
            'Dhuhr': now.replace(hour=12, minute=30),
            'Asr': now.replace(hour=15, minute=45),
            'Maghrib': now.replace(hour=18, minute=15),
            'Isha': now.replace(hour=20, minute=0)
        }

        # Trouver la prochaine prière
        next_prayer = None
        for name, time in prayers.items():
            if time > now:
                next_prayer = (name, time)
                break

        if next_prayer:
            time_diff = next_prayer[1] - now
            hours = time_diff.seconds // 3600
            minutes = (time_diff.seconds % 3600) // 60
            self.next_prayer.setText(f"🕌 Prochaine: {next_prayer[0]} dans {hours}h {minutes}min")

    def check_prayer_notifications(self):
        """Vérifie et affiche les notifications 4 minutes avant"""
        # Simulation de notification
        now = datetime.now()
        # Logique de notification à implémenter
        pass

class DateTimeWidget(QWidget):
    """Widget date/heure/calendrier avec design"""

    def __init__(self):
        super().__init__()
        self.setup_datetime_widget()

        # Timer pour mise à jour temps réel
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)  # Mise à jour chaque seconde

    def setup_datetime_widget(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Date actuelle
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.date_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                background: rgba(255,87,34,0.8);
                border: 2px solid #ff5722;
                border-radius: 8px;
                padding: 10px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.date_label)

        # Heure actuelle
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18pt;
                font-weight: bold;
                background: rgba(255,87,34,0.6);
                border: 2px solid #ff5722;
                border-radius: 8px;
                padding: 15px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.time_label)

        self.update_datetime()

    def update_datetime(self):
        """Met à jour date et heure"""
        now = datetime.now()
        self.date_label.setText(f"📅 {now.strftime('%A %d %B %Y')}")
        self.time_label.setText(f"🕐 {now.strftime('%H:%M:%S')}")

class ConnectionStatusWidget(QWidget):
    """Widget voyants connexion WiFi/Cellulaire"""

    def __init__(self):
        super().__init__()
        self.setup_connection_widget()

        # Timer pour vérification connexions
        self.connection_timer = QTimer()
        self.connection_timer.timeout.connect(self.check_connections)
        self.connection_timer.start(5000)  # Vérification toutes les 5 secondes

        self.check_connections()

    def setup_connection_widget(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Titre connexions
        title = QLabel("📡 CONNEXIONS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(156,39,176,0.8);
                border: 2px solid #9c27b0;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(title)

        # WiFi
        self.wifi_status = QLabel("📶 WiFi: Vérification...")
        self.wifi_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.wifi_status.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(156,39,176,0.6);
                border: 1px solid #9c27b0;
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.wifi_status)

        # Cellulaire
        self.cellular_status = QLabel("📱 Cellulaire: Vérification...")
        self.cellular_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cellular_status.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(156,39,176,0.4);
                border: 1px solid #9c27b0;
                border-radius: 6px;
                padding: 6px;
                margin: 2px;
            }
        """)
        layout.addWidget(self.cellular_status)

    def check_connections(self):
        """Vérifie les connexions"""
        # Test WiFi
        try:
            response = requests.get("http://www.google.com", timeout=3)
            if response.status_code == 200:
                self.wifi_status.setText("🟢 WiFi: Connexion Excellente")
                self.wifi_status.setStyleSheet(self.wifi_status.styleSheet().replace("rgba(156,39,176,0.6)", "rgba(76,175,80,0.6)"))
            else:
                self.wifi_status.setText("🔴 WiFi: Connexion Faible")
                self.wifi_status.setStyleSheet(self.wifi_status.styleSheet().replace("rgba(76,175,80,0.6)", "rgba(244,67,54,0.6)"))
        except:
            self.wifi_status.setText("🔴 WiFi: Aucune Connexion")
            self.wifi_status.setStyleSheet(self.wifi_status.styleSheet().replace("rgba(76,175,80,0.6)", "rgba(244,67,54,0.6)"))

        # Simulation cellulaire
        import random
        if random.choice([True, False]):
            self.cellular_status.setText("🟢 Cellulaire: Signal Fort")
            self.cellular_status.setStyleSheet(self.cellular_status.styleSheet().replace("rgba(156,39,176,0.4)", "rgba(76,175,80,0.4)"))
        else:
            self.cellular_status.setText("🟡 Cellulaire: Signal Moyen")
            self.cellular_status.setStyleSheet(self.cellular_status.styleSheet().replace("rgba(76,175,80,0.4)", "rgba(255,193,7,0.4)"))

class InnovationLogosWidget(QWidget):
    """Widget logos d'innovations en grille 2x3"""

    def __init__(self):
        super().__init__()
        self.setup_logos_widget()

    def setup_logos_widget(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Titre innovations
        title = QLabel("🚀 INNOVATIONS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(255,193,7,0.8);
                border: 2px solid #ffc107;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(title)

        # Grille 2x3 des innovations
        grid_layout = QGridLayout()
        grid_layout.setSpacing(5)

        innovations = [
            ("🕌", "Prières\nGPS"),
            ("🤖", "IA Multi\nMoteurs"),
            ("📹", "Module\nEXTRA"),
            ("📡", "Voyants\nConnexion"),
            ("🌍", "Localisa\ntion Auto"),
            ("⚡", "Mise à\nJour Auto")
        ]

        for i, (icon, desc) in enumerate(innovations):
            row = i // 2
            col = i % 2

            innovation_widget = QWidget()
            innovation_layout = QVBoxLayout(innovation_widget)
            innovation_layout.setContentsMargins(2, 2, 2, 2)

            icon_label = QLabel(icon)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 16pt;
                    background: rgba(255,193,7,0.6);
                    border: 1px solid #ffc107;
                    border-radius: 6px;
                    padding: 4px;
                }
            """)

            desc_label = QLabel(desc)
            desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            desc_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 8pt;
                    background: rgba(255,193,7,0.4);
                    border: 1px solid #ffc107;
                    border-radius: 4px;
                    padding: 2px;
                }
            """)

            innovation_layout.addWidget(icon_label)
            innovation_layout.addWidget(desc_label)

            grid_layout.addWidget(innovation_widget, row, col)

        layout.addLayout(grid_layout)

class MemoStickersWidget(QWidget):
    """Widget mémos et stickers de rappel"""

    def __init__(self):
        super().__init__()
        self.setup_memo_widget()

    def setup_memo_widget(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Titre mémos
        title = QLabel("📝 MÉMOS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(233,30,99,0.8);
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout.addWidget(title)

        # Zone de saisie mémo
        self.memo_input = QLineEdit()
        self.memo_input.setPlaceholderText("Ajouter un mémo...")
        self.memo_input.setStyleSheet("""
            QLineEdit {
                color: white;
                background: rgba(233,30,99,0.6);
                border: 1px solid #e91e63;
                border-radius: 6px;
                padding: 6px;
                font-size: 10pt;
            }
        """)
        layout.addWidget(self.memo_input)

        # Mémos existants
        memos = [
            "📋 Réunion équipe 14h",
            "📞 Appeler client Martin",
            "💾 Sauvegarde mensuelle",
            "📊 Rapport à envoyer"
        ]

        for memo in memos:
            memo_label = QLabel(memo)
            memo_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 9pt;
                    background: rgba(233,30,99,0.4);
                    border: 1px solid #e91e63;
                    border-radius: 4px;
                    padding: 4px;
                    margin: 1px;
                }
            """)
            layout.addWidget(memo_label)

class MasterComptaOfficeComplete(QMainWindow):
    """Interface Office 2024 COMPLÈTE avec tous les widgets"""

    def __init__(self):
        super().__init__()
        self.setup_complete_interface()

    def setup_complete_interface(self):
        """Configure l'interface complète"""
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Office 2024 COMPLÈTE")

        # Taille et position
        screen = QApplication.primaryScreen().geometry()
        width = min(1800, int(screen.width() * 0.95))
        height = min(1200, int(screen.height() * 0.95))
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        # Style principal BLEU (pas blanc)
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.5 #2a5298, stop:1 #1e3c72);
                color: white;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                border-bottom: 2px solid #0d47a1;
                padding: 8px;
                font-size: 12pt;
                font-weight: bold;
                color: white;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 16px;
                border-radius: 6px;
                color: white;
            }
            QMenuBar::item:selected {
                background: rgba(255,255,255,0.2);
                color: white;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #0d47a1);
                border-top: 2px solid #2196f3;
                color: white;
                font-size: 11pt;
                font-weight: bold;
                padding: 5px;
            }
        """)

        # Menu enrichi en haut (pas titre Office)
        self.create_enriched_menubar()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Zone supérieure avec widgets
        self.create_top_widgets_area(main_layout)

        # Zone centrale avec modules
        self.create_central_modules_area(main_layout)

        # Zone inférieure avec outils
        self.create_bottom_tools_area(main_layout)

        # Barre de statut enrichie
        self.create_enriched_statusbar()

        # Paramètres accessibles
        self.create_settings_panel()

    def create_enriched_menubar(self):
        """Crée le menu enrichi en haut"""
        menubar = self.menuBar()

        # Menu Fichier enrichi
        file_menu = menubar.addMenu("📁 FICHIER")
        file_menu.addAction("🆕 Nouveau Document", self.new_document)
        file_menu.addAction("📂 Ouvrir Fichier", self.open_document)
        file_menu.addAction("💾 Enregistrer", self.save_document)
        file_menu.addAction("💾 Enregistrer Sous", self.save_as_document)
        file_menu.addSeparator()
        file_menu.addAction("📊 Importer Données", self.import_data)
        file_menu.addAction("📤 Exporter Données", self.export_data)
        file_menu.addSeparator()
        file_menu.addAction("🖨️ Imprimer", self.print_document)
        file_menu.addAction("❌ Quitter", self.close)

        # Menu Modules
        modules_menu = menubar.addMenu("🏢 MODULES")
        modules_menu.addAction("🚗 Parc Auto", self.open_parc_auto_module)
        modules_menu.addAction("🏢 Immobilier", self.open_immobilier_module)
        modules_menu.addAction("👥 Personnel", self.open_personnel_module)
        modules_menu.addAction("📊 Comptabilité", self.open_comptabilite_module)
        modules_menu.addAction("💰 Finances", self.open_finances_module)
        modules_menu.addAction("📋 Inventaire", self.open_inventaire_module)

        # Menu Outils
        tools_menu = menubar.addMenu("🔧 OUTILS")
        tools_menu.addAction("🗃️ Bases de Données", self.open_database_system)
        tools_menu.addAction("🤖 IA Ollama", self.open_ai_analysis)
        tools_menu.addAction("🔍 Détection Anomalies", self.detect_anomalies)
        tools_menu.addAction("💾 Sauvegarde Auto", self.configure_auto_backup)
        tools_menu.addAction("📊 Rapports", self.generate_reports)

        # Menu Paramètres
        settings_menu = menubar.addMenu("⚙️ PARAMÈTRES")
        settings_menu.addAction("🎨 Interface", self.configure_interface)
        settings_menu.addAction("🌍 Langues", self.configure_languages)
        settings_menu.addAction("🔔 Notifications", self.configure_notifications)
        settings_menu.addAction("💾 Sauvegardes", self.configure_backups)
        settings_menu.addAction("🔒 Sécurité", self.configure_security)

        # Menu Aide
        help_menu = menubar.addMenu("❓ AIDE")
        help_menu.addAction("📖 Documentation", self.show_documentation)
        help_menu.addAction("🎥 Tutoriels", self.show_tutorials)
        help_menu.addAction("🆘 Support", self.show_support)
        help_menu.addAction("ℹ️ À Propos", self.show_about)

    def create_top_widgets_area(self, layout):
        """Crée la zone supérieure avec tous les widgets"""
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 15px;
                margin: 5px;
                padding: 10px;
            }
        """)

        top_layout = QHBoxLayout(top_frame)
        top_layout.setSpacing(15)

        # Date/Heure/Calendrier
        self.datetime_widget = DateTimeWidget()
        top_layout.addWidget(self.datetime_widget)

        # Heures de prière GPS
        self.prayer_widget = PrayerTimesWidget()
        top_layout.addWidget(self.prayer_widget)

        # Météo 15 jours
        self.weather_widget = WeatherWidget()
        top_layout.addWidget(self.weather_widget)

        # Connexions WiFi/Cellulaire
        self.connection_widget = ConnectionStatusWidget()
        top_layout.addWidget(self.connection_widget)

        # Logos innovations
        self.innovations_widget = InnovationLogosWidget()
        top_layout.addWidget(self.innovations_widget)

        # Mémos et stickers
        self.memo_widget = MemoStickersWidget()
        top_layout.addWidget(self.memo_widget)

        layout.addWidget(top_frame)

    def create_central_modules_area(self, layout):
        """Crée la zone centrale avec modules fonctionnels"""
        central_frame = QFrame()
        central_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 15px;
                margin: 5px;
                padding: 15px;
            }
        """)

        central_layout = QVBoxLayout(central_frame)

        # Titre modules
        modules_title = QLabel("🏢 MODULES MASTER COMPTA GÉNÉRAL")
        modules_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        modules_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b35, stop:0.5 #f7931e, stop:1 #ff6b35);
                border: 3px solid #ff6b35;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        central_layout.addWidget(modules_title)

        # Grille des modules (3x3)
        modules_grid = QGridLayout()
        modules_grid.setSpacing(20)

        # Modules principaux avec boutons fonctionnels
        modules = [
            ("🚗", "PARC AUTO", "Gestion véhicules", self.open_parc_auto_module),
            ("🏢", "IMMOBILIER", "Gestion biens", self.open_immobilier_module),
            ("👥", "PERSONNEL", "Gestion employés", self.open_personnel_module),
            ("📊", "COMPTABILITÉ", "Gestion comptes", self.open_comptabilite_module),
            ("💰", "FINANCES", "Gestion finances", self.open_finances_module),
            ("📋", "INVENTAIRE", "Gestion stock", self.open_inventaire_module),
            ("🗃️", "BASES DONNÉES", "Système BDD", self.open_database_system),
            ("🤖", "IA OLLAMA", "Analyse IA", self.open_ai_analysis),
            ("📊", "RAPPORTS", "Génération", self.generate_reports)
        ]

        for i, (icon, title, desc, callback) in enumerate(modules):
            row = i // 3
            col = i % 3

            module_btn = QPushButton()
            module_btn.setMinimumSize(200, 120)
            module_btn.clicked.connect(callback)

            # Style 3D pour chaque module
            colors = [
                "#4caf50", "#2196f3", "#ff9800", "#9c27b0",
                "#f44336", "#00bcd4", "#795548", "#607d8b", "#3f51b5"
            ]
            color = colors[i % len(colors)]

            module_btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:0.5 rgba(255,255,255,0.2), stop:1 {color});
                    color: white;
                    border: 3px solid {color};
                    border-radius: 15px;
                    font-size: 14pt;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.3), stop:0.5 {color}, stop:1 rgba(255,255,255,0.3));
                    border: 3px solid white;
                    transform: scale(1.05);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0,0,0,0.3), stop:0.5 {color}, stop:1 rgba(0,0,0,0.3));
                }}
            """)

            module_btn.setText(f"{icon}\n{title}\n{desc}")

            modules_grid.addWidget(module_btn, row, col)

        central_layout.addLayout(modules_grid)
        layout.addWidget(central_frame)

    def create_bottom_tools_area(self, layout):
        """Crée la zone inférieure avec outils"""
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 15px;
                margin: 5px;
                padding: 10px;
            }
        """)

        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setSpacing(15)

        # Outils rapides
        tools = [
            ("📋", "Copier", self.copy_action),
            ("📄", "Coller", self.paste_action),
            ("✂️", "Couper", self.cut_action),
            ("↩️", "Annuler", self.undo_action),
            ("↪️", "Rétablir", self.redo_action),
            ("🔍", "Rechercher", self.search_action),
            ("💾", "Sauvegarder", self.save_action),
            ("🖨️", "Imprimer", self.print_action)
        ]

        for icon, text, callback in tools:
            tool_btn = QPushButton(f"{icon}\n{text}")
            tool_btn.setMinimumSize(80, 60)
            tool_btn.clicked.connect(callback)
            tool_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #37474f, stop:1 #263238);
                    color: white;
                    border: 2px solid #546e7a;
                    border-radius: 10px;
                    font-size: 10pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #546e7a, stop:1 #37474f);
                    border: 2px solid #90a4ae;
                }
            """)
            bottom_layout.addWidget(tool_btn)

        layout.addWidget(bottom_frame)

    def create_enriched_statusbar(self):
        """Crée la barre de statut enrichie"""
        statusbar = self.statusBar()

        # Message principal
        statusbar.showMessage("🏢 MASTER COMPTA GÉNÉRAL - Interface Office 2024 COMPLÈTE • Tous widgets actifs • Modules fonctionnels")

        # Widgets permanents
        # Heure actuelle
        self.status_time = QLabel()
        self.status_time.setStyleSheet("color: white; font-weight: bold; padding: 5px;")
        statusbar.addPermanentWidget(self.status_time)

        # Statut connexion
        self.status_connection = QLabel("🟢 Connecté")
        self.status_connection.setStyleSheet("color: #4caf50; font-weight: bold; padding: 5px;")
        statusbar.addPermanentWidget(self.status_connection)

        # Mode actuel
        self.status_mode = QLabel("MASTER COMPTA")
        self.status_mode.setStyleSheet("color: #ff9800; font-weight: bold; padding: 5px;")
        statusbar.addPermanentWidget(self.status_mode)

        # Timer pour mise à jour
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_statusbar)
        self.status_timer.start(1000)

    def update_statusbar(self):
        """Met à jour la barre de statut"""
        now = datetime.now()
        self.status_time.setText(f"🕐 {now.strftime('%H:%M:%S')}")

    def create_settings_panel(self):
        """Crée le panneau de paramètres accessible"""
        # Panneau de paramètres sera créé à la demande
        pass

    # Méthodes des modules fonctionnels
    def open_parc_auto_module(self):
        """Ouvre le module Parc Auto avec données complètes"""
        self.show_module_window("🚗 PARC AUTO", self.get_parc_auto_data(), [
            ("➕ Ajouter Véhicule", "#4caf50"),
            ("✏️ Modifier", "#2196f3"),
            ("🗑️ Supprimer", "#f44336"),
            ("📊 Rapport", "#ff9800")
        ])

    def open_immobilier_module(self):
        """Ouvre le module Immobilier avec données complètes"""
        self.show_module_window("🏢 IMMOBILIER", self.get_immobilier_data(), [
            ("🏠 Ajouter Bien", "#4caf50"),
            ("👥 Locataires", "#2196f3"),
            ("💰 Quittances", "#ff9800"),
            ("📊 Charges", "#9c27b0")
        ])

    def open_personnel_module(self):
        """Ouvre le module Personnel avec données complètes"""
        self.show_module_window("👥 PERSONNEL", self.get_personnel_data(), [
            ("👤 Ajouter Employé", "#4caf50"),
            ("💰 Paie", "#2196f3"),
            ("🏖️ Congés", "#ff9800"),
            ("📚 Formation", "#9c27b0")
        ])

    def open_comptabilite_module(self):
        """Ouvre le module Comptabilité"""
        QMessageBox.information(self, "📊 Comptabilité",
            "📊 MODULE COMPTABILITÉ\n\n"
            "💰 Gestion des comptes\n"
            "📋 Écritures comptables\n"
            "📊 Bilan et compte de résultat\n"
            "📈 Analyses financières\n"
            "🔍 Contrôles et vérifications\n\n"
            "Module en cours de développement...")

    def open_finances_module(self):
        """Ouvre le module Finances"""
        QMessageBox.information(self, "💰 Finances",
            "💰 MODULE FINANCES\n\n"
            "💳 Gestion trésorerie\n"
            "📊 Budgets et prévisions\n"
            "💱 Devises et changes\n"
            "📈 Investissements\n"
            "🏦 Relations bancaires\n\n"
            "Module en cours de développement...")

    def open_inventaire_module(self):
        """Ouvre le module Inventaire"""
        QMessageBox.information(self, "📋 Inventaire",
            "📋 MODULE INVENTAIRE\n\n"
            "📦 Gestion stock\n"
            "🏷️ Articles et références\n"
            "📊 Mouvements de stock\n"
            "🔍 Inventaires physiques\n"
            "📈 Valorisation stock\n\n"
            "Module en cours de développement...")

    def open_database_system(self):
        """Ouvre le système de bases de données"""
        QMessageBox.information(self, "🗃️ Système de Bases de Données",
            "🗃️ SYSTÈME DE BASES DE DONNÉES RÉVOLUTIONNAIRE\n\n"
            "📊 Import/Export multi-formats\n"
            "🔍 Détection anomalies automatique\n"
            "💾 Sauvegardes automatiques\n"
            "🤖 IA Ollama intégrée\n"
            "⚡ Performance optimisée\n\n"
            "💡 Lancez: LANCER_DATABASE_SYSTEM.bat\n"
            "Ou: python master_compta_database_interface.py")

    def open_ai_analysis(self):
        """Ouvre l'analyse IA"""
        QMessageBox.information(self, "🤖 Analyse IA",
            "🤖 ANALYSE IA AVEC OLLAMA\n\n"
            "🧠 Modèle: Llama3 local\n"
            "🔍 Analyse: Anomalies et optimisations\n"
            "💬 Requêtes: Langage naturel\n"
            "📊 Recommandations: Intelligentes\n"
            "🔒 Sécurité: Données locales\n\n"
            "💡 Installation Ollama:\n"
            "1. Téléchargez: ollama.ai\n"
            "2. Installez Ollama\n"
            "3. Exécutez: ollama run llama3")

    def show_module_window(self, title, data, buttons):
        """Affiche une fenêtre de module avec données"""
        window = QWidget()
        window.setWindowTitle(title)
        window.setGeometry(200, 200, 1200, 800)
        window.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.5 #2a5298, stop:1 #1e3c72);
                color: white;
            }
            QTableWidget {
                gridline-color: #546e7a;
                background: rgba(255,255,255,0.1);
                border: 2px solid #2196f3;
                border-radius: 10px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                border: 1px solid #0d47a1;
                padding: 10px;
                font-weight: bold;
                color: white;
                font-size: 11pt;
            }
            QTableWidget::item {
                padding: 8px;
                border: 1px solid #546e7a;
                color: white;
            }
            QTableWidget::item:selected {
                background: rgba(33,150,243,0.5);
            }
        """)

        layout = QVBoxLayout(window)

        # Titre du module
        module_title = QLabel(title)
        module_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        module_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b35, stop:0.5 #f7931e, stop:1 #ff6b35);
                border: 3px solid #ff6b35;
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(module_title)

        # Tableau des données
        table = QTableWidget(len(data['rows']), len(data['headers']))
        table.setHorizontalHeaderLabels(data['headers'])

        for row, row_data in enumerate(data['rows']):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                # Couleurs selon le contenu
                if 'Actif' in str(value):
                    item.setBackground(QColor('#4caf50'))
                elif 'Maintenance' in str(value) or 'Préavis' in str(value):
                    item.setBackground(QColor('#ff9800'))
                elif 'Vendu' in str(value) or 'Libre' in str(value):
                    item.setBackground(QColor('#2196f3'))
                elif '€' in str(value):
                    item.setBackground(QColor('#9c27b0'))
                table.setItem(row, col, item)

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        for btn_text, btn_color in buttons:
            btn = QPushButton(btn_text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: {btn_color};
                    color: white;
                    font-weight: bold;
                    font-size: 12pt;
                    padding: 12px 20px;
                    border: 2px solid {btn_color};
                    border-radius: 8px;
                }}
                QPushButton:hover {{
                    background: rgba(255,255,255,0.2);
                    border: 2px solid white;
                }}
            """)
            buttons_layout.addWidget(btn)

        layout.addLayout(buttons_layout)

        window.show()
        # Garder référence
        if not hasattr(self, 'module_windows'):
            self.module_windows = []
        self.module_windows.append(window)

    def get_parc_auto_data(self):
        """Retourne les données du parc auto"""
        return {
            'headers': ['Code', 'Marque', 'Modèle', 'Immatriculation', 'Année', 'Kilométrage', 'Carburant', 'Statut', 'Coût/mois', 'Observations'],
            'rows': [
                ['V001', 'Peugeot', '308', 'AB-123-CD', '2020', '45000', 'Essence', 'Actif', '450€', 'Révision prévue'],
                ['V002', 'Renault', 'Clio', 'EF-456-GH', '2019', '62000', 'Diesel', 'Actif', '380€', 'Bon état'],
                ['V003', 'Citroën', 'C3', 'IJ-789-KL', '2021', '28000', 'Essence', 'Actif', '420€', 'Garantie'],
                ['V004', 'Ford', 'Focus', 'MN-012-OP', '2018', '78000', 'Diesel', 'Maintenance', '520€', 'Réparation en cours'],
                ['V005', 'Volkswagen', 'Golf', 'QR-345-ST', '2022', '15000', 'Hybride', 'Actif', '580€', 'Véhicule récent'],
                ['V006', 'BMW', 'Série 3', 'UV-678-WX', '2020', '55000', 'Diesel', 'Actif', '750€', 'Véhicule direction'],
                ['V007', 'Mercedes', 'Classe A', 'YZ-901-AB', '2021', '32000', 'Essence', 'Actif', '680€', 'Excellent état'],
                ['V008', 'Audi', 'A3', 'CD-234-EF', '2019', '68000', 'Diesel', 'Vendu', '0€', 'Vendu le 15/11/2024']
            ]
        }

    def get_immobilier_data(self):
        """Retourne les données immobilier"""
        return {
            'headers': ['Code', 'Type', 'Adresse', 'Surface (m²)', 'Loyer', 'Locataire', 'Statut', 'Charges', 'Observations'],
            'rows': [
                ['B001', 'Appartement', '15 Rue de la Paix, Paris', '65', '1200€', 'Martin Pierre', 'Occupé', '150€', 'Bail renouvelé'],
                ['B002', 'Bureau', '25 Avenue des Champs, Lyon', '120', '2500€', 'Société ABC', 'Occupé', '300€', 'Contrat 3 ans'],
                ['B003', 'Maison', '8 Impasse du Soleil, Nice', '150', '1800€', 'Famille Durand', 'Occupé', '200€', 'Jardin inclus'],
                ['B004', 'Studio', '42 Boulevard Victor, Marseille', '25', '650€', '', 'Libre', '80€', 'Disponible immédiatement'],
                ['B005', 'Local Commercial', '18 Place du Marché, Toulouse', '80', '1500€', 'Boulangerie Martin', 'Occupé', '180€', 'Bail commercial'],
                ['B006', 'Appartement', '33 Rue des Fleurs, Bordeaux', '90', '1100€', 'Mme Leblanc', 'Préavis', '120€', 'Départ fin décembre']
            ]
        }

    def get_personnel_data(self):
        """Retourne les données personnel"""
        return {
            'headers': ['ID', 'Nom', 'Prénom', 'Poste', 'Service', 'Salaire', 'Date Embauche', 'Statut', 'Congés Restants', 'Observations'],
            'rows': [
                ['E001', 'Martin', 'Jean', 'Directeur', 'Direction', '4500€', '15/01/2020', 'CDI', '18 jours', 'Excellent manager'],
                ['E002', 'Durand', 'Marie', 'Comptable', 'Comptabilité', '2800€', '03/09/2021', 'CDI', '22 jours', 'Très rigoureuse'],
                ['E003', 'Leblanc', 'Pierre', 'Commercial', 'Ventes', '2200€', '12/06/2022', 'CDI', '15 jours', 'Bon vendeur'],
                ['E004', 'Moreau', 'Sophie', 'Secrétaire', 'Administration', '2000€', '08/03/2023', 'CDI', '25 jours', 'Polyvalente'],
                ['E005', 'Bernard', 'Luc', 'Technicien', 'Maintenance', '2400€', '20/11/2021', 'CDI', '12 jours', 'Compétent'],
                ['E006', 'Petit', 'Anne', 'Stagiaire', 'Comptabilité', '600€', '01/09/2024', 'Stage', '0 jours', 'Stage 6 mois'],
                ['E007', 'Roux', 'Thomas', 'Développeur', 'Informatique', '3200€', '10/04/2022', 'CDI', '20 jours', 'Expert technique']
            ]
        }

    # Méthodes d'action des menus
    def new_document(self): QMessageBox.information(self, "Nouveau", "🆕 Nouveau document créé")
    def open_document(self): QMessageBox.information(self, "Ouvrir", "📂 Dialogue d'ouverture")
    def save_document(self): QMessageBox.information(self, "Enregistrer", "💾 Document enregistré")
    def save_as_document(self): QMessageBox.information(self, "Enregistrer sous", "💾 Enregistrer sous...")
    def import_data(self): QMessageBox.information(self, "Import", "📊 Import de données")
    def export_data(self): QMessageBox.information(self, "Export", "📤 Export de données")
    def print_document(self): QMessageBox.information(self, "Imprimer", "🖨️ Impression en cours")
    def detect_anomalies(self): QMessageBox.information(self, "Anomalies", "🔍 Détection d'anomalies lancée")
    def configure_auto_backup(self): QMessageBox.information(self, "Sauvegarde", "💾 Configuration sauvegarde auto")
    def generate_reports(self): QMessageBox.information(self, "Rapports", "📊 Génération de rapports")
    def configure_interface(self): QMessageBox.information(self, "Interface", "🎨 Configuration interface")
    def configure_languages(self): QMessageBox.information(self, "Langues", "🌍 Configuration langues")
    def configure_notifications(self): QMessageBox.information(self, "Notifications", "🔔 Configuration notifications")
    def configure_backups(self): QMessageBox.information(self, "Sauvegardes", "💾 Configuration sauvegardes")
    def configure_security(self): QMessageBox.information(self, "Sécurité", "🔒 Configuration sécurité")
    def show_documentation(self): QMessageBox.information(self, "Documentation", "📖 Documentation MASTER COMPTA")
    def show_tutorials(self): QMessageBox.information(self, "Tutoriels", "🎥 Tutoriels vidéo")
    def show_support(self): QMessageBox.information(self, "Support", "🆘 Support technique")
    def show_about(self): QMessageBox.about(self, "À propos", "🏢 MASTER COMPTA GÉNÉRAL\nInterface Office 2024 COMPLÈTE\n\nTous widgets • Modules fonctionnels • Fond bleu")

    # Méthodes d'action des outils
    def copy_action(self): QMessageBox.information(self, "Copier", "📋 Élément copié")
    def paste_action(self): QMessageBox.information(self, "Coller", "📄 Élément collé")
    def cut_action(self): QMessageBox.information(self, "Couper", "✂️ Élément coupé")
    def undo_action(self): QMessageBox.information(self, "Annuler", "↩️ Action annulée")
    def redo_action(self): QMessageBox.information(self, "Rétablir", "↪️ Action rétablie")
    def search_action(self): QMessageBox.information(self, "Rechercher", "🔍 Recherche lancée")
    def save_action(self): QMessageBox.information(self, "Sauvegarder", "💾 Sauvegarde effectuée")
    def print_action(self): QMessageBox.information(self, "Imprimer", "🖨️ Impression lancée")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Configuration application
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("Office 2024 COMPLET")
    app.setOrganizationName("MASTER COMPTA")

    window = MasterComptaOfficeComplete()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()