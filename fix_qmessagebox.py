#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger tous les appels QMessageBox dans gestimmob_actions.py
"""

import re

def fix_qmessagebox_calls():
    """Corrige tous les appels QMessageBox pour utiliser _get_widget_parent()"""
    
    # <PERSON>re le fichier
    with open('gestimmob_actions.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Patterns à remplacer
    patterns = [
        # QMessageBox.information(self, ...)
        (r'QMessageBox\.information\(self,', 'QMessageBox.information(self._get_widget_parent(),'),
        # QMessageBox.warning(self, ...)
        (r'QMessageBox\.warning\(self,', 'QMessageBox.warning(self._get_widget_parent(),'),
        # QMessageBox.critical(self, ...)
        (r'QMessageBox\.critical\(self,', 'QMessageBox.critical(self._get_widget_parent(),'),
        # QMessageBox.question(self, ...)
        (r'QMessageBox\.question\(self,', 'QMessageBox.question(self._get_widget_parent(),'),
        # QMessageBox.about(self, ...)
        (r'QMessageBox\.about\(self,', 'QMessageBox.about(self._get_widget_parent(),'),
        # QInputDialog.getText(self, ...)
        (r'QInputDialog\.getText\(self,', 'QInputDialog.getText(self._get_widget_parent(),'),
        # QFileDialog.getOpenFileName(self, ...)
        (r'QFileDialog\.getOpenFileName\(\s*self,', 'QFileDialog.getOpenFileName(self._get_widget_parent(),'),
    ]
    
    # Appliquer les remplacements
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # Patterns pour les accès aux tables
    table_patterns = [
        # self.table.item(current_row, 0).text()
        (r'self\.table\.item\(current_row,\s*(\d+)\)\.text\(\)', r'self._safe_get_table_text(self.table, current_row, \1)'),
        # self.fournisseurs_table.item(current_row, 0).text()
        (r'self\.fournisseurs_table\.item\(current_row,\s*(\d+)\)\.text\(\)', r'self._safe_get_table_text(self.fournisseurs_table, current_row, \1)'),
        # self.clients_table.item(current_row, 0).text()
        (r'self\.clients_table\.item\(current_row,\s*(\d+)\)\.text\(\)', r'self._safe_get_table_text(self.clients_table, current_row, \1)'),
    ]
    
    # Appliquer les remplacements pour les tables
    for pattern, replacement in table_patterns:
        content = re.sub(pattern, replacement, content)
    
    # Écrire le fichier corrigé
    with open('gestimmob_actions.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Tous les appels QMessageBox ont été corrigés!")

if __name__ == "__main__":
    fix_qmessagebox_calls()
