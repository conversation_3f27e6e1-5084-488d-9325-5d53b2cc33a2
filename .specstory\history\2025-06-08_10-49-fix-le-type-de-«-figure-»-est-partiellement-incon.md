<!-- Generated by SpecStory -->

# /fix Le type de « figure » est partiellement incon (2025-06-08 11:49:53)

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l'avertissement "Le type de « figure » est partiellement inconnu", il suffit d'ajouter une annotation de type explicite pour `plt` et d'importer `matplotlib.pyplot` sous un alias typé. Cela aide les outils de type (comme Pylance) à mieux comprendre les signatures.

Voici la modification à apporter dans la méthode `afficher_charts` :

````python
    def afficher_charts(self):
        try:
            import matplotlib.pyplot as plt
            from matplotlib.pyplot import Figure  # Ajout pour l'annotation de type
            from collections import defaultdict
            from typing import DefaultDict
            stats_categorie: DefaultDict[str, int]
            stats_statut: DefaultDict[str, int]
            stats_categorie = defaultdict(int)
            stats_statut = defaultdict(int)
            def safe_text(item: Optional[QTableWidgetItem]) -> str:
                return item.text() if item is not None and hasattr(item, "text") else ""
            for row in range(self.table.rowCount()):
                cat = safe_text(self.table.item(row, 2))
                statut = safe_text(self.table.item(row, 9))
                stats_categorie[cat] += 1
                stats_statut[statut] += 1
            if stats_categorie:
                fig: Figure = plt.figure(figsize=(8, 4))  # Annotation de type explicite
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                fig: Figure = plt.figure(figsize=(8, 4))  # Annotation de type explicite
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
        except Exception as e:
            QMessageBox.warning(self, "Erreur graphique", f"Erreur lors de l'affichage des graphiques : {e}")
# ...existing code...
````

Résumé :  
- Ajout de `from matplotlib.pyplot import Figure` pour l'annotation de type.
- Ajout de `fig: Figure = plt.figure(figsize=(8, 4))` pour clarifier le type de retour de `plt.figure` et satisfaire l'analyseur de type.

---

