#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MODULE OLLAMA INTEGRATION - MASTER COMPTA
Connexion à votre Ollama llama3.2 local
Interface noire avec IA intégrée
"""

import requests
import json
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, 
    QLabel, QTextEdit, QLineEdit, QComboBox, QGroupBox, QProgressBar,
    QMessageBox, QTabWidget
)
from PySide6.QtCore import QThread, pyqtSignal, Qt
from PySide6.QtGui import QFont

class OllamaWorker(QThread):
    """Worker thread pour les requêtes Ollama"""
    response_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, prompt, model="llama3.2"):
        super().__init__()
        self.prompt = prompt
        self.model = model
        self.ollama_url = "http://localhost:11434/api/generate"
    
    def run(self):
        """Exécute la requête Ollama"""
        try:
            payload = {
                "model": self.model,
                "prompt": self.prompt,
                "stream": False
            }
            
            response = requests.post(
                self.ollama_url, 
                json=payload, 
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.response_ready.emit(result.get("response", "Pas de réponse"))
            else:
                self.error_occurred.emit(f"Erreur HTTP: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("❌ Ollama non accessible. Vérifiez que 'ollama serve' est lancé")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("⏱️ Timeout - Requête trop longue")
        except Exception as e:
            self.error_occurred.emit(f"Erreur: {str(e)}")

class ModuleOllamaIntegration(QWidget):
    """Module Ollama intégré à MASTER COMPTA - Interface noire"""
    
    def __init__(self):
        super().__init__()
        self.setup_interface_ollama()
        self.ollama_worker = None
    
    def setup_interface_ollama(self):
        """Configure l'interface Ollama noire"""
        layout = QVBoxLayout(self)
        
        # Style noir pour Ollama
        self.setStyleSheet("""
            QWidget {
                background-color: #000000;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel {
                background-color: #1a1a1a;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #2a2a2a;
                color: white;
                border: 2px solid #444444;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a3a3a;
                border: 2px solid #666666;
            }
            QTextEdit, QLineEdit, QComboBox {
                background-color: #1a1a1a;
                color: white;
                border: 2px solid #444444;
                border-radius: 4px;
                padding: 5px;
            }
            QGroupBox {
                background-color: #0a0a0a;
                color: white;
                border: 2px solid #444444;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
                font-weight: bold;
            }
            QGroupBox::title {
                color: white;
                background-color: #2a2a2a;
                border: 1px solid #444444;
                border-radius: 4px;
                padding: 5px 10px;
                margin-left: 10px;
            }
        """)
        
        # Titre
        titre = QLabel("🤖 ASSISTANT IA OLLAMA - LLAMA3.2 - INTERFACE NOIRE")
        titre.setStyleSheet("font-size: 16pt; font-weight: bold; padding: 10px;")
        layout.addWidget(titre)
        
        # Onglets Ollama
        self.tabs_ollama = QTabWidget()
        
        # Onglet 1: Chat IA
        self.tabs_ollama.addTab(self.create_chat_ia(), "💬 CHAT IA")
        
        # Onglet 2: Analyse MASTER COMPTA
        self.tabs_ollama.addTab(self.create_analyse_compta(), "📊 ANALYSE COMPTA")
        
        # Onglet 3: Assistant modules
        self.tabs_ollama.addTab(self.create_assistant_modules(), "🏢 ASSISTANT MODULES")
        
        layout.addWidget(self.tabs_ollama)
        
        # Statut connexion
        self.status_label = QLabel("🔴 Statut: Non connecté à Ollama")
        layout.addWidget(self.status_label)
        
        # Test connexion au démarrage
        self.test_ollama_connection()
    
    def create_chat_ia(self):
        """Onglet Chat IA avec Ollama"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Configuration modèle
        config_group = QGroupBox("🤖 CONFIGURATION OLLAMA")
        config_layout = QGridLayout(config_group)
        
        config_layout.addWidget(QLabel("Modèle:"), 0, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(["llama3.2", "llama3.1", "llama3", "codellama", "mistral"])
        config_layout.addWidget(self.model_combo, 0, 1)
        
        config_layout.addWidget(QLabel("URL Ollama:"), 0, 2)
        self.url_input = QLineEdit("http://localhost:11434")
        config_layout.addWidget(self.url_input, 0, 3)
        
        layout.addWidget(config_group)
        
        # Zone de chat
        chat_group = QGroupBox("💬 CONVERSATION IA")
        chat_layout = QVBoxLayout(chat_group)
        
        # Historique conversation
        self.chat_history = QTextEdit()
        self.chat_history.setPlaceholderText("Historique de la conversation avec Ollama llama3.2...")
        self.chat_history.setMinimumHeight(300)
        self.chat_history.setReadOnly(True)
        chat_layout.addWidget(self.chat_history)
        
        # Zone saisie
        input_layout = QHBoxLayout()
        self.prompt_input = QLineEdit()
        self.prompt_input.setPlaceholderText("Posez votre question à Ollama llama3.2...")
        self.prompt_input.returnPressed.connect(self.send_to_ollama)
        input_layout.addWidget(self.prompt_input)
        
        send_btn = QPushButton("🚀 ENVOYER")
        send_btn.clicked.connect(self.send_to_ollama)
        input_layout.addWidget(send_btn)
        
        chat_layout.addLayout(input_layout)
        layout.addWidget(chat_group)
        
        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return widget
    
    def create_analyse_compta(self):
        """Onglet Analyse MASTER COMPTA avec IA"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Analyse rapide
        analyse_group = QGroupBox("📊 ANALYSE IA MASTER COMPTA")
        analyse_layout = QGridLayout(analyse_group)
        
        # Boutons d'analyse prédéfinis
        analyses = [
            ("📋 Analyser Inventaire", "Analyse mon inventaire et détecte les anomalies"),
            ("🏢 Analyser Fournisseurs", "Analyse mes fournisseurs et optimise les relations"),
            ("🚗 Analyser Parc Auto", "Analyse mon parc automobile et optimise les coûts"),
            ("💰 Analyser Amortissements", "Calcule et optimise mes amortissements"),
            ("📊 Rapport Global", "Génère un rapport complet de tous mes modules"),
            ("🔍 Détecter Anomalies", "Détecte toutes les anomalies dans mes données")
        ]
        
        for i, (titre, prompt) in enumerate(analyses):
            row = i // 2
            col = i % 2
            btn = QPushButton(titre)
            btn.clicked.connect(lambda checked, p=prompt: self.analyse_predefined(p))
            analyse_layout.addWidget(btn, row, col)
        
        layout.addWidget(analyse_group)
        
        # Zone résultats analyse
        self.analyse_results = QTextEdit()
        self.analyse_results.setPlaceholderText("Résultats de l'analyse IA de vos données MASTER COMPTA...")
        self.analyse_results.setMinimumHeight(250)
        layout.addWidget(self.analyse_results)
        
        return widget
    
    def create_assistant_modules(self):
        """Onglet Assistant pour vos 11 modules"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Assistant modules
        assistant_group = QGroupBox("🏢 ASSISTANT VOS 11 MODULES")
        assistant_layout = QGridLayout(assistant_group)
        
        # Boutons pour chaque module
        modules = [
            ("🧮 Calculatrice IA", "Comment optimiser mes calculs d'amortissement ?"),
            ("📋 Inventaire", "Comment améliorer la gestion de mon inventaire ?"),
            ("📄 Réforme", "Guide-moi pour créer un PV de réforme"),
            ("🔍 Recherche", "Comment optimiser mes recherches ?"),
            ("🏢 Fournisseur", "Comment mieux gérer mes fournisseurs ?"),
            ("🏗️ Travaux", "Comment suivre efficacement mes travaux ?"),
            ("🐄 Animaux", "Comment optimiser le suivi de mes animaux ?"),
            ("🖨️ Impression", "Quels sont les meilleurs formats d'impression ?"),
            ("🚗 Matériel", "Comment optimiser mon matériel de travaux ?"),
            ("🚗 Parc Auto", "Comment réduire les coûts de mon parc auto ?"),
            ("🏢 Immobilisations", "Comment optimiser mes immobilisations ?")
        ]
        
        for i, (module, question) in enumerate(modules):
            row = i // 3
            col = i % 3
            btn = QPushButton(module)
            btn.clicked.connect(lambda checked, q=question: self.assistant_module(q))
            assistant_layout.addWidget(btn, row, col)
        
        layout.addWidget(assistant_group)
        
        # Zone conseils assistant
        self.assistant_results = QTextEdit()
        self.assistant_results.setPlaceholderText("Conseils et recommandations IA pour vos modules...")
        self.assistant_results.setMinimumHeight(250)
        layout.addWidget(self.assistant_results)
        
        return widget

    def test_ollama_connection(self):
        """Test la connexion à Ollama"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                self.status_label.setText("🟢 Statut: Connecté à Ollama - Prêt")
                self.status_label.setStyleSheet("color: #00ff00; font-weight: bold;")
            else:
                self.status_label.setText("🟡 Statut: Ollama détecté mais erreur")
                self.status_label.setStyleSheet("color: #ffff00; font-weight: bold;")
        except:
            self.status_label.setText("🔴 Statut: Ollama non accessible - Lancez 'ollama serve'")
            self.status_label.setStyleSheet("color: #ff0000; font-weight: bold;")

    def send_to_ollama(self):
        """Envoie une requête à Ollama"""
        prompt = self.prompt_input.text().strip()
        if not prompt:
            return

        # Affichage dans l'historique
        self.chat_history.append(f"👤 VOUS: {prompt}")
        self.prompt_input.clear()

        # Affichage progression
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Animation infinie

        # Lancement worker
        model = self.model_combo.currentText()
        self.ollama_worker = OllamaWorker(prompt, model)
        self.ollama_worker.response_ready.connect(self.on_ollama_response)
        self.ollama_worker.error_occurred.connect(self.on_ollama_error)
        self.ollama_worker.start()

    def on_ollama_response(self, response):
        """Traite la réponse d'Ollama"""
        self.progress_bar.setVisible(False)
        self.chat_history.append(f"🤖 OLLAMA: {response}")
        self.chat_history.append("─" * 50)

    def on_ollama_error(self, error):
        """Traite les erreurs Ollama"""
        self.progress_bar.setVisible(False)
        self.chat_history.append(f"❌ ERREUR: {error}")
        self.chat_history.append("─" * 50)

    def analyse_predefined(self, prompt):
        """Lance une analyse prédéfinie"""
        self.analyse_results.append(f"🔍 ANALYSE EN COURS: {prompt}")

        # Contexte MASTER COMPTA
        full_prompt = f"""
        Tu es un expert comptable spécialisé dans MASTER COMPTA.
        Contexte: J'utilise un logiciel avec 11 modules:
        - Calculatrice IA, Inventaire (20 critères), Réforme (22 critères)
        - Recherche (24 critères), Fournisseur (17 critères), Suivi Travaux (13 critères)
        - Suivi Animaux (35 critères), Impression, Matériel Travaux (14 critères)
        - Parc Auto (50 critères), Gestion Immobilisations (ERP)

        Question: {prompt}

        Réponds de manière pratique et actionnable.
        """

        # Lancement analyse
        self.ollama_worker = OllamaWorker(full_prompt, self.model_combo.currentText())
        self.ollama_worker.response_ready.connect(lambda r: self.analyse_results.append(f"📊 RÉSULTAT: {r}\n{'='*50}\n"))
        self.ollama_worker.error_occurred.connect(lambda e: self.analyse_results.append(f"❌ ERREUR: {e}\n"))
        self.ollama_worker.start()

    def assistant_module(self, question):
        """Assistant pour un module spécifique"""
        self.assistant_results.append(f"🏢 QUESTION MODULE: {question}")

        # Contexte spécialisé
        full_prompt = f"""
        Tu es un consultant expert en gestion d'entreprise et logiciels de comptabilité.
        Je travaille avec MASTER COMPTA qui a 11 modules spécialisés.

        Ma question: {question}

        Donne-moi des conseils pratiques, des bonnes pratiques, et des recommandations
        spécifiques pour optimiser ce module. Sois concret et actionnable.
        """

        # Lancement assistant
        self.ollama_worker = OllamaWorker(full_prompt, self.model_combo.currentText())
        self.ollama_worker.response_ready.connect(lambda r: self.assistant_results.append(f"💡 CONSEIL: {r}\n{'='*50}\n"))
        self.ollama_worker.error_occurred.connect(lambda e: self.assistant_results.append(f"❌ ERREUR: {e}\n"))
        self.ollama_worker.start()


# Fonction pour intégrer Ollama dans MASTER COMPTA
def ajouter_ollama_a_master_compta():
    """Ajoute le module Ollama à votre interface MASTER COMPTA"""
    from PySide6.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)

    # Lancement module Ollama
    ollama_module = ModuleOllamaIntegration()
    ollama_module.setWindowTitle("🤖 OLLAMA IA - MASTER COMPTA - INTERFACE NOIRE")
    ollama_module.resize(1200, 800)
    ollama_module.show()

    print("🤖 Module Ollama lancé pour MASTER COMPTA !")
    print("🔗 Connexion à votre Ollama llama3.2 local")
    print("🖤 Interface noire intégrée")

    return app.exec()


if __name__ == "__main__":
    ajouter_ollama_a_master_compta()
