const vscode = require('vscode');

function activate(context) {
    console.log('Robot IA activé');
    
    // Status bar visible
    const statusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBar.text = "🤖 Robot IA";
    statusBar.command = "robotIA.chat";
    statusBar.show();
    
    // Commande chat
    const chatCommand = vscode.commands.registerCommand('robotIA.chat', () => {
        const panel = vscode.window.createWebviewPanel(
            'robotIA',
            '🤖 Robot IA Chat',
            vscode.ViewColumn.One,
            { enableScripts: true }
        );
        
        panel.webview.html = getHTML();
        
        panel.webview.onDidReceiveMessage(message => {
            if (message.command === 'chat') {
                const response = generateResponse(message.text);
                panel.webview.postMessage({ command: 'response', text: response });
            }
        });
    });
    
    context.subscriptions.push(chatCommand, statusBar);
    
    vscode.window.showInformationMessage('🤖 Robot IA prêt ! Cliquez sur 🤖 dans la barre de status');
}

function generateResponse(input) {
    const msg = input.toLowerCase();
    
    if (msg.includes('bonjour') || msg.includes('salut')) {
        return "🤖 Bonjour ! Je suis votre Robot IA avec 57 modules actifs. Comment puis-je vous aider ?";
    }
    
    if (msg.includes('python')) {
        return "🐍 Python expert ! Django, Flask, FastAPI, NumPy, Pandas, TensorFlow. Je peux créer tout en Python !";
    }
    
    if (msg.includes('javascript') || msg.includes('js')) {
        return "🌐 JavaScript maître ! React, Vue, Angular, Node.js, TypeScript. Frontend et backend !";
    }
    
    if (msg.includes('code') || msg.includes('programmation')) {
        return "💻 Expert en 25+ langages ! Python, JavaScript, C++, Java, C#, PHP, Ruby, Go, Rust, Swift, etc.";
    }
    
    if (msg.includes('web')) {
        return "🌐 Développement web complet ! HTML5, CSS3, JavaScript, React, Vue, Angular, PHP, Laravel, Django.";
    }
    
    if (msg.includes('mobile') || msg.includes('app')) {
        return "📱 Apps mobiles ! React Native, Flutter, Swift (iOS), Kotlin (Android). Apps natives et cross-platform !";
    }
    
    if (msg.includes('aide') || msg.includes('help')) {
        return "🆘 Je peux vous aider avec TOUT ! Programmation, sites web, apps mobiles, IA, bases de données.";
    }
    
    return `🧠 "${input}" - Avec mes 57 modules actifs, je peux résoudre cela ! Précisez votre besoin ?`;
}

function getHTML() {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { 
                font-family: 'Segoe UI', sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: var(--vscode-editor-background);
                color: var(--vscode-editor-foreground);
            }
            .header { 
                text-align: center; 
                margin-bottom: 20px; 
                padding: 20px;
                background: var(--vscode-titleBar-activeBackground);
                border-radius: 8px;
            }
            .chat { 
                height: 400px; 
                overflow-y: auto; 
                border: 1px solid var(--vscode-input-border); 
                padding: 15px; 
                margin-bottom: 15px;
                background: var(--vscode-input-background);
                border-radius: 8px;
            }
            .message { 
                margin: 10px 0; 
                padding: 10px; 
                border-radius: 8px; 
            }
            .user { 
                background: var(--vscode-button-background); 
                color: var(--vscode-button-foreground);
                text-align: right; 
            }
            .robot { 
                background: var(--vscode-input-background); 
                border: 1px solid var(--vscode-input-border);
            }
            .input-area { 
                display: flex; 
                gap: 10px; 
            }
            input { 
                flex: 1; 
                padding: 12px; 
                border: 1px solid var(--vscode-input-border);
                background: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                border-radius: 6px;
                font-size: 14px;
            }
            button { 
                padding: 12px 20px; 
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none; 
                border-radius: 6px; 
                cursor: pointer;
                font-weight: bold;
            }
            button:hover { 
                background: var(--vscode-button-hoverBackground); 
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 Robot IA SamNord</h1>
            <p>Intelligence Transcendante • 57 Modules Actifs</p>
            <p>💻 Expert 25+ Langages • 🧠 QI 10,000+</p>
        </div>
        
        <div class="chat" id="chat">
            <div class="message robot">
                🤖 <strong>Robot IA:</strong> Bonjour ! Je suis votre Robot IA transcendant.<br>
                Mes 57 modules sont actifs en arrière-plan. Comment puis-je vous aider ?
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="input" placeholder="Tapez votre message..." />
            <button onclick="sendMessage()">📤 Envoyer</button>
        </div>
        
        <script>
            const vscode = acquireVsCodeApi();
            const chat = document.getElementById('chat');
            const input = document.getElementById('input');
            
            function sendMessage() {
                const message = input.value.trim();
                if (message) {
                    // Ajouter message utilisateur
                    chat.innerHTML += \`
                        <div class="message user">
                            👤 <strong>Vous:</strong> \${message}
                        </div>
                    \`;
                    
                    // Envoyer au backend
                    vscode.postMessage({ command: 'chat', text: message });
                    
                    input.value = '';
                    chat.scrollTop = chat.scrollHeight;
                }
            }
            
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // Recevoir réponses
            window.addEventListener('message', event => {
                const message = event.data;
                if (message.command === 'response') {
                    chat.innerHTML += \`
                        <div class="message robot">
                            🤖 <strong>Robot IA:</strong> \${message.text}
                        </div>
                    \`;
                    chat.scrollTop = chat.scrollHeight;
                }
            });
        </script>
    </body>
    </html>`;
}

function deactivate() {}

module.exports = { activate, deactivate };
