{"version": "2.0.0", "tasks": [{"label": "🤖 Robot IA SamNord - Chat", "type": "shell", "command": "python", "args": ["ACTIVER_ROBOT_IA_ICI.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🧠 Intelligence IA Transcendante", "type": "shell", "command": "python", "args": ["modules/intelligence_ia_transcendante.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "📡 <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "type": "shell", "command": "python", "args": ["modules/wifi_avance_complet.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "📱 Bluetooth Complet", "type": "shell", "command": "python", "args": ["modules/bluetooth_complet.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🛡️ Cybersécurité Éthique", "type": "shell", "command": "python", "args": ["modules/cybersecurite_ethique.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🌍 Traduction Temps Réel", "type": "shell", "command": "python", "args": ["modules/traduction_temps_reel.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🤖 Robot IA Principal", "type": "shell", "command": "python", "args": ["robot_ia_universel_40_modules.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}