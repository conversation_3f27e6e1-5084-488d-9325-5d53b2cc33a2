#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyse approfondie pour identifier TOUS les problèmes restants dans main.py
"""

import ast
import re
import sys
import os
from typing import List, Dict, Any

def analyze_syntax_errors():
    """Analyse les erreurs de syntaxe"""
    print("🔍 Analyse des erreurs de syntaxe...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test AST
        tree = ast.parse(content)
        print("✅ Syntaxe AST correcte")
        
        # Analyser l'AST pour des problèmes spécifiques
        issues = []
        
        for node in ast.walk(tree):
            # Vérifier les fonctions sans annotations de retour
            if isinstance(node, ast.FunctionDef):
                if node.returns is None and node.name != '__init__':
                    issues.append(f"Fonction {node.name} ligne {node.lineno}: pas d'annotation de retour")
            
            # Vérifier les variables non typées
            if isinstance(node, ast.AnnAssign) and node.annotation is None:
                issues.append(f"Variable ligne {node.lineno}: annotation manquante")
        
        if issues:
            print(f"⚠️ {len(issues)} problèmes AST détectés:")
            for issue in issues[:5]:
                print(f"  - {issue}")
        else:
            print("✅ Aucun problème AST détecté")
        
        return len(issues) == 0
        
    except SyntaxError as e:
        print(f"❌ Erreur syntaxe ligne {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def analyze_imports():
    """Analyse les problèmes d'imports"""
    print("\n🔍 Analyse des imports...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        in_function = False
        function_name = ""
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Détecter les fonctions
            if stripped.startswith('def '):
                in_function = True
                function_name = stripped.split('(')[0].replace('def ', '')
            elif stripped.startswith('class '):
                in_function = False
            elif stripped == '' or not stripped.startswith(' '):
                if not stripped.startswith('def ') and not stripped.startswith('class '):
                    in_function = False
            
            # Vérifier les imports dans les fonctions
            if in_function and 'import ' in stripped and not stripped.startswith('#'):
                if 'type: ignore' not in stripped:
                    issues.append(f"Ligne {i}: Import dans fonction {function_name} sans type: ignore")
        
        if issues:
            print(f"⚠️ {len(issues)} problèmes d'imports:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("✅ Imports corrects")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def analyze_variables():
    """Analyse les variables non typées"""
    print("\n🔍 Analyse des variables...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Chercher les assignations sans type
            if ' = ' in stripped and not stripped.startswith('#'):
                # Ignorer certains cas
                if any(x in stripped for x in ['def ', 'class ', 'if ', 'for ', 'while ', 'try:', 'except:', 'with ']):
                    continue
                
                # Vérifier si c'est une variable avec type
                if ': ' not in stripped.split(' = ')[0]:
                    # Ignorer les cas valides
                    if not any(x in stripped for x in ['self.', '__', 'import', 'from']):
                        if not stripped.startswith('_'):
                            issues.append(f"Ligne {i}: Variable non typée - {stripped[:50]}...")
        
        if issues:
            print(f"⚠️ {len(issues)} variables non typées:")
            for issue in issues[:5]:
                print(f"  - {issue}")
        else:
            print("✅ Variables correctement typées")
        
        return len(issues) <= 3  # Acceptable si <= 3
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def analyze_method_calls():
    """Analyse les appels de méthodes non définies"""
    print("\n🔍 Analyse des appels de méthodes...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extraire les appels de méthodes
        method_calls = re.findall(r'self\.(\w+)\(', content)
        method_definitions = re.findall(r'def (\w+)\(', content)
        
        # Méthodes Qt standard à ignorer
        qt_methods = {
            'show', 'hide', 'close', 'accept', 'reject', 'exec', 'exec_',
            'setText', 'text', 'setPlaceholderText', 'setEnabled', 'setReadOnly',
            'setWindowTitle', 'setFixedSize', 'resize', 'setMinimumSize',
            'setStyleSheet', 'setCentralWidget', 'setMenuBar', 'setLayout',
            'addWidget', 'addLayout', 'addAction', 'addStretch', 'addRow',
            'setObjectName', 'setPixmap', 'setFont', 'scaled', 'setEchoMode',
            'clicked', 'connect', 'timeout', 'start', 'stop', 'textChanged',
            'itemChanged', 'triggered', 'setRowCount', 'setColumnCount',
            'insertRow', 'setItem', 'item', 'rowCount', 'columnCount',
            'selectedItems', 'row', 'setUpdatesEnabled', 'setSortingEnabled',
            'setAlternatingRowColors', 'setMaxLength', 'toPlainText',
            'setPlainText', 'mapToGlobal', 'viewport'
        }
        
        issues = []
        for method in set(method_calls):
            if method not in method_definitions and method not in qt_methods:
                # Ignorer les méthodes qui commencent par des préfixes Qt
                if not any(method.startswith(prefix) for prefix in ['set', 'get', 'add', 'remove', 'insert', 'clear', 'update']):
                    issues.append(f"Méthode {method}() appelée mais non définie")
        
        if issues:
            print(f"⚠️ {len(issues)} méthodes non définies:")
            for issue in issues[:5]:
                print(f"  - {issue}")
        else:
            print("✅ Appels de méthodes corrects")
        
        return len(issues) <= 2  # Acceptable si <= 2
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def analyze_code_quality():
    """Analyse la qualité générale du code"""
    print("\n🔍 Analyse de la qualité du code...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.splitlines()
        
        metrics = {
            'total_lines': len(lines),
            'functions': len(re.findall(r'def \w+\(', content)),
            'classes': len(re.findall(r'class \w+', content)),
            'type_annotations': content.count('->') + content.count(': str') + content.count(': int') + content.count(': bool'),
            'error_handling': content.count('try:') + content.count('except'),
            'logging_calls': content.count('logging.'),
            'comments': len([l for l in lines if l.strip().startswith('#')]),
            'docstrings': content.count('"""') // 2
        }
        
        print(f"📊 Métriques du code:")
        for metric, value in metrics.items():
            print(f"  - {metric}: {value}")
        
        # Score de qualité
        quality_score = 0
        if metrics['type_annotations'] >= 50:
            quality_score += 25
        if metrics['error_handling'] >= 20:
            quality_score += 25
        if metrics['logging_calls'] >= 10:
            quality_score += 25
        if metrics['docstrings'] >= 5:
            quality_score += 25
        
        print(f"\n📈 Score de qualité: {quality_score}/100")
        
        return quality_score >= 75
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 ANALYSE APPROFONDIE - Recherche de TOUS les Problèmes")
    print("=" * 65)
    
    tests = [
        ("Erreurs de syntaxe", analyze_syntax_errors),
        ("Problèmes d'imports", analyze_imports),
        ("Variables non typées", analyze_variables),
        ("Appels de méthodes", analyze_method_calls),
        ("Qualité du code", analyze_code_quality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "="*65)
    print("📋 RÉSUMÉ DE L'ANALYSE APPROFONDIE")
    print("="*65)
    
    passed = 0
    for test_name, result in results:
        status = "✅ OK" if result else "❌ PROBLÈME"
        print(f"{test_name:25}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 SCORE: {passed}/{len(results)} aspects corrects")
    
    if passed == len(results):
        print("\n🎉 EXCELLENT ! Aucun problème majeur détecté")
        return 0
    elif passed >= 4:
        print("\n✅ TRÈS BIEN ! Quelques problèmes mineurs")
        return 0
    else:
        print("\n⚠️ Des problèmes importants subsistent")
        return 1

if __name__ == "__main__":
    sys.exit(main())
