#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger TOUS les problèmes identifiés dans tous les fichiers Python
"""

import ast
import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Any

def corriger_fichier_python(file_path: str) -> Dict[str, Any]:
    """Corrige tous les problèmes dans un fichier Python"""
    print(f"\n🔧 CORRECTION DE {file_path}")
    print("-" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier non trouvé: {file_path}")
        return {"success": False, "error": "Fichier non trouvé"}
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        corrections_appliquees = []
        
        print(f"📁 Fichier lu: {len(content)} caractères")
        
        # Test initial de syntaxe
        try:
            ast.parse(content)
            print(f"✅ Syntaxe initiale: OK")
        except SyntaxError as e:
            print(f"❌ Erreur syntaxe initiale ligne {e.lineno}: {e.msg}")
        
        # CORRECTION 1: list[...] -> List[...]
        if 'list[' in content:
            old_content = content
            content = re.sub(r'\blist\[', 'List[', content)
            if content != old_content:
                count = len(re.findall(r'\blist\[', old_content))
                corrections_appliquees.append(f"list[...] -> List[...] ({count} occurrences)")
                print(f"✅ Corrigé: list[...] -> List[...] ({count} occurrences)")
        
        # CORRECTION 2: dict[...] -> Dict[...]
        if 'dict[' in content:
            old_content = content
            content = re.sub(r'\bdict\[', 'Dict[', content)
            if content != old_content:
                count = len(re.findall(r'\bdict\[', old_content))
                corrections_appliquees.append(f"dict[...] -> Dict[...] ({count} occurrences)")
                print(f"✅ Corrigé: dict[...] -> Dict[...] ({count} occurrences)")
        
        # CORRECTION 3: any -> Any
        if ': any =' in content.lower():
            old_content = content
            content = re.sub(r': any =', ': Any =', content, flags=re.IGNORECASE)
            if content != old_content:
                count = len(re.findall(r': any =', old_content, flags=re.IGNORECASE))
                corrections_appliquees.append(f"any -> Any ({count} occurrences)")
                print(f"✅ Corrigé: any -> Any ({count} occurrences)")
        
        # CORRECTION 4: Ajouter imports typing si nécessaires
        needs_typing = False
        typing_imports_needed = []
        
        if 'List[' in content or 'Dict[' in content or 'Any' in content:
            if 'from typing import' not in content:
                needs_typing = True
                if 'List[' in content:
                    typing_imports_needed.append('List')
                if 'Dict[' in content:
                    typing_imports_needed.append('Dict')
                if 'Any' in content:
                    typing_imports_needed.append('Any')
        
        if needs_typing:
            # Ajouter d'autres imports utiles
            typing_imports_needed.extend(['Optional', 'Tuple', 'Union'])
            typing_imports_needed = sorted(set(typing_imports_needed))
            
            # Trouver où insérer l'import
            lines = content.splitlines()
            insert_pos = 0
            
            # Chercher après les imports existants
            for i, line in enumerate(lines):
                if line.strip().startswith('from ') or line.strip().startswith('import '):
                    insert_pos = i + 1
                elif line.strip() and not line.strip().startswith('#'):
                    break
            
            import_line = f"from typing import {', '.join(typing_imports_needed)}"
            lines.insert(insert_pos, import_line)
            content = '\n'.join(lines)
            
            corrections_appliquees.append(f"Import typing ajouté: {', '.join(typing_imports_needed)}")
            print(f"✅ Import typing ajouté: {', '.join(typing_imports_needed)}")
        
        # CORRECTION 5: Problèmes spécifiques par fichier
        if 'fournisseur.py' in file_path:
            # Corriger les annotations spécifiques dans fournisseur.py
            if 'fournisseurs: list[list[str]]' in content:
                content = content.replace('fournisseurs: list[list[str]]', 'fournisseurs: List[List[str]]')
                corrections_appliquees.append("fournisseurs: list[list[str]] -> List[List[str]]")
                print(f"✅ Corrigé: fournisseurs: list[list[str]] -> List[List[str]]")
            
            if 'fournisseur: list[str]' in content:
                content = content.replace('fournisseur: list[str]', 'fournisseur: List[str]')
                corrections_appliquees.append("fournisseur: list[str] -> List[str]")
                print(f"✅ Corrigé: fournisseur: list[str] -> List[str]")
        
        # Test final de syntaxe
        try:
            ast.parse(content)
            print(f"✅ Syntaxe finale: OK")
            syntax_ok = True
        except SyntaxError as e:
            print(f"❌ Erreur syntaxe finale ligne {e.lineno}: {e.msg}")
            syntax_ok = False
        
        # Sauvegarder si des corrections ont été appliquées et la syntaxe est OK
        if corrections_appliquees and syntax_ok:
            # Créer un backup
            backup_path = file_path + '.backup_corrections'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # Sauvegarder le fichier corrigé
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"💾 Fichier sauvegardé avec {len(corrections_appliquees)} correction(s)")
            print(f"💾 Backup créé: {backup_path}")
            
            return {
                "success": True,
                "corrections": corrections_appliquees,
                "backup": backup_path
            }
        elif corrections_appliquees and not syntax_ok:
            print(f"⚠️ Corrections trouvées mais erreur de syntaxe - fichier non sauvegardé")
            return {
                "success": False,
                "error": "Erreur de syntaxe après corrections",
                "corrections": corrections_appliquees
            }
        else:
            print(f"✅ Aucune correction nécessaire")
            return {
                "success": True,
                "corrections": []
            }
            
    except Exception as e:
        print(f"❌ Erreur lors du traitement: {e}")
        return {"success": False, "error": str(e)}

def corriger_fichier_sql():
    """Corrige le fichier SQL init.sql"""
    print(f"\n🔧 CORRECTION DU FICHIER SQL")
    print("-" * 60)
    
    sql_file = 'src/database/migrations/init.sql'
    
    if not os.path.exists(sql_file):
        print(f"❌ Fichier SQL non trouvé: {sql_file}")
        return {"success": False, "error": "Fichier SQL non trouvé"}
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Compter les problèmes avant correction
        serial_count_before = len(re.findall(r'\bSERIAL\s+PRIMARY\s+KEY\b', content, re.IGNORECASE))
        boolean_count_before = len(re.findall(r'\bBOOLEAN\b', content, re.IGNORECASE))
        
        print(f"📊 Avant correction:")
        print(f"   SERIAL PRIMARY KEY: {serial_count_before} occurrences")
        print(f"   BOOLEAN: {boolean_count_before} occurrences")
        
        if serial_count_before == 0 and boolean_count_before == 0:
            print(f"✅ Fichier SQL déjà correct")
            return {"success": True, "corrections": []}
        
        # Backup
        backup_path = sql_file + '.backup_corrections'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Corrections
        content = re.sub(
            r'\bid\s+SERIAL\s+PRIMARY\s+KEY\b',
            'id INTEGER PRIMARY KEY AUTOINCREMENT',
            content,
            flags=re.IGNORECASE
        )
        
        content = re.sub(
            r'\bBOOLEAN\b',
            'INTEGER',
            content,
            flags=re.IGNORECASE
        )
        
        # Compter après correction
        serial_count_after = len(re.findall(r'\bSERIAL\s+PRIMARY\s+KEY\b', content, re.IGNORECASE))
        boolean_count_after = len(re.findall(r'\bBOOLEAN\b', content, re.IGNORECASE))
        
        # Sauvegarder
        with open(sql_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        corrections = []
        if serial_count_before > 0:
            corrections.append(f"SERIAL PRIMARY KEY -> INTEGER PRIMARY KEY AUTOINCREMENT ({serial_count_before} corrections)")
        if boolean_count_before > 0:
            corrections.append(f"BOOLEAN -> INTEGER ({boolean_count_before} corrections)")
        
        print(f"📊 Après correction:")
        print(f"   SERIAL PRIMARY KEY: {serial_count_after} occurrences")
        print(f"   BOOLEAN: {boolean_count_after} occurrences")
        print(f"💾 Fichier SQL sauvegardé")
        print(f"💾 Backup créé: {backup_path}")
        
        return {
            "success": True,
            "corrections": corrections,
            "backup": backup_path
        }
        
    except Exception as e:
        print(f"❌ Erreur SQL: {e}")
        return {"success": False, "error": str(e)}

def main():
    """Correction complète de tous les problèmes"""
    print("🚀 CORRECTION COMPLÈTE DE TOUS LES PROBLÈMES")
    print("=" * 80)
    
    # Trouver tous les fichiers Python
    fichiers_python = []
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if not d.startswith('.')]
        for file in files:
            if file.endswith('.py') and not file.startswith('corriger_'):
                fichiers_python.append(os.path.join(root, file))
    
    print(f"📁 Fichiers Python trouvés: {len(fichiers_python)}")
    
    # Correction des fichiers Python
    resultats_python = []
    corrections_totales = 0
    
    for fichier in sorted(fichiers_python):
        resultat = corriger_fichier_python(fichier)
        resultats_python.append(resultat)
        if resultat.get("success") and resultat.get("corrections"):
            corrections_totales += len(resultat["corrections"])
    
    # Correction du fichier SQL
    resultat_sql = corriger_fichier_sql()
    if resultat_sql.get("success") and resultat_sql.get("corrections"):
        corrections_totales += len(resultat_sql["corrections"])
    
    # Résumé final
    print(f"\n{'='*80}")
    print("📊 RÉSUMÉ FINAL DES CORRECTIONS")
    print(f"{'='*80}")
    
    python_success = sum(1 for r in resultats_python if r.get("success"))
    python_total = len(resultats_python)
    sql_success = 1 if resultat_sql.get("success") else 0
    
    print(f"📁 Fichiers Python traités: {python_total}")
    print(f"✅ Fichiers Python corrigés: {python_success}")
    print(f"✅ Fichier SQL corrigé: {'Oui' if sql_success else 'Non'}")
    print(f"🔧 Total corrections appliquées: {corrections_totales}")
    
    # Détail des corrections
    if corrections_totales > 0:
        print(f"\n🔧 DÉTAIL DES CORRECTIONS:")
        
        for i, resultat in enumerate(resultats_python):
            if resultat.get("corrections"):
                fichier = sorted(fichiers_python)[i]
                print(f"\n📄 {fichier}:")
                for correction in resultat["corrections"]:
                    print(f"   ✅ {correction}")
        
        if resultat_sql.get("corrections"):
            print(f"\n📄 SQL (init.sql):")
            for correction in resultat_sql["corrections"]:
                print(f"   ✅ {correction}")
    
    # Test final
    print(f"\n🔍 VALIDATION FINALE:")
    erreurs_finales = 0
    
    for fichier in sorted(fichiers_python):
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
            print(f"✅ {fichier}: Syntaxe OK")
        except Exception as e:
            print(f"❌ {fichier}: {e}")
            erreurs_finales += 1
    
    # Conclusion
    if erreurs_finales == 0 and corrections_totales > 0:
        print(f"\n🎉 CORRECTION COMPLÈTE RÉUSSIE !")
        print(f"✅ {corrections_totales} correction(s) appliquée(s)")
        print(f"✅ Tous les fichiers sont maintenant corrects")
        print(f"✅ L'application peut être lancée")
        return True
    elif erreurs_finales == 0:
        print(f"\n✅ TOUS LES FICHIERS ÉTAIENT DÉJÀ CORRECTS")
        print(f"✅ L'application peut être lancée")
        return True
    else:
        print(f"\n⚠️ {erreurs_finales} FICHIER(S) AVEC DES ERREURS PERSISTANTES")
        print(f"❌ Corrections supplémentaires nécessaires")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat final: {'SUCCÈS COMPLET' if success else 'CORRECTIONS PARTIELLES'}")
    sys.exit(0 if success else 1)
