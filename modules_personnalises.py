#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modules Personnalisés pour GestImmob
Critères spécifiques et spéciaux pour chaque secteur/domaine
"""

import sqlite3
from datetime import datetime
from typing import Dict, List, Any

class ModulesPersonnalises:
    """Gestionnaire des modules personnalisés par secteur"""
    
    def __init__(self, db_connection):
        self.conn = db_connection
        self.init_modules_database()
        
    def init_modules_database(self):
        """Initialise les tables pour les modules personnalisés"""
        cursor = self.conn.cursor()
        
        # Table des secteurs personnalisés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS secteurs_personnalises (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT UNIQUE NOT NULL,
                description TEXT,
                couleur TEXT DEFAULT '#1976d2',
                icone TEXT DEFAULT '🏢',
                criteres_specifiques TEXT, -- JSON des critères
                is_active INTEGER DEFAULT 1,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des critères spécifiques par secteur
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS criteres_secteur (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                secteur_id INTEGER,
                nom_critere TEXT NOT NULL,
                type_critere TEXT NOT NULL, -- text, number, date, select, boolean
                valeurs_possibles TEXT, -- JSON pour les listes déroulantes
                obligatoire INTEGER DEFAULT 0,
                ordre_affichage INTEGER DEFAULT 0,
                FOREIGN KEY (secteur_id) REFERENCES secteurs_personnalises (id)
            )
        ''')
        
        # Table des valeurs des critères pour chaque bien
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS valeurs_criteres (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bien_id INTEGER,
                critere_id INTEGER,
                valeur TEXT,
                date_saisie TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bien_id) REFERENCES immobilisations (id),
                FOREIGN KEY (critere_id) REFERENCES criteres_secteur (id)
            )
        ''')
        
        # Table des modèles de secteurs prédéfinis
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS modeles_secteurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                description TEXT,
                template_json TEXT, -- Configuration complète du secteur
                categorie TEXT DEFAULT 'Standard'
            )
        ''')
        
        self.conn.commit()
        self.init_secteurs_predefinies()
    
    def init_secteurs_predefinies(self):
        """Initialise les secteurs prédéfinis avec critères spécifiques"""
        cursor = self.conn.cursor()
        
        # Vérifier si des secteurs existent déjà
        cursor.execute("SELECT COUNT(*) FROM secteurs_personnalises")
        if cursor.fetchone()[0] > 0:
            return
        
        secteurs_predefinies = [
            {
                'nom': 'Informatique & Technologies',
                'description': 'Équipements informatiques, serveurs, logiciels',
                'couleur': '#2196F3',
                'icone': '💻',
                'criteres': [
                    {'nom': 'Marque', 'type': 'select', 'valeurs': ['Dell', 'HP', 'Lenovo', 'Apple', 'Asus', 'Autre'], 'obligatoire': 1},
                    {'nom': 'Modèle', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Numéro de série', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Système d\'exploitation', 'type': 'select', 'valeurs': ['Windows 11', 'Windows 10', 'macOS', 'Linux', 'Autre'], 'obligatoire': 0},
                    {'nom': 'RAM (Go)', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Stockage (Go)', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Processeur', 'type': 'text', 'obligatoire': 0},
                    {'nom': 'Garantie jusqu\'au', 'type': 'date', 'obligatoire': 0},
                    {'nom': 'Licence logicielle', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'IP fixe', 'type': 'text', 'obligatoire': 0}
                ]
            },
            {
                'nom': 'Véhicules & Transport',
                'description': 'Voitures, camions, véhicules utilitaires',
                'couleur': '#FF9800',
                'icone': '🚗',
                'criteres': [
                    {'nom': 'Marque', 'type': 'select', 'valeurs': ['Renault', 'Peugeot', 'Citroën', 'Volkswagen', 'BMW', 'Mercedes', 'Autre'], 'obligatoire': 1},
                    {'nom': 'Modèle', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Immatriculation', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Kilométrage', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Carburant', 'type': 'select', 'valeurs': ['Essence', 'Diesel', 'Électrique', 'Hybride', 'GPL'], 'obligatoire': 1},
                    {'nom': 'Puissance (CV)', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Date mise en circulation', 'type': 'date', 'obligatoire': 1},
                    {'nom': 'Contrôle technique', 'type': 'date', 'obligatoire': 0},
                    {'nom': 'Assurance jusqu\'au', 'type': 'date', 'obligatoire': 1},
                    {'nom': 'Conducteur principal', 'type': 'text', 'obligatoire': 0}
                ]
            },
            {
                'nom': 'Mobilier & Aménagement',
                'description': 'Bureaux, chaises, mobilier de bureau',
                'couleur': '#8BC34A',
                'icone': '🪑',
                'criteres': [
                    {'nom': 'Type de mobilier', 'type': 'select', 'valeurs': ['Bureau', 'Chaise', 'Armoire', 'Table', 'Étagère', 'Autre'], 'obligatoire': 1},
                    {'nom': 'Matériau principal', 'type': 'select', 'valeurs': ['Bois', 'Métal', 'Plastique', 'Verre', 'Tissu', 'Cuir'], 'obligatoire': 0},
                    {'nom': 'Couleur', 'type': 'text', 'obligatoire': 0},
                    {'nom': 'Dimensions (L×l×h)', 'type': 'text', 'obligatoire': 0},
                    {'nom': 'Poids (kg)', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Ergonomique', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'Réglable', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'État d\'usure', 'type': 'select', 'valeurs': ['Neuf', 'Très bon', 'Bon', 'Correct', 'Usé'], 'obligatoire': 0}
                ]
            },
            {
                'nom': 'Équipements Industriels',
                'description': 'Machines, outils industriels, équipements de production',
                'couleur': '#F44336',
                'icone': '⚙️',
                'criteres': [
                    {'nom': 'Type d\'équipement', 'type': 'select', 'valeurs': ['Machine-outil', 'Compresseur', 'Générateur', 'Pompe', 'Convoyeur', 'Autre'], 'obligatoire': 1},
                    {'nom': 'Puissance (kW)', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Tension (V)', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Capacité/Débit', 'type': 'text', 'obligatoire': 0},
                    {'nom': 'Dernière maintenance', 'type': 'date', 'obligatoire': 0},
                    {'nom': 'Prochaine maintenance', 'type': 'date', 'obligatoire': 0},
                    {'nom': 'Heures de fonctionnement', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Certification CE', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'Manuel disponible', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'Technicien responsable', 'type': 'text', 'obligatoire': 0}
                ]
            },
            {
                'nom': 'Immobilier & Bâtiments',
                'description': 'Bâtiments, terrains, locaux commerciaux',
                'couleur': '#9C27B0',
                'icone': '🏢',
                'criteres': [
                    {'nom': 'Type de bien', 'type': 'select', 'valeurs': ['Bureau', 'Entrepôt', 'Local commercial', 'Terrain', 'Parking', 'Autre'], 'obligatoire': 1},
                    {'nom': 'Surface (m²)', 'type': 'number', 'obligatoire': 1},
                    {'nom': 'Adresse complète', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Code postal', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Ville', 'type': 'text', 'obligatoire': 1},
                    {'nom': 'Étage', 'type': 'number', 'obligatoire': 0},
                    {'nom': 'Ascenseur', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'Parking', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'Chauffage', 'type': 'select', 'valeurs': ['Électrique', 'Gaz', 'Fioul', 'Pompe à chaleur', 'Autre'], 'obligatoire': 0},
                    {'nom': 'Classe énergétique', 'type': 'select', 'valeurs': ['A', 'B', 'C', 'D', 'E', 'F', 'G'], 'obligatoire': 0}
                ]
            },
            {
                'nom': 'Équipements Médicaux',
                'description': 'Matériel médical, équipements de santé',
                'couleur': '#00BCD4',
                'icone': '🏥',
                'criteres': [
                    {'nom': 'Type d\'équipement', 'type': 'select', 'valeurs': ['Diagnostic', 'Thérapeutique', 'Monitoring', 'Chirurgical', 'Autre'], 'obligatoire': 1},
                    {'nom': 'Classe médicale', 'type': 'select', 'valeurs': ['Classe I', 'Classe IIa', 'Classe IIb', 'Classe III'], 'obligatoire': 1},
                    {'nom': 'Certification CE', 'type': 'boolean', 'obligatoire': 1},
                    {'nom': 'FDA approuvé', 'type': 'boolean', 'obligatoire': 0},
                    {'nom': 'Dernière calibration', 'type': 'date', 'obligatoire': 0},
                    {'nom': 'Prochaine calibration', 'type': 'date', 'obligatoire': 0},
                    {'nom': 'Service/Département', 'type': 'text', 'obligatoire': 0},
                    {'nom': 'Responsable médical', 'type': 'text', 'obligatoire': 0},
                    {'nom': 'Formation requise', 'type': 'boolean', 'obligatoire': 0}
                ]
            }
        ]
        
        for secteur in secteurs_predefinies:
            self.creer_secteur_personnalise(
                secteur['nom'],
                secteur['description'],
                secteur['couleur'],
                secteur['icone'],
                secteur['criteres']
            )
    
    def creer_secteur_personnalise(self, nom: str, description: str, couleur: str, icone: str, criteres: List[Dict]):
        """Crée un nouveau secteur personnalisé avec ses critères"""
        cursor = self.conn.cursor()
        
        # Insérer le secteur
        cursor.execute('''
            INSERT INTO secteurs_personnalises (nom, description, couleur, icone)
            VALUES (?, ?, ?, ?)
        ''', (nom, description, couleur, icone))
        
        secteur_id = cursor.lastrowid
        
        # Insérer les critères
        for i, critere in enumerate(criteres):
            valeurs_json = None
            if 'valeurs' in critere:
                import json
                valeurs_json = json.dumps(critere['valeurs'])
            
            cursor.execute('''
                INSERT INTO criteres_secteur 
                (secteur_id, nom_critere, type_critere, valeurs_possibles, obligatoire, ordre_affichage)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                secteur_id,
                critere['nom'],
                critere['type'],
                valeurs_json,
                critere.get('obligatoire', 0),
                i
            ))
        
        self.conn.commit()
        return secteur_id
    
    def get_secteurs_disponibles(self) -> List[Dict]:
        """Récupère tous les secteurs disponibles"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, nom, description, couleur, icone, is_active
            FROM secteurs_personnalises
            WHERE is_active = 1
            ORDER BY nom
        ''')
        
        secteurs = []
        for row in cursor.fetchall():
            secteurs.append({
                'id': row[0],
                'nom': row[1],
                'description': row[2],
                'couleur': row[3],
                'icone': row[4],
                'is_active': row[5]
            })
        
        return secteurs
    
    def get_criteres_secteur(self, secteur_id: int) -> List[Dict]:
        """Récupère les critères d'un secteur spécifique"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, nom_critere, type_critere, valeurs_possibles, obligatoire, ordre_affichage
            FROM criteres_secteur
            WHERE secteur_id = ?
            ORDER BY ordre_affichage
        ''', (secteur_id,))
        
        criteres = []
        for row in cursor.fetchall():
            import json
            valeurs = None
            if row[3]:
                try:
                    valeurs = json.loads(row[3])
                except:
                    valeurs = None
            
            criteres.append({
                'id': row[0],
                'nom': row[1],
                'type': row[2],
                'valeurs_possibles': valeurs,
                'obligatoire': bool(row[4]),
                'ordre': row[5]
            })
        
        return criteres
    
    def sauvegarder_valeurs_criteres(self, bien_id: int, valeurs_criteres: Dict[int, str]):
        """Sauvegarde les valeurs des critères pour un bien"""
        cursor = self.conn.cursor()
        
        # Supprimer les anciennes valeurs
        cursor.execute('DELETE FROM valeurs_criteres WHERE bien_id = ?', (bien_id,))
        
        # Insérer les nouvelles valeurs
        for critere_id, valeur in valeurs_criteres.items():
            if valeur:  # Ne sauvegarder que les valeurs non vides
                cursor.execute('''
                    INSERT INTO valeurs_criteres (bien_id, critere_id, valeur)
                    VALUES (?, ?, ?)
                ''', (bien_id, critere_id, str(valeur)))
        
        self.conn.commit()
    
    def get_valeurs_criteres_bien(self, bien_id: int) -> Dict[int, str]:
        """Récupère les valeurs des critères pour un bien"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT critere_id, valeur
            FROM valeurs_criteres
            WHERE bien_id = ?
        ''', (bien_id,))
        
        valeurs = {}
        for row in cursor.fetchall():
            valeurs[row[0]] = row[1]
        
        return valeurs
    
    def rechercher_par_criteres(self, secteur_id: int, criteres_recherche: Dict) -> List[Dict]:
        """Recherche des biens selon des critères spécifiques"""
        cursor = self.conn.cursor()
        
        # Construction de la requête dynamique
        query = '''
            SELECT DISTINCT i.id, i.designation, i.valeur, i.localisation
            FROM immobilisations i
            JOIN secteurs_personnalises s ON i.secteur = s.nom
            WHERE s.id = ?
        '''
        params = [secteur_id]
        
        for critere_id, valeur_recherche in criteres_recherche.items():
            if valeur_recherche:
                query += '''
                    AND EXISTS (
                        SELECT 1 FROM valeurs_criteres vc
                        WHERE vc.bien_id = i.id
                        AND vc.critere_id = ?
                        AND vc.valeur LIKE ?
                    )
                '''
                params.extend([critere_id, f'%{valeur_recherche}%'])
        
        cursor.execute(query, params)
        
        resultats = []
        for row in cursor.fetchall():
            resultats.append({
                'id': row[0],
                'designation': row[1],
                'valeur': row[2],
                'localisation': row[3]
            })
        
        return resultats
    
    def generer_rapport_secteur(self, secteur_id: int) -> Dict:
        """Génère un rapport détaillé pour un secteur"""
        cursor = self.conn.cursor()
        
        # Informations du secteur
        cursor.execute('''
            SELECT nom, description, couleur, icone
            FROM secteurs_personnalises
            WHERE id = ?
        ''', (secteur_id,))
        
        secteur_info = cursor.fetchone()
        if not secteur_info:
            return None
        
        # Statistiques des biens
        cursor.execute('''
            SELECT COUNT(*), AVG(valeur), SUM(valeur)
            FROM immobilisations i
            JOIN secteurs_personnalises s ON i.secteur = s.nom
            WHERE s.id = ?
        ''', (secteur_id,))
        
        stats = cursor.fetchone()
        
        # Répartition par état
        cursor.execute('''
            SELECT etat, COUNT(*)
            FROM immobilisations i
            JOIN secteurs_personnalises s ON i.secteur = s.nom
            WHERE s.id = ?
            GROUP BY etat
        ''', (secteur_id,))
        
        repartition_etat = dict(cursor.fetchall())
        
        return {
            'secteur': {
                'nom': secteur_info[0],
                'description': secteur_info[1],
                'couleur': secteur_info[2],
                'icone': secteur_info[3]
            },
            'statistiques': {
                'nombre_biens': stats[0] or 0,
                'valeur_moyenne': stats[1] or 0,
                'valeur_totale': stats[2] or 0
            },
            'repartition_etat': repartition_etat
        }
