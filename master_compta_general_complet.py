#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Complète Multilingue
TOUS les 16 modules avec critères détaillés + Interface 3 colonnes
Date/Heure/Calendrier intégrés + Design optimisé
Français • العربية • English
"""

import sys
import sqlite3
import json
import math
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QFrame,
    QScrollArea, QGridLayout, QComboBox, QLineEdit, QTextEdit,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QDateEdit,
    QCheckBox, QProgressBar, QSystemTrayIcon, QMenu, QSplitter,
    QTabWidget, QGroupBox, QSlider, QToolTip, QMenuBar, QStatusBar,
    QToolBar, QMessageBox, QFileDialog, QInputDialog, QColorDialog,
    QTimeEdit, QDateTimeEdit, QCalendarWidget
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QPropertyAnimation, QEasingCurve, QSize, QTranslator, QDate, QTime, QDateTime
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon, QAction, QPalette

class MultilingueManager:
    """Gestionnaire multilingue pour MASTER COMPTA GÉNÉRAL"""
    
    def __init__(self):
        self.current_language = "fr"
        self.translations = self.load_translations()
    
    def load_translations(self):
        """Charge toutes les traductions pour MASTER COMPTA GÉNÉRAL"""
        return {
            # Application
            "app_title": {
                "fr": "🏢 MASTER COMPTA GÉNÉRAL - Gestion Complète",
                "ar": "🏢 ماستر كومبتا جنرال - الإدارة الشاملة", 
                "en": "🏢 MASTER COMPTA GENERAL - Complete Management"
            },
            
            # Modules complets
            "module_gestion_biens": {
                "fr": "🏠 Gestion des Biens",
                "ar": "🏠 إدارة الممتلكات",
                "en": "🏠 Asset Management"
            },
            "module_fournisseurs": {
                "fr": "🏢 Fournisseurs", 
                "ar": "🏢 الموردون",
                "en": "🏢 Suppliers"
            },
            "module_inventaire": {
                "fr": "📦 Inventaire",
                "ar": "📦 المخزون", 
                "en": "📦 Inventory"
            },
            "module_parc_auto": {
                "fr": "🚗 Parc Automobile",
                "ar": "🚗 أسطول السيارات",
                "en": "🚗 Vehicle Fleet"
            },
            "module_animaux": {
                "fr": "🐄 Suivi des Animaux",
                "ar": "🐄 متابعة الحيوانات", 
                "en": "🐄 Animal Tracking"
            },
            "module_travaux": {
                "fr": "🔨 Suivi des Travaux",
                "ar": "🔨 متابعة الأشغال",
                "en": "🔨 Work Tracking"
            },
            "module_outils": {
                "fr": "🔧 Outils de Travaux",
                "ar": "🔧 أدوات العمل",
                "en": "🔧 Work Tools"
            },
            "module_calculatrice": {
                "fr": "🧮 Calculatrice",
                "ar": "🧮 آلة حاسبة",
                "en": "🧮 Calculator"
            },
            "module_documents": {
                "fr": "📄 Documents",
                "ar": "📄 الوثائق",
                "en": "📄 Documents"
            },
            "module_impression": {
                "fr": "🖨️ Impression",
                "ar": "🖨️ الطباعة", 
                "en": "🖨️ Printing"
            },
            "module_archive": {
                "fr": "📚 Archive",
                "ar": "📚 الأرشيف",
                "en": "📚 Archive"
            },
            "module_outillage": {
                "fr": "⚒️ Outillage",
                "ar": "⚒️ الأدوات",
                "en": "⚒️ Tooling"
            },
            "module_moyens_generaux": {
                "fr": "🏭 Moyens Généraux",
                "ar": "🏭 الوسائل العامة",
                "en": "🏭 General Means"
            },
            "module_hotellerie": {
                "fr": "🏨 Hôtellerie",
                "ar": "🏨 الفندقة",
                "en": "🏨 Hospitality"
            },
            "module_agencement": {
                "fr": "🪑 Agencement",
                "ar": "🪑 التأثيث",
                "en": "🪑 Layout"
            },
            "module_batis": {
                "fr": "🏗️ Bâtis",
                "ar": "🏗️ المباني",
                "en": "🏗️ Buildings"
            },
            
            # Interface
            "current_date": {
                "fr": "📅 Date actuelle",
                "ar": "📅 التاريخ الحالي",
                "en": "📅 Current date"
            },
            "current_time": {
                "fr": "🕐 Heure actuelle",
                "ar": "🕐 الوقت الحالي",
                "en": "🕐 Current time"
            },
            "calendar": {
                "fr": "📅 Calendrier",
                "ar": "📅 التقويم",
                "en": "📅 Calendar"
            },
            
            # Critères communs
            "field_code": {
                "fr": "🔢 Code",
                "ar": "🔢 الرمز",
                "en": "🔢 Code"
            },
            "field_designation": {
                "fr": "📝 Désignation",
                "ar": "📝 التسمية",
                "en": "📝 Designation"
            },
            "field_value": {
                "fr": "💰 Valeur",
                "ar": "💰 القيمة",
                "en": "💰 Value"
            },
            "field_date": {
                "fr": "📅 Date",
                "ar": "📅 التاريخ",
                "en": "📅 Date"
            },
            "field_observation": {
                "fr": "📋 Observation",
                "ar": "📋 ملاحظة",
                "en": "📋 Observation"
            },
            "field_status": {
                "fr": "📊 Statut",
                "ar": "📊 الحالة",
                "en": "📊 Status"
            },
            "field_category": {
                "fr": "📂 Catégorie",
                "ar": "📂 الفئة", 
                "en": "📂 Category"
            },
            "field_location": {
                "fr": "📍 Localisation",
                "ar": "📍 الموقع",
                "en": "📍 Location"
            },
            
            # Boutons
            "btn_validate": {
                "fr": "✅ Valider",
                "ar": "✅ تأكيد",
                "en": "✅ Validate"
            },
            "btn_modify": {
                "fr": "✏️ Modifier", 
                "ar": "✏️ تعديل",
                "en": "✏️ Modify"
            },
            "btn_cancel": {
                "fr": "❌ Annuler",
                "ar": "❌ إلغاء",
                "en": "❌ Cancel"
            },
            "btn_back": {
                "fr": "🔙 Retour",
                "ar": "🔙 رجوع",
                "en": "🔙 Back"
            },
            "btn_print": {
                "fr": "🖨️ Imprimer",
                "ar": "🖨️ طباعة",
                "en": "🖨️ Print"
            },
            "btn_email": {
                "fr": "📧 Envoyer par mail",
                "ar": "📧 إرسال بالبريد",
                "en": "📧 Send by email"
            },
            "btn_report": {
                "fr": "📊 Rapport",
                "ar": "📊 تقرير", 
                "en": "📊 Report"
            }
        }
    
    def get_text(self, key):
        """Récupère le texte traduit"""
        if key in self.translations:
            return self.translations[key].get(self.current_language, self.translations[key]["fr"])
        return key
    
    def set_language(self, language):
        """Change la langue"""
        if language in ["fr", "ar", "en"]:
            self.current_language = language

class ColorManager:
    """Gestionnaire de couleurs pour MASTER COMPTA GÉNÉRAL"""
    
    def __init__(self):
        self.color_schemes = {
            "master_compta": {
                "primary": "#1E3A8A",
                "secondary": "#1E40AF", 
                "accent": "#3B82F6",
                "background": "#F8FAFC",
                "text": "#1F2937",
                "button": "#2563EB"
            },
            "bleu_turquoise": {
                "primary": "#40E0D0",
                "secondary": "#20B2AA", 
                "accent": "#00FFFF",
                "background": "#F0FFFF",
                "text": "#000000",
                "button": "#48D1CC"
            },
            "sage_classique": {
                "primary": "#2E8B57",
                "secondary": "#228B22",
                "accent": "#32CD32", 
                "background": "#F0FFF0",
                "text": "#000000",
                "button": "#3CB371"
            },
            "odoo_style": {
                "primary": "#714B67",
                "secondary": "#875A7B",
                "accent": "#A24689",
                "background": "#F9F9F9", 
                "text": "#000000",
                "button": "#8F5A8A"
            },
            "professionnel": {
                "primary": "#1976D2",
                "secondary": "#1565C0",
                "accent": "#2196F3",
                "background": "#E3F2FD",
                "text": "#000000", 
                "button": "#1E88E5"
            }
        }
        self.current_scheme = "master_compta"
    
    def get_colors(self):
        """Retourne le schéma de couleurs actuel"""
        return self.color_schemes[self.current_scheme]
    
    def set_scheme(self, scheme_name):
        """Change le schéma de couleurs"""
        if scheme_name in self.color_schemes:
            self.current_scheme = scheme_name

class ModuleInterface3Colonnes(QDialog):
    """Interface base pour modules avec 3 colonnes et date/heure"""
    
    def __init__(self, parent=None, ml_manager=None, color_manager=None, module_name="", module_key=""):
        super().__init__(parent)
        self.ml = ml_manager
        self.cm = color_manager
        self.module_name = module_name
        self.module_key = module_key
        
        self.setWindowTitle(f"MASTER COMPTA GÉNÉRAL - {module_name}")
        self.setFixedSize(1400, 900)
        self.setup_interface_3_colonnes()
    
    def setup_interface_3_colonnes(self):
        """Configure l'interface avec 3 colonnes et date/heure"""
        layout = QVBoxLayout(self)
        
        # Style avec couleurs MASTER COMPTA GÉNÉRAL
        colors = self.cm.get_colors()
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['primary']}, stop:1 {colors['secondary']});
                border: 3px solid {colors['accent']};
            }}
            QLineEdit, QComboBox, QTextEdit, QDateEdit, QSpinBox, QTimeEdit, QDateTimeEdit {{
                background-color: {colors['background']};
                color: {colors['text']};
                border: 2px solid {colors['accent']};
                border-radius: 8px;
                padding: 8px;
                font-size: 12pt;
                min-height: 25px;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['button']}, stop:1 {colors['secondary']});
                color: white;
                border: 2px solid {colors['accent']};
                border-radius: 10px;
                font-size: 12pt;
                font-weight: bold;
                padding: 12px;
                min-width: 130px;
                min-height: 45px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['accent']}, stop:1 {colors['primary']});
                transform: scale(1.02);
            }}
            QLabel {{
                color: white;
                font-size: 14pt;
                font-weight: bold;
            }}
            QGroupBox {{
                color: white;
                font-size: 13pt;
                font-weight: bold;
                border: 2px solid {colors['accent']};
                border-radius: 10px;
                margin: 10px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }}
        """)
        
        # En-tête avec titre et date/heure
        self.create_header_with_datetime(layout)
        
        # Zone principale avec 3 colonnes
        self.create_3_columns_area(layout)
        
        # Boutons d'action
        self.create_action_buttons(layout)
    
    def create_header_with_datetime(self, layout):
        """Crée l'en-tête avec date/heure/calendrier"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(0,0,0,0.3);
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
            }
        """)
        header_frame.setFixedHeight(120)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Titre du module
        title_label = QLabel(f"📋 {self.module_name}")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        title_label.setStyleSheet("font-size: 20pt; font-weight: bold;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Zone date/heure/calendrier
        datetime_frame = QFrame()
        datetime_layout = QVBoxLayout(datetime_frame)
        
        # Date actuelle
        self.current_date = QLabel()
        self.current_date.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_date.setStyleSheet("font-size: 14pt; color: #FFD700;")
        datetime_layout.addWidget(self.current_date)
        
        # Heure actuelle
        self.current_time = QLabel()
        self.current_time.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_time.setStyleSheet("font-size: 16pt; color: #00FFFF; font-weight: bold;")
        datetime_layout.addWidget(self.current_time)
        
        # Bouton calendrier
        calendar_btn = QPushButton("📅 Calendrier")
        calendar_btn.setFixedSize(120, 35)
        calendar_btn.clicked.connect(self.show_calendar)
        datetime_layout.addWidget(calendar_btn)
        
        header_layout.addWidget(datetime_frame)
        
        layout.addWidget(header_frame)
        
        # Timer pour mettre à jour l'heure
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)  # Mise à jour chaque seconde
        self.update_datetime()
    
    def update_datetime(self):
        """Met à jour la date et l'heure"""
        now = datetime.now()
        
        # Date en français
        date_str = f"{self.ml.get_text('current_date')}: {now.strftime('%d/%m/%Y')}"
        self.current_date.setText(date_str)
        
        # Heure
        time_str = f"{self.ml.get_text('current_time')}: {now.strftime('%H:%M:%S')}"
        self.current_time.setText(time_str)
    
    def show_calendar(self):
        """Affiche le calendrier"""
        calendar_dialog = QDialog(self)
        calendar_dialog.setWindowTitle(self.ml.get_text("calendar"))
        calendar_dialog.setFixedSize(400, 350)
        
        layout = QVBoxLayout(calendar_dialog)
        
        calendar = QCalendarWidget()
        calendar.setStyleSheet("""
            QCalendarWidget {
                background-color: white;
                border: 2px solid #1E3A8A;
                border-radius: 10px;
            }
            QCalendarWidget QToolButton {
                background-color: #1E3A8A;
                color: white;
                border-radius: 5px;
                padding: 5px;
            }
            QCalendarWidget QMenu {
                background-color: white;
                border: 1px solid #1E3A8A;
            }
            QCalendarWidget QSpinBox {
                background-color: white;
                border: 1px solid #1E3A8A;
                border-radius: 3px;
            }
        """)
        layout.addWidget(calendar)
        
        close_btn = QPushButton("✅ Fermer")
        close_btn.clicked.connect(calendar_dialog.accept)
        layout.addWidget(close_btn)
        
        calendar_dialog.exec()
    
    def create_3_columns_area(self, layout):
        """Crée la zone principale avec 3 colonnes"""
        # Scroll area pour le contenu
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                background: transparent;
            }
        """)
        
        # Widget principal avec 3 colonnes
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setSpacing(15)
        
        # Colonne 1 - Identification et informations de base
        col1 = self.create_column_1()
        main_layout.addWidget(col1)
        
        # Colonne 2 - Détails techniques et financiers
        col2 = self.create_column_2()
        main_layout.addWidget(col2)
        
        # Colonne 3 - Localisation et observations
        col3 = self.create_column_3()
        main_layout.addWidget(col3)
        
        scroll_area.setWidget(main_widget)
        layout.addWidget(scroll_area)
    
    def create_column_1(self):
        """Crée la colonne 1 - Identification"""
        group = QGroupBox("🔢 Identification & Base")
        layout = QFormLayout(group)
        layout.setSpacing(10)
        
        # À implémenter dans les classes dérivées
        return group
    
    def create_column_2(self):
        """Crée la colonne 2 - Détails"""
        group = QGroupBox("⚙️ Détails & Technique")
        layout = QFormLayout(group)
        layout.setSpacing(10)
        
        # À implémenter dans les classes dérivées
        return group
    
    def create_column_3(self):
        """Crée la colonne 3 - Localisation"""
        group = QGroupBox("📍 Localisation & Notes")
        layout = QFormLayout(group)
        layout.setSpacing(10)
        
        # À implémenter dans les classes dérivées
        return group
    
    def create_action_buttons(self, layout):
        """Crée les boutons d'action avec rapports multilingues"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(0,0,0,0.2);
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
            }
        """)
        buttons_frame.setFixedHeight(100)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # Boutons principaux
        validate_btn = QPushButton(self.ml.get_text("btn_validate"))
        validate_btn.clicked.connect(self.validate_data)
        buttons_layout.addWidget(validate_btn)
        
        modify_btn = QPushButton(self.ml.get_text("btn_modify"))
        modify_btn.clicked.connect(self.modify_data)
        buttons_layout.addWidget(modify_btn)
        
        # Boutons rapports multilingues
        report_fr_btn = QPushButton("📊 Rapport FR")
        report_fr_btn.clicked.connect(lambda: self.generate_report("fr"))
        buttons_layout.addWidget(report_fr_btn)
        
        report_ar_btn = QPushButton("📊 تقرير AR")
        report_ar_btn.clicked.connect(lambda: self.generate_report("ar"))
        buttons_layout.addWidget(report_ar_btn)
        
        report_en_btn = QPushButton("📊 Report EN")
        report_en_btn.clicked.connect(lambda: self.generate_report("en"))
        buttons_layout.addWidget(report_en_btn)
        
        # Bouton email
        email_btn = QPushButton(self.ml.get_text("btn_email"))
        email_btn.clicked.connect(self.send_email_report)
        buttons_layout.addWidget(email_btn)
        
        # Boutons navigation
        cancel_btn = QPushButton(self.ml.get_text("btn_cancel"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        back_btn = QPushButton(self.ml.get_text("btn_back"))
        back_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(back_btn)
        
        layout.addWidget(buttons_frame)
    
    def validate_data(self):
        """Valide et sauvegarde les données"""
        print(f"✅ Validation {self.module_name}")
        
    def modify_data(self):
        """Modifie les données"""
        print(f"✏️ Modification {self.module_name}")
        
    def generate_report(self, language):
        """Génère un rapport dans la langue spécifiée"""
        print(f"📊 Génération rapport {self.module_name} en {language}")
        
    def send_email_report(self):
        """Envoie le rapport par email"""
        print(f"📧 Envoi rapport {self.module_name} par email")

class ModuleFournisseurs(ModuleInterface3Colonnes):
    """Module Fournisseurs complet avec 20 critères détaillés"""

    def __init__(self, parent=None, ml_manager=None, color_manager=None):
        super().__init__(parent, ml_manager, color_manager,
                        ml_manager.get_text("module_fournisseurs"), "module_fournisseurs")

    def create_column_1(self):
        """Colonne 1 - Identification Fournisseur"""
        group = QGroupBox("🏢 Identification Fournisseur")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Code fournisseur
        self.code_fournisseur = QLineEdit()
        self.code_fournisseur.setPlaceholderText("FOUR001 (automatique)")
        layout.addRow(f"🔢 {self.ml.get_text('field_code')}:", self.code_fournisseur)

        # Raison sociale
        self.raison_sociale = QLineEdit()
        self.raison_sociale.setPlaceholderText("Nom officiel de l'entreprise")
        layout.addRow("🏭 Raison sociale:", self.raison_sociale)

        # Nom commercial
        self.nom_commercial = QLineEdit()
        self.nom_commercial.setPlaceholderText("Nom commercial")
        layout.addRow("🏪 Nom commercial:", self.nom_commercial)

        # Type de fournisseur
        self.type_fournisseur = QComboBox()
        self.type_fournisseur.addItems([
            "Local", "Régional", "National", "International", "Européen",
            "Sous-traitant", "Partenaire", "Distributeur", "Fabricant", "Autre"
        ])
        layout.addRow("📂 Type:", self.type_fournisseur)

        # Secteur d'activité
        self.secteur_activite = QComboBox()
        self.secteur_activite.addItems([
            "Informatique", "Mobilier", "Services", "Équipements", "Construction",
            "Automobile", "Alimentaire", "Textile", "Chimie", "Métallurgie",
            "Électronique", "Télécommunications", "Transport", "Énergie", "Autre"
        ])
        layout.addRow("🎯 Secteur activité:", self.secteur_activite)

        # Statut juridique
        self.statut_juridique = QComboBox()
        self.statut_juridique.addItems([
            "SARL", "SAS", "SA", "SNC", "EURL", "Auto-entrepreneur",
            "Association", "Coopérative", "GIE", "Autre"
        ])
        layout.addRow("⚖️ Statut juridique:", self.statut_juridique)

        # SIRET/SIREN
        self.siret = QLineEdit()
        self.siret.setPlaceholderText("Numéro SIRET")
        layout.addRow("📄 SIRET:", self.siret)

        return group

    def create_column_2(self):
        """Colonne 2 - Contact et Financier"""
        group = QGroupBox("📞 Contact & Financier")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Contact principal
        self.contact_principal = QLineEdit()
        self.contact_principal.setPlaceholderText("Nom du contact principal")
        layout.addRow("👤 Contact principal:", self.contact_principal)

        # Téléphone
        self.telephone = QLineEdit()
        self.telephone.setPlaceholderText("+33 1 23 45 67 89")
        layout.addRow("📞 Téléphone:", self.telephone)

        # Mobile
        self.mobile = QLineEdit()
        self.mobile.setPlaceholderText("+33 6 12 34 56 78")
        layout.addRow("📱 Mobile:", self.mobile)

        # Email
        self.email = QLineEdit()
        self.email.setPlaceholderText("<EMAIL>")
        layout.addRow("📧 Email:", self.email)

        # Site web
        self.site_web = QLineEdit()
        self.site_web.setPlaceholderText("www.fournisseur.com")
        layout.addRow("🌐 Site web:", self.site_web)

        # Conditions de paiement
        self.conditions_paiement = QComboBox()
        self.conditions_paiement.addItems([
            "Comptant", "30 jours", "45 jours", "60 jours", "90 jours",
            "Fin de mois", "30 jours fin de mois", "Virement", "Chèque", "Autre"
        ])
        layout.addRow("💳 Conditions paiement:", self.conditions_paiement)

        # Devise
        self.devise = QComboBox()
        self.devise.addItems(["EUR", "USD", "GBP", "CHF", "JPY", "CAD", "AUD", "DZD", "MAD", "TND"])
        layout.addRow("💰 Devise:", self.devise)

        # Remise habituelle
        self.remise_habituelle = QSpinBox()
        self.remise_habituelle.setRange(0, 100)
        self.remise_habituelle.setSuffix(" %")
        layout.addRow("💸 Remise habituelle:", self.remise_habituelle)

        return group

    def create_column_3(self):
        """Colonne 3 - Adresse et Évaluation"""
        group = QGroupBox("📍 Adresse & Évaluation")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Adresse
        self.adresse = QTextEdit()
        self.adresse.setPlaceholderText("Adresse complète du fournisseur")
        self.adresse.setMaximumHeight(80)
        layout.addRow("📍 Adresse:", self.adresse)

        # Code postal
        self.code_postal = QLineEdit()
        self.code_postal.setPlaceholderText("Code postal")
        layout.addRow("📮 Code postal:", self.code_postal)

        # Ville
        self.ville = QLineEdit()
        self.ville.setPlaceholderText("Ville")
        layout.addRow("🏙️ Ville:", self.ville)

        # Pays
        self.pays = QComboBox()
        self.pays.addItems([
            "France", "Algérie", "Maroc", "Tunisie", "Belgique", "Suisse",
            "Allemagne", "Italie", "Espagne", "Royaume-Uni", "États-Unis", "Autre"
        ])
        layout.addRow("🏳️ Pays:", self.pays)

        # Note qualité
        self.note_qualite = QComboBox()
        self.note_qualite.addItems([
            "Excellent", "Très bon", "Bon", "Correct", "Moyen", "Faible", "Non évalué"
        ])
        layout.addRow("⭐ Note qualité:", self.note_qualite)

        # Délai livraison
        self.delai_livraison = QComboBox()
        self.delai_livraison.addItems([
            "24h", "48h", "3 jours", "1 semaine", "2 semaines", "1 mois", "Variable", "Non défini"
        ])
        layout.addRow("🚚 Délai livraison:", self.delai_livraison)

        # Certification
        self.certification = QComboBox()
        self.certification.addItems([
            "ISO 9001", "ISO 14001", "ISO 45001", "CE", "NF", "Aucune", "Autre"
        ])
        layout.addRow("🏆 Certification:", self.certification)

        # Observation
        self.observation = QTextEdit()
        self.observation.setPlaceholderText(self.ml.get_text('field_observation'))
        self.observation.setMaximumHeight(80)
        layout.addRow(f"📋 {self.ml.get_text('field_observation')}:", self.observation)

        return group

class ModuleInventaire(ModuleInterface3Colonnes):
    """Module Inventaire complet avec 18 critères détaillés"""

    def __init__(self, parent=None, ml_manager=None, color_manager=None):
        super().__init__(parent, ml_manager, color_manager,
                        ml_manager.get_text("module_inventaire"), "module_inventaire")

    def create_column_1(self):
        """Colonne 1 - Identification Article"""
        group = QGroupBox("📦 Identification Article")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Code article
        self.code_article = QLineEdit()
        self.code_article.setPlaceholderText("INV001 (avec code-barre)")
        layout.addRow(f"🔢 {self.ml.get_text('field_code')}:", self.code_article)

        # Code-barre
        self.code_barre = QLineEdit()
        self.code_barre.setPlaceholderText("Code-barre EAN13/UPC")
        layout.addRow("📊 Code-barre:", self.code_barre)

        # Désignation
        self.designation = QLineEdit()
        self.designation.setPlaceholderText("Description de l'article")
        layout.addRow(f"📝 {self.ml.get_text('field_designation')}:", self.designation)

        # Catégorie
        self.categorie = QComboBox()
        self.categorie.addItems([
            "Matières premières", "Produits finis", "Fournitures", "Consommables",
            "Pièces détachées", "Outillage", "Équipements", "Emballages", "Autre"
        ])
        layout.addRow(f"📂 {self.ml.get_text('field_category')}:", self.categorie)

        # Sous-catégorie
        self.sous_categorie = QComboBox()
        self.sous_categorie.addItems([
            "Bureau", "Informatique", "Maintenance", "Production", "Sécurité",
            "Nettoyage", "Alimentaire", "Médical", "Électronique", "Autre"
        ])
        layout.addRow("📋 Sous-catégorie:", self.sous_categorie)

        # Unité de mesure
        self.unite_mesure = QComboBox()
        self.unite_mesure.addItems([
            "Pièce", "Kg", "Litre", "Mètre", "M²", "M³", "Boîte", "Carton", "Palette", "Autre"
        ])
        layout.addRow("📏 Unité:", self.unite_mesure)

        return group

    def create_column_2(self):
        """Colonne 2 - Stock et Prix"""
        group = QGroupBox("📊 Stock & Prix")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Quantité en stock
        self.quantite_stock = QSpinBox()
        self.quantite_stock.setRange(0, 999999)
        layout.addRow("📦 Quantité stock:", self.quantite_stock)

        # Stock minimum
        self.stock_minimum = QSpinBox()
        self.stock_minimum.setRange(0, 999999)
        layout.addRow("⚠️ Stock minimum:", self.stock_minimum)

        # Stock maximum
        self.stock_maximum = QSpinBox()
        self.stock_maximum.setRange(0, 999999)
        layout.addRow("📈 Stock maximum:", self.stock_maximum)

        # Seuil d'alerte
        self.seuil_alerte = QSpinBox()
        self.seuil_alerte.setRange(0, 999999)
        layout.addRow("🚨 Seuil alerte:", self.seuil_alerte)

        # Prix d'achat
        self.prix_achat = QSpinBox()
        self.prix_achat.setRange(0, 999999)
        self.prix_achat.setSuffix(" €")
        layout.addRow("💰 Prix achat:", self.prix_achat)

        # Prix de vente
        self.prix_vente = QSpinBox()
        self.prix_vente.setRange(0, 999999)
        self.prix_vente.setSuffix(" €")
        layout.addRow("💵 Prix vente:", self.prix_vente)

        # TVA
        self.tva = QComboBox()
        self.tva.addItems(["0%", "5.5%", "10%", "20%", "Autre"])
        layout.addRow("📊 TVA:", self.tva)

        # Marge
        self.marge = QSpinBox()
        self.marge.setRange(0, 1000)
        self.marge.setSuffix(" %")
        layout.addRow("📈 Marge:", self.marge)

        return group

    def create_column_3(self):
        """Colonne 3 - Localisation et Fournisseur"""
        group = QGroupBox("📍 Localisation & Fournisseur")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Emplacement
        self.emplacement = QComboBox()
        self.emplacement.addItems([
            "A1", "A2", "A3", "B1", "B2", "B3", "C1", "C2", "C3",
            "Entrepôt", "Réserve", "Magasin", "Autre"
        ])
        layout.addRow("📍 Emplacement:", self.emplacement)

        # Zone de stockage
        self.zone_stockage = QComboBox()
        self.zone_stockage.addItems([
            "Zone froide", "Zone tempérée", "Zone chaude", "Zone sèche",
            "Zone humide", "Zone sécurisée", "Zone normale", "Autre"
        ])
        layout.addRow("🌡️ Zone stockage:", self.zone_stockage)

        # Fournisseur principal
        self.fournisseur_principal = QLineEdit()
        self.fournisseur_principal.setPlaceholderText("Nom du fournisseur")
        layout.addRow("🏢 Fournisseur:", self.fournisseur_principal)

        # Référence fournisseur
        self.reference_fournisseur = QLineEdit()
        self.reference_fournisseur.setPlaceholderText("Référence chez le fournisseur")
        layout.addRow("📄 Réf. fournisseur:", self.reference_fournisseur)

        # Date dernière entrée
        self.date_derniere_entree = QDateEdit()
        self.date_derniere_entree.setCalendarPopup(True)
        layout.addRow("📅 Dernière entrée:", self.date_derniere_entree)

        # Date dernière sortie
        self.date_derniere_sortie = QDateEdit()
        self.date_derniere_sortie.setCalendarPopup(True)
        layout.addRow("📅 Dernière sortie:", self.date_derniere_sortie)

        # Statut
        self.statut = QComboBox()
        self.statut.addItems([
            "Actif", "Inactif", "Obsolète", "En commande", "Rupture", "Arrêté"
        ])
        layout.addRow(f"📊 {self.ml.get_text('field_status')}:", self.statut)

        # Observation
        self.observation = QTextEdit()
        self.observation.setPlaceholderText(self.ml.get_text('field_observation'))
        self.observation.setMaximumHeight(80)
        layout.addRow(f"📋 {self.ml.get_text('field_observation')}:", self.observation)

        return group

class ModuleSuiviAnimaux(ModuleInterface3Colonnes):
    """Module Suivi des Animaux complet avec 15 critères détaillés"""

    def __init__(self, parent=None, ml_manager=None, color_manager=None):
        super().__init__(parent, ml_manager, color_manager,
                        ml_manager.get_text("module_animaux"), "module_animaux")

    def create_column_1(self):
        """Colonne 1 - Identification Animal"""
        group = QGroupBox("🐄 Identification Animal")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Numéro d'identification
        self.numero_identification = QLineEdit()
        self.numero_identification.setPlaceholderText("ANI001 (automatique)")
        layout.addRow("🔢 N° Identification:", self.numero_identification)

        # Nom/Surnom
        self.nom_animal = QLineEdit()
        self.nom_animal.setPlaceholderText("Nom ou surnom de l'animal")
        layout.addRow("📝 Nom:", self.nom_animal)

        # Espèce
        self.espece = QComboBox()
        self.espece.addItems([
            "Bovin", "Ovin", "Caprin", "Porcin", "Équin", "Volaille",
            "Lapin", "Chien", "Chat", "Autre"
        ])
        layout.addRow("🐄 Espèce:", self.espece)

        # Race
        self.race = QLineEdit()
        self.race.setPlaceholderText("Race de l'animal")
        layout.addRow("🧬 Race:", self.race)

        # Sexe
        self.sexe = QComboBox()
        self.sexe.addItems(["Mâle", "Femelle", "Castré", "Stérilisé"])
        layout.addRow("⚥ Sexe:", self.sexe)

        # Date de naissance
        self.date_naissance = QDateEdit()
        self.date_naissance.setCalendarPopup(True)
        layout.addRow("📅 Date naissance:", self.date_naissance)

        return group

    def create_column_2(self):
        """Colonne 2 - Santé et Suivi"""
        group = QGroupBox("🏥 Santé & Suivi")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Poids
        self.poids = QSpinBox()
        self.poids.setRange(0, 9999)
        self.poids.setSuffix(" kg")
        layout.addRow("⚖️ Poids:", self.poids)

        # État de santé
        self.etat_sante = QComboBox()
        self.etat_sante.addItems([
            "Excellent", "Bon", "Correct", "Faible", "Malade", "En traitement"
        ])
        layout.addRow("🏥 État santé:", self.etat_sante)

        # Vaccinations
        self.vaccinations = QTextEdit()
        self.vaccinations.setPlaceholderText("Liste des vaccinations")
        self.vaccinations.setMaximumHeight(60)
        layout.addRow("💉 Vaccinations:", self.vaccinations)

        # Dernière visite vétérinaire
        self.derniere_visite_veto = QDateEdit()
        self.derniere_visite_veto.setCalendarPopup(True)
        layout.addRow("👨‍⚕️ Dernière visite véto:", self.derniere_visite_veto)

        # Prochaine visite
        self.prochaine_visite = QDateEdit()
        self.prochaine_visite.setCalendarPopup(True)
        layout.addRow("📅 Prochaine visite:", self.prochaine_visite)

        # Reproduction
        self.statut_reproduction = QComboBox()
        self.statut_reproduction.addItems([
            "Non reproducteur", "Reproducteur", "Gestante", "Allaitante", "Retraité"
        ])
        layout.addRow("👶 Reproduction:", self.statut_reproduction)

        return group

    def create_column_3(self):
        """Colonne 3 - Localisation et Gestion"""
        group = QGroupBox("📍 Localisation & Gestion")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Localisation
        self.localisation = QComboBox()
        self.localisation.addItems([
            "Étable A", "Étable B", "Pâturage 1", "Pâturage 2", "Bergerie",
            "Porcherie", "Poulailler", "Infirmerie", "Autre"
        ])
        layout.addRow("📍 Localisation:", self.localisation)

        # Propriétaire/Responsable
        self.responsable = QLineEdit()
        self.responsable.setPlaceholderText("Nom du responsable")
        layout.addRow("👤 Responsable:", self.responsable)

        # Valeur estimée
        self.valeur_estimee = QSpinBox()
        self.valeur_estimee.setRange(0, 999999)
        self.valeur_estimee.setSuffix(" €")
        layout.addRow("💰 Valeur estimée:", self.valeur_estimee)

        # Assurance
        self.assurance = QComboBox()
        self.assurance.addItems(["Oui", "Non", "En cours", "Expirée"])
        layout.addRow("🛡️ Assurance:", self.assurance)

        # Statut
        self.statut = QComboBox()
        self.statut.addItems([
            "Présent", "Vendu", "Décédé", "Perdu", "En quarantaine", "Transféré"
        ])
        layout.addRow("📊 Statut:", self.statut)

        # Observation
        self.observation = QTextEdit()
        self.observation.setPlaceholderText("Observations sur l'animal")
        self.observation.setMaximumHeight(80)
        layout.addRow("📋 Observation:", self.observation)

        return group

class ModuleSuiviTravaux(ModuleInterface3Colonnes):
    """Module Suivi des Travaux complet avec 22 critères détaillés"""

    def __init__(self, parent=None, ml_manager=None, color_manager=None):
        super().__init__(parent, ml_manager, color_manager,
                        ml_manager.get_text("module_travaux"), "module_travaux")

    def create_column_1(self):
        """Colonne 1 - Identification Travaux"""
        group = QGroupBox("🔨 Identification Travaux")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Code travaux
        self.code_travaux = QLineEdit()
        self.code_travaux.setPlaceholderText("TRV001 (automatique)")
        layout.addRow("🔢 Code travaux:", self.code_travaux)

        # Désignation
        self.designation = QLineEdit()
        self.designation.setPlaceholderText("Description des travaux")
        layout.addRow("📝 Désignation:", self.designation)

        # Type de travaux
        self.type_travaux = QComboBox()
        self.type_travaux.addItems([
            "Maintenance", "Réparation", "Installation", "Rénovation",
            "Construction", "Démolition", "Nettoyage", "Peinture", "Autre"
        ])
        layout.addRow("🔧 Type travaux:", self.type_travaux)

        # Priorité
        self.priorite = QComboBox()
        self.priorite.addItems(["Très urgente", "Urgente", "Normale", "Faible", "Différée"])
        layout.addRow("⚡ Priorité:", self.priorite)

        # Statut
        self.statut = QComboBox()
        self.statut.addItems([
            "Planifié", "En cours", "Suspendu", "Terminé", "Annulé", "En attente"
        ])
        layout.addRow("📊 Statut:", self.statut)

        # Date demande
        self.date_demande = QDateEdit()
        self.date_demande.setCalendarPopup(True)
        layout.addRow("📅 Date demande:", self.date_demande)

        # Date prévue début
        self.date_prevue_debut = QDateEdit()
        self.date_prevue_debut.setCalendarPopup(True)
        layout.addRow("📅 Début prévu:", self.date_prevue_debut)

        # Date prévue fin
        self.date_prevue_fin = QDateEdit()
        self.date_prevue_fin.setCalendarPopup(True)
        layout.addRow("📅 Fin prévue:", self.date_prevue_fin)

        return group

    def create_column_2(self):
        """Colonne 2 - Exécution et Coûts"""
        group = QGroupBox("💰 Exécution & Coûts")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Date début réelle
        self.date_debut_reelle = QDateEdit()
        self.date_debut_reelle.setCalendarPopup(True)
        layout.addRow("📅 Début réel:", self.date_debut_reelle)

        # Date fin réelle
        self.date_fin_reelle = QDateEdit()
        self.date_fin_reelle.setCalendarPopup(True)
        layout.addRow("📅 Fin réelle:", self.date_fin_reelle)

        # Durée estimée
        self.duree_estimee = QSpinBox()
        self.duree_estimee.setRange(0, 9999)
        self.duree_estimee.setSuffix(" heures")
        layout.addRow("⏱️ Durée estimée:", self.duree_estimee)

        # Durée réelle
        self.duree_reelle = QSpinBox()
        self.duree_reelle.setRange(0, 9999)
        self.duree_reelle.setSuffix(" heures")
        layout.addRow("⏱️ Durée réelle:", self.duree_reelle)

        # Coût estimé
        self.cout_estime = QSpinBox()
        self.cout_estime.setRange(0, 999999)
        self.cout_estime.setSuffix(" €")
        layout.addRow("💰 Coût estimé:", self.cout_estime)

        # Coût réel
        self.cout_reel = QSpinBox()
        self.cout_reel.setRange(0, 999999)
        self.cout_reel.setSuffix(" €")
        layout.addRow("💰 Coût réel:", self.cout_reel)

        # Main d'œuvre
        self.cout_main_oeuvre = QSpinBox()
        self.cout_main_oeuvre.setRange(0, 999999)
        self.cout_main_oeuvre.setSuffix(" €")
        layout.addRow("👷 Main d'œuvre:", self.cout_main_oeuvre)

        # Matériaux
        self.cout_materiaux = QSpinBox()
        self.cout_materiaux.setRange(0, 999999)
        self.cout_materiaux.setSuffix(" €")
        layout.addRow("🧱 Matériaux:", self.cout_materiaux)

        return group

    def create_column_3(self):
        """Colonne 3 - Responsables et Localisation"""
        group = QGroupBox("👥 Responsables & Localisation")
        layout = QFormLayout(group)
        layout.setSpacing(10)

        # Demandeur
        self.demandeur = QLineEdit()
        self.demandeur.setPlaceholderText("Nom du demandeur")
        layout.addRow("👤 Demandeur:", self.demandeur)

        # Responsable travaux
        self.responsable_travaux = QLineEdit()
        self.responsable_travaux.setPlaceholderText("Responsable des travaux")
        layout.addRow("👷 Responsable:", self.responsable_travaux)

        # Entreprise/Équipe
        self.entreprise = QLineEdit()
        self.entreprise.setPlaceholderText("Entreprise ou équipe")
        layout.addRow("🏢 Entreprise:", self.entreprise)

        # Localisation
        self.localisation = QComboBox()
        self.localisation.addItems([
            "Bâtiment A", "Bâtiment B", "Bâtiment C", "Extérieur",
            "Parking", "Jardin", "Toiture", "Sous-sol", "Autre"
        ])
        layout.addRow("📍 Localisation:", self.localisation)

        # Zone précise
        self.zone_precise = QLineEdit()
        self.zone_precise.setPlaceholderText("Zone précise des travaux")
        layout.addRow("🎯 Zone précise:", self.zone_precise)

        # Garantie
        self.garantie = QComboBox()
        self.garantie.addItems(["1 an", "2 ans", "5 ans", "10 ans", "Aucune", "Autre"])
        layout.addRow("🛡️ Garantie:", self.garantie)

        # Observation
        self.observation = QTextEdit()
        self.observation.setPlaceholderText("Observations sur les travaux")
        self.observation.setMaximumHeight(80)
        layout.addRow("📋 Observation:", self.observation)

        return group

class MasterComptaGeneralInterface(QMainWindow):
    """Interface principale MASTER COMPTA GÉNÉRAL avec TOUS les modules"""

    def __init__(self):
        super().__init__()

        # Gestionnaires
        self.ml_manager = MultilingueManager()
        self.color_manager = ColorManager()

        # Configuration
        self.current_user = None
        self.currency_symbol = "€"
        self.code_counter = 1

        # TOUS les 16 modules avec classes complètes
        self.modules_complets = {
            'module_gestion_biens': {
                'couleur': '#1E3A8A', 'icone': '🏠',
                'classe': None,  # ModuleGestionBiens (à implémenter)
                'criteres': 33
            },
            'module_fournisseurs': {
                'couleur': '#059669', 'icone': '🏢',
                'classe': ModuleFournisseurs,
                'criteres': 20
            },
            'module_inventaire': {
                'couleur': '#DC2626', 'icone': '📦',
                'classe': ModuleInventaire,
                'criteres': 18
            },
            'module_parc_auto': {
                'couleur': '#7C2D12', 'icone': '🚗',
                'classe': None,  # ParcAutoComplet (déjà implémenté)
                'criteres': 50
            },
            'module_animaux': {
                'couleur': '#7C3AED', 'icone': '🐄',
                'classe': ModuleSuiviAnimaux,
                'criteres': 15
            },
            'module_travaux': {
                'couleur': '#374151', 'icone': '🔨',
                'classe': ModuleSuiviTravaux,
                'criteres': 22
            },
            'module_outils': {
                'couleur': '#92400E', 'icone': '🔧',
                'classe': None,  # À implémenter
                'criteres': 16
            },
            'module_calculatrice': {
                'couleur': '#065F46', 'icone': '🧮',
                'classe': None,  # CalculatriceAvancee (déjà implémenté)
                'criteres': 0
            },
            'module_documents': {
                'couleur': '#1E40AF', 'icone': '📄',
                'classe': None,  # À implémenter
                'criteres': 12
            },
            'module_impression': {
                'couleur': '#166534', 'icone': '🖨️',
                'classe': None,  # À implémenter
                'criteres': 8
            },
            'module_archive': {
                'couleur': '#581C87', 'icone': '📚',
                'classe': None,  # À implémenter
                'criteres': 14
            },
            'module_outillage': {
                'couleur': '#DC2626', 'icone': '⚒️',
                'classe': None,  # À implémenter
                'criteres': 18
            },
            'module_moyens_generaux': {
                'couleur': '#1E40AF', 'icone': '🏭',
                'classe': None,  # À implémenter
                'criteres': 20
            },
            'module_hotellerie': {
                'couleur': '#BE185D', 'icone': '🏨',
                'classe': None,  # À implémenter
                'criteres': 24
            },
            'module_agencement': {
                'couleur': '#0891B2', 'icone': '🪑',
                'classe': None,  # À implémenter
                'criteres': 16
            },
            'module_batis': {
                'couleur': '#7C2D12', 'icone': '🏗️',
                'classe': None,  # À implémenter
                'criteres': 28
            }
        }

        self.setup_master_compta_interface()

    def setup_master_compta_interface(self):
        """Configure l'interface MASTER COMPTA GÉNÉRAL"""

        # Titre de la fenêtre
        self.setWindowTitle(self.ml_manager.get_text("app_title"))
        self.setGeometry(100, 100, 1600, 1000)

        # Style MASTER COMPTA GÉNÉRAL
        self.apply_master_compta_style()

        # Menu multilingue
        self.create_master_compta_menu()

        # Barre d'outils
        self.create_master_compta_toolbar()

        # Zone centrale avec date/heure et modules
        self.create_master_compta_central_area()

        # Barre de statut
        self.create_master_compta_status_bar()

        # Timer pour date/heure
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)

        # Afficher la fenêtre
        self.show()

    def apply_master_compta_style(self):
        """Applique le style MASTER COMPTA GÉNÉRAL"""
        colors = self.color_manager.get_colors()

        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['primary']}, stop:1 {colors['secondary']});
                border: 3px solid {colors['accent']};
            }}
            QMenuBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['button']}, stop:1 {colors['secondary']});
                color: white;
                font-size: 13pt;
                font-weight: bold;
                border-bottom: 2px solid {colors['accent']};
                padding: 5px;
            }}
            QMenuBar::item {{
                background: transparent;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 8px;
            }}
            QMenuBar::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['accent']}, stop:1 {colors['primary']});
            }}
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['secondary']}, stop:1 {colors['accent']});
                color: white;
                font-weight: bold;
                font-size: 12pt;
                border-top: 2px solid {colors['accent']};
                padding: 8px;
            }}
            QToolBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['button']}, stop:1 {colors['secondary']});
                border: 2px solid {colors['accent']};
                spacing: 8px;
                padding: 8px;
            }}
            QToolButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['background']}, stop:1 {colors['button']});
                color: {colors['text']};
                border: 2px solid {colors['accent']};
                border-radius: 10px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                margin: 3px;
                min-width: 100px;
                min-height: 45px;
            }}
            QToolButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {colors['accent']}, stop:1 {colors['primary']});
                color: white;
                transform: scale(1.05);
            }}
        """)

    def create_master_compta_menu(self):
        """Crée le menu MASTER COMPTA GÉNÉRAL"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        new_action = file_menu.addAction("🆕 Nouveau")
        new_action.triggered.connect(self.nouveau_fichier)

        open_action = file_menu.addAction("📂 Ouvrir")
        open_action.triggered.connect(self.ouvrir_fichier)

        save_action = file_menu.addAction("💾 Sauvegarder")
        save_action.triggered.connect(self.sauvegarder_fichier)

        file_menu.addSeparator()

        exit_action = file_menu.addAction("🚪 Quitter")
        exit_action.triggered.connect(self.close)

        # Menu Modules
        modules_menu = menubar.addMenu("📋 Modules")

        for module_key, config in self.modules_complets.items():
            module_name = self.ml_manager.get_text(module_key)
            action = modules_menu.addAction(f"{config['icone']} {module_name} ({config['criteres']} critères)")
            action.triggered.connect(lambda checked, m=module_key: self.ouvrir_module(m))

        # Menu Langues
        lang_menu = menubar.addMenu("🌍 Langues")

        fr_action = lang_menu.addAction("🇫🇷 Français")
        fr_action.triggered.connect(lambda: self.changer_langue("fr"))

        ar_action = lang_menu.addAction("🇩🇿 العربية")
        ar_action.triggered.connect(lambda: self.changer_langue("ar"))

        en_action = lang_menu.addAction("🇺🇸 English")
        en_action.triggered.connect(lambda: self.changer_langue("en"))

        # Menu Couleurs
        colors_menu = menubar.addMenu("🎨 Couleurs")

        master_action = colors_menu.addAction("🏢 Master Compta")
        master_action.triggered.connect(lambda: self.changer_couleurs("master_compta"))

        turquoise_action = colors_menu.addAction("🌊 Bleu Turquoise")
        turquoise_action.triggered.connect(lambda: self.changer_couleurs("bleu_turquoise"))

        sage_action = colors_menu.addAction("🌿 Sage Classique")
        sage_action.triggered.connect(lambda: self.changer_couleurs("sage_classique"))

        odoo_action = colors_menu.addAction("🟣 Odoo Style")
        odoo_action.triggered.connect(lambda: self.changer_couleurs("odoo_style"))

        pro_action = colors_menu.addAction("💼 Professionnel")
        pro_action.triggered.connect(lambda: self.changer_couleurs("professionnel"))

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = help_menu.addAction("ℹ️ À propos")
        about_action.triggered.connect(self.afficher_apropos)

    def create_master_compta_toolbar(self):
        """Crée la barre d'outils MASTER COMPTA GÉNÉRAL"""
        toolbar = self.addToolBar("Outils MASTER COMPTA GÉNÉRAL")

        # Boutons de la barre d'outils
        toolbar_actions = [
            ("🆕 Nouveau", self.nouveau_fichier),
            ("📂 Ouvrir", self.ouvrir_fichier),
            ("💾 Sauvegarder", self.sauvegarder_fichier),
            ("🖨️ Imprimer", self.imprimer),
            ("🔍 Rechercher", self.rechercher),
            ("🧮 Calculatrice", self.ouvrir_calculatrice),
            ("⚙️ Paramètres", self.ouvrir_parametres),
            ("📊 Rapports", self.generer_rapports)
        ]

        for text, func in toolbar_actions:
            action = toolbar.addAction(text)
            action.triggered.connect(func)

    def create_master_compta_central_area(self):
        """Crée la zone centrale avec date/heure et modules"""
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8FAFC, stop:1 #E2E8F0);
                border: 2px solid #1E3A8A;
                border-radius: 15px;
                margin: 8px;
            }
        """)
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # En-tête avec titre et date/heure
        self.create_header_with_datetime(layout)

        # Zone des modules en grille
        self.create_modules_grid(layout)

        # Zone d'informations
        self.create_info_area(layout)

    def create_header_with_datetime(self, layout):
        """Crée l'en-tête avec date/heure/calendrier"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E3A8A, stop:1 #1E40AF);
                border: 2px solid #3B82F6;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        header_frame.setFixedHeight(150)

        header_layout = QHBoxLayout(header_frame)

        # Titre MASTER COMPTA GÉNÉRAL
        title_label = QLabel("🏢 MASTER COMPTA GÉNÉRAL")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Zone date/heure/calendrier
        datetime_frame = QFrame()
        datetime_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        datetime_layout = QVBoxLayout(datetime_frame)

        # Date actuelle
        self.current_date = QLabel()
        self.current_date.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_date.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        datetime_layout.addWidget(self.current_date)

        # Heure actuelle
        self.current_time = QLabel()
        self.current_time.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_time.setStyleSheet("""
            QLabel {
                color: #00FFFF;
                font-size: 20pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        datetime_layout.addWidget(self.current_time)

        # Bouton calendrier
        calendar_btn = QPushButton("📅 Calendrier")
        calendar_btn.setFixedSize(140, 40)
        calendar_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1E40AF);
                color: white;
                border: 2px solid #60A5FA;
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #60A5FA, stop:1 #3B82F6);
            }
        """)
        calendar_btn.clicked.connect(self.show_calendar)
        datetime_layout.addWidget(calendar_btn)

        header_layout.addWidget(datetime_frame)

        layout.addWidget(header_frame)

    def create_modules_grid(self, layout):
        """Crée la grille des modules"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFFFF, stop:1 #F1F5F9);
                border: 2px solid #CBD5E1;
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
            }
        """)

        modules_layout = QGridLayout(modules_frame)
        modules_layout.setSpacing(15)

        # Organiser les modules en grille 4x4
        row, col = 0, 0
        max_cols = 4

        for module_key, config in self.modules_complets.items():
            module_btn = self.create_master_compta_module_button(module_key, config)
            modules_layout.addWidget(module_btn, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        layout.addWidget(modules_frame)

    def create_master_compta_module_button(self, module_key, config):
        """Crée un bouton de module MASTER COMPTA GÉNÉRAL"""
        module_name = self.ml_manager.get_text(module_key)

        btn = QPushButton(f"{config['icone']}\n{module_name}\n({config['criteres']} critères)")

        # Style MASTER COMPTA GÉNÉRAL
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {config['couleur']}, stop:1 {self.darken_color(config['couleur'])});
                color: white;
                border: 3px solid #1E3A8A;
                border-radius: 15px;
                font-size: 11pt;
                font-weight: bold;
                padding: 20px;
                min-width: 200px;
                min-height: 140px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1E40AF);
                border: 3px solid #60A5FA;
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E40AF, stop:1 #1E3A8A);
                border: 3px solid #1D4ED8;
            }}
        """)

        # Tooltip informatif
        btn.setToolTip(f"🎯 Module: {module_name}\n📋 {config['criteres']} critères détaillés\n💡 Interface 3 colonnes\n🌍 Rapports multilingues\n📧 Envoi par email")

        # Connecter à l'ouverture du module
        btn.clicked.connect(lambda: self.ouvrir_module(module_key))

        return btn

    def create_info_area(self, layout):
        """Crée la zone d'informations"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F1F5F9, stop:1 #E2E8F0);
                border: 2px solid #CBD5E1;
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
            }
        """)
        info_frame.setFixedHeight(120)

        layout_info = QHBoxLayout(info_frame)

        # Informations système
        system_info = QLabel("""
        📊 <b>MASTER COMPTA GÉNÉRAL</b><br>
        🗃️ Base de données: Connectée<br>
        👤 Utilisateur: Administrateur<br>
        💰 Devise: Euro (€)<br>
        🔢 Codification: INV001
        """)
        system_info.setStyleSheet("""
            QLabel {
                background-color: rgba(30, 58, 138, 0.1);
                border: 2px solid #1E3A8A;
                border-radius: 10px;
                padding: 15px;
                font-size: 12pt;
                color: #1E3A8A;
            }
        """)
        layout_info.addWidget(system_info)

        # Statistiques
        stats_info = QLabel("""
        📈 <b>Statistiques</b><br>
        🏠 Biens gérés: 0<br>
        🚗 Véhicules: 0<br>
        📦 Articles: 0<br>
        🏢 Fournisseurs: 0
        """)
        stats_info.setStyleSheet("""
            QLabel {
                background-color: rgba(5, 150, 105, 0.1);
                border: 2px solid #059669;
                border-radius: 10px;
                padding: 15px;
                font-size: 12pt;
                color: #059669;
            }
        """)
        layout_info.addWidget(stats_info)

        # Actions rapides
        quick_actions = QLabel("""
        ⚡ <b>Actions Rapides</b><br>
        🆕 Nouveau bien<br>
        🔍 Recherche avancée<br>
        📊 Rapports multilingues<br>
        💾 Sauvegarde auto
        """)
        quick_actions.setStyleSheet("""
            QLabel {
                background-color: rgba(220, 38, 38, 0.1);
                border: 2px solid #DC2626;
                border-radius: 10px;
                padding: 15px;
                font-size: 12pt;
                color: #DC2626;
            }
        """)
        layout_info.addWidget(quick_actions)

        layout.addWidget(info_frame)

    def create_master_compta_status_bar(self):
        """Crée la barre de statut MASTER COMPTA GÉNÉRAL"""
        status_bar = self.statusBar()
        status_bar.showMessage("🎯 MASTER COMPTA GÉNÉRAL prêt - Interface multilingue | 💰 Devise: € | 🔢 Codification: INV001 | 📋 16 modules avec critères détaillés")

    def update_datetime(self):
        """Met à jour la date et l'heure"""
        now = datetime.now()

        # Date en français
        date_str = f"{self.ml_manager.get_text('current_date')}: {now.strftime('%d/%m/%Y')}"
        self.current_date.setText(date_str)

        # Heure
        time_str = f"{self.ml_manager.get_text('current_time')}: {now.strftime('%H:%M:%S')}"
        self.current_time.setText(time_str)

    def show_calendar(self):
        """Affiche le calendrier"""
        calendar_dialog = QDialog(self)
        calendar_dialog.setWindowTitle(self.ml_manager.get_text("calendar"))
        calendar_dialog.setFixedSize(450, 400)

        layout = QVBoxLayout(calendar_dialog)

        calendar = QCalendarWidget()
        calendar.setStyleSheet("""
            QCalendarWidget {
                background-color: white;
                border: 2px solid #1E3A8A;
                border-radius: 10px;
                font-size: 12pt;
            }
            QCalendarWidget QToolButton {
                background-color: #1E3A8A;
                color: white;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QCalendarWidget QMenu {
                background-color: white;
                border: 1px solid #1E3A8A;
            }
            QCalendarWidget QSpinBox {
                background-color: white;
                border: 1px solid #1E3A8A;
                border-radius: 5px;
                padding: 5px;
            }
            QCalendarWidget QAbstractItemView:enabled {
                background-color: white;
                color: #1E3A8A;
                selection-background-color: #3B82F6;
                selection-color: white;
            }
        """)
        layout.addWidget(calendar)

        close_btn = QPushButton("✅ Fermer")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E3A8A, stop:1 #1E40AF);
                color: white;
                border: 2px solid #3B82F6;
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1E3A8A);
            }
        """)
        close_btn.clicked.connect(calendar_dialog.accept)
        layout.addWidget(close_btn)

        calendar_dialog.exec()

    def darken_color(self, hex_color, factor=0.3):
        """Assombrit une couleur hexadécimale"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    # Méthodes d'action
    def ouvrir_module(self, module_key):
        """Ouvre un module spécifique"""
        config = self.modules_complets.get(module_key)
        if config and config['classe']:
            try:
                module_dialog = config['classe'](self, self.ml_manager, self.color_manager)
                module_dialog.exec()
            except Exception as e:
                print(f"Erreur ouverture module {module_key}: {e}")
        else:
            print(f"📋 Module {module_key} - Interface 3 colonnes à implémenter")
            print(f"🎯 {config['criteres']} critères détaillés prévus")

    def changer_langue(self, language):
        """Change la langue de l'interface"""
        self.ml_manager.set_language(language)
        print(f"🌍 Langue changée: {language}")
        # Redémarrer l'interface pour appliquer les changements
        self.close()
        self.__init__()

    def changer_couleurs(self, scheme):
        """Change le schéma de couleurs"""
        self.color_manager.set_scheme(scheme)
        self.apply_master_compta_style()
        print(f"🎨 Couleurs changées: {scheme}")

    def nouveau_fichier(self):
        """Crée un nouveau fichier"""
        print("🆕 Nouveau fichier")

    def ouvrir_fichier(self):
        """Ouvre un fichier"""
        print("📂 Ouvrir fichier")

    def sauvegarder_fichier(self):
        """Sauvegarde le fichier"""
        print("💾 Sauvegarder fichier")

    def imprimer(self):
        """Lance l'impression"""
        print("🖨️ Impression")

    def rechercher(self):
        """Lance la recherche"""
        print("🔍 Recherche")

    def ouvrir_calculatrice(self):
        """Ouvre la calculatrice"""
        print("🧮 Calculatrice scientifique")

    def ouvrir_parametres(self):
        """Ouvre les paramètres"""
        print("⚙️ Paramètres")

    def generer_rapports(self):
        """Génère les rapports"""
        print("📊 Génération rapports multilingues")

    def afficher_apropos(self):
        """Affiche les informations À propos"""
        print("ℹ️ À propos de MASTER COMPTA GÉNÉRAL")

# Script de lancement
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Master Compta Solutions")

    # Lancement de l'interface
    window = MasterComptaGeneralInterface()

    # Exécution de l'application
    sys.exit(app.exec())
