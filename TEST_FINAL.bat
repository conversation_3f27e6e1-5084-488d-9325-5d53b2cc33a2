@echo off
title TEST FINAL - Verification Complete des Fichiers Python
color 0A

echo.
echo ========================================================================
echo                    TEST FINAL - VERIFICATION COMPLETE
echo                   Identification precise des problemes Python
echo ========================================================================
echo.

REM Variables
set "PYTHON_CMD="
set "TEST_SUCCESS=0"

echo [%TIME%] Demarrage du test final...

REM Affichage du répertoire courant
echo Repertoire courant: %CD%
echo.

REM Détection Python
echo Detection de Python...
py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    echo ✅ Python detecte: py
    goto :python_found
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    echo ✅ Python detecte: python
    goto :python_found
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    echo ✅ Python detecte: python3
    goto :python_found
)

echo ❌ Python non trouve
goto :manual_check

:python_found
echo Python executable: %PYTHON_CMD%
echo.

REM Vérification de la structure
echo Verification de la structure du projet...
if exist "src\main.py" (
    echo ✅ src\main.py trouve
) else (
    echo ❌ src\main.py non trouve
    if exist "..\src\main.py" (
        echo Tentative de remontee au repertoire parent...
        cd ..
        echo Nouveau repertoire: %CD%
    )
)

REM Test direct avec Python
echo.
echo ========================================================================
echo                           TESTS DIRECTS PYTHON
echo ========================================================================
echo.

echo Test 1: Verification de main.py...
if exist "src\main.py" (
    echo Fichier src\main.py existe
    
    REM Test de syntaxe avec Python
    echo Test de syntaxe...
    %PYTHON_CMD% -c "import ast; ast.parse(open('src/main.py', 'r', encoding='utf-8').read()); print('SYNTAXE OK')" 2>test_output.txt
    if not errorlevel 1 (
        echo ✅ main.py: SYNTAXE CORRECTE
        type test_output.txt
    ) else (
        echo ❌ main.py: ERREURS DE SYNTAXE
        type test_output.txt
        goto :show_errors
    )
    
    REM Test de compilation
    echo Test de compilation...
    %PYTHON_CMD% -c "compile(open('src/main.py', 'r', encoding='utf-8').read(), 'main.py', 'exec'); print('COMPILATION OK')" 2>compile_output.txt
    if not errorlevel 1 (
        echo ✅ main.py: COMPILATION REUSSIE
        type compile_output.txt
    ) else (
        echo ❌ main.py: ERREURS DE COMPILATION
        type compile_output.txt
        goto :show_errors
    )
) else (
    echo ❌ src\main.py non trouve
)

echo.
echo Test 2: Verification de user_management.py...
if exist "src\auth\user_management.py" (
    echo Fichier src\auth\user_management.py existe
    
    REM Test de syntaxe
    echo Test de syntaxe...
    %PYTHON_CMD% -c "import ast; ast.parse(open('src/auth/user_management.py', 'r', encoding='utf-8').read()); print('SYNTAXE OK')" 2>user_test_output.txt
    if not errorlevel 1 (
        echo ✅ user_management.py: SYNTAXE CORRECTE
        type user_test_output.txt
    ) else (
        echo ❌ user_management.py: ERREURS DE SYNTAXE
        type user_test_output.txt
        goto :show_errors
    )
    
    REM Test de compilation
    echo Test de compilation...
    %PYTHON_CMD% -c "compile(open('src/auth/user_management.py', 'r', encoding='utf-8').read(), 'user_management.py', 'exec'); print('COMPILATION OK')" 2>user_compile_output.txt
    if not errorlevel 1 (
        echo ✅ user_management.py: COMPILATION REUSSIE
        type user_compile_output.txt
    ) else (
        echo ❌ user_management.py: ERREURS DE COMPILATION
        type user_compile_output.txt
        goto :show_errors
    )
) else (
    echo ❌ src\auth\user_management.py non trouve
)

echo.
echo Test 3: Test universel...
if exist "universal_test.py" (
    echo Execution du test universel...
    %PYTHON_CMD% universal_test.py >universal_output.txt 2>&1
    if not errorlevel 1 (
        echo ✅ Test universel: REUSSI
        type universal_output.txt
        set "TEST_SUCCESS=1"
    ) else (
        echo ❌ Test universel: ECHEC
        type universal_output.txt
    )
) else (
    echo ⚠️ universal_test.py non trouve
)

goto :results

:manual_check
echo.
echo ========================================================================
echo                           VERIFICATION MANUELLE
echo ========================================================================
echo.

echo Python non disponible - verification manuelle...
echo.

echo Verification de l existence des fichiers:
if exist "src\main.py" (
    echo ✅ src\main.py: EXISTE
) else (
    echo ❌ src\main.py: MANQUANT
)

if exist "src\auth\user_management.py" (
    echo ✅ src\auth\user_management.py: EXISTE
) else (
    echo ❌ src\auth\user_management.py: MANQUANT
)

if exist "src\auth\authentication.py" (
    echo ✅ src\auth\authentication.py: EXISTE
) else (
    echo ❌ src\auth\authentication.py: MANQUANT
)

if exist "src\auth\encryption.py" (
    echo ✅ src\auth\encryption.py: EXISTE
) else (
    echo ❌ src\auth\encryption.py: MANQUANT
)

goto :results

:show_errors
echo.
echo ========================================================================
echo                           ERREURS DETECTEES
echo ========================================================================
echo.

echo DETAILS DES ERREURS:
if exist "test_output.txt" (
    echo --- Erreurs main.py ---
    type test_output.txt
    echo.
)

if exist "compile_output.txt" (
    echo --- Erreurs compilation main.py ---
    type compile_output.txt
    echo.
)

if exist "user_test_output.txt" (
    echo --- Erreurs user_management.py ---
    type user_test_output.txt
    echo.
)

if exist "user_compile_output.txt" (
    echo --- Erreurs compilation user_management.py ---
    type user_compile_output.txt
    echo.
)

:results
echo.
echo ========================================================================
echo                              RESULTATS FINAUX
echo ========================================================================
echo.

if %TEST_SUCCESS%==1 (
    echo [%TIME%] 🎉 TOUS LES TESTS SONT REUSSIS !
    echo.
    echo ✅ AUCUN PROBLEME PYTHON DETECTE
    echo ✅ Syntaxe correcte dans tous les fichiers
    echo ✅ Compilation reussie pour tous les fichiers
    echo ✅ Code parfaitement fonctionnel
    echo.
    echo 🏆 LES FICHIERS PYTHON SONT PARFAITS !
) else (
    echo [%TIME%] ⚠️ PROBLEMES DETECTES
    echo.
    echo Consultez les details des erreurs ci-dessus
    echo.
    echo ACTIONS RECOMMANDEES:
    echo 1. Verifiez les erreurs de syntaxe affichees
    echo 2. Corrigez les problemes identifies
    echo 3. Relancez ce test pour validation
)

echo.
echo FICHIERS DE SORTIE CREES:
if exist "test_output.txt" echo   📄 test_output.txt: Resultats test main.py
if exist "compile_output.txt" echo   📄 compile_output.txt: Resultats compilation main.py
if exist "user_test_output.txt" echo   📄 user_test_output.txt: Resultats test user_management.py
if exist "user_compile_output.txt" echo   📄 user_compile_output.txt: Resultats compilation user_management.py
if exist "universal_output.txt" echo   📄 universal_output.txt: Resultats test universel

echo.
echo COMMANDES UTILES:
if defined PYTHON_CMD (
    echo   %PYTHON_CMD% universal_test.py        - Test universel
    echo   %PYTHON_CMD% src\main.py              - Lancer GestImmob
) else (
    echo   python universal_test.py        - Test universel
    echo   python src\main.py              - Lancer GestImmob
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul

REM Nettoyage
if exist "test_output.txt" del test_output.txt
if exist "compile_output.txt" del compile_output.txt
if exist "user_test_output.txt" del user_test_output.txt
if exist "user_compile_output.txt" del user_compile_output.txt
