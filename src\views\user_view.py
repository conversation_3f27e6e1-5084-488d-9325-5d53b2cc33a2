from PySide6.QtWidgets import Q<PERSON><PERSON>t, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox
from PySide6.QtCore import Qt

class UserView(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("User Dashboard")
        self.setGeometry(100, 100, 800, 600)

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        self.username_label = QLabel("Username:")
        self.username_input = QLineEdit()
        self.password_label = QLabel("Password:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)

        self.login_button = QPushButton("Login")
        self.login_button.clicked.connect(self.login)

        layout.addWidget(self.username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(self.password_label)
        layout.addWidget(self.password_input)
        layout.addWidget(self.login_button)

        self.setLayout(layout)

    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()

        # Here you would typically call the authentication method
        # For example: success = Authentication.login(username, password)

        # Simulating a successful login for demonstration
        success = True  # Replace with actual authentication logic

        if success:
            QMessageBox.information(self, "Success", "Login successful!")
            # Proceed to user-specific functionalities
        else:
            QMessageBox.warning(self, "Error", "Invalid username or password.")