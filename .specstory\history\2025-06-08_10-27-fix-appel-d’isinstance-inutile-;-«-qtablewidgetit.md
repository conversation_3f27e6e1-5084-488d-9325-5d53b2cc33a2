<!-- Generated by SpecStory -->

# /fix Appel d’isinstance inutile ; « QTableWidgetIt (2025-06-08 11:27:28)

_**User**_

/fix Appel d’isinstance inutile ; « QTableWidgetItem » est toujours une instance de « QTableWidgetItem »

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez l'appel inutile à `isinstance(item, QTableWidgetItem)` dans la fonction `safe_text`, car `item` est toujours un `QTableWidgetItem` à cet endroit.

````python
        def safe_text(item: 'QTableWidgetItem') -> str:
            # Suppression du test isinstance inutile
            text_attr = getattr(item, "text", None)
            if callable(text_attr):
                try:
                    v = text_attr()
                    if v is not None:
                        return str(v)
                except Exception:
                    return ""
            return ""
# ...existing code...
````

---

