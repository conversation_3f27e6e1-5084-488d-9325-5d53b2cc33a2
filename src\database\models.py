from sqlalchemy import (
    Column, Integer, String, ForeignKey, DateTime, Boolean, Text, DECIMAL
)
from sqlalchemy.orm import relationship, declarative_base, Mapped, mapped_column
from datetime import datetime, timezone

# Base ORM universelle
Base = declarative_base()

# Table des rôles (multilingue, extensible)
class Role(Base):
    __tablename__ = 'roles'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(255), default="")
    langue = Column(String(10), default="fr")
    users = relationship("User", back_populates="role")

# Table des utilisateurs (sécurité, multilingue, multi-logiciel)
class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(255), unique=True)
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    actif = Column(Boolean, default=True)
    langue = Column(String(10), default="fr")
    logiciel = Column(String(100), default="GestImmob")
    role = relationship("Role", back_populates="users")

# Table des paramètres globaux/configuration (optimisation, IA, Big Data, multilingue)
class GlobalConfig(Base):
    __tablename__ = 'configuration_globale'
    id = Column(Integer, primary_key=True, autoincrement=True)
    cle = Column(String(100), unique=True, nullable=False)
    valeur = Column(Text)
    type_parametre = Column(String(50))
    description = Column(Text)
    langue = Column(String(10), default="fr")
    date_modif = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

# Table des modèles IA utilisés
class ModeleIA(Base):
    __tablename__ = 'modeles_ia'
    id = Column(Integer, primary_key=True, autoincrement=True)
    nom_modele = Column(String(100), nullable=False)
    version = Column(String(50))
    fournisseur = Column(String(100))
    langue = Column(String(10), default="fr")
    type_modele = Column(String(50))
    actif = Column(Boolean, default=True)
    date_ajout = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    description = Column(Text)

# Table des logs d'audit et sécurité
class AuditLog(Base):
    __tablename__ = 'logs_securite'
    id = Column(Integer, primary_key=True, autoincrement=True)
    module = Column(String(100))
    action = Column(String(255))
    utilisateur = Column(String(255))
    date_action = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    niveau = Column(String(50))
    details = Column(Text)

# Table des langues supportées
class LangueSupportee(Base):
    __tablename__ = 'langues_traductions'
    id = Column(Integer, primary_key=True, autoincrement=True)
    code_langue = Column(String(10), unique=True, nullable=False)
    nom_langue = Column(String(100), nullable=False)
    actif = Column(Boolean, default=True)
    date_ajout = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    description = Column(Text)

# Table des fournisseurs (exemple enrichi)
class Fournisseur(Base):
    __tablename__ = 'fournisseurs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    nom = Column(String(255), nullable=False)
    adresse = Column(Text)
    telephone = Column(String(50))
    email = Column(String(255))
    type = Column(String(50))
    siret = Column(String(50))
    registre_commerce = Column(String(50))
    nif = Column(String(50))
    compte_bancaire = Column(String(100))
    banque = Column(String(100))
    contact = Column(String(255))
    categorie = Column(String(50))
    statut = Column(String(50))
    historique_achats = Column(Text)
    historique_factures = Column(Text)
    # Correction du type d'annotation pour impaye
    impaye: Mapped[float] = mapped_column(DECIMAL(18,2), nullable=True)
    date_creation = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    date_modif = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

# Table des immobilisations (exemple enrichi)
class Immobilisation(Base):
    __tablename__ = 'immobilisations'
    id = Column(Integer, primary_key=True, autoincrement=True)
    designation = Column(String(255), nullable=False)
    valeur: Mapped[float] = mapped_column(DECIMAL(18,2), nullable=False)
    annee = Column(Integer)
    localisation = Column(String(255))
    secteur = Column(String(255))
    observation = Column(Text)
    fournisseur_id = Column(Integer, ForeignKey('fournisseurs.id'))
    date_achat = Column(DateTime)
    duree_vie = Column(Integer)
    statut = Column(String(50))
    code_barres = Column(String(100))
    langue = Column(String(10), default="fr")
    fournisseur = relationship("Fournisseur")

# ...ajoutez ici d'autres modèles selon vos besoins (inventaire, documents, logs, etc.)...