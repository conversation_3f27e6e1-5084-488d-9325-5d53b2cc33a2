<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:31:55)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Corriger l'accès à `.text()` sur un objet potentiellement `None` en utilisant une fonction utilitaire pour éviter l'erreur.

````python
# ...existing code...
    def afficher_charts(self):
        try:
            import matplotlib.pyplot as plt
            from collections import defaultdict
            from typing import DefaultDict
            stats_categorie: DefaultDict[str, int] = defaultdict(int)
            stats_statut: DefaultDict[str, int] = defaultdict(int)
            def safe_text(item):
                return item.text() if item is not None and hasattr(item, "text") else ""
            for row in range(self.table.rowCount()):
                cat = safe_text(self.table.item(row, 2))
                statut = safe_text(self.table.item(row, 9))
                stats_categorie[cat] += 1
                stats_statut[statut] += 1
# ...existing code...
````

---

