#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RÉSOLUTION DÉFINITIVE DE TOUS LES PROBLÈMES PYTHON
Solution complète et exhaustive pour régler le problème agaçant de Python
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
import winreg
from pathlib import Path
import json
import time

class ResolveurPythonDefinitif:
    def __init__(self):
        self.systeme = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.python_installe = False
        self.python_path = None
        self.pip_installe = False
        self.problemes_detectes = []
        self.solutions_appliquees = []

    def detecter_tous_problemes_python(self):
        """Détecte TOUS les problèmes Python possibles"""
        print("🔍 DÉTECTION EXHAUSTIVE DES PROBLÈMES PYTHON")
        print("=" * 60)

        # Test 1: Python installé ?
        print("1. Test installation Python...")
        if not self._tester_python_installe():
            self.problemes_detectes.append("python_non_installe")
            print("❌ Python non installé")
        else:
            print("✅ Python détecté")

        # Test 2: Python dans le PATH ?
        print("\n2. Test Python dans PATH...")
        if not self._tester_python_path():
            self.problemes_detectes.append("python_pas_dans_path")
            print("❌ Python pas dans PATH")
        else:
            print("✅ Python dans PATH")

        # Test 3: Pip installé ?
        print("\n3. Test pip...")
        if not self._tester_pip():
            self.problemes_detectes.append("pip_non_installe")
            print("❌ pip non installé")
        else:
            print("✅ pip disponible")

        # Test 4: Version Python compatible ?
        print("\n4. Test version Python...")
        if not self._tester_version_python():
            self.problemes_detectes.append("version_python_incompatible")
            print("❌ Version Python incompatible")
        else:
            print("✅ Version Python compatible")

        # Test 5: Permissions ?
        print("\n5. Test permissions...")
        if not self._tester_permissions():
            self.problemes_detectes.append("permissions_insuffisantes")
            print("❌ Permissions insuffisantes")
        else:
            print("✅ Permissions OK")

        # Test 6: Variables d'environnement ?
        print("\n6. Test variables d'environnement...")
        if not self._tester_variables_environnement():
            self.problemes_detectes.append("variables_environnement_incorrectes")
            print("❌ Variables d'environnement incorrectes")
        else:
            print("✅ Variables d'environnement OK")

        # Test 7: Conflits d'installation ?
        print("\n7. Test conflits d'installation...")
        if self._detecter_conflits_installation():
            self.problemes_detectes.append("conflits_installation")
            print("❌ Conflits d'installation détectés")
        else:
            print("✅ Pas de conflits")

        print(f"\n📊 RÉSUMÉ DÉTECTION:")
        print(f"Problèmes détectés: {len(self.problemes_detectes)}")
        for probleme in self.problemes_detectes:
            print(f"  - {probleme}")

        return len(self.problemes_detectes) == 0

    def _tester_python_installe(self):
        """Teste si Python est installé"""
        commandes_test = ['python', 'python3', 'py']

        for cmd in commandes_test:
            try:
                result = subprocess.run([cmd, '--version'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.python_path = cmd
                    self.python_installe = True
                    return True
            except:
                continue

        return False

    def _tester_python_path(self):
        """Teste si Python est dans le PATH"""
        if self.systeme == 'windows':
            try:
                # Vérifier dans le registre Windows
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment")
                path_value, _ = winreg.QueryValueEx(key, "Path")
                winreg.CloseKey(key)

                return 'python' in path_value.lower()
            except:
                pass

        # Test universel
        path_env = os.environ.get('PATH', '')
        return any('python' in p.lower() for p in path_env.split(os.pathsep))

    def _tester_pip(self):
        """Teste si pip est disponible"""
        commandes_pip = ['pip', 'pip3', 'python -m pip', 'python3 -m pip', 'py -m pip']

        for cmd in commandes_pip:
            try:
                result = subprocess.run(cmd.split() + ['--version'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.pip_installe = True
                    return True
            except:
                continue

        return False

    def _tester_version_python(self):
        """Teste si la version Python est compatible"""
        if not self.python_installe:
            return False

        try:
            result = subprocess.run([self.python_path, '-c',
                                   'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")'],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                version_str = result.stdout.strip()
                major, minor = map(int, version_str.split('.'))

                # Python 3.8+ requis
                return major >= 3 and minor >= 8
        except:
            pass

        return False

    def _tester_permissions(self):
        """Teste les permissions"""
        try:
            # Test écriture dans le répertoire courant
            test_file = Path('test_permissions.tmp')
            test_file.write_text('test')
            test_file.unlink()

            # Test installation pip (simulation)
            if self.pip_installe:
                result = subprocess.run([self.python_path, '-m', 'pip', 'list'],
                                      capture_output=True, text=True, timeout=10)
                return result.returncode == 0

            return True
        except:
            return False

    def _tester_variables_environnement(self):
        """Teste les variables d'environnement"""
        variables_requises = ['PATH']

        if self.systeme == 'windows':
            variables_requises.extend(['PYTHONPATH', 'PYTHONHOME'])

        for var in variables_requises:
            if var == 'PATH':
                continue  # Déjà testé

            value = os.environ.get(var)
            if var in ['PYTHONPATH', 'PYTHONHOME'] and value:
                # Vérifier que les chemins existent
                for path in value.split(os.pathsep):
                    if path and not os.path.exists(path):
                        return False

        return True

    def _detecter_conflits_installation(self):
        """Détecte les conflits d'installation"""
        conflits = []

        # Détecter multiples installations Python
        installations_python = []

        if self.systeme == 'windows':
            # Chercher dans les emplacements standards
            emplacements = [
                r"C:\Python*",
                r"C:\Program Files\Python*",
                r"C:\Program Files (x86)\Python*",
                r"C:\Users\<USER>\AppData\Local\Programs\Python*"
            ]

            for emplacement in emplacements:
                try:
                    for path in Path('C:\\').glob(emplacement.replace('C:\\', '')):
                        if path.is_dir():
                            installations_python.append(str(path))
                except:
                    continue

        # Plus de 2 installations = conflit potentiel
        if len(installations_python) > 2:
            conflits.append(f"Multiples installations: {len(installations_python)}")

        return len(conflits) > 0

    def resoudre_tous_problemes(self):
        """Résout TOUS les problèmes Python détectés"""
        print(f"\n🔧 RÉSOLUTION DE TOUS LES PROBLÈMES PYTHON")
        print("=" * 60)

        if not self.problemes_detectes:
            print("✅ Aucun problème à résoudre !")
            return True

        print(f"🔧 Résolution de {len(self.problemes_detectes)} problème(s)...")

        for probleme in self.problemes_detectes:
            print(f"\n🔧 Résolution: {probleme}")

            if probleme == "python_non_installe":
                self._installer_python_automatiquement()
            elif probleme == "python_pas_dans_path":
                self._ajouter_python_au_path()
            elif probleme == "pip_non_installe":
                self._installer_pip()
            elif probleme == "version_python_incompatible":
                self._mettre_a_jour_python()
            elif probleme == "permissions_insuffisantes":
                self._corriger_permissions()
            elif probleme == "variables_environnement_incorrectes":
                self._corriger_variables_environnement()
            elif probleme == "conflits_installation":
                self._resoudre_conflits_installation()

        # Test final
        print(f"\n✅ VALIDATION FINALE")
        if self._tester_python_installe() and self._tester_pip():
            print("🎉 TOUS LES PROBLÈMES PYTHON RÉSOLUS !")
            return True
        else:
            print("⚠️ Certains problèmes persistent")
            return False

    def _installer_python_automatiquement(self):
        """Installe Python automatiquement"""
        print("📦 Installation automatique de Python...")

        if self.systeme == 'windows':
            self._installer_python_windows()
        elif self.systeme == 'linux':
            self._installer_python_linux()
        elif self.systeme == 'darwin':
            self._installer_python_macos()
        else:
            print(f"❌ Installation automatique non supportée pour {self.systeme}")

    def _installer_python_windows(self):
        """Installe Python sur Windows"""
        try:
            print("🪟 Installation Python pour Windows...")

            # Déterminer l'architecture
            if '64' in self.architecture or 'amd64' in self.architecture:
                arch = 'amd64'
            else:
                arch = 'win32'

            # URL de téléchargement Python 3.11
            python_version = "3.11.7"
            url = f"https://www.python.org/ftp/python/{python_version}/python-{python_version}-{arch}.exe"

            print(f"📥 Téléchargement Python {python_version}...")
            installer_path = Path("python_installer.exe")

            # Télécharger l'installateur
            urllib.request.urlretrieve(url, installer_path)

            print("🔧 Installation en cours...")
            # Installer avec options automatiques
            result = subprocess.run([
                str(installer_path),
                '/quiet',
                'InstallAllUsers=1',
                'PrependPath=1',
                'Include_test=0'
            ], timeout=600)

            if result.returncode == 0:
                print("✅ Python installé avec succès")
                self.solutions_appliquees.append("Python installé automatiquement")

                # Nettoyer
                installer_path.unlink(missing_ok=True)

                # Mettre à jour les variables
                self.python_installe = True
                self.python_path = 'python'

            else:
                print("❌ Échec installation Python")

        except Exception as e:
            print(f"❌ Erreur installation Python: {e}")
            # Fallback: installation manuelle
            self._guide_installation_manuelle_windows()

    def _installer_python_linux(self):
        """Installe Python sur Linux"""
        try:
            print("🐧 Installation Python pour Linux...")

            # Détecter la distribution
            if os.path.exists('/etc/debian_version'):
                # Debian/Ubuntu
                commands = [
                    ['sudo', 'apt', 'update'],
                    ['sudo', 'apt', 'install', '-y', 'python3', 'python3-pip', 'python3-venv']
                ]
            elif os.path.exists('/etc/redhat-release'):
                # RedHat/CentOS/Fedora
                commands = [
                    ['sudo', 'yum', 'install', '-y', 'python3', 'python3-pip']
                ]
            else:
                print("❌ Distribution Linux non reconnue")
                return

            for cmd in commands:
                print(f"🔧 Exécution: {' '.join(cmd)}")
                result = subprocess.run(cmd, timeout=300)
                if result.returncode != 0:
                    print(f"⚠️ Échec commande: {' '.join(cmd)}")

            print("✅ Installation Python Linux terminée")
            self.solutions_appliquees.append("Python installé sur Linux")

        except Exception as e:
            print(f"❌ Erreur installation Python Linux: {e}")

    def _installer_python_macos(self):
        """Installe Python sur macOS"""
        try:
            print("🍎 Installation Python pour macOS...")

            # Vérifier Homebrew
            result = subprocess.run(['brew', '--version'], capture_output=True, timeout=10)

            if result.returncode == 0:
                # Installer avec Homebrew
                commands = [
                    ['brew', 'install', 'python@3.11'],
                    ['brew', 'link', 'python@3.11']
                ]

                for cmd in commands:
                    print(f"🔧 Exécution: {' '.join(cmd)}")
                    subprocess.run(cmd, timeout=300)

                print("✅ Installation Python macOS terminée")
                self.solutions_appliquees.append("Python installé sur macOS")
            else:
                print("❌ Homebrew requis pour installation automatique")
                self._guide_installation_manuelle_macos()

        except Exception as e:
            print(f"❌ Erreur installation Python macOS: {e}")

    def _ajouter_python_au_path(self):
        """Ajoute Python au PATH"""
        print("🛤️ Ajout de Python au PATH...")

        if self.systeme == 'windows':
            self._ajouter_python_path_windows()
        else:
            self._ajouter_python_path_unix()

    def _ajouter_python_path_windows(self):
        """Ajoute Python au PATH Windows"""
        try:
            # Trouver l'installation Python
            python_dirs = []

            # Chercher dans les emplacements standards
            search_paths = [
                Path(r"C:\Python*"),
                Path(r"C:\Program Files\Python*"),
                Path(r"C:\Users") / os.environ.get('USERNAME', '') / r"AppData\Local\Programs\Python*"
            ]

            for search_path in search_paths:
                try:
                    for path in search_path.parent.glob(search_path.name):
                        if path.is_dir() and (path / 'python.exe').exists():
                            python_dirs.append(str(path))
                            python_dirs.append(str(path / 'Scripts'))
                except:
                    continue

            if python_dirs:
                # Modifier le registre pour ajouter au PATH
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                                       r"Environment", 0, winreg.KEY_ALL_ACCESS)

                    try:
                        current_path, _ = winreg.QueryValueEx(key, "Path")
                    except FileNotFoundError:
                        current_path = ""

                    # Ajouter les chemins Python
                    new_paths = []
                    for python_dir in python_dirs:
                        if python_dir not in current_path:
                            new_paths.append(python_dir)

                    if new_paths:
                        new_path = current_path + ";" + ";".join(new_paths)
                        winreg.SetValueEx(key, "Path", 0, winreg.REG_EXPAND_SZ, new_path)

                        print("✅ Python ajouté au PATH utilisateur")
                        self.solutions_appliquees.append("Python ajouté au PATH")

                    winreg.CloseKey(key)

                except Exception as e:
                    print(f"❌ Erreur modification registre: {e}")
            else:
                print("❌ Installation Python non trouvée")

        except Exception as e:
            print(f"❌ Erreur ajout PATH: {e}")

    def _ajouter_python_path_unix(self):
        """Ajoute Python au PATH Unix"""
        try:
            # Trouver Python
            python_paths = []

            common_paths = ['/usr/bin', '/usr/local/bin', '/opt/python*/bin']

            for path_pattern in common_paths:
                for path in Path('/').glob(path_pattern.lstrip('/')):
                    if (path / 'python3').exists():
                        python_paths.append(str(path))

            if python_paths:
                # Ajouter au .bashrc/.zshrc
                shell_configs = [
                    Path.home() / '.bashrc',
                    Path.home() / '.zshrc',
                    Path.home() / '.profile'
                ]

                for config_file in shell_configs:
                    if config_file.exists():
                        with open(config_file, 'a') as f:
                            f.write(f"\n# Python PATH ajouté automatiquement\n")
                            for python_path in python_paths:
                                f.write(f"export PATH=\"{python_path}:$PATH\"\n")

                        print(f"✅ Python ajouté à {config_file}")
                        self.solutions_appliquees.append(f"Python ajouté au PATH dans {config_file}")
                        break

        except Exception as e:
            print(f"❌ Erreur ajout PATH Unix: {e}")

    def _installer_pip(self):
        """Installe pip"""
        print("📦 Installation de pip...")

        try:
            # Méthode 1: get-pip.py
            print("📥 Téléchargement get-pip.py...")

            get_pip_url = "https://bootstrap.pypa.io/get-pip.py"
            get_pip_path = Path("get-pip.py")

            urllib.request.urlretrieve(get_pip_url, get_pip_path)

            # Installer pip
            if self.python_path:
                result = subprocess.run([self.python_path, str(get_pip_path)], timeout=300)

                if result.returncode == 0:
                    print("✅ pip installé avec succès")
                    self.solutions_appliquees.append("pip installé")
                    self.pip_installe = True
                else:
                    print("❌ Échec installation pip")

            # Nettoyer
            get_pip_path.unlink(missing_ok=True)

        except Exception as e:
            print(f"❌ Erreur installation pip: {e}")

            # Méthode alternative: ensurepip
            try:
                print("🔄 Tentative avec ensurepip...")
                result = subprocess.run([self.python_path, '-m', 'ensurepip', '--upgrade'], timeout=300)

                if result.returncode == 0:
                    print("✅ pip installé avec ensurepip")
                    self.solutions_appliquees.append("pip installé avec ensurepip")
                    self.pip_installe = True

            except Exception as e2:
                print(f"❌ Erreur ensurepip: {e2}")

    def _mettre_a_jour_python(self):
        """Met à jour Python vers une version compatible"""
        print("🔄 Mise à jour de Python...")

        # Désinstaller l'ancienne version et installer la nouvelle
        if self.systeme == 'windows':
            print("⚠️ Mise à jour manuelle requise sur Windows")
            self._guide_mise_a_jour_windows()
        else:
            # Sur Unix, on peut essayer de mettre à jour
            self._installer_python_automatiquement()

    def _corriger_permissions(self):
        """Corrige les problèmes de permissions"""
        print("🔐 Correction des permissions...")

        try:
            if self.systeme == 'windows':
                # Sur Windows, essayer de relancer en tant qu'administrateur
                print("💡 Redémarrage en tant qu'administrateur recommandé")

                # Créer un script batch pour relancer en admin
                admin_script = Path("run_as_admin.bat")
                with open(admin_script, 'w') as f:
                    f.write(f'@echo off\n')
                    f.write(f'cd /d "{os.getcwd()}"\n')
                    f.write(f'python RESOUDRE_PYTHON_DEFINITIF.py\n')
                    f.write(f'pause\n')

                print(f"📄 Script admin créé: {admin_script}")
                print("💡 Clic droit → Exécuter en tant qu'administrateur")

            else:
                # Sur Unix, corriger les permissions des répertoires Python
                python_dirs = ['/usr/local/lib/python*', '/usr/lib/python*']

                for pattern in python_dirs:
                    for path in Path('/').glob(pattern.lstrip('/')):
                        try:
                            subprocess.run(['sudo', 'chmod', '-R', '755', str(path)], timeout=60)
                        except:
                            continue

                print("✅ Permissions corrigées")
                self.solutions_appliquees.append("Permissions corrigées")

        except Exception as e:
            print(f"❌ Erreur correction permissions: {e}")

    def _corriger_variables_environnement(self):
        """Corrige les variables d'environnement"""
        print("🌍 Correction des variables d'environnement...")

        try:
            if self.systeme == 'windows':
                # Nettoyer PYTHONPATH et PYTHONHOME incorrects
                variables_a_nettoyer = ['PYTHONPATH', 'PYTHONHOME']

                for var in variables_a_nettoyer:
                    try:
                        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                                           r"Environment", 0, winreg.KEY_ALL_ACCESS)

                        try:
                            winreg.DeleteValue(key, var)
                            print(f"✅ Variable {var} nettoyée")
                        except FileNotFoundError:
                            pass  # Variable n'existe pas

                        winreg.CloseKey(key)

                    except Exception as e:
                        print(f"⚠️ Erreur nettoyage {var}: {e}")

                self.solutions_appliquees.append("Variables d'environnement nettoyées")

        except Exception as e:
            print(f"❌ Erreur correction variables: {e}")

    def _resoudre_conflits_installation(self):
        """Résout les conflits d'installation"""
        print("🔄 Résolution des conflits d'installation...")

        try:
            # Lister toutes les installations Python
            installations = self._lister_installations_python()

            if len(installations) > 1:
                print(f"📋 {len(installations)} installations Python trouvées:")
                for i, install in enumerate(installations):
                    print(f"  {i+1}. {install}")

                # Recommander la plus récente
                installation_recommandee = max(installations, key=lambda x: os.path.getmtime(x))
                print(f"💡 Installation recommandée: {installation_recommandee}")

                # Mettre à jour les variables pour utiliser la bonne installation
                self.python_path = str(Path(installation_recommandee) / 'python.exe')

                print("✅ Conflit résolu - utilisation de l'installation la plus récente")
                self.solutions_appliquees.append("Conflits d'installation résolus")

        except Exception as e:
            print(f"❌ Erreur résolution conflits: {e}")

    def _lister_installations_python(self):
        """Liste toutes les installations Python"""
        installations = []

        if self.systeme == 'windows':
            search_paths = [
                r"C:\Python*",
                r"C:\Program Files\Python*",
                r"C:\Program Files (x86)\Python*",
                r"C:\Users\<USER>\AppData\Local\Programs\Python*"
            ]

            for pattern in search_paths:
                try:
                    for path in Path('C:\\').glob(pattern.replace('C:\\', '')):
                        if path.is_dir() and (path / 'python.exe').exists():
                            installations.append(str(path))
                except:
                    continue

        return installations

    def _guide_installation_manuelle_windows(self):
        """Guide d'installation manuelle pour Windows"""
        print("\n📋 GUIDE D'INSTALLATION MANUELLE WINDOWS")
        print("-" * 50)
        print("1. Allez sur https://www.python.org/downloads/")
        print("2. Téléchargez Python 3.11 ou plus récent")
        print("3. Lancez l'installateur")
        print("4. ✅ COCHEZ 'Add Python to PATH'")
        print("5. Cliquez 'Install Now'")
        print("6. Redémarrez votre ordinateur")
        print("7. Relancez ce script")

    def _guide_installation_manuelle_macos(self):
        """Guide d'installation manuelle pour macOS"""
        print("\n📋 GUIDE D'INSTALLATION MANUELLE MACOS")
        print("-" * 50)
        print("1. Installez Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
        print("2. Puis: brew install python@3.11")
        print("3. Ou téléchargez depuis https://www.python.org/downloads/")

    def _guide_mise_a_jour_windows(self):
        """Guide de mise à jour pour Windows"""
        print("\n📋 GUIDE DE MISE À JOUR WINDOWS")
        print("-" * 50)
        print("1. Désinstallez l'ancienne version via Panneau de configuration")
        print("2. Téléchargez Python 3.11+ depuis https://www.python.org/downloads/")
        print("3. Installez en cochant 'Add Python to PATH'")
        print("4. Redémarrez et relancez ce script")

    def tester_installation_finale(self):
        """Teste l'installation finale de Python"""
        print(f"\n🧪 TEST FINAL DE L'INSTALLATION PYTHON")
        print("=" * 60)

        tests_reussis = 0
        tests_total = 6

        # Test 1: Python disponible
        print("1. Test disponibilité Python...")
        if self._test_python_disponible():
            print("   ✅ Python disponible")
            tests_reussis += 1
        else:
            print("   ❌ Python non disponible")

        # Test 2: Version correcte
        print("2. Test version Python...")
        if self._test_version_correcte():
            print("   ✅ Version Python correcte")
            tests_reussis += 1
        else:
            print("   ❌ Version Python incorrecte")

        # Test 3: pip disponible
        print("3. Test disponibilité pip...")
        if self._test_pip_disponible():
            print("   ✅ pip disponible")
            tests_reussis += 1
        else:
            print("   ❌ pip non disponible")

        # Test 4: Installation de package
        print("4. Test installation package...")
        if self._test_installation_package():
            print("   ✅ Installation package OK")
            tests_reussis += 1
        else:
            print("   ❌ Installation package échoue")

        # Test 5: Import modules standards
        print("5. Test import modules...")
        if self._test_import_modules():
            print("   ✅ Import modules OK")
            tests_reussis += 1
        else:
            print("   ❌ Import modules échoue")

        # Test 6: Exécution script
        print("6. Test exécution script...")
        if self._test_execution_script():
            print("   ✅ Exécution script OK")
            tests_reussis += 1
        else:
            print("   ❌ Exécution script échoue")

        # Résultats
        pourcentage = (tests_reussis / tests_total) * 100
        print(f"\n📊 RÉSULTATS FINAUX:")
        print(f"Tests réussis: {tests_reussis}/{tests_total} ({pourcentage:.1f}%)")

        if tests_reussis == tests_total:
            print("🎉 PYTHON PARFAITEMENT CONFIGURÉ !")
            return True
        elif tests_reussis >= 4:
            print("✅ Python fonctionnel (quelques problèmes mineurs)")
            return True
        else:
            print("❌ Python non fonctionnel")
            return False

    def _test_python_disponible(self):
        """Teste si Python est disponible"""
        commandes = ['python', 'python3', 'py']

        for cmd in commandes:
            try:
                result = subprocess.run([cmd, '--version'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.python_path = cmd
                    return True
            except:
                continue
        return False

    def _test_version_correcte(self):
        """Teste si la version Python est correcte"""
        if not self.python_path:
            return False

        try:
            result = subprocess.run([self.python_path, '-c',
                                   'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")'],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                version_str = result.stdout.strip()
                major, minor = map(int, version_str.split('.'))
                return major >= 3 and minor >= 8
        except:
            pass
        return False

    def _test_pip_disponible(self):
        """Teste si pip est disponible"""
        if not self.python_path:
            return False

        try:
            result = subprocess.run([self.python_path, '-m', 'pip', '--version'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False

    def _test_installation_package(self):
        """Teste l'installation d'un package"""
        if not self.python_path:
            return False

        try:
            # Tester avec un package léger
            result = subprocess.run([self.python_path, '-m', 'pip', 'install', '--quiet', 'requests'],
                                  capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                # Tester l'import
                result2 = subprocess.run([self.python_path, '-c', 'import requests; print("OK")'],
                                       capture_output=True, text=True, timeout=10)
                return result2.returncode == 0
        except:
            pass
        return False

    def _test_import_modules(self):
        """Teste l'import de modules standards"""
        if not self.python_path:
            return False

        modules_test = ['os', 'sys', 'json', 'pathlib', 'datetime']

        try:
            imports = '; '.join([f'import {module}' for module in modules_test])
            result = subprocess.run([self.python_path, '-c', f'{imports}; print("OK")'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False

    def _test_execution_script(self):
        """Teste l'exécution d'un script simple"""
        if not self.python_path:
            return False

        try:
            # Créer un script de test
            test_script = Path('test_python.py')
            test_script.write_text('''#!/usr/bin/env python3
print("Python fonctionne parfaitement !")
import sys
print(f"Version: {sys.version}")
''')

            # Exécuter le script
            result = subprocess.run([self.python_path, str(test_script)],
                                  capture_output=True, text=True, timeout=10)

            # Nettoyer
            test_script.unlink(missing_ok=True)

            return result.returncode == 0

        except:
            return False

    def generer_rapport_final(self):
        """Génère un rapport final complet"""
        print(f"\n📊 RAPPORT FINAL - RÉSOLUTION PYTHON")
        print("=" * 80)

        print(f"🖥️ Système: {self.systeme} ({self.architecture})")
        print(f"🐍 Python installé: {'✅ Oui' if self.python_installe else '❌ Non'}")
        print(f"📦 pip installé: {'✅ Oui' if self.pip_installe else '❌ Non'}")
        print(f"🛤️ Python path: {self.python_path or 'Non défini'}")

        print(f"\n🔍 Problèmes détectés: {len(self.problemes_detectes)}")
        for probleme in self.problemes_detectes:
            print(f"  - {probleme}")

        print(f"\n🔧 Solutions appliquées: {len(self.solutions_appliquees)}")
        for solution in self.solutions_appliquees:
            print(f"  - {solution}")

        # Test final
        if self.tester_installation_finale():
            print(f"\n🎉 PYTHON COMPLÈTEMENT RÉSOLU !")
            print(f"✅ Toutes les fonctionnalités Python sont opérationnelles")
            print(f"✅ Vous pouvez maintenant utiliser Python sans problème")

            print(f"\n🚀 COMMANDES PYTHON DISPONIBLES:")
            if self.python_path:
                print(f"  {self.python_path} --version")
                print(f"  {self.python_path} -m pip install package_name")
                print(f"  {self.python_path} script.py")

            return True
        else:
            print(f"\n⚠️ RÉSOLUTION PARTIELLE")
            print(f"Certains problèmes persistent")
            print(f"Consultez les guides d'installation manuelle ci-dessus")
            return False

def main():
    """Fonction principale de résolution Python"""
    print("🐍 RÉSOLUTION DÉFINITIVE DES PROBLÈMES PYTHON")
    print("=" * 80)
    print("Solution complète pour régler le problème agaçant de Python")
    print("une fois pour toutes !")

    resolveur = ResolveurPythonDefinitif()

    try:
        # Étape 1: Détection exhaustive
        print(f"\n🔍 ÉTAPE 1: DÉTECTION EXHAUSTIVE")
        problemes_detectes = not resolveur.detecter_tous_problemes_python()

        if not problemes_detectes:
            print(f"\n🎉 AUCUN PROBLÈME DÉTECTÉ !")
            print(f"✅ Python est déjà parfaitement configuré")
            resolveur.tester_installation_finale()
            return True

        # Étape 2: Résolution automatique
        print(f"\n🔧 ÉTAPE 2: RÉSOLUTION AUTOMATIQUE")
        if resolveur.resoudre_tous_problemes():
            print(f"✅ Résolution automatique réussie")
        else:
            print(f"⚠️ Résolution automatique partielle")

        # Étape 3: Rapport final
        print(f"\n📊 ÉTAPE 3: RAPPORT FINAL")
        succes = resolveur.generer_rapport_final()

        if succes:
            print(f"\n🏆 PROBLÈME PYTHON DÉFINITIVEMENT RÉSOLU !")
            print(f"🎯 Python est maintenant parfaitement fonctionnel")
        else:
            print(f"\n⚠️ RÉSOLUTION PARTIELLE")
            print(f"Suivez les guides d'installation manuelle affichés")

        return succes

    except KeyboardInterrupt:
        print(f"\n❌ Résolution interrompue par l'utilisateur")
        return False
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        return False

if __name__ == "__main__":
    main()