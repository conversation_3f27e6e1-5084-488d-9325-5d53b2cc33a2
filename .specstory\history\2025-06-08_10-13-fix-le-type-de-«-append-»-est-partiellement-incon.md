<!-- Generated by SpecStory -->

# /fix Le type de « append » est partiellement incon (2025-06-08 11:13:11)

_**User**_

/fix Le type de « append » est partiellement inconnu
  Le type de "append" est "(object: Unknown, /) -> None"

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Pour corriger l'avertissement sur le type de `append`, il suffit d'indiquer explicitement que `fournisseur` est une liste de chaînes de caractères (`list[str]`). Cela aide l'analyseur de type à comprendre le type attendu pour `append`.

Voici la modification à apporter dans la méthode `sauvegarder_fournisseurs` :

````python
    def sauvegarder_fournisseurs(self):
        fournisseurs = []
        for row in range(self.table.rowCount()):
            fournisseur: list[str] = []  # Ajout de l'annotation de type explicite
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                fournisseur.append(item.text() if item else "")
            fournisseurs.append(fournisseur)
        with open(self.fichier_persistance, "w", encoding="utf-8") as f:
            json.dump(fournisseurs, f, ensure_ascii=False, indent=2)
# ...existing code...
````

---

