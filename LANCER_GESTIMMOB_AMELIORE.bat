@echo off
title GESTIMMOB AMELIORE - Modules Personnalises par Secteur
color 0B

echo.
echo ========================================================================
echo                    🎯 GESTIMMOB AMÉLIORE 🎯
echo              Modules Personnalisés par Secteur et Domaine
echo ========================================================================
echo.

echo [%TIME%] Lancement de GestImmob avec modules personnalises...

REM Variables
set "PYTHON_CMD="
set "AMELIORATION_EFFECTUEE=0"

echo.
echo 🔍 DETECTION PYTHON...

REM Test avec py (recommandé pour Windows)
py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :check_files
)

REM Test avec python
python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :check_files
)

echo ❌ Python non detecte
echo 💡 Executez SOLUTION_PYTHON_FINALE.bat pour installer Python
goto :end

:check_files
echo.
echo 📁 VERIFICATION DES FICHIERS...

if exist "gestimmob_final.py" (
    echo ✅ Fichier principal: gestimmob_final.py
) else (
    echo ❌ Fichier principal manquant: gestimmob_final.py
    goto :end
)

if exist "modules_personnalises.py" (
    echo ✅ Module personnalise: modules_personnalises.py
) else (
    echo ❌ Module manquant: modules_personnalises.py
    goto :end
)

if exist "interface_modules_personnalises.py" (
    echo ✅ Interface modules: interface_modules_personnalises.py
) else (
    echo ❌ Interface manquante: interface_modules_personnalises.py
    goto :end
)

if exist "ameliorer_secteurs_gestimmob.py" (
    echo ✅ Script d amelioration: ameliorer_secteurs_gestimmob.py
) else (
    echo ❌ Script d amelioration manquant
    goto :end
)

echo.
echo 🔧 AMELIORATION DE LA BASE DE DONNEES...

REM Exécuter le script d'amélioration
echo Execution du script d amelioration des secteurs...
%PYTHON_CMD% ameliorer_secteurs_gestimmob.py

if not errorlevel 1 (
    echo ✅ Base de donnees amelioree avec succes !
    set /a AMELIORATION_EFFECTUEE=1
) else (
    echo ⚠️ Probleme lors de l amelioration - on continue...
)

echo.
echo 📦 VERIFICATION DEPENDANCES...

REM Vérifier PySide6
echo Test PySide6...
%PYTHON_CMD% -c "import PySide6; print('✅ PySide6: Disponible')" 2>nul
if errorlevel 1 (
    echo ❌ PySide6 manquant - Installation...
    %PYTHON_CMD% -m pip install PySide6 --quiet
    if not errorlevel 1 (
        echo ✅ PySide6 installe
    ) else (
        echo ⚠️ Probleme installation PySide6
    )
) else (
    echo ✅ PySide6 OK
)

echo.
echo 🔧 PREPARATION ENVIRONNEMENT...

REM Créer les répertoires nécessaires
if not exist "data" (
    mkdir data
    echo ✅ Repertoire data cree
)

if not exist "logs" (
    mkdir logs
    echo ✅ Repertoire logs cree
)

if not exist "backups" (
    mkdir backups
    echo ✅ Repertoire backups cree
)

echo.
echo ========================================================================
echo                    🚀 LANCEMENT GESTIMMOB AMELIORE 🚀
echo ========================================================================
echo.

echo [%TIME%] Demarrage de GestImmob avec modules personnalises...
echo.
echo 🎯 FONCTIONNALITES AMELIOREES:
echo   💻 Informatique ^& Technologies - Criteres techniques avances
echo   🚗 Vehicules ^& Transport - Suivi complet du parc automobile
echo   🪑 Mobilier ^& Amenagement - Gestion ergonomique
echo   ⚙️ Equipements Industriels - Maintenance preventive
echo   🏢 Immobilier ^& Batiments - Gestion patrimoniale
echo   🏥 Equipements Medicaux - Conformite reglementaire
echo.

echo Configuration:
echo   Python: %PYTHON_CMD%
echo   Fichier: gestimmob_final.py
echo   Modules: Personnalises actives
echo   Base: Amelioree avec secteurs specialises
echo.

echo 🎯 Lancement en cours...
echo.

REM Lancement de l'application
%PYTHON_CMD% gestimmob_final.py

set "EXIT_CODE=%ERRORLEVEL%"

echo.
echo ========================================================================
echo                              RESULTAT FINAL
echo ========================================================================
echo.

if %EXIT_CODE%==0 (
    echo [%TIME%] ✅ GESTIMMOB AMELIORE FERME NORMALEMENT
    echo.
    echo 🎉 LANCEMENT REUSSI !
    echo ✅ GestImmob ameliore a fonctionne parfaitement
    echo ✅ Modules personnalises operationnels
    echo ✅ Secteurs specialises disponibles
    echo.
    echo 🎯 FONCTIONNALITES DISPONIBLES:
    echo   ✅ Gestion par secteurs specialises
    echo   ✅ Criteres specifiques par domaine
    echo   ✅ Saisie adaptee a chaque secteur
    echo   ✅ Recherche avancee par criteres
    echo   ✅ Rapports sectoriels detailles
    echo   ✅ Interface moderne et intuitive
    echo.
    echo 💡 Pour relancer: executez a nouveau ce script
    echo    Ou tapez: %PYTHON_CMD% gestimmob_final.py
    echo.
) else (
    echo [%TIME%] ❌ GESTIMMOB FERME AVEC ERREUR (Code: %EXIT_CODE%)
    echo.
    echo 🔧 DIAGNOSTIC:
    
    %PYTHON_CMD% -c "print('✅ Python de base: OK')" 2>nul || echo ❌ Python de base: Probleme
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul || echo ❌ PySide6: Probleme
    %PYTHON_CMD% -c "import sqlite3; print('✅ SQLite: OK')" 2>nul || echo ❌ SQLite: Probleme
    
    echo.
    echo 💡 SOLUTIONS:
    echo 1. Relancez ce script
    echo 2. Installez PySide6: pip install PySide6
    echo 3. Executez SOLUTION_PYTHON_FINALE.bat
    echo 4. Verifiez les erreurs ci-dessus
)

:end
echo.
echo ========================================================================
echo                              RESUME
echo ========================================================================
echo.

echo [%TIME%] Script de lancement termine

if %EXIT_CODE%==0 (
    echo.
    echo 🏆 SUCCES TOTAL !
    echo ✅ GestImmob ameliore lance et ferme normalement
    echo ✅ Modules personnalises parfaitement fonctionnels
    echo ✅ Base de donnees amelioree avec secteurs specialises
    echo.
    echo 🎯 GESTIMMOB AMELIORE EST PRET !
    echo.
    echo 📋 SECTEURS SPECIALISES DISPONIBLES:
    echo   💻 Informatique ^& Technologies
    echo      • Marque, modele, numero de serie
    echo      • Systeme d exploitation, RAM, stockage
    echo      • Processeur, carte graphique
    echo      • Garantie, licences, IP fixe
    echo.
    echo   🚗 Vehicules ^& Transport
    echo      • Immatriculation, VIN, kilometrage
    echo      • Carburant, puissance, cylindree
    echo      • Controle technique, assurance
    echo      • Conducteur, vignette Crit Air
    echo.
    echo   🪑 Mobilier ^& Amenagement
    echo      • Type, materiau, couleur
    echo      • Dimensions, poids, ergonomie
    echo      • Reglages, etat d usure
    echo      • Normes, assemblage
    echo.
    echo   ⚙️ Equipements Industriels
    echo      • Type, puissance, tension
    echo      • Capacite, pression de service
    echo      • Maintenance preventive
    echo      • Certifications, technicien responsable
    echo.
    echo 🔧 FONCTIONNALITES AVANCEES:
    echo   ✅ Criteres specifiques par secteur
    echo   ✅ Saisie guidee et adaptee
    echo   ✅ Recherche multicriteres
    echo   ✅ Rapports sectoriels
    echo   ✅ Suivi maintenance et garanties
    echo   ✅ Gestion des certifications
    echo.
) else (
    echo.
    echo ⚠️ PROBLEME DE LANCEMENT
    echo.
    echo 📋 VERIFICATIONS EFFECTUEES:
    echo   - Python: %PYTHON_CMD%
    echo   - Fichiers: Presents
    echo   - Amelioration: %AMELIORATION_EFFECTUEE%
    echo   - Dependances: Verifiees
    echo.
    echo 🔧 ACTIONS RECOMMANDEES:
    echo 1. Relancez ce script
    echo 2. Executez SOLUTION_PYTHON_FINALE.bat
    echo 3. Installez manuellement: pip install PySide6
    echo 4. Verifiez les messages d erreur
)

echo.
echo 💡 CONSEIL: Pour un acces rapide aux modules personnalises,
echo    lancez GestImmob et cliquez sur l onglet "🎯 Modules Personnalises"
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
