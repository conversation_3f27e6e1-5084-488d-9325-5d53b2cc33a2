<!-- Generated by SpecStory -->

# /fix La méthode "utcnow" dans la classe "datetime" (2025-06-08 11:06:49)

_**User**_

/fix La méthode "utcnow" dans la classe "datetime" est obsolète
  Use timezone-aware objects to represent datetimes in UTC; e.g. by calling .now(datetime.timezone.utc)

---

_**Assistant**_

### [models.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/database/models.py)

Remplacez `datetime.utcnow` par `datetime.now(timezone.utc)` pour utiliser des objets datetime avec fuseau horaire (timezone-aware). Ajoutez également l'import de `timezone`.

````python
from datetime import datetime, timezone
# ...existing code...

    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
# ...existing code...
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
# ...existing code...
    date_modif = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
# ...existing code...
    date_ajout = Column(DateTime, default=lambda: datetime.now(timezone.utc))
# ...existing code...
    date_action = Column(DateTime, default=lambda: datetime.now(timezone.utc))
# ...existing code...
    date_ajout = Column(DateTime, default=lambda: datetime.now(timezone.utc))
# ...existing code...
    date_creation = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    date_modif = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
# ...existing code...
````

---

