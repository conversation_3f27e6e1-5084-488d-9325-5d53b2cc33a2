<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:23:08)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème vient du fait que `self.table.item(row, col)` peut retourner `None`, et dans ce cas, appeler `.text()` dessus provoque une erreur. Il faut donc d'abord stocker l'item dans une variable, puis vérifier qu'il n'est pas `None` avant d'appeler `.text()`.

Corrigez la ligne concernée dans la méthode `exporter_csv` :

````python
# ...existing code...
                for row in range(self.table.rowCount()):
                    writer.writerow([
                        # Correction : éviter d'appeler .text() sur None
                        (self.table.item(row, col).text() if self.table.item(row, col) is not None else "")
                        for col in range(self.table.columnCount())
                    ])
# ...existing code...
````

---

