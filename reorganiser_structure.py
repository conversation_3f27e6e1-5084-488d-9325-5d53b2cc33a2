#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour réorganiser automatiquement la structure du projet
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, List

class ReorganisateurStructure:
    def __init__(self):
        self.actions_effectuees = []
        self.erreurs = []
        
    def creer_structure_recommandee(self):
        """Crée la structure de répertoires recommandée"""
        print("📁 CRÉATION DE LA STRUCTURE RECOMMANDÉE")
        print("=" * 50)
        
        repertoires_a_creer = [
            'src/core',
            'src/database/migrations',
            'src/auth',
            'src/views',
            'src/modules',
            'src/utils',
            'config',
            'data/backups',
            'data/exports',
            'docs',
            'tools/scripts'
        ]
        
        for repertoire in repertoires_a_creer:
            try:
                Path(repertoire).mkdir(parents=True, exist_ok=True)
                print(f"✅ Créé: {repertoire}/")
                self.actions_effectuees.append(f"Répertoire créé: {repertoire}")
            except Exception as e:
                print(f"❌ Erreur création {repertoire}: {e}")
                self.erreurs.append(f"Erreur création {repertoire}: {e}")
    
    def creer_fichiers_init(self):
        """Crée les fichiers __init__.py manquants"""
        print(f"\n📄 CRÉATION DES FICHIERS __init__.py")
        print("-" * 40)
        
        modules_python = [
            'src',
            'src/core',
            'src/database',
            'src/auth',
            'src/views',
            'src/modules',
            'src/utils'
        ]
        
        for module in modules_python:
            init_file = Path(module) / '__init__.py'
            if not init_file.exists():
                try:
                    with open(init_file, 'w', encoding='utf-8') as f:
                        f.write(f'# -*- coding: utf-8 -*-\n')
                        f.write(f'"""\n')
                        f.write(f'Module {module.replace("/", ".")}\n')
                        f.write(f'"""\n')
                    print(f"✅ Créé: {init_file}")
                    self.actions_effectuees.append(f"Fichier __init__.py créé: {init_file}")
                except Exception as e:
                    print(f"❌ Erreur création {init_file}: {e}")
                    self.erreurs.append(f"Erreur création {init_file}: {e}")
    
    def reorganiser_fichiers_existants(self):
        """Réorganise les fichiers existants selon la nouvelle structure"""
        print(f"\n🔄 RÉORGANISATION DES FICHIERS EXISTANTS")
        print("-" * 40)
        
        # Mapping des fichiers à déplacer
        deplacements = {
            # Fichiers déjà dans src/ qui doivent être déplacés dans des sous-modules
            'src/inventaire.py': 'src/modules/inventaire.py',
            'src/fournisseur.py': 'src/modules/fournisseur.py',
            'src/document.py': 'src/modules/document.py',
            'src/recherche.py': 'src/modules/recherche.py',
            'src/reforme.py': 'src/modules/reforme.py',
            
            # Fichiers de configuration
            'config.json': 'config/settings.json',
            
            # Fichiers racine vers tools/
            'setup.py': 'tools/setup.py',
            'requirements.txt': 'tools/requirements.txt'
        }
        
        for source, destination in deplacements.items():
            if os.path.exists(source):
                try:
                    # Créer le répertoire de destination si nécessaire
                    Path(destination).parent.mkdir(parents=True, exist_ok=True)
                    
                    # Déplacer le fichier
                    shutil.move(source, destination)
                    print(f"✅ Déplacé: {source} → {destination}")
                    self.actions_effectuees.append(f"Fichier déplacé: {source} → {destination}")
                except Exception as e:
                    print(f"❌ Erreur déplacement {source}: {e}")
                    self.erreurs.append(f"Erreur déplacement {source}: {e}")
            else:
                print(f"⚠️ Fichier non trouvé: {source}")
    
    def creer_fichiers_configuration(self):
        """Crée les fichiers de configuration manquants"""
        print(f"\n⚙️ CRÉATION DES FICHIERS DE CONFIGURATION")
        print("-" * 40)
        
        # Configuration principale
        config_principal = {
            "app": {
                "name": "GestImmob",
                "version": "1.0.0",
                "description": "Système de gestion d'immobilisations",
                "author": "Équipe GestImmob"
            },
            "database": {
                "type": "sqlite",
                "path": "data/users.db",
                "backup_interval": 24
            },
            "security": {
                "encryption_enabled": True,
                "session_timeout": 3600,
                "max_login_attempts": 3
            },
            "ui": {
                "theme": "default",
                "language": "fr",
                "window_size": [1200, 800]
            }
        }
        
        # Configuration base de données
        config_database = {
            "connections": {
                "default": {
                    "driver": "sqlite",
                    "database": "data/users.db",
                    "echo": False
                }
            },
            "migrations": {
                "directory": "src/database/migrations",
                "auto_upgrade": True
            }
        }
        
        # Configuration logging
        config_logging = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "standard": {
                    "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
                }
            },
            "handlers": {
                "default": {
                    "level": "INFO",
                    "formatter": "standard",
                    "class": "logging.StreamHandler"
                },
                "file": {
                    "level": "DEBUG",
                    "formatter": "standard",
                    "class": "logging.FileHandler",
                    "filename": "data/gestimmob.log"
                }
            },
            "loggers": {
                "": {
                    "handlers": ["default", "file"],
                    "level": "DEBUG",
                    "propagate": False
                }
            }
        }
        
        configs_a_creer = {
            'config/settings.json': config_principal,
            'config/database.json': config_database,
            'config/logging.json': config_logging
        }
        
        for fichier_config, contenu in configs_a_creer.items():
            try:
                Path(fichier_config).parent.mkdir(parents=True, exist_ok=True)
                with open(fichier_config, 'w', encoding='utf-8') as f:
                    json.dump(contenu, f, indent=2, ensure_ascii=False)
                print(f"✅ Créé: {fichier_config}")
                self.actions_effectuees.append(f"Configuration créée: {fichier_config}")
            except Exception as e:
                print(f"❌ Erreur création {fichier_config}: {e}")
                self.erreurs.append(f"Erreur création {fichier_config}: {e}")
    
    def creer_requirements_txt(self):
        """Crée le fichier requirements.txt avec toutes les dépendances"""
        print(f"\n📦 CRÉATION DU FICHIER REQUIREMENTS.TXT")
        print("-" * 40)
        
        dependances = [
            "# Interface graphique",
            "PySide6>=6.5.0",
            "",
            "# Base de données",
            "SQLAlchemy>=2.0.0",
            "sqlite3",
            "",
            "# Sécurité",
            "cryptography>=41.0.0",
            "",
            "# Traitement de données",
            "pandas>=2.0.0",
            "numpy>=1.24.0",
            "",
            "# Export et rapports",
            "openpyxl>=3.1.0",
            "reportlab>=4.0.0",
            "matplotlib>=3.7.0",
            "",
            "# Codes-barres et QR codes",
            "qrcode>=7.4.0",
            "python-barcode>=0.14.0",
            "Pillow>=10.0.0",
            "",
            "# Utilitaires",
            "python-dateutil>=2.8.0",
            "requests>=2.31.0"
        ]
        
        try:
            with open('requirements.txt', 'w', encoding='utf-8') as f:
                f.write('\n'.join(dependances))
            print(f"✅ Créé: requirements.txt")
            self.actions_effectuees.append("Fichier requirements.txt créé")
        except Exception as e:
            print(f"❌ Erreur création requirements.txt: {e}")
            self.erreurs.append(f"Erreur création requirements.txt: {e}")
    
    def creer_setup_py(self):
        """Crée le fichier setup.py pour l'installation"""
        print(f"\n🛠️ CRÉATION DU FICHIER SETUP.PY")
        print("-" * 40)
        
        setup_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'installation pour GestImmob
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="gestimmob",
    version="1.0.0",
    author="Équipe GestImmob",
    description="Système de gestion d'immobilisations",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "gestimmob=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.sql", "*.md"],
    },
)
'''
        
        try:
            with open('setup.py', 'w', encoding='utf-8') as f:
                f.write(setup_content)
            print(f"✅ Créé: setup.py")
            self.actions_effectuees.append("Fichier setup.py créé")
        except Exception as e:
            print(f"❌ Erreur création setup.py: {e}")
            self.erreurs.append(f"Erreur création setup.py: {e}")
    
    def creer_documentation(self):
        """Crée les fichiers de documentation"""
        print(f"\n📚 CRÉATION DE LA DOCUMENTATION")
        print("-" * 40)
        
        readme_content = '''# GestImmob - Système de Gestion d'Immobilisations

## Description
GestImmob est un système complet de gestion d'immobilisations développé en Python avec PySide6.

## Fonctionnalités
- Gestion des immobilisations
- Gestion des fournisseurs
- Gestion des documents
- Recherche avancée
- Système de réformes
- Interface d'administration
- Authentification sécurisée

## Installation

### Prérequis
- Python 3.8 ou supérieur
- SQLite 3

### Installation des dépendances
```bash
pip install -r requirements.txt
```

### Installation du logiciel
```bash
python setup.py install
```

## Utilisation
```bash
python src/main.py
```

## Structure du projet
```
gestion-software/
├── src/                    # Code source principal
│   ├── core/              # Modules core
│   ├── database/          # Gestion base de données
│   ├── auth/              # Authentification
│   ├── views/             # Interfaces utilisateur
│   ├── modules/           # Modules métier
│   └── utils/             # Utilitaires
├── config/                # Fichiers de configuration
├── data/                  # Données et base de données
├── docs/                  # Documentation
└── tools/                 # Outils et scripts
```

## Licence
MIT License
'''
        
        install_content = '''# Guide d'Installation GestImmob

## Prérequis système

### Windows
- Windows 10 ou supérieur
- Python 3.8+ (téléchargeable depuis python.org)
- SQLite 3 (inclus avec Python)

### Linux
```bash
sudo apt update
sudo apt install python3 python3-pip sqlite3
```

### macOS
```bash
brew install python3 sqlite3
```

## Installation étape par étape

1. **Cloner ou télécharger le projet**
2. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```
3. **Configurer la base de données**
   ```bash
   python src/database/init_db.py
   ```
4. **Lancer l'application**
   ```bash
   python src/main.py
   ```

## Dépannage

### Erreur PySide6
```bash
pip install --upgrade PySide6
```

### Erreur SQLAlchemy
```bash
pip install --upgrade SQLAlchemy
```
'''
        
        docs_a_creer = {
            'README.md': readme_content,
            'docs/INSTALL.md': install_content
        }
        
        for fichier_doc, contenu in docs_a_creer.items():
            try:
                Path(fichier_doc).parent.mkdir(parents=True, exist_ok=True)
                with open(fichier_doc, 'w', encoding='utf-8') as f:
                    f.write(contenu)
                print(f"✅ Créé: {fichier_doc}")
                self.actions_effectuees.append(f"Documentation créée: {fichier_doc}")
            except Exception as e:
                print(f"❌ Erreur création {fichier_doc}: {e}")
                self.erreurs.append(f"Erreur création {fichier_doc}: {e}")
    
    def generer_rapport_reorganisation(self):
        """Génère un rapport de réorganisation"""
        print(f"\n📊 RAPPORT DE RÉORGANISATION")
        print("=" * 50)
        
        print(f"✅ Actions réussies: {len(self.actions_effectuees)}")
        print(f"❌ Erreurs: {len(self.erreurs)}")
        
        if self.actions_effectuees:
            print(f"\n✅ ACTIONS EFFECTUÉES:")
            for action in self.actions_effectuees:
                print(f"  - {action}")
        
        if self.erreurs:
            print(f"\n❌ ERREURS RENCONTRÉES:")
            for erreur in self.erreurs:
                print(f"  - {erreur}")
        
        return len(self.erreurs) == 0

def main():
    """Fonction principale de réorganisation"""
    print("🔄 RÉORGANISATION AUTOMATIQUE DE LA STRUCTURE")
    print("=" * 80)

    reorganisateur = ReorganisateurStructure()

    # Exécuter toutes les étapes
    reorganisateur.creer_structure_recommandee()
    reorganisateur.creer_fichiers_init()
    reorganisateur.reorganiser_fichiers_existants()
    reorganisateur.creer_fichiers_configuration()
    reorganisateur.creer_requirements_txt()
    reorganisateur.creer_setup_py()
    reorganisateur.creer_documentation()

    # Générer le rapport
    succes = reorganisateur.generer_rapport_reorganisation()

    if succes:
        print(f"\n🎉 RÉORGANISATION TERMINÉE AVEC SUCCÈS !")
        print(f"✅ Structure du projet optimisée")
        print(f"✅ Fichiers de configuration créés")
        print(f"✅ Documentation générée")
        print(f"\n🔄 PROCHAINES ÉTAPES:")
        print(f"1. Exécuter: python gerer_fichiers_doubles.py")
        print(f"2. Installer dépendances: pip install -r requirements.txt")
        print(f"3. Lancer l'application: python src/main.py")
    else:
        print(f"\n⚠️ RÉORGANISATION TERMINÉE AVEC DES ERREURS")
        print(f"Consultez le rapport ci-dessus pour plus de détails")

    return succes

if __name__ == "__main__":
    main()
