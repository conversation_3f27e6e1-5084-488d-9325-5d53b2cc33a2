<!-- Generated by SpecStory -->

# /fix Arguments de type attendus pour la classe gén (2025-06-08 03:10:01)

_**User**_

/fix Arguments de type attendus pour la classe générique "dict"

---

_**Assistant**_

### [authentication.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/authentication.py)

Le problème vient de l'utilisation de `dict` sans spécifier les types de clés et de valeurs attendus. Pour corriger cela, il faut préciser les types dans le dictionnaire de sessions. Ici, chaque session est un dictionnaire avec des clés de type `str` et des valeurs de type `Any`.

````python
# ...existing code...
from typing import Any, Dict

class Authentication:
    def __init__(self, user_manager: Any):
        self.user_manager = user_manager
        self.sessions: Dict[str, Dict[str, Any]] = {}
# ...existing code...
````

---

