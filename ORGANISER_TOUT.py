#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script principal pour organiser et structurer complètement le projet GestImmob
"""

import os
import sys
import subprocess
from pathlib import Path

class OrganisateurComplet:
    def __init__(self):
        self.etapes_completees = []
        self.etapes_echouees = []
        
    def executer_script(self, script_name: str, description: str) -> bool:
        """Exécute un script et retourne le succès"""
        print(f"\n{'='*80}")
        print(f"ÉTAPE: {description}")
        print(f"Script: {script_name}")
        print(f"{'='*80}")
        
        if not os.path.exists(script_name):
            print(f"❌ Script non trouvé: {script_name}")
            self.etapes_echouees.append(f"{description} - Script manquant")
            return False
        
        try:
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=False, text=True, timeout=600)
            
            if result.returncode == 0:
                print(f"\n✅ {description}: RÉUSSI")
                self.etapes_completees.append(description)
                return True
            else:
                print(f"\n❌ {description}: ÉCHEC (code {result.returncode})")
                self.etapes_echouees.append(f"{description} - Code {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"\n⏱️ {description}: TIMEOUT")
            self.etapes_echouees.append(f"{description} - Timeout")
            return False
        except Exception as e:
            print(f"\n❌ {description}: ERREUR {e}")
            self.etapes_echouees.append(f"{description} - {e}")
            return False
    
    def demander_confirmation(self, message: str) -> bool:
        """Demande confirmation à l'utilisateur"""
        while True:
            reponse = input(f"\n{message} (o/n): ").lower().strip()
            if reponse in ['o', 'oui', 'y', 'yes']:
                return True
            elif reponse in ['n', 'non', 'no']:
                return False
            else:
                print("Répondez par 'o' (oui) ou 'n' (non)")
    
    def afficher_introduction(self):
        """Affiche l'introduction du script"""
        print("🚀 ORGANISATION COMPLÈTE DU PROJET GESTIMMOB")
        print("=" * 80)
        print("Ce script va complètement organiser et structurer votre projet :")
        print()
        print("📋 ÉTAPES PRÉVUES:")
        print("1. 🔍 Analyse complète de la structure actuelle")
        print("2. 🔄 Réorganisation automatique des fichiers")
        print("3. 🗑️ Gestion des fichiers en double (avec confirmation)")
        print("4. 📦 Installation des dépendances et outils externes")
        print("5. 🔧 Correction de tous les codes Python")
        print("6. 🗄️ Correction du fichier SQL")
        print("7. ✅ Validation finale et lancement")
        print()
        print("⚠️ IMPORTANT:")
        print("- Des sauvegardes seront créées avant toute modification")
        print("- Vous serez consulté avant toute suppression")
        print("- Le processus peut prendre plusieurs minutes")
        print()
    
    def organiser_projet_complet(self):
        """Organise complètement le projet"""
        self.afficher_introduction()
        
        if not self.demander_confirmation("🚀 Commencer l'organisation complète du projet ?"):
            print("❌ Organisation annulée par l'utilisateur")
            return False
        
        # ÉTAPE 1: Analyse de la structure
        print(f"\n🔍 ÉTAPE 1/7: ANALYSE DE LA STRUCTURE")
        if not self.executer_script('analyser_structure_complete.py', 
                                   'Analyse complète de la structure'):
            print("⚠️ Analyse échouée, mais on continue...")
        
        # ÉTAPE 2: Réorganisation
        print(f"\n🔄 ÉTAPE 2/7: RÉORGANISATION")
        if self.demander_confirmation("Réorganiser automatiquement la structure du projet ?"):
            self.executer_script('reorganiser_structure.py', 
                               'Réorganisation automatique de la structure')
        else:
            print("⏭️ Réorganisation ignorée")
        
        # ÉTAPE 3: Gestion des fichiers en double
        print(f"\n🗑️ ÉTAPE 3/7: FICHIERS EN DOUBLE")
        if self.demander_confirmation("Identifier et gérer les fichiers en double ?"):
            self.executer_script('gerer_fichiers_doubles.py', 
                               'Gestion des fichiers en double')
        else:
            print("⏭️ Gestion des doublons ignorée")
        
        # ÉTAPE 4: Installation des dépendances
        print(f"\n📦 ÉTAPE 4/7: DÉPENDANCES")
        if self.demander_confirmation("Installer automatiquement toutes les dépendances ?"):
            self.executer_script('installer_dependances.py', 
                               'Installation des dépendances et outils')
        else:
            print("⏭️ Installation des dépendances ignorée")
        
        # ÉTAPE 5: Correction des codes Python
        print(f"\n🔧 ÉTAPE 5/7: CORRECTION PYTHON")
        if self.demander_confirmation("Corriger automatiquement tous les codes Python ?"):
            if os.path.exists('corriger_tous_problemes.py'):
                self.executer_script('corriger_tous_problemes.py', 
                                   'Correction de tous les codes Python')
            elif os.path.exists('VERIFIER_ET_CORRIGER_TOUT.bat'):
                print("Exécution du script batch de correction...")
                try:
                    os.system('VERIFIER_ET_CORRIGER_TOUT.bat')
                    self.etapes_completees.append('Correction des codes Python')
                except:
                    self.etapes_echouees.append('Correction des codes Python')
            else:
                print("⚠️ Scripts de correction non trouvés")
        else:
            print("⏭️ Correction Python ignorée")
        
        # ÉTAPE 6: Correction SQL
        print(f"\n🗄️ ÉTAPE 6/7: CORRECTION SQL")
        if self.demander_confirmation("Corriger le fichier SQL (PostgreSQL → SQLite) ?"):
            if os.path.exists('fix_sql_complete.py'):
                self.executer_script('fix_sql_complete.py', 
                                   'Correction du fichier SQL')
            else:
                print("⚠️ Script de correction SQL non trouvé")
        else:
            print("⏭️ Correction SQL ignorée")
        
        # ÉTAPE 7: Validation finale
        print(f"\n✅ ÉTAPE 7/7: VALIDATION FINALE")
        self.validation_finale()
        
        return True
    
    def validation_finale(self):
        """Effectue la validation finale du projet"""
        print("🔍 VALIDATION FINALE DU PROJET")
        print("-" * 50)
        
        validations = []
        
        # Vérifier la structure
        if os.path.exists('src/main.py'):
            validations.append("✅ Fichier principal présent")
        else:
            validations.append("❌ Fichier principal manquant")
        
        # Vérifier les dépendances critiques
        try:
            import PySide6
            validations.append("✅ PySide6 installé")
        except ImportError:
            validations.append("❌ PySide6 manquant")
        
        try:
            import sqlalchemy
            validations.append("✅ SQLAlchemy installé")
        except ImportError:
            validations.append("❌ SQLAlchemy manquant")
        
        # Vérifier la syntaxe Python
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            compile(content, 'src/main.py', 'exec')
            validations.append("✅ Syntaxe main.py correcte")
        except:
            validations.append("❌ Erreur syntaxe main.py")
        
        # Vérifier le fichier SQL
        if os.path.exists('src/database/migrations/init.sql'):
            with open('src/database/migrations/init.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            if 'SERIAL PRIMARY KEY' not in sql_content:
                validations.append("✅ Fichier SQL compatible SQLite")
            else:
                validations.append("❌ Fichier SQL contient encore SERIAL")
        else:
            validations.append("⚠️ Fichier SQL non trouvé")
        
        # Afficher les résultats
        for validation in validations:
            print(f"  {validation}")
        
        # Test de lancement
        if self.demander_confirmation("Tester le lancement de l'application ?"):
            self.tester_lancement()
    
    def tester_lancement(self):
        """Teste le lancement de l'application"""
        print(f"\n🚀 TEST DE LANCEMENT")
        print("-" * 30)
        
        if os.path.exists('src/main.py'):
            print("Tentative de lancement de GestImmob...")
            try:
                # Lancer l'application en arrière-plan
                subprocess.Popen([sys.executable, 'src/main.py'])
                print("✅ Application lancée avec succès !")
                print("   L'application devrait s'ouvrir dans quelques secondes")
            except Exception as e:
                print(f"❌ Erreur lancement: {e}")
        else:
            print("❌ Fichier main.py non trouvé")
    
    def generer_rapport_final(self):
        """Génère le rapport final d'organisation"""
        print(f"\n📊 RAPPORT FINAL D'ORGANISATION")
        print("=" * 80)
        
        print(f"✅ Étapes réussies: {len(self.etapes_completees)}")
        print(f"❌ Étapes échouées: {len(self.etapes_echouees)}")
        
        if self.etapes_completees:
            print(f"\n✅ ÉTAPES RÉUSSIES:")
            for etape in self.etapes_completees:
                print(f"  - {etape}")
        
        if self.etapes_echouees:
            print(f"\n❌ ÉTAPES ÉCHOUÉES:")
            for etape in self.etapes_echouees:
                print(f"  - {etape}")
        
        taux_succes = len(self.etapes_completees) / (len(self.etapes_completees) + len(self.etapes_echouees)) * 100 if (self.etapes_completees or self.etapes_echouees) else 100
        
        print(f"\n📈 TAUX DE RÉUSSITE: {taux_succes:.1f}%")
        
        if taux_succes >= 80:
            print(f"\n🎉 ORGANISATION RÉUSSIE !")
            print(f"✅ Le projet GestImmob est maintenant complètement organisé")
            print(f"✅ Structure optimisée, dépendances installées, codes corrigés")
            print(f"✅ L'application est prête à être utilisée")
            
            print(f"\n🚀 COMMANDES UTILES:")
            print(f"  python src/main.py                    # Lancer l'application")
            print(f"  python installer_dependances.py      # Réinstaller les dépendances")
            print(f"  python gerer_fichiers_doubles.py     # Nettoyer les doublons")
            
        else:
            print(f"\n⚠️ ORGANISATION PARTIELLE")
            print(f"Consultez les étapes échouées ci-dessus")
            print(f"Vous pouvez relancer les scripts individuels si nécessaire")
        
        return taux_succes >= 80

def main():
    """Fonction principale"""
    organisateur = OrganisateurComplet()
    
    try:
        organisateur.organiser_projet_complet()
        succes = organisateur.generer_rapport_final()
        
        print(f"\n{'='*80}")
        if succes:
            print("🏆 ORGANISATION COMPLÈTE TERMINÉE AVEC SUCCÈS !")
        else:
            print("⚠️ ORGANISATION TERMINÉE AVEC DES PROBLÈMES")
        print(f"{'='*80}")
        
        return succes
        
    except KeyboardInterrupt:
        print(f"\n❌ Organisation interrompue par l'utilisateur")
        return False
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        return False

if __name__ == "__main__":
    main()
