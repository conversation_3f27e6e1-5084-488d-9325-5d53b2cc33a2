{"timestamp": "2025-06-09 00:16:33", "summary": {"total_tests": 56, "tests_passed": 55, "tests_failed": 1, "success_rate": 98.21}, "detailed_results": {"syntax": {"total_files": 32, "valid_files": 32, "errors": []}, "imports": {"sys": "OK", "os": "OK", "pathlib": "OK", "json": "OK", "sqlite3": "OK", "datetime": "OK", "logging": "OK", "time": "OK", "hashlib": "OK", "uuid": "OK", "base64": "OK", "PySide6": "OK"}, "modules": {"sync_module.py": "OK - Importable", "validation_module.py": "OK - Importable", "performance_module.py": "OK - Importable", "security_module.py": "ERREUR IMPORT: cannot import name 'str' from 'typing' (C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py)"}, "synchronization": {"python_sync": "OK", "scripts/sync.ps1": "OK - <PERSON><PERSON><PERSON>", "web/js/sync.js": "OK - <PERSON><PERSON><PERSON>", "sql/sync_schema.sql": "OK - <PERSON><PERSON><PERSON>"}, "main_application": {"enterprise_import": "OK", "build_system": "OK"}, "multilanguage_scripts": {"powershell": "ATTENTION - Syntaxe incomplète", "javascript": "ATTENTION - Syntaxe incomplète"}, "database": {"sqlite": "OK - Fonctionnel"}, "performance": {"monitoring": "OK - 2.01s"}}}