#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de lancement simple pour GestImmob
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def verifier_python():
    """Vérifie que Python est correctement configuré"""
    print("🐍 Vérification Python...")
    print(f"Version Python: {sys.version}")
    print(f"Exécutable: {sys.executable}")
    return True

def verifier_dependances():
    """Vérifie et installe les dépendances nécessaires"""
    print("\n📦 Vérification des dépendances...")
    
    dependances = {
        'PySide6': 'PySide6',
        'sqlalchemy': 'SQLAlchemy'
    }
    
    dependances_manquantes = []
    
    for module, package in dependances.items():
        try:
            importlib.import_module(module)
            print(f"✅ {package}: OK")
        except ImportError:
            print(f"❌ {package}: Manquant")
            dependances_manquantes.append(package)
    
    if dependances_manquantes:
        print(f"\n📥 Installation des dépendances manquantes...")
        for package in dependances_manquantes:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                             check=True, capture_output=True)
                print(f"✅ {package} installé avec succès")
            except subprocess.CalledProcessError as e:
                print(f"❌ Échec installation {package}: {e}")
                return False
    
    return True

def trouver_fichier_principal():
    """Trouve le fichier principal de GestImmob"""
    print("\n📁 Recherche du fichier principal...")
    
    # Ordre de priorité des fichiers
    fichiers_possibles = [
        'src/main.py',
        'gestimmob_final.py',
        'gestimmob_complet_final.py',
        'LANCER_GESTIMMOB_FINAL_V5.py',
        'gestimmob_enterprise_v5.py',
        'gestimmob_simple.py'
    ]
    
    for fichier in fichiers_possibles:
        if os.path.exists(fichier):
            print(f"✅ Fichier trouvé: {fichier}")
            return fichier
    
    print("❌ Aucun fichier principal trouvé")
    print("📋 Fichiers recherchés:")
    for fichier in fichiers_possibles:
        print(f"  - {fichier}")
    
    return None

def preparer_environnement():
    """Prépare l'environnement d'exécution"""
    print("\n🔧 Préparation de l'environnement...")
    
    # Créer les répertoires nécessaires
    repertoires = ['data', 'logs', 'backups', 'temp']
    
    for repertoire in repertoires:
        if not os.path.exists(repertoire):
            os.makedirs(repertoire)
            print(f"✅ Répertoire créé: {repertoire}")
        else:
            print(f"✅ Répertoire existant: {repertoire}")

def tester_syntaxe(fichier):
    """Teste la syntaxe du fichier Python"""
    print(f"\n🧪 Test de syntaxe: {fichier}")
    
    try:
        with open(fichier, 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, fichier, 'exec')
        print("✅ Syntaxe correcte")
        return True
    
    except SyntaxError as e:
        print(f"❌ Erreur de syntaxe: {e}")
        return False
    except Exception as e:
        print(f"⚠️ Erreur lors du test: {e}")
        return False

def lancer_application(fichier):
    """Lance l'application GestImmob"""
    print(f"\n🚀 Lancement de GestImmob...")
    print(f"Fichier: {fichier}")
    print(f"Python: {sys.executable}")
    print()
    
    try:
        # Lancer l'application
        result = subprocess.run([sys.executable, fichier], 
                              cwd=os.getcwd())
        
        if result.returncode == 0:
            print("\n✅ GestImmob fermé normalement")
            return True
        else:
            print(f"\n❌ GestImmob fermé avec erreur (code: {result.returncode})")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ Interruption par l'utilisateur")
        return True
    except Exception as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        return False

def diagnostic_rapide():
    """Effectue un diagnostic rapide en cas de problème"""
    print("\n🔍 Diagnostic rapide...")
    
    # Test Python de base
    try:
        import sys
        print(f"✅ Python de base: OK (version {sys.version_info.major}.{sys.version_info.minor})")
    except:
        print("❌ Problème Python de base")
    
    # Test PySide6
    try:
        import PySide6
        print("✅ PySide6: OK")
    except ImportError:
        print("❌ PySide6: Manquant")
    
    # Test SQLAlchemy
    try:
        import sqlalchemy
        print("✅ SQLAlchemy: OK")
    except ImportError:
        print("❌ SQLAlchemy: Manquant")

def main():
    """Fonction principale"""
    print("=" * 70)
    print("                    LANCEMENT GESTIMMOB")
    print("                 Script Python Simple")
    print("=" * 70)
    
    try:
        # Étape 1: Vérifier Python
        if not verifier_python():
            print("❌ Problème avec Python")
            return False
        
        # Étape 2: Vérifier les dépendances
        if not verifier_dependances():
            print("❌ Problème avec les dépendances")
            diagnostic_rapide()
            return False
        
        # Étape 3: Trouver le fichier principal
        fichier_principal = trouver_fichier_principal()
        if not fichier_principal:
            print("❌ Fichier principal non trouvé")
            return False
        
        # Étape 4: Préparer l'environnement
        preparer_environnement()
        
        # Étape 5: Tester la syntaxe
        if not tester_syntaxe(fichier_principal):
            print("⚠️ Problème de syntaxe détecté, mais on continue...")
        
        # Étape 6: Lancer l'application
        succes = lancer_application(fichier_principal)
        
        # Résumé final
        print("\n" + "=" * 70)
        print("                        RÉSUMÉ FINAL")
        print("=" * 70)
        
        if succes:
            print("🎉 LANCEMENT RÉUSSI !")
            print("✅ GestImmob a été lancé et fermé normalement")
            print(f"✅ Configuration: Python + {fichier_principal}")
        else:
            print("⚠️ PROBLÈME DE LANCEMENT")
            print("📋 Actions recommandées:")
            print("1. Vérifiez les messages d'erreur ci-dessus")
            print("2. Installez les dépendances manquantes")
            print("3. Exécutez SOLUTION_PYTHON_FINALE.bat")
            print("4. Relancez ce script")
            diagnostic_rapide()
        
        return succes
        
    except KeyboardInterrupt:
        print("\n❌ Script interrompu par l'utilisateur")
        return False
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        diagnostic_rapide()
        return False

if __name__ == "__main__":
    main()
    
    # Pause pour voir les résultats
    try:
        input("\nAppuyez sur Entrée pour fermer...")
    except:
        pass
