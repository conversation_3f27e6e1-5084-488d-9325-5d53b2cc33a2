<!-- Generated by SpecStory -->

# /fix Le type de « _print_table » est partiellement (2025-06-08 11:20:02)

_**User**_

/fix Le type de « _print_table » est partiellement inconnu
  Le type de "_print_table" est "(printer: Unknown) -> None"

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème de type partiellement inconnu pour `_print_table` vient du fait que le paramètre `printer` n'est pas typé explicitement. Il suffit d'ajouter une annotation de type pour clarifier que `printer` est de type `QPrinter`.

Voici la correction à apporter :

````python
# ...existing code...
    def _print_table(self, printer: 'QPrinter'):
        from PySide6.QtGui import QTextDocument
        # ...existing code...
# ...existing code...
````

Astuce : il est préférable d'importer `QPrinter` en haut du fichier pour éviter l'importation dynamique, mais l'annotation de type suffit pour lever l'avertissement de type.

---

