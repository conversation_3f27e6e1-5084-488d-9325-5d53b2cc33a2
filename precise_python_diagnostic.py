#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DIAGNOSTIC PRÉCIS DE TOUS LES PROBLÈMES PYTHON
Identifie exactement où sont les erreurs
"""

import ast
import os
import sys
import traceback
from pathlib import Path
from typing import List, Dict, Any, <PERSON><PERSON>

def test_file_compilation(file_path: Path) -> Tuple[bool, str, List[str]]:
    """Teste la compilation d'un fichier Python avec détails précis"""
    errors = []
    warnings = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Compilation basique
        try:
            compile(content, str(file_path), 'exec')
        except SyntaxError as e:
            error_msg = f"ERREUR SYNTAXE ligne {e.lineno}: {e.msg}"
            if e.text:
                error_msg += f"\n    Code: {e.text.strip()}"
            errors.append(error_msg)
            return False, error_msg, errors
        except Exception as e:
            error_msg = f"ERREUR COMPILATION: {e}"
            errors.append(error_msg)
            return False, error_msg, errors
        
        # Test 2: AST parsing
        try:
            tree = ast.parse(content)
        except SyntaxError as e:
            error_msg = f"ERREUR AST ligne {e.lineno}: {e.msg}"
            errors.append(error_msg)
            return False, error_msg, errors
        
        # Test 3: Analyse des noms non définis
        undefined_names = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                # Ignorer les built-ins et mots-clés Python
                if node.id not in ['self', 'super', 'len', 'str', 'int', 'float', 'bool', 
                                  'list', 'dict', 'tuple', 'set', 'print', 'range', 'enumerate',
                                  'zip', 'map', 'filter', 'any', 'all', 'min', 'max', 'sum',
                                  'open', 'type', 'isinstance', 'hasattr', 'getattr', 'setattr',
                                  'True', 'False', 'None', '__name__', '__file__', '__main__']:
                    # Vérifier si c'est défini dans le fichier
                    if node.id not in content and node.id not in ['QApplication', 'QMainWindow', 'Qt']:
                        undefined_names.append(node.id)
        
        if undefined_names:
            unique_undefined = list(set(undefined_names))
            if len(unique_undefined) > 5:  # Seulement si beaucoup de noms non définis
                warnings.append(f"Noms potentiellement non définis: {', '.join(unique_undefined[:10])}")
        
        return True, "OK", warnings
        
    except Exception as e:
        error_msg = f"ERREUR LECTURE: {e}"
        errors.append(error_msg)
        return False, error_msg, errors

def analyze_specific_issues(file_path: Path) -> List[str]:
    """Analyse des problèmes spécifiques connus"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.splitlines()
        
        # Vérifier les problèmes spécifiques
        for i, line in enumerate(lines, 1):
            # Problème 1: 'any' au lieu de 'Any'
            if ': any =' in line:
                issues.append(f"Ligne {i}: 'any' devrait être 'Any'")
            
            # Problème 2: Fonctions manquantes appelées
            if 'encrypt_password(' in line and 'def encrypt_password' not in content:
                issues.append(f"Ligne {i}: Fonction 'encrypt_password' appelée mais non définie")
            
            if 'get_db_session(' in line and 'def get_db_session' not in content:
                issues.append(f"Ligne {i}: Fonction 'get_db_session' appelée mais non définie")
            
            # Problème 3: Imports manquants
            if 'from typing import' not in content and ': ' in line and ' = ' in line:
                if 'typing_missing' not in [issue.split(':')[1].strip() for issue in issues]:
                    issues.append(f"Import typing manquant pour les annotations de type")
                    break
            
            # Problème 4: Variables non typées importantes
            if ' = ' in line and ':' not in line.split('=')[0]:
                if any(x in line for x in ['cursor', 'connection', 'session', 'config']):
                    var_match = line.split('=')[0].strip()
                    if not any(x in line for x in ['def ', 'class ', 'import', '#']):
                        issues.append(f"Ligne {i}: Variable importante '{var_match}' non typée")
        
        return issues
        
    except Exception as e:
        return [f"Erreur analyse: {e}"]

def main():
    """Diagnostic précis de tous les fichiers Python"""
    print("🔍 DIAGNOSTIC PRÉCIS DE TOUS LES PROBLÈMES PYTHON")
    print("=" * 70)
    
    # Trouver tous les fichiers Python
    python_files = []
    for root, dirs, files in os.walk('.'):
        # Ignorer certains répertoires
        dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.vscode']
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    print(f"📁 Fichiers Python trouvés: {len(python_files)}")
    print()
    
    total_errors = 0
    total_warnings = 0
    critical_files = []
    
    # Analyser chaque fichier
    for file_path in sorted(python_files):
        print(f"🔍 Analyse de {file_path}...")
        
        # Test de compilation
        success, message, warnings = test_file_compilation(file_path)
        
        if success:
            print(f"✅ {file_path.name}: Compilation OK")
            if warnings:
                print(f"⚠️ {file_path.name}: {len(warnings)} avertissements")
                for warning in warnings:
                    print(f"    {warning}")
                total_warnings += len(warnings)
        else:
            print(f"❌ {file_path.name}: {message}")
            total_errors += 1
            critical_files.append(str(file_path))
        
        # Analyse des problèmes spécifiques
        specific_issues = analyze_specific_issues(file_path)
        if specific_issues:
            print(f"🔧 {file_path.name}: {len(specific_issues)} problèmes spécifiques")
            for issue in specific_issues:
                print(f"    {issue}")
        
        print()
    
    # Résumé final
    print("=" * 70)
    print("📊 RÉSUMÉ DU DIAGNOSTIC PRÉCIS")
    print("=" * 70)
    
    success_rate = ((len(python_files) - total_errors) / len(python_files)) * 100 if python_files else 0
    
    print(f"✅ Fichiers sans erreur: {len(python_files) - total_errors}/{len(python_files)}")
    print(f"❌ Fichiers avec erreurs: {total_errors}")
    print(f"⚠️ Total avertissements: {total_warnings}")
    print(f"📈 Taux de réussite: {success_rate:.1f}%")
    
    if critical_files:
        print(f"\n🚨 FICHIERS CRITIQUES AVEC ERREURS:")
        for file_path in critical_files:
            print(f"  - {file_path}")
    
    # Tests spécifiques des fichiers principaux
    print(f"\n🎯 TESTS SPÉCIFIQUES DES FICHIERS PRINCIPAUX:")
    
    main_files = [
        'src/main.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py',
        'src/auth/user_management.py'
    ]
    
    for file_path in main_files:
        if Path(file_path).exists():
            success, message, warnings = test_file_compilation(Path(file_path))
            status = "✅" if success else "❌"
            print(f"  {status} {file_path}: {message}")
            
            if not success:
                print(f"    🚨 PROBLÈME CRITIQUE dans {file_path}")
                
                # Diagnostic approfondi pour les fichiers critiques
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Essayer de trouver la ligne exacte du problème
                    lines = content.splitlines()
                    for i, line in enumerate(lines, 1):
                        try:
                            compile(line, f"{file_path}_line_{i}", 'exec')
                        except SyntaxError as e:
                            print(f"      Ligne {i} problématique: {line.strip()}")
                            print(f"      Erreur: {e.msg}")
                            break
                except Exception as e:
                    print(f"      Erreur diagnostic approfondi: {e}")
        else:
            print(f"  ❌ {file_path}: FICHIER MANQUANT")
    
    if total_errors == 0:
        print(f"\n🎉 TOUS LES FICHIERS PYTHON SONT PARFAITS !")
        return True
    else:
        print(f"\n🚨 {total_errors} FICHIERS NÉCESSITENT DES CORRECTIONS URGENTES")
        return False

if __name__ == "__main__":
    main()
