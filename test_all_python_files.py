#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de tous les fichiers Python pour identifier les problèmes restants
"""

import ast
import os
import sys
import traceback
from pathlib import Path

def test_python_file(file_path):
    """Test complet d'un fichier Python"""
    print(f"\n{'='*60}")
    print(f"TEST: {file_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier non trouvé: {file_path}")
        return False, [f"Fichier non trouvé"]
    
    errors = []
    
    try:
        # Lecture du fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier: {len(content)} caractères, {len(content.splitlines())} lignes")
        
        if not content.strip():
            print(f"❌ Fichier vide")
            return False, ["Fichier vide"]
        
        # Test AST
        print(f"\n🔍 Test AST:")
        try:
            ast.parse(content, filename=file_path)
            print(f"✅ AST: OK")
        except SyntaxError as e:
            error = f"AST ligne {e.lineno}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            
            # Contexte de l'erreur
            lines = content.splitlines()
            if e.lineno and e.lineno <= len(lines):
                print(f"📍 Contexte:")
                start = max(0, e.lineno - 2)
                end = min(len(lines), e.lineno + 2)
                for i in range(start, end):
                    marker = " >>> " if i == e.lineno - 1 else "     "
                    print(f"{marker}{i+1:3d}: {lines[i]}")
        except Exception as e:
            error = f"AST erreur: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
        
        # Test compilation
        print(f"\n🔍 Test Compilation:")
        try:
            compile(content, file_path, 'exec')
            print(f"✅ Compilation: OK")
        except SyntaxError as e:
            error = f"Compilation ligne {e.lineno}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
        except Exception as e:
            error = f"Compilation erreur: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
        
        # Test imports
        print(f"\n🔍 Test Imports:")
        try:
            # Simuler l'import (sans exécuter)
            tree = ast.parse(content)
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        imports.append(f"{module}.{alias.name}")
            
            print(f"✅ Imports détectés: {len(imports)}")
            if imports:
                for imp in imports[:5]:  # Limiter l'affichage
                    print(f"   - {imp}")
                if len(imports) > 5:
                    print(f"   ... et {len(imports) - 5} autres")
        except Exception as e:
            error = f"Analyse imports: {e}"
            print(f"⚠️ {error}")
        
        # Résultat
        if errors:
            print(f"\n❌ FICHIER AVEC ERREURS: {len(errors)} problème(s)")
            return False, errors
        else:
            print(f"\n✅ FICHIER PARFAIT")
            return True, []
            
    except Exception as e:
        error = f"Erreur lecture: {e}"
        print(f"❌ {error}")
        return False, [error]

def main():
    """Test de tous les fichiers Python"""
    print("🔍 TEST DE TOUS LES FICHIERS PYTHON")
    print("=" * 80)
    
    # Liste des fichiers critiques à tester
    critical_files = [
        'src/main.py',
        'src/config.py',
        'src/database/db_connection.py',
        'src/database/models.py',
        'src/views/admin_view.py',
        'src/auth/user_management.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py',
        'src/utils/config.py'
    ]
    
    results = {}
    total_errors = []
    
    # Test des fichiers critiques
    print(f"\n📋 TEST DES FICHIERS CRITIQUES")
    print("-" * 50)
    
    for file_path in critical_files:
        is_valid, errors = test_python_file(file_path)
        results[file_path] = {
            'valid': is_valid,
            'errors': errors,
            'critical': True
        }
        if errors:
            total_errors.extend([f"{file_path}: {error}" for error in errors])
    
    # Test des autres fichiers Python
    print(f"\n📋 TEST DES AUTRES FICHIERS PYTHON")
    print("-" * 50)
    
    other_files = []
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if not d.startswith('.')]
        for file in files:
            if file.endswith('.py'):
                file_path = str(Path(root) / file)
                if file_path not in critical_files and not file_path.startswith('./'):
                    other_files.append(file_path)
    
    print(f"📁 Autres fichiers Python trouvés: {len(other_files)}")
    
    for file_path in other_files[:10]:  # Limiter à 10 fichiers
        is_valid, errors = test_python_file(file_path)
        results[file_path] = {
            'valid': is_valid,
            'errors': errors,
            'critical': False
        }
        if errors:
            total_errors.extend([f"{file_path}: {error}" for error in errors])
    
    # Résumé final
    print(f"\n{'='*80}")
    print("📊 RÉSUMÉ FINAL")
    print(f"{'='*80}")
    
    critical_valid = sum(1 for file_path in critical_files if results.get(file_path, {}).get('valid', False))
    critical_total = len(critical_files)
    
    other_valid = sum(1 for file_path, result in results.items() if not result.get('critical', True) and result.get('valid', False))
    other_total = len([f for f in results.keys() if not results[f].get('critical', True)])
    
    total_valid = critical_valid + other_valid
    total_files = critical_total + other_total
    
    print(f"📁 Fichiers testés: {total_files}")
    print(f"✅ Fichiers valides: {total_valid}")
    print(f"❌ Fichiers avec erreurs: {total_files - total_valid}")
    print(f"🚨 Total erreurs: {len(total_errors)}")
    
    print(f"\n📊 FICHIERS CRITIQUES:")
    print(f"   ✅ Valides: {critical_valid}/{critical_total}")
    if critical_valid < critical_total:
        print(f"   ❌ Problématiques: {critical_total - critical_valid}")
    
    if other_total > 0:
        print(f"\n📊 AUTRES FICHIERS:")
        print(f"   ✅ Valides: {other_valid}/{other_total}")
        if other_valid < other_total:
            print(f"   ❌ Problématiques: {other_total - other_valid}")
    
    # Détail des erreurs
    if total_errors:
        print(f"\n🚨 ERREURS DÉTECTÉES:")
        for i, error in enumerate(total_errors, 1):
            print(f"  {i:2d}. {error}")
    
    # Détail par fichier critique
    print(f"\n📋 DÉTAIL FICHIERS CRITIQUES:")
    for file_path in critical_files:
        result = results.get(file_path, {'valid': False, 'errors': ['Non testé']})
        status = "✅ VALIDE" if result['valid'] else "❌ ERREURS"
        error_count = len(result['errors'])
        print(f"  {status:12} {file_path} ({error_count} erreur(s))")
    
    # Conclusion
    if critical_valid == critical_total:
        print(f"\n🎉 TOUS LES FICHIERS CRITIQUES SONT PARFAITS!")
        print(f"✅ L'application peut être lancée")
        return True
    else:
        print(f"\n🚨 {critical_total - critical_valid} FICHIER(S) CRITIQUE(S) AVEC ERREURS")
        print(f"❌ Corrections nécessaires avant le lancement")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat: {'SUCCÈS' if success else 'ÉCHEC'}")
    sys.exit(0 if success else 1)
