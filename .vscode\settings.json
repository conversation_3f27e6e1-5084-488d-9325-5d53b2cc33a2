{"robotIA.user": "SamNord@110577", "robotIA.password": "<PERSON><PERSON><PERSON>@22", "robotIA.autoStart": true, "robotIA.theme": "dark", "python.defaultInterpreterPath": "python", "files.associations": {"*.py": "python"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}, "remote.autoForwardPorts": false, "remote.downloadExtensionsLocally": true, "workbench.enableExperiments": false, "extensions.ignoreRecommendations": true, "workbench.colorTheme": "Dark+ (default dark)", "editor.fontSize": 14, "terminal.integrated.fontSize": 12, "workbench.activityBar.visible": true, "workbench.sideBar.location": "left"}