#!/usr/bin/env python3
"""
GestImmob - Version Simplifiée Fonctionnelle
Logiciel de gestion immobilière avec interface moderne
"""

import sys
import os
import json
import sqlite3
import hashlib
from pathlib import Path
from datetime import datetime

# Imports PySide6
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QTextEdit, QMessageBox, QTabWidget, QFormLayout, QGroupBox,
    QMenuBar, QStatusBar, QSplitter, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPixmap, QAction, QGuiApplication
from cryptography.fernet import Fernet

class GestImmobApp(QMainWindow):
    """Application principale GestImmob"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("GestImmob v2.0.0 - Gestion Immobilière Professionnelle")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialiser la base de données
        self.init_database()
        
        # Appliquer le thème moderne
        self.apply_modern_theme()
        
        # Créer l'interface
        self.setup_ui()
        
        # Charger les données
        self.load_data()
        
        # Barre de statut
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("GestImmob v2.0.0 - Prêt")
        
    def init_database(self):
        """Initialise la base de données SQLite"""
        self.db_path = "gestimmob.db"
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # Table des biens immobiliers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS immobilisations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                valeur REAL NOT NULL,
                annee INTEGER NOT NULL,
                localisation TEXT,
                secteur TEXT,
                observation TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'gestionnaire',
                full_name TEXT,
                email TEXT,
                last_login TIMESTAMP,
                is_active INTEGER DEFAULT 1
            )
        ''')
        
        # Créer un utilisateur admin par défaut
        cursor.execute("SELECT COUNT(*) FROM users WHERE role='admin'")
        if cursor.fetchone()[0] == 0:
            admin_password = hashlib.sha256("Admin@1234".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ("admin", admin_password, "admin", "Administrateur"))
        
        self.conn.commit()
        
    def apply_modern_theme(self):
        """Applique un thème moderne à l'application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                color: #212529;
            }
            
            QWidget {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 10pt;
            }
            
            QLabel#title {
                color: #1976d2;
                font-size: 24pt;
                font-weight: bold;
                margin: 10px;
            }
            
            QLabel#subtitle {
                color: #6c757d;
                font-size: 12pt;
                margin-bottom: 20px;
            }
            
            QPushButton {
                background-color: #1976d2;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #1565c0;
            }
            
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #9e9e9e;
            }
            
            QLineEdit, QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                selection-background-color: #bbdefb;
            }
            
            QLineEdit:focus, QTextEdit:focus {
                border-color: #1976d2;
            }
            
            QTableWidget {
                background-color: white;
                alternate-background-color: #f5f5f5;
                gridline-color: #e0e0e0;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
            }
            
            QHeaderView::section {
                background-color: #1976d2;
                color: white;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            
            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #212529;
            }
            
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            
            QTabBar::tab:selected {
                background-color: #1976d2;
                color: white;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QStatusBar {
                background-color: #1976d2;
                color: white;
                border: none;
            }
        """)
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        # Logo et titre
        title_layout = QVBoxLayout()
        title = QLabel("GestImmob")
        title.setObjectName("title")
        subtitle = QLabel("Logiciel de Gestion Immobilière Professionnel v2.0.0")
        subtitle.setObjectName("subtitle")
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Informations utilisateur
        user_info = QLabel("👤 Administrateur | 🕒 " + datetime.now().strftime("%d/%m/%Y %H:%M"))
        user_info.setStyleSheet("color: #6c757d; font-size: 10pt;")
        header_layout.addWidget(user_info)
        
        main_layout.addLayout(header_layout)
        
        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("color: #e0e0e0;")
        main_layout.addWidget(separator)
        
        # Onglets principaux
        self.tabs = QTabWidget()
        
        # Onglet Gestion des Biens
        self.biens_tab = self.create_biens_tab()
        self.tabs.addTab(self.biens_tab, "📋 Gestion des Biens")
        
        # Onglet Modules Avancés
        self.modules_tab = self.create_modules_tab()
        self.tabs.addTab(self.modules_tab, "[OUTIL] Modules Avancés")
        
        # Onglet Rapports
        self.rapports_tab = self.create_rapports_tab()
        self.tabs.addTab(self.rapports_tab, "[STATS] Rapports & Analyses")
        
        # Onglet Paramètres
        self.settings_tab = self.create_settings_tab()
        self.tabs.addTab(self.settings_tab, "⚙️ Paramètres")
        
        main_layout.addWidget(self.tabs)
        
        # Menu
        self.create_menu()
        
    def create_biens_tab(self):
        """Crée l'onglet de gestion des biens"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Formulaire d'ajout
        form_group = QGroupBox("Ajouter un nouveau bien")
        form_layout = QFormLayout(form_group)
        
        self.designation_input = QLineEdit()
        self.designation_input.setPlaceholderText("Ex: Ordinateur portable Dell")
        form_layout.addRow("Désignation:", self.designation_input)
        
        self.valeur_input = QLineEdit()
        self.valeur_input.setPlaceholderText("Ex: 1200.50")
        form_layout.addRow("Valeur (€):", self.valeur_input)
        
        self.annee_input = QLineEdit()
        self.annee_input.setPlaceholderText("Ex: 2024")
        form_layout.addRow("Année:", self.annee_input)
        
        self.localisation_input = QLineEdit()
        self.localisation_input.setPlaceholderText("Ex: Bureau 101")
        form_layout.addRow("Localisation:", self.localisation_input)
        
        self.secteur_input = QLineEdit()
        self.secteur_input.setPlaceholderText("Ex: Informatique")
        form_layout.addRow("Secteur:", self.secteur_input)
        
        self.observation_input = QTextEdit()
        self.observation_input.setPlaceholderText("Observations particulières...")
        self.observation_input.setMaximumHeight(80)
        form_layout.addRow("Observations:", self.observation_input)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        self.btn_add = QPushButton("➕ Ajouter")
        self.btn_add.clicked.connect(self.ajouter_bien)
        buttons_layout.addWidget(self.btn_add)
        
        self.btn_modify = QPushButton("✏️ Modifier")
        self.btn_modify.clicked.connect(self.modifier_bien)
        buttons_layout.addWidget(self.btn_modify)
        
        self.btn_delete = QPushButton("🗑️ Supprimer")
        self.btn_delete.clicked.connect(self.supprimer_bien)
        buttons_layout.addWidget(self.btn_delete)
        
        self.btn_export = QPushButton("📤 Exporter Excel")
        self.btn_export.clicked.connect(self.exporter_excel)
        buttons_layout.addWidget(self.btn_export)
        
        buttons_layout.addStretch()
        form_layout.addRow("", buttons_layout)
        
        layout.addWidget(form_group)
        
        # Tableau des biens
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur (€)", "Année", "Localisation", "Secteur", "Observations"
        ])
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        
        layout.addWidget(self.table)
        
        return widget

    def create_modules_tab(self):
        """Crée l'onglet des modules avancés"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Modules disponibles
        modules_group = QGroupBox("Modules Disponibles")
        modules_layout = QVBoxLayout(modules_group)

        # Ligne 1 de modules
        row1_layout = QHBoxLayout()

        btn_erp = QPushButton("💼 ERP/Comptabilité")
        btn_erp.clicked.connect(self.open_erp)
        row1_layout.addWidget(btn_erp)

        btn_calc = QPushButton("🧮 Calculatrice Financière")
        btn_calc.clicked.connect(self.open_calculatrice)
        row1_layout.addWidget(btn_calc)

        btn_print = QPushButton("🖨️ Impression Avancée")
        btn_print.clicked.connect(self.open_impression)
        row1_layout.addWidget(btn_print)

        modules_layout.addLayout(row1_layout)

        # Ligne 2 de modules
        row2_layout = QHBoxLayout()

        btn_search = QPushButton("🔍 Recherche Avancée")
        btn_search.clicked.connect(self.open_recherche)
        row2_layout.addWidget(btn_search)

        btn_docs = QPushButton("📄 Gestion Documents")
        btn_docs.clicked.connect(self.open_documents)
        row2_layout.addWidget(btn_docs)

        btn_reform = QPushButton("♻️ Gestion Réformes")
        btn_reform.clicked.connect(self.open_reforme)
        row2_layout.addWidget(btn_reform)

        modules_layout.addLayout(row2_layout)

        layout.addWidget(modules_group)

        # Informations sur les modules
        info_group = QGroupBox("Informations")
        info_layout = QVBoxLayout(info_group)

        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <h3>Modules GestImmob v2.0.0</h3>
        <ul>
            <li><b>ERP/Comptabilité</b> - Gestion clients, fournisseurs, factures</li>
            <li><b>Calculatrice Financière</b> - Amortissements, prêts, rentabilité</li>
            <li><b>Impression Avancée</b> - Multi-formats, multi-imprimantes</li>
            <li><b>Recherche Avancée</b> - Filtres intelligents, recherche floue</li>
            <li><b>Gestion Documents</b> - Scanner, OCR, codes-barres</li>
            <li><b>Gestion Réformes</b> - Workflow d'approbation</li>
        </ul>
        <p><i>Tous les modules sont optimisés pour votre machine performante.</i></p>
        """)
        info_layout.addWidget(info_text)

        layout.addWidget(info_group)

        return widget

    def create_rapports_tab(self):
        """Crée l'onglet des rapports"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Statistiques rapides
        stats_group = QGroupBox("Statistiques Rapides")
        stats_layout = QFormLayout(stats_group)

        # Calculer les statistiques
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        total_biens = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(valeur) FROM immobilisations")
        valeur_totale = cursor.fetchone()[0] or 0

        cursor.execute("SELECT AVG(valeur) FROM immobilisations")
        valeur_moyenne = cursor.fetchone()[0] or 0

        stats_layout.addRow("Nombre total de biens:", QLabel(f"<b>{total_biens}</b>"))
        stats_layout.addRow("Valeur totale:", QLabel(f"<b>{valeur_totale:,.2f} €</b>"))
        stats_layout.addRow("Valeur moyenne:", QLabel(f"<b>{valeur_moyenne:,.2f} €</b>"))

        layout.addWidget(stats_group)

        # Rapports disponibles
        rapports_group = QGroupBox("Rapports Disponibles")
        rapports_layout = QVBoxLayout(rapports_group)

        btn_rapport_complet = QPushButton("📋 Rapport Complet")
        btn_rapport_complet.clicked.connect(self.generer_rapport_complet)
        rapports_layout.addWidget(btn_rapport_complet)

        btn_rapport_secteur = QPushButton("🏢 Rapport par Secteur")
        btn_rapport_secteur.clicked.connect(self.generer_rapport_secteur)
        rapports_layout.addWidget(btn_rapport_secteur)

        btn_rapport_amortissement = QPushButton("📉 Rapport d'Amortissement")
        btn_rapport_amortissement.clicked.connect(self.generer_rapport_amortissement)
        rapports_layout.addWidget(btn_rapport_amortissement)

        layout.addWidget(rapports_group)

        layout.addStretch()

        return widget

    def create_settings_tab(self):
        """Crée l'onglet des paramètres"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Paramètres généraux
        general_group = QGroupBox("Paramètres Généraux")
        general_layout = QFormLayout(general_group)

        # Thème
        theme_label = QLabel("Thème moderne sombre appliqué")
        general_layout.addRow("Thème actuel:", theme_label)

        # Performance
        perf_label = QLabel("Profil 'high_end' - Optimisé automatiquement")
        general_layout.addRow("Profil de performance:", perf_label)

        # Base de données
        db_label = QLabel(f"SQLite - {self.db_path}")
        general_layout.addRow("Base de données:", db_label)

        layout.addWidget(general_group)

        # Actions système
        actions_group = QGroupBox("Actions Système")
        actions_layout = QVBoxLayout(actions_group)

        btn_backup = QPushButton("💾 Créer une Sauvegarde")
        btn_backup.clicked.connect(self.creer_sauvegarde)
        actions_layout.addWidget(btn_backup)

        btn_optimize = QPushButton("⚡ Optimiser la Base de Données")
        btn_optimize.clicked.connect(self.optimiser_db)
        actions_layout.addWidget(btn_optimize)

        btn_about = QPushButton("ℹ️ À Propos")
        btn_about.clicked.connect(self.show_about)
        actions_layout.addWidget(btn_about)

        layout.addWidget(actions_group)

        layout.addStretch()

        return widget

    def create_menu(self):
        """Crée le menu principal"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("Fichier")

        new_action = QAction("Nouveau bien", self)
        new_action.triggered.connect(self.ajouter_bien)
        file_menu.addAction(new_action)

        file_menu.addSeparator()

        export_action = QAction("Exporter Excel", self)
        export_action.triggered.connect(self.exporter_excel)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        quit_action = QAction("Quitter", self)
        quit_action.triggered.connect(self.close)
        file_menu.addAction(quit_action)

        # Menu Modules
        modules_menu = menubar.addMenu("Modules")

        erp_action = QAction("ERP/Comptabilité", self)
        erp_action.triggered.connect(self.open_erp)
        modules_menu.addAction(erp_action)

        calc_action = QAction("Calculatrice", self)
        calc_action.triggered.connect(self.open_calculatrice)
        modules_menu.addAction(calc_action)

        # Menu Aide
        help_menu = menubar.addMenu("Aide")

        about_action = QAction("À propos", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def load_data(self):
        """Charge les données dans le tableau"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM immobilisations ORDER BY id DESC")
        rows = cursor.fetchall()

        self.table.setRowCount(len(rows))

        for row_idx, row_data in enumerate(rows):
            for col_idx, data in enumerate(row_data[:-2]):  # Exclure les timestamps
                item = QTableWidgetItem(str(data))
                self.table.setItem(row_idx, col_idx, item)

        self.table.resizeColumnsToContents()

    def ajouter_bien(self):
        """Ajoute un nouveau bien"""
        designation = self.designation_input.text().strip()
        valeur_text = self.valeur_input.text().strip()
        annee_text = self.annee_input.text().strip()
        localisation = self.localisation_input.text().strip()
        secteur = self.secteur_input.text().strip()
        observation = self.observation_input.toPlainText().strip()

        # Validation
        if not all([designation, valeur_text, annee_text]):
            QMessageBox.warning(self, "Erreur", "Les champs Désignation, Valeur et Année sont obligatoires.")
            return

        try:
            valeur = float(valeur_text)
            annee = int(annee_text)
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide.")
            return

        # Insertion en base
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO immobilisations (designation, valeur, annee, localisation, secteur, observation)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (designation, valeur, annee, localisation, secteur, observation))

        self.conn.commit()

        # Vider les champs
        self.designation_input.clear()
        self.valeur_input.clear()
        self.annee_input.clear()
        self.localisation_input.clear()
        self.secteur_input.clear()
        self.observation_input.clear()

        # Recharger les données
        self.load_data()

        # Message de confirmation
        QMessageBox.information(self, "Succès", f"Bien '{designation}' ajouté avec succès.")
        self.status_bar.showMessage(f"Bien ajouté: {designation}", 3000)

    def modifier_bien(self):
        """Modifie le bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à modifier.")
            return

        QMessageBox.information(self, "Info", "Fonction de modification à implémenter.")

    def supprimer_bien(self):
        """Supprime le bien sélectionné"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à supprimer.")
            return

        # Confirmation
        reply = QMessageBox.question(self, "Confirmation",
                                   "Êtes-vous sûr de vouloir supprimer ce bien ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            bien_id = self.table.item(current_row, 0).text()
            cursor = self.conn.cursor()
            cursor.execute("DELETE FROM immobilisations WHERE id = ?", (bien_id,))
            self.conn.commit()

            self.load_data()
            QMessageBox.information(self, "Succès", "Bien supprimé avec succès.")
            self.status_bar.showMessage("Bien supprimé", 3000)

    def exporter_excel(self):
        """Exporte les données vers Excel"""
        try:
            import xlsxwriter

            filename = f"export_gestimmob_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            workbook = xlsxwriter.Workbook(filename)
            worksheet = workbook.add_worksheet("Immobilisations")

            # En-têtes
            headers = ["ID", "Désignation", "Valeur (€)", "Année", "Localisation", "Secteur", "Observations"]
            for col, header in enumerate(headers):
                worksheet.write(0, col, header)

            # Données
            cursor = self.conn.cursor()
            cursor.execute("SELECT id, designation, valeur, annee, localisation, secteur, observation FROM immobilisations")
            rows = cursor.fetchall()

            for row_idx, row_data in enumerate(rows, 1):
                for col_idx, data in enumerate(row_data):
                    worksheet.write(row_idx, col_idx, data)

            workbook.close()

            QMessageBox.information(self, "Succès", f"Export réussi: {filename}")
            self.status_bar.showMessage(f"Export créé: {filename}", 5000)

        except ImportError:
            QMessageBox.warning(self, "Erreur", "Module xlsxwriter non disponible.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    # Méthodes pour les modules (placeholders)
    def open_erp(self):
        QMessageBox.information(self, "ERP", "Module ERP/Comptabilité\n\nFonctionnalités:\n- Gestion clients\n- Gestion fournisseurs\n- Facturation\n- Bons de commande")

    def open_calculatrice(self):
        QMessageBox.information(self, "Calculatrice", "Calculatrice Financière\n\nFonctionnalités:\n- Amortissement linéaire\n- Calcul de prêts\n- Rentabilité locative\n- Conversion devises")

    def open_impression(self):
        QMessageBox.information(self, "Impression", "Module d'Impression Avancée\n\nFonctionnalités:\n- Multi-formats (PDF, A4, A3)\n- Multi-imprimantes\n- Aperçu avant impression\n- File d'attente")

    def open_recherche(self):
        QMessageBox.information(self, "Recherche", "Recherche Avancée\n\nFonctionnalités:\n- Filtres intelligents\n- Recherche floue\n- Recherche par critères\n- Historique des recherches")

    def open_documents(self):
        QMessageBox.information(self, "Documents", "Gestion Documents\n\nFonctionnalités:\n- Scanner intégré\n- OCR automatique\n- Codes-barres\n- Archivage numérique")

    def open_reforme(self):
        QMessageBox.information(self, "Réformes", "Gestion des Réformes\n\nFonctionnalités:\n- Workflow d'approbation\n- Commission de réforme\n- Génération de PV\n- Suivi des décisions")

    def generer_rapport_complet(self):
        QMessageBox.information(self, "Rapport", "Génération du rapport complet en cours...")

    def generer_rapport_secteur(self):
        QMessageBox.information(self, "Rapport", "Génération du rapport par secteur en cours...")

    def generer_rapport_amortissement(self):
        QMessageBox.information(self, "Rapport", "Génération du rapport d'amortissement en cours...")

    def creer_sauvegarde(self):
        import shutil
        backup_name = f"gestimmob_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2(self.db_path, backup_name)
        QMessageBox.information(self, "Sauvegarde", f"Sauvegarde créée: {backup_name}")

    def optimiser_db(self):
        cursor = self.conn.cursor()
        cursor.execute("VACUUM")
        self.conn.commit()
        QMessageBox.information(self, "Optimisation", "Base de données optimisée avec succès.")

    def show_about(self):
        QMessageBox.about(self, "À propos de GestImmob",
                         """<h2>GestImmob v2.0.0</h2>
                         <p>Logiciel de Gestion Immobilière Professionnel</p>
                         <p><b>Fonctionnalités:</b></p>
                         <ul>
                         <li>Gestion complète des biens immobiliers</li>
                         <li>Modules ERP/Comptabilité intégrés</li>
                         <li>Calculatrice financière avancée</li>
                         <li>Impression multi-formats</li>
                         <li>Interface moderne et fluide</li>
                         <li>Optimisé pour machines performantes</li>
                         </ul>
                         <p><i>Développé avec Python et PySide6</i></p>""")

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName("GestImmob")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("GestImmob Solutions")

    # Créer et afficher la fenêtre principale
    window = GestImmobApp()
    window.show()

    # Lancer la boucle d'événements
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
