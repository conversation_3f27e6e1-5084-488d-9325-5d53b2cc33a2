from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QLabel, QFileDialog, QInputDialog,
    QStackedWidget, QColorDialog
)
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QPixmap, QColor
import logging
import requests
from suivi_animaux import SuiviAnimauxWidget
from inventaire import InventaireWidget
from parc_auto import ParcAutoWidget
from materiel_travaux import MaterielTravauxWidget
from suivi_travaux import SuiviTravauxWidget
from calculatrice import CalculatriceWidget  # à créer

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Logiciel de Gestion Immobilière et Travaux")
        self.resize(1600, 900)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f4fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #1976d2;
                color: white;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QTableWidget {
                background-color: #ffffff;
                border: 1px solid #bdbdbd;
            }
            QHeaderView::section {
                background-color: #1976d2;
                color: white;
                font-weight: bold;
            }
        """)
        layout = QVBoxLayout(self)
        btn_layout = QHBoxLayout()
        self.btn_inventaire = QPushButton("Inventaire")
        self.btn_parc_auto = QPushButton("Parc Auto")
        self.btn_materiel_travaux = QPushButton("Matériel de Travaux")
        self.btn_suivi_travaux = QPushButton("Suivi des Travaux")
        self.btn_suivi_animaux = QPushButton("Suivi des Animaux")
        self.btn_calculatrice = QPushButton("Calculatrice IA")
        self.btn_sync = QPushButton("Synchroniser")
        self.btn_save = QPushButton("Sauvegarder")
        self.btn_actualiser = QPushButton("Actualiser")
        btn_layout.addWidget(self.btn_inventaire)
        btn_layout.addWidget(self.btn_parc_auto)
        btn_layout.addWidget(self.btn_materiel_travaux)
        btn_layout.addWidget(self.btn_suivi_travaux)
        btn_layout.addWidget(self.btn_suivi_animaux)
        btn_layout.addWidget(self.btn_calculatrice)
        btn_layout.addWidget(self.btn_sync)
        btn_layout.addWidget(self.btn_save)
        btn_layout.addWidget(self.btn_actualiser)
        layout.addLayout(btn_layout)
        self.central_widget = QStackedWidget()
        layout.addWidget(self.central_widget)
        self.setLayout(layout)
        # Connexions
        self.btn_inventaire.clicked.connect(self.open_inventaire)
        self.btn_parc_auto.clicked.connect(self.open_parc_auto)
        self.btn_materiel_travaux.clicked.connect(self.open_materiel_travaux)
        self.btn_suivi_travaux.clicked.connect(self.open_suivi_travaux)
        self.btn_suivi_animaux.clicked.connect(self.open_suivi_animaux)
        self.btn_calculatrice.clicked.connect(self.open_calculatrice)
        self.btn_sync.clicked.connect(self.synchroniser)
        self.btn_save.clicked.connect(self.sauvegarder)
        self.btn_actualiser.clicked.connect(self.actualiser)

    def open_inventaire(self):
        self._show_module(InventaireWidget())

    def open_parc_auto(self):
        self._show_module(ParcAutoWidget())

    def open_materiel_travaux(self):
        self._show_module(MaterielTravauxWidget())

    def open_suivi_travaux(self):
        self._show_module(SuiviTravauxWidget())

    def open_suivi_animaux(self):
        self._show_module(SuiviAnimauxWidget())

    def _show_module(self, widget):
        # Cache tous les widgets enfants
        for i in reversed(range(self.layout().count())):
            self.layout().itemAt(i).widget().setParent(None)
        # Affiche le nouveau widget
        self.layout().addWidget(widget)
        widget.setFocus()

    def synchroniser(self):
        # ...logique de synchronisation multi-base ou API...
        QMessageBox.information(self, "Synchronisation", "Synchronisation effectuée avec succès.")

    def sauvegarder(self):
        # ...logique de sauvegarde (fichier, base, etc.)...
        QMessageBox.information(self, "Sauvegarde", "Sauvegarde effectuée avec succès.")

    def actualiser(self):
        # ...logique d'actualisation des données...
        QMessageBox.information(self, "Actualiser", "Données actualisées avec succès.")

    def open_calculatrice(self):
        self._show_module(CalculatriceWidget())