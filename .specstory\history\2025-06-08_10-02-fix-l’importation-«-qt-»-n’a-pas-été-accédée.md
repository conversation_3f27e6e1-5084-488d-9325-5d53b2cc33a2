<!-- Generated by SpecStory -->

# /fix L’importation « Qt » n’a pas été accédée (2025-06-08 11:02:32)

_**User**_

/fix L’importation « Qt » n’a pas été accédée

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Supprimez l'import inutile de `Qt` car il n'est pas utilisé dans le code.

````python
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QTextEdit, QLabel, QMessageBox
# ...existing code...
````

---

