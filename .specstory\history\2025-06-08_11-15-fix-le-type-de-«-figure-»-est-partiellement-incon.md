<!-- Generated by SpecStory -->

# /fix Le type de « figure » est partiellement incon (2025-06-08 12:15:05)

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l'avertissement de typage sur `plt.figure`, il suffit d'importer explicitement le type `Figure` depuis `matplotlib.figure` et d'annoter la variable si besoin. Ici, vous pouvez ajouter l'import en haut de la méthode concernée, ou simplement ignorer l'avertissement car l'appel est correct. Pour lever toute ambiguïté, ajoutez l'import du type `Figure` et annotez la variable si vous l'utilisez.

Voici la modification à appliquer dans la méthode `afficher_charts` :

````python
    def afficher_charts(self):
        try:
            import matplotlib.pyplot as plt
            from matplotlib.figure import Figure  # Ajout de l'import explicite du type Figure
            from collections import defaultdict
            from typing import DefaultDict
            # ...existing code...
            if stats_categorie:
                fig: Figure = plt.figure(figsize=(8, 4))  # Annotation de type explicite
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                fig: Figure = plt.figure(figsize=(8, 4))  # Annotation de type explicite
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            # ...existing code...
````

**Résumé** :  
- Ajoutez `from matplotlib.figure import Figure` dans la méthode.
- Annotez la variable retournée par `plt.figure` avec `fig: Figure = ...` pour lever l'ambiguïté de typage.
- Cela satisfera les outils de typage statique comme Pylance.

---

