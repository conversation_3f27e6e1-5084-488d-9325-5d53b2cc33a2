#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Détection et résolution automatique des conflits de modules
"""

import os
import ast
import sys
import shutil
import datetime
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict

class DetecteurConflits:
    def __init__(self):
        self.conflits_detectes = []
        self.resolutions_appliquees = []
        self.sauvegarde_conflits = None
        
    def creer_sauvegarde_conflits(self):
        """Crée une sauvegarde spécifique aux conflits"""
        print("💾 SAUVEGARDE SPÉCIFIQUE CONFLITS")
        print("=" * 40)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = Path(f"backup_conflits_{timestamp}")
        
        try:
            backup_dir.mkdir(exist_ok=True)
            
            # Sauvegarder tous les fichiers Python
            for py_file in Path('.').rglob('*.py'):
                if not any(part.startswith('.') for part in py_file.parts):
                    backup_path = backup_dir / py_file
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(py_file, backup_path)
            
            self.sauvegarde_conflits = str(backup_dir)
            print(f"✅ Sauvegarde conflits: {backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde conflits: {e}")
            return False
    
    def analyser_imports_conflits(self):
        """Analyse les conflits d'imports entre modules"""
        print(f"\n🔍 ANALYSE DES CONFLITS D'IMPORTS")
        print("-" * 40)
        
        imports_map = defaultdict(list)
        conflits_imports = []
        
        # Analyser tous les fichiers Python
        for py_file in Path('.').rglob('*.py'):
            if not any(part.startswith('.') for part in py_file.parts):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Import):
                            for alias in node.names:
                                imports_map[alias.name].append(str(py_file))
                        elif isinstance(node, ast.ImportFrom):
                            if node.module:
                                imports_map[node.module].append(str(py_file))
                
                except Exception as e:
                    print(f"⚠️ Erreur analyse {py_file}: {e}")
        
        # Détecter les conflits potentiels
        for module, fichiers in imports_map.items():
            if len(fichiers) > 1 and any(name in module for name in ['inventaire', 'fournisseur', 'document', 'reforme', 'recherche']):
                conflits_imports.append({
                    'module': module,
                    'fichiers': fichiers,
                    'type': 'import_circulaire'
                })
        
        self.conflits_detectes.extend(conflits_imports)
        
        if conflits_imports:
            print(f"❌ {len(conflits_imports)} conflit(s) d'imports détecté(s)")
            for conflit in conflits_imports:
                print(f"  - Module: {conflit['module']}")
                print(f"    Fichiers: {', '.join(conflit['fichiers'][:3])}")
        else:
            print("✅ Aucun conflit d'imports détecté")
    
    def analyser_noms_conflits(self):
        """Analyse les conflits de noms de classes/fonctions"""
        print(f"\n🔍 ANALYSE DES CONFLITS DE NOMS")
        print("-" * 40)
        
        noms_classes = defaultdict(list)
        noms_fonctions = defaultdict(list)
        conflits_noms = []
        
        for py_file in Path('.').rglob('*.py'):
            if not any(part.startswith('.') for part in py_file.parts):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.ClassDef):
                            noms_classes[node.name].append(str(py_file))
                        elif isinstance(node, ast.FunctionDef):
                            noms_fonctions[node.name].append(str(py_file))
                
                except Exception as e:
                    print(f"⚠️ Erreur analyse noms {py_file}: {e}")
        
        # Détecter conflits de classes
        for nom_classe, fichiers in noms_classes.items():
            if len(fichiers) > 1:
                conflits_noms.append({
                    'nom': nom_classe,
                    'type': 'classe',
                    'fichiers': fichiers
                })
        
        # Détecter conflits de fonctions critiques
        for nom_fonction, fichiers in noms_fonctions.items():
            if len(fichiers) > 1 and nom_fonction in ['main', 'init', 'setup', 'configure']:
                conflits_noms.append({
                    'nom': nom_fonction,
                    'type': 'fonction',
                    'fichiers': fichiers
                })
        
        self.conflits_detectes.extend(conflits_noms)
        
        if conflits_noms:
            print(f"❌ {len(conflits_noms)} conflit(s) de noms détecté(s)")
            for conflit in conflits_noms:
                print(f"  - {conflit['type'].capitalize()}: {conflit['nom']}")
                print(f"    Fichiers: {', '.join(conflit['fichiers'][:3])}")
        else:
            print("✅ Aucun conflit de noms détecté")
    
    def analyser_variables_globales(self):
        """Analyse les conflits de variables globales"""
        print(f"\n🔍 ANALYSE DES VARIABLES GLOBALES")
        print("-" * 40)
        
        variables_globales = defaultdict(list)
        conflits_variables = []
        
        for py_file in Path('.').rglob('*.py'):
            if not any(part.startswith('.') for part in py_file.parts):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    # Variables globales potentiellement problématiques
                                    if target.id.isupper() or target.id in ['config', 'settings', 'db', 'app']:
                                        variables_globales[target.id].append(str(py_file))
                
                except Exception as e:
                    print(f"⚠️ Erreur analyse variables {py_file}: {e}")
        
        # Détecter conflits de variables globales
        for nom_var, fichiers in variables_globales.items():
            if len(fichiers) > 1:
                conflits_variables.append({
                    'nom': nom_var,
                    'type': 'variable_globale',
                    'fichiers': fichiers
                })
        
        self.conflits_detectes.extend(conflits_variables)
        
        if conflits_variables:
            print(f"❌ {len(conflits_variables)} conflit(s) de variables détecté(s)")
            for conflit in conflits_variables:
                print(f"  - Variable: {conflit['nom']}")
                print(f"    Fichiers: {', '.join(conflit['fichiers'][:3])}")
        else:
            print("✅ Aucun conflit de variables détecté")
    
    def resoudre_conflits_automatiquement(self):
        """Résout automatiquement les conflits détectés"""
        print(f"\n🔧 RÉSOLUTION AUTOMATIQUE DES CONFLITS")
        print("=" * 50)
        
        if not self.conflits_detectes:
            print("✅ Aucun conflit à résoudre")
            return True
        
        print(f"🔧 Résolution de {len(self.conflits_detectes)} conflit(s)...")
        
        for conflit in self.conflits_detectes:
            try:
                if conflit['type'] == 'import_circulaire':
                    self._resoudre_import_circulaire(conflit)
                elif conflit['type'] == 'classe':
                    self._resoudre_conflit_classe(conflit)
                elif conflit['type'] == 'fonction':
                    self._resoudre_conflit_fonction(conflit)
                elif conflit['type'] == 'variable_globale':
                    self._resoudre_conflit_variable(conflit)
                
            except Exception as e:
                print(f"❌ Erreur résolution conflit {conflit}: {e}")
        
        print(f"✅ {len(self.resolutions_appliquees)} résolution(s) appliquée(s)")
        return True
    
    def _resoudre_import_circulaire(self, conflit: Dict):
        """Résout un conflit d'import circulaire"""
        print(f"🔄 Résolution import circulaire: {conflit['module']}")
        
        # Stratégie: Créer un module d'interface commun
        interface_module = f"src/interfaces/{conflit['module']}_interface.py"
        
        try:
            Path(interface_module).parent.mkdir(parents=True, exist_ok=True)
            
            interface_content = f'''# -*- coding: utf-8 -*-
"""
Interface commune pour {conflit['module']}
Résout les imports circulaires
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

class {conflit['module'].capitalize()}Interface(ABC):
    """Interface commune pour éviter les imports circulaires"""
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialise le module"""
        pass
    
    @abstractmethod
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Traite les données"""
        pass
    
    @abstractmethod
    def cleanup(self) -> bool:
        """Nettoie les ressources"""
        pass
'''
            
            with open(interface_module, 'w', encoding='utf-8') as f:
                f.write(interface_content)
            
            self.resolutions_appliquees.append(f"Interface créée: {interface_module}")
            print(f"  ✅ Interface créée: {interface_module}")
            
        except Exception as e:
            print(f"  ❌ Erreur création interface: {e}")
    
    def _resoudre_conflit_classe(self, conflit: Dict):
        """Résout un conflit de nom de classe"""
        print(f"🏷️ Résolution conflit classe: {conflit['nom']}")
        
        # Stratégie: Renommer les classes avec préfixe de module
        for i, fichier in enumerate(conflit['fichiers']):
            if i == 0:
                continue  # Garder le premier fichier inchangé
            
            try:
                # Déterminer le préfixe basé sur le chemin
                module_name = self._extraire_nom_module(fichier)
                nouveau_nom = f"{module_name.capitalize()}{conflit['nom']}"
                
                # Lire et modifier le fichier
                with open(fichier, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Remplacer le nom de classe
                content = content.replace(f"class {conflit['nom']}", f"class {nouveau_nom}")
                content = content.replace(f"{conflit['nom']}(", f"{nouveau_nom}(")
                
                with open(fichier, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.resolutions_appliquees.append(f"Classe renommée: {conflit['nom']} → {nouveau_nom} dans {fichier}")
                print(f"  ✅ Classe renommée: {conflit['nom']} → {nouveau_nom}")
                
            except Exception as e:
                print(f"  ❌ Erreur renommage classe dans {fichier}: {e}")
    
    def _resoudre_conflit_fonction(self, conflit: Dict):
        """Résout un conflit de nom de fonction"""
        print(f"⚙️ Résolution conflit fonction: {conflit['nom']}")
        
        # Stratégie similaire aux classes
        for i, fichier in enumerate(conflit['fichiers']):
            if i == 0:
                continue
            
            try:
                module_name = self._extraire_nom_module(fichier)
                nouveau_nom = f"{module_name}_{conflit['nom']}"
                
                with open(fichier, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                content = content.replace(f"def {conflit['nom']}(", f"def {nouveau_nom}(")
                content = content.replace(f"{conflit['nom']}(", f"{nouveau_nom}(")
                
                with open(fichier, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.resolutions_appliquees.append(f"Fonction renommée: {conflit['nom']} → {nouveau_nom} dans {fichier}")
                print(f"  ✅ Fonction renommée: {conflit['nom']} → {nouveau_nom}")
                
            except Exception as e:
                print(f"  ❌ Erreur renommage fonction dans {fichier}: {e}")
    
    def _resoudre_conflit_variable(self, conflit: Dict):
        """Résout un conflit de variable globale"""
        print(f"📊 Résolution conflit variable: {conflit['nom']}")
        
        # Stratégie: Encapsuler dans une classe de configuration
        for i, fichier in enumerate(conflit['fichiers']):
            if i == 0:
                continue
            
            try:
                module_name = self._extraire_nom_module(fichier)
                
                with open(fichier, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Remplacer la variable globale par une propriété de classe
                config_class = f"""
class {module_name.capitalize()}Config:
    {conflit['nom']} = None  # Valeur à définir
"""
                
                # Ajouter la classe au début du fichier
                lines = content.splitlines()
                insert_pos = 0
                for i, line in enumerate(lines):
                    if not line.strip().startswith('#') and not line.strip().startswith('"""') and line.strip():
                        insert_pos = i
                        break
                
                lines.insert(insert_pos, config_class)
                content = '\n'.join(lines)
                
                # Remplacer les références à la variable
                content = content.replace(f"{conflit['nom']} =", f"{module_name.capitalize()}Config.{conflit['nom']} =")
                
                with open(fichier, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.resolutions_appliquees.append(f"Variable encapsulée: {conflit['nom']} dans {fichier}")
                print(f"  ✅ Variable encapsulée: {conflit['nom']}")
                
            except Exception as e:
                print(f"  ❌ Erreur encapsulation variable dans {fichier}: {e}")
    
    def _extraire_nom_module(self, fichier: str) -> str:
        """Extrait le nom du module depuis le chemin du fichier"""
        path = Path(fichier)
        
        # Essayer d'extraire depuis le nom du répertoire parent
        if 'modules' in path.parts:
            modules_index = path.parts.index('modules')
            if modules_index + 1 < len(path.parts):
                return path.parts[modules_index + 1]
        
        # Sinon, utiliser le nom du fichier
        return path.stem.replace('_', '').lower()
    
    def valider_resolutions(self):
        """Valide que les résolutions n'ont pas cassé la syntaxe"""
        print(f"\n✅ VALIDATION DES RÉSOLUTIONS")
        print("-" * 40)
        
        erreurs_syntaxe = []
        
        for py_file in Path('.').rglob('*.py'):
            if not any(part.startswith('.') for part in py_file.parts):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    ast.parse(content)
                    
                except SyntaxError as e:
                    erreurs_syntaxe.append(f"{py_file}: {e}")
                except Exception as e:
                    print(f"⚠️ Erreur validation {py_file}: {e}")
        
        if erreurs_syntaxe:
            print(f"❌ {len(erreurs_syntaxe)} erreur(s) de syntaxe après résolution:")
            for erreur in erreurs_syntaxe:
                print(f"  - {erreur}")
            return False
        else:
            print("✅ Toutes les résolutions sont syntaxiquement correctes")
            return True
    
    def generer_rapport_conflits(self):
        """Génère un rapport complet des conflits"""
        print(f"\n📊 RAPPORT DE RÉSOLUTION DES CONFLITS")
        print("=" * 60)
        
        print(f"🔍 Conflits détectés: {len(self.conflits_detectes)}")
        print(f"🔧 Résolutions appliquées: {len(self.resolutions_appliquees)}")
        print(f"💾 Sauvegarde: {self.sauvegarde_conflits or 'Non effectuée'}")
        
        if self.conflits_detectes:
            print(f"\n🔍 CONFLITS DÉTECTÉS:")
            for conflit in self.conflits_detectes:
                print(f"  - Type: {conflit['type']}")
                print(f"    Nom: {conflit.get('nom', conflit.get('module', 'N/A'))}")
        
        if self.resolutions_appliquees:
            print(f"\n🔧 RÉSOLUTIONS APPLIQUÉES:")
            for resolution in self.resolutions_appliquees:
                print(f"  - {resolution}")
        
        succes = len(self.conflits_detectes) == 0 or len(self.resolutions_appliquees) > 0
        
        if succes:
            print(f"\n🎉 RÉSOLUTION DES CONFLITS RÉUSSIE !")
            print(f"✅ Modules isolés et sans conflit")
            print(f"✅ Application stable")
        else:
            print(f"\n⚠️ RÉSOLUTION PARTIELLE")
            print(f"Certains conflits nécessitent une intervention manuelle")
        
        return succes

def main():
    """Fonction principale de détection et résolution des conflits"""
    print("🔍 DÉTECTION ET RÉSOLUTION DES CONFLITS")
    print("=" * 80)
    
    detecteur = DetecteurConflits()
    
    try:
        # Sauvegarde de sécurité
        if not detecteur.creer_sauvegarde_conflits():
            print("❌ Impossible de créer la sauvegarde - Arrêt")
            return False
        
        # Analyse des conflits
        detecteur.analyser_imports_conflits()
        detecteur.analyser_noms_conflits()
        detecteur.analyser_variables_globales()
        
        # Résolution automatique
        detecteur.resoudre_conflits_automatiquement()
        
        # Validation
        if not detecteur.valider_resolutions():
            print(f"⚠️ Erreurs de syntaxe détectées après résolution")
            print(f"💾 Restauration possible depuis: {detecteur.sauvegarde_conflits}")
        
        # Rapport final
        succes = detecteur.generer_rapport_conflits()
        
        return succes
        
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        if detecteur.sauvegarde_conflits:
            print(f"💾 Restauration possible depuis: {detecteur.sauvegarde_conflits}")
        return False

if __name__ == "__main__":
    main()
