<!-- Generated by SpecStory -->

# /fix Désolé... Nous ne pouvons pas affecter l’attr (2025-06-08 10:59:41)

_**User**_

/fix Désolé... Nous ne pouvons pas affecter l’attribut « username » pour la classe « User »
  L'expression de type « str » ne peut pas être attribuée à l’attribut « username » de la classe « User »
    « str » n’est pas assignable à « Column[str] »

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Résumé :  
Le problème vient probablement d'une confusion de typage ou d'une mauvaise déclaration de la classe `User`. L'attribut `username` doit être accédé comme une propriété d'instance (et non comme un objet `Column`).  
La déclaration actuelle est correcte pour SQLAlchemy, mais si vous avez un problème de typage (par exemple avec un outil comme mypy), il faut ajouter des annotations de type pour les attributs d'instance.

**Correctif :**
- Ajoutez des annotations de type pour les attributs d'instance dans la classe `User` pour aider les outils de typage à comprendre que `user.username` est bien une chaîne (`str`) et non un objet `Column`.
- Aucun changement n'est nécessaire sur la ligne d'affectation elle-même.

````python
class User(Base):
    __tablename__ = "users"
    id: int  # Ajout annotation type
    username: str  # Ajout annotation type
    password: str  # Ajout annotation type
    role: str  # Ajout annotation type
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    role = Column(String, default="user")
# ...existing code...
    def update_user(self, user_id: int, username: Optional[str] = None, password: Optional[str] = None, role: Optional[str] = None) -> "User":
        user: "User" = self.session.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found.")
        if username:
            user.username = username  # Cette ligne est correcte avec l'annotation ci-dessus
        # ...existing code...
````

Ce correctif permet à l'outil de typage de comprendre que `user.username` est bien une chaîne de caractères, ce qui résout l'erreur de typage.

---

