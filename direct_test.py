import ast

# Test direct de main.py
try:
    with open('src/main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Test AST
    ast.parse(content)
    print("main.py: AST OK")
    
    # Test compilation
    compile(content, 'main.py', 'exec')
    print("main.py: Compilation OK")
    
except Exception as e:
    print(f"main.py ERROR: {e}")

# Test direct de user_management.py
try:
    with open('src/auth/user_management.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Test AST
    ast.parse(content)
    print("user_management.py: AST OK")
    
    # Test compilation
    compile(content, 'user_management.py', 'exec')
    print("user_management.py: Compilation OK")
    
except Exception as e:
    print(f"user_management.py ERROR: {e}")

print("Test completed")
