<!-- Generated by SpecStory -->

# /fix Le type de « alerte_modification » est partie (2025-06-08 11:11:10)

_**User**_

/fix Le type de « alerte_modification » est partiellement inconnu
  Le type de "alerte_modification" est "(item: Unknown) -> None", Le type d'argument est partiellement inconnu
  L’argument correspond au paramètre « slot » dans la fonction « connect »
  Le type d’argument est « (item: Unknown) -> None »

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Pour corriger l'avertissement sur le type du slot `alerte_modification`, il faut préciser le type de l'argument dans la signature de la méthode. Ici, il s'agit d'un objet `QTableWidgetItem`. Ajoutez l'import correspondant et annotez la méthode.

````python
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox
)
# ...existing code...

from PySide6.QtWidgets import QTableWidgetItem  # déjà importé, mais gardez-le pour clarté

class FournisseurWidget(QWidget):
    # ...existing code...

    def alerte_modification(self, item: QTableWidgetItem):
        # Détection d'anomalie simple : email vide ou téléphone non numérique
        # ...existing code...
````

---

