@echo off
title TEST SIMPLE - Verification des Problemes
color 0A

echo.
echo ========================================================================
echo                    TEST SIMPLE - VERIFICATION COMPLETE
echo                   Identification precise des problemes restants
echo ========================================================================
echo.

REM Variables
set "PYTHON_CMD="
set "ERRORS_FOUND=0"

echo [%TIME%] Demarrage du test simple...

REM Détection Python
echo.
echo [%TIME%] Detection de Python...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    echo ✅ Python detecte: py
    goto :python_found
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    echo ✅ Python detecte: python
    goto :python_found
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    echo ✅ Python detecte: python3
    goto :python_found
)

echo ❌ Python non trouve
goto :manual_test

:python_found
echo Python executable: %PYTHON_CMD%

REM Vérification du répertoire
echo.
echo [%TIME%] Verification du repertoire...
echo Repertoire: %CD%

if exist "src\main.py" (
    echo ✅ Structure projet detectee
) else (
    if exist "..\src\main.py" (
        cd ..
        echo Remonte au repertoire parent: %CD%
    )
)

REM Test avec le script Python simple
echo.
echo ========================================================================
echo                           TEST PYTHON SIMPLE
echo ========================================================================
echo.

if exist "simple_syntax_test.py" (
    echo Execution du test simple...
    %PYTHON_CMD% simple_syntax_test.py
    if not errorlevel 1 (
        echo ✅ Test Python simple: REUSSI
    ) else (
        echo ❌ Test Python simple: ECHEC
        set /a ERRORS_FOUND+=1
    )
) else (
    echo Test manuel des fichiers Python...
    goto :manual_test
)

goto :sql_test

:manual_test
echo.
echo ========================================================================
echo                           TEST MANUEL
echo ========================================================================
echo.

echo Test manuel des fichiers critiques...

REM Test manuel de chaque fichier
if exist "src\main.py" (
    echo Test de src\main.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/main.py', 'r', encoding='utf-8').read()); print('✅ main.py: OK')" 2>nul
        if not errorlevel 1 (
            echo ✅ main.py: SYNTAXE OK
        ) else (
            echo ❌ main.py: ERREUR SYNTAXE
            set /a ERRORS_FOUND+=1
        )
    )
)

if exist "src\config.py" (
    echo Test de src\config.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/config.py', 'r', encoding='utf-8').read()); print('✅ config.py: OK')" 2>nul
        if not errorlevel 1 (
            echo ✅ config.py: SYNTAXE OK
        ) else (
            echo ❌ config.py: ERREUR SYNTAXE
            set /a ERRORS_FOUND+=1
        )
    )
)

if exist "src\database\db_connection.py" (
    echo Test de src\database\db_connection.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/database/db_connection.py', 'r', encoding='utf-8').read()); print('✅ db_connection.py: OK')" 2>nul
        if not errorlevel 1 (
            echo ✅ db_connection.py: SYNTAXE OK
        ) else (
            echo ❌ db_connection.py: ERREUR SYNTAXE
            set /a ERRORS_FOUND+=1
        )
    )
)

if exist "src\auth\user_management.py" (
    echo Test de src\auth\user_management.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/auth/user_management.py', 'r', encoding='utf-8').read()); print('✅ user_management.py: OK')" 2>nul
        if not errorlevel 1 (
            echo ✅ user_management.py: SYNTAXE OK
        ) else (
            echo ❌ user_management.py: ERREUR SYNTAXE
            set /a ERRORS_FOUND+=1
        )
    )
)

:sql_test
REM Test du fichier SQL
echo.
echo ========================================================================
echo                           TEST SQL
echo ========================================================================
echo.

if exist "src\database\migrations\init.sql" (
    echo Test du fichier SQL...
    
    REM Compter les occurrences SERIAL
    findstr /C:"SERIAL PRIMARY KEY" "src\database\migrations\init.sql" >nul
    if not errorlevel 1 (
        echo ❌ SQL: Occurrences SERIAL PRIMARY KEY detectees
        set /a ERRORS_FOUND+=1
        
        REM Compter exactement combien
        for /f %%i in ('findstr /C:"SERIAL PRIMARY KEY" "src\database\migrations\init.sql" ^| find /c /v ""') do (
            echo    Nombre d occurrences SERIAL: %%i
        )
    ) else (
        echo ✅ SQL: Aucune occurrence SERIAL PRIMARY KEY
    )
    
    REM Vérifier les corrections SQLite
    findstr /C:"INTEGER PRIMARY KEY AUTOINCREMENT" "src\database\migrations\init.sql" >nul
    if not errorlevel 1 (
        echo ✅ SQL: Syntaxe SQLite detectee
        
        REM Compter les corrections
        for /f %%i in ('findstr /C:"INTEGER PRIMARY KEY AUTOINCREMENT" "src\database\migrations\init.sql" ^| find /c /v ""') do (
            echo    Nombre de corrections SQLite: %%i
        )
    ) else (
        echo ❌ SQL: Aucune syntaxe SQLite detectee
        set /a ERRORS_FOUND+=1
    )
) else (
    echo ❌ Fichier init.sql non trouve
    set /a ERRORS_FOUND+=1
)

REM Résultats finaux
echo.
echo ========================================================================
echo                              RESULTATS FINAUX
echo ========================================================================
echo.

if %ERRORS_FOUND%==0 (
    echo [%TIME%] 🎉 AUCUN PROBLEME DETECTE !
    echo.
    echo ✅ TOUS LES FICHIERS PYTHON: SYNTAXE PARFAITE
    echo ✅ FICHIER SQL: COMPATIBLE SQLITE
    echo ✅ APPLICATION: PRETE POUR UTILISATION
    echo.
    echo 🏆 CONCLUSION: TOUS LES PROBLEMES ONT ETE CORRIGES !
    echo.
    echo COMMANDES DE LANCEMENT:
    if defined PYTHON_CMD (
        echo   %PYTHON_CMD% src\main.py
    ) else (
        echo   python src\main.py
    )
) else (
    echo [%TIME%] 🚨 %ERRORS_FOUND% PROBLEME(S) DETECTE(S)
    echo.
    echo ❌ Des corrections sont encore necessaires
    echo.
    echo ACTIONS RECOMMANDEES:
    echo   1. Corriger les erreurs de syntaxe Python detectees
    echo   2. Corriger les occurrences SERIAL dans init.sql
    echo   3. Re-executer ce test pour verification
    echo.
    echo OUTILS DISPONIBLES:
    if exist "fix_sql_complete.py" echo   - python fix_sql_complete.py (pour corriger SQL)
    if exist "fix_all_remaining_issues.py" echo   - python fix_all_remaining_issues.py (pour corriger Python)
)

echo.
echo FICHIERS TESTES:
if exist "src\main.py" echo   📄 src\main.py
if exist "src\config.py" echo   📄 src\config.py
if exist "src\database\db_connection.py" echo   📄 src\database\db_connection.py
if exist "src\auth\user_management.py" echo   📄 src\auth\user_management.py
if exist "src\database\migrations\init.sql" echo   📄 src\database\migrations\init.sql

echo.
echo [%TIME%] Test simple termine.
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
