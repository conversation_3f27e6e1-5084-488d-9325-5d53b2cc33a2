from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QFileDialog
)
import os

class DocumentWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Gestion et Scan des Documents")
        self.resize(1200, 700)
        layout = QVBoxLayout(self)
        self.table = QTableWidget(0, 10)
        self.table.setHorizontalHeaderLabels([
            "Module lié", "Type doc", "Date doc", "Objet", "Référence", "Fichier", "Code-barres", "Observation", "Synchronisé", "Utilisateur"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter Document")
        self.scan_btn = QPushButton("Scanner Code-barres")
        self.sync_btn = QPushButton("Synchroniser")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.scan_btn)
        btn_layout.addWidget(self.sync_btn)
        layout.addLayout(btn_layout)

        self.add_btn.clicked.connect(self.ajouter_document)
        self.scan_btn.clicked.connect(self.scanner_codebarre)
        self.sync_btn.clicked.connect(self.synchroniser_documents)

    def ajouter_document(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter un document")
        form = QFormLayout(dialog)
        module = QComboBox(); module.addItems([
            "Immobilisation", "Réforme", "Inventaire", "Fournisseur", "Facture", "Achat", "Vente", "Autre"
        ])
        doc_type = QComboBox(); doc_type.addItems(["PV", "Rapport", "Facture", "Photo", "Courrier", "Autre"])
        doc_date = QLineEdit()
        doc_objet = QLineEdit()
        doc_ref = QLineEdit()
        doc_file = QLineEdit()
        btn_file = QPushButton("Choisir fichier")
        doc_codebarre = QLineEdit()
        doc_obs = QLineEdit()
        utilisateur = QLineEdit()
        def choisir_fichier():
            file, _ = QFileDialog.getOpenFileName(dialog, "Sélectionner un document", "", "Tous les fichiers (*)")
            if file:
                doc_file.setText(file)
        btn_file.clicked.connect(choisir_fichier)
        form.addRow("Module lié:", module)
        form.addRow("Type de document:", doc_type)
        form.addRow("Date du document:", doc_date)
        form.addRow("Objet:", doc_objet)
        form.addRow("Référence:", doc_ref)
        form.addRow("Fichier:", doc_file)
        form.addRow("", btn_file)
        form.addRow("Code-barres:", doc_codebarre)
        form.addRow("Observation:", doc_obs)
        form.addRow("Utilisateur:", utilisateur)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        if dialog.exec() == 1:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(module.currentText()))
            self.table.setItem(row, 1, QTableWidgetItem(doc_type.currentText()))
            self.table.setItem(row, 2, QTableWidgetItem(doc_date.text()))
            self.table.setItem(row, 3, QTableWidgetItem(doc_objet.text()))
            self.table.setItem(row, 4, QTableWidgetItem(doc_ref.text()))
            self.table.setItem(row, 5, QTableWidgetItem(os.path.basename(doc_file.text())))
            self.table.setItem(row, 6, QTableWidgetItem(doc_codebarre.text()))
            self.table.setItem(row, 7, QTableWidgetItem(doc_obs.text()))
            self.table.setItem(row, 8, QTableWidgetItem("Oui"))  # Simuler synchronisation
            self.table.setItem(row, 9, QTableWidgetItem(utilisateur.text()))
            QMessageBox.information(dialog, "Document", "Document ajouté et synchronisé (à compléter selon la base de données).")

    def scanner_codebarre(self):
        from PySide6.QtWidgets import QInputDialog
        code, ok = QInputDialog.getText(self, "Scan code-barres", "Scannez ou saisissez le code-barres du document :")
        if ok and code:
            QMessageBox.information(self, "Code-barres", f"Code-barres scanné : {code}")

    def synchroniser_documents(self):
        QMessageBox.information(self, "Synchronisation", "Synchronisation des documents effectuée (à compléter selon la base de données).")
