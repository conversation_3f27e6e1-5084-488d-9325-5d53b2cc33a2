import os
import json
import logging

class Config:
    """Configuration settings for the application."""

    # Security settings
    SECRET_KEY = os.getenv("SECRET_KEY", "your_secret_key")
    PASSWORD_HASHING_ALGORITHM = "bcrypt"

    # Authentication settings
    SESSION_TIMEOUT = int(os.getenv("SESSION_TIMEOUT", 30))  # in minutes

    # Logging settings
    LOGGING_LEVEL = os.getenv("LOGGING_LEVEL", "INFO")
    LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Database settings
    SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL", "sqlite:///gestion_immob.db")
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    @staticmethod
    def init_app(app):
        """Initialize the app with the configuration."""
        app.config.from_object(Config)

    @staticmethod
    def load():
        config_path = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "config.json"))
        default_config = {
            "SECRET_KEY": "your_secret_key",
            "SESSION_TIMEOUT": 30,
            "LOGGING_LEVEL": "INFO",
            "LOGGING_FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "DATABASE_URL": "sqlite:///gestion_immob.db",
            "LANGUE": "fr",
            "THEME": "clair",
            "SYNCHRONISATION_AUTO": True,
            "SYNCHRONISATION_INTERVALLE_MIN": 10,
            "PERFORMANCE_MODE": "optimise",
            "BIGDATA_MODE": True,
            "IA_MODELE": "DeepSeek",
            "CLOUD_SYNC": True,
            "CLOUD_PROVIDER": "aws",
            "CLOUD_BUCKET": "immob-sync",
            "MULTI_LANGUES": ["fr", "en", "ar", "es", "de", "zh", "ru"],
            "MODULES_ACTIFS": [
                "inventaire", "fournisseur", "recherche", "reforme", "document", "impression", "erp", "calculatrice"
            ]
        }
        if not os.path.exists(config_path):
            config_dir = os.path.dirname(config_path)
            if config_dir and not os.path.exists(config_dir):
                try:
                    os.makedirs(config_dir, exist_ok=True)
                except Exception as e:
                    raise RuntimeError(f"Could not create config directory at {config_dir}: {e}")
            try:
                with open(config_path, "w", encoding="utf-8") as config_file:
                    json.dump(default_config, config_file, indent=4, ensure_ascii=False)
            except Exception as e:
                raise RuntimeError(f"Could not create config file at {config_path}: {e}")
            return default_config
        try:
            with open(config_path, "r", encoding="utf-8") as config_file:
                config = json.load(config_file)
                # Synchronisation des clés manquantes
                for k, v in default_config.items():
                    if k not in config:
                        config[k] = v
                return config
        except (json.JSONDecodeError, ValueError):
            config_dir = os.path.dirname(config_path)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            try:
                with open(config_path, "w", encoding="utf-8") as config_file:
                    json.dump(default_config, config_file, indent=4, ensure_ascii=False)
            except FileNotFoundError:
                raise RuntimeError(f"Could not create config file at {config_path}")
            return default_config

    # Class variable to hold loaded configuration
    CONFIG = None

    @classmethod
    def get(cls, key, default=None):
        """Get a configuration value from the loaded config."""
        if cls.CONFIG is None:
            cls.CONFIG = cls.load()
        return cls.CONFIG.get(key, default)

config = Config.load()
log_level_str = config.get("LOGGING_LEVEL", "INFO")
log_level = getattr(logging, log_level_str.upper(), logging.INFO)
logging.basicConfig(
    level=log_level,
    format=config.get("LOGGING_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
)
logging.getLogger().info(f"Config loaded and synchronised: {config}")