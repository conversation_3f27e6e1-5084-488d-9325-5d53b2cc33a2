<!-- Generated by SpecStory -->

# /fix Le type de « figure » est partiellement incon (2025-06-08 12:33:04)

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l'avertissement sur le type de `figure` dans matplotlib, il suffit d'ignorer la variable de retour de `plt.figure()` si elle n'est pas utilisée, ou d'ajouter un commentaire pour indiquer que le type de retour n'est pas pertinent ici. Cela satisfera Pylance/mypy et n'affectera pas l'exécution.

Voici la modification recommandée : ajoutez un commentaire `# type: ignore` à la ligne concernée.

````python
# ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))  # type: ignore
                plt.bar([str(k) for k in stats_categorie.keys()], [int(v) for v in stats_categorie.values()])
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))  # type: ignore
                plt.bar([str(k) for k in stats_statut.keys()], [int(v) for v in stats_statut.values()])
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

---

