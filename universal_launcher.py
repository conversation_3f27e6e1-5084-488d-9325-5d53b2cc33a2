#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LANCEUR UNIVERSEL - AUTOMATISATION COMPLÈTE
Lance tous les outils et corrections automatiquement
"""

import os
import sys
import subprocess
import platform
import time
from pathlib import Path
from typing import List, Dict, Any

class UniversalLauncher:
    def __init__(self):
        self.system = platform.system()
        self.results: Dict[str, bool] = {}
        self.start_time = time.time()
        
    def check_prerequisites(self) -> bool:
        """Vérifie les prérequis système"""
        print("🔍 VÉRIFICATION DES PRÉREQUIS")
        print("=" * 40)
        
        checks = {
            'Python': self.check_python(),
            'Fichier main.py': Path('src/main.py').exists(),
            'Requirements.txt': Path('requirements.txt').exists()
        }
        
        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"{status} {check_name}")
        
        all_passed = all(checks.values())
        
        if all_passed:
            print("\n✅ Tous les prérequis sont satisfaits")
        else:
            print("\n❌ Certains prérequis manquent")
        
        return all_passed
    
    def check_python(self) -> bool:
        """Vérifie la version Python"""
        try:
            version = sys.version_info
            return version >= (3, 8)
        except:
            return False
    
    def run_master_automation(self) -> bool:
        """Lance l'automatisation maître"""
        print("\n🚀 LANCEMENT DE L'AUTOMATISATION MAÎTRE")
        print("=" * 50)
        
        try:
            result = subprocess.run([
                sys.executable, 'master_automation.py'
            ], timeout=300)  # 5 minutes max
            
            success = result.returncode == 0
            self.results['master_automation'] = success
            
            if success:
                print("✅ Automatisation maître terminée avec succès")
            else:
                print("❌ Erreur dans l'automatisation maître")
            
            return success
        except subprocess.TimeoutExpired:
            print("⏰ Timeout de l'automatisation maître")
            self.results['master_automation'] = False
            return False
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.results['master_automation'] = False
            return False
    
    def run_orchestrator(self) -> bool:
        """Lance l'orchestrateur maître"""
        print("\n🎼 LANCEMENT DE L'ORCHESTRATEUR")
        print("=" * 40)
        
        if not Path('master_orchestrator.py').exists():
            print("⚠️ Orchestrateur non créé - création en cours...")
            return False
        
        try:
            result = subprocess.run([
                sys.executable, 'master_orchestrator.py'
            ], timeout=180)  # 3 minutes max
            
            success = result.returncode == 0
            self.results['orchestrator'] = success
            
            if success:
                print("✅ Orchestration terminée avec succès")
            else:
                print("❌ Erreur dans l'orchestration")
            
            return success
        except subprocess.TimeoutExpired:
            print("⏰ Timeout de l'orchestrateur")
            self.results['orchestrator'] = False
            return False
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.results['orchestrator'] = False
            return False
    
    def final_validation(self) -> bool:
        """Validation finale complète"""
        print("\n🔍 VALIDATION FINALE")
        print("=" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, 'ultimate_validator.py'
            ], timeout=60)
            
            success = result.returncode == 0
            self.results['final_validation'] = success
            
            if success:
                print("✅ Validation finale réussie")
            else:
                print("❌ Problèmes détectés lors de la validation")
            
            return success
        except subprocess.TimeoutExpired:
            print("⏰ Timeout de la validation")
            self.results['final_validation'] = False
            return False
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.results['final_validation'] = False
            return False
    
    def launch_application(self) -> bool:
        """Lance l'application GestImmob"""
        print("\n🚀 LANCEMENT DE GESTIMMOB")
        print("=" * 35)
        
        if not Path('start_gestimmob.py').exists():
            print("⚠️ Script de démarrage non trouvé - démarrage direct")
            script_path = 'src/main.py'
        else:
            script_path = 'start_gestimmob.py'
        
        try:
            print(f"Démarrage de {script_path}...")
            
            # Lancer en arrière-plan
            process = subprocess.Popen([
                sys.executable, script_path
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Attendre un peu pour voir si ça démarre
            time.sleep(3)
            
            if process.poll() is None:
                print("✅ GestImmob démarré avec succès")
                print(f"📝 PID: {process.pid}")
                self.results['application'] = True
                return True
            else:
                print("❌ Erreur au démarrage de GestImmob")
                self.results['application'] = False
                return False
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.results['application'] = False
            return False
    
    def create_status_report(self) -> None:
        """Crée un rapport de statut"""
        print("\n📋 RAPPORT DE STATUT FINAL")
        print("=" * 40)
        
        total_time = time.time() - self.start_time
        
        print(f"⏱️ Temps total: {total_time:.1f} secondes")
        print(f"🖥️ Système: {self.system}")
        print(f"🐍 Python: {sys.version.split()[0]}")
        
        print("\n📊 RÉSULTATS:")
        success_count = 0
        
        for step, result in self.results.items():
            status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
            print(f"  {step:20}: {status}")
            if result:
                success_count += 1
        
        success_rate = (success_count / len(self.results)) * 100 if self.results else 0
        
        print(f"\n📈 Taux de réussite: {success_rate:.1f}%")
        
        # Sauvegarde du rapport
        try:
            report = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'system': self.system,
                'python_version': sys.version.split()[0],
                'total_time': total_time,
                'results': self.results,
                'success_rate': success_rate
            }
            
            import json
            with open('launch_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print("💾 Rapport sauvegardé: launch_report.json")
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde rapport: {e}")
    
    def run_complete_launch(self) -> bool:
        """Lance le processus complet"""
        print("🌟 LANCEUR UNIVERSEL GESTIMMOB")
        print("Automatisation complète avec tous les moyens")
        print("=" * 60)
        
        # Étapes du lancement
        steps = [
            ("Vérification prérequis", self.check_prerequisites),
            ("Automatisation maître", self.run_master_automation),
            ("Orchestration", self.run_orchestrator),
            ("Validation finale", self.final_validation),
            ("Lancement application", self.launch_application)
        ]
        
        overall_success = True
        
        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}...")
            
            try:
                success = step_func()
                if not success:
                    overall_success = False
                    
                    # Continuer même en cas d'échec pour certaines étapes
                    if step_name in ["Automatisation maître", "Orchestration"]:
                        print(f"⚠️ {step_name} échoué mais continuation...")
                    elif step_name == "Vérification prérequis":
                        print("❌ Prérequis manquants - arrêt")
                        break
                        
            except Exception as e:
                print(f"❌ Erreur dans {step_name}: {e}")
                overall_success = False
        
        # Rapport final
        self.create_status_report()
        
        if overall_success:
            print("\n🎉 LANCEMENT COMPLET RÉUSSI !")
            print("✅ GestImmob est prêt à l'utilisation")
        else:
            print("\n⚠️ Lancement partiellement réussi")
            print("📝 Consultez le rapport pour plus de détails")
        
        return overall_success

def main():
    """Fonction principale"""
    launcher = UniversalLauncher()
    success = launcher.run_complete_launch()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
