#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Module de sécurité pour GestImmob"""

import hashlib
import secrets
from cryptography.fernet import Fernet
from typing import str, bytes
import logging

logger = logging.getLogger(__name__)

class SecurityManager:
    """Gestionnaire de sécurité"""

    def __init__(self):
        self.key = None

    def generate_key(self) -> bytes:
        """Génère une clé de chiffrement"""
        self.key = Fernet.generate_key()
        return self.key

    def encrypt_data(self, data: str) -> bytes:
        """Chiffre des données"""
        if not self.key:
            self.generate_key()
        f = Fernet(self.key)
        return f.encrypt(data.encode())

    def decrypt_data(self, encrypted_data: bytes) -> str:
        """Déchiffre des données"""
        if not self.key:
            raise ValueError("Aucune clé de chiffrement")
        f = Fernet(self.key)
        return f.decrypt(encrypted_data).decode()

    def hash_password(self, password: str) -> str:
        """Hash un mot de passe"""
        salt = secrets.token_hex(16)
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()

security_manager = SecurityManager()
