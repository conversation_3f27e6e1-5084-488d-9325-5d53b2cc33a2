{"version": "2.0.0", "date_import": "2025-06-09T09:28:24.205531", "source_databases": ["gestimmob.db", "gestimmob_complet.db", "gestimmob_complet_final.db", "gestimmob_final.db", "gestimmob_simple.db", "immobilisations.db", "votre_base.db"], "secteurs_personnalises": [{"nom": "Informatique & Technologies", "description": "Équipements informatiques avec critères techniques avancés", "couleur": "#2196F3", "icone": "💻", "criteres": []}, {"nom": "Véhicules & Transport", "description": "Gestion complète du parc automobile avec suivi technique", "couleur": "#FF9800", "icone": "🚗", "criteres": []}, {"nom": "Mobilier & Aménagement", "description": "Gestion du mobilier avec critères ergonomiques et esthétiques", "couleur": "#8BC34A", "icone": "🪑", "criteres": []}, {"nom": "Équipements Industriels", "description": "Machines et équipements industriels avec suivi maintenance", "couleur": "#F44336", "icone": "⚙️", "criteres": []}, {"nom": "1", "description": "Secteur 1", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "2", "description": "Secteur 2", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "3", "description": "Secteur 3", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "4", "description": "Secteur 4", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "5", "description": "Secteur 5", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "6", "description": "Secteur 6", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "7", "description": "Secteur 7", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "8", "description": "Secteur 8", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "9", "description": "Secteur 9", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "10", "description": "Secteur 10", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "11", "description": "Secteur 11", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "12", "description": "Secteur 12", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "13", "description": "Secteur 13", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "14", "description": "Secteur 14", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "15", "description": "Secteur 15", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "16", "description": "Secteur 16", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "17", "description": "Secteur 17", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "18", "description": "Se<PERSON>eur 18", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "19", "description": "<PERSON><PERSON><PERSON> 19", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "20", "description": "Secteur 20", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "21", "description": "Secteur 21", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "22", "description": "Secteur 22", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "23", "description": "Se<PERSON>eur 23", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "24", "description": "<PERSON><PERSON><PERSON> 24", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "25", "description": "Secteur 25", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "26", "description": "<PERSON><PERSON><PERSON> 26", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "27", "description": "<PERSON><PERSON><PERSON> 27", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "28", "description": "<PERSON><PERSON><PERSON> 28", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "29", "description": "<PERSON><PERSON><PERSON> 29", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "30", "description": "Se<PERSON>eur 30", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "31", "description": "Secteur 31", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "32", "description": "Secteur 32", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "33", "description": "<PERSON><PERSON><PERSON> 33", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "34", "description": "<PERSON><PERSON><PERSON> 34", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "35", "description": "<PERSON><PERSON><PERSON> 35", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "36", "description": "<PERSON><PERSON><PERSON> 36", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "37", "description": "<PERSON><PERSON><PERSON> 37", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "38", "description": "<PERSON><PERSON><PERSON> 38", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "39", "description": "<PERSON><PERSON><PERSON> 39", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "40", "description": "<PERSON><PERSON>eur 40", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "41", "description": "<PERSON><PERSON><PERSON> 41", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "42", "description": "<PERSON><PERSON><PERSON> 42", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "43", "description": "<PERSON><PERSON><PERSON> 43", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "44", "description": "<PERSON><PERSON><PERSON> 44", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "45", "description": "<PERSON><PERSON><PERSON> 45", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "46", "description": "<PERSON><PERSON><PERSON> 46", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "47", "description": "<PERSON><PERSON><PERSON> 47", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "48", "description": "<PERSON><PERSON><PERSON> 48", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "49", "description": "<PERSON><PERSON><PERSON> 49", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "50", "description": "<PERSON><PERSON><PERSON> 50", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "51", "description": "<PERSON><PERSON><PERSON> 51", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "52", "description": "<PERSON><PERSON><PERSON> 52", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "53", "description": "<PERSON><PERSON><PERSON> 53", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "54", "description": "<PERSON><PERSON><PERSON> 54", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "55", "description": "<PERSON><PERSON><PERSON> 55", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "56", "description": "<PERSON><PERSON><PERSON> 56", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "57", "description": "<PERSON><PERSON><PERSON> 57", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "58", "description": "<PERSON><PERSON><PERSON> 58", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "59", "description": "<PERSON><PERSON><PERSON> 59", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "60", "description": "<PERSON><PERSON><PERSON> 60", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "61", "description": "<PERSON><PERSON><PERSON> 61", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "62", "description": "<PERSON><PERSON><PERSON> 62", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "63", "description": "<PERSON><PERSON><PERSON> 63", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "64", "description": "Secteur 64", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "65", "description": "<PERSON><PERSON><PERSON> 65", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "66", "description": "<PERSON><PERSON><PERSON> 66", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "67", "description": "<PERSON><PERSON><PERSON> 67", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "68", "description": "<PERSON><PERSON><PERSON> 68", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "69", "description": "<PERSON><PERSON><PERSON> 69", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "70", "description": "<PERSON><PERSON><PERSON> 70", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "71", "description": "<PERSON><PERSON><PERSON> 71", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "72", "description": "<PERSON><PERSON><PERSON> 72", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "73", "description": "<PERSON><PERSON><PERSON> 73", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "74", "description": "<PERSON><PERSON><PERSON> 74", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "75", "description": "<PERSON><PERSON><PERSON> 75", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "76", "description": "<PERSON><PERSON><PERSON> 76", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "77", "description": "<PERSON><PERSON><PERSON> 77", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "78", "description": "<PERSON><PERSON>eur 78", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "79", "description": "<PERSON><PERSON><PERSON> 79", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "80", "description": "<PERSON><PERSON><PERSON> 80", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "81", "description": "<PERSON><PERSON><PERSON> 81", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "82", "description": "<PERSON><PERSON>eur 82", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "83", "description": "<PERSON><PERSON>eur 83", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "84", "description": "Secteur 84", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "85", "description": "<PERSON><PERSON><PERSON> 85", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "86", "description": "Secteur 86", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "87", "description": "<PERSON><PERSON><PERSON> 87", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "88", "description": "<PERSON><PERSON>eur 88", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "89", "description": "<PERSON><PERSON>eur 89", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "90", "description": "<PERSON><PERSON><PERSON> 90", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "91", "description": "<PERSON><PERSON><PERSON> 91", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "92", "description": "<PERSON><PERSON><PERSON> 92", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "93", "description": "<PERSON><PERSON><PERSON> 93", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "94", "description": "Secteur 94", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "95", "description": "<PERSON><PERSON><PERSON> 95", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "96", "description": "Secteur 96", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "97", "description": "<PERSON><PERSON>eur 97", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "98", "description": "Secteur 98", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "99", "description": "Secteur 99", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "100", "description": "Secteur 100", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "101", "description": "Secteur 101", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "102", "description": "Secteur 102", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "103", "description": "Secteur 103", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "104", "description": "<PERSON><PERSON><PERSON> 104", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "105", "description": "Secteur 105", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "106", "description": "Secteur 106", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "107", "description": "<PERSON><PERSON>eur 107", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "108", "description": "<PERSON><PERSON>eur 108", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "109", "description": "<PERSON><PERSON>eur 109", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "110", "description": "Secteur 110", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "111", "description": "<PERSON><PERSON><PERSON> 111", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "112", "description": "<PERSON><PERSON><PERSON> 112", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "113", "description": "<PERSON><PERSON><PERSON> 113", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "114", "description": "<PERSON><PERSON><PERSON> 114", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "115", "description": "<PERSON><PERSON>eur 115", "couleur": "#1976d2", "icone": "🏢", "criteres": []}, {"nom": "116", "description": "<PERSON><PERSON><PERSON> 116", "couleur": "#1976d2", "icone": "🏢", "criteres": []}], "parametres_application": {"gestimmob_complet_final.db_parametres": {"1": "entreprise_nom", "2": "entreprise_adresse", "3": "tva_defaut", "4": "delai_paiement", "5": "duree_amortissement"}}, "utilisateurs": [{"id": 1, "username": "admin", "role": "admin", "full_name": "Administrateur", "email": null, "last_login": null, "is_active": 1}, {"id": 1, "username": "admin", "role": "admin", "full_name": "Administrateur", "email": "<EMAIL>", "telephone": null, "departement": null, "last_login": null, "is_active": 1, "preferences": null}, {"id": 1, "username": "admin", "nom": "Administrateur", "prenom": "Système", "email": null, "role": "admin", "permissions": "ALL", "derniere_connexion": null, "is_active": 1, "date_creation": "2025-06-08 22:00:13"}, {"id": 1, "username": "admin", "role": "admin"}, {"id": 1, "username": "admin", "role": "administrator", "date_creation": "2025-06-08 22:35:39"}, {"id": 1, "username": "admin", "attempt_time": "2025-06-07 18:31:33", "success": 0}, {"id": 2, "username": "admin", "attempt_time": "2025-06-07 18:31:57", "success": 1}, {"id": 3, "username": "admin", "attempt_time": "2025-06-07 18:54:29", "success": 1}, {"id": 4, "username": "admin", "attempt_time": "2025-06-08 01:17:41", "success": 1}], "exemples_donnees": [{"id": 1, "designation": "Ordinateur portable Dell Latitude 5520", "valeur": 1200.0, "annee": 2023, "localisation": "Bureau 101", "secteur": "Informatique & Technologies", "observation": "Ordinateur de direction avec configuration haute performance", "code_barre": null, "etat": "<PERSON>", "responsable": null, "date_acquisition": null, "duree_amortissement": 5, "valeur_residuelle": 0.0, "date_creation": "2025-06-09 08:02:20", "secteur_personnalise_id": null, "numero_serie": "*********", "marque": "Dell", "modele": "Latitude 5520", "garantie_jusqu_au": null, "derniere_maintenance": null, "prochaine_maintenance": null}, {"id": 2, "designation": "Véhicule de service Renault Clio", "valeur": 18500.0, "annee": 2022, "localisation": "Parking entreprise", "secteur": "Véhicules & Transport", "observation": "Véhicule de service pour déplacements commerciaux", "code_barre": null, "etat": "<PERSON>", "responsable": null, "date_acquisition": null, "duree_amortissement": 5, "valeur_residuelle": 0.0, "date_creation": "2025-06-09 08:02:20", "secteur_personnalise_id": null, "numero_serie": "VF1RJA00123456789", "marque": "Renault", "modele": "Clio V", "garantie_jusqu_au": null, "derniere_maintenance": null, "prochaine_maintenance": null}, {"id": 3, "designation": "Bureau ergonomique réglable", "valeur": 450.0, "annee": 2023, "localisation": "Open space", "secteur": "Mobilier & Aménagement", "observation": "Bureau assis-debout électrique", "code_barre": null, "etat": "<PERSON>", "responsable": null, "date_acquisition": null, "duree_amortissement": 5, "valeur_residuelle": 0.0, "date_creation": "2025-06-09 08:02:20", "secteur_personnalise_id": null, "numero_serie": null, "marque": "Steelcase", "modele": "Series 7", "garantie_jusqu_au": null, "derniere_maintenance": null, "prochaine_maintenance": null}, {"id": 4, "designation": "Ordinateur portable Dell Latitude 5520", "valeur": 1200.0, "annee": 2023, "localisation": "Bureau 101", "secteur": "Informatique & Technologies", "observation": "Ordinateur de direction avec configuration haute performance", "code_barre": null, "etat": "<PERSON>", "responsable": null, "date_acquisition": null, "duree_amortissement": 5, "valeur_residuelle": 0.0, "date_creation": "2025-06-09 08:02:45", "secteur_personnalise_id": null, "numero_serie": "*********", "marque": "Dell", "modele": "Latitude 5520", "garantie_jusqu_au": null, "derniere_maintenance": null, "prochaine_maintenance": null}, {"id": 5, "designation": "Véhicule de service Renault Clio", "valeur": 18500.0, "annee": 2022, "localisation": "Parking entreprise", "secteur": "Véhicules & Transport", "observation": "Véhicule de service pour déplacements commerciaux", "code_barre": null, "etat": "<PERSON>", "responsable": null, "date_acquisition": null, "duree_amortissement": 5, "valeur_residuelle": 0.0, "date_creation": "2025-06-09 08:02:45", "secteur_personnalise_id": null, "numero_serie": "VF1RJA00123456789", "marque": "Renault", "modele": "Clio V", "garantie_jusqu_au": null, "derniere_maintenance": null, "prochaine_maintenance": null}, {"id": 6, "designation": "Bureau ergonomique réglable", "valeur": 450.0, "annee": 2023, "localisation": "Open space", "secteur": "Mobilier & Aménagement", "observation": "Bureau assis-debout électrique", "code_barre": null, "etat": "<PERSON>", "responsable": null, "date_acquisition": null, "duree_amortissement": 5, "valeur_residuelle": 0.0, "date_creation": "2025-06-09 08:02:45", "secteur_personnalise_id": null, "numero_serie": null, "marque": "Steelcase", "modele": "Series 7", "garantie_jusqu_au": null, "derniere_maintenance": null, "prochaine_maintenance": null}, {"id": 1, "designation": "Ordinateur Portable Dell", "marque": "Dell", "modele": "Latitude 5520", "numero_serie": "DL123456", "code_barre": null, "valeur_acquisition": 1200.0, "valeur_residuelle": 0.0, "duree_amortissement": 5, "date_acquisition": "2025-06-08", "localisation": null, "secteur": "Informatique", "etat": "<PERSON>", "responsable": null, "observations": null, "date_creation": "2025-06-08 22:35:39"}, {"id": 2, "designation": "Imprimante HP", "marque": "HP", "modele": "LaserJet Pro", "numero_serie": "HP789012", "code_barre": null, "valeur_acquisition": 350.0, "valeur_residuelle": 0.0, "duree_amortissement": 5, "date_acquisition": "2025-06-08", "localisation": null, "secteur": "Informatique", "etat": "Excellent", "responsable": null, "observations": null, "date_creation": "2025-06-08 22:35:39"}, {"id": 3, "designation": "Bureau Ergonomique", "marque": "IKEA", "modele": "BEKANT", "numero_serie": "IK345678", "code_barre": null, "valeur_acquisition": 180.0, "valeur_residuelle": 0.0, "duree_amortissement": 5, "date_acquisition": "2025-06-08", "localisation": null, "secteur": "Mobilier", "etat": "<PERSON>", "responsable": null, "observations": null, "date_creation": "2025-06-08 22:35:39"}]}