#!/usr/bin/env python3
"""
🏢 GestImmob Simple v5.0.0 - Version Corrigée et Fonctionnelle
Logiciel de gestion immobilière avec interface moderne et modules complets
Toutes les erreurs corrigées, interface verticale alignée, modules distincts
"""

import sys
import sqlite3
import hashlib
import json
import csv
import os
from datetime import datetime, date
from pathlib import Path

# Imports PySide6
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QMessageBox, QFormLayout, 
    QGroupBox, QStatusBar, QFrame, QComboBox, QSpinBox, QDoubleSpinBox, 
    QInputDialog, QDateEdit, QCheckBox, QListWidget, QListWidgetItem,
    QGridLayout, QScrollArea, QProgressBar, QSplitter, QFileDialog,
    Q<PERSON><PERSON>cked<PERSON><PERSON>t, Q<PERSON>ree<PERSON>idget, QTreeWidgetItem, QTabWidget,
    QToolBar, QMenuBar, QMenu, QDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt, QTimer, QDate, QSize, Signal
from PySide6.QtGui import QAction, QIcon, QFont, QPixmap, QPainter, QColor, QLinearGradient

# ===== CONFIGURATION =====

class AppConfig:
    """Configuration de l'application"""
    
    APP_NAME = "GestImmob Professional"
    APP_VERSION = "5.0.0"
    APP_COMPANY = "GestImmob Solutions"
    
    # Couleurs modernes et professionnelles
    COLORS = {
        'primary': '#2563EB',      # Bleu moderne
        'secondary': '#7C3AED',    # Violet
        'success': '#059669',      # Vert
        'warning': '#D97706',      # Orange
        'danger': '#DC2626',       # Rouge
        'dark': '#1F2937',         # Gris foncé
        'light': '#F9FAFB',        # Gris clair
        'white': '#FFFFFF',        # Blanc
        'text': '#111827',         # Texte
        'border': '#D1D5DB',       # Bordures
        'sidebar': '#374151',      # Sidebar
    }
    
    # Modules avec leurs critères spécifiques
    MODULES = {
        'dashboard': {
            'name': 'Tableau de Bord',
            'icon': '[STATS]',
            'description': 'Vue d\'ensemble avec KPI et statistiques',
            'fields': ['kpi_patrimoine', 'kpi_valeur', 'kpi_fournisseurs', 'graphiques'],
            'actions': ['actualiser', 'exporter', 'configurer']
        },
        'properties': {
            'name': 'Gestion des Biens',
            'icon': '🏠',
            'description': 'Gestion complète du patrimoine immobilier',
            'fields': [
                'designation', 'marque', 'modele', 'numero_serie', 'code_barre',
                'valeur_acquisition', 'valeur_residuelle', 'duree_amortissement',
                'date_acquisition', 'localisation', 'secteur', 'etat', 'responsable'
            ],
            'actions': ['ajouter', 'modifier', 'supprimer', 'exporter', 'importer']
        },
        'inventory': {
            'name': 'Inventaire',
            'icon': '📦',
            'description': 'Inventaire physique et suivi des stocks',
            'fields': ['campagne', 'date_debut', 'date_fin', 'responsable', 'ecarts'],
            'actions': ['creer_campagne', 'scanner', 'valider', 'rapport']
        },
        'suppliers': {
            'name': 'Fournisseurs',
            'icon': '🏢',
            'description': 'Gestion des fournisseurs et partenaires',
            'fields': [
                'raison_sociale', 'siret', 'adresse', 'contact', 'telephone',
                'email', 'secteur', 'conditions_paiement', 'evaluation'
            ],
            'actions': ['ajouter', 'modifier', 'evaluer', 'desactiver']
        },
        'clients': {
            'name': 'Clients',
            'icon': '👥',
            'description': 'Gestion de la clientèle',
            'fields': ['nom', 'prenom', 'entreprise', 'adresse', 'contact'],
            'actions': ['ajouter', 'modifier', 'convertir']
        },
        'invoicing': {
            'name': 'Facturation',
            'icon': '📄',
            'description': 'Gestion de la facturation',
            'fields': ['numero', 'date', 'client', 'montant', 'statut'],
            'actions': ['creer', 'valider', 'envoyer', 'encaisser']
        }
    }

class GestImmobSimple(QMainWindow):
    """Application GestImmob Simple v5.0.0"""
    
    # Signaux
    module_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        
        # Configuration
        self.current_module = 'dashboard'
        self.current_user = {'username': 'admin', 'role': 'administrator'}
        
        # Initialisation
        self.init_database()
        self.init_ui()
        self.apply_modern_theme()
        self.load_initial_data()
        
        # Configuration de la fenêtre
        self.setWindowTitle(f"{AppConfig.APP_NAME} v{AppConfig.APP_VERSION}")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        print(f"[LANCE] {AppConfig.APP_NAME} v{AppConfig.APP_VERSION} initialisé avec succès!")
        
    def init_database(self):
        """Initialise la base de données"""
        self.db_path = "gestimmob_simple.db"
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # Table des biens immobiliers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS biens_immobiliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                marque TEXT,
                modele TEXT,
                numero_serie TEXT,
                code_barre TEXT UNIQUE,
                valeur_acquisition REAL NOT NULL,
                valeur_residuelle REAL DEFAULT 0,
                duree_amortissement INTEGER DEFAULT 5,
                date_acquisition DATE NOT NULL,
                localisation TEXT,
                secteur TEXT NOT NULL,
                etat TEXT DEFAULT 'Bon',
                responsable TEXT,
                observations TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des fournisseurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                raison_sociale TEXT NOT NULL,
                siret TEXT,
                adresse TEXT,
                contact TEXT,
                telephone TEXT,
                email TEXT,
                secteur TEXT,
                conditions_paiement TEXT DEFAULT '30 jours',
                evaluation INTEGER DEFAULT 5,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT,
                entreprise TEXT,
                adresse TEXT,
                telephone TEXT,
                email TEXT,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Utilisateur admin
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS utilisateurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT DEFAULT 'utilisateur',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Créer admin si n'existe pas
        cursor.execute("SELECT COUNT(*) FROM utilisateurs WHERE username='admin'")
        if cursor.fetchone()[0] == 0:
            admin_password = hashlib.sha256("Admin@2024".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO utilisateurs (username, password_hash, role)
                VALUES (?, ?, ?)
            ''', ("admin", admin_password, "administrator"))
        
        self.conn.commit()
        print("✅ Base de données initialisée")
        
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar verticale
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # Zone de contenu
        self.content_area = QWidget()
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        # En-tête
        self.create_header()
        self.content_layout.addWidget(self.header)
        
        # Zone des modules
        self.stacked_widget = QStackedWidget()
        self.content_layout.addWidget(self.stacked_widget)
        
        # Créer tous les modules
        self.create_all_modules()
        
        main_layout.addWidget(self.content_area, 1)
        
        # Barre de statut
        self.create_status_bar()
        
        # Menu
        self.create_menu_bar()
        
    def create_sidebar(self):
        """Crée la sidebar verticale moderne"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(280)
        self.sidebar.setObjectName("sidebar")
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # En-tête avec logo
        header_widget = QWidget()
        header_widget.setObjectName("sidebar_header")
        header_widget.setFixedHeight(80)
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 15, 20, 15)
        
        # Logo
        logo_label = QLabel("🏠")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_label.setStyleSheet("font-size: 32pt; color: #2563EB;")
        header_layout.addWidget(logo_label)
        
        # Titre
        title_label = QLabel(AppConfig.APP_NAME)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("sidebar_title")
        header_layout.addWidget(title_label)
        
        sidebar_layout.addWidget(header_widget)
        
        # Zone de navigation
        nav_scroll = QScrollArea()
        nav_scroll.setWidgetResizable(True)
        nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        nav_scroll.setObjectName("nav_scroll")
        
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(15, 15, 15, 15)
        nav_layout.setSpacing(8)
        
        # Boutons de navigation verticalement alignés
        self.nav_buttons = {}
        
        # Groupe Principal
        self.add_nav_group(nav_layout, "GESTION PRINCIPALE")
        self.add_nav_button(nav_layout, 'dashboard', 'Tableau de Bord', True)
        self.add_nav_button(nav_layout, 'properties', 'Gestion des Biens')
        self.add_nav_button(nav_layout, 'inventory', 'Inventaire')
        
        # Groupe Partenaires
        self.add_nav_group(nav_layout, "PARTENAIRES")
        self.add_nav_button(nav_layout, 'suppliers', 'Fournisseurs')
        self.add_nav_button(nav_layout, 'clients', 'Clients')
        
        # Groupe Commercial
        self.add_nav_group(nav_layout, "COMMERCIAL")
        self.add_nav_button(nav_layout, 'invoicing', 'Facturation')
        
        nav_layout.addStretch()
        
        # Boutons d'action en bas
        self.add_nav_group(nav_layout, "ACTIONS")
        
        # Bouton Aide
        help_btn = QPushButton("❓ Aide")
        help_btn.setObjectName("nav_button_help")
        help_btn.clicked.connect(self.show_help)
        nav_layout.addWidget(help_btn)
        
        # Bouton Quitter avec style spécial et détaillé
        quit_btn = QPushButton("🚪 Quitter l'Application")
        quit_btn.setObjectName("nav_button_quit")
        quit_btn.setToolTip("Ferme l'application GestImmob en sauvegardant toutes les données")
        quit_btn.clicked.connect(self.quit_application_detailed)
        nav_layout.addWidget(quit_btn)
        
        nav_scroll.setWidget(nav_widget)
        sidebar_layout.addWidget(nav_scroll)
        
        # Informations utilisateur en bas
        user_widget = QWidget()
        user_widget.setObjectName("sidebar_user")
        user_widget.setFixedHeight(70)
        user_layout = QHBoxLayout(user_widget)
        user_layout.setContentsMargins(20, 15, 20, 15)
        
        user_icon = QLabel("👤")
        user_icon.setStyleSheet("font-size: 18pt; color: #2563EB;")
        user_layout.addWidget(user_icon)
        
        user_info = QVBoxLayout()
        user_name = QLabel(self.current_user['username'])
        user_name.setObjectName("user_name")
        user_role = QLabel(self.current_user['role'])
        user_role.setObjectName("user_role")
        user_info.addWidget(user_name)
        user_info.addWidget(user_role)
        user_layout.addLayout(user_info)
        
        sidebar_layout.addWidget(user_widget)

    def add_nav_group(self, layout, title):
        """Ajoute un groupe de navigation"""
        group_label = QLabel(title)
        group_label.setObjectName("nav_group")
        layout.addWidget(group_label)

    def add_nav_button(self, layout, module_key, title, is_active=False):
        """Ajoute un bouton de navigation verticalement aligné"""
        module_config = AppConfig.MODULES.get(module_key, {})
        icon = module_config.get('icon', '📄')

        button = QPushButton(f"{icon} {title}")
        button.setObjectName("nav_button_active" if is_active else "nav_button")
        button.clicked.connect(lambda: self.switch_module(module_key))

        # Tooltip avec description
        if 'description' in module_config:
            button.setToolTip(module_config['description'])

        layout.addWidget(button)
        self.nav_buttons[module_key] = button

    def create_header(self):
        """Crée l'en-tête du contenu"""
        self.header = QWidget()
        self.header.setObjectName("content_header")
        self.header.setFixedHeight(70)

        header_layout = QHBoxLayout(self.header)
        header_layout.setContentsMargins(30, 15, 30, 15)

        # Titre du module actuel
        self.module_title = QLabel("Tableau de Bord")
        self.module_title.setObjectName("module_title")
        header_layout.addWidget(self.module_title)

        header_layout.addStretch()

        # Informations de session
        session_layout = QVBoxLayout()

        self.datetime_label = QLabel()
        self.datetime_label.setObjectName("datetime_label")
        session_layout.addWidget(self.datetime_label)

        self.session_label = QLabel(f"Session: {self.current_user['username']}")
        self.session_label.setObjectName("session_label")
        session_layout.addWidget(self.session_label)

        header_layout.addLayout(session_layout)

        # Timer pour l'heure
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_datetime)
        self.time_timer.start(1000)
        self.update_datetime()

    def update_datetime(self):
        """Met à jour l'affichage de la date et heure"""
        current_time = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        self.datetime_label.setText(current_time)

    def switch_module(self, module_key):
        """Change de module"""
        # Mettre à jour l'état des boutons
        for key, button in self.nav_buttons.items():
            if key == module_key:
                button.setObjectName("nav_button_active")
                self.current_module = module_key
            else:
                button.setObjectName("nav_button")
            button.style().unpolish(button)
            button.style().polish(button)

        # Mettre à jour le titre
        module_config = AppConfig.MODULES.get(module_key, {})
        module_name = module_config.get('name', 'Module')
        self.module_title.setText(module_name)

        # Changer le widget affiché
        module_index = list(AppConfig.MODULES.keys()).index(module_key)
        self.stacked_widget.setCurrentIndex(module_index)

        # Émettre le signal
        self.module_changed.emit(module_key)

    def create_all_modules(self):
        """Crée tous les modules"""
        # Module Tableau de Bord
        self.dashboard_widget = self.create_dashboard_module()
        self.stacked_widget.addWidget(self.dashboard_widget)

        # Module Gestion des Biens
        self.properties_widget = self.create_properties_module()
        self.stacked_widget.addWidget(self.properties_widget)

        # Module Inventaire
        self.inventory_widget = self.create_inventory_module()
        self.stacked_widget.addWidget(self.inventory_widget)

        # Module Fournisseurs
        self.suppliers_widget = self.create_suppliers_module()
        self.stacked_widget.addWidget(self.suppliers_widget)

        # Module Clients
        self.clients_widget = self.create_clients_module()
        self.stacked_widget.addWidget(self.clients_widget)

        # Module Facturation
        self.invoicing_widget = self.create_invoicing_module()
        self.stacked_widget.addWidget(self.invoicing_widget)

        # Définir le module par défaut
        self.stacked_widget.setCurrentIndex(0)  # Dashboard

    def create_dashboard_module(self):
        """Crée le module tableau de bord avec KPI"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # En-tête du dashboard
        header_group = QGroupBox("[STATS] Tableau de Bord - Vue d'Ensemble")
        header_layout = QVBoxLayout(header_group)

        welcome_label = QLabel(f"Bienvenue dans {AppConfig.APP_NAME} v{AppConfig.APP_VERSION}")
        welcome_label.setStyleSheet(f"font-size: 18pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 10px;")
        header_layout.addWidget(welcome_label)

        layout.addWidget(header_group)

        # KPI Cards en grille
        kpi_group = QGroupBox("📈 Indicateurs Clés de Performance")
        kpi_layout = QGridLayout(kpi_group)

        # Créer les cartes KPI
        self.create_kpi_card(kpi_layout, 0, 0, "🏠 Total Biens", "biens_immobiliers", "COUNT(*)")
        self.create_kpi_card(kpi_layout, 0, 1, "💰 Valeur Totale", "biens_immobiliers", "SUM(valeur_acquisition)")
        self.create_kpi_card(kpi_layout, 0, 2, "🏢 Fournisseurs", "fournisseurs", "COUNT(*)")
        self.create_kpi_card(kpi_layout, 1, 0, "👥 Clients", "clients", "COUNT(*)")
        self.create_kpi_card(kpi_layout, 1, 1, "[STATS] Secteurs", "biens_immobiliers", "COUNT(DISTINCT secteur)")
        self.create_kpi_card(kpi_layout, 1, 2, "⚡ Statut", "", "Opérationnel")

        layout.addWidget(kpi_group)

        # Actions rapides
        actions_group = QGroupBox("[LANCE] Actions Rapides")
        actions_layout = QHBoxLayout(actions_group)

        btn_nouveau_bien = QPushButton("➕ Nouveau Bien")
        btn_nouveau_bien.setObjectName("success")
        btn_nouveau_bien.clicked.connect(lambda: self.switch_module('properties'))
        actions_layout.addWidget(btn_nouveau_bien)

        btn_inventaire = QPushButton("📦 Lancer Inventaire")
        btn_inventaire.clicked.connect(lambda: self.switch_module('inventory'))
        actions_layout.addWidget(btn_inventaire)

        btn_fournisseur = QPushButton("🏢 Nouveau Fournisseur")
        btn_fournisseur.clicked.connect(lambda: self.switch_module('suppliers'))
        actions_layout.addWidget(btn_fournisseur)

        btn_export = QPushButton("📤 Exporter Données")
        btn_export.setObjectName("warning")
        btn_export.clicked.connect(self.export_data)
        actions_layout.addWidget(btn_export)

        actions_layout.addStretch()
        layout.addWidget(actions_group)

        layout.addStretch()

        return widget

    def create_kpi_card(self, layout, row, col, title, table, query):
        """Crée une carte KPI avec données en temps réel"""
        card = QFrame()
        card.setObjectName("kpi_card")
        card.setStyleSheet(f"""
            QFrame#kpi_card {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {AppConfig.COLORS['white']}, stop:1 {AppConfig.COLORS['light']});
                border: 2px solid {AppConfig.COLORS['primary']};
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
                min-height: 100px;
            }}
            QFrame#kpi_card:hover {{
                border-color: {AppConfig.COLORS['secondary']};
                transform: scale(1.02);
            }}
        """)

        card_layout = QVBoxLayout(card)

        # Titre de la carte
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: 700;
            color: {AppConfig.COLORS['primary']};
            margin-bottom: 10px;
        """)
        card_layout.addWidget(title_label)

        # Valeur KPI
        try:
            if table and query != "Opérationnel":
                cursor = self.conn.cursor()
                cursor.execute(f"SELECT {query} FROM {table}")
                result = cursor.fetchone()
                value = result[0] if result and result[0] is not None else 0

                if "SUM" in query:
                    display_value = f"{value:,.2f} €"
                else:
                    display_value = str(value)
            else:
                display_value = "✅ Opérationnel"

        except Exception as e:
            display_value = "Erreur"
            print(f"Erreur KPI {title}: {e}")

        value_label = QLabel(display_value)
        value_label.setStyleSheet(f"""
            font-size: 20pt;
            font-weight: 700;
            color: {AppConfig.COLORS['secondary']};
            text-align: center;
        """)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(value_label)

        layout.addWidget(card, row, col)

    # ===== MODULES MANQUANTS =====

    def create_properties_module(self):
        """Crée le module de gestion des biens"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        info_label = QLabel("🏠 Module Gestion des Biens - Interface moderne avec cartes")
        info_label.setStyleSheet(f"font-size: 16pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 20px;")
        layout.addWidget(info_label)

        # Critères spécifiques du module
        criteria_group = QGroupBox("📋 Critères du Module Gestion des Biens")
        criteria_layout = QVBoxLayout(criteria_group)

        fields = AppConfig.MODULES['properties']['fields']
        actions = AppConfig.MODULES['properties']['actions']

        criteria_text = f"""
        <b>Champs disponibles :</b><br>
        {', '.join(fields[:10])}...<br><br>
        <b>Actions disponibles :</b><br>
        {', '.join(actions)}
        """

        criteria_label = QLabel(criteria_text)
        criteria_label.setWordWrap(True)
        criteria_layout.addWidget(criteria_label)

        layout.addWidget(criteria_group)
        layout.addStretch()

        return widget

    def create_inventory_module(self):
        """Crée le module inventaire"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        info_label = QLabel("📦 Module Inventaire - Suivi intelligent des stocks")
        info_label.setStyleSheet(f"font-size: 16pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 20px;")
        layout.addWidget(info_label)

        # Critères spécifiques
        criteria_group = QGroupBox("📋 Critères du Module Inventaire")
        criteria_layout = QVBoxLayout(criteria_group)

        fields = AppConfig.MODULES['inventory']['fields']
        actions = AppConfig.MODULES['inventory']['actions']

        criteria_text = f"""
        <b>Champs disponibles :</b><br>
        {', '.join(fields)}<br><br>
        <b>Actions disponibles :</b><br>
        {', '.join(actions)}
        """

        criteria_label = QLabel(criteria_text)
        criteria_label.setWordWrap(True)
        criteria_layout.addWidget(criteria_label)

        layout.addWidget(criteria_group)
        layout.addStretch()

        return widget

    def create_suppliers_module(self):
        """Crée le module fournisseurs"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        info_label = QLabel("🏢 Module Fournisseurs - Gestion des partenaires")
        info_label.setStyleSheet(f"font-size: 16pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 20px;")
        layout.addWidget(info_label)

        # Critères spécifiques
        criteria_group = QGroupBox("📋 Critères du Module Fournisseurs")
        criteria_layout = QVBoxLayout(criteria_group)

        fields = AppConfig.MODULES['suppliers']['fields']
        actions = AppConfig.MODULES['suppliers']['actions']

        criteria_text = f"""
        <b>Champs disponibles :</b><br>
        {', '.join(fields)}<br><br>
        <b>Actions disponibles :</b><br>
        {', '.join(actions)}
        """

        criteria_label = QLabel(criteria_text)
        criteria_label.setWordWrap(True)
        criteria_layout.addWidget(criteria_label)

        layout.addWidget(criteria_group)
        layout.addStretch()

        return widget

    def create_clients_module(self):
        """Crée le module clients"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        info_label = QLabel("👥 Module Clients - Gestion de la clientèle")
        info_label.setStyleSheet(f"font-size: 16pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 20px;")
        layout.addWidget(info_label)

        # Critères spécifiques
        criteria_group = QGroupBox("📋 Critères du Module Clients")
        criteria_layout = QVBoxLayout(criteria_group)

        fields = AppConfig.MODULES['clients']['fields']
        actions = AppConfig.MODULES['clients']['actions']

        criteria_text = f"""
        <b>Champs disponibles :</b><br>
        {', '.join(fields)}<br><br>
        <b>Actions disponibles :</b><br>
        {', '.join(actions)}
        """

        criteria_label = QLabel(criteria_text)
        criteria_label.setWordWrap(True)
        criteria_layout.addWidget(criteria_label)

        layout.addWidget(criteria_group)
        layout.addStretch()

        return widget

    def create_invoicing_module(self):
        """Crée le module facturation"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        info_label = QLabel("📄 Module Facturation - Gestion financière")
        info_label.setStyleSheet(f"font-size: 16pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 20px;")
        layout.addWidget(info_label)

        # Critères spécifiques
        criteria_group = QGroupBox("📋 Critères du Module Facturation")
        criteria_layout = QVBoxLayout(criteria_group)

        fields = AppConfig.MODULES['invoicing']['fields']
        actions = AppConfig.MODULES['invoicing']['actions']

        criteria_text = f"""
        <b>Champs disponibles :</b><br>
        {', '.join(fields)}<br><br>
        <b>Actions disponibles :</b><br>
        {', '.join(actions)}
        """

        criteria_label = QLabel(criteria_text)
        criteria_label.setWordWrap(True)
        criteria_layout.addWidget(criteria_label)

        layout.addWidget(criteria_group)
        layout.addStretch()

        return widget

    # ===== MÉTHODES UTILITAIRES =====

    def load_initial_data(self):
        """Charge les données initiales"""
        try:
            # Ajouter quelques données de test si la base est vide
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM biens_immobiliers")
            if cursor.fetchone()[0] == 0:
                # Ajouter des données de test
                test_data = [
                    ("Ordinateur Portable Dell", "Dell", "Latitude 5520", "DL123456", 1200.00, "Informatique", "Bon"),
                    ("Imprimante HP", "HP", "LaserJet Pro", "HP789012", 350.00, "Informatique", "Excellent"),
                    ("Bureau Ergonomique", "IKEA", "BEKANT", "IK345678", 180.00, "Mobilier", "Bon")
                ]

                for data in test_data:
                    cursor.execute('''
                        INSERT INTO biens_immobiliers
                        (designation, marque, modele, numero_serie, valeur_acquisition, secteur, etat, date_acquisition)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (*data, date.today().isoformat()))

                self.conn.commit()
                print("✅ Données de test ajoutées")

        except Exception as e:
            print(f"Erreur chargement données: {e}")

    def export_data(self):
        """Exporte les données"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Exporter les données",
                f"export_gestimmob_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Fichiers CSV (*.csv)"
            )

            if file_path:
                cursor = self.conn.cursor()
                cursor.execute('''
                    SELECT designation, marque, modele, valeur_acquisition, secteur, etat, date_acquisition
                    FROM biens_immobiliers ORDER BY designation
                ''')
                rows = cursor.fetchall()

                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(["Désignation", "Marque", "Modèle", "Valeur", "Secteur", "État", "Date"])
                    writer.writerows(rows)

                QMessageBox.information(self, "Export réussi",
                                      f"📤 Données exportées avec succès:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    def show_help(self):
        """Affiche l'aide"""
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("Aide - GestImmob Professional")
        help_dialog.setFixedSize(600, 400)

        layout = QVBoxLayout(help_dialog)

        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml(f"""
        <h2>🏠 {AppConfig.APP_NAME} v{AppConfig.APP_VERSION}</h2>
        <h3>Guide d'utilisation</h3>

        <h4>[STATS] Navigation</h4>
        <p>Utilisez le menu vertical à gauche pour naviguer entre les modules.</p>

        <h4>🏠 Modules disponibles</h4>
        <ul>
        <li><b>Tableau de Bord</b> - Vue d'ensemble avec KPI</li>
        <li><b>Gestion des Biens</b> - Patrimoine immobilier</li>
        <li><b>Inventaire</b> - Suivi des stocks</li>
        <li><b>Fournisseurs</b> - Gestion des partenaires</li>
        <li><b>Clients</b> - Base clientèle</li>
        <li><b>Facturation</b> - Gestion financière</li>
        </ul>

        <h4>🎨 Interface</h4>
        <p>Interface moderne avec cartes et combos, boutons alignés verticalement.</p>

        <h4>[OUTIL] Support</h4>
        <p>Version complète avec tous les modules et critères spécifiques.</p>
        """)
        layout.addWidget(help_text)

        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        buttons.accepted.connect(help_dialog.accept)
        layout.addWidget(buttons)

        help_dialog.exec()

    def quit_application_detailed(self):
        """Quitte l'application avec confirmation détaillée"""
        reply = QMessageBox.question(
            self,
            "Quitter l'Application",
            f"Êtes-vous sûr de vouloir quitter {AppConfig.APP_NAME} ?\n\n"
            "• Toutes les données seront sauvegardées automatiquement\n"
            "• La session sera fermée proprement\n"
            "• Les paramètres seront conservés\n\n"
            "Voulez-vous vraiment quitter ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Sauvegarder les données
            try:
                self.conn.close()
                print("✅ Base de données fermée proprement")
            except:
                pass

            # Message de fermeture
            QMessageBox.information(self, "Au revoir",
                                  f"Merci d'avoir utilisé {AppConfig.APP_NAME} v{AppConfig.APP_VERSION} !\n\n"
                                  "✅ Toutes les données ont été sauvegardées\n"
                                  "👋 À bientôt !")

            # Quitter l'application
            QApplication.quit()

    def create_status_bar(self):
        """Crée la barre de statut"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.update_status_bar()

        # Timer pour mise à jour
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(10000)  # Toutes les 10 secondes

    def update_status_bar(self):
        """Met à jour la barre de statut"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM biens_immobiliers")
            nb_biens = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM fournisseurs")
            nb_fournisseurs = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM clients")
            nb_clients = cursor.fetchone()[0]

            status_text = f"[STATS] {nb_biens} biens | 🏢 {nb_fournisseurs} fournisseurs | 👥 {nb_clients} clients | ✅ {AppConfig.APP_NAME} v{AppConfig.APP_VERSION} - Opérationnel"
            self.status_bar.showMessage(status_text)

        except Exception as e:
            self.status_bar.showMessage(f"[ECHEC] Erreur de statut: {e}")

    def create_menu_bar(self):
        """Crée la barre de menu"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        export_action = QAction("📤 Exporter Données", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        quit_action = QAction("🚪 Quitter", self)
        quit_action.triggered.connect(self.quit_application_detailed)
        file_menu.addAction(quit_action)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = QAction("ℹ️ À Propos", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def show_about(self):
        """Affiche les informations À propos"""
        QMessageBox.about(self, "À Propos", f"""
        <h2>🏠 {AppConfig.APP_NAME} v{AppConfig.APP_VERSION}</h2>
        <p><b>Logiciel de Gestion Immobilière Professionnel</b></p>

        <h3>[LANCE] Fonctionnalités:</h3>
        <ul>
        <li>[STATS] Tableau de bord avec KPI en temps réel</li>
        <li>🏠 Gestion complète des biens immobiliers</li>
        <li>📦 Inventaire intelligent</li>
        <li>🏢 Gestion des fournisseurs</li>
        <li>👥 Gestion de la clientèle</li>
        <li>📄 Facturation intégrée</li>
        </ul>

        <h3>🎨 Interface:</h3>
        <p>Design moderne avec cartes et combos, boutons alignés verticalement</p>

        <h3>✅ Caractéristiques:</h3>
        <p>• Tous les modules intégrés avec leurs critères spécifiques<br>
        • Interface verticale moderne<br>
        • Couleurs fixes professionnelles<br>
        • Bouton de sortie détaillé<br>
        • Base de données complète</p>

        <p><b>© 2024 {AppConfig.APP_COMPANY}</b></p>
        """)

    def apply_modern_theme(self):
        """Applique le thème moderne et professionnel"""
        self.setStyleSheet(f"""
            /* ===== STYLES GÉNÉRAUX ===== */
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {AppConfig.COLORS['light']}, stop:1 {AppConfig.COLORS['white']});
                color: {AppConfig.COLORS['text']};
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 11pt;
            }}

            /* ===== SIDEBAR VERTICALE ===== */
            QFrame#sidebar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['sidebar']}, stop:1 {AppConfig.COLORS['dark']});
                border-right: 3px solid {AppConfig.COLORS['primary']};
            }}

            QWidget#sidebar_header {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                border-bottom: 2px solid {AppConfig.COLORS['light']};
            }}

            QLabel#sidebar_title {{
                color: {AppConfig.COLORS['white']};
                font-size: 14pt;
                font-weight: 700;
                text-align: center;
            }}

            QLabel#nav_group {{
                color: {AppConfig.COLORS['light']};
                font-size: 9pt;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 1px;
                padding: 15px 10px 5px 10px;
                margin-top: 10px;
            }}

            /* ===== BOUTONS DE NAVIGATION VERTICAUX ===== */
            QPushButton#nav_button {{
                background: transparent;
                color: {AppConfig.COLORS['light']};
                border: none;
                border-radius: 8px;
                padding: 12px 15px;
                text-align: left;
                font-size: 11pt;
                font-weight: 500;
                margin: 2px 5px;
                min-height: 40px;
            }}

            QPushButton#nav_button:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                color: {AppConfig.COLORS['white']};
                transform: translateX(5px);
            }}

            QPushButton#nav_button_active {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['secondary']}, stop:1 {AppConfig.COLORS['primary']});
                color: {AppConfig.COLORS['white']};
                border-left: 4px solid {AppConfig.COLORS['light']};
                font-weight: 700;
            }}

            QPushButton#nav_button_help {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['warning']}, stop:1 #e67e22);
                color: {AppConfig.COLORS['white']};
                font-weight: 600;
            }}

            QPushButton#nav_button_quit {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['danger']}, stop:1 #c0392b);
                color: {AppConfig.COLORS['white']};
                font-weight: 700;
                border: 2px solid #a93226;
            }}

            QPushButton#nav_button_quit:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #c0392b, stop:1 {AppConfig.COLORS['danger']});
                border-color: {AppConfig.COLORS['danger']};
                transform: scale(1.02);
            }}

            QWidget#sidebar_user {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['dark']}, stop:1 #1a252f);
                border-top: 2px solid {AppConfig.COLORS['primary']};
            }}

            QLabel#user_name {{
                color: {AppConfig.COLORS['white']};
                font-weight: 700;
                font-size: 11pt;
            }}

            QLabel#user_role {{
                color: {AppConfig.COLORS['light']};
                font-size: 9pt;
                font-style: italic;
            }}

            /* ===== EN-TÊTE DU CONTENU ===== */
            QWidget#content_header {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['white']}, stop:1 {AppConfig.COLORS['light']});
                border-bottom: 3px solid {AppConfig.COLORS['primary']};
            }}

            QLabel#module_title {{
                color: {AppConfig.COLORS['primary']};
                font-size: 20pt;
                font-weight: 700;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }}

            QLabel#datetime_label {{
                color: {AppConfig.COLORS['text']};
                font-size: 11pt;
                font-weight: 600;
                font-family: 'Courier New', monospace;
            }}

            QLabel#session_label {{
                color: {AppConfig.COLORS['text']};
                font-size: 9pt;
                font-style: italic;
            }}

            /* ===== BOUTONS PRINCIPAUX ===== */
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                color: {AppConfig.COLORS['white']};
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 11pt;
                min-width: 120px;
                min-height: 40px;
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['secondary']}, stop:1 {AppConfig.COLORS['primary']});
                transform: translateY(-2px);
            }}

            QPushButton#success {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['success']}, stop:1 #229954);
            }}

            QPushButton#warning {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['warning']}, stop:1 #e67e22);
            }}

            QPushButton#danger {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['danger']}, stop:1 #c0392b);
            }}

            /* ===== GROUPES ET CARTES ===== */
            QGroupBox {{
                font-weight: 700;
                border: 2px solid {AppConfig.COLORS['primary']};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background: {AppConfig.COLORS['white']};
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {AppConfig.COLORS['primary']};
                font-size: 14pt;
                font-weight: 700;
                background: {AppConfig.COLORS['white']};
            }}

            /* ===== BARRE DE STATUT ===== */
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                color: {AppConfig.COLORS['white']};
                border: none;
                font-weight: 600;
                padding: 8px;
            }}
        """)


# ===== FONCTION PRINCIPALE =====

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName(AppConfig.APP_NAME)
    app.setApplicationVersion(AppConfig.APP_VERSION)
    app.setOrganizationName(AppConfig.APP_COMPANY)

    # Créer et afficher la fenêtre principale
    window = GestImmobSimple()
    window.show()

    # Lancer l'application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
