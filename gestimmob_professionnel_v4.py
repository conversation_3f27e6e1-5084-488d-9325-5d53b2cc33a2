# -*- coding: utf-8 -*-
#!/usr/bin/env python3
"""
GestImmob v4.0.0 - Version Professionnelle Exceptionnelle
Logiciel de gestion immobilière inspiré des meilleurs logiciels professionnels
Interface verticale moderne, codification propre, modules distincts
Encodage: UTF-8 avec support complet des caractères français et émojis
"""

# Configuration d'encodage renforcée pour Windows
import sys
import os
import locale

# Forcer l'encodage UTF-8 sur Windows
try:
    # Configuration locale française
    locale.setlocale(locale.LC_ALL, 'fr_FR.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'French_France.1252')
    except:
        pass

# Configuration des flux de sortie
if sys.platform.startswith('win'):
    import io

    # Redirection des flux avec UTF-8
    if hasattr(sys.stdout, 'buffer'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'buffer'):
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

    # Configuration de l'environnement
    os.environ['PYTHONIOENCODING'] = 'utf-8'
import sqlite3
import hashlib
import json
from datetime import datetime
from pathlib import Path

# Imports PySide6
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QMessageBox, QFormLayout,
    QGroupBox, QStatusBar, QFrame, QComboBox, QSpinBox, QDoubleSpinBox,
    QDateEdit, QListWidget, QListWidgetItem,
    QGridLayout, QScrollArea, QSplitter,
    QStackedWidget, QDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt, QTimer, QDate, Signal
from PySide6.QtGui import QAction
from typing import Dict, Any


# ===== CONFIGURATION GLOBALE =====

class AppConfig:
    """Configuration globale de l'application"""
    
    # Informations application
    APP_NAME = "GestImmob Professional"
    APP_VERSION = "4.0.0"
    APP_COMPANY = "GestImmob Solutions Enterprise"
    
    # Couleurs professionnelles (inspirées de SAP/Oracle)
    COLORS = {
        'primary': '#0F4C75',      # Bleu corporate profond
        'secondary': '#3282B8',    # Bleu moderne
        'accent': '#BBE1FA',       # Bleu clair
        'success': '#27AE60',      # Vert succès
        'warning': '#F39C12',      # Orange attention
        'danger': '#E74C3C',       # Rouge erreur
        'dark': '#2C3E50',         # Gris foncé
        'light': '#ECF0F1',        # Gris clair
        'white': '#FFFFFF',        # Blanc
        'text': '#2C3E50',         # Texte principal
        'text_light': '#7F8C8D',   # Texte secondaire
        'border': '#BDC3C7',       # Bordures
        'background': '#F8F9FA',   # Arrière-plan
        'sidebar': '#34495E',      # Barre latérale
        'hover': '#E8F4FD'         # Survol
    }
    
    # Dimensions
    WINDOW_MIN_WIDTH = 1400
    WINDOW_MIN_HEIGHT = 900
    SIDEBAR_WIDTH = 280
    HEADER_HEIGHT = 80
    
    # Polices avec support Unicode complet
    FONT_FAMILY = "'Segoe UI', 'Arial Unicode MS', 'Roboto', 'Arial', sans-serif"
    FONT_SIZE_SMALL = 9
    FONT_SIZE_NORMAL = 11
    FONT_SIZE_LARGE = 14
    FONT_SIZE_TITLE = 24

    # Icônes avec émojis UTF-8 parfaitement encodés
    ICONS = {
        'dashboard': '📊',
        'properties': '🏠',
        'inventory': '📦',
        'suppliers': '🏢',
        'clients': '👥',
        'invoicing': '📄',
        'orders': '📋',
        'reforms': '♻️',
        'documents': '📁',
        'calculator': '🧮',
        'reports': '📈',
        'maintenance': '🔧',
        'settings': '⚙️',
        'help': '❓',
        'exit': '🚪',
        'add': '➕',
        'edit': '✏️',
        'delete': '🗑️',
        'save': '💾',
        'export': '📤',
        'import': '📥',
        'search': '🔍',
        'filter': '🔽',
        'refresh': '🔄',
        'print': '🖨️',
        'stats': '📊',
        'tools': '🛠️',
        'warning': '⚠️',
        'success': '✅',
        'error': '❌',
        'info': 'ℹ️'
    }

class ModuleConfig:
    """Configuration des modules avec leurs critères spécifiques"""

    MODULES: Dict[str, Dict[str, Any]] = {
        'dashboard': {
            'name': 'Tableau de Bord',
            'icon': AppConfig.ICONS['dashboard'],
            'description': 'Vue d\'ensemble et indicateurs clés',
            'fields': ['kpi_patrimoine', 'kpi_fournisseurs', 'kpi_clients', 'graphiques_secteurs'],
            'actions': ['actualiser_donnees', 'exporter_dashboard', 'configurer_kpi'],
            'permissions': ['view_dashboard', 'export_dashboard']
        },
        'properties': {
            'name': 'Gestion des Biens',
            'icon': AppConfig.ICONS['properties'],
            'description': 'Gestion complète du patrimoine immobilier',
            'fields': [
                'designation', 'marque', 'modele', 'numero_serie', 'code_barre',
                'valeur_acquisition', 'valeur_residuelle', 'duree_amortissement',
                'date_acquisition', 'date_mise_service', 'localisation_batiment',
                'localisation_etage', 'localisation_bureau', 'secteur_activite',
                'etat_physique', 'etat_fonctionnel', 'responsable_bien',
                'coordonnees_gps', 'photo_principale', 'photos_annexes',
                'fournisseur_id', 'facture_achat', 'garantie_fin', 'maintenance_contrat'
            ],
            'actions': ['ajouter', 'modifier', 'supprimer', 'dupliquer', 'transferer', 'reformer'],
            'permissions': ['view_properties', 'add_properties', 'edit_properties', 'delete_properties']
        },
        'inventory': {
            'name': 'Inventaire',
            'icon': AppConfig.ICONS['inventory'],
            'description': 'Inventaire physique et suivi des stocks',
            'fields': [
                'campagne_inventaire', 'date_debut', 'date_fin', 'responsable_inventaire',
                'secteur_inventaire', 'statut_inventaire', 'ecarts_constates',
                'biens_manquants', 'biens_excedentaires', 'valorisation_ecarts'
            ],
            'actions': ['creer_campagne', 'scanner_codes', 'saisir_inventaire', 'valider_ecarts'],
            'permissions': ['view_inventory', 'manage_inventory', 'validate_inventory']
        },
        'suppliers': {
            'name': 'Fournisseurs',
            'icon': AppConfig.ICONS['suppliers'],
            'description': 'Gestion des fournisseurs et partenaires',
            'fields': [
                'raison_sociale', 'nom_commercial', 'siret', 'numero_tva', 'code_naf',
                'adresse_siege', 'adresse_livraison', 'adresse_facturation',
                'contact_commercial', 'contact_technique', 'contact_comptable',
                'telephone_principal', 'telephone_mobile', 'email_principal',
                'site_web', 'secteur_activite', 'chiffre_affaires', 'effectif',
                'certification_qualite', 'assurance_responsabilite', 'conditions_paiement',
                'delai_livraison_standard', 'note_evaluation', 'statut_agrement'
            ],
            'actions': ['ajouter', 'modifier', 'desactiver', 'evaluer', 'agreer'],
            'permissions': ['view_suppliers', 'manage_suppliers', 'evaluate_suppliers']
        },
        'clients': {
            'name': 'Clients',
            'icon': AppConfig.ICONS['clients'],
            'description': 'Gestion de la clientèle et prospects',
            'fields': [
                'type_client', 'civilite', 'nom', 'prenom', 'raison_sociale',
                'siret_client', 'numero_tva_client', 'adresse_principale',
                'adresse_livraison', 'adresse_facturation', 'telephone_fixe',
                'telephone_mobile', 'email_principal', 'email_secondaire',
                'date_naissance', 'profession', 'revenus_annuels', 'situation_familiale',
                'banque_principale', 'iban', 'mode_paiement_prefere', 'limite_credit',
                'commercial_attribue', 'source_prospection', 'statut_client'
            ],
            'actions': ['ajouter', 'modifier', 'convertir_prospect', 'attribuer_commercial'],
            'permissions': ['view_clients', 'manage_clients', 'view_financial_data']
        },
        'invoicing': {
            'name': 'Facturation',
            'icon': AppConfig.ICONS['invoicing'],
            'description': 'Gestion de la facturation et des paiements',
            'fields': [
                'numero_facture', 'date_emission', 'date_echeance', 'client_id',
                'adresse_facturation', 'conditions_paiement', 'mode_paiement',
                'taux_tva', 'montant_ht', 'montant_tva', 'montant_ttc',
                'remise_commerciale', 'escompte', 'statut_facture', 'date_paiement',
                'reference_paiement', 'compte_comptable', 'piece_jointe'
            ],
            'actions': ['creer', 'modifier', 'valider', 'envoyer', 'relancer', 'encaisser'],
            'permissions': ['view_invoices', 'create_invoices', 'validate_invoices', 'manage_payments']
        }
    }

class GestImmobProfessional(QMainWindow):
    """Application GestImmob Professionnelle v4.0.0"""
    
    # Signaux personnalisés
    module_changed = Signal(str)
    data_updated = Signal()
    
    def __init__(self):
        super().__init__()
        
        # Configuration de la fenêtre principale
        self.setWindowTitle(f"{AppConfig.APP_NAME} v{AppConfig.APP_VERSION}")
        self.setMinimumSize(AppConfig.WINDOW_MIN_WIDTH, AppConfig.WINDOW_MIN_HEIGHT)
        self.resize(1600, 1000)
        
        # Variables d'état avec types
        self.current_module: str = 'dashboard'
        self.current_user: Dict[str, str] = {'username': 'admin', 'role': 'administrator'}
        self.modules_data: Dict[str, Any] = {}
        self.nav_buttons: Dict[str, QPushButton] = {}
        
        # Initialisation
        self.init_database()
        self.init_ui()
        self.apply_professional_theme()
        self.load_initial_data()
        self.setup_connections()
        
        # Timer pour les mises à jour
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status_bar)
        self.update_timer.start(5000)  # Mise à jour toutes les 5 secondes
        
    def init_database(self) -> None:
        """Initialise la base de données avec toutes les tables professionnelles"""
        self.db_path = "gestimmob_professional.db"
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # Table des biens immobiliers (version professionnelle complète)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS biens_immobiliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                marque TEXT,
                modele TEXT,
                numero_serie TEXT UNIQUE,
                code_barre TEXT UNIQUE,
                valeur_acquisition REAL NOT NULL,
                valeur_residuelle REAL DEFAULT 0,
                duree_amortissement INTEGER DEFAULT 5,
                date_acquisition DATE NOT NULL,
                date_mise_service DATE,
                localisation_batiment TEXT,
                localisation_etage TEXT,
                localisation_bureau TEXT,
                secteur_activite TEXT NOT NULL,
                etat_physique TEXT DEFAULT 'Bon',
                etat_fonctionnel TEXT DEFAULT 'Operationnel',
                responsable_bien TEXT,
                coordonnees_gps TEXT,
                photo_principale TEXT,
                photos_annexes TEXT,
                fournisseur_id INTEGER,
                facture_achat TEXT,
                garantie_fin DATE,
                maintenance_contrat TEXT,
                observations TEXT,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                utilisateur_creation TEXT,
                utilisateur_modification TEXT,
                FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id)
            )
        ''')
        
        # Table des fournisseurs (version professionnelle complète)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                raison_sociale TEXT NOT NULL,
                nom_commercial TEXT,
                siret TEXT UNIQUE,
                numero_tva TEXT,
                code_naf TEXT,
                adresse_siege TEXT,
                adresse_livraison TEXT,
                adresse_facturation TEXT,
                contact_commercial TEXT,
                contact_technique TEXT,
                contact_comptable TEXT,
                telephone_principal TEXT,
                telephone_mobile TEXT,
                email_principal TEXT,
                site_web TEXT,
                secteur_activite TEXT,
                chiffre_affaires REAL,
                effectif INTEGER,
                certification_qualite TEXT,
                assurance_responsabilite TEXT,
                conditions_paiement TEXT DEFAULT '30 jours',
                delai_livraison_standard INTEGER DEFAULT 7,
                note_evaluation INTEGER DEFAULT 5,
                statut_agrement TEXT DEFAULT 'En cours',
                date_agrement DATE,
                date_derniere_commande DATE,
                montant_total_commandes REAL DEFAULT 0,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des clients (version professionnelle complète)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_client TEXT DEFAULT 'Particulier',
                civilite TEXT,
                nom TEXT NOT NULL,
                prenom TEXT,
                raison_sociale TEXT,
                siret_client TEXT,
                numero_tva_client TEXT,
                adresse_principale TEXT,
                adresse_livraison TEXT,
                adresse_facturation TEXT,
                telephone_fixe TEXT,
                telephone_mobile TEXT,
                email_principal TEXT,
                email_secondaire TEXT,
                date_naissance DATE,
                profession TEXT,
                revenus_annuels REAL,
                situation_familiale TEXT,
                banque_principale TEXT,
                iban TEXT,
                mode_paiement_prefere TEXT,
                limite_credit REAL DEFAULT 0,
                commercial_attribue TEXT,
                source_prospection TEXT,
                statut_client TEXT DEFAULT 'Prospect',
                date_premier_contact DATE,
                date_derniere_commande DATE,
                chiffre_affaires_total REAL DEFAULT 0,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Autres tables professionnelles...
        self.create_additional_tables(cursor)
        
        # Utilisateur administrateur
        self.create_admin_user(cursor)
        
        self.conn.commit()
        
    def create_additional_tables(self, cursor: sqlite3.Cursor) -> None:
        """Crée les tables additionnelles"""
        # Table des factures
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero_facture TEXT UNIQUE NOT NULL,
                date_emission DATE NOT NULL,
                date_echeance DATE,
                client_id INTEGER NOT NULL,
                adresse_facturation TEXT,
                conditions_paiement TEXT,
                mode_paiement TEXT,
                taux_tva REAL DEFAULT 20.0,
                montant_ht REAL NOT NULL,
                montant_tva REAL NOT NULL,
                montant_ttc REAL NOT NULL,
                remise_commerciale REAL DEFAULT 0,
                escompte REAL DEFAULT 0,
                statut_facture TEXT DEFAULT 'Brouillon',
                date_paiement DATE,
                reference_paiement TEXT,
                compte_comptable TEXT,
                piece_jointe TEXT,
                observations TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients(id)
            )
        ''')
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS utilisateurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                nom TEXT,
                prenom TEXT,
                email TEXT,
                role TEXT NOT NULL DEFAULT 'utilisateur',
                permissions TEXT,
                derniere_connexion TIMESTAMP,
                tentatives_connexion INTEGER DEFAULT 0,
                compte_verrouille INTEGER DEFAULT 0,
                date_expiration_mot_passe DATE,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
    def create_admin_user(self, cursor: sqlite3.Cursor) -> None:
        """Crée l'utilisateur administrateur"""
        cursor.execute("SELECT COUNT(*) FROM utilisateurs WHERE role='administrator'")
        if cursor.fetchone()[0] == 0:
            admin_password = hashlib.sha256("Admin@1234".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO utilisateurs (username, password_hash, nom, prenom, role, permissions)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ("admin", admin_password, "Administrateur", "Système", "administrator", "ALL"))
            
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur professionnelle"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal horizontal (sidebar + contenu)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Création de la sidebar verticale
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # Zone de contenu principal
        self.content_area = QWidget()
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        # En-tête du contenu
        self.create_header()
        self.content_layout.addWidget(self.header)
        
        # Zone de contenu des modules
        self.stacked_widget = QStackedWidget()
        self.content_layout.addWidget(self.stacked_widget)
        
        # Création de tous les modules
        self.create_all_modules()
        
        main_layout.addWidget(self.content_area, 1)  # Prend tout l'espace restant
        
        # Barre de statut professionnelle
        self.create_status_bar()
        
        # Menu principal
        self.create_menu_bar()

    def create_sidebar(self) -> None:
        """Crée la barre latérale verticale moderne"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(AppConfig.SIDEBAR_WIDTH)
        self.sidebar.setObjectName("sidebar")

        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # Logo et titre de l'application
        header_widget = QWidget()
        header_widget.setObjectName("sidebar_header")
        header_widget.setFixedHeight(AppConfig.HEADER_HEIGHT)
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 15, 20, 15)

        # Logo (emoji stylisé)
        logo_label = QLabel("🏠")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_label.setStyleSheet("font-size: 32pt; color: #BBE1FA;")
        header_layout.addWidget(logo_label)

        # Titre
        title_label = QLabel(AppConfig.APP_NAME)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("sidebar_title")
        header_layout.addWidget(title_label)

        # Version
        version_label = QLabel(f"v{AppConfig.APP_VERSION}")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setObjectName("sidebar_version")
        header_layout.addWidget(version_label)

        sidebar_layout.addWidget(header_widget)

        # Zone de navigation avec scroll
        nav_scroll = QScrollArea()
        nav_scroll.setWidgetResizable(True)
        nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        nav_scroll.setObjectName("nav_scroll")

        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(10, 10, 10, 10)
        nav_layout.setSpacing(5)

        # Boutons de navigation pour chaque module
        self.nav_buttons = {}

        # Groupe Gestion
        self.add_nav_group(nav_layout, "GESTION PRINCIPALE")
        self.add_nav_button(nav_layout, 'dashboard', 'Tableau de Bord', True)
        self.add_nav_button(nav_layout, 'properties', 'Gestion des Biens')
        self.add_nav_button(nav_layout, 'inventory', 'Inventaire')

        # Groupe Partenaires
        self.add_nav_group(nav_layout, "PARTENAIRES")
        self.add_nav_button(nav_layout, 'suppliers', 'Fournisseurs')
        self.add_nav_button(nav_layout, 'clients', 'Clients')

        # Groupe Commercial
        self.add_nav_group(nav_layout, "COMMERCIAL")
        self.add_nav_button(nav_layout, 'invoicing', 'Facturation')
        self.add_nav_button(nav_layout, 'orders', 'Bons de Commande')

        # Groupe Outils
        self.add_nav_group(nav_layout, "OUTILS")
        self.add_nav_button(nav_layout, 'calculator', 'Calculatrice')
        self.add_nav_button(nav_layout, 'reports', 'Rapports')
        self.add_nav_button(nav_layout, 'documents', 'Documents')

        # Groupe Administration
        self.add_nav_group(nav_layout, "ADMINISTRATION")
        self.add_nav_button(nav_layout, 'reforms', 'Gestion Réformes')
        self.add_nav_button(nav_layout, 'maintenance', 'Maintenance')
        self.add_nav_button(nav_layout, 'settings', 'Paramètres')

        nav_layout.addStretch()

        # Boutons d'action en bas
        self.add_nav_group(nav_layout, "ACTIONS")

        # Bouton Aide
        help_btn = QPushButton(f"{AppConfig.ICONS['help']} Aide")
        help_btn.setObjectName("nav_button_secondary")
        help_btn.clicked.connect(self.show_help)
        nav_layout.addWidget(help_btn)

        # Bouton Quitter avec style spécial
        quit_btn = QPushButton(f"{AppConfig.ICONS['exit']} Quitter l'Application")
        quit_btn.setObjectName("nav_button_quit")
        quit_btn.clicked.connect(self.quit_application)
        nav_layout.addWidget(quit_btn)

        nav_scroll.setWidget(nav_widget)
        sidebar_layout.addWidget(nav_scroll)

        # Informations utilisateur en bas
        user_widget = QWidget()
        user_widget.setObjectName("sidebar_user")
        user_widget.setFixedHeight(60)
        user_layout = QHBoxLayout(user_widget)
        user_layout.setContentsMargins(15, 10, 15, 10)

        user_icon = QLabel("👤")
        user_icon.setStyleSheet("font-size: 16pt; color: #BBE1FA;")
        user_layout.addWidget(user_icon)

        user_info = QVBoxLayout()
        user_name = QLabel(self.current_user['username'])
        user_name.setObjectName("user_name")
        user_role = QLabel(self.current_user['role'])
        user_role.setObjectName("user_role")
        user_info.addWidget(user_name)
        user_info.addWidget(user_role)
        user_layout.addLayout(user_info)

        sidebar_layout.addWidget(user_widget)

    def add_nav_group(self, layout: QVBoxLayout, title: str) -> None:
        """Ajoute un groupe de navigation"""
        group_label = QLabel(title)
        group_label.setObjectName("nav_group")
        layout.addWidget(group_label)

    def add_nav_button(self, layout: QVBoxLayout, module_key: str, title: str, is_active: bool = False) -> None:
        """Ajoute un bouton de navigation"""
        module_config = ModuleConfig.MODULES.get(module_key, {})
        icon = module_config.get('icon', '📄')

        button = QPushButton(f"{icon} {title}")
        button.setObjectName("nav_button_active" if is_active else "nav_button")
        button.clicked.connect(lambda: self.switch_module(module_key))

        # Tooltip avec description
        if 'description' in module_config:
            button.setToolTip(module_config['description'])

        layout.addWidget(button)
        self.nav_buttons[module_key] = button

    def create_header(self) -> None:
        """Crée l'en-tête du contenu"""
        self.header = QWidget()
        self.header.setObjectName("content_header")
        self.header.setFixedHeight(AppConfig.HEADER_HEIGHT)

        header_layout = QHBoxLayout(self.header)
        header_layout.setContentsMargins(30, 15, 30, 15)

        # Titre du module actuel
        self.module_title = QLabel("Tableau de Bord")
        self.module_title.setObjectName("module_title")
        header_layout.addWidget(self.module_title)

        header_layout.addStretch()

        # Informations de session
        session_layout = QVBoxLayout()

        self.datetime_label = QLabel()
        self.datetime_label.setObjectName("datetime_label")
        session_layout.addWidget(self.datetime_label)

        self.session_label = QLabel(f"Session: {self.current_user['username']}")
        self.session_label.setObjectName("session_label")
        session_layout.addWidget(self.session_label)

        header_layout.addLayout(session_layout)

        # Timer pour l'heure
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_datetime)
        self.time_timer.start(1000)
        self.update_datetime()

    def create_all_modules(self) -> None:
        """Crée tous les modules avec leurs interfaces spécifiques"""
        # Module Tableau de Bord
        self.dashboard_widget = self.create_dashboard_module()
        self.stacked_widget.addWidget(self.dashboard_widget)

        # Module Gestion des Biens
        self.properties_widget = self.create_properties_module()
        self.stacked_widget.addWidget(self.properties_widget)

        # Module Inventaire
        self.inventory_widget = self.create_inventory_module()
        self.stacked_widget.addWidget(self.inventory_widget)

        # Module Fournisseurs
        self.suppliers_widget = self.create_suppliers_module()
        self.stacked_widget.addWidget(self.suppliers_widget)

        # Module Clients
        self.clients_widget = self.create_clients_module()
        self.stacked_widget.addWidget(self.clients_widget)

        # Module Facturation
        self.invoicing_widget = self.create_invoicing_module()
        self.stacked_widget.addWidget(self.invoicing_widget)

        # Autres modules (stubs pour l'instant)
        for module_key in ['orders', 'calculator', 'reports', 'documents', 'reforms', 'maintenance', 'settings']:
            stub_widget = self.create_module_stub(module_key)
            self.stacked_widget.addWidget(stub_widget)

        # Définir le module par défaut
        self.stacked_widget.setCurrentIndex(0)  # Dashboard

    # ===== MÉTHODES UTILITAIRES =====

    def update_datetime(self) -> None:
        """Met à jour l'affichage de la date et heure"""
        current_time = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        self.datetime_label.setText(current_time)

    def switch_module(self, module_key: str) -> None:
        """Change de module"""
        # Mettre à jour l'état des boutons
        for key, button in self.nav_buttons.items():
            if key == module_key:
                button.setObjectName("nav_button_active")
                self.current_module = module_key
            else:
                button.setObjectName("nav_button")
            button.style().unpolish(button)
            button.style().polish(button)

        # Mettre à jour le titre
        module_config = ModuleConfig.MODULES.get(module_key, {})
        module_name = module_config.get('name', 'Module')
        self.module_title.setText(module_name)

        # Changer le widget affiché
        module_index = list(ModuleConfig.MODULES.keys()).index(module_key)
        self.stacked_widget.setCurrentIndex(module_index)

        # Émettre le signal
        self.module_changed.emit(module_key)

    def show_help(self) -> None:
        """Affiche l'aide"""
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("Aide - GestImmob Professional")
        help_dialog.setFixedSize(600, 400)

        layout = QVBoxLayout(help_dialog)

        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h2>🏠 GestImmob Professional v4.0.0</h2>
        <h3>Guide d'utilisation rapide</h3>

        <h4>[STATS] Navigation</h4>
        <p>Utilisez le menu vertical à gauche pour naviguer entre les modules.</p>

        <h4>🏠 Gestion des Biens</h4>
        <p>Module complet pour gérer votre patrimoine immobilier avec tous les champs professionnels.</p>

        <h4>📦 Inventaire</h4>
        <p>Outils d'inventaire physique avec scanner de codes-barres et suivi des écarts.</p>

        <h4>🏢 Fournisseurs</h4>
        <p>Base de données complète des fournisseurs avec informations légales et commerciales.</p>

        <h4>👥 Clients</h4>
        <p>Gestion de la clientèle et des prospects avec données financières.</p>

        <h4>📄 Facturation</h4>
        <p>Module de facturation complet avec gestion des paiements.</p>

        <h4>[OUTIL] Support</h4>
        <p>Pour toute assistance, contactez l'administrateur système.</p>
        """)
        layout.addWidget(help_text)

        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        buttons.accepted.connect(help_dialog.accept)
        layout.addWidget(buttons)

        help_dialog.exec()

    def quit_application(self) -> None:
        """Quitte l'application avec confirmation"""
        reply = QMessageBox.question(
            self,
            "Quitter l'Application",
            "Êtes-vous sûr de vouloir quitter GestImmob Professional ?\n\nToutes les données non sauvegardées seront perdues.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Sauvegarder les données si nécessaire
            self.save_session_data()

            # Fermer la connexion à la base de données
            if hasattr(self, 'conn'):
                self.conn.close()

            # Quitter l'application
            QApplication.quit()

    def save_session_data(self) -> None:
        """Sauvegarde les données de session"""
        try:
            # Sauvegarder les préférences utilisateur
            session_data: Dict[str, Any] = {
                'last_module': self.current_module,
                'window_geometry': {
                    'width': self.width(),
                    'height': self.height(),
                    'x': self.x(),
                    'y': self.y()
                },
                'last_login': datetime.now().isoformat()
            }

            # Écrire dans un fichier de configuration
            config_path = Path("gestimmob_session.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2)

        except Exception as e:
            print(f"Erreur lors de la sauvegarde de session: {e}")

    def create_module_stub(self, module_key: str) -> QWidget:
        """Crée un stub pour les modules non encore implémentés"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(30, 30, 30, 30)

        module_config = ModuleConfig.MODULES.get(module_key, {})
        module_name = module_config.get('name', 'Module')
        module_icon = module_config.get('icon', '📄')
        module_description = module_config.get('description', 'Module en développement')

        # En-tête du module
        header_layout = QHBoxLayout()

        icon_label = QLabel(module_icon)
        icon_label.setStyleSheet("font-size: 48pt;")
        header_layout.addWidget(icon_label)

        title_layout = QVBoxLayout()
        title_label = QLabel(module_name)
        title_label.setObjectName("module_stub_title")
        title_layout.addWidget(title_label)

        desc_label = QLabel(module_description)
        desc_label.setObjectName("module_stub_description")
        title_layout.addWidget(desc_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Informations sur le module
        info_group = QGroupBox("Informations du Module")
        info_layout = QFormLayout(info_group)

        # Afficher les champs du module
        fields = module_config.get('fields', [])
        if fields:
            fields_text = ", ".join(fields[:10])  # Limiter à 10 champs
            if len(fields) > 10:
                fields_text += f"... (+{len(fields) - 10} autres)"
            info_layout.addRow("Champs principaux:", QLabel(fields_text))

        # Afficher les actions du module
        actions = module_config.get('actions', [])
        if actions:
            actions_text = ", ".join(actions)
            info_layout.addRow("Actions disponibles:", QLabel(actions_text))

        # Afficher les permissions
        permissions = module_config.get('permissions', [])
        if permissions:
            permissions_text = ", ".join(permissions)
            info_layout.addRow("Permissions requises:", QLabel(permissions_text))

        layout.addWidget(info_group)

        # Message de développement
        dev_label = QLabel("🚧 Ce module est en cours de développement et sera disponible dans une prochaine version.")
        dev_label.setObjectName("module_stub_dev")
        dev_label.setWordWrap(True)
        layout.addWidget(dev_label)

        layout.addStretch()

        return widget

    # ===== THÈME PROFESSIONNEL =====

    def apply_professional_theme(self) -> None:
        """Applique le thème professionnel inspiré des meilleurs logiciels"""
        self.setStyleSheet(f"""
            /* ===== STYLES GÉNÉRAUX ===== */
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {AppConfig.COLORS['background']}, stop:1 {AppConfig.COLORS['light']});
                color: {AppConfig.COLORS['text']};
                font-family: {AppConfig.FONT_FAMILY};
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
            }}

            QWidget {{
                background-color: transparent;
                color: {AppConfig.COLORS['text']};
                font-family: {AppConfig.FONT_FAMILY};
            }}

            /* ===== SIDEBAR VERTICALE ===== */
            QFrame#sidebar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['sidebar']}, stop:1 {AppConfig.COLORS['dark']});
                border-right: 3px solid {AppConfig.COLORS['primary']};
            }}

            QWidget#sidebar_header {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                border-bottom: 2px solid {AppConfig.COLORS['accent']};
            }}

            QLabel#sidebar_title {{
                color: {AppConfig.COLORS['white']};
                font-size: {AppConfig.FONT_SIZE_LARGE}pt;
                font-weight: 700;
                text-align: center;
            }}

            QLabel#sidebar_version {{
                color: {AppConfig.COLORS['accent']};
                font-size: {AppConfig.FONT_SIZE_SMALL}pt;
                font-weight: 500;
                text-align: center;
            }}

            QScrollArea#nav_scroll {{
                background: transparent;
                border: none;
            }}

            QLabel#nav_group {{
                color: {AppConfig.COLORS['accent']};
                font-size: {AppConfig.FONT_SIZE_SMALL}pt;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 1px;
                padding: 15px 10px 5px 10px;
                margin-top: 10px;
            }}

            /* ===== BOUTONS DE NAVIGATION ===== */
            QPushButton#nav_button {{
                background: transparent;
                color: {AppConfig.COLORS['light']};
                border: none;
                border-radius: 8px;
                padding: 12px 15px;
                text-align: left;
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
                font-weight: 500;
                margin: 2px 5px;
                min-height: 40px;
            }}

            QPushButton#nav_button:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                color: {AppConfig.COLORS['white']};
                transform: translateX(5px);
            }}

            QPushButton#nav_button_active {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['secondary']}, stop:1 {AppConfig.COLORS['primary']});
                color: {AppConfig.COLORS['white']};
                border-left: 4px solid {AppConfig.COLORS['accent']};
                font-weight: 700;
            }}

            QPushButton#nav_button_secondary {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['warning']}, stop:1 #e67e22);
                color: {AppConfig.COLORS['white']};
                font-weight: 600;
            }}

            QPushButton#nav_button_quit {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['danger']}, stop:1 #c0392b);
                color: {AppConfig.COLORS['white']};
                font-weight: 700;
                border: 2px solid #a93226;
            }}

            QPushButton#nav_button_quit:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #c0392b, stop:1 {AppConfig.COLORS['danger']});
                border-color: {AppConfig.COLORS['danger']};
                transform: scale(1.02);
            }}

            QWidget#sidebar_user {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['dark']}, stop:1 #1a252f);
                border-top: 2px solid {AppConfig.COLORS['primary']};
            }}

            QLabel#user_name {{
                color: {AppConfig.COLORS['white']};
                font-weight: 700;
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
            }}

            QLabel#user_role {{
                color: {AppConfig.COLORS['accent']};
                font-size: {AppConfig.FONT_SIZE_SMALL}pt;
                font-style: italic;
            }}

            /* ===== EN-TÊTE DU CONTENU ===== */
            QWidget#content_header {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['white']}, stop:1 {AppConfig.COLORS['light']});
                border-bottom: 3px solid {AppConfig.COLORS['primary']};
            }}

            QLabel#module_title {{
                color: {AppConfig.COLORS['primary']};
                font-size: {AppConfig.FONT_SIZE_TITLE}pt;
                font-weight: 700;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }}

            QLabel#datetime_label {{
                color: {AppConfig.COLORS['text']};
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
                font-weight: 600;
                font-family: 'Courier New', monospace;
            }}

            QLabel#session_label {{
                color: {AppConfig.COLORS['text_light']};
                font-size: {AppConfig.FONT_SIZE_SMALL}pt;
                font-style: italic;
            }}

            /* ===== BOUTONS PRINCIPAUX ===== */
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                color: {AppConfig.COLORS['white']};
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
                min-width: 120px;
                min-height: 40px;
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['secondary']}, stop:1 {AppConfig.COLORS['primary']});
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }}

            QPushButton:pressed {{
                background: {AppConfig.COLORS['dark']};
                transform: translateY(0px);
            }}

            QPushButton#success {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['success']}, stop:1 #229954);
            }}

            QPushButton#warning {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['warning']}, stop:1 #e67e22);
            }}

            QPushButton#danger {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {AppConfig.COLORS['danger']}, stop:1 #c0392b);
            }}

            /* ===== CHAMPS DE SAISIE ===== */
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {AppConfig.COLORS['border']};
                border-radius: 6px;
                padding: 10px;
                background: {AppConfig.COLORS['white']};
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
                selection-background-color: {AppConfig.COLORS['secondary']};
            }}

            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
                border-color: {AppConfig.COLORS['primary']};
                box-shadow: 0 0 8px rgba(15, 76, 117, 0.3);
            }}

            QLineEdit:hover, QComboBox:hover {{
                border-color: {AppConfig.COLORS['secondary']};
            }}

            /* ===== GROUPES ET CARTES ===== */
            QGroupBox {{
                font-weight: 700;
                border: 2px solid {AppConfig.COLORS['primary']};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background: {AppConfig.COLORS['white']};
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {AppConfig.COLORS['primary']};
                font-size: {AppConfig.FONT_SIZE_LARGE}pt;
                font-weight: 700;
                background: {AppConfig.COLORS['white']};
            }}

            /* ===== BARRE DE STATUT ===== */
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                color: {AppConfig.COLORS['white']};
                border: none;
                font-weight: 600;
                padding: 8px;
            }}

            /* ===== STUBS DE MODULES ===== */
            QLabel#module_stub_title {{
                color: {AppConfig.COLORS['primary']};
                font-size: 20pt;
                font-weight: 700;
                margin-bottom: 10px;
            }}

            QLabel#module_stub_description {{
                color: {AppConfig.COLORS['text_light']};
                font-size: {AppConfig.FONT_SIZE_LARGE}pt;
                font-style: italic;
                margin-bottom: 20px;
            }}

            QLabel#module_stub_dev {{
                color: {AppConfig.COLORS['warning']};
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
                font-weight: 600;
                background: rgba(243, 156, 18, 0.1);
                border: 2px solid {AppConfig.COLORS['warning']};
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
            }}

            /* ===== SCROLLBARS ===== */
            QScrollBar:vertical {{
                background: {AppConfig.COLORS['light']};
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }}

            QScrollBar::handle:vertical {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {AppConfig.COLORS['primary']}, stop:1 {AppConfig.COLORS['secondary']});
                border-radius: 6px;
                min-height: 20px;
            }}

            QScrollBar::handle:vertical:hover {{
                background: {AppConfig.COLORS['secondary']};
            }}

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}

            /* ===== TOOLTIPS ===== */
            QToolTip {{
                background: {AppConfig.COLORS['dark']};
                color: {AppConfig.COLORS['white']};
                border: 2px solid {AppConfig.COLORS['primary']};
                border-radius: 6px;
                padding: 8px;
                font-size: {AppConfig.FONT_SIZE_NORMAL}pt;
            }}
        """)

    # ===== MODULES PRINCIPAUX AVEC CRITÈRES SPÉCIFIQUES =====

    def create_dashboard_module(self) -> QWidget:
        """Crée le module tableau de bord avec KPI et graphiques"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # En-tête du dashboard
        header_group = QGroupBox("[STATS] Tableau de Bord - Vue d'Ensemble")
        header_layout = QVBoxLayout(header_group)

        welcome_label = QLabel(f"Bienvenue dans GestImmob Professional v{AppConfig.APP_VERSION}")
        welcome_label.setStyleSheet(f"font-size: 18pt; color: {AppConfig.COLORS['primary']}; font-weight: 700; margin: 10px;")
        header_layout.addWidget(welcome_label)

        layout.addWidget(header_group)

        # KPI Cards en grille
        kpi_group = QGroupBox("📈 Indicateurs Clés de Performance")
        kpi_layout = QGridLayout(kpi_group)

        # Créer les cartes KPI
        self.create_kpi_card(kpi_layout, 0, 0, "🏠 Patrimoine Total", "biens_immobiliers", "COUNT(*)", "€")
        self.create_kpi_card(kpi_layout, 0, 1, "💰 Valeur Totale", "biens_immobiliers", "SUM(valeur_acquisition)", "€")
        self.create_kpi_card(kpi_layout, 0, 2, "🏢 Fournisseurs Actifs", "fournisseurs", "COUNT(*) WHERE statut='Actif'", "")
        self.create_kpi_card(kpi_layout, 1, 0, "👥 Clients", "clients", "COUNT(*)", "")
        self.create_kpi_card(kpi_layout, 1, 1, "📄 Factures du Mois", "factures", "COUNT(*) WHERE date_emission >= date('now', 'start of month')", "")
        self.create_kpi_card(kpi_layout, 1, 2, "[ATTENTION] Biens à Maintenir", "biens_immobiliers", "COUNT(*) WHERE etat_physique IN ('Moyen', 'Mauvais')", "")

        layout.addWidget(kpi_group)

        # Actions rapides
        actions_group = QGroupBox("[LANCE] Actions Rapides")
        actions_layout = QHBoxLayout(actions_group)

        btn_nouveau_bien = QPushButton(f"{AppConfig.ICONS['add']} Nouveau Bien")
        btn_nouveau_bien.setObjectName("success")
        btn_nouveau_bien.clicked.connect(lambda: self.switch_module('properties'))
        actions_layout.addWidget(btn_nouveau_bien)

        btn_inventaire = QPushButton(f"{AppConfig.ICONS['inventory']} Lancer Inventaire")
        btn_inventaire.clicked.connect(lambda: self.switch_module('inventory'))
        actions_layout.addWidget(btn_inventaire)

        btn_rapport = QPushButton(f"{AppConfig.ICONS['reports']} Générer Rapport")
        btn_rapport.clicked.connect(lambda: self.switch_module('reports'))
        actions_layout.addWidget(btn_rapport)

        btn_maintenance = QPushButton(f"{AppConfig.ICONS['maintenance']} Maintenance")
        btn_maintenance.setObjectName("warning")
        btn_maintenance.clicked.connect(lambda: self.switch_module('maintenance'))
        actions_layout.addWidget(btn_maintenance)

        actions_layout.addStretch()
        layout.addWidget(actions_group)

        layout.addStretch()

        return widget

    def create_kpi_card(self, layout: QGridLayout, row: int, col: int, title: str, table: str, query: str, unit: str) -> None:
        """Crée une carte KPI avec données en temps réel"""
        card = QFrame()
        card.setObjectName("kpi_card")
        card.setStyleSheet(f"""
            QFrame#kpi_card {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {AppConfig.COLORS['white']}, stop:1 {AppConfig.COLORS['light']});
                border: 2px solid {AppConfig.COLORS['primary']};
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
                min-height: 120px;
            }}
            QFrame#kpi_card:hover {{
                border-color: {AppConfig.COLORS['secondary']};
                transform: scale(1.02);
            }}
        """)

        card_layout = QVBoxLayout(card)

        # Titre de la carte
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: 700;
            color: {AppConfig.COLORS['primary']};
            margin-bottom: 10px;
        """)
        card_layout.addWidget(title_label)

        # Valeur KPI
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"SELECT {query} FROM {table}")
            result = cursor.fetchone()
            value = result[0] if result and result[0] is not None else 0

            if unit == "€" and isinstance(value, (int, float)):
                display_value = f"{value:,.2f} {unit}"
            else:
                display_value = f"{value} {unit}".strip()

        except Exception as e:
            display_value = "Erreur"
            print(f"Erreur KPI {title}: {e}")

        value_label = QLabel(display_value)
        value_label.setStyleSheet(f"""
            font-size: 24pt;
            font-weight: 700;
            color: {AppConfig.COLORS['secondary']};
            text-align: center;
        """)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(value_label)

        layout.addWidget(card, row, col)

    def create_properties_module(self) -> QWidget:
        """Crée le module de gestion des biens avec tous les critères professionnels"""
        widget = QWidget()
        main_layout = QHBoxLayout(widget)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Splitter pour diviser l'interface
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # ===== PARTIE GAUCHE : FORMULAIRE =====
        form_widget = QWidget()
        form_widget.setFixedWidth(500)
        form_layout = QVBoxLayout(form_widget)

        # Groupe Informations Générales
        general_group = QGroupBox("🏠 Informations Générales du Bien")
        general_layout = QFormLayout(general_group)

        self.prop_designation = QLineEdit()
        self.prop_designation.setPlaceholderText("Désignation du bien (obligatoire)")
        general_layout.addRow("Désignation *:", self.prop_designation)

        self.prop_marque = QLineEdit()
        self.prop_marque.setPlaceholderText("Marque du bien")
        general_layout.addRow("Marque:", self.prop_marque)

        self.prop_modele = QLineEdit()
        self.prop_modele.setPlaceholderText("Modèle du bien")
        general_layout.addRow("Modèle:", self.prop_modele)

        self.prop_numero_serie = QLineEdit()
        self.prop_numero_serie.setPlaceholderText("Numéro de série unique")
        general_layout.addRow("N° Série:", self.prop_numero_serie)

        self.prop_code_barre = QLineEdit()
        self.prop_code_barre.setPlaceholderText("Code-barres (généré automatiquement)")
        self.prop_code_barre.setReadOnly(True)
        general_layout.addRow("Code-barres:", self.prop_code_barre)

        form_layout.addWidget(general_group)

        # Groupe Informations Financières
        financial_group = QGroupBox("💰 Informations Financières")
        financial_layout = QFormLayout(financial_group)

        self.prop_valeur_acquisition = QDoubleSpinBox()
        self.prop_valeur_acquisition.setRange(0, 999999999.99)
        self.prop_valeur_acquisition.setSuffix(" €")
        self.prop_valeur_acquisition.setValue(1000.00)
        financial_layout.addRow("Valeur d'acquisition *:", self.prop_valeur_acquisition)

        self.prop_valeur_residuelle = QDoubleSpinBox()
        self.prop_valeur_residuelle.setRange(0, 999999999.99)
        self.prop_valeur_residuelle.setSuffix(" €")
        financial_layout.addRow("Valeur résiduelle:", self.prop_valeur_residuelle)

        self.prop_duree_amortissement = QSpinBox()
        self.prop_duree_amortissement.setRange(1, 50)
        self.prop_duree_amortissement.setValue(5)
        self.prop_duree_amortissement.setSuffix(" ans")
        financial_layout.addRow("Durée amortissement:", self.prop_duree_amortissement)

        form_layout.addWidget(financial_group)

        # Groupe Dates
        dates_group = QGroupBox("📅 Dates Importantes")
        dates_layout = QFormLayout(dates_group)

        self.prop_date_acquisition = QDateEdit()
        self.prop_date_acquisition.setDate(QDate.currentDate())
        self.prop_date_acquisition.setCalendarPopup(True)
        dates_layout.addRow("Date d'acquisition *:", self.prop_date_acquisition)

        self.prop_date_mise_service = QDateEdit()
        self.prop_date_mise_service.setDate(QDate.currentDate())
        self.prop_date_mise_service.setCalendarPopup(True)
        dates_layout.addRow("Date mise en service:", self.prop_date_mise_service)

        self.prop_garantie_fin = QDateEdit()
        self.prop_garantie_fin.setDate(QDate.currentDate().addYears(2))
        self.prop_garantie_fin.setCalendarPopup(True)
        dates_layout.addRow("Fin de garantie:", self.prop_garantie_fin)

        form_layout.addWidget(dates_group)

        # Groupe Localisation
        location_group = QGroupBox("📍 Localisation Détaillée")
        location_layout = QFormLayout(location_group)

        self.prop_localisation_batiment = QLineEdit()
        self.prop_localisation_batiment.setPlaceholderText("Nom du bâtiment")
        location_layout.addRow("Bâtiment:", self.prop_localisation_batiment)

        self.prop_localisation_etage = QLineEdit()
        self.prop_localisation_etage.setPlaceholderText("Étage")
        location_layout.addRow("Étage:", self.prop_localisation_etage)

        self.prop_localisation_bureau = QLineEdit()
        self.prop_localisation_bureau.setPlaceholderText("Bureau/Salle")
        location_layout.addRow("Bureau/Salle:", self.prop_localisation_bureau)

        self.prop_coordonnees_gps = QLineEdit()
        self.prop_coordonnees_gps.setPlaceholderText("Latitude, Longitude")
        location_layout.addRow("Coordonnées GPS:", self.prop_coordonnees_gps)

        form_layout.addWidget(location_group)

        # Groupe Classification
        classification_group = QGroupBox("📂 Classification et État")
        classification_layout = QFormLayout(classification_group)

        self.prop_secteur_activite = QComboBox()
        self.prop_secteur_activite.addItems([
            "Administration", "Informatique", "Comptabilité", "Commercial",
            "Production", "Logistique", "Maintenance", "Sécurité", "Autre"
        ])
        classification_layout.addRow("Secteur d'activité *:", self.prop_secteur_activite)

        self.prop_etat_physique = QComboBox()
        self.prop_etat_physique.addItems(["Excellent", "Bon", "Moyen", "Mauvais", "Hors service"])
        self.prop_etat_physique.setCurrentText("Bon")
        classification_layout.addRow("État physique:", self.prop_etat_physique)

        self.prop_etat_fonctionnel = QComboBox()
        self.prop_etat_fonctionnel.addItems(["Opérationnel", "Partiellement opérationnel", "Non opérationnel", "En maintenance"])
        classification_layout.addRow("État fonctionnel:", self.prop_etat_fonctionnel)

        form_layout.addWidget(classification_group)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        btn_add = QPushButton(f"{AppConfig.ICONS['add']} Ajouter")
        btn_add.setObjectName("success")
        btn_add.clicked.connect(self.add_property)
        buttons_layout.addWidget(btn_add)

        btn_edit = QPushButton(f"{AppConfig.ICONS['edit']} Modifier")
        btn_edit.clicked.connect(self.edit_property)
        buttons_layout.addWidget(btn_edit)

        btn_delete = QPushButton(f"{AppConfig.ICONS['delete']} Supprimer")
        btn_delete.setObjectName("danger")
        btn_delete.clicked.connect(self.delete_property)
        buttons_layout.addWidget(btn_delete)

        btn_clear = QPushButton("🧹 Vider")
        btn_clear.setObjectName("warning")
        btn_clear.clicked.connect(self.clear_property_form)
        buttons_layout.addWidget(btn_clear)

        form_layout.addLayout(buttons_layout)
        form_layout.addStretch()

        splitter.addWidget(form_widget)

        # ===== PARTIE DROITE : LISTE ET FILTRES =====
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)

        # Filtres
        filters_group = QGroupBox("🔍 Filtres et Recherche")
        filters_layout = QGridLayout(filters_group)

        self.prop_search = QLineEdit()
        self.prop_search.setPlaceholderText("Rechercher par désignation, marque, modèle...")
        self.prop_search.textChanged.connect(self.filter_properties)
        filters_layout.addWidget(QLabel("Recherche:"), 0, 0)
        filters_layout.addWidget(self.prop_search, 0, 1)

        self.prop_filter_secteur = QComboBox()
        self.prop_filter_secteur.addItems(["Tous les secteurs", "Administration", "Informatique", "Comptabilité", "Commercial", "Production", "Logistique", "Maintenance", "Sécurité", "Autre"])
        self.prop_filter_secteur.currentTextChanged.connect(self.filter_properties)
        filters_layout.addWidget(QLabel("Secteur:"), 1, 0)
        filters_layout.addWidget(self.prop_filter_secteur, 1, 1)

        self.prop_filter_etat = QComboBox()
        self.prop_filter_etat.addItems(["Tous les états", "Excellent", "Bon", "Moyen", "Mauvais", "Hors service"])
        self.prop_filter_etat.currentTextChanged.connect(self.filter_properties)
        filters_layout.addWidget(QLabel("État:"), 2, 0)
        filters_layout.addWidget(self.prop_filter_etat, 2, 1)

        list_layout.addWidget(filters_group)

        # Liste des biens
        self.properties_list = QListWidget()
        self.properties_list.itemClicked.connect(self.select_property)
        list_layout.addWidget(self.properties_list)

        splitter.addWidget(list_widget)

        main_layout.addWidget(splitter)

        return widget

    # ===== MÉTHODES D'ACTIONS POUR LES BIENS =====

    def add_property(self) -> None:
        """Ajoute un nouveau bien immobilier"""
        try:
            # Validation
            if not self.prop_designation.text().strip():
                QMessageBox.warning(self, "Erreur", "La désignation est obligatoire.")
                return

            if self.prop_valeur_acquisition.value() <= 0:
                QMessageBox.warning(self, "Erreur", "La valeur d'acquisition doit être positive.")
                return

            # Génération du code-barres
            import time
            code_barre = f"GIP{int(time.time())}"

            # Insertion en base
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO biens_immobiliers (
                    designation, marque, modele, numero_serie, code_barre,
                    valeur_acquisition, valeur_residuelle, duree_amortissement,
                    date_acquisition, date_mise_service, localisation_batiment,
                    localisation_etage, localisation_bureau, secteur_activite,
                    etat_physique, etat_fonctionnel, coordonnees_gps,
                    garantie_fin, utilisateur_creation
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.prop_designation.text().strip(),
                self.prop_marque.text().strip(),
                self.prop_modele.text().strip(),
                self.prop_numero_serie.text().strip(),
                code_barre,
                self.prop_valeur_acquisition.value(),
                self.prop_valeur_residuelle.value(),
                self.prop_duree_amortissement.value(),
                self.prop_date_acquisition.date().toString("yyyy-MM-dd"),
                self.prop_date_mise_service.date().toString("yyyy-MM-dd"),
                self.prop_localisation_batiment.text().strip(),
                self.prop_localisation_etage.text().strip(),
                self.prop_localisation_bureau.text().strip(),
                self.prop_secteur_activite.currentText(),
                self.prop_etat_physique.currentText(),
                self.prop_etat_fonctionnel.currentText(),
                self.prop_coordonnees_gps.text().strip(),
                self.prop_garantie_fin.date().toString("yyyy-MM-dd"),
                self.current_user['username']
            ))

            self.conn.commit()
            self.clear_property_form()
            self.load_properties_data()

            QMessageBox.information(self, "Succès",
                                  f"Bien '{self.prop_designation.text()}' ajouté avec succès.\nCode-barres: {code_barre}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def edit_property(self) -> None:
        """Modifie le bien sélectionné"""
        current_item = self.properties_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à modifier.")
            return

        QMessageBox.information(self, "Modification",
                              "Modifiez les champs souhaités et cliquez sur 'Ajouter' pour sauvegarder.")

    def delete_property(self) -> None:
        """Supprime le bien sélectionné"""
        current_item = self.properties_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à supprimer.")
            return

        bien_id = current_item.data(1)

        reply = QMessageBox.question(self, "Confirmation",
                                   "Êtes-vous sûr de vouloir supprimer ce bien ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            try:
                cursor = self.conn.cursor()
                cursor.execute("DELETE FROM biens_immobiliers WHERE id = ?", (bien_id,))
                self.conn.commit()

                self.load_properties_data()
                self.clear_property_form()

                QMessageBox.information(self, "Succès", "Bien supprimé avec succès.")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {e}")

    def clear_property_form(self) -> None:
        """Vide le formulaire des biens"""
        self.prop_designation.clear()
        self.prop_marque.clear()
        self.prop_modele.clear()
        self.prop_numero_serie.clear()
        self.prop_code_barre.clear()
        self.prop_valeur_acquisition.setValue(1000.00)
        self.prop_valeur_residuelle.setValue(0.00)
        self.prop_duree_amortissement.setValue(5)
        self.prop_date_acquisition.setDate(QDate.currentDate())
        self.prop_date_mise_service.setDate(QDate.currentDate())
        self.prop_localisation_batiment.clear()
        self.prop_localisation_etage.clear()
        self.prop_localisation_bureau.clear()
        self.prop_coordonnees_gps.clear()
        self.prop_secteur_activite.setCurrentIndex(0)
        self.prop_etat_physique.setCurrentIndex(1)  # "Bon"
        self.prop_etat_fonctionnel.setCurrentIndex(0)  # "Opérationnel"
        self.prop_garantie_fin.setDate(QDate.currentDate().addYears(2))

    def filter_properties(self) -> None:
        """Filtre les biens selon les critères"""
        search_text = self.prop_search.text().lower()
        secteur_filter = self.prop_filter_secteur.currentText()
        etat_filter = self.prop_filter_etat.currentText()

        for i in range(self.properties_list.count()):
            item = self.properties_list.item(i)
            item_text = item.text().lower()

            # Filtrer par texte de recherche
            text_match = search_text in item_text if search_text else True

            # Filtrer par secteur
            secteur_match = True
            if secteur_filter != "Tous les secteurs":
                secteur_match = secteur_filter.lower() in item_text

            # Filtrer par état
            etat_match = True
            if etat_filter != "Tous les états":
                etat_match = etat_filter.lower() in item_text

            # Afficher/masquer l'item
            item.setHidden(not (text_match and secteur_match and etat_match))

    def select_property(self, item: QListWidgetItem) -> None:
        """Sélectionne un bien et remplit le formulaire"""
        try:
            bien_id = item.data(1)
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT designation, marque, modele, numero_serie, code_barre,
                       valeur_acquisition, valeur_residuelle, duree_amortissement,
                       date_acquisition, date_mise_service, localisation_batiment,
                       localisation_etage, localisation_bureau, secteur_activite,
                       etat_physique, etat_fonctionnel, coordonnees_gps, garantie_fin
                FROM biens_immobiliers WHERE id = ?
            ''', (bien_id,))

            row = cursor.fetchone()
            if row:
                self.prop_designation.setText(row[0] or "")
                self.prop_marque.setText(row[1] or "")
                self.prop_modele.setText(row[2] or "")
                self.prop_numero_serie.setText(row[3] or "")
                self.prop_code_barre.setText(row[4] or "")
                self.prop_valeur_acquisition.setValue(row[5] or 0)
                self.prop_valeur_residuelle.setValue(row[6] or 0)
                self.prop_duree_amortissement.setValue(row[7] or 5)

                if row[8]:
                    date_acq = QDate.fromString(row[8], "yyyy-MM-dd")
                    self.prop_date_acquisition.setDate(date_acq)

                if row[9]:
                    date_service = QDate.fromString(row[9], "yyyy-MM-dd")
                    self.prop_date_mise_service.setDate(date_service)

                self.prop_localisation_batiment.setText(row[10] or "")
                self.prop_localisation_etage.setText(row[11] or "")
                self.prop_localisation_bureau.setText(row[12] or "")

                # Sélectionner le secteur
                secteur_index = self.prop_secteur_activite.findText(row[13] or "Administration")
                if secteur_index >= 0:
                    self.prop_secteur_activite.setCurrentIndex(secteur_index)

                # Sélectionner l'état physique
                etat_phys_index = self.prop_etat_physique.findText(row[14] or "Bon")
                if etat_phys_index >= 0:
                    self.prop_etat_physique.setCurrentIndex(etat_phys_index)

                # Sélectionner l'état fonctionnel
                etat_fonc_index = self.prop_etat_fonctionnel.findText(row[15] or "Opérationnel")
                if etat_fonc_index >= 0:
                    self.prop_etat_fonctionnel.setCurrentIndex(etat_fonc_index)

                self.prop_coordonnees_gps.setText(row[16] or "")

                if row[17]:
                    date_garantie = QDate.fromString(row[17], "yyyy-MM-dd")
                    self.prop_garantie_fin.setDate(date_garantie)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sélection: {e}")

    def load_properties_data(self) -> None:
        """Charge les données des biens dans la liste"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT id, designation, valeur_acquisition, secteur_activite, etat_physique,
                       localisation_batiment, code_barre
                FROM biens_immobiliers ORDER BY id DESC
            ''')
            rows = cursor.fetchall()

            self.properties_list.clear()

            for row in rows:
                item_text = f"🏠 {row[1]} | {row[2]:,.2f}€ | {row[3]} | {row[4]} | 📍 {row[5] or 'N/A'}"
                item = QListWidgetItem(item_text)
                item.setData(1, row[0])  # Stocker l'ID
                self.properties_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des biens: {e}")

    # ===== AUTRES MODULES (STUBS AMÉLIORÉS) =====

    def create_inventory_module(self) -> QWidget:
        """Module inventaire avec critères spécifiques"""
        return self.create_module_stub('inventory')

    def create_suppliers_module(self) -> QWidget:
        """Module fournisseurs avec critères spécifiques"""
        return self.create_module_stub('suppliers')

    def create_clients_module(self) -> QWidget:
        """Module clients avec critères spécifiques"""
        return self.create_module_stub('clients')

    def create_invoicing_module(self) -> QWidget:
        """Module facturation avec critères spécifiques"""
        return self.create_module_stub('invoicing')

    # ===== MÉTHODES UTILITAIRES FINALES =====

    def load_initial_data(self) -> None:
        """Charge les données initiales"""
        if hasattr(self, 'properties_list'):
            self.load_properties_data()

    def setup_connections(self) -> None:
        """Configure les connexions de signaux"""
        self.module_changed.connect(self.on_module_changed)

    def on_module_changed(self, module_key: str) -> None:
        """Appelé quand le module change"""
        if module_key == 'properties' and hasattr(self, 'properties_list'):
            self.load_properties_data()

    def create_status_bar(self) -> None:
        """Crée la barre de statut"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.update_status_bar()

    def update_status_bar(self) -> None:
        """Met à jour la barre de statut"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM biens_immobiliers")
            nb_biens = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM fournisseurs WHERE statut='Actif'")
            nb_fournisseurs = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM clients")
            nb_clients = cursor.fetchone()[0]

            status_text = f"[STATS] {nb_biens} biens | 🏢 {nb_fournisseurs} fournisseurs | 👥 {nb_clients} clients | ✅ {AppConfig.APP_NAME} v{AppConfig.APP_VERSION} - Opérationnel"
            self.status_bar.showMessage(status_text)

        except Exception as e:
            self.status_bar.showMessage(f"[ECHEC] Erreur de statut: {e}")

    def create_menu_bar(self) -> None:
        """Crée la barre de menu"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        export_action = QAction("📤 Exporter Données", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        quit_action = QAction("🚪 Quitter", self)
        quit_action.triggered.connect(self.quit_application)
        file_menu.addAction(quit_action)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = QAction("ℹ️ À Propos", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def export_data(self) -> None:
        """Exporte les données"""
        QMessageBox.information(self, "Export", "Fonctionnalité d'export disponible dans la version complète.")

    def show_about(self) -> None:
        """Affiche les informations À propos"""
        QMessageBox.about(self, "À Propos", f"""
        <h2>🏠 {AppConfig.APP_NAME} v{AppConfig.APP_VERSION}</h2>
        <p><b>Logiciel de Gestion Immobilière Professionnel</b></p>

        <h3>[LANCE] Fonctionnalités:</h3>
        <ul>
        <li>[STATS] Tableau de bord avec KPI en temps réel</li>
        <li>🏠 Gestion complète des biens immobiliers</li>
        <li>📦 Inventaire physique avancé</li>
        <li>🏢 Gestion des fournisseurs</li>
        <li>👥 Gestion de la clientèle</li>
        <li>📄 Facturation intégrée</li>
        </ul>

        <h3>🎨 Interface:</h3>
        <p>Design professionnel inspiré des meilleurs logiciels d'entreprise</p>

        <h3>💻 Technologie:</h3>
        <p>Python 3.13+ et PySide6</p>

        <p><b>© 2024 {AppConfig.APP_COMPANY}</b></p>
        """)


# ===== FONCTION PRINCIPALE =====

def main() -> None:
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName(AppConfig.APP_NAME)
    app.setApplicationVersion(AppConfig.APP_VERSION)
    app.setOrganizationName(AppConfig.APP_COMPANY)

    # Créer et afficher la fenêtre principale
    window = GestImmobProfessional()
    window.show()

    # Lancer l'application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
