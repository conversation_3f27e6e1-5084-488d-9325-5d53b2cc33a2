# -*- coding: utf-8 -*-
"""
Test final d'encodage UTF-8 avec BOM pour le module inventaire
"""

import sys
import os
import json

def test_encodage_complet():
    """Test complet de l'encodage UTF-8 avec BOM."""
    print("🔍 TEST FINAL D'ENCODAGE UTF-8 AVEC BOM")
    print("=" * 60)
    
    # Test 1: Caractères français
    caracteres_francais = "àáâäèéêëìíîïòóôöùúûüçñ"
    print(f"✅ Caractères français: {caracteres_francais}")
    
    # Test 2: Émojis
    emojis = "📦🔄✅⚠️📊🗑️➕✏️📱🌐📄📋🔍"
    print(f"✅ Émojis: {emojis}")
    
    # Test 3: Mots français complets
    mots_francais = [
        "Réfrigérateur", "Électroménager", "Quantité", "Créé", 
        "Modifié", "Périmé", "Réform<PERSON>", "Café", "<PERSON><PERSON><PERSON>",
        "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"
    ]
    print("✅ Mots français:")
    for mot in mots_francais:
        print(f"   - {mot}")
    
    # Test 4: Caractères spéciaux
    caracteres_speciaux = "€ • ™ © ® ° ± × ÷"
    print(f"✅ Caractères spéciaux: {caracteres_speciaux}")
    
    # Test 5: Phrases complètes
    phrases = [
        "Réfrigérateur Samsung dans la cuisine principale",
        "Café moulu Arabica périmé le 15/03/2024",
        "Chaise ergonomique commandée en février",
        "Ordinateur portable en réparation - écran défaillant"
    ]
    print("✅ Phrases complètes:")
    for phrase in phrases:
        print(f"   - {phrase}")
    
    # Test 6: Écriture/lecture de fichier JSON avec UTF-8+BOM
    try:
        donnees_test = {
            "articles": [
                {
                    "designation": "Réfrigérateur Samsung",
                    "categorie": "Électroménager",
                    "responsable": "Marie Dupré",
                    "statut": "✅ Créé",
                    "alerte": "⚠️ Périmé"
                },
                {
                    "designation": "Café moulu Arabica",
                    "categorie": "Consommable",
                    "responsable": "Amélie Dubois",
                    "statut": "🔧 En réparation",
                    "alerte": "📱 Code scanné"
                }
            ],
            "caracteres_test": caracteres_francais,
            "emojis_test": emojis,
            "caracteres_speciaux": caracteres_speciaux
        }
        
        # Écriture avec UTF-8+BOM
        with open("test_encodage_final.json", "w", encoding="utf-8-sig") as f:
            json.dump(donnees_test, f, ensure_ascii=False, indent=2)
        
        # Lecture avec UTF-8+BOM
        with open("test_encodage_final.json", "r", encoding="utf-8-sig") as f:
            donnees_lues = json.load(f)
        
        print("✅ Test fichier JSON UTF-8+BOM réussi!")
        print(f"   Articles lus: {len(donnees_lues['articles'])}")
        print(f"   Caractères: {donnees_lues['caracteres_test']}")
        print(f"   Émojis: {donnees_lues['emojis_test']}")
        
        # Nettoyage
        os.remove("test_encodage_final.json")
        
    except Exception as e:
        print(f"❌ Erreur test fichier: {e}")
        return False
    
    # Test 7: Compilation du module inventaire
    try:
        import py_compile
        py_compile.compile("inventaire.py", doraise=True)
        print("✅ Compilation du module inventaire réussie!")
    except Exception as e:
        print(f"❌ Erreur compilation: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 TOUS LES TESTS D'ENCODAGE UTF-8 SONT RÉUSSIS!")
    print("✅ Le module inventaire.py est prêt pour la production")
    print("✅ Support complet des caractères français et émojis")
    print("✅ Encodage UTF-8 avec BOM fonctionnel")
    print("✅ Compatibilité Windows optimisée")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_encodage_complet()
    sys.exit(0 if success else 1)
