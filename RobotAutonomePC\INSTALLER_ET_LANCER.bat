@echo off
title 🚀 ROBOT IA - INSTALLATION + LANCEMENT AUTOMATIQUE
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🚀 ROBOT IA AUTONOME PC - INSTALLATION + LANCEMENT AUTOMATIQUE         ██
echo ██    📁 INSTALLATION BUREAU + LANCEMENT IMMÉDIAT                            ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 INSTALLATION ET LANCEMENT AUTOMATIQUE
echo 📁 Installation sur bureau
echo 🚀 Lancement immédiat après installation
echo 👤 Configuration pour SamNord@110577
echo.

echo 🔄 ÉTAPE 1: Installation sur bureau...
call "INSTALLATION_BUREAU_EXECUTABLE.bat"

echo.
echo 🔄 ÉTAPE 2: Vérification installation...
set "ROBOT_BUREAU=%USERPROFILE%\Desktop\RobotIA_SamNord"

if exist "%ROBOT_BUREAU%" (
    echo ✅ Installation bureau réussie !
    echo 📁 Dossier: %ROBOT_BUREAU%
) else (
    echo ❌ Erreur installation bureau !
    pause
    exit /b 1
)

echo.
echo 🔄 ÉTAPE 3: Test lancement Robot IA...
cd /d "%ROBOT_BUREAU%"

echo 🚀 Tentative lancement version exécutable...
if exist "executable\RobotIA_SamNord.exe" (
    echo ✅ Version exécutable trouvée !
    echo 🚀 Lancement RobotIA_SamNord.exe...
    start "" "executable\RobotIA_SamNord.exe"
    echo ✅ Robot IA lancé en version exécutable !
    goto SUCCESS
)

echo 🐍 Tentative lancement version Python...
python --version >nul 2>&1
if %errorLevel% equ 0 (
    if exist "robot_autonome_pc.py" (
        echo ✅ Version Python trouvée !
        echo 🚀 Lancement robot_autonome_pc.py...
        start "" python robot_autonome_pc.py
        echo ✅ Robot IA lancé en version Python !
        goto SUCCESS
    )
)

echo 📊 Tentative lancement menu universel...
if exist "LANCEMENT_FINAL_UNIVERSEL.bat" (
    echo ✅ Menu universel trouvé !
    echo 🚀 Lancement menu complet...
    start "" "LANCEMENT_FINAL_UNIVERSEL.bat"
    echo ✅ Menu universel lancé !
    goto SUCCESS
)

echo 🔐 Tentative lancement connexion sécurisée...
if exist "🔐 CONNEXION SAMNORD.bat" (
    echo ✅ Connexion sécurisée trouvée !
    echo 🚀 Lancement connexion SamNord...
    start "" "🔐 CONNEXION SAMNORD.bat"
    echo ✅ Connexion sécurisée lancée !
    goto SUCCESS
)

echo ❌ Aucune méthode de lancement disponible !
echo 🔧 Vérifiez l'installation
pause
exit /b 1

:SUCCESS
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         ✅ INSTALLATION + LANCEMENT RÉUSSIS !                             ██
echo ██         🎉 ROBOT IA AUTONOME PC OPÉRATIONNEL !                            ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎉 SUCCÈS COMPLET !
echo.
echo 📁 DOSSIER BUREAU: %ROBOT_BUREAU%
echo 🚀 ROBOT IA LANCÉ AUTOMATIQUEMENT
echo 👤 UTILISATEUR: SamNord@110577
echo 🔑 MOT DE PASSE: NorDine@22
echo.
echo 🖥️ RACCOURCIS BUREAU DISPONIBLES:
echo ✅ 🤖 Robot IA Autonome PC - SamNord
echo ✅ 🔐 Robot IA Sécurisé - SamNord
echo ✅ 📁 Dossier Robot IA - SamNord
echo ✅ 📋 Guide Robot IA - SamNord
echo.
echo 🎯 PROCHAINES UTILISATIONS:
echo Double-cliquer "🤖 Robot IA Autonome PC - SamNord" sur bureau
echo.
echo 🌟 PROFITEZ DE VOTRE ROBOT IA TRANSCENDANT !
echo.
timeout /t 5 >nul
echo 👋 Fermeture automatique...
exit /b 0
