('C:\\Users\\<USER>\\Desktop\\logiciel '
 'GestImmob\\gestion-software\\build\\GestImmob\\PYZ-00.pyz',
 [('PIL',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.ImageCms',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('PIL._util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL._version',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('PIL.features',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('PySide6',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE-2'),
  ('PySide6.support',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE-2'),
  ('PySide6.support.deprecated',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE-2'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE-2'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE-2'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE-2'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE-2'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE-2'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE-2'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE-2'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils.qt',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE-2'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE-2'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE-2'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE-2'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE-2'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE-2'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE-2'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('auth', '-', 'PYMODULE-2'),
  ('auth.authentication',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\auth\\authentication.py',
   'PYMODULE-2'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE-2'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE-2'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE-2'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE-2'),
  ('calculatrice',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\calculatrice.py',
   'PYMODULE-2'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE-2'),
  ('certifi',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-2'),
  ('certifi.core',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-2'),
  ('charset_normalizer',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer.api',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-2'),
  ('charset_normalizer.cd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-2'),
  ('charset_normalizer.constant',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-2'),
  ('charset_normalizer.legacy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-2'),
  ('charset_normalizer.models',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-2'),
  ('charset_normalizer.utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-2'),
  ('charset_normalizer.version',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-2'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE-2'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE-2'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE-2'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE-2'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('config',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\config.py',
   'PYMODULE-2'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE-2'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE-2'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE-2'),
  ('cryptography',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.__about__',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE-2'),
  ('cryptography.exceptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE-2'),
  ('cryptography.fernet',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE-2'),
  ('cryptography.hazmat',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat._oid',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.backends',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.decrepit',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.decrepit.ciphers',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives._asymmetric',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives._serialization',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.hmac',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.padding',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE-2'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE-2'),
  ('cryptography.utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE-2'),
  ('cryptography.x509',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE-2'),
  ('cryptography.x509.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE-2'),
  ('cryptography.x509.certificate_transparency',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE-2'),
  ('cryptography.x509.extensions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE-2'),
  ('cryptography.x509.general_name',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE-2'),
  ('cryptography.x509.name',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE-2'),
  ('cryptography.x509.oid',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE-2'),
  ('cryptography.x509.verification',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE-2'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE-2'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE-2'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE-2'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-2'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-2'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE-2'),
  ('database', '-', 'PYMODULE-2'),
  ('database.db_connection',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\database\\db_connection.py',
   'PYMODULE-2'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE-2'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE-2'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE-2'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE-2'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE-2'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE-2'),
  ('document',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\document.py',
   'PYMODULE-2'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE-2'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE-2'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fileinput.py',
   'PYMODULE-2'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE-2'),
  ('fournisseur',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\fournisseur.py',
   'PYMODULE-2'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE-2'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE-2'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE-2'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE-2'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE-2'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE-2'),
  ('greenlet',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE-2'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE-2'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE-2'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE-2'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE-2'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE-2'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE-2'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE-2'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookies.py',
   'PYMODULE-2'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE-2'),
  ('idna',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-2'),
  ('idna.core',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-2'),
  ('idna.idnadata',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-2'),
  ('idna.intranges',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-2'),
  ('idna.package_data',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-2'),
  ('idna.uts46data',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-2'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('impression',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\impression.py',
   'PYMODULE-2'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE-2'),
  ('inventaire',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\inventaire.py',
   'PYMODULE-2'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE-2'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE-2'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE-2'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE-2'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE-2'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE-2'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE-2'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE-2'),
  ('modules', '-', 'PYMODULE-2'),
  ('modules.settings_manager',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\modules\\settings_manager.py',
   'PYMODULE-2'),
  ('modules.sql_manager',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\modules\\sql_manager.py',
   'PYMODULE-2'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE-2'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE-2'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE-2'),
  ('numpy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy._core',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.tests', '-', 'PYMODULE-2'),
  ('numpy._core.tests._natype',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy.char',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.f2py',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py.__version__',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._backend',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._distutils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._meson',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-2'),
  ('numpy.f2py._isocbind',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-2'),
  ('numpy.f2py.auxfuncs',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.capi_maps',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-2'),
  ('numpy.f2py.cb_rules',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.cfuncs',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.common_rules',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.crackfortran',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-2'),
  ('numpy.f2py.diagnose',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-2'),
  ('numpy.f2py.f2py2e',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-2'),
  ('numpy.f2py.f90mod_rules',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.func2subr',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-2'),
  ('numpy.f2py.rules',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.symbolic',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-2'),
  ('numpy.f2py.use_rules',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.recfunctions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private.extbuild',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-2'),
  ('numpy.testing._private.utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-2'),
  ('numpy.testing.overrides',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.version',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE-2'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE-2'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE-2'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE-2'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE-2'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE-2'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE-2'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE-2'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE-2'),
  ('psutil',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE-2'),
  ('psutil._common',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE-2'),
  ('psutil._pswindows',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE-2'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE-2'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE-2'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE-2'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE-2'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE-2'),
  ('recherche',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\recherche.py',
   'PYMODULE-2'),
  ('reforme',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\reforme.py',
   'PYMODULE-2'),
  ('requests',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-2'),
  ('requests.__version__',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-2'),
  ('requests._internal_utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-2'),
  ('requests.adapters',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-2'),
  ('requests.api',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-2'),
  ('requests.auth',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-2'),
  ('requests.certs',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-2'),
  ('requests.compat',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-2'),
  ('requests.cookies',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-2'),
  ('requests.exceptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-2'),
  ('requests.hooks',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-2'),
  ('requests.models',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-2'),
  ('requests.packages',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-2'),
  ('requests.sessions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-2'),
  ('requests.status_codes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-2'),
  ('requests.structures',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-2'),
  ('requests.utils',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-2'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE-2'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE-2'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE-2'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE-2'),
  ('shiboken6',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE-2'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE-2'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE-2'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE-2'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE-2'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE-2'),
  ('sqlalchemy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.connectors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.connectors.aioodbc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE-2'),
  ('sqlalchemy.connectors.asyncio',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE-2'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE-2'),
  ('sqlalchemy.cyextension',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects._typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql.json',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.expression',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle.types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.oracle.vector',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.operators',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.postgresql.types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite.dml',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE-2'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine._py_processors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine._py_row',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine._py_util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.characteristics',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.create',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.cursor',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.default',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.events',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.interfaces',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.mock',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.processors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.reflection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.result',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.row',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.url',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE-2'),
  ('sqlalchemy.engine.util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE-2'),
  ('sqlalchemy.event',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.event.api',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE-2'),
  ('sqlalchemy.event.attr',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE-2'),
  ('sqlalchemy.event.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.event.legacy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE-2'),
  ('sqlalchemy.event.registry',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE-2'),
  ('sqlalchemy.exc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio.engine',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio.exc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio.result',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio.scoping',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.asyncio.session',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.baked',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE-2'),
  ('sqlalchemy.ext.compiler',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE-2'),
  ('sqlalchemy.future',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.future.engine',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE-2'),
  ('sqlalchemy.inspection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE-2'),
  ('sqlalchemy.log',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm._orm_constructors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm._typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.attributes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.bulk_persistence',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.clsregistry',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.collections',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.context',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.decl_api',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.decl_base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.dependency',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.descriptor_props',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.dynamic',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.evaluator',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.events',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.exc',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.identity',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.instrumentation',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.interfaces',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.loading',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.mapped_collection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.mapper',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.path_registry',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.persistence',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.properties',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.query',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.relationships',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.scoping',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.session',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.state',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.state_changes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.strategies',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.strategy_options',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.sync',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.unitofwork',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE-2'),
  ('sqlalchemy.orm.writeonly',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE-2'),
  ('sqlalchemy.pool',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.pool.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.pool.events',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE-2'),
  ('sqlalchemy.pool.impl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE-2'),
  ('sqlalchemy.schema',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql._dml_constructors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql._elements_constructors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql._orm_types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql._py_util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql._selectable_constructors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql._typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.annotation',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.base',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.cache_key',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.coercions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.compiler',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.crud',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.ddl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.dml',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.elements',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.events',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.expression',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.functions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.lambdas',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.naming',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.operators',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.roles',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.schema',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.selectable',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.traversals',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.type_api',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE-2'),
  ('sqlalchemy.sql.visitors',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE-2'),
  ('sqlalchemy.types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE-2'),
  ('sqlalchemy.util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE-2'),
  ('sqlalchemy.util._collections',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE-2'),
  ('sqlalchemy.util._concurrency_py3k',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE-2'),
  ('sqlalchemy.util._has_cy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE-2'),
  ('sqlalchemy.util._py_collections',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.compat',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.concurrency',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.deprecations',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.langhelpers',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.preloaded',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.queue',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.topological',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE-2'),
  ('sqlalchemy.util.typing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE-2'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE-2'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE-2'),
  ('src', '-', 'PYMODULE-2'),
  ('src.auth', '-', 'PYMODULE-2'),
  ('src.auth.authentication',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\auth\\authentication.py',
   'PYMODULE-2'),
  ('src.config',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\config.py',
   'PYMODULE-2'),
  ('src.database', '-', 'PYMODULE-2'),
  ('src.database.db_connection',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\database\\db_connection.py',
   'PYMODULE-2'),
  ('src.main',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\main.py',
   'PYMODULE-2'),
  ('src.utils', '-', 'PYMODULE-2'),
  ('src.utils.config',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\config.py',
   'PYMODULE-2'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE-2'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE-2'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE-2'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE-2'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE-2'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE-2'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE-2'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE-2'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE-2'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE-2'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE-2'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE-2'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE-2'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE-2'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE-2'),
  ('typing_extensions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-2'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE-2'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE-2'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE-2'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE-2'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE-2'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE-2'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE-2'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE-2'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE-2'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE-2'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE-2'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE-2'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib3',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-2'),
  ('urllib3._base_connection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-2'),
  ('urllib3._collections',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-2'),
  ('urllib3._request_methods',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-2'),
  ('urllib3._version',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-2'),
  ('urllib3.connection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-2'),
  ('urllib3.connectionpool',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-2'),
  ('urllib3.contrib',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.connection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.fetch',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.request',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.response',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-2'),
  ('urllib3.contrib.pyopenssl',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-2'),
  ('urllib3.contrib.socks',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-2'),
  ('urllib3.exceptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3.fields',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-2'),
  ('urllib3.filepost',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-2'),
  ('urllib3.http2',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.http2.connection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-2'),
  ('urllib3.http2.probe',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-2'),
  ('urllib3.poolmanager',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-2'),
  ('urllib3.response',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-2'),
  ('urllib3.util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.util.connection',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-2'),
  ('urllib3.util.proxy',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-2'),
  ('urllib3.util.request',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-2'),
  ('urllib3.util.response',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-2'),
  ('urllib3.util.retry',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-2'),
  ('urllib3.util.ssltransport',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-2'),
  ('urllib3.util.timeout',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-2'),
  ('urllib3.util.url',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-2'),
  ('urllib3.util.util',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-2'),
  ('urllib3.util.wait',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-2'),
  ('utils', '-', 'PYMODULE-2'),
  ('utils.config',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\config.py',
   'PYMODULE-2'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE-2'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE-2'),
  ('xlsxwriter',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE-2'),
  ('xlsxwriter.app',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_area',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_bar',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_column',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_doughnut',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_line',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_pie',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_radar',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_scatter',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_stock',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE-2'),
  ('xlsxwriter.chartsheet',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.comments',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE-2'),
  ('xlsxwriter.contenttypes',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE-2'),
  ('xlsxwriter.core',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE-2'),
  ('xlsxwriter.custom',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE-2'),
  ('xlsxwriter.drawing',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE-2'),
  ('xlsxwriter.exceptions',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE-2'),
  ('xlsxwriter.feature_property_bag',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE-2'),
  ('xlsxwriter.format',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE-2'),
  ('xlsxwriter.image',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE-2'),
  ('xlsxwriter.metadata',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE-2'),
  ('xlsxwriter.packager',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE-2'),
  ('xlsxwriter.relationships',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_rel',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_structure',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_types',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE-2'),
  ('xlsxwriter.shape',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE-2'),
  ('xlsxwriter.sharedstrings',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE-2'),
  ('xlsxwriter.styles',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE-2'),
  ('xlsxwriter.table',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE-2'),
  ('xlsxwriter.theme',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE-2'),
  ('xlsxwriter.url',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE-2'),
  ('xlsxwriter.utility',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE-2'),
  ('xlsxwriter.vml',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE-2'),
  ('xlsxwriter.workbook',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE-2'),
  ('xlsxwriter.worksheet',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.xmlwriter',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE-2'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE-2'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE-2'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE-2'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE-2'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE-2')])
