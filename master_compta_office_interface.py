#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Microsoft Office 2024
Interface Ribbon identique • Léger • Support universel
Adapté fonctions MASTER COMPTA • Principe Microsoft
"""

import sys
import os
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QTabWidget, QToolBar, QMenuBar,
    QMenu, QSplitter, QTextEdit, QTableWidget, QTableWidgetItem,
    QComboBox, QSpinBox, QLineEdit, QGroupBox, QGridLayout, QScrollArea,
    QStatusBar, QProgressBar, QFileDialog, QMessageBox, QButtonGroup
)
from PySide6.QtCore import Qt, QTimer, QSize, Signal
from PySide6.QtGui import <PERSON>Font, Q<PERSON>con, QPixmap, Q<PERSON><PERSON>ter, QColor, QAction

class OfficeRibbonTab(QWidget):
    """Onglet Ribbon style Office 2024"""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ribbon_tab()

    def setup_ribbon_tab(self):
        """Configure l'onglet Ribbon"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # Style Office 2024
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: none;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 9pt;
                color: #495057;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 5px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
                background: white;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 6px 12px;
                font-size: 9pt;
                color: #495057;
                min-width: 60px;
                min-height: 24px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 1px solid #2196f3;
                color: #1976d2;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #bbdefb, stop:1 #90caf9);
                border: 1px solid #1976d2;
            }
        """)

    def add_group(self, title, buttons):
        """Ajoute un groupe de boutons"""
        group = QGroupBox(title)
        group_layout = QGridLayout(group)
        group_layout.setSpacing(3)

        row, col = 0, 0
        for btn_config in buttons:
            if isinstance(btn_config, dict):
                btn = QPushButton(btn_config.get('text', ''))
                if 'icon' in btn_config:
                    btn.setText(f"{btn_config['icon']} {btn_config['text']}")
                if 'callback' in btn_config:
                    btn.clicked.connect(btn_config['callback'])
                if 'size' in btn_config:
                    if btn_config['size'] == 'large':
                        btn.setMinimumSize(80, 60)
                        btn.setStyleSheet(btn.styleSheet() + """
                            QPushButton {
                                font-size: 10pt;
                                font-weight: bold;
                            }
                        """)
                    elif btn_config['size'] == 'small':
                        btn.setMinimumSize(50, 24)
            else:
                btn = QPushButton(btn_config)

            group_layout.addWidget(btn, row, col)
            col += 1
            if col > 2:  # 3 colonnes max
                col = 0
                row += 1

        self.layout().addWidget(group)
        return group

class OfficeRibbonWidget(QWidget):
    """Widget Ribbon principal style Office 2024"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ribbon()

    def setup_ribbon(self):
        """Configure le Ribbon"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Style général Office 2024
        self.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-top: none;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-bottom: none;
                padding: 8px 16px;
                margin-right: 2px;
                font-size: 10pt;
                color: #495057;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
                color: #2196f3;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                color: #1976d2;
            }
        """)

        # Onglets Ribbon
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)

        # Créer les onglets Office
        self.create_home_tab()
        self.create_insert_tab()
        self.create_data_tab()
        self.create_formulas_tab()
        self.create_review_tab()
        self.create_view_tab()
        self.create_master_compta_tab()

        layout.addWidget(self.tab_widget)

    def create_home_tab(self):
        """Crée l'onglet Accueil"""
        home_tab = OfficeRibbonTab("Accueil")

        # Groupe Presse-papiers
        clipboard_buttons = [
            {'text': 'Coller', 'icon': '📋', 'size': 'large', 'callback': self.paste_action},
            {'text': 'Couper', 'icon': '✂️', 'size': 'small', 'callback': self.cut_action},
            {'text': 'Copier', 'icon': '📄', 'size': 'small', 'callback': self.copy_action}
        ]
        home_tab.add_group("Presse-papiers", clipboard_buttons)

        # Groupe Police
        font_buttons = [
            {'text': 'Gras', 'icon': '𝐁', 'size': 'small', 'callback': self.bold_action},
            {'text': 'Italique', 'icon': '𝐼', 'size': 'small', 'callback': self.italic_action},
            {'text': 'Souligné', 'icon': '𝐔', 'size': 'small', 'callback': self.underline_action}
        ]
        home_tab.add_group("Police", font_buttons)

        # Groupe Alignement
        align_buttons = [
            {'text': 'Gauche', 'icon': '⬅️', 'size': 'small', 'callback': self.align_left},
            {'text': 'Centre', 'icon': '↔️', 'size': 'small', 'callback': self.align_center},
            {'text': 'Droite', 'icon': '➡️', 'size': 'small', 'callback': self.align_right}
        ]
        home_tab.add_group("Alignement", align_buttons)

        self.tab_widget.addTab(home_tab, "🏠 Accueil")

    def create_insert_tab(self):
        """Crée l'onglet Insertion"""
        insert_tab = OfficeRibbonTab("Insertion")

        # Groupe Tableaux
        table_buttons = [
            {'text': 'Tableau', 'icon': '📊', 'size': 'large', 'callback': self.insert_table},
            {'text': 'Graphique', 'icon': '📈', 'size': 'small', 'callback': self.insert_chart},
            {'text': 'Image', 'icon': '🖼️', 'size': 'small', 'callback': self.insert_image}
        ]
        insert_tab.add_group("Tableaux", table_buttons)

        # Groupe Illustrations
        illustration_buttons = [
            {'text': 'Formes', 'icon': '🔷', 'size': 'small', 'callback': self.insert_shapes},
            {'text': 'Icônes', 'icon': '🎨', 'size': 'small', 'callback': self.insert_icons},
            {'text': 'SmartArt', 'icon': '🎯', 'size': 'small', 'callback': self.insert_smartart}
        ]
        insert_tab.add_group("Illustrations", illustration_buttons)

        self.tab_widget.addTab(insert_tab, "📝 Insertion")

    def create_data_tab(self):
        """Crée l'onglet Données"""
        data_tab = OfficeRibbonTab("Données")

        # Groupe Obtenir des données
        get_data_buttons = [
            {'text': 'Excel', 'icon': '📊', 'size': 'large', 'callback': self.import_excel},
            {'text': 'CSV', 'icon': '📄', 'size': 'small', 'callback': self.import_csv},
            {'text': 'Base', 'icon': '🗃️', 'size': 'small', 'callback': self.import_database}
        ]
        data_tab.add_group("Obtenir des données", get_data_buttons)

        # Groupe Outils de données
        data_tools_buttons = [
            {'text': 'Trier', 'icon': '🔄', 'size': 'small', 'callback': self.sort_data},
            {'text': 'Filtrer', 'icon': '🔍', 'size': 'small', 'callback': self.filter_data},
            {'text': 'Valider', 'icon': '✅', 'size': 'small', 'callback': self.validate_data}
        ]
        data_tab.add_group("Outils de données", data_tools_buttons)

        self.tab_widget.addTab(data_tab, "📊 Données")

    def create_formulas_tab(self):
        """Crée l'onglet Formules"""
        formulas_tab = OfficeRibbonTab("Formules")

        # Groupe Bibliothèque de fonctions
        functions_buttons = [
            {'text': 'Somme', 'icon': '∑', 'size': 'large', 'callback': self.insert_sum},
            {'text': 'Moyenne', 'icon': '📊', 'size': 'small', 'callback': self.insert_average},
            {'text': 'Max/Min', 'icon': '📈', 'size': 'small', 'callback': self.insert_maxmin}
        ]
        formulas_tab.add_group("Bibliothèque de fonctions", functions_buttons)

        # Groupe Calcul
        calc_buttons = [
            {'text': 'Calculer', 'icon': '🧮', 'size': 'small', 'callback': self.calculate_now},
            {'text': 'Options', 'icon': '⚙️', 'size': 'small', 'callback': self.calc_options}
        ]
        formulas_tab.add_group("Calcul", calc_buttons)

        self.tab_widget.addTab(formulas_tab, "🧮 Formules")

    def create_review_tab(self):
        """Crée l'onglet Révision"""
        review_tab = OfficeRibbonTab("Révision")

        # Groupe Vérification
        proofing_buttons = [
            {'text': 'Orthographe', 'icon': '📝', 'size': 'large', 'callback': self.spell_check},
            {'text': 'Grammaire', 'icon': '📖', 'size': 'small', 'callback': self.grammar_check}
        ]
        review_tab.add_group("Vérification", proofing_buttons)

        # Groupe Commentaires
        comments_buttons = [
            {'text': 'Nouveau', 'icon': '💬', 'size': 'small', 'callback': self.new_comment},
            {'text': 'Supprimer', 'icon': '🗑️', 'size': 'small', 'callback': self.delete_comment}
        ]
        review_tab.add_group("Commentaires", comments_buttons)

        self.tab_widget.addTab(review_tab, "📋 Révision")

    def create_view_tab(self):
        """Crée l'onglet Affichage"""
        view_tab = OfficeRibbonTab("Affichage")

        # Groupe Affichages classeur
        workbook_views_buttons = [
            {'text': 'Normal', 'icon': '📄', 'size': 'small', 'callback': self.normal_view},
            {'text': 'Mise en page', 'icon': '📋', 'size': 'small', 'callback': self.page_layout_view},
            {'text': 'Aperçu', 'icon': '👁️', 'size': 'small', 'callback': self.page_break_preview}
        ]
        view_tab.add_group("Affichages classeur", workbook_views_buttons)

        # Groupe Zoom
        zoom_buttons = [
            {'text': 'Zoom', 'icon': '🔍', 'size': 'large', 'callback': self.zoom_dialog},
            {'text': '100%', 'icon': '💯', 'size': 'small', 'callback': self.zoom_100},
            {'text': 'Ajuster', 'icon': '📐', 'size': 'small', 'callback': self.zoom_fit}
        ]
        view_tab.add_group("Zoom", zoom_buttons)

        self.tab_widget.addTab(view_tab, "👁️ Affichage")

    def create_master_compta_tab(self):
        """Crée l'onglet spécial MASTER COMPTA"""
        master_tab = OfficeRibbonTab("MASTER COMPTA")

        # Style spécial pour l'onglet MASTER COMPTA
        master_tab.setStyleSheet(master_tab.styleSheet() + """
            QGroupBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3e0, stop:1 #ffe0b2);
                border: 2px solid #ff9800;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3e0, stop:1 #ffcc02);
                border: 1px solid #ff9800;
                color: #e65100;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffcc02, stop:1 #ffa000);
                border: 1px solid #e65100;
                color: white;
            }
        """)

        # Groupe Modules MASTER COMPTA
        modules_buttons = [
            {'text': 'Parc Auto', 'icon': '🚗', 'size': 'large', 'callback': self.open_parc_auto},
            {'text': 'Immobilier', 'icon': '🏢', 'size': 'small', 'callback': self.open_immobilier},
            {'text': 'Personnel', 'icon': '👥', 'size': 'small', 'callback': self.open_personnel}
        ]
        master_tab.add_group("Modules", modules_buttons)

        # Groupe Bases de données
        database_buttons = [
            {'text': 'Anomalies', 'icon': '🔍', 'size': 'small', 'callback': self.detect_anomalies},
            {'text': 'Sauvegarde', 'icon': '💾', 'size': 'small', 'callback': self.backup_data},
            {'text': 'IA Ollama', 'icon': '🤖', 'size': 'small', 'callback': self.open_ai_analysis}
        ]
        master_tab.add_group("Bases de données", database_buttons)

        # Groupe Rapports
        reports_buttons = [
            {'text': 'Générer', 'icon': '📊', 'size': 'large', 'callback': self.generate_report},
            {'text': 'Exporter', 'icon': '📤', 'size': 'small', 'callback': self.export_report},
            {'text': 'Imprimer', 'icon': '🖨️', 'size': 'small', 'callback': self.print_report}
        ]
        master_tab.add_group("Rapports", reports_buttons)

        self.tab_widget.addTab(master_tab, "🏢 MASTER COMPTA")

    # Méthodes d'action (callbacks)
    def paste_action(self): print("📋 Coller")
    def cut_action(self): print("✂️ Couper")
    def copy_action(self): print("📄 Copier")
    def bold_action(self): print("𝐁 Gras")
    def italic_action(self): print("𝐼 Italique")
    def underline_action(self): print("𝐔 Souligné")
    def align_left(self): print("⬅️ Aligner à gauche")
    def align_center(self): print("↔️ Centrer")
    def align_right(self): print("➡️ Aligner à droite")
    def insert_table(self): print("📊 Insérer tableau")
    def insert_chart(self): print("📈 Insérer graphique")
    def insert_image(self): print("🖼️ Insérer image")
    def insert_shapes(self): print("🔷 Insérer formes")
    def insert_icons(self): print("🎨 Insérer icônes")
    def insert_smartart(self): print("🎯 Insérer SmartArt")
    def import_excel(self): print("📊 Importer Excel")
    def import_csv(self): print("📄 Importer CSV")
    def import_database(self): print("🗃️ Importer base de données")
    def sort_data(self): print("🔄 Trier données")
    def filter_data(self): print("🔍 Filtrer données")
    def validate_data(self): print("✅ Valider données")
    def insert_sum(self): print("∑ Insérer somme")
    def insert_average(self): print("📊 Insérer moyenne")
    def insert_maxmin(self): print("📈 Insérer max/min")
    def calculate_now(self): print("🧮 Calculer maintenant")
    def calc_options(self): print("⚙️ Options de calcul")
    def spell_check(self): print("📝 Vérification orthographe")
    def grammar_check(self): print("📖 Vérification grammaire")
    def new_comment(self): print("💬 Nouveau commentaire")
    def delete_comment(self): print("🗑️ Supprimer commentaire")
    def normal_view(self): print("📄 Affichage normal")
    def page_layout_view(self): print("📋 Mise en page")
    def page_break_preview(self): print("👁️ Aperçu des sauts")
    def zoom_dialog(self): print("🔍 Dialogue zoom")
    def zoom_100(self): print("💯 Zoom 100%")
    def zoom_fit(self): print("📐 Ajuster zoom")
    def open_parc_auto(self): print("🚗 Ouvrir Parc Auto")
    def open_immobilier(self): print("🏢 Ouvrir Immobilier")
    def open_personnel(self): print("👥 Ouvrir Personnel")
    def detect_anomalies(self): print("🔍 Détecter anomalies")
    def backup_data(self): print("💾 Sauvegarder données")
    def open_ai_analysis(self): print("🤖 Analyse IA")
    def generate_report(self): print("📊 Générer rapport")
    def export_report(self): print("📤 Exporter rapport")
    def print_report(self): print("🖨️ Imprimer rapport")

class MasterComptaOfficeInterface(QMainWindow):
    """Interface principale style Microsoft Office 2024"""

    def __init__(self):
        super().__init__()
        self.setup_office_interface()

    def setup_office_interface(self):
        """Configure l'interface Office"""
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Microsoft Office 2024")

        # Taille et position
        screen = QApplication.primaryScreen().geometry()
        width = min(1600, int(screen.width() * 0.9))
        height = min(1000, int(screen.height() * 0.9))
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        # Style général Office 2024
        self.setStyleSheet("""
            QMainWindow {
                background: #f8f9fa;
                color: #495057;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 1px solid #dee2e6;
                padding: 4px;
                font-size: 10pt;
            }
            QMenuBar::item {
                background: transparent;
                padding: 6px 12px;
                border-radius: 3px;
            }
            QMenuBar::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-top: 1px solid #dee2e6;
                color: #6c757d;
                font-size: 9pt;
            }
        """)

        # Barre de menu Office
        self.create_office_menubar()

        # Widget central avec Ribbon
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Ribbon Office
        self.ribbon = OfficeRibbonWidget()
        main_layout.addWidget(self.ribbon)

        # Zone de travail
        self.create_work_area(main_layout)

        # Barre de statut Office
        self.create_office_statusbar()

    def create_office_menubar(self):
        """Crée la barre de menu Office"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")
        file_menu.addAction("🆕 Nouveau", self.new_file)
        file_menu.addAction("📂 Ouvrir", self.open_file)
        file_menu.addAction("💾 Enregistrer", self.save_file)
        file_menu.addAction("💾 Enregistrer sous", self.save_as_file)
        file_menu.addSeparator()
        file_menu.addAction("🖨️ Imprimer", self.print_file)
        file_menu.addSeparator()
        file_menu.addAction("❌ Fermer", self.close)

        # Menu Édition
        edit_menu = menubar.addMenu("✏️ Édition")
        edit_menu.addAction("↩️ Annuler", self.undo_action)
        edit_menu.addAction("↪️ Rétablir", self.redo_action)
        edit_menu.addSeparator()
        edit_menu.addAction("✂️ Couper", self.cut_action)
        edit_menu.addAction("📄 Copier", self.copy_action)
        edit_menu.addAction("📋 Coller", self.paste_action)

        # Menu Affichage
        view_menu = menubar.addMenu("👁️ Affichage")
        view_menu.addAction("🔍 Zoom avant", self.zoom_in)
        view_menu.addAction("🔍 Zoom arrière", self.zoom_out)
        view_menu.addAction("💯 Zoom 100%", self.zoom_100)
        view_menu.addSeparator()
        view_menu.addAction("📏 Règle", self.toggle_ruler)
        view_menu.addAction("📐 Grille", self.toggle_grid)

        # Menu MASTER COMPTA
        master_menu = menubar.addMenu("🏢 MASTER COMPTA")
        master_menu.addAction("🚗 Parc Auto", self.open_parc_auto)
        master_menu.addAction("🏢 Immobilier", self.open_immobilier)
        master_menu.addAction("👥 Personnel", self.open_personnel)
        master_menu.addSeparator()
        master_menu.addAction("🗃️ Bases de données", self.open_database_system)
        master_menu.addAction("🤖 IA Ollama", self.open_ai_analysis)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")
        help_menu.addAction("📖 Documentation", self.show_help)
        help_menu.addAction("ℹ️ À propos", self.show_about)

    def create_work_area(self, layout):
        """Crée la zone de travail"""
        # Splitter principal
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Zone de document (style Excel/Word)
        self.document_area = QWidget()
        self.document_area.setStyleSheet("""
            QWidget {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin: 5px;
            }
        """)

        # Layout de la zone document
        doc_layout = QVBoxLayout(self.document_area)

        # Barre d'outils rapide (style Office)
        quick_toolbar = self.create_quick_toolbar()
        doc_layout.addWidget(quick_toolbar)

        # Zone de contenu principal
        content_area = QWidget()
        content_area.setStyleSheet("""
            QWidget {
                background: white;
                border: none;
                margin: 10px;
            }
        """)
        content_layout = QVBoxLayout(content_area)

        # Titre de bienvenue
        welcome_title = QLabel("🏢 MASTER COMPTA GÉNÉRAL")
        welcome_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_title.setStyleSheet("""
            QLabel {
                font-size: 24pt;
                font-weight: bold;
                color: #2196f3;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 2px solid #2196f3;
                border-radius: 8px;
                padding: 20px;
                margin: 20px;
            }
        """)
        content_layout.addWidget(welcome_title)

        # Description
        description = QLabel("Interface Microsoft Office 2024 • Ribbon • Léger • Support Universel")
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                color: #6c757d;
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
                margin: 10px 20px;
            }
        """)
        content_layout.addWidget(description)

        # Zone de travail simulée (tableau style Excel)
        self.work_table = QTableWidget(10, 8)
        self.work_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin: 10px 20px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                padding: 6px;
                font-weight: bold;
                color: #495057;
            }
            QTableWidget::item {
                border: 1px solid #dee2e6;
                padding: 4px;
            }
            QTableWidget::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
        """)

        # En-têtes colonnes style Excel
        headers = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
        self.work_table.setHorizontalHeaderLabels(headers)

        # Données d'exemple
        sample_data = [
            ['Produit', 'Quantité', 'Prix', 'Total', '', '', '', ''],
            ['Article 1', '10', '25.50', '255.00', '', '', '', ''],
            ['Article 2', '5', '45.00', '225.00', '', '', '', ''],
            ['Article 3', '8', '12.75', '102.00', '', '', '', ''],
            ['', '', 'TOTAL:', '582.00', '', '', '', ''],
        ]

        for row, row_data in enumerate(sample_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                if row == 0:  # En-tête
                    item.setBackground(QColor('#e3f2fd'))
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                elif row == 4 and col == 2:  # Total
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                    item.setBackground(QColor('#fff3e0'))
                elif row == 4 and col == 3:  # Valeur total
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                    item.setBackground(QColor('#ffcc02'))
                self.work_table.setItem(row, col, item)

        content_layout.addWidget(self.work_table)

        doc_layout.addWidget(content_area)

        # Panel latéral (style Office)
        side_panel = self.create_side_panel()

        # Ajouter au splitter
        splitter.addWidget(self.document_area)
        splitter.addWidget(side_panel)
        splitter.setSizes([1200, 300])  # 80% document, 20% panel

        layout.addWidget(splitter)

    def create_quick_toolbar(self):
        """Crée la barre d'outils rapide"""
        toolbar = QWidget()
        toolbar.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-bottom: 1px solid #dee2e6;
                margin: 0px;
                padding: 4px;
            }
            QPushButton {
                background: transparent;
                border: 1px solid transparent;
                border-radius: 3px;
                padding: 4px 8px;
                margin: 1px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background: #e3f2fd;
                border: 1px solid #2196f3;
                color: #1976d2;
            }
        """)

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(5, 2, 5, 2)

        # Boutons rapides
        quick_buttons = [
            "💾", "📂", "🖨️", "↩️", "↪️", "📋", "🔍"
        ]

        for btn_text in quick_buttons:
            btn = QPushButton(btn_text)
            btn.setMaximumSize(24, 24)
            layout.addWidget(btn)

        layout.addStretch()

        # Zoom
        zoom_label = QLabel("Zoom:")
        zoom_label.setStyleSheet("color: #6c757d; font-size: 9pt;")
        layout.addWidget(zoom_label)

        zoom_combo = QComboBox()
        zoom_combo.addItems(["50%", "75%", "100%", "125%", "150%", "200%"])
        zoom_combo.setCurrentText("100%")
        zoom_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9pt;
                min-width: 60px;
            }
        """)
        layout.addWidget(zoom_combo)

        return toolbar

    def create_side_panel(self):
        """Crée le panel latéral"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-left: 1px solid #dee2e6;
            }
            QLabel {
                color: #495057;
                font-weight: bold;
                padding: 8px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout(panel)

        # Titre panel
        panel_title = QLabel("📋 PROPRIÉTÉS")
        panel_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(panel_title)

        # Informations
        info_labels = [
            "📄 Document: Nouveau",
            "📅 Créé: Aujourd'hui",
            "👤 Auteur: MASTER COMPTA",
            "💾 Taille: 0 KB",
            "🔧 Version: Office 2024",
            "🌍 Langue: Français"
        ]

        for info in info_labels:
            label = QLabel(info)
            label.setStyleSheet("""
                QLabel {
                    background: white;
                    border: 1px solid #dee2e6;
                    border-radius: 3px;
                    padding: 6px;
                    margin: 2px;
                    font-size: 9pt;
                }
            """)
            layout.addWidget(label)

        layout.addStretch()

        return panel

    def create_office_statusbar(self):
        """Crée la barre de statut Office"""
        statusbar = self.statusBar()

        # Messages de statut
        statusbar.showMessage("Prêt • Interface Microsoft Office 2024 • MASTER COMPTA GÉNÉRAL")

        # Widgets permanents
        # Nombre de mots (style Word)
        word_count = QLabel("Mots: 0")
        word_count.setStyleSheet("color: #6c757d; padding: 2px 8px;")
        statusbar.addPermanentWidget(word_count)

        # Position curseur (style Excel)
        cursor_pos = QLabel("A1")
        cursor_pos.setStyleSheet("color: #6c757d; padding: 2px 8px; font-weight: bold;")
        statusbar.addPermanentWidget(cursor_pos)

        # Mode
        mode_label = QLabel("MASTER COMPTA")
        mode_label.setStyleSheet("color: #2196f3; padding: 2px 8px; font-weight: bold;")
        statusbar.addPermanentWidget(mode_label)

    # Méthodes d'action des menus
    def new_file(self): print("🆕 Nouveau fichier")
    def open_file(self): print("📂 Ouvrir fichier")
    def save_file(self): print("💾 Enregistrer")
    def save_as_file(self): print("💾 Enregistrer sous")
    def print_file(self): print("🖨️ Imprimer")
    def undo_action(self): print("↩️ Annuler")
    def redo_action(self): print("↪️ Rétablir")
    def cut_action(self): print("✂️ Couper")
    def copy_action(self): print("📄 Copier")
    def paste_action(self): print("📋 Coller")
    def zoom_in(self): print("🔍 Zoom avant")
    def zoom_out(self): print("🔍 Zoom arrière")
    def zoom_100(self): print("💯 Zoom 100%")
    def toggle_ruler(self): print("📏 Basculer règle")
    def toggle_grid(self): print("📐 Basculer grille")
    def open_parc_auto(self):
        """Ouvre le module Parc Auto avec données"""
        self.show_parc_auto_window()

    def open_immobilier(self):
        """Ouvre le module Immobilier avec données"""
        self.show_immobilier_window()

    def open_personnel(self):
        """Ouvre le module Personnel avec données"""
        self.show_personnel_window()

    def open_database_system(self):
        """Ouvre le système de bases de données"""
        self.show_database_window()

    def open_ai_analysis(self):
        """Ouvre l'analyse IA"""
        self.show_ai_window()

    def show_help(self):
        """Affiche l'aide"""
        QMessageBox.information(self, "📖 Aide",
            "🏢 MASTER COMPTA GÉNÉRAL - Interface Office 2024\n\n"
            "📋 Utilisez les onglets Ribbon pour accéder aux fonctions\n"
            "🚗 Parc Auto : Gestion véhicules\n"
            "🏢 Immobilier : Gestion biens\n"
            "👥 Personnel : Gestion employés\n"
            "🗃️ Bases : Système de données\n"
            "🤖 IA : Analyse intelligente")

    def show_about(self):
        """Affiche à propos"""
        QMessageBox.about(self, "ℹ️ À propos",
            "🏢 MASTER COMPTA GÉNÉRAL\n"
            "Interface Microsoft Office 2024\n\n"
            "Version : Office 2024\n"
            "Développé avec : Python + PySide6\n"
            "Compatible : Windows, Mac, Linux\n"
            "Performance : Optimisée légère\n\n"
            "© 2024 MASTER COMPTA")

    def show_parc_auto_window(self):
        """Affiche la fenêtre Parc Auto avec données"""
        window = QWidget()
        window.setWindowTitle("🚗 PARC AUTO - Gestion Véhicules")
        window.setGeometry(200, 200, 1000, 700)
        window.setStyleSheet("""
            QWidget {
                background: white;
                color: #495057;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background: white;
                border: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 1px solid #2196f3;
                padding: 8px;
                font-weight: bold;
                color: #1976d2;
            }
        """)

        layout = QVBoxLayout(window)

        # Titre
        title = QLabel("🚗 PARC AUTO - GESTION VÉHICULES")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2196f3;
                background: #e3f2fd;
                border: 2px solid #2196f3;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)

        # Tableau des véhicules
        table = QTableWidget(8, 10)
        headers = ['Code', 'Marque', 'Modèle', 'Immatriculation', 'Année', 'Kilométrage', 'Carburant', 'Statut', 'Coût/mois', 'Observations']
        table.setHorizontalHeaderLabels(headers)

        # Données d'exemple
        vehicles_data = [
            ['V001', 'Peugeot', '308', 'AB-123-CD', '2020', '45000', 'Essence', 'Actif', '450€', 'Révision prévue'],
            ['V002', 'Renault', 'Clio', 'EF-456-GH', '2019', '62000', 'Diesel', 'Actif', '380€', 'Bon état'],
            ['V003', 'Citroën', 'C3', 'IJ-789-KL', '2021', '28000', 'Essence', 'Actif', '420€', 'Garantie'],
            ['V004', 'Ford', 'Focus', 'MN-012-OP', '2018', '78000', 'Diesel', 'Maintenance', '520€', 'Réparation en cours'],
            ['V005', 'Volkswagen', 'Golf', 'QR-345-ST', '2022', '15000', 'Hybride', 'Actif', '580€', 'Véhicule récent'],
            ['V006', 'BMW', 'Série 3', 'UV-678-WX', '2020', '55000', 'Diesel', 'Actif', '750€', 'Véhicule direction'],
            ['V007', 'Mercedes', 'Classe A', 'YZ-901-AB', '2021', '32000', 'Essence', 'Actif', '680€', 'Excellent état'],
            ['V008', 'Audi', 'A3', 'CD-234-EF', '2019', '68000', 'Diesel', 'Vendu', '0€', 'Vendu le 15/11/2024']
        ]

        for row, row_data in enumerate(vehicles_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                if col == 7:  # Colonne Statut
                    if value == 'Actif':
                        item.setBackground(QColor('#c8e6c9'))  # Vert clair
                    elif value == 'Maintenance':
                        item.setBackground(QColor('#fff3e0'))  # Orange clair
                    elif value == 'Vendu':
                        item.setBackground(QColor('#ffcdd2'))  # Rouge clair
                elif col == 8:  # Colonne Coût
                    if '€' in value and value != '0€':
                        item.setBackground(QColor('#e1f5fe'))  # Bleu clair
                table.setItem(row, col, item)

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        add_btn = QPushButton("➕ Ajouter Véhicule")
        add_btn.setStyleSheet("QPushButton { background: #4caf50; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(add_btn)

        edit_btn = QPushButton("✏️ Modifier")
        edit_btn.setStyleSheet("QPushButton { background: #2196f3; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(edit_btn)

        delete_btn = QPushButton("🗑️ Supprimer")
        delete_btn.setStyleSheet("QPushButton { background: #f44336; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(delete_btn)

        report_btn = QPushButton("📊 Rapport")
        report_btn.setStyleSheet("QPushButton { background: #ff9800; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(report_btn)

        layout.addLayout(buttons_layout)

        window.show()
        # Garder une référence pour éviter la suppression
        if not hasattr(self, 'module_windows'):
            self.module_windows = []
        self.module_windows.append(window)

    def show_immobilier_window(self):
        """Affiche la fenêtre Immobilier avec données"""
        window = QWidget()
        window.setWindowTitle("🏢 IMMOBILIER - Gestion Biens")
        window.setGeometry(250, 250, 1100, 750)
        window.setStyleSheet("""
            QWidget {
                background: white;
                color: #495057;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background: white;
                border: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3e0, stop:1 #ffcc02);
                border: 1px solid #ff9800;
                padding: 8px;
                font-weight: bold;
                color: #e65100;
            }
        """)

        layout = QVBoxLayout(window)

        # Titre
        title = QLabel("🏢 IMMOBILIER - GESTION BIENS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #ff9800;
                background: #fff3e0;
                border: 2px solid #ff9800;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)

        # Tableau des biens
        table = QTableWidget(6, 9)
        headers = ['Code', 'Type', 'Adresse', 'Surface (m²)', 'Loyer', 'Locataire', 'Statut', 'Charges', 'Observations']
        table.setHorizontalHeaderLabels(headers)

        # Données d'exemple
        properties_data = [
            ['B001', 'Appartement', '15 Rue de la Paix, Paris', '65', '1200€', 'Martin Pierre', 'Occupé', '150€', 'Bail renouvelé'],
            ['B002', 'Bureau', '25 Avenue des Champs, Lyon', '120', '2500€', 'Société ABC', 'Occupé', '300€', 'Contrat 3 ans'],
            ['B003', 'Maison', '8 Impasse du Soleil, Nice', '150', '1800€', 'Famille Durand', 'Occupé', '200€', 'Jardin inclus'],
            ['B004', 'Studio', '42 Boulevard Victor, Marseille', '25', '650€', '', 'Libre', '80€', 'Disponible immédiatement'],
            ['B005', 'Local Commercial', '18 Place du Marché, Toulouse', '80', '1500€', 'Boulangerie Martin', 'Occupé', '180€', 'Bail commercial'],
            ['B006', 'Appartement', '33 Rue des Fleurs, Bordeaux', '90', '1100€', 'Mme Leblanc', 'Préavis', '120€', 'Départ fin décembre']
        ]

        for row, row_data in enumerate(properties_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                if col == 6:  # Colonne Statut
                    if value == 'Occupé':
                        item.setBackground(QColor('#c8e6c9'))  # Vert clair
                    elif value == 'Libre':
                        item.setBackground(QColor('#e1f5fe'))  # Bleu clair
                    elif value == 'Préavis':
                        item.setBackground(QColor('#fff3e0'))  # Orange clair
                elif col == 4 or col == 7:  # Colonnes Loyer et Charges
                    if '€' in value:
                        item.setBackground(QColor('#f3e5f5'))  # Violet clair
                table.setItem(row, col, item)

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        add_btn = QPushButton("🏠 Ajouter Bien")
        add_btn.setStyleSheet("QPushButton { background: #4caf50; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(add_btn)

        tenant_btn = QPushButton("👥 Gestion Locataires")
        tenant_btn.setStyleSheet("QPushButton { background: #2196f3; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(tenant_btn)

        rent_btn = QPushButton("💰 Quittances")
        rent_btn.setStyleSheet("QPushButton { background: #ff9800; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(rent_btn)

        charges_btn = QPushButton("📊 Charges")
        charges_btn.setStyleSheet("QPushButton { background: #9c27b0; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(charges_btn)

        layout.addLayout(buttons_layout)

        window.show()
        if not hasattr(self, 'module_windows'):
            self.module_windows = []
        self.module_windows.append(window)

    def show_personnel_window(self):
        """Affiche la fenêtre Personnel avec données"""
        window = QWidget()
        window.setWindowTitle("👥 PERSONNEL - Gestion Employés")
        window.setGeometry(300, 300, 1200, 800)
        window.setStyleSheet("""
            QWidget {
                background: white;
                color: #495057;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background: white;
                border: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c9);
                border: 1px solid #4caf50;
                padding: 8px;
                font-weight: bold;
                color: #2e7d32;
            }
        """)

        layout = QVBoxLayout(window)

        # Titre
        title = QLabel("👥 PERSONNEL - GESTION EMPLOYÉS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #4caf50;
                background: #e8f5e8;
                border: 2px solid #4caf50;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)

        # Tableau du personnel
        table = QTableWidget(7, 10)
        headers = ['ID', 'Nom', 'Prénom', 'Poste', 'Service', 'Salaire', 'Date Embauche', 'Statut', 'Congés Restants', 'Observations']
        table.setHorizontalHeaderLabels(headers)

        # Données d'exemple
        staff_data = [
            ['E001', 'Martin', 'Jean', 'Directeur', 'Direction', '4500€', '15/01/2020', 'CDI', '18 jours', 'Excellent manager'],
            ['E002', 'Durand', 'Marie', 'Comptable', 'Comptabilité', '2800€', '03/09/2021', 'CDI', '22 jours', 'Très rigoureuse'],
            ['E003', 'Leblanc', 'Pierre', 'Commercial', 'Ventes', '2200€', '12/06/2022', 'CDI', '15 jours', 'Bon vendeur'],
            ['E004', 'Moreau', 'Sophie', 'Secrétaire', 'Administration', '2000€', '08/03/2023', 'CDI', '25 jours', 'Polyvalente'],
            ['E005', 'Bernard', 'Luc', 'Technicien', 'Maintenance', '2400€', '20/11/2021', 'CDI', '12 jours', 'Compétent'],
            ['E006', 'Petit', 'Anne', 'Stagiaire', 'Comptabilité', '600€', '01/09/2024', 'Stage', '0 jours', 'Stage 6 mois'],
            ['E007', 'Roux', 'Thomas', 'Développeur', 'Informatique', '3200€', '10/04/2022', 'CDI', '20 jours', 'Expert technique']
        ]

        for row, row_data in enumerate(staff_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                if col == 7:  # Colonne Statut
                    if value == 'CDI':
                        item.setBackground(QColor('#c8e6c9'))  # Vert clair
                    elif value == 'Stage':
                        item.setBackground(QColor('#fff3e0'))  # Orange clair
                elif col == 5:  # Colonne Salaire
                    if '€' in value:
                        item.setBackground(QColor('#e1f5fe'))  # Bleu clair
                elif col == 8:  # Colonne Congés
                    if 'jours' in value:
                        days = int(value.split()[0])
                        if days > 20:
                            item.setBackground(QColor('#c8e6c9'))  # Vert
                        elif days > 10:
                            item.setBackground(QColor('#fff3e0'))  # Orange
                        else:
                            item.setBackground(QColor('#ffcdd2'))  # Rouge clair
                table.setItem(row, col, item)

        table.resizeColumnsToContents()
        layout.addWidget(table)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        add_btn = QPushButton("👤 Ajouter Employé")
        add_btn.setStyleSheet("QPushButton { background: #4caf50; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(add_btn)

        payroll_btn = QPushButton("💰 Paie")
        payroll_btn.setStyleSheet("QPushButton { background: #2196f3; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(payroll_btn)

        leave_btn = QPushButton("🏖️ Congés")
        leave_btn.setStyleSheet("QPushButton { background: #ff9800; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(leave_btn)

        training_btn = QPushButton("📚 Formation")
        training_btn.setStyleSheet("QPushButton { background: #9c27b0; color: white; font-weight: bold; padding: 10px; }")
        buttons_layout.addWidget(training_btn)

        layout.addLayout(buttons_layout)

        window.show()
        if not hasattr(self, 'module_windows'):
            self.module_windows = []
        self.module_windows.append(window)

    def show_database_window(self):
        """Affiche la fenêtre système de bases de données"""
        QMessageBox.information(self, "🗃️ Système de Bases de Données",
            "🗃️ SYSTÈME DE BASES DE DONNÉES RÉVOLUTIONNAIRE\n\n"
            "📊 Import/Export multi-formats\n"
            "🔍 Détection anomalies automatique\n"
            "💾 Sauvegardes automatiques\n"
            "🤖 IA Ollama intégrée\n"
            "⚡ Performance optimisée\n\n"
            "💡 Lancez: LANCER_DATABASE_SYSTEM.bat\n"
            "Ou: python master_compta_database_interface.py")

    def show_ai_window(self):
        """Affiche la fenêtre analyse IA"""
        QMessageBox.information(self, "🤖 Analyse IA",
            "🤖 ANALYSE IA AVEC OLLAMA\n\n"
            "🧠 Modèle: Llama3 local\n"
            "🔍 Analyse: Anomalies et optimisations\n"
            "💬 Requêtes: Langage naturel\n"
            "📊 Recommandations: Intelligentes\n"
            "🔒 Sécurité: Données locales\n\n"
            "💡 Installation Ollama:\n"
            "1. Téléchargez: ollama.ai\n"
            "2. Installez Ollama\n"
            "3. Exécutez: ollama run llama3")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Style moderne

    # Configuration Office
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("Office 2024")
    app.setOrganizationName("MASTER COMPTA")

    window = MasterComptaOfficeInterface()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()