# -*- coding: utf-8 -*-
"""
Test d'encodage UTF-8 pour le module inventaire
"""

def test_encodage():
    print("🔍 Test d'encodage UTF-8")
    print("=" * 50)
    
    # Test des caractères français
    caracteres_francais = "àáâäèéêëìíîïòóôöùúûüçñ"
    print(f"Caractères français: {caracteres_francais}")
    
    # Test des émojis
    emojis = "📦🔄✅⚠️📊🗑️➕✏️📱🌐📄📋🔍"
    print(f"Émojis: {emojis}")
    
    # Test des mots français
    mots_francais = [
        "Réfrigérateur", "Électroménager", "Quantité", "Créé", 
        "Modifié", "Périmé", "Réformé", "Café", "Amélie"
    ]
    print("Mots français:")
    for mot in mots_francais:
        print(f"  - {mot}")
    
    # Test d'écriture/lecture de fichier
    try:
        with open("test_utf8.txt", "w", encoding="utf-8") as f:
            f.write("Test d'encodage UTF-8\n")
            f.write(f"Caractères: {caracteres_francais}\n")
            f.write(f"Émojis: {emojis}\n")
            for mot in mots_francais:
                f.write(f"{mot}\n")
        
        with open("test_utf8.txt", "r", encoding="utf-8") as f:
            contenu = f.read()
            print("\n📁 Contenu du fichier test:")
            print(contenu)
        
        print("✅ Test d'encodage UTF-8 réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    test_encodage()
