<!-- Generated by SpecStory -->

# /fix Le type de « impaye » est partiellement incon (2025-06-08 11:08:48)

_**User**_

/fix Le type de « impaye » est partiellement inconnu
  Le type de "impaye" est "Column[Unknown]"

---

_**Assistant**_

### [models.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/database/models.py)

Corrigez l'annotation de type pour `impaye` afin d'utiliser `Mapped` avec le type Python natif (`float` ou `Decimal`). SQLAlchemy ne peut pas inférer le type Python à partir de `DECIMAL` sans annotation explicite. Utilisez `Mapped[float]` ou `Mapped[Decimal]` selon vos besoins, mais il est courant d'utiliser `float` pour la compatibilité.

````python
# ...existing code...
from sqlalchemy.orm import relationship, declarative_base, Mapped, mapped_column
# ...existing code...
class Fournisseur(Base):
    __tablename__ = 'fournisseurs'
    # ...existing code...
    # Correction du type d'annotation pour impaye
    impaye: Mapped[float] = mapped_column(DECIMAL(18,2), nullable=True)
    # ...existing code...
# ...existing code...
````

---

