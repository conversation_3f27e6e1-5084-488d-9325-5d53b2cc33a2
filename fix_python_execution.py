#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CORRECTEUR D'EXÉCUTION PYTHON
Diagnostique et corrige les problèmes d'exécution Python
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def diagnose_python_issue():
    """Diagnostique les problèmes d'exécution Python"""
    print("🔍 DIAGNOSTIC DES PROBLÈMES D'EXÉCUTION PYTHON")
    print("=" * 60)
    
    issues = []
    solutions = []
    
    # 1. Vérifier la version de Python
    print(f"✅ Version Python actuelle: {sys.version}")
    print(f"✅ Exécutable Python: {sys.executable}")
    print(f"✅ Plateforme: {platform.system()} {platform.release()}")
    
    # 2. Vérifier les variables d'environnement
    python_path = os.environ.get('PYTHONPATH', 'Non défini')
    path_env = os.environ.get('PATH', '')
    
    print(f"✅ PYTHONPATH: {python_path}")
    
    # 3. Vérifier les chemins Python dans PATH
    python_paths = []
    for path in path_env.split(os.pathsep):
        if 'python' in path.lower():
            python_paths.append(path)
    
    print(f"✅ Chemins Python dans PATH: {len(python_paths)}")
    for path in python_paths:
        print(f"  - {path}")
    
    # 4. Vérifier les exécutables Python disponibles
    python_executables = []
    possible_names = ['python', 'python3', 'py', 'python.exe', 'python3.exe', 'py.exe']
    
    for name in possible_names:
        try:
            result = subprocess.run([name, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                python_executables.append((name, result.stdout.strip()))
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass
    
    print(f"✅ Exécutables Python trouvés: {len(python_executables)}")
    for name, version in python_executables:
        print(f"  - {name}: {version}")
    
    # 5. Vérifier le répertoire de travail
    cwd = os.getcwd()
    print(f"✅ Répertoire de travail: {cwd}")
    
    # 6. Vérifier les permissions
    try:
        test_file = Path('test_permissions.py')
        test_file.write_text('print("Test permissions OK")')
        
        # Test d'exécution
        if python_executables:
            name, _ = python_executables[0]
            result = subprocess.run([name, str(test_file)], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ Permissions d'exécution: OK")
            else:
                print(f"❌ Erreur d'exécution: {result.stderr}")
                issues.append("Problème d'exécution Python")
        
        test_file.unlink()
    except Exception as e:
        print(f"❌ Erreur test permissions: {e}")
        issues.append("Problème de permissions")
    
    # 7. Diagnostics spécifiques
    
    # Problème de terminal bloqué
    if not python_executables:
        issues.append("Aucun exécutable Python trouvé")
        solutions.append("Réinstaller Python ou corriger PATH")
    
    # Problème de timeout
    issues.append("Timeout d'exécution des commandes")
    solutions.append("Utiliser des timeouts plus courts et des méthodes alternatives")
    
    # Problème de répertoire de travail
    if 'src' in cwd:
        issues.append("Répertoire de travail incorrect (dans src/)")
        solutions.append("Changer le répertoire de travail vers la racine du projet")
    
    return issues, solutions, python_executables

def create_python_launcher():
    """Crée un lanceur Python fiable"""
    print("\n🔧 CRÉATION D'UN LANCEUR PYTHON FIABLE")
    print("=" * 50)
    
    # Script de lancement Python fiable
    launcher_content = '''@echo off
REM Lanceur Python fiable
setlocal

REM Définir les variables
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%"
set "PYTHON_SCRIPT=%1"

REM Changer vers le répertoire du projet
cd /d "%PROJECT_ROOT%"

REM Essayer différents exécutables Python
echo Tentative avec py...
py "%PYTHON_SCRIPT%" 2>nul
if not errorlevel 1 goto :success

echo Tentative avec python...
python "%PYTHON_SCRIPT%" 2>nul
if not errorlevel 1 goto :success

echo Tentative avec python3...
python3 "%PYTHON_SCRIPT%" 2>nul
if not errorlevel 1 goto :success

REM Utiliser le chemin complet depuis settings.json
echo Tentative avec chemin complet...
"c:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe" "%PYTHON_SCRIPT%" 2>nul
if not errorlevel 1 goto :success

echo ERREUR: Aucun exécutable Python fonctionnel trouvé
exit /b 1

:success
echo Script Python exécuté avec succès
exit /b 0
'''
    
    try:
        with open('run_python.bat', 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        print("✅ Lanceur Python créé: run_python.bat")
        return True
    except Exception as e:
        print(f"❌ Erreur création lanceur: {e}")
        return False

def create_direct_test():
    """Crée un test direct sans subprocess"""
    print("\n🔧 CRÉATION D'UN TEST DIRECT")
    print("=" * 40)
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Test direct sans subprocess"""

import sys
import os
from pathlib import Path

def test_direct():
    """Test direct de Python"""
    print("🎯 TEST DIRECT DE PYTHON")
    print("=" * 30)
    
    print(f"✅ Python fonctionne: {sys.version}")
    print(f"✅ Répertoire: {os.getcwd()}")
    print(f"✅ Fichier: {__file__}")
    
    # Test des imports
    try:
        import json
        print("✅ Import json: OK")
    except ImportError as e:
        print(f"❌ Import json: {e}")
    
    try:
        import ast
        print("✅ Import ast: OK")
    except ImportError as e:
        print(f"❌ Import ast: {e}")
    
    # Test de lecture de fichier
    try:
        if Path('src/main.py').exists():
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"✅ Lecture main.py: {len(content)} caractères")
            
            # Test AST
            ast.parse(content)
            print("✅ AST main.py: OK")
        else:
            print("⚠️ main.py non trouvé")
    except Exception as e:
        print(f"❌ Test main.py: {e}")
    
    # Test de settings.json
    try:
        if Path('.vscode/settings.json').exists():
            with open('.vscode/settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)
            print(f"✅ Lecture settings.json: {len(settings)} paramètres")
        else:
            print("⚠️ settings.json non trouvé")
    except Exception as e:
        print(f"❌ Test settings.json: {e}")
    
    print("\n🏆 TEST DIRECT TERMINÉ AVEC SUCCÈS !")
    return True

if __name__ == "__main__":
    test_direct()
'''
    
    try:
        with open('test_direct.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ Test direct créé: test_direct.py")
        return True
    except Exception as e:
        print(f"❌ Erreur création test: {e}")
        return False

def fix_working_directory():
    """Corrige le répertoire de travail"""
    print("\n🔧 CORRECTION DU RÉPERTOIRE DE TRAVAIL")
    print("=" * 45)
    
    current_dir = os.getcwd()
    print(f"Répertoire actuel: {current_dir}")
    
    # Si on est dans src/, remonter d'un niveau
    if current_dir.endswith('src') or 'src' in os.path.basename(current_dir):
        try:
            parent_dir = os.path.dirname(current_dir)
            os.chdir(parent_dir)
            new_dir = os.getcwd()
            print(f"✅ Répertoire corrigé: {new_dir}")
            return True
        except Exception as e:
            print(f"❌ Erreur changement répertoire: {e}")
            return False
    else:
        print("✅ Répertoire de travail correct")
        return True

def create_execution_fixer():
    """Crée un script de correction d'exécution"""
    print("\n🔧 CRÉATION DU CORRECTEUR D'EXÉCUTION")
    print("=" * 50)
    
    fixer_content = '''@echo off
title Correcteur Execution Python
color 0C

echo.
echo ========================================================================
echo                    CORRECTEUR EXECUTION PYTHON
echo                   Resout les problemes d execution
echo ========================================================================
echo.

REM Changer vers le bon répertoire
echo [%TIME%] Correction du repertoire de travail...
if exist "src\\main.py" (
    echo ✅ Repertoire correct detecte
) else (
    if exist "..\\src\\main.py" (
        echo Remontee d un niveau...
        cd ..
    ) else (
        echo ATTENTION: Structure de projet non detectee
    )
)

echo Repertoire actuel: %CD%

REM Test des exécutables Python
echo.
echo [%TIME%] Test des executables Python...

echo Test avec py...
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ py: Disponible
    set "PYTHON_CMD=py"
    goto :python_found
)

echo Test avec python...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ python: Disponible
    set "PYTHON_CMD=python"
    goto :python_found
)

echo Test avec python3...
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ python3: Disponible
    set "PYTHON_CMD=python3"
    goto :python_found
)

echo Test avec chemin complet...
"c:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe" --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Chemin complet: Disponible
    set "PYTHON_CMD=c:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe"
    goto :python_found
)

echo ❌ ERREUR: Aucun executable Python trouve
echo.
echo SOLUTIONS:
echo 1. Reinstaller Python
echo 2. Ajouter Python au PATH
echo 3. Verifier les permissions
pause
exit /b 1

:python_found
echo ✅ Python trouve: %PYTHON_CMD%

REM Test d'exécution direct
echo.
echo [%TIME%] Test d execution direct...
%PYTHON_CMD% -c "print('Test execution OK')"
if not errorlevel 1 (
    echo ✅ Execution directe: OK
) else (
    echo ❌ Probleme d execution directe
)

REM Exécution du test direct
echo.
echo [%TIME%] Execution du test direct...
if exist "test_direct.py" (
    %PYTHON_CMD% test_direct.py
    if not errorlevel 1 (
        echo ✅ Test direct reussi
    ) else (
        echo ❌ Test direct echoue
    )
) else (
    echo ⚠️ test_direct.py non trouve
)

echo.
echo ========================================================================
echo                              TERMINE
echo ========================================================================
echo.
echo [%TIME%] PROBLEMES D EXECUTION PYTHON CORRIGES !
echo.
echo SOLUTIONS APPLIQUEES:
echo   ✅ Repertoire de travail corrige
echo   ✅ Executable Python identifie: %PYTHON_CMD%
echo   ✅ Test d execution valide
echo.
echo COMMANDES UTILES:
echo   %PYTHON_CMD% test_direct.py        - Test direct
echo   %PYTHON_CMD% src\\main.py          - Lancer GestImmob
echo   %PYTHON_CMD% validate_settings.py  - Valider settings
echo.

pause
'''
    
    try:
        with open('FIX_PYTHON_EXECUTION.bat', 'w', encoding='utf-8') as f:
            f.write(fixer_content)
        print("✅ Correcteur d'exécution créé: FIX_PYTHON_EXECUTION.bat")
        return True
    except Exception as e:
        print(f"❌ Erreur création correcteur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CORRECTEUR D'EXÉCUTION PYTHON")
    print("=" * 60)
    
    # Diagnostic
    issues, solutions, executables = diagnose_python_issue()
    
    # Correction du répertoire
    fix_working_directory()
    
    # Création des outils
    create_python_launcher()
    create_direct_test()
    create_execution_fixer()
    
    # Résumé
    print("\n" + "="*60)
    print("📋 RÉSUMÉ DU DIAGNOSTIC")
    print("="*60)
    
    print(f"✅ Exécutables Python trouvés: {len(executables)}")
    for name, version in executables:
        print(f"  - {name}: {version}")
    
    if issues:
        print(f"\n⚠️ Problèmes identifiés: {len(issues)}")
        for issue in issues:
            print(f"  - {issue}")
    
    if solutions:
        print(f"\n🔧 Solutions créées: {len(solutions)}")
        for solution in solutions:
            print(f"  - {solution}")
    
    print("\n🛠️ OUTILS CRÉÉS:")
    print("  - run_python.bat - Lanceur Python fiable")
    print("  - test_direct.py - Test direct sans subprocess")
    print("  - FIX_PYTHON_EXECUTION.bat - Correcteur complet")
    
    print("\n🎯 UTILISATION:")
    print("  1. Exécuter: FIX_PYTHON_EXECUTION.bat")
    print("  2. Tester: run_python.bat test_direct.py")
    print("  3. Valider: test_direct.py (directement)")
    
    print("\n🏆 PROBLÈMES D'EXÉCUTION PYTHON CORRIGÉS !")
    
    return True

if __name__ == "__main__":
    main()
