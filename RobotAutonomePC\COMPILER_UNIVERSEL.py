#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌍 COMPILATEUR UNIVERSEL ROBOT IA
Génère exécutables pour Windows, Linux, macOS
Créé par Augment Agent
"""

import os
import sys
import platform
import subprocess
import shutil
from datetime import datetime

class UniversalCompiler:
    """🌍 Compilateur universel multi-plateformes"""
    
    def __init__(self):
        self.current_os = platform.system()
        self.output_dir = "executables"
        self.app_name = "RobotIA_SamNord"
        
        # Configuration compilation
        self.compile_config = {
            "windows": {
                "extension": ".exe",
                "icon": "robot_icon.ico",
                "options": ["--onefile", "--windowed", "--noconsole"]
            },
            "linux": {
                "extension": "",
                "formats": ["AppImage", ".deb", ".rpm"],
                "options": ["--onefile"]
            },
            "macos": {
                "extension": ".app",
                "formats": [".app", ".dmg"],
                "options": ["--onefile", "--windowed"]
            }
        }
    
    def setup_compilation_environment(self):
        """Configuration environnement compilation"""
        print("🔧 CONFIGURATION ENVIRONNEMENT COMPILATION")
        print("=" * 50)
        
        # Installation outils compilation
        tools = [
            "pyinstaller",
            "cx_Freeze", 
            "auto-py-to-exe",
            "nuitka"
        ]
        
        for tool in tools:
            try:
                print(f"📦 Installation {tool}...")
                subprocess.run([sys.executable, "-m", "pip", "install", tool], 
                             capture_output=True, text=True, check=True)
                print(f"✅ {tool} installé")
            except subprocess.CalledProcessError:
                print(f"⚠️ Erreur installation {tool}")
        
        # Création dossier sortie
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 Dossier sortie: {self.output_dir}")
        
        print("✅ Environnement compilation prêt !")
    
    def compile_for_windows(self):
        """Compilation pour Windows"""
        print("\n🪟 COMPILATION POUR WINDOWS")
        print("=" * 35)
        
        # Configuration Windows
        output_name = f"{self.app_name}_Windows.exe"
        output_path = os.path.join(self.output_dir, output_name)
        
        # Commande PyInstaller
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name", self.app_name + "_Windows",
            "--distpath", self.output_dir,
            "--workpath", "build_windows",
            "--specpath", "specs",
            "robot_ia_universel.py"
        ]
        
        # Ajout icône si disponible
        if os.path.exists("robot_icon.ico"):
            cmd.extend(["--icon", "robot_icon.ico"])
        
        try:
            print("🔄 Compilation Windows en cours...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ Compilation Windows réussie !")
            print(f"📁 Exécutable: {output_path}")
            
            # Création installateur Windows
            self.create_windows_installer()
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur compilation Windows: {e}")
            print(f"Sortie: {e.stdout}")
            print(f"Erreur: {e.stderr}")
    
    def compile_for_linux(self):
        """Compilation pour Linux"""
        print("\n🐧 COMPILATION POUR LINUX")
        print("=" * 30)
        
        # Exécutable Linux standard
        output_name = f"{self.app_name}_Linux"
        output_path = os.path.join(self.output_dir, output_name)
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--name", self.app_name + "_Linux",
            "--distpath", self.output_dir,
            "--workpath", "build_linux",
            "--specpath", "specs",
            "robot_ia_universel.py"
        ]
        
        try:
            print("🔄 Compilation Linux en cours...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ Compilation Linux réussie !")
            print(f"📁 Exécutable: {output_path}")
            
            # Permissions exécution
            os.chmod(output_path, 0o755)
            
            # Création packages Linux
            self.create_linux_packages()
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur compilation Linux: {e}")
    
    def compile_for_macos(self):
        """Compilation pour macOS"""
        print("\n🍎 COMPILATION POUR MACOS")
        print("=" * 30)
        
        # Application macOS
        output_name = f"{self.app_name}_macOS.app"
        output_path = os.path.join(self.output_dir, output_name)
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name", self.app_name + "_macOS",
            "--distpath", self.output_dir,
            "--workpath", "build_macos",
            "--specpath", "specs",
            "robot_ia_universel.py"
        ]
        
        try:
            print("🔄 Compilation macOS en cours...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ Compilation macOS réussie !")
            print(f"📁 Application: {output_path}")
            
            # Création DMG
            self.create_macos_dmg()
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur compilation macOS: {e}")
    
    def create_windows_installer(self):
        """Création installateur Windows"""
        print("📦 Création installateur Windows...")
        
        # Script NSIS pour installateur
        nsis_script = f'''
!define APPNAME "Robot IA Autonome PC"
!define COMPANYNAME "SamNord"
!define DESCRIPTION "Robot IA Universel pour SamNord@110577"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

Name "${{APPNAME}}"
OutFile "{self.output_dir}\\{self.app_name}_Windows_Installer.exe"
InstallDir "$PROGRAMFILES\\${{APPNAME}}"

Page directory
Page instfiles

Section "install"
    SetOutPath $INSTDIR
    File "{self.output_dir}\\{self.app_name}_Windows.exe"
    
    CreateDirectory "$SMPROGRAMS\\${{APPNAME}}"
    CreateShortCut "$SMPROGRAMS\\${{APPNAME}}\\${{APPNAME}}.lnk" "$INSTDIR\\{self.app_name}_Windows.exe"
    CreateShortCut "$DESKTOP\\Robot IA - SamNord.lnk" "$INSTDIR\\{self.app_name}_Windows.exe"
SectionEnd
'''
        
        # Sauvegarde script
        with open("installer_windows.nsi", "w", encoding="utf-8") as f:
            f.write(nsis_script)
        
        print("✅ Script installateur Windows créé")
    
    def create_linux_packages(self):
        """Création packages Linux"""
        print("📦 Création packages Linux...")
        
        # Package .deb (Debian/Ubuntu)
        self.create_deb_package()
        
        # Package .rpm (RedHat/Fedora)
        self.create_rpm_package()
        
        # AppImage
        self.create_appimage()
    
    def create_deb_package(self):
        """Création package .deb"""
        print("📦 Création package .deb...")
        
        # Structure package Debian
        deb_dir = f"{self.output_dir}/deb_package"
        os.makedirs(f"{deb_dir}/DEBIAN", exist_ok=True)
        os.makedirs(f"{deb_dir}/usr/bin", exist_ok=True)
        os.makedirs(f"{deb_dir}/usr/share/applications", exist_ok=True)
        
        # Fichier control
        control_content = f'''Package: robot-ia-samnord
Version: 1.0.0
Section: utils
Priority: optional
Architecture: amd64
Maintainer: SamNord <<EMAIL>>
Description: Robot IA Autonome PC pour SamNord@110577
 Robot IA universel avec intelligence transcendante,
 scanner télécommunications et cybersécurité éthique.
'''
        
        with open(f"{deb_dir}/DEBIAN/control", "w") as f:
            f.write(control_content)
        
        # Copie exécutable
        shutil.copy(f"{self.output_dir}/{self.app_name}_Linux", 
                   f"{deb_dir}/usr/bin/robot-ia-samnord")
        
        # Fichier .desktop
        desktop_content = f'''[Desktop Entry]
Name=Robot IA SamNord
Comment=Robot IA Autonome PC
Exec=/usr/bin/robot-ia-samnord
Icon=robot-ia
Terminal=false
Type=Application
Categories=Utility;
'''
        
        with open(f"{deb_dir}/usr/share/applications/robot-ia-samnord.desktop", "w") as f:
            f.write(desktop_content)
        
        print("✅ Package .deb préparé")
    
    def create_rpm_package(self):
        """Création package .rpm"""
        print("📦 Création package .rpm...")
        
        # Spec file RPM
        spec_content = f'''Name: robot-ia-samnord
Version: 1.0.0
Release: 1
Summary: Robot IA Autonome PC pour SamNord@110577
License: Proprietary
Group: Applications/System

%description
Robot IA universel avec intelligence transcendante,
scanner télécommunications et cybersécurité éthique.

%files
/usr/bin/robot-ia-samnord
/usr/share/applications/robot-ia-samnord.desktop

%install
mkdir -p %{{buildroot}}/usr/bin
mkdir -p %{{buildroot}}/usr/share/applications
cp {self.output_dir}/{self.app_name}_Linux %{{buildroot}}/usr/bin/robot-ia-samnord
'''
        
        with open("robot-ia-samnord.spec", "w") as f:
            f.write(spec_content)
        
        print("✅ Spec file .rpm créé")
    
    def create_appimage(self):
        """Création AppImage"""
        print("📦 Création AppImage...")
        
        # Structure AppImage
        appimage_dir = f"{self.output_dir}/AppImage"
        os.makedirs(appimage_dir, exist_ok=True)
        
        # Copie exécutable
        shutil.copy(f"{self.output_dir}/{self.app_name}_Linux", 
                   f"{appimage_dir}/robot-ia-samnord")
        
        # AppRun script
        apprun_content = '''#!/bin/bash
SELF=$(readlink -f "$0")
HERE=${SELF%/*}
export PATH="${HERE}:${PATH}"
exec "${HERE}/robot-ia-samnord" "$@"
'''
        
        with open(f"{appimage_dir}/AppRun", "w") as f:
            f.write(apprun_content)
        
        os.chmod(f"{appimage_dir}/AppRun", 0o755)
        
        print("✅ AppImage préparé")
    
    def create_macos_dmg(self):
        """Création DMG macOS"""
        print("📦 Création DMG macOS...")
        
        dmg_name = f"{self.app_name}_macOS.dmg"
        dmg_path = os.path.join(self.output_dir, dmg_name)
        
        # Commande création DMG
        cmd = [
            "hdiutil", "create",
            "-volname", "Robot IA SamNord",
            "-srcfolder", f"{self.output_dir}/{self.app_name}_macOS.app",
            "-ov", "-format", "UDZO",
            dmg_path
        ]
        
        try:
            subprocess.run(cmd, check=True)
            print(f"✅ DMG créé: {dmg_path}")
        except subprocess.CalledProcessError:
            print("⚠️ Erreur création DMG (nécessite macOS)")
    
    def compile_all_platforms(self):
        """Compilation pour toutes les plateformes"""
        print("🌍 COMPILATION UNIVERSELLE MULTI-PLATEFORMES")
        print("=" * 55)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Configuration environnement
        self.setup_compilation_environment()
        
        # Compilation selon plateforme actuelle
        if self.current_os == "Windows":
            print("🪟 Plateforme détectée: Windows")
            self.compile_for_windows()
            print("\n💡 Pour Linux/macOS: exécutez sur ces systèmes")
            
        elif self.current_os == "Linux":
            print("🐧 Plateforme détectée: Linux")
            self.compile_for_linux()
            print("\n💡 Pour Windows/macOS: exécutez sur ces systèmes")
            
        elif self.current_os == "Darwin":
            print("🍎 Plateforme détectée: macOS")
            self.compile_for_macos()
            print("\n💡 Pour Windows/Linux: exécutez sur ces systèmes")
        
        # Résumé
        self.show_compilation_summary()
    
    def show_compilation_summary(self):
        """Résumé compilation"""
        print("\n📊 RÉSUMÉ COMPILATION")
        print("=" * 25)
        
        if os.path.exists(self.output_dir):
            files = os.listdir(self.output_dir)
            print(f"📁 Dossier: {self.output_dir}")
            print(f"📦 Fichiers générés: {len(files)}")
            
            for file in files:
                file_path = os.path.join(self.output_dir, file)
                size = os.path.getsize(file_path) / (1024*1024)  # MB
                print(f"   📄 {file} ({size:.1f} MB)")
        
        print("\n🎯 INSTRUCTIONS DÉPLOIEMENT:")
        print("🪟 Windows: Exécuter .exe ou installateur")
        print("🐧 Linux: ./executable ou installer .deb/.rpm")
        print("🍎 macOS: Glisser .app vers Applications ou monter .dmg")
        
        print("\n✅ Compilation universelle terminée !")

def main():
    """Fonction principale"""
    print("🌍 COMPILATEUR UNIVERSEL ROBOT IA")
    print("=" * 40)
    
    compiler = UniversalCompiler()
    compiler.compile_all_platforms()

if __name__ == "__main__":
    main()
