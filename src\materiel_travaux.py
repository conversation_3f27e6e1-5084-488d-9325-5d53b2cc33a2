from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QFileDialog
)
from PySide6.QtCore import QTimer
from PySide6.QtGui import QColor

class MaterielTravauxWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Suivi du matériel de travaux")
        self.resize(1400, 800)
        layout = QVBoxLayout(self)
        self.table = QTableWidget(0, 14)
        self.table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Catégorie", "Marque", "Modèle", "N° Série", "Date acquisition",
            "Valeur", "Statut", "Emplacement", "Affectation", "Amortissement", "Observation", "Photo"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter matériel")
        self.edit_btn = QPushButton("Modifier")
        self.delete_btn = QPushButton("Supprimer")
        self.export_btn = QPushButton("Exporter Excel")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.export_btn)
        layout.addLayout(btn_layout)
        self.add_btn.clicked.connect(self.ajouter_materiel)
        self.edit_btn.clicked.connect(self.modifier_materiel)
        self.delete_btn.clicked.connect(self.supprimer_materiel)
        self.export_btn.clicked.connect(self.exporter_excel)
        self.changements_detectes = False

    def ajouter_materiel(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter matériel")
        form = QFormLayout()
        designation = QLineEdit()
        categorie = QComboBox(); categorie.addItems(["Engin", "Outil", "Véhicule", "Matériel électrique", "Autre"])
        marque = QLineEdit()
        modele = QLineEdit()
        num_serie = QLineEdit()
        date_acquisition = QLineEdit()
        valeur = QLineEdit()
        statut = QComboBox(); statut.addItems(["En service", "En panne", "Réformé", "Stocké", "Sorti"])
        emplacement = QLineEdit()
        affectation = QLineEdit()
        amortissement = QLineEdit()
        observation = QLineEdit()
        photo = QLineEdit()
        photo_btn = QPushButton("Choisir photo")
        def choisir_photo():
            file, _ = QFileDialog.getOpenFileName(dialog, "Choisir une photo", "", "Images (*.png *.jpg *.jpeg)")
            if file:
                photo.setText(file)
        photo_btn.clicked.connect(choisir_photo)
        form.addRow("Désignation:", designation)
        form.addRow("Catégorie:", categorie)
        form.addRow("Marque:", marque)
        form.addRow("Modèle:", modele)
        form.addRow("N° Série:", num_serie)
        form.addRow("Date acquisition:", date_acquisition)
        form.addRow("Valeur:", valeur)
        form.addRow("Statut:", statut)
        form.addRow("Emplacement:", emplacement)
        form.addRow("Affectation:", affectation)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Observation:", observation)
        form.addRow("Photo:", photo)
        form.addRow("", photo_btn)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
            self.table.setItem(row, 1, QTableWidgetItem(designation.text()))
            self.table.setItem(row, 2, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(marque.text()))
            self.table.setItem(row, 4, QTableWidgetItem(modele.text()))
            self.table.setItem(row, 5, QTableWidgetItem(num_serie.text()))
            self.table.setItem(row, 6, QTableWidgetItem(date_acquisition.text()))
            self.table.setItem(row, 7, QTableWidgetItem(valeur.text()))
            self.table.setItem(row, 8, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 9, QTableWidgetItem(emplacement.text()))
            self.table.setItem(row, 10, QTableWidgetItem(affectation.text()))
            self.table.setItem(row, 11, QTableWidgetItem(amortissement.text()))
            self.table.setItem(row, 12, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 13, QTableWidgetItem(photo.text()))
            self.changements_detectes = True

    def modifier_materiel(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier matériel")
        form = QFormLayout()
        def safe_text(item):
            return item.text() if item else ""
        designation = QLineEdit(safe_text(self.table.item(row, 1)))
        categorie = QComboBox(); categorie.addItems(["Engin", "Outil", "Véhicule", "Matériel électrique", "Autre"])
        categorie.setCurrentText(safe_text(self.table.item(row, 2)))
        marque = QLineEdit(safe_text(self.table.item(row, 3)))
        modele = QLineEdit(safe_text(self.table.item(row, 4)))
        num_serie = QLineEdit(safe_text(self.table.item(row, 5)))
        date_acquisition = QLineEdit(safe_text(self.table.item(row, 6)))
        valeur = QLineEdit(safe_text(self.table.item(row, 7)))
        statut = QComboBox(); statut.addItems(["En service", "En panne", "Réformé", "Stocké", "Sorti"])
        statut.setCurrentText(safe_text(self.table.item(row, 8)))
        emplacement = QLineEdit(safe_text(self.table.item(row, 9)))
        affectation = QLineEdit(safe_text(self.table.item(row, 10)))
        amortissement = QLineEdit(safe_text(self.table.item(row, 11)))
        observation = QLineEdit(safe_text(self.table.item(row, 12)))
        photo = QLineEdit(safe_text(self.table.item(row, 13)))
        photo_btn = QPushButton("Choisir photo")
        def choisir_photo():
            file, _ = QFileDialog.getOpenFileName(dialog, "Choisir une photo", "", "Images (*.png *.jpg *.jpeg)")
            if file:
                photo.setText(file)
        photo_btn.clicked.connect(choisir_photo)
        form.addRow("Désignation:", designation)
        form.addRow("Catégorie:", categorie)
        form.addRow("Marque:", marque)
        form.addRow("Modèle:", modele)
        form.addRow("N° Série:", num_serie)
        form.addRow("Date acquisition:", date_acquisition)
        form.addRow("Valeur:", valeur)
        form.addRow("Statut:", statut)
        form.addRow("Emplacement:", emplacement)
        form.addRow("Affectation:", affectation)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Observation:", observation)
        form.addRow("Photo:", photo)
        form.addRow("", photo_btn)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            self.table.setItem(row, 1, QTableWidgetItem(designation.text()))
            self.table.setItem(row, 2, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(marque.text()))
            self.table.setItem(row, 4, QTableWidgetItem(modele.text()))
            self.table.setItem(row, 5, QTableWidgetItem(num_serie.text()))
            self.table.setItem(row, 6, QTableWidgetItem(date_acquisition.text()))
            self.table.setItem(row, 7, QTableWidgetItem(valeur.text()))
            self.table.setItem(row, 8, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 9, QTableWidgetItem(emplacement.text()))
            self.table.setItem(row, 10, QTableWidgetItem(affectation.text()))
            self.table.setItem(row, 11, QTableWidgetItem(amortissement.text()))
            self.table.setItem(row, 12, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 13, QTableWidgetItem(photo.text()))
            self.changements_detectes = True

    def supprimer_materiel(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à supprimer.")
            return
        row = selected[0].row()
        self.table.removeRow(row)
        self.changements_detectes = True

    def exporter_excel(self):
        try:
            import xlsxwriter
            file, _ = QFileDialog.getSaveFileName(self, "Exporter Excel", "", "Excel (*.xlsx)")
            if not file:
                return
            workbook = xlsxwriter.Workbook(file)
            worksheet = workbook.add_worksheet()
            for col in range(self.table.columnCount()):
                worksheet.write(0, col, self.table.horizontalHeaderItem(col).text())
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    worksheet.write(row+1, col, item.text() if item else "")
            workbook.close()
            QMessageBox.information(self, "Export Excel", "Export Excel terminé.")
        except ImportError:
            QMessageBox.warning(self, "Export Excel", "Le module xlsxwriter n'est pas installé.")
        except Exception as e:
            QMessageBox.warning(self, "Export Excel", f"Erreur lors de l'export : {e}")

    def synchroniser(self):
        # ...logique de synchronisation avec la base de données...
        QMessageBox.information(self, "Synchronisation", "Synchronisation du matériel de travaux effectuée.")
        self.changements_detectes = False

    def sauvegarde_auto(self):
        if self.changements_detectes:
            # ...logique de sauvegarde...
            self.changements_detectes = False

    def actualiser(self):
        # ...logique pour recharger les données...
        QMessageBox.information(self, "Actualisation", "Actualisation du matériel de travaux effectuée.")
