#!/usr/bin/env python3
"""
Script de lancement pour GestImmob v2.0.0 Complet
Logiciel de gestion immobilière avec tous les modules opérationnels
"""

import sys
import os
from pathlib import Path

def verifier_dependances():
    """Vérifie que toutes les dépendances sont installées"""
    dependances = [
        ("PySide6", "Interface graphique moderne"),
        ("sqlite3", "Base de données (inclus avec Python)"),
        ("cryptography", "Sécurité et chiffrement"),
        ("xlsxwriter", "Export Excel"),
        ("psutil", "Informations système")
    ]
    
    manquantes = []
    
    for module, description in dependances:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"[ECHEC] {module} - MANQUANT ({description})")
            manquantes.append(module)
    
    if manquantes:
        print(f"\n[ATTENTION] Dépendances manquantes: {', '.join(manquantes)}")
        print("\n📦 Pour installer les dépendances manquantes:")
        for module in manquantes:
            if module != "sqlite3":  # sqlite3 est inclus avec Python
                print(f"   pip install {module}")
        print("\n🔄 Relancez ce script après installation.")
        return False
    
    print("\n✅ Toutes les dépendances sont installées !")
    return True

def afficher_banner():
    """Affiche le banner de démarrage"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🏠 GestImmob v2.0.0 Complet              ║
║              Logiciel de Gestion Immobilière                 ║
║                     Professionnel                            ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  [LANCE] TOUS LES MODULES OPÉRATIONNELS:                         ║
║                                                              ║
║  📋 Gestion des Biens Immobiliers                          ║
║  📦 Inventaire avec Scanner Code-barres                    ║
║  🏢 Gestion des Fournisseurs                               ║
║  💼 ERP/Comptabilité Complet                               ║
║  🧮 Calculatrice Financière Avancée                        ║
║  🔍 Recherche et Filtres Intelligents                      ║
║  ♻️ Gestion des Réformes                                   ║
║  📄 Gestion Documentaire                                   ║
║  🖨️ Impression Multi-formats                               ║
║  [STATS] Rapports et Analyses                                   ║
║                                                              ║
║  🎨 INTERFACE MODERNE:                                      ║
║                                                              ║
║  🌈 Thèmes dynamiques avec couleurs changeantes            ║
║  🎭 Logo animé et interface fluide                         ║
║  ⚡ Optimisé pour machines performantes                     ║
║  📱 Design responsive et moderne                            ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def main():
    """Fonction principale de lancement"""
    print("🔍 Vérification de l'environnement...")
    
    # Afficher le banner
    afficher_banner()
    
    # Vérifier les dépendances
    print("📦 Vérification des dépendances...")
    if not verifier_dependances():
        input("\n⏸️ Appuyez sur Entrée pour quitter...")
        return 1
    
    # Vérifier que le fichier principal existe
    fichier_principal = Path("gestimmob_complet.py")
    if not fichier_principal.exists():
        print(f"[ECHEC] Fichier principal non trouvé: {fichier_principal}")
        print("   Assurez-vous que gestimmob_complet.py est dans le même dossier.")
        input("\n⏸️ Appuyez sur Entrée pour quitter...")
        return 1
    
    print(f"✅ Fichier principal trouvé: {fichier_principal}")
    
    # Informations sur la machine
    try:
        import psutil
        import platform
        
        print(f"\n💻 Informations système:")
        print(f"   OS: {platform.system()} {platform.release()}")
        print(f"   Python: {platform.python_version()}")
        print(f"   RAM: {psutil.virtual_memory().total / (1024**3):.1f} GB")
        print(f"   CPU: {psutil.cpu_count()} cœurs")
        
        # Déterminer le profil de performance
        ram_gb = psutil.virtual_memory().total / (1024**3)
        cpu_count = psutil.cpu_count()
        
        if ram_gb >= 16 and cpu_count >= 8:
            profil = "HIGH_END"
            print(f"   [LANCE] Profil: {profil} (Machine performante détectée)")
        elif ram_gb >= 8 and cpu_count >= 4:
            profil = "MEDIUM"
            print(f"   ⚡ Profil: {profil} (Machine standard)")
        else:
            profil = "LOW_END"
            print(f"   💻 Profil: {profil} (Machine basique)")
            
    except ImportError:
        print("   ℹ️ Informations système non disponibles (psutil manquant)")
    
    print("\n[LANCE] Lancement de GestImmob...")
    print("   ⏳ Initialisation de l'interface graphique...")
    print("   🎨 Application des thèmes dynamiques...")
    print("   [STATS] Chargement de la base de données...")
    
    try:
        # Importer et lancer l'application
        from gestimmob_complet import main as app_main
        
        print("   ✅ Application initialisée avec succès !")
        print("\n[SUCCES] GestImmob est maintenant opérationnel !")
        print("\n📋 Instructions d'utilisation:")
        print("   • Utilisez les onglets pour naviguer entre les modules")
        print("   • Les couleurs changent automatiquement toutes les 30 secondes")
        print("   • Cliquez sur 'Changer Couleurs' pour un changement immédiat")
        print("   • Tous les boutons sont opérationnels et fonctionnels")
        print("   • Les calculs financiers sont précis et détaillés")
        print("   • L'export Excel est disponible pour tous les modules")
        
        print("\n[OUTIL] Fonctionnalités principales:")
        print("   📋 Gestion des Biens: Ajout, modification, suppression")
        print("   📦 Inventaire: Scanner, filtres, géolocalisation")
        print("   🧮 Calculatrice: Amortissement, prêts, rentabilité")
        print("   [STATS] Rapports: Statistiques en temps réel")
        print("   💾 Sauvegarde: Automatique et manuelle")
        
        print("\n[ATTENTION] Pour fermer l'application:")
        print("   • Utilisez le menu Fichier > Quitter")
        print("   • Ou fermez la fenêtre principale")
        print("   • Ou appuyez sur Ctrl+C dans ce terminal")
        
        print("\n" + "="*60)
        print("🎯 GESTIMMOB V2.0.0 - TOUS MODULES OPÉRATIONNELS")
        print("="*60)
        
        # Lancer l'application
        return app_main()
        
    except ImportError as e:
        print(f"[ECHEC] Erreur d'importation: {e}")
        print("   Vérifiez que gestimmob_complet.py est présent et correct.")
        input("\n⏸️ Appuyez sur Entrée pour quitter...")
        return 1
        
    except Exception as e:
        print(f"[ECHEC] Erreur lors du lancement: {e}")
        print("   Consultez les logs pour plus de détails.")
        input("\n⏸️ Appuyez sur Entrée pour quitter...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ Arrêt demandé par l'utilisateur.")
        print("👋 Merci d'avoir utilisé GestImmob !")
        sys.exit(0)
    except Exception as e:
        print(f"\n[ECHEC] Erreur critique: {e}")
        input("\n⏸️ Appuyez sur Entrée pour quitter...")
        sys.exit(1)
