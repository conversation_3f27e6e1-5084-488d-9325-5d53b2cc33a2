#!/bin/bash

echo "========================================"
echo "INSTALLATION DES OUTILS POUR GESTIMMOB"
echo "========================================"

echo
echo "1. Mise à jour de pip..."
python3 -m pip install --upgrade pip

echo
echo "2. Installation des dépendances principales..."
python3 -m pip install -r requirements.txt

echo
echo "3. Installation des outils de qualité de code..."
python3 -m pip install flake8 pylint mypy black autopep8

echo
echo "4. Installation des outils de test..."
python3 -m pip install pytest pytest-cov pytest-mock

echo
echo "5. Installation des outils de sécurité..."
python3 -m pip install bandit safety

echo
echo "6. Vérification des installations..."
python3 -c "import PySide6; print('PySide6: OK')"
python3 -c "import sqlite3; print('SQLite3: OK')"
python3 -c "import cryptography; print('Cryptography: OK')"
python3 -c "import requests; print('Requests: OK')"

echo
echo "7. Création des répertoires nécessaires..."
mkdir -p logs backups exports tests

echo
echo "8. Configuration des outils..."
python3 setup_tools.py

echo
echo "9. Permissions d'exécution..."
chmod +x start_gestimmob.py
chmod +x diagnostic.py
chmod +x ultimate_validator.py

echo
echo "========================================"
echo "INSTALLATION TERMINÉE !"
echo "========================================"
echo
echo "Pour démarrer GestImmob:"
echo "  python3 start_gestimmob.py"
echo
echo "Pour valider le code:"
echo "  python3 ultimate_validator.py"
echo
echo "Pour diagnostiquer:"
echo "  python3 diagnostic.py"
echo
