import os
import json
from typing import Any, Dict

CONFIG_FILE = "config.json"

def load_config() -> Dict[str, Any]:
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return {
        "DATABASES": ["immobilisations.db"],
        "DEFAULT_DB": "immobilisations.db"
    }

def save_config(config: Dict[str, Any]):
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
