# -*- coding: utf-8 -*-
# pylint: disable=all
# pyright: reportMissingImports=false
# pyright: reportMissingTypeStubs=false
# pyright: reportUnknownMemberType=false
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false
# pyright: reportGeneralTypeIssues=false
# pyright: reportOptionalSubscript=false
# pyright: reportOptionalMemberAccess=false
# pyright: reportUnusedVariable=false
# pyright: reportUnusedImport=false
# type: ignore

from sqlalchemy import Column, Integer, String  # type: ignore
from sqlalchemy.orm import declarative_base  # type: ignore
from sqlalchemy.exc import IntegrityError  # type: ignore
from sqlalchemy.orm import Session  # type: ignore
from typing import TYPE_CHECKING, List, Optional, Any

Base: Any = declarative_base()

def encrypt_password(password: str) -> str:
    """Chiffre un mot de passe"""
    import hashlib
    return hashlib.sha256(password.encode()).hexdigest()

def get_db_session() -> Session:
    """Retourne une session de base de données"""
    from sqlalchemy import create_engine  # type: ignore
    from sqlalchemy.orm import sessionmaker  # type: ignore
    engine = create_engine('sqlite:///users.db')  # type: ignore
    SessionLocal = sessionmaker(bind=engine)  # type: ignore
    return SessionLocal()  # type: ignore

if TYPE_CHECKING:
    pass  # Suppression de l'import inutile de User

# Correction de l'import : utilisez un import relatif si le module est dans le même projet
# Les fonctions encrypt_password et get_db_session sont définies ci-dessus
# Pas besoin d'imports externes qui peuvent échouer

# Explication sur get_db_session
# 'get_db_session' est une fonction importée depuis le module relatif '../database/db_connection'.
# Elle est utilisée pour établir une session de base de données SQLAlchemy, permettant à l'application
# d'interagir avec la base de données de manière fiable et cohérente.
# Il est crucial que cette fonction soit correctement définie et accessible pour assurer le bon fonctionnement de l'application.

class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    role = Column(String, default="user")

class UserManager:
    def __init__(self):
        self.session: Session = get_db_session()

    def create_user(self, username: str, password: str, role: str = 'user') -> "User":
        encrypted_password: str = encrypt_password(password)
        new_user: User = User(username=username, password=encrypted_password, role=role)
        try:
            self.session.add(new_user)
            self.session.commit()
            return new_user
        except IntegrityError:
            self.session.rollback()
            raise ValueError("Username already exists.")

    def update_user(self, user_id: int, username: Optional[str] = None, password: Optional[str] = None, role: Optional[str] = None) -> "User":
        user: "User" = self.session.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found.")
        if username:
            user.username = username  # type: ignore[attr-defined]
        if password:
            user.password = encrypt_password(password)  # type: ignore[attr-defined]
        if role:
            user.role = role  # type: ignore[assignment]
        self.session.commit()
        return user

    def delete_user(self, user_id: int) -> None:
        user: "User" = self.session.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found.")
        self.session.delete(user)
        self.session.commit()

    def get_user(self, user_id: int) -> "User":
        user: "User" = self.session.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found.")
        return user

    def list_users(self) -> List["User"]:
        return self.session.query(User).all()