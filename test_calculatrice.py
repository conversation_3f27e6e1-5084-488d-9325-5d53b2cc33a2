#!/usr/bin/env python3
"""
Test de la calculatrice financière
"""

def test_calculatrice():
    print("=== Test Calculatrice Financière ===")
    
    # Test calcul d'amortissement
    valeur = 10000
    duree = 5
    amortissement_annuel = valeur / duree
    print(f"[OK] Amortissement linéaire: {amortissement_annuel}€/an pour {valeur}€ sur {duree} ans")
    
    # Test calcul de prêt
    capital = 200000
    taux_annuel = 0.02
    duree_annees = 20
    taux_mensuel = taux_annuel / 12
    nb_mensualites = duree_annees * 12
    
    if taux_mensuel > 0:
        mensualite = capital * (taux_mensuel * (1 + taux_mensuel) ** nb_mensualites) / ((1 + taux_mensuel) ** nb_mensualites - 1)
    else:
        mensualite = capital / nb_mensualites
    
    print(f"[OK] Mensualité prêt: {mensualite:.2f}€ pour {capital}€ à {taux_annuel*100}% sur {duree_annees} ans")
    
    # Test calcul de rentabilité
    prix_achat = 150000
    loyer_mensuel = 800
    charges = 100
    loyer_net_annuel = (loyer_mensuel - charges) * 12
    rentabilite = loyer_net_annuel / prix_achat * 100
    
    print(f"[OK] Rentabilité locative: {rentabilite:.1f}% pour {prix_achat}€ avec {loyer_mensuel}€/mois")
    
    print("[SUCCES] Calculatrice financière opérationnelle !")
    return True

if __name__ == "__main__":
    test_calculatrice()
