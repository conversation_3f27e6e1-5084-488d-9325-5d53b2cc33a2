
-- Script d'intégration des configurations clonées
-- Exécutez ce script pour finaliser l'intégration

-- Copier les secteurs clonés vers la table principale si nécessaire
INSERT OR IGNORE INTO secteurs_personnalises (nom, description, couleur, icone)
SELECT nom, description, couleur, icone FROM secteurs_personnalises WHERE id > 0;

-- Copier les paramètres vers la table principale
INSERT OR REPLACE INTO parametres (nom, valeur, description)
SELECT nom, valeur, description FROM parametres_clones;

-- Afficher un résumé
SELECT 'Secteurs disponibles' as type, COUNT(*) as nombre FROM secteurs_personnalises
UNION ALL
SELECT 'Paramètres configurés' as type, COUNT(*) as nombre FROM parametres_clones
UNION ALL
SELECT 'Utilisateurs clonés' as type, COUNT(*) as nombre FROM utilisateurs_clones
UNION ALL
SELECT 'Exemples disponibles' as type, COUNT(*) as nombre FROM immobilisations_clones;
