<!-- Generated by SpecStory -->

# /fix Le type de « stats_categorie » est partiellem (2025-06-08 11:31:20)

_**User**_

/fix Le type de « stats_categorie » est partiellement inconnu
  Le type de "stats_categorie" est "defaultdict[Unknown, int]"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger le problème de type partiellement inconnu pour `stats_categorie`, il faut typer explicitement le defaultdict avec `str` comme clé. On ajoute aussi l'import du module `typing` pour l'annotation.

````python
# ...existing code...
    def afficher_charts(self):
        try:
            import matplotlib.pyplot as plt
            from collections import defaultdict
            from typing import DefaultDict
            stats_categorie: DefaultDict[str, int] = defaultdict(int)
            stats_statut: DefaultDict[str, int] = defaultdict(int)
            # ...existing code...
````

---

