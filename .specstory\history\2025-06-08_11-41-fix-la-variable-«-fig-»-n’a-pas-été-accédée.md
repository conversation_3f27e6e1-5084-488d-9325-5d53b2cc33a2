<!-- Generated by SpecStory -->

# /fix La variable « fig » n’a pas été accédée (2025-06-08 12:41:24)

_**User**_

/fix La variable « fig » n’a pas été accédée

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Le problème est que la variable `fig` est déclarée mais jamais utilisée. Pour corriger cela, il suffit de ne pas assigner le retour de `plt.figure()` à une variable si elle n'est pas utilisée, ou bien de supprimer la déclaration de `fig` si elle n'est pas nécessaire.

Voici la correction :

````python
# ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))
                plt.bar([str(k) for k in stats_categorie.keys()], [int(v) for v in stats_categorie.values()])
                title: Text = plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))
                plt.bar([str(k) for k in stats_statut.keys()], [int(v) for v in stats_statut.values()])
                title: Text = plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

---

