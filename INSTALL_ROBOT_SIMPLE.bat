@echo off
title Installation Robot IA SamNord
color 0A

echo.
echo ================================================
echo    INSTALLATION ROBOT IA SAMNORD
echo    Interface comme Augment Agent
echo    SamNord@110577 - NorDine@22
echo ================================================
echo.

echo Etape 1: Creation dossier projet sur bureau...
if not exist "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet" (
    mkdir "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet"
    echo Dossier projet cree
)

echo.
echo Etape 2: Copie fichiers projet...
xcopy "*.py" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\" /Y >nul 2>&1
xcopy "*.bat" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\" /Y >nul 2>&1
xcopy "*.md" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\" /Y >nul 2>&1
xcopy "modules" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\modules\" /E /I /Y >nul 2>&1
xcopy "vscode_extension" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\vscode_extension\" /E /I /Y >nul 2>&1

echo Projet copie sur le bureau

echo.
echo Etape 3: Installation extension VS Code...
set "VSCODE_EXTENSIONS=%USERPROFILE%\.vscode\extensions"
set "EXTENSION_DIR=%VSCODE_EXTENSIONS%\robot-ia-samnord-1.0.0"

if not exist "%VSCODE_EXTENSIONS%" mkdir "%VSCODE_EXTENSIONS%"
if not exist "%EXTENSION_DIR%" mkdir "%EXTENSION_DIR%"

xcopy "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\vscode_extension\*" "%EXTENSION_DIR%\" /E /I /Y >nul 2>&1

echo Extension installee dans VS Code

echo.
echo Etape 4: Lancement VS Code avec projet...
start "" code "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet"

echo VS Code lance avec le projet Robot IA

echo.
echo Etape 5: Lancement Robot IA en externe...
start "" cmd /c "cd /d %USERPROFILE%\Desktop\RobotIA_SamNord_Projet && python robot_ia_universel_40_modules.py && pause"

echo Robot IA lance en externe

echo.
echo ================================================
echo    INSTALLATION TERMINEE !
echo    Robot IA disponible dans VS Code et externe
echo    Utilisateur: SamNord@110577
echo    Mot de passe: NorDine@22
echo ================================================
echo.

echo INSTRUCTIONS:
echo 1. Dans VS Code: Ctrl+Shift+P puis "Robot IA"
echo 2. Ou cliquer icone Robot dans barre laterale
echo 3. Interface chat: Ctrl+Shift+C
echo 4. Fenetre externe Robot IA ouverte separement
echo.

pause
exit /b 0
