#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - 11 Modules Restants Complets
Interface 3 colonnes avec style noir et texte blanc
Cadres photos agrandis + Champs observation étendus
Critères détaillés pour chaque module
"""

import sys
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QFrame, QFormLayout, QLineEdit, QComboBox, QTextEdit, QSpinBox,
    QDateEdit, QCheckBox, QGroupBox, QScrollArea, QCalendarWidget
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor, QPixmap

class ModuleInterface3ColonnesNoir(QDialog):
    """Interface base pour modules avec 3 colonnes et style noir"""
    
    def __init__(self, parent=None, module_name="", module_icon=""):
        super().__init__(parent)
        self.module_name = module_name
        self.module_icon = module_icon
        
        self.setWindowTitle(f"🏢 MASTER COMPTA GÉNÉRAL - {module_name}")
        self.setFixedSize(1400, 900)
        self.setup_interface_noir_3_colonnes()
    
    def setup_interface_noir_3_colonnes(self):
        """Configure l'interface noire avec 3 colonnes"""
        
        # Style noir avec texte blanc
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.3 #1a1a1a, stop:0.7 #2d2d2d, stop:1 #000000);
                color: white;
                border: 3px solid #FFD700;
                border-radius: 15px;
            }
            QLineEdit, QComboBox, QTextEdit, QSpinBox, QDateEdit {
                background-color: #1a1a1a;
                color: white;
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 10px;
                font-size: 12pt;
                min-height: 30px;
            }
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus {
                border: 2px solid #FFFFFF;
                background-color: #2d2d2d;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:1 #2d2d2d);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 12px;
                font-size: 12pt;
                font-weight: bold;
                padding: 15px;
                min-width: 140px;
                min-height: 50px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                color: black;
                transform: scale(1.02);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #4d4d4d);
                color: white;
            }
            QLabel {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                background: transparent;
            }
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #FFD700;
                border-radius: 12px;
                margin: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                color: #FFD700;
                font-size: 15pt;
            }
            QTextEdit {
                min-height: 120px;
                max-height: 200px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête avec date/heure
        self.create_header_noir(layout)
        
        # Zone principale avec 3 colonnes
        self.create_3_columns_area_noir(layout)
        
        # Boutons d'action
        self.create_action_buttons_noir(layout)
    
    def create_header_noir(self, layout):
        """Crée l'en-tête noir avec date/heure"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #2d2d2d, stop:1 #000000);
                border: 3px solid #FFD700;
                border-radius: 20px;
                padding: 25px;
                margin: 15px;
            }
        """)
        header_frame.setFixedHeight(150)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Icône et titre
        title_layout = QVBoxLayout()
        
        title_label = QLabel(f"{self.module_icon} {self.module_name}")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 22pt;
                font-weight: bold;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
        """)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Interface 3 Colonnes • Fond Noir • Texte Blanc")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14pt;
                background: transparent;
            }
        """)
        title_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(title_layout)
        
        header_layout.addStretch()
        
        # Date et heure
        datetime_layout = QVBoxLayout()
        
        self.current_date = QLabel()
        self.current_date.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_date.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        datetime_layout.addWidget(self.current_date)
        
        self.current_time = QLabel()
        self.current_time.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_time.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18pt;
                font-weight: bold;
                background: rgba(255,255,255,0.1);
                border: 2px solid white;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        datetime_layout.addWidget(self.current_time)
        
        # Bouton calendrier
        calendar_btn = QPushButton("📅 Calendrier")
        calendar_btn.setFixedSize(130, 40)
        calendar_btn.clicked.connect(self.show_calendar_noir)
        datetime_layout.addWidget(calendar_btn)
        
        header_layout.addLayout(datetime_layout)
        
        layout.addWidget(header_frame)
        
        # Timer pour date/heure
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)
        self.update_datetime()
    
    def create_3_columns_area_noir(self, layout):
        """Crée la zone principale avec 3 colonnes"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid #FFD700;
                border-radius: 10px;
                background: transparent;
            }
        """)
        
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setSpacing(20)
        
        # Colonne 1 - Identification
        col1 = self.create_column_1()
        main_layout.addWidget(col1)
        
        # Colonne 2 - Détails
        col2 = self.create_column_2()
        main_layout.addWidget(col2)
        
        # Colonne 3 - Localisation et Photos
        col3 = self.create_column_3()
        main_layout.addWidget(col3)
        
        scroll_area.setWidget(main_widget)
        layout.addWidget(scroll_area)
    
    def create_column_1(self):
        """Colonne 1 - À implémenter dans les classes dérivées"""
        group = QGroupBox("🔢 Identification & Base")
        layout = QFormLayout(group)
        return group
    
    def create_column_2(self):
        """Colonne 2 - À implémenter dans les classes dérivées"""
        group = QGroupBox("⚙️ Détails & Technique")
        layout = QFormLayout(group)
        return group
    
    def create_column_3(self):
        """Colonne 3 - À implémenter dans les classes dérivées"""
        group = QGroupBox("📍 Localisation & Photos")
        layout = QFormLayout(group)
        return group
    
    def create_action_buttons_noir(self, layout):
        """Crée les boutons d'action avec style noir"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 15px;
                padding: 20px;
                margin: 15px;
            }
        """)
        buttons_frame.setFixedHeight(120)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # Boutons principaux
        validate_btn = QPushButton("✅ Valider")
        validate_btn.clicked.connect(self.validate_data)
        buttons_layout.addWidget(validate_btn)
        
        modify_btn = QPushButton("✏️ Modifier")
        modify_btn.clicked.connect(self.modify_data)
        buttons_layout.addWidget(modify_btn)
        
        # Boutons rapports multilingues
        report_fr_btn = QPushButton("📊 Rapport FR")
        report_fr_btn.clicked.connect(lambda: self.generate_report("fr"))
        buttons_layout.addWidget(report_fr_btn)
        
        report_ar_btn = QPushButton("📊 تقرير AR")
        report_ar_btn.clicked.connect(lambda: self.generate_report("ar"))
        buttons_layout.addWidget(report_ar_btn)
        
        report_en_btn = QPushButton("📊 Report EN")
        report_en_btn.clicked.connect(lambda: self.generate_report("en"))
        buttons_layout.addWidget(report_en_btn)
        
        # Bouton email
        email_btn = QPushButton("📧 Envoyer Email")
        email_btn.clicked.connect(self.send_email_report)
        buttons_layout.addWidget(email_btn)
        
        # Boutons navigation
        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        back_btn = QPushButton("🔙 Retour")
        back_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(back_btn)
        
        layout.addWidget(buttons_frame)
    
    def update_datetime(self):
        """Met à jour la date et l'heure"""
        now = datetime.now()
        date_str = f"📅 {now.strftime('%d/%m/%Y')}"
        time_str = f"🕐 {now.strftime('%H:%M:%S')}"
        
        if hasattr(self, 'current_date'):
            self.current_date.setText(date_str)
        if hasattr(self, 'current_time'):
            self.current_time.setText(time_str)
    
    def show_calendar_noir(self):
        """Affiche le calendrier avec style noir"""
        calendar_dialog = QDialog(self)
        calendar_dialog.setWindowTitle("📅 Calendrier")
        calendar_dialog.setFixedSize(500, 450)
        calendar_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                border: 3px solid #FFD700;
                border-radius: 15px;
            }
        """)
        
        layout = QVBoxLayout(calendar_dialog)
        
        calendar = QCalendarWidget()
        calendar.setStyleSheet("""
            QCalendarWidget {
                background-color: #1a1a1a;
                border: 2px solid #FFD700;
                border-radius: 10px;
                font-size: 12pt;
                color: white;
            }
            QCalendarWidget QToolButton {
                background-color: #000000;
                color: white;
                border: 1px solid #FFD700;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QCalendarWidget QToolButton:hover {
                background-color: #FFD700;
                color: black;
            }
            QCalendarWidget QAbstractItemView:enabled {
                background-color: #1a1a1a;
                color: white;
                selection-background-color: #FFD700;
                selection-color: black;
            }
        """)
        layout.addWidget(calendar)
        
        close_btn = QPushButton("✅ Fermer")
        close_btn.clicked.connect(calendar_dialog.accept)
        layout.addWidget(close_btn)
        
        calendar_dialog.exec()
    
    # Méthodes à implémenter dans les classes dérivées
    def validate_data(self):
        print(f"✅ Validation {self.module_name}")
    
    def modify_data(self):
        print(f"✏️ Modification {self.module_name}")
    
    def generate_report(self, language):
        print(f"📊 Génération rapport {self.module_name} en {language}")
    
    def send_email_report(self):
        print(f"📧 Envoi rapport {self.module_name} par email")

class ModuleOutilsTravaux(ModuleInterface3ColonnesNoir):
    """Module Outils de Travaux avec 16 critères détaillés"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "Outils de Travaux", "🔧")
    
    def create_column_1(self):
        """Colonne 1 - Identification Outil"""
        group = QGroupBox("🔧 Identification Outil")
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # Code outil
        self.code_outil = QLineEdit()
        self.code_outil.setPlaceholderText("OUT001 (automatique)")
        layout.addRow("🔢 Code outil:", self.code_outil)
        
        # Désignation
        self.designation = QLineEdit()
        self.designation.setPlaceholderText("Nom de l'outil")
        layout.addRow("📝 Désignation:", self.designation)
        
        # Type d'outil
        self.type_outil = QComboBox()
        self.type_outil.addItems([
            "Manuel", "Électrique", "Pneumatique", "Hydraulique", "Thermique",
            "Mesure", "Coupe", "Perçage", "Vissage", "Soudage", "Autre"
        ])
        layout.addRow("🔧 Type:", self.type_outil)
        
        # Catégorie
        self.categorie = QComboBox()
        self.categorie.addItems([
            "Outillage à main", "Machines portatives", "Équipement de mesure",
            "Outillage spécialisé", "Sécurité", "Maintenance", "Autre"
        ])
        layout.addRow("📂 Catégorie:", self.categorie)
        
        # Marque
        self.marque = QLineEdit()
        self.marque.setPlaceholderText("Marque de l'outil")
        layout.addRow("🏭 Marque:", self.marque)
        
        # Modèle
        self.modele = QLineEdit()
        self.modele.setPlaceholderText("Modèle de l'outil")
        layout.addRow("📱 Modèle:", self.modele)
        
        return group
    
    def create_column_2(self):
        """Colonne 2 - Caractéristiques et État"""
        group = QGroupBox("⚙️ Caractéristiques & État")
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # Numéro de série
        self.numero_serie = QLineEdit()
        self.numero_serie.setPlaceholderText("Numéro de série")
        layout.addRow("🔢 N° Série:", self.numero_serie)
        
        # Date d'acquisition
        self.date_acquisition = QDateEdit()
        self.date_acquisition.setCalendarPopup(True)
        layout.addRow("📅 Date acquisition:", self.date_acquisition)
        
        # Valeur d'acquisition
        self.valeur_acquisition = QSpinBox()
        self.valeur_acquisition.setRange(0, 999999)
        self.valeur_acquisition.setSuffix(" €")
        layout.addRow("💰 Valeur acquisition:", self.valeur_acquisition)
        
        # État général
        self.etat_general = QComboBox()
        self.etat_general.addItems([
            "Neuf", "Excellent", "Très bon", "Bon", "Correct", "Usagé",
            "Défaillant", "Hors service", "À réparer", "À réformer"
        ])
        layout.addRow("📊 État général:", self.etat_general)
        
        # Fréquence d'utilisation
        self.frequence_utilisation = QComboBox()
        self.frequence_utilisation.addItems([
            "Quotidienne", "Hebdomadaire", "Mensuelle", "Occasionnelle", "Rare"
        ])
        layout.addRow("📈 Fréquence usage:", self.frequence_utilisation)
        
        # Dernière maintenance
        self.derniere_maintenance = QDateEdit()
        self.derniere_maintenance.setCalendarPopup(True)
        layout.addRow("🔧 Dernière maintenance:", self.derniere_maintenance)
        
        return group
    
    def create_column_3(self):
        """Colonne 3 - Localisation et Photos"""
        group = QGroupBox("📍 Localisation & Photos")
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # Localisation
        self.localisation = QComboBox()
        self.localisation.addItems([
            "Atelier principal", "Atelier secondaire", "Magasin outils",
            "Véhicule de service", "Chantier", "Prêté", "Autre"
        ])
        layout.addRow("📍 Localisation:", self.localisation)
        
        # Responsable
        self.responsable = QLineEdit()
        self.responsable.setPlaceholderText("Nom du responsable")
        layout.addRow("👤 Responsable:", self.responsable)
        
        # Utilisateur principal
        self.utilisateur_principal = QLineEdit()
        self.utilisateur_principal.setPlaceholderText("Utilisateur principal")
        layout.addRow("👥 Utilisateur:", self.utilisateur_principal)
        
        # Cadre photo agrandi
        self.photo_outil = QTextEdit()
        self.photo_outil.setPlaceholderText("📸 CADRE PHOTO AGRANDI\n\nCliquez pour ajouter une photo de l'outil\n\nTaille recommandée: 800x600 pixels\nFormats supportés: JPG, PNG, BMP")
        self.photo_outil.setMinimumHeight(150)
        self.photo_outil.setMaximumHeight(200)
        self.photo_outil.setStyleSheet("""
            QTextEdit {
                background-color: #2d2d2d;
                border: 3px dashed #FFD700;
                border-radius: 10px;
                color: #FFD700;
                font-size: 14pt;
                text-align: center;
                padding: 20px;
            }
        """)
        layout.addRow("📸 Photo outil:", self.photo_outil)
        
        # Observation étendue
        self.observation = QTextEdit()
        self.observation.setPlaceholderText("📝 CHAMP OBSERVATION ÉTENDU\n\nDétails sur l'outil, historique d'utilisation, incidents, réparations effectuées, recommandations d'usage, précautions particulières...")
        self.observation.setMinimumHeight(150)
        self.observation.setMaximumHeight(200)
        layout.addRow("📋 Observation étendue:", self.observation)
        
        return group

class ModuleDocuments(ModuleInterface3ColonnesNoir):
    """Module Documents avec 12 critères détaillés"""

    def __init__(self, parent=None):
        super().__init__(parent, "Documents", "📄")

    def create_column_1(self):
        """Colonne 1 - Identification Document"""
        group = QGroupBox("📄 Identification Document")
        layout = QFormLayout(group)
        layout.setSpacing(15)

        # Code document
        self.code_document = QLineEdit()
        self.code_document.setPlaceholderText("DOC001 (automatique)")
        layout.addRow("🔢 Code document:", self.code_document)

        # Titre
        self.titre = QLineEdit()
        self.titre.setPlaceholderText("Titre du document")
        layout.addRow("📝 Titre:", self.titre)

        # Type de document
        self.type_document = QComboBox()
        self.type_document.addItems([
            "Contrat", "Facture", "Devis", "Rapport", "Manuel", "Procédure",
            "Certificat", "Garantie", "Plan", "Photo", "Autre"
        ])
        layout.addRow("📂 Type:", self.type_document)

        # Catégorie
        self.categorie = QComboBox()
        self.categorie.addItems([
            "Administratif", "Technique", "Commercial", "Juridique", "Financier",
            "Qualité", "Sécurité", "Formation", "Autre"
        ])
        layout.addRow("📋 Catégorie:", self.categorie)

        # Format
        self.format_document = QComboBox()
        self.format_document.addItems([
            "PDF", "Word", "Excel", "PowerPoint", "Image", "Vidéo", "Audio", "Autre"
        ])
        layout.addRow("📄 Format:", self.format_document)

        return group

    def create_column_2(self):
        """Colonne 2 - Détails et Gestion"""
        group = QGroupBox("⚙️ Détails & Gestion")
        layout = QFormLayout(group)
        layout.setSpacing(15)

        # Auteur
        self.auteur = QLineEdit()
        self.auteur.setPlaceholderText("Auteur du document")
        layout.addRow("👤 Auteur:", self.auteur)

        # Date de création
        self.date_creation = QDateEdit()
        self.date_creation.setCalendarPopup(True)
        layout.addRow("📅 Date création:", self.date_creation)

        # Version
        self.version = QLineEdit()
        self.version.setPlaceholderText("v1.0")
        layout.addRow("🔢 Version:", self.version)

        # Statut
        self.statut = QComboBox()
        self.statut.addItems([
            "Brouillon", "En révision", "Validé", "Publié", "Archivé", "Obsolète"
        ])
        layout.addRow("📊 Statut:", self.statut)

        # Niveau de confidentialité
        self.confidentialite = QComboBox()
        self.confidentialite.addItems([
            "Public", "Interne", "Confidentiel", "Secret", "Très secret"
        ])
        layout.addRow("🔒 Confidentialité:", self.confidentialite)

        # Date d'expiration
        self.date_expiration = QDateEdit()
        self.date_expiration.setCalendarPopup(True)
        layout.addRow("⏰ Date expiration:", self.date_expiration)

        return group

    def create_column_3(self):
        """Colonne 3 - Stockage et Aperçu"""
        group = QGroupBox("📍 Stockage & Aperçu")
        layout = QFormLayout(group)
        layout.setSpacing(15)

        # Emplacement physique
        self.emplacement_physique = QLineEdit()
        self.emplacement_physique.setPlaceholderText("Classeur, étagère, bureau...")
        layout.addRow("📍 Emplacement physique:", self.emplacement_physique)

        # Chemin numérique
        self.chemin_numerique = QLineEdit()
        self.chemin_numerique.setPlaceholderText("Chemin du fichier numérique")
        layout.addRow("💾 Chemin numérique:", self.chemin_numerique)

        # Aperçu document agrandi
        self.apercu_document = QTextEdit()
        self.apercu_document.setPlaceholderText("📄 APERÇU DOCUMENT AGRANDI\n\nPrévisualisation du document\nMiniature ou extrait du contenu\n\nCliquez pour ouvrir le document complet")
        self.apercu_document.setMinimumHeight(180)
        self.apercu_document.setMaximumHeight(220)
        self.apercu_document.setStyleSheet("""
            QTextEdit {
                background-color: #2d2d2d;
                border: 3px solid #FFD700;
                border-radius: 10px;
                color: white;
                font-size: 12pt;
                padding: 15px;
            }
        """)
        layout.addRow("📄 Aperçu agrandi:", self.apercu_document)

        # Commentaires étendus
        self.commentaires = QTextEdit()
        self.commentaires.setPlaceholderText("💬 COMMENTAIRES ÉTENDUS\n\nRésumé du contenu, mots-clés, références croisées, historique des modifications, commentaires des utilisateurs...")
        self.commentaires.setMinimumHeight(150)
        layout.addRow("💬 Commentaires étendus:", self.commentaires)

        return group
