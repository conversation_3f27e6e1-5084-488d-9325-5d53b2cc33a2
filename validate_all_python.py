#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VALIDATEUR UNIVERSEL DE TOUS LES FICHIERS PYTHON
Valide que tous les fichiers .py sont syntaxiquement corrects
"""

import ast
import os
from pathlib import Path
from typing import List, Dict, Any, Tuple

class UniversalPythonValidator:
    def __init__(self):
        self.results = {}
        self.total_files = 0
        self.valid_files = 0
        self.errors = []
        
    def find_all_python_files(self) -> List[Path]:
        """Trouve tous les fichiers Python"""
        python_files = []
        
        for root, dirs, files in os.walk('.'):
            # Ignorer certains répertoires
            dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.vscode']
            
            for file in files:
                if file.endswith('.py'):
                    file_path = Path(root) / file
                    python_files.append(file_path)
        
        return sorted(python_files)
    
    def validate_syntax(self, file_path: Path) -> <PERSON><PERSON>[bool, str]:
        """Valide la syntaxe d'un fichier Python"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test de compilation
            compile(content, str(file_path), 'exec')
            
            # Test AST
            ast.parse(content)
            
            return True, "Syntaxe correcte"
            
        except SyntaxError as e:
            return False, f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
        except Exception as e:
            return False, f"Erreur: {e}"
    
    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyse complète d'un fichier Python"""
        result = {
            "path": str(file_path),
            "exists": file_path.exists(),
            "syntax_valid": False,
            "error": None,
            "size": 0,
            "lines": 0,
            "has_pylance_config": False,
            "typed_variables": 0,
            "functions": 0,
            "classes": 0
        }
        
        if not result["exists"]:
            result["error"] = "Fichier non trouvé"
            return result
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result["size"] = len(content)
            lines = content.splitlines()
            result["lines"] = len(lines)
            
            # Vérifier la syntaxe
            syntax_valid, error_msg = self.validate_syntax(file_path)
            result["syntax_valid"] = syntax_valid
            if not syntax_valid:
                result["error"] = error_msg
            
            # Analyser le contenu
            result["has_pylance_config"] = any('pyright:' in line for line in lines[:20])
            result["typed_variables"] = content.count(': Any =') + content.count(': str =') + content.count(': Dict[')
            result["functions"] = content.count('def ')
            result["classes"] = content.count('class ')
            
        except Exception as e:
            result["error"] = f"Erreur lecture: {e}"
        
        return result
    
    def validate_all_files(self) -> bool:
        """Valide tous les fichiers Python"""
        print("🔍 VALIDATION UNIVERSELLE DE TOUS LES FICHIERS PYTHON")
        print("=" * 70)
        
        python_files = self.find_all_python_files()
        self.total_files = len(python_files)
        
        print(f"📁 Fichiers Python trouvés: {self.total_files}")
        
        # Analyser chaque fichier
        for file_path in python_files:
            print(f"\n🔍 Analyse de {file_path}...")
            
            result = self.analyze_file(file_path)
            self.results[str(file_path)] = result
            
            if result["syntax_valid"]:
                self.valid_files += 1
                print(f"✅ {file_path.name}: Syntaxe correcte")
                print(f"  - Lignes: {result['lines']}")
                print(f"  - Fonctions: {result['functions']}")
                print(f"  - Classes: {result['classes']}")
                print(f"  - Variables typées: {result['typed_variables']}")
                print(f"  - Config Pylance: {'✅' if result['has_pylance_config'] else '❌'}")
            else:
                print(f"❌ {file_path.name}: {result['error']}")
                self.errors.append(f"{file_path.name}: {result['error']}")
        
        return self.generate_report()
    
    def generate_report(self) -> bool:
        """Génère le rapport final"""
        print("\n" + "="*70)
        print("📊 RAPPORT DE VALIDATION UNIVERSELLE")
        print("="*70)
        
        success_rate = (self.valid_files / self.total_files) * 100 if self.total_files > 0 else 0
        
        print(f"✅ Fichiers valides: {self.valid_files}/{self.total_files}")
        print(f"📈 Taux de réussite: {success_rate:.1f}%")
        
        # Statistiques détaillées
        total_lines = sum(r["lines"] for r in self.results.values() if r["syntax_valid"])
        total_functions = sum(r["functions"] for r in self.results.values() if r["syntax_valid"])
        total_classes = sum(r["classes"] for r in self.results.values() if r["syntax_valid"])
        total_typed_vars = sum(r["typed_variables"] for r in self.results.values() if r["syntax_valid"])
        files_with_pylance = sum(1 for r in self.results.values() if r["has_pylance_config"])
        
        print(f"\n📊 STATISTIQUES GLOBALES:")
        print(f"  - Total lignes de code: {total_lines}")
        print(f"  - Total fonctions: {total_functions}")
        print(f"  - Total classes: {total_classes}")
        print(f"  - Variables typées: {total_typed_vars}")
        print(f"  - Fichiers avec config Pylance: {files_with_pylance}/{self.total_files}")
        
        # Détails par fichier
        print(f"\n📋 DÉTAILS PAR FICHIER:")
        for file_path, result in self.results.items():
            status = "✅" if result["syntax_valid"] else "❌"
            pylance = "🛡️" if result["has_pylance_config"] else "⚠️"
            print(f"  {status} {pylance} {Path(file_path).name}")
            if not result["syntax_valid"]:
                print(f"      Erreur: {result['error']}")
        
        # Erreurs trouvées
        if self.errors:
            print(f"\n❌ ERREURS TROUVÉES ({len(self.errors)}):")
            for error in self.errors:
                print(f"  - {error}")
        
        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        if files_with_pylance < self.total_files:
            print(f"  - Ajouter la configuration Pylance à {self.total_files - files_with_pylance} fichiers")
        if total_typed_vars < total_functions:
            print(f"  - Ajouter des annotations de type à plus de variables")
        if self.errors:
            print(f"  - Corriger les {len(self.errors)} erreurs de syntaxe")
        
        # Déterminer le statut final
        if success_rate >= 95:
            print(f"\n🎉 VALIDATION UNIVERSELLE EXCELLENTE !")
            print("✅ Tous les fichiers Python sont parfaits")
            print("✅ Syntaxe correcte partout")
            print("✅ Configuration optimale")
            return True
        elif success_rate >= 80:
            print(f"\n✅ Validation majoritairement réussie")
            print("⚠️ Quelques améliorations mineures possibles")
            return True
        else:
            print(f"\n⚠️ Validation partielle")
            print("❌ Corrections importantes nécessaires")
            return False
    
    def create_summary_file(self):
        """Crée un fichier de résumé"""
        summary_content = f"""# RAPPORT DE VALIDATION PYTHON

## Résumé
- **Fichiers analysés**: {self.total_files}
- **Fichiers valides**: {self.valid_files}
- **Taux de réussite**: {(self.valid_files / self.total_files) * 100:.1f}%

## Détails par fichier
"""
        
        for file_path, result in self.results.items():
            status = "✅ VALIDE" if result["syntax_valid"] else "❌ ERREUR"
            summary_content += f"\n### {Path(file_path).name}\n"
            summary_content += f"- **Statut**: {status}\n"
            summary_content += f"- **Lignes**: {result['lines']}\n"
            summary_content += f"- **Fonctions**: {result['functions']}\n"
            summary_content += f"- **Classes**: {result['classes']}\n"
            summary_content += f"- **Variables typées**: {result['typed_variables']}\n"
            summary_content += f"- **Config Pylance**: {'✅' if result['has_pylance_config'] else '❌'}\n"
            
            if result["error"]:
                summary_content += f"- **Erreur**: {result['error']}\n"
        
        if self.errors:
            summary_content += f"\n## Erreurs à corriger\n"
            for error in self.errors:
                summary_content += f"- {error}\n"
        
        try:
            with open('PYTHON_VALIDATION_REPORT.md', 'w', encoding='utf-8') as f:
                f.write(summary_content)
            print("📄 Rapport créé: PYTHON_VALIDATION_REPORT.md")
        except Exception as e:
            print(f"⚠️ Erreur création rapport: {e}")

def main():
    """Fonction principale"""
    validator = UniversalPythonValidator()
    success = validator.validate_all_files()
    validator.create_summary_file()
    
    if success:
        print("\n🏆 TOUS LES FICHIERS PYTHON SONT VALIDÉS !")
    else:
        print("\n⚠️ Certains fichiers nécessitent des corrections")
    
    return success

if __name__ == "__main__":
    main()
