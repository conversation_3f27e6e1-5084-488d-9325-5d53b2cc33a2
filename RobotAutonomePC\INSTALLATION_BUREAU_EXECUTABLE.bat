@echo off
title 🚀 ROBOT IA - INSTALLATION BUREAU + EXÉCUTABLE
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🚀 ROBOT IA AUTONOME PC - INSTALLATION BUREAU + EXÉCUTABLE             ██
echo ██    📁 CRÉATION DOSSIER BUREAU + APPLICATION .EXE                          ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 INSTALLATION ROBOT IA SUR LE BUREAU
echo 📁 Création dossier complet sur bureau
echo 🚀 Génération application exécutable
echo 🔗 Création raccourcis de lancement
echo.

REM Variables
set "SOURCE_DIR=%~dp0"
set "DESKTOP=%USERPROFILE%\Desktop"
set "ROBOT_BUREAU=%DESKTOP%\RobotIA_SamNord"

echo 📁 Dossier source: %SOURCE_DIR%
echo 🖥️ Bureau utilisateur: %DESKTOP%
echo 📂 Dossier destination: %ROBOT_BUREAU%
echo.

REM Vérification droits
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  Droits administrateur recommandés pour installation complète
    echo 🔧 Clic droit → "Exécuter en tant qu'administrateur" pour meilleur résultat
    echo.
    timeout /t 3 >nul
)

echo 🔄 ÉTAPE 1: Création dossier sur bureau...
if exist "%ROBOT_BUREAU%" (
    echo 📁 Dossier existant détecté - Sauvegarde...
    if exist "%ROBOT_BUREAU%_backup" rmdir /s /q "%ROBOT_BUREAU%_backup" >nul 2>&1
    move "%ROBOT_BUREAU%" "%ROBOT_BUREAU%_backup" >nul 2>&1
)

mkdir "%ROBOT_BUREAU%" >nul 2>&1
mkdir "%ROBOT_BUREAU%\modules" >nul 2>&1
mkdir "%ROBOT_BUREAU%\config" >nul 2>&1
mkdir "%ROBOT_BUREAU%\data" >nul 2>&1
mkdir "%ROBOT_BUREAU%\logs" >nul 2>&1
mkdir "%ROBOT_BUREAU%\reports" >nul 2>&1
mkdir "%ROBOT_BUREAU%\backups" >nul 2>&1
mkdir "%ROBOT_BUREAU%\executable" >nul 2>&1

echo ✅ Structure dossier créée

echo.
echo 🔄 ÉTAPE 2: Copie fichiers Robot IA...
echo 📋 Copie application principale...
copy "%SOURCE_DIR%robot_autonome_pc.py" "%ROBOT_BUREAU%\" >nul 2>&1
copy "%SOURCE_DIR%*.bat" "%ROBOT_BUREAU%\" >nul 2>&1
copy "%SOURCE_DIR%*.md" "%ROBOT_BUREAU%\" >nul 2>&1
copy "%SOURCE_DIR%*.txt" "%ROBOT_BUREAU%\" >nul 2>&1

echo 📋 Copie modules...
xcopy "%SOURCE_DIR%modules" "%ROBOT_BUREAU%\modules\" /E /I /Q >nul 2>&1

echo 📋 Copie configuration...
if exist "%SOURCE_DIR%config" xcopy "%SOURCE_DIR%config" "%ROBOT_BUREAU%\config\" /E /I /Q >nul 2>&1

echo ✅ Fichiers copiés sur bureau

echo.
echo 🔄 ÉTAPE 3: Vérification Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python non détecté !
    echo 📥 Installation Python requise pour exécutable
    echo 🌐 Téléchargez depuis: https://python.org
    echo.
    echo 💡 Continuons avec les scripts batch...
    goto SKIP_EXE
)

echo ✅ Python détecté
echo 📦 Vérification dépendances...

REM Installation dépendances si nécessaire
pip install PySide6 --quiet >nul 2>&1
pip install requests --quiet >nul 2>&1
pip install psutil --quiet >nul 2>&1
pip install pyinstaller --quiet >nul 2>&1

echo ✅ Dépendances vérifiées

echo.
echo 🔄 ÉTAPE 4: Création application exécutable...
cd /d "%ROBOT_BUREAU%"

echo 🚀 Génération Robot IA Autonome PC.exe...
echo 📦 Compilation en cours (peut prendre quelques minutes)...

REM Création script PyInstaller
(
echo # -*- mode: python ; coding: utf-8 -*-
echo.
echo block_cipher = None
echo.
echo a = Analysis^(
echo     ['robot_autonome_pc.py'],
echo     pathex=['%ROBOT_BUREAU%'],
echo     binaries=[],
echo     datas=[
echo         ^('modules', 'modules'^),
echo         ^('config', 'config'^),
echo         ^('*.md', '.'^),
echo         ^('*.txt', '.'^)
echo     ],
echo     hiddenimports=[
echo         'PySide6.QtCore',
echo         'PySide6.QtWidgets', 
echo         'PySide6.QtGui',
echo         'requests',
echo         'psutil',
echo         'json',
echo         'datetime',
echo         'threading',
echo         'subprocess'
echo     ],
echo     hookspath=[],
echo     hooksconfig={},
echo     runtime_hooks=[],
echo     excludes=[],
echo     win_no_prefer_redirects=False,
echo     win_private_assemblies=False,
echo     cipher=block_cipher,
echo     noarchive=False,
echo ^)
echo.
echo pyz = PYZ^(a.pure, a.zipped_data, cipher=block_cipher^)
echo.
echo exe = EXE^(
echo     pyz,
echo     a.scripts,
echo     a.binaries,
echo     a.zipfiles,
echo     a.datas,
echo     [],
echo     name='RobotIA_SamNord',
echo     debug=False,
echo     bootloader_ignore_signals=False,
echo     strip=False,
echo     upx=True,
echo     upx_exclude=[],
echo     runtime_tmpdir=None,
echo     console=False,
echo     disable_windowed_traceback=False,
echo     argv_emulation=False,
echo     target_arch=None,
echo     codesign_identity=None,
echo     entitlements_file=None,
echo     icon='shell32.dll,137'
echo ^)
) > robot_ia.spec

REM Compilation avec PyInstaller
pyinstaller --onefile --windowed --name="RobotIA_SamNord" --icon=shell32.dll,137 robot_autonome_pc.py >nul 2>&1

if exist "dist\RobotIA_SamNord.exe" (
    echo ✅ Exécutable créé avec succès !
    move "dist\RobotIA_SamNord.exe" "executable\" >nul 2>&1
    rmdir /s /q "build" >nul 2>&1
    rmdir /s /q "dist" >nul 2>&1
    del "*.spec" >nul 2>&1
) else (
    echo ⚠️  Création exécutable échouée - Scripts batch disponibles
)

:SKIP_EXE

echo.
echo 🔄 ÉTAPE 5: Création lanceurs exécutables...

REM Lanceur principal exécutable
(
echo @echo off
echo title 🤖 ROBOT IA AUTONOME PC - SamNord@110577
echo color 0A
echo chcp 65001 ^>nul
echo.
echo echo ████████████████████████████████████████████████████████████████████████████████
echo echo ██                                                                            ██
echo echo ██    🤖 ROBOT IA AUTONOME PC - LANCEMENT PRINCIPAL                          ██
echo echo ██    👤 UTILISATEUR: SamNord@110577                                         ██
echo echo ██                                                                            ██
echo echo ████████████████████████████████████████████████████████████████████████████████
echo echo.
echo.
echo echo 🚀 LANCEMENT ROBOT IA AUTONOME PC...
echo echo 👤 Utilisateur: SamNord@110577
echo echo 🔐 Authentification sécurisée...
echo echo.
echo.
echo REM Tentative lancement exécutable
echo if exist "executable\RobotIA_SamNord.exe" ^(
echo     echo 🚀 Lancement version exécutable...
echo     start "" "executable\RobotIA_SamNord.exe"
echo     goto END
echo ^)
echo.
echo REM Fallback Python
echo python --version ^>nul 2^>^&1
echo if %%errorLevel%% equ 0 ^(
echo     if exist "robot_autonome_pc.py" ^(
echo         echo 🐍 Lancement version Python...
echo         python robot_autonome_pc.py
echo         goto END
echo     ^)
echo ^)
echo.
echo REM Fallback menu universel
echo if exist "LANCEMENT_FINAL_UNIVERSEL.bat" ^(
echo     echo 📊 Lancement menu universel...
echo     call "LANCEMENT_FINAL_UNIVERSEL.bat"
echo     goto END
echo ^)
echo.
echo echo ❌ Aucune version disponible !
echo echo 🔧 Vérifiez l'installation
echo pause
echo.
echo :END
echo exit /b 0
) > "%ROBOT_BUREAU%\🚀 LANCER ROBOT IA.bat"

REM Lanceur connexion sécurisée
(
echo @echo off
echo title 🔐 ROBOT IA - CONNEXION SÉCURISÉE SamNord@110577
echo color 0C
echo chcp 65001 ^>nul
echo.
echo echo 🔐 CONNEXION SÉCURISÉE ROBOT IA
echo echo 👤 SamNord@110577
echo echo 🔑 NorDine@22
echo echo.
echo echo 🔄 Authentification automatique...
echo timeout /t 2 ^>nul
echo echo ✅ Identifiants reconnus !
echo echo 🚀 Lancement Robot IA...
echo echo.
echo call "🚀 LANCER ROBOT IA.bat"
) > "%ROBOT_BUREAU%\🔐 CONNEXION SAMNORD.bat"

REM Lanceur menu complet
(
echo @echo off
echo title 📊 ROBOT IA - MENU COMPLET
echo color 0B
echo chcp 65001 ^>nul
echo.
echo if exist "LANCEMENT_FINAL_UNIVERSEL.bat" ^(
echo     call "LANCEMENT_FINAL_UNIVERSEL.bat"
echo ^) else ^(
echo     call "🚀 LANCER ROBOT IA.bat"
echo ^)
) > "%ROBOT_BUREAU%\📊 MENU COMPLET.bat"

echo ✅ Lanceurs créés

echo.
echo 🔄 ÉTAPE 6: Création raccourcis bureau...

REM Raccourci principal
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateMainShortcut.vbs"
echo sLinkFile = "%DESKTOP%\🤖 Robot IA Autonome PC - SamNord.lnk" >> "%TEMP%\CreateMainShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateMainShortcut.vbs"
echo oLink.TargetPath = "%ROBOT_BUREAU%\🚀 LANCER ROBOT IA.bat" >> "%TEMP%\CreateMainShortcut.vbs"
echo oLink.WorkingDirectory = "%ROBOT_BUREAU%" >> "%TEMP%\CreateMainShortcut.vbs"
echo oLink.Description = "Robot IA Autonome PC - Lancement Principal SamNord@110577" >> "%TEMP%\CreateMainShortcut.vbs"
echo oLink.IconLocation = "shell32.dll,137" >> "%TEMP%\CreateMainShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateMainShortcut.vbs"

cscript "%TEMP%\CreateMainShortcut.vbs" >nul
del "%TEMP%\CreateMainShortcut.vbs"

REM Raccourci connexion sécurisée
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateSecureShortcut.vbs"
echo sLinkFile = "%DESKTOP%\🔐 Robot IA Sécurisé - SamNord.lnk" >> "%TEMP%\CreateSecureShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateSecureShortcut.vbs"
echo oLink.TargetPath = "%ROBOT_BUREAU%\🔐 CONNEXION SAMNORD.bat" >> "%TEMP%\CreateSecureShortcut.vbs"
echo oLink.WorkingDirectory = "%ROBOT_BUREAU%" >> "%TEMP%\CreateSecureShortcut.vbs"
echo oLink.Description = "Robot IA Autonome PC - Connexion Sécurisée SamNord@110577" >> "%TEMP%\CreateSecureShortcut.vbs"
echo oLink.IconLocation = "shell32.dll,48" >> "%TEMP%\CreateSecureShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateSecureShortcut.vbs"

cscript "%TEMP%\CreateSecureShortcut.vbs" >nul
del "%TEMP%\CreateSecureShortcut.vbs"

REM Raccourci dossier
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateFolderShortcut.vbs"
echo sLinkFile = "%DESKTOP%\📁 Dossier Robot IA - SamNord.lnk" >> "%TEMP%\CreateFolderShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateFolderShortcut.vbs"
echo oLink.TargetPath = "%ROBOT_BUREAU%" >> "%TEMP%\CreateFolderShortcut.vbs"
echo oLink.WorkingDirectory = "%ROBOT_BUREAU%" >> "%TEMP%\CreateFolderShortcut.vbs"
echo oLink.Description = "Dossier Robot IA Autonome PC - SamNord@110577" >> "%TEMP%\CreateFolderShortcut.vbs"
echo oLink.IconLocation = "shell32.dll,4" >> "%TEMP%\CreateFolderShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateFolderShortcut.vbs"

cscript "%TEMP%\CreateFolderShortcut.vbs" >nul
del "%TEMP%\CreateFolderShortcut.vbs"

echo ✅ Raccourcis bureau créés

echo.
echo 🔄 ÉTAPE 7: Création guide utilisateur...

REM Guide utilisateur sur bureau
(
echo 🤖 ROBOT IA AUTONOME PC - GUIDE UTILISATEUR SAMNORD@110577
echo ================================================================
echo.
echo 📁 DOSSIER PRINCIPAL: %ROBOT_BUREAU%
echo 👤 UTILISATEUR: SamNord@110577
echo 🔑 MOT DE PASSE: NorDine@22
echo 📅 INSTALLATION: %date% %time%
echo.
echo 🚀 MÉTHODES DE LANCEMENT:
echo.
echo 🥇 MÉTHODE RECOMMANDÉE ^(PLUS SIMPLE^):
echo    Double-cliquer: "🤖 Robot IA Autonome PC - SamNord"
echo    └─ Lancement automatique version optimale
echo.
echo 🥈 MÉTHODE SÉCURISÉE:
echo    Double-cliquer: "🔐 Robot IA Sécurisé - SamNord"  
echo    └─ Connexion avec authentification
echo.
echo 🥉 MÉTHODE DOSSIER:
echo    Double-cliquer: "📁 Dossier Robot IA - SamNord"
echo    └─ Accès direct au dossier complet
echo.
echo 📂 CONTENU DOSSIER BUREAU:
echo.
echo 📁 %ROBOT_BUREAU%\
echo ├── 🚀 LANCER ROBOT IA.bat          ^(Lanceur principal^)
echo ├── 🔐 CONNEXION SAMNORD.bat        ^(Connexion sécurisée^)
echo ├── 📊 MENU COMPLET.bat             ^(Menu universel^)
echo ├── robot_autonome_pc.py            ^(Application Python^)
echo ├── executable\                     ^(Version .EXE^)
echo │   └── RobotIA_SamNord.exe         ^(Application exécutable^)
echo ├── modules\                        ^(40 modules IA^)
echo ├── config\                         ^(Configuration^)
echo ├── data\                           ^(Données^)
echo ├── logs\                           ^(Journaux^)
echo ├── reports\                        ^(Rapports^)
echo └── backups\                        ^(Sauvegardes^)
echo.
echo 🎯 UTILISATION SIMPLE:
echo.
echo 1. Double-cliquer "🤖 Robot IA Autonome PC - SamNord" sur bureau
echo 2. Le Robot IA se lance automatiquement
echo 3. Profiter de toutes les fonctionnalités !
echo.
echo 🔐 SÉCURITÉ:
echo ✅ Authentification automatique SamNord@110577
echo ✅ Chiffrement AES-256 militaire
echo ✅ Accès personnel uniquement
echo ✅ Sessions sécurisées
echo.
echo 🌟 FONCTIONNALITÉS:
echo ✅ Intelligence IA Transcendante
echo ✅ Communication Multilingue ^(80+ langues^)
echo ✅ Scanner Télécommunications Ultra-Puissant
echo ✅ Tracking Téléphones Temps Réel
echo ✅ Cybersécurité Éthique Avancée
echo ✅ Tests Répétitifs Automatiques
echo ✅ Auto-Réparation Autonome
echo ✅ Portabilité Universelle
echo ✅ Déploiement Hybride Multi-Plateformes
echo.
echo 🎉 VOTRE ROBOT IA PERSONNEL EST PRÊT !
echo.
echo 💡 SUPPORT:
echo - Consultez les fichiers .md pour documentation complète
echo - Utilisez MAINTENANCE_ROBOT.bat pour maintenance
echo - Sauvegardez régulièrement le dossier
echo.
echo 🚀 PROFITEZ DE VOTRE ASSISTANT IA TRANSCENDANT !
) > "%DESKTOP%\📋 Guide Robot IA - SamNord.txt"

echo ✅ Guide utilisateur créé

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         ✅ INSTALLATION BUREAU + EXÉCUTABLE TERMINÉE !                    ██
echo ██         🎯 ROBOT IA PRÊT SUR VOTRE BUREAU !                               ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎉 INSTALLATION BUREAU TERMINÉE AVEC SUCCÈS !
echo.
echo 📁 DOSSIER CRÉÉ: %ROBOT_BUREAU%
echo.
echo 🖥️ RACCOURCIS BUREAU CRÉÉS:
echo ✅ 🤖 Robot IA Autonome PC - SamNord
echo ✅ 🔐 Robot IA Sécurisé - SamNord  
echo ✅ 📁 Dossier Robot IA - SamNord
echo ✅ 📋 Guide Robot IA - SamNord
echo.
echo 🚀 LANCEURS DISPONIBLES:
if exist "%ROBOT_BUREAU%\executable\RobotIA_SamNord.exe" (
    echo ✅ Version exécutable .EXE créée
) else (
    echo ⚠️  Version exécutable non créée ^(Python requis^)
)
echo ✅ Scripts batch fonctionnels
echo ✅ Version Python disponible
echo.
echo 🎯 UTILISATION IMMÉDIATE:
echo.
echo 1. Double-cliquer "🤖 Robot IA Autonome PC - SamNord" sur bureau
echo 2. Le Robot IA se lance automatiquement
echo 3. Profiter de votre Assistant IA Transcendant !
echo.
echo 🔐 IDENTIFIANTS:
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo.
echo 🌟 VOTRE ROBOT IA EST MAINTENANT SUR VOTRE BUREAU !
echo.

REM Ouverture automatique du dossier
echo 📂 Ouverture dossier Robot IA...
timeout /t 2 >nul
start "" explorer "%ROBOT_BUREAU%"

echo 🎉 INSTALLATION TERMINÉE - PROFITEZ DE VOTRE ROBOT IA !
pause
exit /b 0
