@echo off
title GestImmob - Logiciel de Gestion Immobilière
color 0A

echo ========================================
echo    GestImmob v2.0.0
echo    Logiciel de Gestion Immobilière
echo ========================================
echo.

REM Vérifier que Python est installé
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERREUR: Python n'est pas installé ou n'est pas dans le PATH
    echo.
    echo Veuillez installer Python 3.8+ depuis https://python.org
    echo.
    pause
    exit /b 1
)

echo Python détecté: 
python --version

echo.
echo Vérification des dépendances...

REM Installer les dépendances si nécessaire
if exist "requirements.txt" (
    echo Installation/mise à jour des dépendances...
    pip install -r requirements.txt --quiet --upgrade
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ATTENTION: Certaines dépendances n'ont pas pu être installées
        echo L'application peut ne pas fonctionner correctement
        echo.
    )
) else (
    echo Fichier requirements.txt non trouvé
)

echo.
echo Initialisation de la base de données...

REM Créer la base de données si elle n'existe pas
if not exist "immobilisations.db" (
    echo Création de la base de données...
    python -c "import sqlite3; sqlite3.connect('immobilisations.db').close()"
)

REM Créer les dossiers nécessaires
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "data" mkdir data
if not exist "temp" mkdir temp

echo.
echo Démarrage de GestImmob...
echo.

REM Lancer l'application
python src/main.py

REM Gestion des erreurs
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ========================================
    echo ERREUR: L'application s'est fermée de manière inattendue
    echo Code d'erreur: %ERRORLEVEL%
    echo ========================================
    echo.
    echo Vérifiez les logs dans le dossier 'logs' pour plus d'informations
    echo.
    echo Solutions possibles:
    echo 1. Réinstallez les dépendances: pip install -r requirements.txt
    echo 2. Vérifiez que tous les fichiers sont présents
    echo 3. Contactez le support technique
    echo.
) else (
    echo.
    echo GestImmob fermé normalement
)

echo.
pause
