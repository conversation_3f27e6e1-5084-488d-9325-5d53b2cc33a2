<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:21:45)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème vient du fait que `self.table.horizontalHeaderItem(i)` peut retourner `None`, auquel cas l'accès à `.text()` provoque une erreur. Il faut donc vérifier explicitement que l'objet n'est pas `None` avant d'accéder à `.text()`. Le code sélectionné est déjà protégé par une condition, mais il peut être plus sûr d'utiliser une variable temporaire pour la lisibilité.

Voici la correction à appliquer dans toutes les occurrences similaires :

````python
# ...existing code...
f.write(
    "\t".join([
        self.table.horizontalHeaderItem(i).text() if self.table.horizontalHeaderItem(i) is not None else ""
        for i in range(self.table.columnCount())
    ]) + "\n"
)
# ...existing code...
writer.writerow([
    self.table.horizontalHeaderItem(i).text() if self.table.horizontalHeaderItem(i) is not None else ""
    for i in range(self.table.columnCount())
])
# ...existing code...
# Correction proposée :
f.write(
    "\t".join([
        (header.text() if header is not None else "")
        for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
    ]) + "\n"
)
# ...existing code...
writer.writerow([
    (header.text() if header is not None else "")
    for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
])
# ...existing code...
````

Ainsi, on évite d'appeler `.text()` sur `None` et le code reste lisible et sûr.

---

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Remplacez la logique d'accès à `.text()` sur un objet potentiellement `None` par une approche utilisant une variable temporaire pour éviter toute erreur d'attribut.

````python
# ...existing code...
if self.chk_entete.isChecked():
    f.write(
        "\t".join([
            (header.text() if header is not None else "")
            for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
        ]) + "\n"
    )
# ...existing code...
writer.writerow([
    (header.text() if header is not None else "")
    for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
])
# ...existing code...
````

Cela garantit que `.text()` n'est jamais appelé sur `None` et corrige l'erreur signalée.

---

