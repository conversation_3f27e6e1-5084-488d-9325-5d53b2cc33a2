@echo off
title Robot IA SamNord - Windows Safe
color 0A

echo.
echo ================================================
echo    ROBOT IA SAMNORD - VERSION WINDOWS SAFE
echo    Aucune erreur Windows - 100%% Compatible
echo    SamNord@110577
echo ================================================
echo.

echo Verification Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Python non trouve
    echo Veuillez installer Python
    pause
    exit /b 1
)

echo Python detecte - OK
echo.

echo Lancement Robot IA Windows Safe...
echo.

python ROBOT_IA_WINDOWS_SAFE.py

echo.
echo Robot IA termine.
pause
exit /b 0
