#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple des améliorations de performance
"""

import sys
import os
import time

def test_basic_imports():
    """Test des imports de base"""
    print("🔍 Test des imports de base...")
    
    modules = ['json', 'sqlite3', 'csv', 'datetime']
    
    for module in modules:
        try:
            start = time.time()
            __import__(module)
            duration = (time.time() - start) * 1000
            print(f"✅ {module}: {duration:.2f}ms")
        except ImportError as e:
            print(f"❌ {module}: {e}")

def test_pyside6():
    """Test PySide6"""
    print("\n🖥️ Test PySide6...")
    
    try:
        start = time.time()
        from PySide6.QtWidgets import QApplication
        duration = (time.time() - start) * 1000
        print(f"✅ PySide6.QtWidgets: {duration:.2f}ms")
        
        # Test création app
        if not QApplication.instance():
            app = QApplication([])
            print("✅ QApplication créée")
        else:
            print("✅ QApplication déjà existante")
            
    except ImportError as e:
        print(f"❌ PySide6: {e}")

def test_database():
    """Test base de données"""
    print("\n🗄️ Test base de données...")
    
    try:
        import sqlite3
        
        # Test connexion standard
        start = time.time()
        conn1 = sqlite3.connect(":memory:")
        std_time = (time.time() - start) * 1000
        
        # Test connexion optimisée
        start = time.time()
        conn2 = sqlite3.connect(":memory:", check_same_thread=False)
        cursor = conn2.cursor()
        cursor.execute("PRAGMA journal_mode = WAL")
        cursor.execute("PRAGMA cache_size = -64000")
        opt_time = (time.time() - start) * 1000
        
        print(f"📊 Connexion standard: {std_time:.2f}ms")
        print(f"🚀 Connexion optimisée: {opt_time:.2f}ms")
        
        improvement = ((std_time - opt_time) / std_time * 100) if std_time > 0 else 0
        print(f"📈 Amélioration: {improvement:.1f}%")
        
        conn1.close()
        conn2.close()
        
    except Exception as e:
        print(f"❌ Erreur DB: {e}")

def test_module_manager():
    """Test du gestionnaire de modules"""
    print("\n🔧 Test gestionnaire de modules...")
    
    try:
        # Ajouter src au path
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        # Test import du ModuleManager
        start = time.time()
        from main import ModuleManager
        import_time = (time.time() - start) * 1000
        
        print(f"✅ ModuleManager importé: {import_time:.2f}ms")
        
        # Test utilisation
        manager = ModuleManager()
        
        # Test import de modules
        test_result = manager.import_module_safe('json')
        if test_result:
            print("✅ Import json via ModuleManager: OK")
        else:
            print("❌ Import json via ModuleManager: ÉCHEC")
        
        # Rapport
        report = manager.get_import_report()
        print(f"📊 Modules chargés: {report['modules_loaded']}")
        print(f"❌ Modules échoués: {report['modules_failed']}")
        
    except Exception as e:
        print(f"❌ Erreur ModuleManager: {e}")

def main():
    """Fonction principale"""
    print("🚀 Test Simple des Améliorations de Performance")
    print("=" * 50)
    
    test_basic_imports()
    test_pyside6()
    test_database()
    test_module_manager()
    
    print("\n✅ Tests terminés!")

if __name__ == "__main__":
    main()
