<!-- Generated by SpecStory -->

# /fix Le type de « _is_float » est partiellement in (2025-06-08 11:30:28)

_**User**_

/fix Le type de « _is_float » est partiellement inconnu
  Le type de "_is_float" est "(value: Unknown) -> bool"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Déclarez le type d'argument de la méthode `_is_float` pour lever l'ambiguïté de type. Ajoutez une annotation de type explicite (`value: str`) à la définition de la méthode `_is_float`.

````python
# ...existing code...
    def _is_float(self, value: str) -> bool:
        try:
            float(value)
            return True
        except Exception:
            return False
# ...existing code...
````

---

