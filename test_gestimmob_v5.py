#!/usr/bin/env python3
"""
🧪 TESTS GESTIMMOB v5.0.0 - Vérification Complète
Script de test pour valider toutes les fonctionnalités
"""

import sys
import sqlite3
import os
from pathlib import Path

def print_test_header(test_name):
    """Affiche l'en-tête d'un test"""
    print(f"\n🧪 TEST: {test_name}")
    print("-" * 50)

def test_file_structure():
    """Teste la structure des fichiers"""
    print_test_header("Structure des Fichiers")
    
    required_files = [
        "gestimmob_simple_v5.py",
        "LANCER_GESTIMMOB_FINAL_V5.py", 
        "README_GESTIMMOB_V5.md"
    ]
    
    all_good = True
    for file in required_files:
        if Path(file).exists():
            size = Path(file).stat().st_size / 1024
            print(f"✅ {file} ({size:.1f} KB)")
        else:
            print(f"[ECHEC] {file} - MANQUANT")
            all_good = False
    
    return all_good

def test_imports():
    """Teste les imports Python"""
    print_test_header("Imports Python")
    
    try:
        import sqlite3
        print("✅ sqlite3")
    except ImportError:
        print("[ECHEC] sqlite3")
        return False
    
    try:
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__}")
    except ImportError:
        print("[ECHEC] PySide6 - pip install PySide6")
        return False
    
    try:
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6.QtWidgets")
    except ImportError:
        print("[ECHEC] PySide6.QtWidgets")
        return False
    
    return True

def test_database_creation():
    """Teste la création de la base de données"""
    print_test_header("Base de Données")
    
    # Supprimer la base de test si elle existe
    test_db = "test_gestimmob.db"
    if Path(test_db).exists():
        os.remove(test_db)
    
    try:
        # Créer une base de test
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        # Table des biens
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS biens_immobiliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                marque TEXT,
                modele TEXT,
                numero_serie TEXT,
                code_barre TEXT UNIQUE,
                valeur_acquisition REAL NOT NULL,
                valeur_residuelle REAL DEFAULT 0,
                duree_amortissement INTEGER DEFAULT 5,
                date_acquisition DATE NOT NULL,
                localisation TEXT,
                secteur TEXT NOT NULL,
                etat TEXT DEFAULT 'Bon',
                responsable TEXT,
                observations TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des fournisseurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                raison_sociale TEXT NOT NULL,
                siret TEXT,
                adresse TEXT,
                contact TEXT,
                telephone TEXT,
                email TEXT,
                secteur TEXT,
                conditions_paiement TEXT DEFAULT '30 jours',
                evaluation INTEGER DEFAULT 5,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT,
                entreprise TEXT,
                adresse TEXT,
                telephone TEXT,
                email TEXT,
                statut TEXT DEFAULT 'Actif',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        print("✅ Tables créées avec succès")
        
        # Tester l'insertion de données
        cursor.execute('''
            INSERT INTO biens_immobiliers 
            (designation, marque, modele, valeur_acquisition, secteur, date_acquisition)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ("Test Ordinateur", "Dell", "Latitude", 1200.0, "Informatique", "2024-01-01"))
        
        cursor.execute('''
            INSERT INTO fournisseurs 
            (raison_sociale, secteur, evaluation)
            VALUES (?, ?, ?)
        ''', ("Test Fournisseur", "Informatique", 8))
        
        cursor.execute('''
            INSERT INTO clients 
            (nom, prenom, entreprise)
            VALUES (?, ?, ?)
        ''', ("Dupont", "Jean", "Test Entreprise"))
        
        conn.commit()
        print("✅ Données de test insérées")
        
        # Vérifier les données
        cursor.execute("SELECT COUNT(*) FROM biens_immobiliers")
        nb_biens = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM fournisseurs")
        nb_fournisseurs = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM clients")
        nb_clients = cursor.fetchone()[0]
        
        print(f"✅ Données vérifiées: {nb_biens} biens, {nb_fournisseurs} fournisseurs, {nb_clients} clients")
        
        conn.close()
        
        # Nettoyer
        os.remove(test_db)
        print("✅ Base de test nettoyée")
        
        return True
        
    except Exception as e:
        print(f"[ECHEC] Erreur base de données: {e}")
        return False

def test_application_syntax():
    """Teste la syntaxe de l'application"""
    print_test_header("Syntaxe de l'Application")
    
    try:
        # Compiler le fichier pour vérifier la syntaxe
        with open("gestimmob_simple_v5.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, "gestimmob_simple_v5.py", "exec")
        print("✅ Syntaxe Python valide")
        
        # Vérifier les imports spécifiques
        if "from PySide6.QtWidgets import" in code:
            print("✅ Imports PySide6 présents")
        else:
            print("[ECHEC] Imports PySide6 manquants")
            return False
        
        if "class GestImmobSimple(QMainWindow)" in code:
            print("✅ Classe principale définie")
        else:
            print("[ECHEC] Classe principale manquante")
            return False
        
        if "def create_dashboard_module" in code:
            print("✅ Module tableau de bord présent")
        else:
            print("[ECHEC] Module tableau de bord manquant")
            return False
        
        if "def create_properties_module" in code:
            print("✅ Module gestion des biens présent")
        else:
            print("[ECHEC] Module gestion des biens manquant")
            return False
        
        return True
        
    except SyntaxError as e:
        print(f"[ECHEC] Erreur de syntaxe: {e}")
        return False
    except Exception as e:
        print(f"[ECHEC] Erreur: {e}")
        return False

def test_configuration():
    """Teste la configuration"""
    print_test_header("Configuration")
    
    try:
        with open("gestimmob_simple_v5.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Vérifier la configuration
        if "class AppConfig:" in code:
            print("✅ Classe de configuration présente")
        else:
            print("[ECHEC] Classe de configuration manquante")
            return False
        
        if "APP_NAME = " in code:
            print("✅ Nom de l'application défini")
        else:
            print("[ECHEC] Nom de l'application manquant")
            return False
        
        if "MODULES = {" in code:
            print("✅ Configuration des modules présente")
        else:
            print("[ECHEC] Configuration des modules manquante")
            return False
        
        if "COLORS = {" in code:
            print("✅ Configuration des couleurs présente")
        else:
            print("[ECHEC] Configuration des couleurs manquante")
            return False
        
        return True
        
    except Exception as e:
        print(f"[ECHEC] Erreur: {e}")
        return False

def run_all_tests():
    """Lance tous les tests"""
    print("🧪 " + "="*60)
    print("[LANCE] TESTS GESTIMMOB v5.0.0 - VALIDATION COMPLÈTE")
    print("🎯 Vérification de toutes les fonctionnalités")
    print("="*64)
    
    tests = [
        ("Structure des Fichiers", test_file_structure),
        ("Imports Python", test_imports),
        ("Base de Données", test_database_creation),
        ("Syntaxe Application", test_application_syntax),
        ("Configuration", test_configuration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"[ECHEC] Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "="*64)
    print("[STATS] RÉSUMÉ DES TESTS")
    print("="*64)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "[ECHEC] ÉCHEC"
        print(f"{status:12} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 RÉSULTAT GLOBAL: {passed}/{total} tests réussis")
    
    if passed == total:
        print("[SUCCES] TOUS LES TESTS RÉUSSIS - APPLICATION PRÊTE !")
        print("[LANCE] Vous pouvez lancer l'application en toute sécurité")
    else:
        print("[ATTENTION]  Certains tests ont échoué - Vérifiez les erreurs ci-dessus")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🎮 Voulez-vous lancer l'application maintenant ? (O/n): ", end="")
        response = input().strip().lower()
        if response in ['', 'o', 'oui', 'y', 'yes']:
            print("\n[LANCE] Lancement de GestImmob v5.0.0...")
            os.system("python gestimmob_simple_v5.py")
    
    input("\nAppuyez sur Entrée pour quitter...")
