{"SECRET_KEY": "your_secret_key", "SESSION_TIMEOUT": 30, "LOGGING_LEVEL": "INFO", "LOGGING_FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "DATABASE_URL": "sqlite:///gestion_immob.db", "DEFAULT_DB": "immobilisations.db", "langue": "fr", "theme": "clair", "sauvegarde_auto": true, "sauvegarde_intervalle_min": 5, "synchronisation_auto": true, "synchronisation_intervalle_min": 10, "performance_mode": "optimise", "bigdata_mode": true, "ia_modele": "DeepSeek", "multi_langues": ["fr", "en", "ar", "es", "de", "zh", "ru"], "api_github": "https://api.github.com", "api_wikipedia": "https://fr.wikipedia.org/api/rest_v1/page/summary/", "derniere_version": "1.0.0", "modules_actifs": ["inventaire", "<PERSON><PERSON><PERSON><PERSON>", "recherche", "reforme", "document", "impression", "erp", "calculatrice"], "historique_max": 1000, "modele_ia_disponibles": ["DeepSeek", "OpenAI", "GoogleGemini", "<PERSON><PERSON><PERSON>", "Llama2"], "modele_ia_defaut": "DeepSeek", "cloud_sync": true, "cloud_provider": "aws", "cloud_bucket": "immob-sync", "crypto_mode": "fernet", "log_level": "INFO"}