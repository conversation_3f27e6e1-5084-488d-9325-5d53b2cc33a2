#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que les corrections de main.py fonctionnent
"""

import sys
import os

def test_main_import():
    """Test l'import du module main"""
    print("🔍 Test d'import du module main...")
    
    try:
        # Ajouter src au path
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        # Test import principal
        print("  - Import du module main...")
        import main
        print("  ✅ Module main importé avec succès")
        
        # Test ModuleManager
        print("  - Test ModuleManager...")
        manager = main.ModuleManager()
        print("  ✅ ModuleManager créé avec succès")
        
        # Test import sécurisé
        print("  - Test import sécurisé...")
        result = manager.import_module_safe('json')
        if result:
            print("  ✅ Import sécurisé fonctionne")
        else:
            print("  ❌ Import sécurisé échoué")
        
        # Test rapport
        print("  - Test rapport d'import...")
        report = manager.get_import_report()
        print(f"  📊 Modules chargés: {report['modules_loaded']}")
        print(f"  📊 Modules échoués: {report['modules_failed']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur lors de l'import: {e}")
        return False

def test_database_connection():
    """Test la connexion à la base de données optimisée"""
    print("\n🗄️ Test de la connexion base de données...")
    
    try:
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from main import DatabaseConnection
        
        # Test connexion
        print("  - Création de la connexion...")
        db = DatabaseConnection(":memory:")
        print("  ✅ Connexion créée avec succès")
        
        # Test optimisations
        print("  - Vérification des optimisations...")
        cursor = db.conn.cursor()
        
        # Vérifier quelques PRAGMA
        result = cursor.execute("PRAGMA journal_mode").fetchone()
        print(f"  📊 Journal mode: {result[0] if result else 'Unknown'}")
        
        result = cursor.execute("PRAGMA cache_size").fetchone()
        print(f"  📊 Cache size: {result[0] if result else 'Unknown'}")
        
        db.close()
        print("  ✅ Base de données optimisée et fermée")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur base de données: {e}")
        return False

def test_dependency_check():
    """Test la vérification des dépendances"""
    print("\n🔍 Test de vérification des dépendances...")
    
    try:
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        from main import check_system_dependencies
        
        print("  - Exécution de la vérification...")
        report = check_system_dependencies()
        
        print(f"  📊 Python: {report['python_version']}")
        print(f"  📊 Plateforme: {report['platform']}")
        print(f"  📊 Modules disponibles: {len(report['available_modules'])}")
        print(f"  📊 Modules manquants: {len(report['missing_modules'])}")
        
        if report['missing_modules']:
            print(f"  ⚠️ Modules manquants: {', '.join(report['missing_modules'])}")
        
        print("  ✅ Vérification des dépendances terminée")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur vérification dépendances: {e}")
        return False

def test_type_annotations():
    """Test que les annotations de type sont correctes"""
    print("\n📝 Test des annotations de type...")
    
    try:
        # Test de compilation
        import py_compile
        src_path = os.path.join(os.path.dirname(__file__), 'src', 'main.py')
        
        print("  - Compilation du fichier...")
        py_compile.compile(src_path, doraise=True)
        print("  ✅ Compilation réussie - pas d'erreurs de syntaxe")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur de compilation: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test des Corrections de main.py")
    print("=" * 40)
    
    tests = [
        ("Import du module", test_main_import),
        ("Connexion base de données", test_database_connection),
        ("Vérification dépendances", test_dependency_check),
        ("Annotations de type", test_type_annotations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n📋 Résumé des Tests:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Tous les tests sont passés avec succès!")
        return 0
    else:
        print("⚠️ Certains tests ont échoué")
        return 1

if __name__ == "__main__":
    sys.exit(main())
