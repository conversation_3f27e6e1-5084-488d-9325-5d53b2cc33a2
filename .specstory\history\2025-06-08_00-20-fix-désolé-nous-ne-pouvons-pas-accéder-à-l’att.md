<!-- Generated by SpecStory -->

# /fix Désolé... Nous ne pouvons pas accéder à l’att (2025-06-08 01:20:36)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « PageSize » pour la classe « type[QPrinter] »
  L’attribut « PageSize » est inconnu

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Remplacer l'utilisation incorrecte de `QPrinter.PageSize.A3` par la constante correcte, qui est simplement `QPrinter.A3` (et de même pour A4 et Letter). Les constantes de format de page sont des attributs directs de QPrinter, pas d'un sous-objet `PageSize`.

````python
        # ...existing code...
        if fmt == "PDF":
            # Correction : utiliser QPrinter.OutputFormat.PdfFormat
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
            file, _ = QFileDialog.getSaveFileName(self, "Enregistrer PDF", "", "PDF (*.pdf)")
            if file:
                printer.setOutputFileName(file)
        elif fmt == "A3":
            printer.setPageSize(QPrinter.A3)
        elif fmt == "A4":
            printer.setPageSize(QPrinter.A4)
        elif fmt == "Lettre":
            printer.setPageSize(QPrinter.Letter)
        elif fmt == "Paysage":
            printer.setOrientation(QPrinter.Orientation.Landscape)
        elif fmt == "Portrait":
            printer.setOrientation(QPrinter.Orientation.Portrait)
        # ...existing code...
````

---

