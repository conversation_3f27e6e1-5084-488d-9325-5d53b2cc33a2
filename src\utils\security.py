from werkzeug.security import generate_password_hash, check_password_hash
import re

def validate_username(username):
    if not re.match("^[a-zA-Z0-9_]{3,20}$", username):
        raise ValueError("Username must be 3-20 characters long and can only contain letters, numbers, and underscores.")

def validate_password(password):
    if len(password) < 8:
        raise ValueError("Password must be at least 8 characters long.")
    if not re.search("[A-Z]", password):
        raise ValueError("Password must contain at least one uppercase letter.")
    if not re.search("[a-z]", password):
        raise ValueError("Password must contain at least one lowercase letter.")
    if not re.search("[0-9]", password):
        raise ValueError("Password must contain at least one digit.")
    if not re.search("[@#$%^&+=]", password):
        raise ValueError("Password must contain at least one special character.")

def hash_password(password):
    return generate_password_hash(password)

def verify_password(stored_password, provided_password):
    return check_password_hash(stored_password, provided_password)