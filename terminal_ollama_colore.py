#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TERMINAL OLLAMA COLORÉ - MASTER COMPTA
Terminal avec couleurs, émojis et logos pour Ollama
Écriture verte principale et différentes couleurs
"""

import sys
import requests
import json
import time
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QTextEdit, QLineEdit, QGroupBox, QFrame
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QTextCharFormat, QColor, QTextCursor

class OllamaColorWorker(QThread):
    """Worker Ollama avec réponses colorées"""
    response_ready = Signal(str, str)  # message, couleur
    error_occurred = Signal(str, str)  # erreur, couleur
    
    def __init__(self, prompt, model="llama3.2"):
        super().__init__()
        self.prompt = prompt
        self.model = model
        self.ollama_url = "http://localhost:11434/api/generate"
    
    def run(self):
        """Exécute la requête Ollama avec gestion colorée"""
        try:
            payload = {
                "model": self.model,
                "prompt": self.prompt,
                "stream": False
            }
            
            response = requests.post(self.ollama_url, json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                ollama_response = result.get("response", "Pas de réponse")
                self.response_ready.emit(ollama_response, "green")
            else:
                self.error_occurred.emit(f"Erreur HTTP: {response.status_code}", "red")
                
        except requests.exceptions.ConnectionError:
            self.error_occurred.emit("❌ Ollama non accessible. Serveur arrêté", "red")
        except requests.exceptions.Timeout:
            self.error_occurred.emit("⏱️ Timeout - Ollama prend trop de temps", "orange")
        except Exception as e:
            self.error_occurred.emit(f"❌ Erreur: {str(e)}", "red")

class TerminalOllamaColore(QMainWindow):
    """Terminal Ollama avec couleurs, émojis et logos"""
    
    def __init__(self):
        super().__init__()
        self.ollama_worker = None
        self.setup_terminal_colore()
    
    def setup_terminal_colore(self):
        """Configure le terminal coloré"""
        self.setWindowTitle("🤖 TERMINAL OLLAMA COLORÉ - MASTER COMPTA")
        self.setFixedSize(1200, 800)
        
        # Style terminal noir avec accents colorés
        self.setStyleSheet("""
            QMainWindow {
                background-color: #000000;
                color: #00ff00;
            }
            QWidget {
                background-color: #000000;
                color: #00ff00;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12pt;
            }
            QTextEdit {
                background-color: #0a0a0a;
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11pt;
                line-height: 1.4;
            }
            QLineEdit {
                background-color: #1a1a1a;
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12pt;
                font-weight: bold;
            }
            QLineEdit:focus {
                border: 3px solid #00ff88;
                background-color: #2a2a2a;
            }
            QPushButton {
                background-color: #2a2a2a;
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 6px;
                padding: 10px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #3a3a3a;
                border: 2px solid #00ff88;
                color: #00ff88;
            }
            QLabel {
                background-color: #1a1a1a;
                color: #00ff00;
                border: 1px solid #00ff00;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QGroupBox {
                background-color: #0a0a0a;
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 8px;
                margin: 8px;
                padding-top: 15px;
                font-weight: bold;
                font-size: 13pt;
            }
            QGroupBox::title {
                color: #00ff00;
                background-color: #2a2a2a;
                border: 1px solid #00ff00;
                border-radius: 4px;
                padding: 5px 10px;
                margin-left: 10px;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # En-tête avec logo et titre
        self.create_header_colore(layout)
        
        # Terminal principal
        self.create_terminal_principal(layout)
        
        # Zone de saisie
        self.create_zone_saisie(layout)
        
        # Barre de statut colorée
        self.create_status_colore()
        
        # Démarrage avec message d'accueil
        self.afficher_accueil()
    
    def create_header_colore(self, layout):
        """En-tête coloré avec logos"""
        header_frame = QFrame()
        header_frame.setFixedHeight(100)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #001100, stop:0.5 #002200, stop:1 #001100);
                border: 3px solid #00ff00;
                border-radius: 10px;
                margin: 5px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Logo Ollama
        logo_ollama = QLabel("🤖")
        logo_ollama.setStyleSheet("""
            QLabel {
                font-size: 48pt;
                color: #00ff00;
                background-color: #2a2a2a;
                border: 3px solid #00ff00;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
                min-width: 80px;
                max-width: 80px;
            }
        """)
        header_layout.addWidget(logo_ollama)
        
        # Titre principal
        titre_principal = QLabel("🌟 TERMINAL OLLAMA COLORÉ 🌟\n💚 ÉCRITURE VERTE PRINCIPALE 💚")
        titre_principal.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titre_principal.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #00ff00;
                background-color: #1a1a1a;
                border: 2px solid #00ff00;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
        """)
        header_layout.addWidget(titre_principal, 1)
        
        # Logo Master Compta
        logo_master = QLabel("🏢")
        logo_master.setStyleSheet("""
            QLabel {
                font-size: 48pt;
                color: #0088ff;
                background-color: #2a2a2a;
                border: 3px solid #0088ff;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
                min-width: 80px;
                max-width: 80px;
            }
        """)
        header_layout.addWidget(logo_master)
        
        layout.addWidget(header_frame)
    
    def create_terminal_principal(self, layout):
        """Terminal principal avec couleurs"""
        terminal_group = QGroupBox("💻 TERMINAL OLLAMA - ÉCRITURE COLORÉE")
        terminal_layout = QVBoxLayout(terminal_group)
        
        # Zone d'affichage terminal
        self.terminal_display = QTextEdit()
        self.terminal_display.setMinimumHeight(400)
        self.terminal_display.setReadOnly(True)
        
        # Configuration des couleurs de texte
        self.setup_text_colors()
        
        terminal_layout.addWidget(self.terminal_display)
        layout.addWidget(terminal_group)
    
    def create_zone_saisie(self, layout):
        """Zone de saisie colorée"""
        saisie_group = QGroupBox("✍️ SAISIE COMMANDE - VERT PRINCIPAL")
        saisie_layout = QVBoxLayout(saisie_group)
        
        # Zone de saisie
        input_layout = QHBoxLayout()
        
        # Prompt coloré
        prompt_label = QLabel("🤖 OLLAMA ➤")
        prompt_label.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #00ff00;
                background-color: #2a2a2a;
                border: 2px solid #00ff00;
                border-radius: 6px;
                padding: 8px;
                margin: 3px;
                min-width: 100px;
                max-width: 100px;
            }
        """)
        input_layout.addWidget(prompt_label)
        
        # Champ de saisie
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("💬 Tapez votre message à Ollama (écriture verte)...")
        self.input_field.returnPressed.connect(self.envoyer_message)
        input_layout.addWidget(self.input_field)
        
        # Bouton envoyer
        send_btn = QPushButton("🚀 ENVOYER")
        send_btn.clicked.connect(self.envoyer_message)
        send_btn.setStyleSheet("""
            QPushButton {
                background-color: #2a2a2a;
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 6px;
                padding: 10px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #3a3a3a;
                border: 2px solid #00ff88;
                color: #00ff88;
            }
        """)
        input_layout.addWidget(send_btn)
        
        saisie_layout.addLayout(input_layout)
        
        # Boutons de commandes rapides colorées
        self.create_boutons_rapides(saisie_layout)
        
        layout.addWidget(saisie_group)
    
    def create_boutons_rapides(self, layout):
        """Boutons de commandes rapides avec couleurs"""
        boutons_layout = QHBoxLayout()
        
        commandes = [
            ("💚 Saluer", "Bonjour Ollama ! Comment allez-vous ?", "#00ff00"),
            ("🔵 Analyser", "Analysez mes données MASTER COMPTA", "#0088ff"),
            ("🟡 Calculer", "Aidez-moi avec des calculs comptables", "#ffaa00"),
            ("🟣 Optimiser", "Comment optimiser mon logiciel ?", "#aa00ff"),
            ("🔴 Aide", "Donnez-moi de l'aide sur MASTER COMPTA", "#ff0044")
        ]
        
        for titre, commande, couleur in commandes:
            btn = QPushButton(titre)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: #2a2a2a;
                    color: {couleur};
                    border: 2px solid {couleur};
                    border-radius: 6px;
                    padding: 8px;
                    font-weight: bold;
                    font-size: 10pt;
                }}
                QPushButton:hover {{
                    background-color: #3a3a3a;
                    border: 3px solid {couleur};
                }}
            """)
            btn.clicked.connect(lambda checked, cmd=commande: self.commande_rapide(cmd))
            boutons_layout.addWidget(btn)
        
        layout.addLayout(boutons_layout)
    
    def create_status_colore(self):
        """Barre de statut colorée"""
        status = self.statusBar()
        status.setStyleSheet("""
            QStatusBar {
                background-color: #1a1a1a;
                color: #00ff00;
                border-top: 2px solid #00ff00;
                padding: 5px;
                font-weight: bold;
                font-size: 11pt;
            }
        """)
        status.showMessage("🟢 Terminal Ollama Coloré - Prêt pour écriture verte")
    
    def setup_text_colors(self):
        """Configure les couleurs de texte"""
        # Formats de couleurs
        self.color_formats = {
            'green': self.create_color_format('#00ff00'),      # Vert principal
            'blue': self.create_color_format('#0088ff'),       # Bleu
            'yellow': self.create_color_format('#ffaa00'),     # Jaune
            'red': self.create_color_format('#ff0044'),        # Rouge
            'purple': self.create_color_format('#aa00ff'),     # Violet
            'orange': self.create_color_format('#ff8800'),     # Orange
            'cyan': self.create_color_format('#00ffff'),       # Cyan
            'white': self.create_color_format('#ffffff'),      # Blanc
            'gray': self.create_color_format('#888888')        # Gris
        }
    
    def create_color_format(self, color):
        """Crée un format de couleur"""
        format = QTextCharFormat()
        format.setForeground(QColor(color))
        format.setFont(QFont('Consolas', 11, QFont.Weight.Bold))
        return format

    def afficher_accueil(self):
        """Affiche le message d'accueil coloré"""
        accueil = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🤖 TERMINAL OLLAMA COLORÉ - MASTER COMPTA 🤖                ║
║                           💚 ÉCRITURE VERTE PRINCIPALE 💚                      ║
╚══════════════════════════════════════════════════════════════════════════════╝

🌟 BIENVENUE DANS LE TERMINAL OLLAMA COLORÉ ! 🌟

📅 Date/Heure: {datetime.now().strftime('%d/%m/%Y - %H:%M:%S')}
🤖 Modèle: llama3.2
💚 Couleur principale: VERT
🎨 Couleurs disponibles: Vert, Bleu, Jaune, Rouge, Violet, Orange, Cyan

═══════════════════════════════════════════════════════════════════════════════

🔥 FONCTIONNALITÉS COLORÉES:
💚 Réponses Ollama en VERT (couleur principale)
🔵 Messages système en BLEU
🟡 Avertissements en JAUNE
🔴 Erreurs en ROUGE
🟣 Commandes spéciales en VIOLET

═══════════════════════════════════════════════════════════════════════════════

✨ COMMANDES RAPIDES DISPONIBLES:
💚 Saluer Ollama
🔵 Analyser données MASTER COMPTA
🟡 Aide calculs comptables
🟣 Optimisation logiciel
🔴 Aide générale

═══════════════════════════════════════════════════════════════════════════════

🚀 Tapez votre message et appuyez sur Entrée pour commencer !
💬 L'écriture d'Ollama apparaîtra en VERT (couleur principale)

"""
        self.ajouter_texte_colore(accueil, 'green')
        self.ajouter_texte_colore("🟢 Système prêt - En attente de vos commandes...\n", 'blue')

    def ajouter_texte_colore(self, texte, couleur='green'):
        """Ajoute du texte coloré au terminal"""
        cursor = self.terminal_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)

        # Applique la couleur
        if couleur in self.color_formats:
            cursor.insertText(texte, self.color_formats[couleur])
        else:
            cursor.insertText(texte, self.color_formats['green'])  # Vert par défaut

        # Scroll automatique
        self.terminal_display.setTextCursor(cursor)
        self.terminal_display.ensureCursorVisible()

    def ajouter_ligne_separatrice(self, couleur='green'):
        """Ajoute une ligne séparatrice colorée"""
        separateur = "═" * 80 + "\n"
        self.ajouter_texte_colore(separateur, couleur)

    def ajouter_timestamp_colore(self):
        """Ajoute un timestamp coloré"""
        timestamp = f"[{datetime.now().strftime('%H:%M:%S')}] "
        self.ajouter_texte_colore(timestamp, 'gray')

    def envoyer_message(self):
        """Envoie un message à Ollama avec affichage coloré"""
        message = self.input_field.text().strip()
        if not message:
            return

        # Affichage du message utilisateur
        self.ajouter_ligne_separatrice('blue')
        self.ajouter_timestamp_colore()
        self.ajouter_texte_colore("👤 VOUS: ", 'cyan')
        self.ajouter_texte_colore(f"{message}\n", 'white')

        # Vide le champ
        self.input_field.clear()

        # Affichage en attente
        self.ajouter_timestamp_colore()
        self.ajouter_texte_colore("🤖 OLLAMA: ", 'green')
        self.ajouter_texte_colore("💭 Réflexion en cours", 'yellow')

        # Animation de points
        self.animation_points = 0
        self.timer_animation = QTimer()
        self.timer_animation.timeout.connect(self.animer_attente)
        self.timer_animation.start(500)

        # Lancement worker Ollama
        self.ollama_worker = OllamaColorWorker(message)
        self.ollama_worker.response_ready.connect(self.on_ollama_response)
        self.ollama_worker.error_occurred.connect(self.on_ollama_error)
        self.ollama_worker.start()

        # Mise à jour statut
        self.statusBar().showMessage("🟡 Ollama traite votre demande...")

    def animer_attente(self):
        """Animation de points pendant l'attente"""
        points = "." * (self.animation_points % 4)
        # Efface la ligne précédente et ajoute les nouveaux points
        cursor = self.terminal_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine, QTextCursor.MoveMode.KeepAnchor)
        cursor.removeSelectedText()

        self.ajouter_timestamp_colore()
        self.ajouter_texte_colore("🤖 OLLAMA: ", 'green')
        self.ajouter_texte_colore(f"💭 Réflexion en cours{points}", 'yellow')

        self.animation_points += 1

    def on_ollama_response(self, response, couleur):
        """Traite la réponse d'Ollama avec couleurs"""
        # Arrête l'animation
        if hasattr(self, 'timer_animation'):
            self.timer_animation.stop()

        # Efface la ligne d'attente
        cursor = self.terminal_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine, QTextCursor.MoveMode.KeepAnchor)
        cursor.removeSelectedText()

        # Affiche la réponse en VERT (couleur principale)
        self.ajouter_timestamp_colore()
        self.ajouter_texte_colore("🤖 OLLAMA: ", 'green')
        self.ajouter_texte_colore("✨ ", 'yellow')
        self.ajouter_texte_colore(f"{response}\n", 'green')  # VERT PRINCIPAL

        self.ajouter_ligne_separatrice('green')
        self.ajouter_texte_colore("🟢 Réponse terminée - Prêt pour la prochaine question\n", 'blue')

        # Mise à jour statut
        self.statusBar().showMessage("🟢 Réponse reçue - Terminal prêt")

    def on_ollama_error(self, erreur, couleur):
        """Traite les erreurs avec couleurs"""
        # Arrête l'animation
        if hasattr(self, 'timer_animation'):
            self.timer_animation.stop()

        # Efface la ligne d'attente
        cursor = self.terminal_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine, QTextCursor.MoveMode.KeepAnchor)
        cursor.removeSelectedText()

        # Affiche l'erreur en ROUGE
        self.ajouter_timestamp_colore()
        self.ajouter_texte_colore("❌ ERREUR: ", 'red')
        self.ajouter_texte_colore(f"{erreur}\n", 'red')

        self.ajouter_ligne_separatrice('red')
        self.ajouter_texte_colore("🔴 Erreur survenue - Vérifiez Ollama\n", 'orange')

        # Mise à jour statut
        self.statusBar().showMessage("🔴 Erreur Ollama - Vérifiez la connexion")

    def commande_rapide(self, commande):
        """Exécute une commande rapide"""
        self.input_field.setText(commande)
        self.envoyer_message()


def main():
    """Lance le terminal Ollama coloré"""
    app = QApplication(sys.argv)

    # Configuration application
    app.setApplicationName("Terminal Ollama Coloré")
    app.setApplicationVersion("1.0")

    # Lancement terminal
    terminal = TerminalOllamaColore()
    terminal.show()

    print("🤖 Terminal Ollama Coloré lancé !")
    print("💚 Écriture verte principale activée")
    print("🌈 Couleurs multiples disponibles")
    print("📝 Écriture lisible avec émojis et logos")

    return app.exec()


if __name__ == "__main__":
    main()
