from PySide6.QtWidgets import <PERSON><PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QTextEdit, QLabel, QMessageBox
from typing import List

class CalculatriceWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Calculatrice IA Multifonction")
        self.setStyleSheet("""
            QWidget { background-color: #e3f2fd; }
            QPushButton { background-color: #1976d2; color: white; border-radius: 6px; font-weight: bold; }
            QPushButton:hover { background-color: #1565c0; }
            QLineEdit, QTextEdit { background-color: #fff; }
        """)
        layout = QVBoxLayout(self)
        self.display = QLineEdit()
        self.display.setReadOnly(True)
        layout.addWidget(self.display)
        grid = QHBoxLayout()
        self.buttons: List[QPushButton] = []
        for label in ["7", "8", "9", "/", "4", "5", "6", "*", "1", "2", "3", "-", "0", ".", "=", "+"]:
            btn = QPushButton(label)
            btn.clicked.connect(self.on_button_clicked)
            self.buttons.append(btn)
            grid.addWidget(btn)
        layout.addLayout(grid)
        self.history = QTextEdit()
        self.history.setReadOnly(True)
        layout.addWidget(QLabel("Historique des calculs (IA DeepSeek):"))
        layout.addWidget(self.history)
        self.setLayout(layout)
        self.calculs: List[str] = []

    def on_button_clicked(self):
        sender = self.sender()
        text = ""
        # Correction : vérifier que le sender est bien un QPushButton
        if isinstance(sender, QPushButton):
            text = sender.text()
        else:
            return
        if text == "=":
            try:
                expr = self.display.text()
                # Détection et calcul de l'amortissement linéaire ou dégressif
                if expr.lower().startswith("amortlin(") and expr.endswith(")"):
                    # Syntaxe : amortlin(valeur, duree_mois)
                    params = expr[9:-1].split(",")
                    if len(params) == 2:
                        valeur = float(params[0])
                        duree = int(params[1])
                        result = valeur / duree
                        msg = f"Amortissement linéaire mensuel : {result:.2f}"
                        self.calculs.append(expr + " = " + msg)
                        self.display.setText(str(result))
                        self._update_history()
                        return
                elif expr.lower().startswith("amortdeg(") and expr.endswith(")"):
                    # Syntaxe : amortdeg(valeur, taux, duree_mois)
                    params = expr[9:-1].split(",")
                    if len(params) == 3:
                        valeur = float(params[0])
                        taux = float(params[1]) / 100
                        duree = int(params[2])
                        montant = valeur
                        details: list[str] = []  # Correction : annotation explicite du type
                        amort = 0  # Initialisation pour éviter l'indépendance
                        for mois in range(1, duree + 1):
                            amort = montant * taux
                            montant -= amort
                            details.append(f"Mois {mois}: {amort:.2f}")
                        msg = " | ".join(details)
                        self.calculs.append(expr + " = " + msg)
                        self.display.setText(str(amort))
                        self._update_history()
                        return
                # Calcul normal
                result = str(eval(expr))
                self.calculs.append(expr + " = " + result)
                self.display.setText(result)
                self._update_history()
            except Exception:
                QMessageBox.warning(self, "Erreur", "Calcul invalide.")
        else:
            self.display.setText(self.display.text() + text)

    def _update_history(self):
        # Simule une analyse IA gratuite (DeepSeek)
        self.history.setPlainText("\n".join(self.calculs) + "\n\nAnalyse IA : " + self._analyse_ia())

    def _analyse_ia(self):
        # Placeholder DeepSeek : ici, on pourrait appeler une API IA gratuite
        if not self.calculs:
            return "Aucun calcul à analyser."
        last = self.calculs[-1]
        if "/" in last and "= 0" in last:
            return "Attention : division par zéro détectée."
        if "*" in last or "/" in last:
            return "Opération complexe détectée."
        return "Calcul simple."

    # Paramètres et configuration avancés pour la calculatrice et le logiciel
    def set_config(self, config: dict[str, object]):
        """
        Appliquer une configuration avancée (ex : langue, thème, précision, IA, etc.)
        """
        self.config = config
        # Exemple : changer la langue de l'interface
        if config.get("langue") == "en":
            self.setWindowTitle("AI Multifunction Calculator")
        # Exemple : changer le thème
        if config.get("theme") == "dark":
            self.setStyleSheet("""
                QWidget { background-color: #222; color: #fff; }
                QPushButton { background-color: #1976d2; color: white; }
                QLineEdit, QTextEdit { background-color: #333; color: #fff; }
            """)
        # ...autres paramètres...

    def get_config(self):
        """
        Retourne la configuration courante.
        """
        return getattr(self, "config", {})

    def synchroniser(self):
        """
        Synchronisation avec la base de données, cloud ou API (Big Data, IA, etc.)
        """
        # Placeholder : à connecter à la logique globale du logiciel
        # Peut intégrer des modèles IA, des API multilingues, etc.
        return "Synchronisation effectuée (Big Data/IA)."

    def enrichir_avec_bigdata(self, requete: str):
        """
        Exemple d'enrichissement avec des données externes (Big Data, API, modèles IA multilingues)
        """
        # Placeholder : ici, on pourrait interroger une API, un modèle IA, ou une base multilingue
        # Pour la démo, retourne une valeur simulée
        if requete.lower() == "amortissement":
            return "Méthodes : linéaire, dégressif, US GAAP, IFRS, etc. (multilingue, multirègles)"
        return "Aucune donnée enrichie trouvée."

    def set_language(self, lang_code: str):
        """
        Changer la langue de l'interface (ex : 'fr', 'en', 'ar', etc.)
        """
        # Peut être enrichi avec des fichiers de traduction ou des modèles IA multilingues
        if lang_code == "en":
            self.setWindowTitle("AI Multifunction Calculator")
        elif lang_code == "ar":
            self.setWindowTitle("حاسبة الذكاء الاصطناعي متعددة الوظائف")
        else:
            self.setWindowTitle("Calculatrice IA Multifonction")
