<!-- Generated by SpecStory -->

# /fix Le type de paramètre « event » est inconnu, L (2025-06-08 11:11:53)

_**User**_

/fix Le type de paramètre « event » est inconnu, L'annotation de type est manquante pour le paramètre "event"

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Ajouter une annotation de type pour le paramètre `event` dans la méthode `closeEvent`. Le type correct est `QCloseEvent` provenant de `PySide6.QtGui`. Il faut donc aussi l'importer.

````python
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox
)
from PySide6.QtCore import QTimer
from PySide6.QtGui import QColor, QCloseEvent  # Ajout de QCloseEvent
import logging

# ...existing code...

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarde_auto()
        event.accept()
# ...existing code...
````

---

