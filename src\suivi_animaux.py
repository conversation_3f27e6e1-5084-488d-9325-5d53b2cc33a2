from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QFileDialog
)
from PySide6.QtCore import QTimer
from PySide6.QtGui import QColor

class SuiviAnimauxWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Suivi des animaux")
        self.resize(1400, 800)
        layout = QVBoxLayout(self)
        self.table = QTableWidget(0, 35)
        self.table.setHorizontalHeaderLabels([
            "ID", "Nom", "Espèce", "Race", "Sexe", "Date naissance", "Valeur", "Statut",
            "Dernier contrôle médical", "Observations médicales", "Localisation actuelle",
            "Déplacements", "Pays", "Parc", "Photo", "Observation",
            "État santé", "Vé<PERSON>rinaire", "Vaccins", "Médicaments", "Documents médicaux",
            "Valeur comptable", "Amortissement", "Cause mortalité/anomalie",
            "Consommation mensuelle", "Consommation annuelle", "Unité consommation",
            "Durée de vie", "Régime", "Poids", "Infos internet",
            "Plateforme de partage", "Diagnostic partagé", "Parc d'origine", "Pays d'origine"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter animal")
        self.edit_btn = QPushButton("Modifier")
        self.delete_btn = QPushButton("Supprimer")
        self.export_btn = QPushButton("Exporter Excel")
        self.import_btn = QPushButton("Importer infos internet")
        self.sync_btn = QPushButton("Synchroniser")
        self.save_btn = QPushButton("Sauvegarder")
        self.refresh_btn = QPushButton("Actualiser")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.export_btn)
        btn_layout.addWidget(self.import_btn)
        btn_layout.addWidget(self.sync_btn)
        btn_layout.addWidget(self.save_btn)
        btn_layout.addWidget(self.refresh_btn)
        layout.addLayout(btn_layout)
        self.add_btn.clicked.connect(self.ajouter_animal)
        self.edit_btn.clicked.connect(self.modifier_animal)
        self.delete_btn.clicked.connect(self.supprimer_animal)
        self.export_btn.clicked.connect(self.exporter_excel)
        self.import_btn.clicked.connect(self.importer_infos_internet)
        self.sync_btn.clicked.connect(self.synchroniser)
        self.save_btn.clicked.connect(self.sauvegarder)
        self.refresh_btn.clicked.connect(self.actualiser)
        self.changements_detectes = False
        self.timer_sync = QTimer()
        self.timer_sync.timeout.connect(self.sauvegarde_auto)
        self.timer_sync.start(180000)  # 3 min

    def ajouter_animal(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter animal")
        form = QFormLayout()
        nom = QLineEdit()
        espece = QComboBox(); espece.addItems(["Bovin", "Ovin", "Caprin", "Équin", "Canin", "Félin", "Autre"])
        race = QLineEdit()
        sexe = QComboBox(); sexe.addItems(["Mâle", "Femelle"])
        date_naissance = QLineEdit()
        valeur = QLineEdit()
        statut = QComboBox(); statut.addItems(["Vivant", "Vendu", "Décédé", "Transféré"])
        dernier_controle = QLineEdit()
        obs_medicales = QLineEdit()
        localisation = QLineEdit()
        deplacements = QLineEdit()
        pays = QComboBox(); pays.addItems(["France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "Autre"])
        parc = QLineEdit()
        photo = QLineEdit()
        photo_btn = QPushButton("Choisir photo")
        observation = QLineEdit()
        etat_sante = QComboBox(); etat_sante.addItems([
            "Excellent", "Bon", "Moyen", "Mauvais", "Critique"
        ])
        veterinaire = QLineEdit()
        vaccins = QLineEdit()
        medicaments = QLineEdit()
        documents_medicaux = QLineEdit()
        doc_btn = QPushButton("Ajouter document médical")
        valeur_comptable = QLineEdit()
        amortissement = QLineEdit()
        cause_mortalite = QLineEdit()
        consommation_mensuelle = QLineEdit()
        consommation_annuelle = QLineEdit()
        unite_conso = QComboBox(); unite_conso.addItems([
            "kg", "litre", "pièce", "autre"
        ])
        duree_vie = QLineEdit()
        regime = QComboBox(); regime.addItems(["Herbivore", "Carnivore", "Omnivore", "Autre"])
        poids = QLineEdit()
        infos_internet = QLineEdit()
        # Critères enrichis pour le partage et la collaboration mondiale
        plateforme_partage = QComboBox(); plateforme_partage.addItems([
            "Aucune", "VetGlobal", "WorldVetNet", "FAO", "OIE", "Plateforme locale", "Autre"
        ])
        diagnostic_partage = QLineEdit()
        parc_origine = QLineEdit()
        pays_origine = QComboBox(); pays_origine.addItems([
            "France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "États-Unis", "Brésil", "Chine", "Autre"
        ])
        def choisir_photo():
            file, _ = QFileDialog.getOpenFileName(dialog, "Choisir une photo", "", "Images (*.png *.jpg *.jpeg)")
            if file:
                photo.setText(file)
        def ajouter_document():
            file, _ = QFileDialog.getOpenFileName(dialog, "Ajouter document médical", "", "PDF (*.pdf);;Images (*.png *.jpg *.jpeg);;Tous les fichiers (*)")
            if file:
                if documents_medicaux.text():
                    documents_medicaux.setText(documents_medicaux.text() + "; " + file)
                else:
                    documents_medicaux.setText(file)
        photo_btn.clicked.connect(choisir_photo)
        doc_btn.clicked.connect(ajouter_document)
        form.addRow("Nom:", nom)
        form.addRow("Espèce:", espece)
        form.addRow("Race:", race)
        form.addRow("Sexe:", sexe)
        form.addRow("Date naissance:", date_naissance)
        form.addRow("Valeur:", valeur)
        form.addRow("Statut:", statut)
        form.addRow("Dernier contrôle médical:", dernier_controle)
        form.addRow("Observations médicales:", obs_medicales)
        form.addRow("Localisation actuelle:", localisation)
        form.addRow("Déplacements:", deplacements)
        form.addRow("Pays:", pays)
        form.addRow("Parc:", parc)
        form.addRow("Photo:", photo)
        form.addRow("", photo_btn)
        form.addRow("Observation:", observation)
        form.addRow("État de santé:", etat_sante)
        form.addRow("Vétérinaire référent:", veterinaire)
        form.addRow("Vaccins:", vaccins)
        form.addRow("Médicaments:", medicaments)
        form.addRow("Documents médicaux:", documents_medicaux)
        form.addRow("", doc_btn)
        form.addRow("Valeur comptable:", valeur_comptable)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Cause mortalité/anomalie:", cause_mortalite)
        form.addRow("Consommation mensuelle:", consommation_mensuelle)
        form.addRow("Consommation annuelle:", consommation_annuelle)
        form.addRow("Unité consommation:", unite_conso)
        form.addRow("Durée de vie (années):", duree_vie)
        form.addRow("Régime:", regime)
        form.addRow("Poids (kg):", poids)
        form.addRow("Infos internet:", infos_internet)
        form.addRow("Plateforme de partage:", plateforme_partage)
        form.addRow("Diagnostic partagé:", diagnostic_partage)
        form.addRow("Parc d'origine:", parc_origine)
        form.addRow("Pays d'origine:", pays_origine)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
            self.table.setItem(row, 1, QTableWidgetItem(nom.text()))
            self.table.setItem(row, 2, QTableWidgetItem(espece.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(race.text()))
            self.table.setItem(row, 4, QTableWidgetItem(sexe.currentText()))
            self.table.setItem(row, 5, QTableWidgetItem(date_naissance.text()))
            self.table.setItem(row, 6, QTableWidgetItem(valeur.text()))
            self.table.setItem(row, 7, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 8, QTableWidgetItem(dernier_controle.text()))
            self.table.setItem(row, 9, QTableWidgetItem(obs_medicales.text()))
            self.table.setItem(row, 10, QTableWidgetItem(localisation.text()))
            self.table.setItem(row, 11, QTableWidgetItem(deplacements.text()))
            self.table.setItem(row, 12, QTableWidgetItem(pays.currentText()))
            self.table.setItem(row, 13, QTableWidgetItem(parc.text()))
            self.table.setItem(row, 14, QTableWidgetItem(photo.text()))
            self.table.setItem(row, 15, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 16, QTableWidgetItem(etat_sante.currentText()))
            self.table.setItem(row, 17, QTableWidgetItem(veterinaire.text()))
            self.table.setItem(row, 18, QTableWidgetItem(vaccins.text()))
            self.table.setItem(row, 19, QTableWidgetItem(medicaments.text()))
            self.table.setItem(row, 20, QTableWidgetItem(documents_medicaux.text()))
            self.table.setItem(row, 21, QTableWidgetItem(valeur_comptable.text()))
            self.table.setItem(row, 22, QTableWidgetItem(amortissement.text()))
            self.table.setItem(row, 23, QTableWidgetItem(cause_mortalite.text()))
            self.table.setItem(row, 24, QTableWidgetItem(consommation_mensuelle.text()))
            self.table.setItem(row, 25, QTableWidgetItem(consommation_annuelle.text()))
            self.table.setItem(row, 26, QTableWidgetItem(unite_conso.currentText()))
            self.table.setItem(row, 27, QTableWidgetItem(duree_vie.text()))
            self.table.setItem(row, 28, QTableWidgetItem(regime.currentText()))
            self.table.setItem(row, 29, QTableWidgetItem(poids.text()))
            self.table.setItem(row, 30, QTableWidgetItem(infos_internet.text()))
            self.table.setItem(row, 31, QTableWidgetItem(plateforme_partage.currentText()))
            self.table.setItem(row, 32, QTableWidgetItem(diagnostic_partage.text()))
            self.table.setItem(row, 33, QTableWidgetItem(parc_origine.text()))
            self.table.setItem(row, 34, QTableWidgetItem(pays_origine.currentText()))
            self.changements_detectes = True

    def modifier_animal(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier animal")
        form = QFormLayout()
        def safe_text(item):
            return item.text() if item else ""
        nom = QLineEdit(safe_text(self.table.item(row, 1)))
        espece = QComboBox(); espece.addItems(["Bovin", "Ovin", "Caprin", "Équin", "Canin", "Félin", "Autre"])
        espece.setCurrentText(safe_text(self.table.item(row, 2)))
        race = QLineEdit(safe_text(self.table.item(row, 3)))
        sexe = QComboBox(); sexe.addItems(["Mâle", "Femelle"])
        sexe.setCurrentText(safe_text(self.table.item(row, 4)))
        date_naissance = QLineEdit(safe_text(self.table.item(row, 5)))
        valeur = QLineEdit(safe_text(self.table.item(row, 6)))
        statut = QComboBox(); statut.addItems(["Vivant", "Vendu", "Décédé", "Transféré"])
        statut.setCurrentText(safe_text(self.table.item(row, 7)))
        dernier_controle = QLineEdit(safe_text(self.table.item(row, 8)))
        obs_medicales = QLineEdit(safe_text(self.table.item(row, 9)))
        localisation = QLineEdit(safe_text(self.table.item(row, 10)))
        deplacements = QLineEdit(safe_text(self.table.item(row, 11)))
        pays = QComboBox(); pays.addItems(["France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "Autre"])
        pays.setCurrentText(safe_text(self.table.item(row, 12)))
        parc = QLineEdit(safe_text(self.table.item(row, 13)))
        photo = QLineEdit(safe_text(self.table.item(row, 14)))
        photo_btn = QPushButton("Choisir photo")
        observation = QLineEdit(safe_text(self.table.item(row, 15)))
        etat_sante = QComboBox(); etat_sante.addItems([
            "Excellent", "Bon", "Moyen", "Mauvais", "Critique"
        ])
        etat_sante.setCurrentText(safe_text(self.table.item(row, 16)))
        veterinaire = QLineEdit(safe_text(self.table.item(row, 17)))
        vaccins = QLineEdit(safe_text(self.table.item(row, 18)))
        medicaments = QLineEdit(safe_text(self.table.item(row, 19)))
        documents_medicaux = QLineEdit(safe_text(self.table.item(row, 20)))
        doc_btn = QPushButton("Ajouter document médical")
        valeur_comptable = QLineEdit(safe_text(self.table.item(row, 21)))
        amortissement = QLineEdit(safe_text(self.table.item(row, 22)))
        cause_mortalite = QLineEdit(safe_text(self.table.item(row, 23)))
        consommation_mensuelle = QLineEdit(safe_text(self.table.item(row, 24)))
        consommation_annuelle = QLineEdit(safe_text(self.table.item(row, 25)))
        unite_conso = QComboBox(); unite_conso.addItems([
            "kg", "litre", "pièce", "autre"
        ])
        unite_conso.setCurrentText(safe_text(self.table.item(row, 26)))
        duree_vie = QLineEdit(safe_text(self.table.item(row, 27)))
        regime = QComboBox(); regime.addItems(["Herbivore", "Carnivore", "Omnivore", "Autre"])
        regime.setCurrentText(safe_text(self.table.item(row, 28)))
        poids = QLineEdit(safe_text(self.table.item(row, 29)))
        infos_internet = QLineEdit(safe_text(self.table.item(row, 30)))
        # Critères enrichis pour le partage et la collaboration mondiale
        plateforme_partage = QComboBox(); plateforme_partage.addItems([
            "Aucune", "VetGlobal", "WorldVetNet", "FAO", "OIE", "Plateforme locale", "Autre"
        ])
        plateforme_partage.setCurrentText(safe_text(self.table.item(row, 31)))
        diagnostic_partage = QLineEdit(safe_text(self.table.item(row, 32)))
        parc_origine = QLineEdit(safe_text(self.table.item(row, 33)))
        pays_origine = QComboBox(); pays_origine.addItems([
            "France", "Algérie", "Maroc", "Tunisie", "Espagne", "Italie", "États-Unis", "Brésil", "Chine", "Autre"
        ])
        pays_origine.setCurrentText(safe_text(self.table.item(row, 34)))
        def choisir_photo():
            file, _ = QFileDialog.getOpenFileName(dialog, "Choisir une photo", "", "Images (*.png *.jpg *.jpeg)")
            if file:
                photo.setText(file)
        def ajouter_document():
            file, _ = QFileDialog.getOpenFileName(dialog, "Ajouter document médical", "", "PDF (*.pdf);;Images (*.png *.jpg *.jpeg);;Tous les fichiers (*)")
            if file:
                if documents_medicaux.text():
                    documents_medicaux.setText(documents_medicaux.text() + "; " + file)
                else:
                    documents_medicaux.setText(file)
        photo_btn.clicked.connect(choisir_photo)
        doc_btn.clicked.connect(ajouter_document)
        form.addRow("Nom:", nom)
        form.addRow("Espèce:", espece)
        form.addRow("Race:", race)
        form.addRow("Sexe:", sexe)
        form.addRow("Date naissance:", date_naissance)
        form.addRow("Valeur:", valeur)
        form.addRow("Statut:", statut)
        form.addRow("Dernier contrôle médical:", dernier_controle)
        form.addRow("Observations médicales:", obs_medicales)
        form.addRow("Localisation actuelle:", localisation)
        form.addRow("Déplacements:", deplacements)
        form.addRow("Pays:", pays)
        form.addRow("Parc:", parc)
        form.addRow("Photo:", photo)
        form.addRow("", photo_btn)
        form.addRow("Observation:", observation)
        form.addRow("État de santé:", etat_sante)
        form.addRow("Vétérinaire référent:", veterinaire)
        form.addRow("Vaccins:", vaccins)
        form.addRow("Médicaments:", medicaments)
        form.addRow("Documents médicaux:", documents_medicaux)
        form.addRow("", doc_btn)
        form.addRow("Valeur comptable:", valeur_comptable)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Cause mortalité/anomalie:", cause_mortalite)
        form.addRow("Consommation mensuelle:", consommation_mensuelle)
        form.addRow("Consommation annuelle:", consommation_annuelle)
        form.addRow("Unité consommation:", unite_conso)
        form.addRow("Durée de vie (années):", duree_vie)
        form.addRow("Régime:", regime)
        form.addRow("Poids (kg):", poids)
        form.addRow("Infos internet:", infos_internet)
        form.addRow("Plateforme de partage:", plateforme_partage)
        form.addRow("Diagnostic partagé:", diagnostic_partage)
        form.addRow("Parc d'origine:", parc_origine)
        form.addRow("Pays d'origine:", pays_origine)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            self.table.setItem(row, 1, QTableWidgetItem(nom.text()))
            self.table.setItem(row, 2, QTableWidgetItem(espece.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(race.text()))
            self.table.setItem(row, 4, QTableWidgetItem(sexe.currentText()))
            self.table.setItem(row, 5, QTableWidgetItem(date_naissance.text()))
            self.table.setItem(row, 6, QTableWidgetItem(valeur.text()))
            self.table.setItem(row, 7, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 8, QTableWidgetItem(dernier_controle.text()))
            self.table.setItem(row, 9, QTableWidgetItem(obs_medicales.text()))
            self.table.setItem(row, 10, QTableWidgetItem(localisation.text()))
            self.table.setItem(row, 11, QTableWidgetItem(deplacements.text()))
            self.table.setItem(row, 12, QTableWidgetItem(pays.currentText()))
            self.table.setItem(row, 13, QTableWidgetItem(parc.text()))
            self.table.setItem(row, 14, QTableWidgetItem(photo.text()))
            self.table.setItem(row, 15, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 16, QTableWidgetItem(etat_sante.currentText()))
            self.table.setItem(row, 17, QTableWidgetItem(veterinaire.text()))
            self.table.setItem(row, 18, QTableWidgetItem(vaccins.text()))
            self.table.setItem(row, 19, QTableWidgetItem(medicaments.text()))
            self.table.setItem(row, 20, QTableWidgetItem(documents_medicaux.text()))
            self.table.setItem(row, 21, QTableWidgetItem(valeur_comptable.text()))
            self.table.setItem(row, 22, QTableWidgetItem(amortissement.text()))
            self.table.setItem(row, 23, QTableWidgetItem(cause_mortalite.text()))
            self.table.setItem(row, 24, QTableWidgetItem(consommation_mensuelle.text()))
            self.table.setItem(row, 25, QTableWidgetItem(consommation_annuelle.text()))
            self.table.setItem(row, 26, QTableWidgetItem(unite_conso.currentText()))
            self.table.setItem(row, 27, QTableWidgetItem(duree_vie.text()))
            self.table.setItem(row, 28, QTableWidgetItem(regime.currentText()))
            self.table.setItem(row, 29, QTableWidgetItem(poids.text()))
            self.table.setItem(row, 30, QTableWidgetItem(infos_internet.text()))
            self.table.setItem(row, 31, QTableWidgetItem(plateforme_partage.currentText()))
            self.table.setItem(row, 32, QTableWidgetItem(diagnostic_partage.text()))
            self.table.setItem(row, 33, QTableWidgetItem(parc_origine.text()))
            self.table.setItem(row, 34, QTableWidgetItem(pays_origine.currentText()))
            self.changements_detectes = True

    def supprimer_animal(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à supprimer.")
            return
        row = selected[0].row()
        self.table.removeRow(row)
        self.changements_detectes = True

    def exporter_excel(self):
        try:
            import xlsxwriter
            file, _ = QFileDialog.getSaveFileName(self, "Exporter Excel", "", "Excel (*.xlsx)")
            if not file:
                return
            workbook = xlsxwriter.Workbook(file)
            worksheet = workbook.add_worksheet()
            for col in range(self.table.columnCount()):
                worksheet.write(0, col, self.table.horizontalHeaderItem(col).text())
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    worksheet.write(row+1, col, item.text() if item else "")
            workbook.close()
            QMessageBox.information(self, "Export Excel", "Export Excel terminé.")
        except ImportError:
            QMessageBox.warning(self, "Export Excel", "Le module xlsxwriter n'est pas installé.")
        except Exception as e:
            QMessageBox.warning(self, "Export Excel", f"Erreur lors de l'export : {e}")

    def importer_infos_internet(self):
        """
        Exemple d'importation d'informations sur une espèce animale depuis Wikipedia ou une API publique.
        Remplit automatiquement les champs durée de vie, régime, poids, etc.
        """
        try:
            from PySide6.QtWidgets import QInputDialog
            espece, ok = QInputDialog.getText(self, "Import Internet", "Entrez l'espèce animale (ex: Bovin, Ovin, Chien, Chat, etc.):")
            if not ok or not espece:
                return
            # Exemple simple : requête Wikipedia (résumé)
            import requests
            url = f"https://fr.wikipedia.org/api/rest_v1/page/summary/{espece}"
            resp = requests.get(url, timeout=10)
            if resp.status_code == 200:
                data = resp.json()
                extract = data.get("extract", "")
                # Recherche de durée de vie, poids, régime dans le résumé (simple heuristique)
                import re
                duree_vie = ""
                poids = ""
                regime = ""
                # Durée de vie
                m = re.search(r"(\d{1,2})\s*(ans|année|années)", extract)
                if m:
                    duree_vie = m.group(1)
                # Poids
                m2 = re.search(r"(\d{1,4})\s*(kg|kilogrammes|kilogramme)", extract)
                if m2:
                    poids = m2.group(1)
                # Régime
                if "herbivore" in extract.lower():
                    regime = "Herbivore"
                elif "carnivore" in extract.lower():
                    regime = "Carnivore"
                elif "omnivore" in extract.lower():
                    regime = "Omnivore"
                else:
                    regime = "Autre"
                # Ajout dans la table (ligne vide à compléter)
                row = self.table.rowCount()
                self.table.insertRow(row)
                self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
                self.table.setItem(row, 2, QTableWidgetItem(espece))
                self.table.setItem(row, 27, QTableWidgetItem(duree_vie))
                self.table.setItem(row, 28, QTableWidgetItem(regime))
                self.table.setItem(row, 29, QTableWidgetItem(poids))
                self.table.setItem(row, 30, QTableWidgetItem(extract[:300] + ("..." if len(extract) > 300 else "")))
                self.changements_detectes = True
                QMessageBox.information(self, "Import Internet", f"Informations importées pour l'espèce : {espece}")
            else:
                QMessageBox.warning(self, "Erreur Internet", f"Erreur lors de la récupération des données (code {resp.status_code})")
        except Exception as e:
            QMessageBox.warning(self, "Erreur Internet", f"Erreur lors de l'importation : {e}")

    def synchroniser(self):
        # Synchronisation avec la base de données ou API pour les animaux
        # ...logique de synchronisation...
        QMessageBox.information(self, "Synchronisation", "Synchronisation du suivi des animaux effectuée avec succès.")
        self.changements_detectes = False

    def sauvegarder(self):
        # Sauvegarde manuelle
        self.sauvegarde_auto()

    def sauvegarde_auto(self):
        if self.changements_detectes:
            # ...logique de sauvegarde (fichier, base, etc.)...
            # Par exemple : enregistrer dans la base ou un fichier local
            self.changements_detectes = False

    def actualiser(self):
        # ...logique pour recharger les données depuis la base ou API...
        QMessageBox.information(self, "Actualisation", "Actualisation du suivi des animaux effectuée.")
