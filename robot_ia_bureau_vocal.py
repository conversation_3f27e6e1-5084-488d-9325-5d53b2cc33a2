#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎤 ROBOT IA SAMNORD - APPLICATION BUREAU VOCALE
Micro pour discussion vocale + Écran terminal
Pour SamNord@110577
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import time
from datetime import datetime
import threading

class RobotIABureauVocal:
    """Application Robot IA avec micro vocal et terminal"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎤 Robot IA SamNord - Discussion Vocale + Terminal")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1e1e1e')
        
        # Configuration style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('Dark.TFrame', background='#1e1e1e')
        self.style.configure('Dark.TLabel', background='#1e1e1e', foreground='white')
        self.style.configure('Dark.TButton', background='#007acc', foreground='white')
        
        self.user = "SamNord@110577"
        self.password = "NorDine@22"
        self.micro_actif = False
        self.ecoute_vocale = False
        
        self.creer_interface()
    
    def creer_interface(self):
        """Création interface avec micro et terminal"""
        # Header principal
        header_frame = ttk.Frame(self.root, style='Dark.TFrame')
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(header_frame, text="🎤 Robot IA SamNord - Discussion Vocale + Terminal", 
                               font=('Arial', 16, 'bold'), style='Dark.TLabel')
        title_label.pack()
        
        user_label = ttk.Label(header_frame, text=f"👤 {self.user} | 🔑 {self.password}", 
                              font=('Arial', 10), style='Dark.TLabel')
        user_label.pack()
        
        # Frame principal divisé
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # PARTIE GAUCHE - CONTRÔLES MICRO
        self.creer_zone_micro(main_frame)
        
        # PARTIE DROITE - TERMINAL
        self.creer_terminal(main_frame)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("✅ Robot IA prêt | 🎤 Micro disponible | 💻 Terminal actif")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                              relief='sunken', style='Dark.TLabel')
        status_bar.pack(side='bottom', fill='x')
    
    def creer_zone_micro(self, parent):
        """Zone contrôles micro vocal"""
        # Frame gauche pour micro
        micro_frame = ttk.Frame(parent, style='Dark.TFrame')
        micro_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # Header micro
        ttk.Label(micro_frame, text="🎤 DISCUSSION VOCALE", 
                 font=('Arial', 14, 'bold'), style='Dark.TLabel').pack(pady=10)
        
        # Status micro
        self.micro_status_var = tk.StringVar()
        self.micro_status_var.set("🎤 Micro: OFF")
        self.micro_status_label = ttk.Label(micro_frame, textvariable=self.micro_status_var, 
                                           font=('Arial', 12), style='Dark.TLabel')
        self.micro_status_label.pack(pady=5)
        
        # Bouton micro principal
        self.btn_micro = tk.Button(micro_frame, text="🎤\nACTIVER\nMICRO", 
                                  bg='#007acc', fg='white', font=('Arial', 14, 'bold'),
                                  width=12, height=4, command=self.toggle_micro)
        self.btn_micro.pack(pady=20)
        
        # Indicateur écoute
        self.ecoute_frame = ttk.Frame(micro_frame, style='Dark.TFrame')
        self.ecoute_frame.pack(pady=10)
        
        self.ecoute_label = ttk.Label(self.ecoute_frame, text="🔴 En attente", 
                                     font=('Arial', 11), style='Dark.TLabel')
        self.ecoute_label.pack()
        
        # Boutons contrôle
        controls_frame = ttk.Frame(micro_frame, style='Dark.TFrame')
        controls_frame.pack(pady=20)
        
        tk.Button(controls_frame, text="🔊 Test Audio", bg='#00aa00', fg='white',
                 font=('Arial', 10), width=12, command=self.test_audio).pack(pady=5)
        
        tk.Button(controls_frame, text="🎵 Synthèse", bg='#aa00aa', fg='white',
                 font=('Arial', 10), width=12, command=self.test_synthese).pack(pady=5)
        
        # Instructions
        instructions_frame = ttk.Frame(micro_frame, style='Dark.TFrame')
        instructions_frame.pack(pady=20, fill='x')
        
        ttk.Label(instructions_frame, text="📋 INSTRUCTIONS:", 
                 font=('Arial', 10, 'bold'), style='Dark.TLabel').pack(anchor='w')
        
        instructions = [
            "1. Cliquez 'ACTIVER MICRO'",
            "2. Parlez au Robot IA",
            "3. Réponses dans le terminal",
            "4. Discussion vocale continue"
        ]
        
        for instruction in instructions:
            ttk.Label(instructions_frame, text=instruction, 
                     font=('Arial', 9), style='Dark.TLabel').pack(anchor='w', padx=10)
    
    def creer_terminal(self, parent):
        """Terminal pour affichage réponses"""
        # Frame droite pour terminal
        terminal_frame = ttk.Frame(parent, style='Dark.TFrame')
        terminal_frame.pack(side='right', fill='both', expand=True)
        
        # Header terminal
        terminal_header = ttk.Frame(terminal_frame, style='Dark.TFrame')
        terminal_header.pack(fill='x', pady=(0, 5))
        
        ttk.Label(terminal_header, text="💻 TERMINAL ROBOT IA", 
                 font=('Arial', 14, 'bold'), style='Dark.TLabel').pack(side='left')
        
        # Boutons terminal
        tk.Button(terminal_header, text="🗑️ Effacer", bg='#ff4444', fg='white',
                 font=('Arial', 9), command=self.effacer_terminal).pack(side='right', padx=5)
        
        # Zone terminal
        self.terminal = scrolledtext.ScrolledText(terminal_frame, bg='#0c0c0c', fg='#00ff00', 
                                                 font=('Consolas', 11), insertbackground='#00ff00')
        self.terminal.pack(fill='both', expand=True)
        
        # Configuration couleurs terminal
        self.terminal.tag_configure("system", foreground="#ffaa00", font=('Consolas', 10, 'bold'))
        self.terminal.tag_configure("user_vocal", foreground="#00d4ff", font=('Consolas', 11, 'bold'))
        self.terminal.tag_configure("robot", foreground="#00ff88", font=('Consolas', 11))
        self.terminal.tag_configure("error", foreground="#ff4444", font=('Consolas', 10))
        
        # Messages initiaux terminal
        self.ajouter_terminal_system("=" * 60)
        self.ajouter_terminal_system("ROBOT IA SAMNORD - TERMINAL DE DISCUSSION VOCALE")
        self.ajouter_terminal_system("=" * 60)
        self.ajouter_terminal_system(f"Utilisateur: {self.user}")
        self.ajouter_terminal_system(f"Session: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        self.ajouter_terminal_system("=" * 60)
        self.ajouter_terminal_robot("🤖 Robot IA: Bonjour SamNord@110577 !")
        self.ajouter_terminal_robot("🤖 Robot IA: Activez le micro pour commencer la discussion vocale.")
        self.ajouter_terminal_robot("🤖 Robot IA: Je vous écouterai et répondrai ici dans le terminal.")
        self.ajouter_terminal_system("")
        
        # Zone saisie texte (optionnelle)
        input_frame = ttk.Frame(terminal_frame, style='Dark.TFrame')
        input_frame.pack(fill='x', pady=5)
        
        ttk.Label(input_frame, text="💬 Texte:", style='Dark.TLabel').pack(side='left', padx=(0, 5))
        
        self.text_entry = tk.Entry(input_frame, bg='#2d2d30', fg='white', 
                                  font=('Arial', 10), insertbackground='white')
        self.text_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        self.text_entry.bind('<Return>', self.envoyer_texte)
        
        tk.Button(input_frame, text="📤", bg='#007acc', fg='white',
                 font=('Arial', 10), command=self.envoyer_texte).pack(side='right')
    
    def toggle_micro(self):
        """Activer/désactiver micro"""
        self.micro_actif = not self.micro_actif
        
        if self.micro_actif:
            self.btn_micro.config(text="🎤\nMICRO\nON", bg='#00aa00')
            self.micro_status_var.set("🎤 Micro: ON - Écoute active")
            self.ecoute_label.config(text="🟢 Écoute en cours...")
            self.status_var.set("✅ Micro activé | 🎤 Écoute vocale active | 💻 Terminal prêt")
            
            self.ajouter_terminal_system("🎤 MICRO ACTIVÉ - Écoute vocale démarrée")
            self.ajouter_terminal_robot("🤖 Robot IA: Je vous écoute ! Parlez-moi.")
            
            # Démarrer simulation écoute
            self.demarrer_ecoute_vocale()
            
        else:
            self.btn_micro.config(text="🎤\nACTIVER\nMICRO", bg='#007acc')
            self.micro_status_var.set("🎤 Micro: OFF")
            self.ecoute_label.config(text="🔴 En attente")
            self.status_var.set("✅ Robot IA prêt | 🎤 Micro disponible | 💻 Terminal actif")
            
            self.ajouter_terminal_system("🎤 MICRO DÉSACTIVÉ")
            self.ecoute_vocale = False
    
    def demarrer_ecoute_vocale(self):
        """Démarrer écoute vocale simulée"""
        self.ecoute_vocale = True
        
        def ecoute_thread():
            while self.ecoute_vocale and self.micro_actif:
                # Simulation détection vocale
                time.sleep(3)
                if self.ecoute_vocale:
                    self.simuler_detection_vocale()
        
        threading.Thread(target=ecoute_thread, daemon=True).start()
    
    def simuler_detection_vocale(self):
        """Simulation détection vocale"""
        messages_simules = [
            "Bonjour Robot IA",
            "Comment ça va ?",
            "Quels sont tes modules ?",
            "Active le WiFi",
            "Lance la cybersécurité",
            "Status du système",
            "Merci Robot IA"
        ]
        
        import random
        message_vocal = random.choice(messages_simules)
        
        # Afficher dans terminal
        self.ajouter_terminal_vocal(f"🎤 Vous avez dit: \"{message_vocal}\"")
        
        # Générer réponse
        reponse = self.generer_reponse_vocale(message_vocal)
        self.root.after(1000, lambda: self.ajouter_terminal_robot(f"🤖 Robot IA: {reponse}"))
    
    def generer_reponse_vocale(self, message):
        """Générer réponse vocale"""
        msg_lower = message.lower()
        
        if 'bonjour' in msg_lower or 'salut' in msg_lower:
            return "Bonjour SamNord@110577 ! Ravi de vous entendre !"
        elif 'comment' in msg_lower and 'va' in msg_lower:
            return "Je vais parfaitement bien ! Tous mes systèmes sont opérationnels."
        elif 'modules' in msg_lower:
            return "J'ai 57 modules disponibles : Intelligence IA, WiFi, Bluetooth, Cybersécurité, etc."
        elif 'wifi' in msg_lower:
            return "Module WiFi activé ! Scan des réseaux en cours..."
        elif 'cyber' in msg_lower or 'sécurité' in msg_lower:
            return "Cybersécurité activée ! Protection impénétrable en place."
        elif 'status' in msg_lower:
            return "Status : Tous systèmes opérationnels, performance 100%."
        elif 'merci' in msg_lower:
            return "De rien SamNord@110577 ! C'est un plaisir de vous aider."
        else:
            return "Intéressant ! Comment puis-je vous aider avec mes 57 modules ?"
    
    def test_audio(self):
        """Test audio"""
        self.ajouter_terminal_system("🔊 TEST AUDIO - Vérification système...")
        messagebox.showinfo("Test Audio", "🔊 Test audio réussi !\nSystème audio opérationnel.")
        self.ajouter_terminal_system("✅ Test audio réussi")
    
    def test_synthese(self):
        """Test synthèse vocale"""
        self.ajouter_terminal_system("🎵 TEST SYNTHÈSE VOCALE...")
        self.ajouter_terminal_robot("🤖 Robot IA: Test synthèse vocale - Bonjour SamNord@110577 !")
        messagebox.showinfo("Synthèse", "🎵 Synthèse vocale testée !")
    
    def envoyer_texte(self, event=None):
        """Envoyer message texte"""
        message = self.text_entry.get().strip()
        if message:
            self.ajouter_terminal_vocal(f"💬 Texte: \"{message}\"")
            self.text_entry.delete(0, tk.END)
            
            reponse = self.generer_reponse_vocale(message)
            self.root.after(500, lambda: self.ajouter_terminal_robot(f"🤖 Robot IA: {reponse}"))
    
    def effacer_terminal(self):
        """Effacer terminal"""
        self.terminal.delete(1.0, tk.END)
        self.ajouter_terminal_system("🗑️ Terminal effacé")
    
    def ajouter_terminal_system(self, message):
        """Ajouter message système au terminal"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.terminal.insert(tk.END, f"[{timestamp}] {message}\n", "system")
        self.terminal.see(tk.END)
    
    def ajouter_terminal_vocal(self, message):
        """Ajouter message vocal au terminal"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.terminal.insert(tk.END, f"[{timestamp}] {message}\n", "user_vocal")
        self.terminal.see(tk.END)
    
    def ajouter_terminal_robot(self, message):
        """Ajouter réponse robot au terminal"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.terminal.insert(tk.END, f"[{timestamp}] {message}\n", "robot")
        self.terminal.see(tk.END)
    
    def lancer(self):
        """Lancer application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    app = RobotIABureauVocal()
    app.lancer()

if __name__ == "__main__":
    main()
