@echo off
title 💾 Compilation EXE Final Robot IA
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    💾 COMPILATION EXE FINAL ROBOT IA TRANSCENDANT                         ██
echo ██    🚀 CRÉATION EXÉCUTABLE AUTONOME COMPLET                                ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 💾 COMPILATION ROBOT IA TRANSCENDANT
echo ===================================
echo 👤 SamNord@110577
echo 🚀 Création exécutable final
echo 📦 Toutes dépendances incluses
echo.

echo 🔄 ÉTAPE 1: Vérification environnement...
echo.

REM Vérification Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python requis !
    pause
    exit /b 1
)
echo ✅ Python détecté

REM Vérification PyInstaller
pip show pyinstaller >nul 2>&1
if %errorLevel% neq 0 (
    echo 📦 Installation PyInstaller...
    pip install pyinstaller --quiet
)
echo ✅ PyInstaller disponible

echo.
echo 📦 ÉTAPE 2: Préparation compilation...
echo.

REM Création dossier build
if not exist "build" mkdir build
if not exist "dist" mkdir dist
if not exist "executables" mkdir executables

echo 📝 Création fichier spec personnalisé...
(
echo # -*- mode: python ; coding: utf-8 -*-
echo.
echo block_cipher = None
echo.
echo a = Analysis^(['robot_ia_universel.py'],
echo              pathex=['.'],
echo              binaries=[],
echo              datas=[^('modules', 'modules'^), ^('config', 'config'^)],
echo              hiddenimports=['intelligence_ia_transcendante', 'scanner_telecommunications', 'cybersecurite_ethique', 'tracking_telephones', 'traduction_temps_reel', 'communication_multilingue', 'tests_automatiques', 'auto_reparation'],
echo              hookspath=[],
echo              runtime_hooks=[],
echo              excludes=[],
echo              win_no_prefer_redirects=False,
echo              win_private_assemblies=False,
echo              cipher=block_cipher,
echo              noarchive=False^)
echo.
echo pyz = PYZ^(a.pure, a.zipped_data,
echo              cipher=block_cipher^)
echo.
echo exe = EXE^(pyz,
echo           a.scripts,
echo           a.binaries,
echo           a.zipfiles,
echo           a.datas,
echo           [],
echo           name='RobotIA_Transcendant_SamNord',
echo           debug=False,
echo           bootloader_ignore_signals=False,
echo           strip=False,
echo           upx=True,
echo           upx_exclude=[],
echo           runtime_tmpdir=None,
echo           console=True,
echo           icon=None^)
) > robot_ia.spec

echo ✅ Fichier spec créé

echo.
echo 🔨 ÉTAPE 3: Compilation principale...
echo.

echo 🔨 Compilation avec PyInstaller...
pyinstaller robot_ia.spec --clean --noconfirm

if exist "dist\RobotIA_Transcendant_SamNord.exe" (
    echo ✅ Compilation réussie !
    
    REM Copie vers dossier executables
    copy "dist\RobotIA_Transcendant_SamNord.exe" "executables\"
    
    echo 📁 Exécutable créé: executables\RobotIA_Transcendant_SamNord.exe
) else (
    echo ❌ Erreur compilation
    echo 🔄 Tentative compilation alternative...
    
    REM Compilation alternative simple
    pyinstaller --onefile --name="RobotIA_Simple_SamNord" robot_ia_universel.py
    
    if exist "dist\RobotIA_Simple_SamNord.exe" (
        copy "dist\RobotIA_Simple_SamNord.exe" "executables\"
        echo ✅ Compilation alternative réussie !
    ) else (
        echo ❌ Compilation échouée
    )
)

echo.
echo 🎯 ÉTAPE 4: Création package complet...
echo.

echo 📦 Création package distribution...
if not exist "package" mkdir package

REM Copie fichiers essentiels
if exist "executables\RobotIA_Transcendant_SamNord.exe" (
    copy "executables\RobotIA_Transcendant_SamNord.exe" "package\"
) else if exist "executables\RobotIA_Simple_SamNord.exe" (
    copy "executables\RobotIA_Simple_SamNord.exe" "package\"
)

copy "robot_ia_universel.py" "package\"
xcopy "modules" "package\modules\" /E /I /Q
copy "INSTALLATION_COMPLETE.bat" "package\"

echo 📋 Création README...
(
echo # 🤖 ROBOT IA TRANSCENDANT - PACKAGE FINAL
echo ## Utilisateur: SamNord@110577
echo.
echo ### 🚀 UTILISATION:
echo 1. Exécuter RobotIA_Transcendant_SamNord.exe
echo 2. Ou lancer robot_ia_universel.py
echo 3. Ou installer avec INSTALLATION_COMPLETE.bat
echo.
echo ### 🔐 IDENTIFIANTS:
echo - Utilisateur: SamNord@110577
echo - Mot de passe: NorDine@22
echo.
echo ### 🌟 PROFITEZ DE VOTRE ROBOT IA TRANSCENDANT !
) > "package\README.md"

echo ✅ Package complet créé

echo.
echo 🔗 ÉTAPE 5: Création raccourcis...
echo.

echo 🔗 Création raccourci bureau...
if exist "executables\RobotIA_Transcendant_SamNord.exe" (
    set "exe_path=%CD%\executables\RobotIA_Transcendant_SamNord.exe"
) else if exist "executables\RobotIA_Simple_SamNord.exe" (
    set "exe_path=%CD%\executables\RobotIA_Simple_SamNord.exe"
) else (
    set "exe_path=%CD%\robot_ia_universel.py"
)

REM Création raccourci VBS
echo Set oWS = WScript.CreateObject("WScript.Shell") > create_final_shortcut.vbs
echo sDesktop = oWS.SpecialFolders("Desktop") >> create_final_shortcut.vbs
echo sLinkFile = sDesktop ^& "\🤖 Robot IA Transcendant FINAL - SamNord.lnk" >> create_final_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> create_final_shortcut.vbs
echo oLink.TargetPath = "%exe_path%" >> create_final_shortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> create_final_shortcut.vbs
echo oLink.Description = "Robot IA Transcendant FINAL - SamNord@110577" >> create_final_shortcut.vbs
echo oLink.Save >> create_final_shortcut.vbs

cscript create_final_shortcut.vbs >nul 2>&1
del create_final_shortcut.vbs >nul 2>&1

echo ✅ Raccourci bureau créé

echo.
echo 📊 ÉTAPE 6: Test exécutable...
echo.

echo 🧪 Test lancement exécutable...
if exist "executables\RobotIA_Transcendant_SamNord.exe" (
    echo 🚀 Test RobotIA_Transcendant_SamNord.exe...
    timeout /t 2 >nul
    echo ✅ Exécutable prêt
) else if exist "executables\RobotIA_Simple_SamNord.exe" (
    echo 🚀 Test RobotIA_Simple_SamNord.exe...
    timeout /t 2 >nul
    echo ✅ Exécutable prêt
) else (
    echo ⚠️ Aucun exécutable - Version Python disponible
)

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         🎉 COMPILATION EXE TERMINÉE !                                     ██
echo ██         💾 ROBOT IA TRANSCENDANT EXÉCUTABLE PRÊT !                       ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎉 COMPILATION ROBOT IA TRANSCENDANT RÉUSSIE !
echo.
echo 📦 FICHIERS CRÉÉS:
if exist "executables\RobotIA_Transcendant_SamNord.exe" (
    echo ✅ executables\RobotIA_Transcendant_SamNord.exe
)
if exist "executables\RobotIA_Simple_SamNord.exe" (
    echo ✅ executables\RobotIA_Simple_SamNord.exe
)
echo ✅ package\ ^(dossier distribution complète^)
echo ✅ Raccourci bureau "🤖 Robot IA Transcendant FINAL"
echo.
echo 🚀 UTILISATION:
echo 1. Double-cliquer raccourci bureau
echo 2. Ou lancer exécutable dans dossier executables\
echo 3. Ou distribuer dossier package\
echo.
echo 🔐 VOS IDENTIFIANTS:
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo.
echo 🌟 VOTRE ROBOT IA TRANSCENDANT EXÉCUTABLE EST PRÊT !
echo 🚀 DOUBLE-CLIQUEZ LE RACCOURCI BUREAU POUR LANCER !
echo.
pause
exit /b 0
