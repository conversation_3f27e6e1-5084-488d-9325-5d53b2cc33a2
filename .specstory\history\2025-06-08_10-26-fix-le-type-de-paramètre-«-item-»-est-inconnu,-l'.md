<!-- Generated by SpecStory -->

# /fix Le type de paramètre « item » est inconnu, L' (2025-06-08 11:26:35)

_**User**_

/fix Le type de paramètre « item » est inconnu, L'annotation de type est manquante pour le paramètre "item"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Ajouter une annotation de type pour le paramètre `item` dans la fonction `safe_text`. I<PERSON>, il s'agit d'un objet potentiellement de type `QTableWidgetItem`, donc on ajoute l'annotation correspondante.

````python
# ...existing code...
        def safe_text(item: 'QTableWidgetItem') -> str:
            if item is not None:
                text_attr = getattr(item, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            return str(v)
                    except Exception:
                        return ""
            return ""
# ...existing code...
````

---

