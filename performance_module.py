#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Module de performance pour GestImmob"""

import time
import psutil
import threading
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Moniteur de performance"""

    def __init__(self):
        self.metrics = {}
        self.is_monitoring = False

    def start_monitoring(self):
        """Démarre le monitoring"""
        self.is_monitoring = True
        threading.Thread(target=self._monitor_loop, daemon=True).start()
        logger.info("Monitoring de performance démarré")

    def _monitor_loop(self):
        """Boucle de monitoring"""
        while self.is_monitoring:
            self.metrics.update({
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'timestamp': time.time()
            })
            time.sleep(1)

    def get_metrics(self) -> Dict[str, Any]:
        """Retourne les métriques"""
        return self.metrics.copy()

performance_monitor = PerformanceMonitor()
