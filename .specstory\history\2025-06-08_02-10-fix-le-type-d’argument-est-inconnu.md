<!-- Generated by SpecStory -->

# /fix Le type d’argument est inconnu (2025-06-08 03:10:29)

_**User**_

/fix Le type d’argument est inconnu
  L’argument correspond au paramètre « key » dans la fonction « get »

---

_**Assistant**_

### [authentication.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/authentication.py)

Le problème vient du fait que la méthode `get_current_user` n'indique pas le type de `session_id`. Ajoutez une annotation de type explicite pour `session_id` (str) afin d'aider l'éditeur/type checker à comprendre le type attendu.

````python
# ...existing code...
    def get_current_user(self, session_id: str) -> Any:
        return self.sessions.get(session_id)
# ...existing code...
````

---

