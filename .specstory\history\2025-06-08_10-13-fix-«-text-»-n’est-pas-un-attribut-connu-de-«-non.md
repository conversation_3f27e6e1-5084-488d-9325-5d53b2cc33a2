<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:13:58)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Corriger l'export CSV pour éviter l'erreur lorsque `horizontalHeaderItem(i)` retourne `None` (par exemple, si une colonne n'a pas de titre). On utilise une expression conditionnelle pour éviter d'appeler `.text()` sur `None`.

````python
    def exporter_csv(self):
        path = "fournisseurs_export.csv"
        with open(path, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            # Correction ici : éviter d'appeler .text() sur None
            writer.writerow([
                self.table.horizontalHeaderItem(i).text() if self.table.horizontalHeaderItem(i) is not None else ""
                for i in range(self.table.columnCount())
            ])
            for row in range(self.table.rowCount()):
                writer.writerow([
                    self.table.item(row, col).text() if self.table.item(row, col) else ""
                    for col in range(self.table.columnCount())
                ])
        QMessageBox.information(self, "Export CSV", f"Export effectué : {path}")
# ...existing code...
````

---

