#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour améliorer GestImmob avec des secteurs et critères spécifiques
"""

import sqlite3
import json
from datetime import datetime

def ameliorer_base_donnees():
    """Améliore la base de données avec les nouveaux secteurs et critères"""
    
    # Connexion à la base de données
    conn = sqlite3.connect("gestimmob_final.db")
    cursor = conn.cursor()
    
    print("🔧 Amélioration de GestImmob avec des secteurs spécialisés...")
    
    # 1. Créer les nouvelles tables si elles n'existent pas
    print("📊 Création des tables pour les modules personnalisés...")
    
    # Table des secteurs personnalisés
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS secteurs_personnalises (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT UNIQUE NOT NULL,
            description TEXT,
            couleur TEXT DEFAULT '#1976d2',
            icone TEXT DEFAULT '🏢',
            criteres_specifiques TEXT,
            is_active INTEGER DEFAULT 1,
            date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des critères spécifiques par secteur
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS criteres_secteur (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            secteur_id INTEGER,
            nom_critere TEXT NOT NULL,
            type_critere TEXT NOT NULL,
            valeurs_possibles TEXT,
            obligatoire INTEGER DEFAULT 0,
            ordre_affichage INTEGER DEFAULT 0,
            FOREIGN KEY (secteur_id) REFERENCES secteurs_personnalises (id)
        )
    ''')
    
    # Table des valeurs des critères pour chaque bien
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS valeurs_criteres (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bien_id INTEGER,
            critere_id INTEGER,
            valeur TEXT,
            date_saisie TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (bien_id) REFERENCES immobilisations (id),
            FOREIGN KEY (critere_id) REFERENCES criteres_secteur (id)
        )
    ''')
    
    # 2. Améliorer la table des immobilisations
    print("🏠 Amélioration de la table des immobilisations...")
    
    # Ajouter de nouvelles colonnes si elles n'existent pas
    nouvelles_colonnes = [
        ('code_barre', 'TEXT'),
        ('etat', 'TEXT DEFAULT "Bon"'),
        ('responsable', 'TEXT'),
        ('date_acquisition', 'DATE'),
        ('duree_amortissement', 'INTEGER DEFAULT 5'),
        ('valeur_residuelle', 'REAL DEFAULT 0'),
        ('date_creation', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
        ('secteur_personnalise_id', 'INTEGER'),
        ('numero_serie', 'TEXT'),
        ('marque', 'TEXT'),
        ('modele', 'TEXT'),
        ('garantie_jusqu_au', 'DATE'),
        ('derniere_maintenance', 'DATE'),
        ('prochaine_maintenance', 'DATE')
    ]
    
    for colonne, type_sql in nouvelles_colonnes:
        try:
            cursor.execute(f'ALTER TABLE immobilisations ADD COLUMN {colonne} {type_sql}')
            print(f"✅ Colonne '{colonne}' ajoutée")
        except sqlite3.OperationalError:
            # La colonne existe déjà
            pass
    
    # 3. Insérer les secteurs prédéfinis améliorés
    print("🎯 Insertion des secteurs spécialisés...")
    
    secteurs_ameliores = [
        {
            'nom': 'Informatique & Technologies',
            'description': 'Équipements informatiques avec critères techniques avancés',
            'couleur': '#2196F3',
            'icone': '💻',
            'criteres': [
                {'nom': 'Marque', 'type': 'select', 'valeurs': ['Dell', 'HP', 'Lenovo', 'Apple', 'Asus', 'Microsoft', 'Acer', 'MSI', 'Autre'], 'obligatoire': 1},
                {'nom': 'Modèle', 'type': 'text', 'obligatoire': 1},
                {'nom': 'Numéro de série', 'type': 'text', 'obligatoire': 1},
                {'nom': 'Système d\'exploitation', 'type': 'select', 'valeurs': ['Windows 11', 'Windows 10', 'Windows Server', 'macOS Ventura', 'macOS Monterey', 'Ubuntu', 'CentOS', 'Autre'], 'obligatoire': 0},
                {'nom': 'RAM (Go)', 'type': 'select', 'valeurs': ['4', '8', '16', '32', '64', '128', 'Autre'], 'obligatoire': 0},
                {'nom': 'Stockage (Go)', 'type': 'select', 'valeurs': ['256', '512', '1000', '2000', '4000', 'Autre'], 'obligatoire': 0},
                {'nom': 'Type de stockage', 'type': 'select', 'valeurs': ['SSD', 'HDD', 'SSD + HDD', 'NVMe', 'Autre'], 'obligatoire': 0},
                {'nom': 'Processeur', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Carte graphique', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Garantie jusqu\'au', 'type': 'date', 'obligatoire': 0},
                {'nom': 'Licence Office', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Antivirus', 'type': 'text', 'obligatoire': 0},
                {'nom': 'IP fixe', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Nom réseau', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Utilisateur principal', 'type': 'text', 'obligatoire': 0}
            ]
        },
        {
            'nom': 'Véhicules & Transport',
            'description': 'Gestion complète du parc automobile avec suivi technique',
            'couleur': '#FF9800',
            'icone': '🚗',
            'criteres': [
                {'nom': 'Marque', 'type': 'select', 'valeurs': ['Renault', 'Peugeot', 'Citroën', 'Volkswagen', 'BMW', 'Mercedes', 'Audi', 'Ford', 'Toyota', 'Nissan', 'Autre'], 'obligatoire': 1},
                {'nom': 'Modèle', 'type': 'text', 'obligatoire': 1},
                {'nom': 'Immatriculation', 'type': 'text', 'obligatoire': 1},
                {'nom': 'VIN (N° châssis)', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Kilométrage actuel', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Carburant', 'type': 'select', 'valeurs': ['Essence', 'Diesel', 'Électrique', 'Hybride essence', 'Hybride diesel', 'GPL', 'GNV', 'Hydrogène'], 'obligatoire': 1},
                {'nom': 'Puissance (CV)', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Cylindrée (cm³)', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Date mise en circulation', 'type': 'date', 'obligatoire': 1},
                {'nom': 'Contrôle technique', 'type': 'date', 'obligatoire': 0},
                {'nom': 'Prochaine révision', 'type': 'date', 'obligatoire': 0},
                {'nom': 'Assurance jusqu\'au', 'type': 'date', 'obligatoire': 1},
                {'nom': 'Compagnie assurance', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Conducteur principal', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Carte grise', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Vignette Crit\'Air', 'type': 'select', 'valeurs': ['0', '1', '2', '3', '4', '5', 'Non classé'], 'obligatoire': 0}
            ]
        },
        {
            'nom': 'Mobilier & Aménagement',
            'description': 'Gestion du mobilier avec critères ergonomiques et esthétiques',
            'couleur': '#8BC34A',
            'icone': '🪑',
            'criteres': [
                {'nom': 'Type de mobilier', 'type': 'select', 'valeurs': ['Bureau', 'Chaise de bureau', 'Fauteuil', 'Table de réunion', 'Armoire', 'Étagère', 'Caisson', 'Cloison', 'Autre'], 'obligatoire': 1},
                {'nom': 'Matériau principal', 'type': 'select', 'valeurs': ['Bois massif', 'Aggloméré', 'MDF', 'Métal', 'Plastique', 'Verre', 'Tissu', 'Cuir', 'Simili cuir'], 'obligatoire': 0},
                {'nom': 'Couleur principale', 'type': 'select', 'valeurs': ['Blanc', 'Noir', 'Gris', 'Beige', 'Marron', 'Bleu', 'Rouge', 'Vert', 'Autre'], 'obligatoire': 0},
                {'nom': 'Dimensions (L×l×h cm)', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Poids (kg)', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Ergonomique', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Réglable en hauteur', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Roulettes', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'État d\'usure', 'type': 'select', 'valeurs': ['Neuf', 'Très bon', 'Bon', 'Correct', 'Usé', 'À remplacer'], 'obligatoire': 0},
                {'nom': 'Norme NF', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Assemblage requis', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Notice disponible', 'type': 'boolean', 'obligatoire': 0}
            ]
        },
        {
            'nom': 'Équipements Industriels',
            'description': 'Machines et équipements industriels avec suivi maintenance',
            'couleur': '#F44336',
            'icone': '⚙️',
            'criteres': [
                {'nom': 'Type d\'équipement', 'type': 'select', 'valeurs': ['Machine-outil', 'Compresseur', 'Générateur', 'Pompe', 'Convoyeur', 'Robot', 'Presse', 'Four', 'Autre'], 'obligatoire': 1},
                {'nom': 'Puissance nominale (kW)', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Tension d\'alimentation (V)', 'type': 'select', 'valeurs': ['230', '400', '690', '1000', 'Autre'], 'obligatoire': 0},
                {'nom': 'Capacité/Débit', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Pression de service (bar)', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Dernière maintenance', 'type': 'date', 'obligatoire': 0},
                {'nom': 'Prochaine maintenance', 'type': 'date', 'obligatoire': 0},
                {'nom': 'Fréquence maintenance (mois)', 'type': 'select', 'valeurs': ['1', '3', '6', '12', '24', 'Autre'], 'obligatoire': 0},
                {'nom': 'Heures de fonctionnement', 'type': 'number', 'obligatoire': 0},
                {'nom': 'Certification CE', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Norme ISO', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Manuel disponible', 'type': 'boolean', 'obligatoire': 0},
                {'nom': 'Technicien responsable', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Fournisseur maintenance', 'type': 'text', 'obligatoire': 0},
                {'nom': 'Pièces de rechange', 'type': 'boolean', 'obligatoire': 0}
            ]
        }
    ]
    
    for secteur in secteurs_ameliores:
        try:
            # Insérer le secteur
            cursor.execute('''
                INSERT OR REPLACE INTO secteurs_personnalises (nom, description, couleur, icone)
                VALUES (?, ?, ?, ?)
            ''', (secteur['nom'], secteur['description'], secteur['couleur'], secteur['icone']))
            
            secteur_id = cursor.lastrowid
            
            # Supprimer les anciens critères
            cursor.execute('DELETE FROM criteres_secteur WHERE secteur_id = ?', (secteur_id,))
            
            # Insérer les nouveaux critères
            for i, critere in enumerate(secteur['criteres']):
                valeurs_json = None
                if 'valeurs' in critere:
                    valeurs_json = json.dumps(critere['valeurs'])
                
                cursor.execute('''
                    INSERT INTO criteres_secteur 
                    (secteur_id, nom_critere, type_critere, valeurs_possibles, obligatoire, ordre_affichage)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    secteur_id,
                    critere['nom'],
                    critere['type'],
                    valeurs_json,
                    critere.get('obligatoire', 0),
                    i
                ))
            
            print(f"✅ Secteur '{secteur['nom']}' créé avec {len(secteur['criteres'])} critères")
            
        except Exception as e:
            print(f"❌ Erreur pour le secteur '{secteur['nom']}': {e}")
    
    # 4. Mettre à jour les secteurs existants dans la table immobilisations
    print("🔄 Mise à jour des secteurs existants...")
    
    # Mapping des anciens secteurs vers les nouveaux
    mapping_secteurs = {
        'Informatique': 'Informatique & Technologies',
        'Véhicules': 'Véhicules & Transport',
        'Mobilier': 'Mobilier & Aménagement',
        'Équipements': 'Équipements Industriels'
    }
    
    for ancien, nouveau in mapping_secteurs.items():
        cursor.execute('''
            UPDATE immobilisations 
            SET secteur = ? 
            WHERE secteur = ?
        ''', (nouveau, ancien))
        
        nb_updated = cursor.rowcount
        if nb_updated > 0:
            print(f"✅ {nb_updated} biens mis à jour: '{ancien}' → '{nouveau}'")
    
    # 5. Créer des index pour améliorer les performances
    print("📈 Création des index pour optimiser les performances...")
    
    index_queries = [
        "CREATE INDEX IF NOT EXISTS idx_secteurs_nom ON secteurs_personnalises(nom)",
        "CREATE INDEX IF NOT EXISTS idx_criteres_secteur ON criteres_secteur(secteur_id)",
        "CREATE INDEX IF NOT EXISTS idx_valeurs_bien ON valeurs_criteres(bien_id)",
        "CREATE INDEX IF NOT EXISTS idx_valeurs_critere ON valeurs_criteres(critere_id)",
        "CREATE INDEX IF NOT EXISTS idx_immob_secteur ON immobilisations(secteur)",
        "CREATE INDEX IF NOT EXISTS idx_immob_marque ON immobilisations(marque)",
        "CREATE INDEX IF NOT EXISTS idx_immob_modele ON immobilisations(modele)"
    ]
    
    for query in index_queries:
        cursor.execute(query)
    
    print("✅ Index créés")
    
    # 6. Insérer des données d'exemple
    print("📝 Insertion de données d'exemple...")
    
    exemples_biens = [
        {
            'designation': 'Ordinateur portable Dell Latitude 5520',
            'valeur': 1200.00,
            'annee': 2023,
            'localisation': 'Bureau 101',
            'secteur': 'Informatique & Technologies',
            'observation': 'Ordinateur de direction avec configuration haute performance',
            'marque': 'Dell',
            'modele': 'Latitude 5520',
            'numero_serie': '*********'
        },
        {
            'designation': 'Véhicule de service Renault Clio',
            'valeur': 18500.00,
            'annee': 2022,
            'localisation': 'Parking entreprise',
            'secteur': 'Véhicules & Transport',
            'observation': 'Véhicule de service pour déplacements commerciaux',
            'marque': 'Renault',
            'modele': 'Clio V',
            'numero_serie': 'VF1RJA00123456789'
        },
        {
            'designation': 'Bureau ergonomique réglable',
            'valeur': 450.00,
            'annee': 2023,
            'localisation': 'Open space',
            'secteur': 'Mobilier & Aménagement',
            'observation': 'Bureau assis-debout électrique',
            'marque': 'Steelcase',
            'modele': 'Series 7'
        }
    ]
    
    for bien in exemples_biens:
        try:
            cursor.execute('''
                INSERT INTO immobilisations 
                (designation, valeur, annee, localisation, secteur, observation, marque, modele, numero_serie)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                bien['designation'], bien['valeur'], bien['annee'], 
                bien['localisation'], bien['secteur'], bien['observation'],
                bien.get('marque'), bien.get('modele'), bien.get('numero_serie')
            ))
            print(f"✅ Bien d'exemple ajouté: {bien['designation']}")
        except Exception as e:
            print(f"⚠️ Bien déjà existant: {bien['designation']}")
    
    # Valider les changements
    conn.commit()
    conn.close()
    
    print("\n🎉 AMÉLIORATION TERMINÉE AVEC SUCCÈS !")
    print("=" * 60)
    print("✅ Base de données améliorée avec des secteurs spécialisés")
    print("✅ Critères spécifiques ajoutés pour chaque domaine")
    print("✅ Tables optimisées avec des index")
    print("✅ Données d'exemple insérées")
    print("\n🚀 GestImmob est maintenant prêt avec les modules personnalisés !")

if __name__ == "__main__":
    ameliorer_base_donnees()
