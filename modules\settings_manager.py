import json
from typing import Any, Dict, Optional, List
from dataclasses import dataclass
import sqlite3
import pyodbc
import logging
from pathlib import Path
from functools import lru_cache

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SettingsMetadata:
    answer: Optional[str] = None
    db_type: Optional[str] = None
    constrained: Optional[bool] = None
    aux_tables: Optional[bool] = None
    run_mode: Optional[str] = None
    description: Optional[str] = None  # Ajout d'un champ description

class SettingsManager:
    def __init__(self, filename: str = "settings.json"):
        self.filename: str = filename
        self.settings: Dict[str, Any] = self.load()
        self.metadata: Dict[str, SettingsMetadata] = {}
        self.performance: Dict[str, Any] = {}
        self.ui_config: Dict[str, Any] = {
            "button_colors": {
                "ajouter": "#4CAF50",
                "modifier": "#2196F3",
                "supprimer": "#F44336",
                "synchroniser": "#FF9800",
                "scanner": "#9C27B0",
                "charts": "#607D8B",
                "import_online": "#009688",
                "export_excel": "#FFC107",
                "export_csv": "#795548"
            },
            "window_theme": "light"
        }

    def load(self) -> Dict[str, Any]:
        try:
            with open(self.filename, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception:
            return {}

    def save(self) -> None:
        with open(self.filename, "w", encoding="utf-8") as f:
            json.dump(self.settings, f, ensure_ascii=False, indent=2)

    def get(self, key: str, default: Any = None) -> Any:
        return self.settings.get(key, default)

    def set(self, key: str, value: Any) -> None:
        self.settings[key] = value
        self.save()

    def set_metadata(self, key: str, metadata: SettingsMetadata) -> None:
        self.metadata[key] = metadata

    def get_metadata(self, key: str) -> Optional[SettingsMetadata]:
        return self.metadata.get(key)

    def get_queries_by_alias(self, alias: str) -> Dict[str, SettingsMetadata]:
        return {k: v for k, v in self.metadata.items() if getattr(v, "alias", None) == alias}

    def set_performance(self, key: str, value: Any) -> None:
        self.performance[key] = value

    def get_performance(self, key: str, default: Any = None) -> Any:
        return self.performance.get(key, default)

    def get_button_color(self, button_name: str) -> str:
        # Correction pour Pylance : accès sûr à self.ui_config
        button_colors = self.ui_config.get("button_colors", {})
        return button_colors.get(button_name, "#CCCCCC")

    def set_window_theme(self, theme: str) -> None:
        self.ui_config["window_theme"] = theme

    def get_window_theme(self) -> str:
        return self.ui_config.get("window_theme", "light")

def resolve_path(path: str) -> str:
    if path.startswith(":PRIVE:"):
        return path.replace(":PRIVE:", "/mnt/private/")
    return path

# --- Module SQLQuery/SQLManager simplifié pour gestion rapide des fichiers SQL ---
from pathlib import Path
from typing import Dict

class SQLQuery:
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.alias = ""
        self.live_answer = False
        self.sql_content = ""
        self.extra_metadata: Dict[str, str] = {}
        self._parse_file()
    
    def _parse_file(self):
        with open(self.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # Extraction des métadonnées
        metadata_block = content.split('*/')[0].strip('/*').strip()
        for line in metadata_block.split('\n'):
            line = line.strip()
            if ':' in line:
                key, value = [part.strip() for part in line.split(':', 1)]
                if key.lower() == 'alias':
                    self.alias = value
                elif key.lower() == 'liveanswer':
                    self.live_answer = value.upper() == 'TRUE'
                else:
                    self.extra_metadata[key] = value
        # Extraction du SQL
        self.sql_content = content.split('*/')[1].strip()

class SQLManager:
    def __init__(self, sql_folder: str):
        self.sql_folder = Path(sql_folder)
        self.queries: Dict[str, SQLQuery] = {}
        self._load_queries()

    def preprocess_sql(self, sql: str) -> str:
        if 'c:\\winimmo' in sql:
            return sql.replace('c:\\winimmo', '/mnt/winimmo')
        return sql

    def _load_queries(self):
        for sql_file in self.sql_folder.glob('*.SQL'):
            query = SQLQuery(str(sql_file))  # Correction ici
            self.queries[sql_file.stem.upper()] = query

    def get_query(self, query_name: str) -> SQLQuery:
        query = self.queries.get(query_name.upper())
        if query is None:
            raise ValueError(f"Query '{query_name}' not found")
        return query

    def execute_query(self, query_name: str, connection: Any, params: Optional[Dict[str, Any]] = None) -> Optional[List[Any]]:
        logger.info(f"Executing query: {query_name}")
        query = self.get_query(query_name)
        if not query:
            raise ValueError(f"Query {query_name} not found")
        sql = self.preprocess_sql(query.sql_content)
        if params:
            sql = sql.format(**params)
        cursor = connection.cursor()
        cursor.execute(sql)
        if query.live_answer:
            return cursor.fetchall()
        return None

    @lru_cache(maxsize=32)
    def execute_query_cached(self, query_name: str, connection) -> List[Any] | None:
        """
        Exécute une requête SQL et utilise le cache pour les résultats.
        :param query_name: Nom de la requête dans les fichiers SQL.
        :param connection: Connexion à la base de données.
        :return: Résultats de la requête ou None si non applicable.
        """
        logger.info(f"Executing cached query: {query_name}")
        return self.execute_query(query_name, connection)

# Exemple d'utilisation du SettingsManager et du SQLManager

def open_module_window(module_name: str, settings: SettingsManager):
    """
    Fonction utilitaire pour ouvrir une fenêtre/module en fonction du bouton cliqué.
    Peut être appelée depuis l'interface principale.
    """
    logger.info(f"Ouverture du module {module_name}")
    # Ici, vous pouvez ajouter la logique pour ouvrir la fenêtre correspondante
    # et charger les dépendances nécessaires selon le module.
    # Par exemple :
    if module_name == "ajouter":
        logger.info("Module Ajouter ouvert")
        # ...ouvrir la fenêtre d'ajout...
    elif module_name == "modifier":
        logger.info("Module Modifier ouvert")
        # ...ouvrir la fenêtre de modification...
    # ...etc pour chaque bouton/module...

def main():
    # Initialisation
    settings = SettingsManager()
    manager = SQLManager("sql_queries")
    
    # Connexion à la base de données (adaptez selon votre SGBD)
    # Pour SQLite :
    # conn = sqlite3.connect("equip.db")
    # Pour Paradox :
    conn = pyodbc.connect(r"Driver={Microsoft Paradox Driver (*.db )};DBQ=C:\chemin\vers\vos\bases;")
    
    try:
        # Exécution de la requête REFORME
        logger.info(f"Exécution de REFORME sur {manager.get_query('REFORME').metadata.db_type or 'default'}")
        equipements_reformes = manager.execute_query("REFORME", conn)
        if equipements_reformes:
            print("Équipements réformés:")
            for equip in equipements_reformes:
                print(equip)
        
        # Exécution de la requête SQL avec paramètres
        logger.info(f"Exécution de SQL sur {manager.get_query('SQL').metadata.db_type or 'default'}")
        bureaux = manager.execute_query("SQL", conn, {
            # Vous pourriez ajouter des paramètres si la requête était paramétrée
        })
        if bureaux:
            print("\nListe des bureaux:")
            for bureau in bureaux:
                print(f"{bureau[0]}-{bureau[1]}-{bureau[2]} - {bureau[3]}")
    
    finally:
        conn.close()

    # Synchronisation automatique des paramètres/performance
    settings.set_performance("last_sync", "auto")
    logger.info(f"Performance: {settings.performance}")

if __name__ == "__main__":
    # Utiliser la classe SQLManager locale, pas besoin d'import
    # import sqlite3  # ou autre connecteur DB

    # Initialisation
    sql_manager = SQLManager("sql_queries")

    # Connexion à la base de données
    import sqlite3
    conn = sqlite3.connect("votre_base.db")

    # Exécution d'une requête
    resultats = sql_manager.execute_query("NUM_INV", conn)
    print(resultats)

    # Avec paramètres (si votre SQL utilise des variables)
    resultats_bureau = sql_manager.execute_query(
        "SEL-BUR", 
        conn, 
        {"cod_site": "01", "cod_bat": "01"}
    )
    print(resultats_bureau)

def some_function(conn: str) -> None:
    if not isinstance(conn, str):
        raise TypeError(f"Expected str for conn, got {type(conn).__name__}")
    # ...le reste de votre code...
