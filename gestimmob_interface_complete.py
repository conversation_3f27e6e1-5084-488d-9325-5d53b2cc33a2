#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GestImmob - Interface Complète avec Style d'Inventaire
Basé sur vos modules existants + nouveaux modules demandés
"""

import sys
import sqlite3
import json
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QFrame,
    QScrollArea, QGridLayout, QComboBox, QLineEdit, QTextEdit,
    QDialog, QDialogButtonBox, QFormLayout, QSpinBox, QDateEdit,
    QCheckBox, QProgressBar, QSystemTrayIcon, QMenu, QSplitter,
    QTabWidget, QGroupBox, QSlider, QToolTip
)
from PySide6.QtCore import Qt, <PERSON><PERSON>imer, <PERSON><PERSON><PERSON><PERSON>, Signal, Q<PERSON><PERSON>tyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon, QAction

class GestImmobInterfaceComplete(QMainWindow):
    """Interface complète GestImmob avec style d'inventaire validé"""
    
    def __init__(self):
        super().__init__()
        
        # Configuration de base
        self.current_user = None
        self.work_timer = QTimer()
        self.work_start_time = None
        self.currency_symbol = "€"  # Modifiable selon le pays
        self.code_counter = 1
        
        # Modules existants (basés sur votre structure)
        self.modules_existants = {
            'Informatique & Technologies': {'couleur': '#2196F3', 'icone': '💻', 'criteres': 15},
            'Véhicules & Transport': {'couleur': '#FF9800', 'icone': '🚗', 'criteres': 16},
            'Mobilier & Aménagement': {'couleur': '#8BC34A', 'icone': '🪑', 'criteres': 12},
            'Équipements Industriels': {'couleur': '#F44336', 'icone': '⚙️', 'criteres': 15}
        }
        
        # Nouveaux modules demandés
        self.nouveaux_modules = {
            'Gestion des Biens': {'couleur': '#1976D2', 'icone': '🏠'},
            'Fournisseurs': {'couleur': '#388E3C', 'icone': '🏢'},
            'Inventaire': {'couleur': '#F57C00', 'icone': '📦'},
            'Parc Auto': {'couleur': '#D32F2F', 'icone': '🚗'},
            'Suivi des Animaux': {'couleur': '#7B1FA2', 'icone': '🐄'},
            'Suivi des Travaux': {'couleur': '#455A64', 'icone': '🔨'},
            'Outils de Travaux': {'couleur': '#5D4037', 'icone': '🔧'},
            'Calculatrice': {'couleur': '#00796B', 'icone': '🧮'},
            'Documents': {'couleur': '#303F9F', 'icone': '📄'},
            'Impression': {'couleur': '#689F38', 'icone': '🖨️'},
            'Archive': {'couleur': '#512DA8', 'icone': '📚'},
            'Outillage': {'couleur': '#E64A19', 'icone': '⚒️'},
            'Moyens Généraux': {'couleur': '#1976D2', 'icone': '🏭'},
            'Hôtellerie': {'couleur': '#C2185B', 'icone': '🏨'},
            'Agencement': {'couleur': '#00ACC1', 'icone': '🪑'},
            'Bâtis': {'couleur': '#6A4C93', 'icone': '🏗️'}
        }
        
        # Initialisation
        self.init_database()
        self.setup_login_window()
    
    def init_database(self):
        """Initialise la base de données avec vos structures existantes"""
        try:
            self.conn = sqlite3.connect("gestimmob_final.db")
            cursor = self.conn.cursor()
            
            # Table principale des immobilisations (basée sur votre structure)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS immobilisations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    code_barre TEXT,
                    designation TEXT NOT NULL,
                    valeur REAL DEFAULT 0,
                    secteur TEXT,
                    localisation TEXT,
                    etat TEXT DEFAULT 'Bon',
                    responsable TEXT,
                    date_acquisition DATE,
                    observation TEXT,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des modules (pour vos critères existants + nouveaux)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS modules_criteres (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    module_nom TEXT NOT NULL,
                    critere_nom TEXT NOT NULL,
                    critere_type TEXT NOT NULL,
                    critere_valeurs TEXT,
                    obligatoire INTEGER DEFAULT 0,
                    ordre INTEGER DEFAULT 0
                )
            ''')
            
            # Table des utilisateurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS utilisateurs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    nom_complet TEXT,
                    role TEXT DEFAULT 'user',
                    photo_path TEXT,
                    actif INTEGER DEFAULT 1,
                    derniere_connexion TIMESTAMP
                )
            ''')
            
            # Insérer utilisateur par défaut
            cursor.execute('''
                INSERT OR IGNORE INTO utilisateurs (username, password, nom_complet, role)
                VALUES ('admin', 'Admin@1234', 'Administrateur', 'admin')
            ''')
            
            self.conn.commit()
            print("✅ Base de données initialisée avec vos structures existantes")
            
        except Exception as e:
            print(f"❌ Erreur initialisation base: {e}")
    
    def setup_login_window(self):
        """Fenêtre de connexion avec photo et sécurité"""
        self.login_dialog = QDialog()
        self.login_dialog.setWindowTitle("🔐 Connexion GestImmob")
        self.login_dialog.setFixedSize(400, 500)
        
        # Style noir pour la fenêtre de connexion
        self.login_dialog.setStyleSheet("""
            QDialog {
                background-color: #000000;
                border: 2px solid #333333;
            }
            QLabel {
                color: white;
                font-size: 14pt;
            }
            QLineEdit {
                background-color: #333333;
                color: white;
                border: 2px solid #555555;
                border-radius: 8px;
                padding: 10px;
                font-size: 12pt;
            }
            QPushButton {
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
        """)
        
        layout = QVBoxLayout(self.login_dialog)
        
        # Photo d'ambiance bureau futuriste
        photo_label = QLabel()
        photo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        photo_label.setStyleSheet("""
            QLabel {
                background-color: #1976D2;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        photo_label.setText("🖼️ Bureau Futuriste\n🖥️ Environnement Moderne\n⚡ Outils Avancés")
        layout.addWidget(photo_label)
        
        # Titre
        title_label = QLabel("🔐 Connexion Sécurisée")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 20px;")
        layout.addWidget(title_label)
        
        # Champs de connexion
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("👤 Nom d'utilisateur")
        layout.addWidget(self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("🔒 Mot de passe")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        layout.addWidget(self.password_input)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        login_btn = QPushButton("🔓 Se Connecter")
        login_btn.clicked.connect(self.authenticate_user)
        buttons_layout.addWidget(login_btn)
        
        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.clicked.connect(self.login_dialog.reject)
        cancel_btn.setStyleSheet("background-color: #F44336;")
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        # Afficher la fenêtre de connexion
        if self.login_dialog.exec() == QDialog.DialogCode.Accepted:
            self.setup_main_interface()
        else:
            sys.exit()
    
    def authenticate_user(self):
        """Authentification utilisateur"""
        username = self.username_input.text()
        password = self.password_input.text()
        
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, username, nom_complet, role, photo_path 
            FROM utilisateurs 
            WHERE username = ? AND password = ? AND actif = 1
        ''', (username, password))
        
        user = cursor.fetchone()
        
        if user:
            self.current_user = {
                'id': user[0],
                'username': user[1],
                'nom_complet': user[2],
                'role': user[3],
                'photo_path': user[4]
            }
            
            # Mettre à jour dernière connexion
            cursor.execute('''
                UPDATE utilisateurs 
                SET derniere_connexion = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user[0],))
            self.conn.commit()
            
            self.login_dialog.accept()
        else:
            # Message d'erreur
            error_label = QLabel("❌ Identifiants incorrects")
            error_label.setStyleSheet("color: red; font-weight: bold;")
            self.login_dialog.layout().addWidget(error_label)
    
    def setup_main_interface(self):
        """Interface principale avec style d'inventaire validé"""
        # Configuration de la fenêtre principale
        self.setWindowTitle(f"🏢 GestImmob - {self.current_user['nom_complet']}")
        self.setGeometry(100, 100, 1400, 900)
        
        # Style principal - Cadre noir avec fond blanc
        self.setStyleSheet("""
            QMainWindow {
                background-color: #000000;
                border: 3px solid #333333;
            }
        """)
        
        # Widget central avec fond blanc
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background-color: #FFFFFF;
                border: 1px solid #CCCCCC;
            }
        """)
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)
        
        # En-tête avec chronomètre et info utilisateur
        self.create_header(main_layout)
        
        # Zone principale avec modules organisés
        self.create_modules_area(main_layout)
        
        # Tableau principal avec en-têtes d'inventaire
        self.create_main_table(main_layout)
        
        # Rangée de boutons colorés en bas
        self.create_colored_buttons_bar(main_layout)
        
        # Démarrer le chronomètre de travail
        self.start_work_timer()
        
        # Afficher la fenêtre
        self.show()
    
    def create_header(self, layout):
        """Crée l'en-tête avec chronomètre et info utilisateur"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Info utilisateur
        user_info = QLabel(f"👤 {self.current_user['nom_complet']} | 🔑 {self.current_user['role']}")
        user_info.setStyleSheet("font-size: 14pt; font-weight: bold; color: #333333;")
        header_layout.addWidget(user_info)
        
        header_layout.addStretch()
        
        # Chronomètre de travail
        self.work_timer_label = QLabel("⏱️ 00:00:00")
        self.work_timer_label.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #1976D2;
                background-color: #E3F2FD;
                border: 2px solid #1976D2;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        header_layout.addWidget(self.work_timer_label)
        
        # Symbole monétaire
        currency_label = QLabel(f"💰 {self.currency_symbol}")
        currency_label.setStyleSheet("font-size: 14pt; font-weight: bold; color: #4CAF50;")
        header_layout.addWidget(currency_label)
        
        layout.addWidget(header_frame)
    
    def create_modules_area(self, layout):
        """Crée la zone des modules organisés de haut en bas"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background-color: #FAFAFA;
                border: 1px solid #DDDDDD;
                border-radius: 5px;
            }
        """)
        modules_frame.setFixedHeight(200)
        
        modules_layout = QHBoxLayout(modules_frame)
        
        # Scroll area pour les modules
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        modules_widget = QWidget()
        modules_grid = QGridLayout(modules_widget)
        
        # Organiser tous les modules (existants + nouveaux) de haut en bas
        tous_modules = {**self.modules_existants, **self.nouveaux_modules}
        
        row, col = 0, 0
        max_cols = 8  # 8 colonnes pour organiser les modules
        
        for nom_module, config in tous_modules.items():
            module_btn = self.create_module_button(nom_module, config)
            modules_grid.addWidget(module_btn, row, col)
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        scroll_area.setWidget(modules_widget)
        modules_layout.addWidget(scroll_area)
        
        layout.addWidget(modules_frame)
    
    def create_module_button(self, nom_module, config):
        """Crée un bouton de module avec style et tooltip"""
        btn = QPushButton(f"{config['icone']}\n{nom_module}")
        
        # Style du bouton de module
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {config['couleur']};
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-size: 10pt;
                font-weight: bold;
                min-width: 120px;
                max-width: 140px;
                min-height: 80px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(config['couleur'])};
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(config['couleur'], 0.3)};
            }}
        """)
        
        # Tooltip avec informations
        if nom_module in self.modules_existants:
            tooltip = f"📋 Module existant\n🎯 {self.modules_existants[nom_module]['criteres']} critères configurés\n🔧 Basé sur vos spécifications"
        else:
            tooltip = f"🆕 Nouveau module\n📋 Critères à configurer\n🎯 Codification INV001"
        
        btn.setToolTip(tooltip)
        
        # Connecter à l'ouverture de fenêtre spécifique
        btn.clicked.connect(lambda: self.open_module_window(nom_module, config))
        
        return btn
    
    def darken_color(self, hex_color, factor=0.2):
        """Assombrit une couleur hexadécimale"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
    
    def start_work_timer(self):
        """Démarre le chronomètre de travail"""
        self.work_start_time = datetime.now()
        
        # Timer qui se met à jour chaque seconde
        self.work_timer.timeout.connect(self.update_work_timer)
        self.work_timer.start(1000)  # 1 seconde
    
    def update_work_timer(self):
        """Met à jour l'affichage du chronomètre"""
        if self.work_start_time:
            elapsed = datetime.now() - self.work_start_time
            hours, remainder = divmod(elapsed.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            
            time_str = f"⏱️ {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            self.work_timer_label.setText(time_str)


    def create_main_table(self, layout):
        """Crée le tableau principal avec en-têtes d'inventaire"""
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: #FFFFFF;
                border: 1px solid #CCCCCC;
                border-radius: 5px;
            }
        """)

        table_layout = QVBoxLayout(table_frame)

        # Titre du tableau
        table_title = QLabel("📊 Inventaire Principal - Gestion des Immobilisations")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #333333;
                padding: 10px;
                background-color: #F5F5F5;
                border-bottom: 1px solid #CCCCCC;
            }
        """)
        table_layout.addWidget(table_title)

        # Tableau avec vos en-têtes d'inventaire
        self.main_table = QTableWidget()
        entetes = [
            "Code", "Code-Barre", "Désignation", "Valeur", "Secteur",
            "Localisation", "État", "Responsable", "Date Acquisition", "Observation"
        ]
        self.main_table.setColumnCount(len(entetes))
        self.main_table.setHorizontalHeaderLabels(entetes)

        # Style du tableau
        self.main_table.setStyleSheet("""
            QTableWidget {
                background-color: #FFFFFF;
                gridline-color: #E0E0E0;
                border: none;
                font-size: 12pt;
            }
            QHeaderView::section {
                background-color: #E3F2FD;
                color: #1976D2;
                font-weight: bold;
                font-size: 14pt;
                border: 1px solid #BBBBBB;
                padding: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
        """)

        # Charger les données existantes
        self.load_table_data()

        table_layout.addWidget(self.main_table)
        layout.addWidget(table_frame)

    def create_colored_buttons_bar(self, layout):
        """Crée la rangée de boutons colorés en bas (style validé)"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #000000;
                border: 2px solid #333333;
                border-radius: 8px;
            }
        """)
        buttons_frame.setFixedHeight(100)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 15, 15, 15)
        buttons_layout.setSpacing(15)

        # Boutons colorés avec icônes (style validé)
        boutons_config = [
            {"text": "Ajouter", "color": "#2196F3", "icon": "➕", "action": self.ajouter_item},
            {"text": "Modifier", "color": "#4CAF50", "icon": "✏️", "action": self.modifier_item},
            {"text": "Supprimer", "color": "#F44336", "icon": "🗑️", "action": self.supprimer_item},
            {"text": "Rechercher", "color": "#FF9800", "icon": "🔍", "action": self.rechercher_item},
            {"text": "Imprimer", "color": "#9C27B0", "icon": "🖨️", "action": self.imprimer_rapport},
            {"text": "Exporter", "color": "#607D8B", "icon": "📤", "action": self.exporter_donnees},
            {"text": "Import", "color": "#795548", "icon": "📥", "action": self.importer_donnees},
            {"text": "Alertes", "color": "#E91E63", "icon": "🚨", "action": self.afficher_alertes}
        ]

        for config in boutons_config:
            btn = self.create_colored_button(config)
            buttons_layout.addWidget(btn)

        # Espace flexible
        buttons_layout.addStretch()

        # Jauges graphiques pour import/export
        self.create_progress_gauges(buttons_layout)

        layout.addWidget(buttons_frame)

    def create_colored_button(self, config):
        """Crée un bouton coloré avec style validé"""
        btn = QPushButton(f"{config['icon']} {config['text']}")

        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {config['color']};
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 18px;
                font-size: 11pt;
                font-weight: bold;
                min-width: 110px;
                min-height: 50px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(config['color'])};
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(config['color'], 0.3)};
                transform: translateY(0px);
            }}
        """)

        # Tooltip avec indication
        btn.setToolTip(f"🎯 {config['text']}\n💡 Cliquez pour {config['text'].lower()}")

        # Connecter l'action
        btn.clicked.connect(config['action'])

        return btn

    def create_progress_gauges(self, layout):
        """Crée les jauges graphiques pour import/export"""
        gauges_frame = QFrame()
        gauges_frame.setStyleSheet("""
            QFrame {
                background-color: #1A1A1A;
                border: 1px solid #444444;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        gauges_frame.setFixedWidth(200)

        gauges_layout = QVBoxLayout(gauges_frame)

        # Jauge d'import
        import_label = QLabel("📥 Import")
        import_label.setStyleSheet("color: white; font-size: 10pt; font-weight: bold;")
        gauges_layout.addWidget(import_label)

        self.import_progress = QProgressBar()
        self.import_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                background-color: #333333;
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        self.import_progress.setValue(0)
        gauges_layout.addWidget(self.import_progress)

        # Jauge d'export
        export_label = QLabel("📤 Export")
        export_label.setStyleSheet("color: white; font-size: 10pt; font-weight: bold;")
        gauges_layout.addWidget(export_label)

        self.export_progress = QProgressBar()
        self.export_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                background-color: #333333;
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #2196F3;
                border-radius: 3px;
            }
        """)
        self.export_progress.setValue(0)
        gauges_layout.addWidget(self.export_progress)

        layout.addWidget(gauges_frame)

    def load_table_data(self):
        """Charge les données dans le tableau principal"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT code, code_barre, designation, valeur, secteur,
                       localisation, etat, responsable, date_acquisition, observation
                FROM immobilisations
                ORDER BY date_creation DESC
            ''')

            data = cursor.fetchall()
            self.main_table.setRowCount(len(data))

            for row, item in enumerate(data):
                for col, value in enumerate(item):
                    table_item = QTableWidgetItem(str(value) if value else "")

                    # Coloration selon l'état
                    if col == 6 and value:  # Colonne État
                        if value == "Bon":
                            table_item.setBackground(QColor("#E8F5E8"))
                        elif value == "Moyen":
                            table_item.setBackground(QColor("#FFF3E0"))
                        elif value == "Mauvais":
                            table_item.setBackground(QColor("#FFEBEE"))

                    self.main_table.setItem(row, col, table_item)

            # Ajuster la largeur des colonnes
            self.main_table.resizeColumnsToContents()

        except Exception as e:
            print(f"❌ Erreur chargement données: {e}")

    def open_module_window(self, nom_module, config):
        """Ouvre une fenêtre spécifique pour un module avec critères"""
        print(f"🪟 Ouverture du module: {nom_module}")

        # Créer la fenêtre de module avec style noir
        module_window = QDialog(self)
        module_window.setWindowTitle(f"{config['icone']} {nom_module}")
        module_window.setFixedSize(900, 700)

        # Style noir pour la fenêtre de module
        module_window.setStyleSheet("""
            QDialog {
                background-color: #000000;
                border: 2px solid #333333;
            }
            QLabel {
                color: white;
                font-size: 14pt;
            }
            QLineEdit, QComboBox, QTextEdit, QSpinBox, QDateEdit {
                background-color: #87CEEB;
                color: #000000;
                border: 2px solid #555555;
                border-radius: 8px;
                padding: 8px;
                font-size: 12pt;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #4CAF50;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            QPushButton {
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-size: 12pt;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
        """)

        layout = QVBoxLayout(module_window)

        # Titre avec logo
        title_label = QLabel(f"{config['icone']} {nom_module}")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; margin: 20px;")
        layout.addWidget(title_label)

        # Zone de critères avec combobox colorées
        self.create_module_criteria_form(layout, nom_module)

        # Boutons de validation
        self.create_module_buttons(layout, module_window, nom_module)

        # Afficher la fenêtre
        module_window.exec()

    def create_module_criteria_form(self, layout, nom_module):
        """Crée le formulaire de critères pour un module"""
        # Scroll area pour les critères
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #1A1A1A;
                border: 1px solid #444444;
                border-radius: 8px;
            }
        """)

        criteria_widget = QWidget()
        criteria_layout = QFormLayout(criteria_widget)
        criteria_layout.setSpacing(15)

        # Critères spécifiques selon le module
        criteres = self.get_module_criteria(nom_module)

        for critere in criteres:
            label = QLabel(f"{critere['icone']} {critere['nom']}:")
            label.setStyleSheet("color: white; font-size: 14pt; font-weight: bold;")
            label.setToolTip(critere.get('tooltip', f"Critère: {critere['nom']}"))

            # Créer le contrôle selon le type
            if critere['type'] == 'combo':
                control = QComboBox()
                control.addItems(critere['valeurs'])
                control.setStyleSheet("""
                    QComboBox {
                        background-color: #87CEEB;
                        color: #000000;
                        font-weight: bold;
                    }
                    QComboBox QAbstractItemView {
                        background-color: #87CEEB;
                        color: #000000;
                        selection-background-color: #4CAF50;
                    }
                """)
            elif critere['type'] == 'text':
                control = QLineEdit()
                control.setPlaceholderText(critere.get('placeholder', ''))
            elif critere['type'] == 'number':
                control = QSpinBox()
                control.setRange(0, 999999)
                control.setSuffix(f" {self.currency_symbol}")
            elif critere['type'] == 'date':
                control = QDateEdit()
                control.setCalendarPopup(True)
            elif critere['type'] == 'textarea':
                control = QTextEdit()
                control.setMaximumHeight(80)
            else:
                control = QLineEdit()

            # Tooltip pour le contrôle
            control.setToolTip(critere.get('tooltip', f"Saisir: {critere['nom']}"))

            criteria_layout.addRow(label, control)

        scroll_area.setWidget(criteria_widget)
        layout.addWidget(scroll_area)

    def get_module_criteria(self, nom_module):
        """Retourne les critères spécifiques pour un module"""
        criteres_modules = {
            'Gestion des Biens': [
                {'nom': 'Code Bien', 'type': 'text', 'icone': '🔢', 'placeholder': 'INV001', 'tooltip': 'Code automatique INV001'},
                {'nom': 'Désignation', 'type': 'text', 'icone': '📝', 'placeholder': 'Nom du bien', 'tooltip': 'Nom descriptif du bien'},
                {'nom': 'Catégorie', 'type': 'combo', 'icone': '📂', 'valeurs': ['Mobilier', 'Informatique', 'Véhicule', 'Équipement'], 'tooltip': 'Catégorie du bien'},
                {'nom': 'Valeur', 'type': 'number', 'icone': '💰', 'tooltip': f'Valeur en {self.currency_symbol}'},
                {'nom': 'État', 'type': 'combo', 'icone': '🔧', 'valeurs': ['Neuf', 'Bon', 'Moyen', 'Mauvais', 'Hors service'], 'tooltip': 'État actuel du bien'},
                {'nom': 'Localisation', 'type': 'combo', 'icone': '📍', 'valeurs': ['Bureau', 'Entrepôt', 'Atelier', 'Extérieur'], 'tooltip': 'Emplacement du bien'},
                {'nom': 'Responsable', 'type': 'combo', 'icone': '👤', 'valeurs': ['Admin', 'Gestionnaire', 'Technicien'], 'tooltip': 'Personne responsable'},
                {'nom': 'Date Acquisition', 'type': 'date', 'icone': '📅', 'tooltip': 'Date d\'acquisition du bien'},
                {'nom': 'Observation', 'type': 'textarea', 'icone': '📋', 'tooltip': 'Remarques et observations'}
            ],
            'Fournisseurs': [
                {'nom': 'Code Fournisseur', 'type': 'text', 'icone': '🏢', 'placeholder': 'FOUR001', 'tooltip': 'Code fournisseur'},
                {'nom': 'Raison Sociale', 'type': 'text', 'icone': '🏭', 'placeholder': 'Nom de l\'entreprise', 'tooltip': 'Nom officiel du fournisseur'},
                {'nom': 'Type', 'type': 'combo', 'icone': '📂', 'valeurs': ['Local', 'National', 'International'], 'tooltip': 'Type de fournisseur'},
                {'nom': 'Secteur Activité', 'type': 'combo', 'icone': '🎯', 'valeurs': ['Informatique', 'Mobilier', 'Services', 'Équipements'], 'tooltip': 'Secteur d\'activité'},
                {'nom': 'Téléphone', 'type': 'text', 'icone': '📞', 'placeholder': '+33 1 23 45 67 89', 'tooltip': 'Numéro de téléphone'},
                {'nom': 'Email', 'type': 'text', 'icone': '📧', 'placeholder': '<EMAIL>', 'tooltip': 'Adresse email'},
                {'nom': 'Adresse', 'type': 'textarea', 'icone': '📍', 'tooltip': 'Adresse complète'},
                {'nom': 'Note Qualité', 'type': 'combo', 'icone': '⭐', 'valeurs': ['Excellent', 'Très Bon', 'Bon', 'Moyen', 'Faible'], 'tooltip': 'Évaluation qualité'}
            ],
            'Inventaire': [
                {'nom': 'Code Article', 'type': 'text', 'icone': '📦', 'placeholder': 'INV001', 'tooltip': 'Code article avec code-barre'},
                {'nom': 'Désignation', 'type': 'text', 'icone': '📝', 'placeholder': 'Nom de l\'article', 'tooltip': 'Description de l\'article'},
                {'nom': 'Quantité Stock', 'type': 'number', 'icone': '📊', 'tooltip': 'Quantité en stock'},
                {'nom': 'Seuil Alerte', 'type': 'number', 'icone': '🚨', 'tooltip': 'Seuil d\'alerte stock'},
                {'nom': 'Prix Unitaire', 'type': 'number', 'icone': '💰', 'tooltip': f'Prix unitaire en {self.currency_symbol}'},
                {'nom': 'Unité', 'type': 'combo', 'icone': '📏', 'valeurs': ['Pièce', 'Kg', 'Litre', 'Mètre', 'Boîte'], 'tooltip': 'Unité de mesure'},
                {'nom': 'Emplacement', 'type': 'combo', 'icone': '📍', 'valeurs': ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'], 'tooltip': 'Emplacement en stock'},
                {'nom': 'Fournisseur', 'type': 'combo', 'icone': '🏢', 'valeurs': ['Fournisseur 1', 'Fournisseur 2'], 'tooltip': 'Fournisseur principal'}
            ],
            'Parc Auto': [
                {'nom': 'Immatriculation', 'type': 'text', 'icone': '🚗', 'placeholder': 'AB-123-CD', 'tooltip': 'Numéro d\'immatriculation'},
                {'nom': 'Marque', 'type': 'combo', 'icone': '🏭', 'valeurs': ['Renault', 'Peugeot', 'Citroën', 'Ford', 'Volkswagen'], 'tooltip': 'Marque du véhicule'},
                {'nom': 'Modèle', 'type': 'text', 'icone': '🚙', 'placeholder': 'Modèle du véhicule', 'tooltip': 'Modèle exact'},
                {'nom': 'Année', 'type': 'number', 'icone': '📅', 'tooltip': 'Année de mise en circulation'},
                {'nom': 'Kilométrage', 'type': 'number', 'icone': '🛣️', 'tooltip': 'Kilométrage actuel'},
                {'nom': 'Carburant', 'type': 'combo', 'icone': '⛽', 'valeurs': ['Essence', 'Diesel', 'Électrique', 'Hybride'], 'tooltip': 'Type de carburant'},
                {'nom': 'Assurance', 'type': 'text', 'icone': '🛡️', 'placeholder': 'Compagnie d\'assurance', 'tooltip': 'Assureur du véhicule'},
                {'nom': 'Contrôle Technique', 'type': 'date', 'icone': '🔧', 'tooltip': 'Date du prochain contrôle technique'}
            ]
        }

        # Retourner les critères du module ou des critères par défaut
        return criteres_modules.get(nom_module, [
            {'nom': 'Code', 'type': 'text', 'icone': '🔢', 'placeholder': 'INV001', 'tooltip': 'Code automatique'},
            {'nom': 'Désignation', 'type': 'text', 'icone': '📝', 'placeholder': 'Nom', 'tooltip': 'Désignation'},
            {'nom': 'Valeur', 'type': 'number', 'icone': '💰', 'tooltip': f'Valeur en {self.currency_symbol}'},
            {'nom': 'Observation', 'type': 'textarea', 'icone': '📋', 'tooltip': 'Remarques'}
        ])

    def create_module_buttons(self, layout, dialog, nom_module):
        """Crée les boutons de validation pour un module"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #1A1A1A;
                border: 1px solid #444444;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)

        # Bouton Valider
        validate_btn = QPushButton("✅ Valider")
        validate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
        """)
        validate_btn.setToolTip("💾 Sauvegarder les données")
        validate_btn.clicked.connect(lambda: self.validate_module_data(dialog, nom_module))
        buttons_layout.addWidget(validate_btn)

        # Bouton Annuler
        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                font-weight: bold;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #E53935;
            }
        """)
        cancel_btn.setToolTip("🚫 Annuler sans sauvegarder")
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        # Bouton Retour
        back_btn = QPushButton("🔙 Retour")
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        back_btn.setToolTip("↩️ Retour au menu principal")
        back_btn.clicked.connect(dialog.accept)
        buttons_layout.addWidget(back_btn)

        # Bouton Modifier
        modify_btn = QPushButton("✏️ Modifier")
        modify_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        modify_btn.setToolTip("🔧 Modifier les données")
        modify_btn.clicked.connect(lambda: self.modify_module_data(dialog, nom_module))
        buttons_layout.addWidget(modify_btn)

        layout.addWidget(buttons_frame)

    def validate_module_data(self, dialog, nom_module):
        """Valide et sauvegarde les données du module"""
        print(f"✅ Validation des données pour: {nom_module}")

        # Générer un nouveau code automatique
        nouveau_code = f"INV{self.code_counter:03d}"
        self.code_counter += 1

        print(f"🔢 Nouveau code généré: {nouveau_code}")

        # Ici vous pouvez ajouter la logique de sauvegarde
        # selon vos critères spécifiques

        dialog.accept()

    def modify_module_data(self, dialog, nom_module):
        """Modifie les données du module"""
        print(f"✏️ Modification des données pour: {nom_module}")
        # Logique de modification selon vos critères

    # Actions des boutons colorés
    def ajouter_item(self):
        """Action Ajouter avec fenêtre de critères"""
        print("➕ Action: Ajouter un nouvel item")
        # Ouvrir fenêtre d'ajout avec critères spécifiques

    def modifier_item(self):
        """Action Modifier avec fenêtre de critères"""
        print("✏️ Action: Modifier l'item sélectionné")
        # Ouvrir fenêtre de modification avec critères pré-remplis

    def supprimer_item(self):
        """Action Supprimer avec confirmation"""
        print("🗑️ Action: Supprimer l'item sélectionné")

    def rechercher_item(self):
        """Action Rechercher avec critères avancés"""
        print("🔍 Action: Recherche avancée")

    def imprimer_rapport(self):
        """Action Imprimer avec options"""
        print("🖨️ Action: Impression de rapport")

    def exporter_donnees(self):
        """Action Exporter avec jauge de progression"""
        print("📤 Action: Export des données")
        # Simuler progression
        for i in range(101):
            self.export_progress.setValue(i)
            QApplication.processEvents()

    def importer_donnees(self):
        """Action Importer avec jauge de progression"""
        print("📥 Action: Import des données")
        # Simuler progression
        for i in range(101):
            self.import_progress.setValue(i)
            QApplication.processEvents()

    def afficher_alertes(self):
        """Action Alertes et voyants de prévention"""
        print("🚨 Action: Affichage des alertes")

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName("GestImmob Interface Complète")
    app.setApplicationVersion("2.0.0")

    # Créer et afficher l'interface
    window = GestImmobInterfaceComplete()

    return app.exec()

if __name__ == "__main__":
    main()
