from PySide6.QtWidgets import QWidget, QVBoxLayout, QPushButton, QLabel, QFileDialog, QComboBox, QTableWidget, QTableWidgetItem, QMessageBox, QHBoxLayout, QCheckBox, QLineEdit
from PySide6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PySide6.QtGui import QPageSize

class ImpressionWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Impression avancée (multi-marques, multi-formats)")
        self.resize(900, 600)
        layout = QVBoxLayout(self)
        self.info = QLabel("Sélectionnez les données à imprimer et configurez les paramètres d'impression.")
        layout.addWidget(self.info)
        self.table = QTableWidget(0, 5)
        self.table.setHorizontalHeaderLabels(["Col1", "Col2", "Col3", "Col4", "Col5"])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.btn_import = QPushButton("Importer données")
        self.btn_print = QPushButton("Imprimer")
        self.btn_preview = QPushButton("Aperçu avant impression")
        self.btn_export_txt = QPushButton("Exporter TXT")
        self.combo_format = QComboBox()
        self.combo_format.addItems(["PDF", "A4", "A3", "Lettre", "Paysage", "Portrait"])
        self.printer_select = QComboBox()
        self.printer_select.addItem("Défaut")
        try:
            from PySide6.QtPrintSupport import QPrinterInfo
            for p in QPrinterInfo.availablePrinters():
                self.printer_select.addItem(p.printerName())
        except Exception:
            pass
        self.chk_entete = QCheckBox("Inclure en-tête")
        self.chk_pied = QCheckBox("Inclure pied de page")
        self.combo_connexion = QComboBox()
        self.combo_connexion.addItems(["WiFi", "Câble USB", "Réseau"])
        btn_layout.addWidget(self.btn_import)
        btn_layout.addWidget(self.combo_format)
        btn_layout.addWidget(self.printer_select)
        btn_layout.addWidget(self.chk_entete)
        btn_layout.addWidget(self.chk_pied)
        btn_layout.addWidget(self.btn_preview)
        btn_layout.addWidget(self.btn_export_txt)
        btn_layout.addWidget(self.btn_print)
        btn_layout.addWidget(self.combo_connexion)
        layout.addLayout(btn_layout)
        # Ajout d'une zone de filtre rapide par colonne
        filter_layout = QHBoxLayout()
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("Filtrer par valeur (toutes colonnes)...")
        self.filter_btn = QPushButton("Filtrer")
        filter_layout.addWidget(self.filter_input)
        filter_layout.addWidget(self.filter_btn)
        layout.addLayout(filter_layout)
        self.filter_btn.clicked.connect(self.filtrer_table)
        # Ajout d'un bouton pour exporter en CSV
        self.btn_export_csv = QPushButton("Exporter CSV")
        btn_layout.addWidget(self.btn_export_csv)
        self.btn_export_csv.clicked.connect(self.exporter_csv)
        # Ajout d'un bouton pour imprimer seulement la sélection
        self.btn_print_selection = QPushButton("Imprimer sélection")
        btn_layout.addWidget(self.btn_print_selection)
        self.btn_print_selection.clicked.connect(self.imprimer_selection)
        self.btn_import.clicked.connect(self.importer_donnees)
        self.btn_print.clicked.connect(self.imprimer)
        self.btn_preview.clicked.connect(self.apercu)
        self.btn_export_txt.clicked.connect(self.exporter_txt)

    def importer_donnees(self):
        file, _ = QFileDialog.getOpenFileName(self, "Importer CSV", "", "CSV (*.csv);;Tous les fichiers (*)")
        if file:
            import csv
            with open(file, "r", encoding="utf-8") as f:
                reader = csv.reader(f)
                self.table.setRowCount(0)
                for row_data in reader:
                    row = self.table.rowCount()
                    self.table.insertRow(row)
                    for col, value in enumerate(row_data):
                        if col < self.table.columnCount():
                            self.table.setItem(row, col, QTableWidgetItem(value))

    def imprimer(self):
        printer = self._get_printer()
        fmt = self.combo_format.currentText()
        connexion = self.combo_connexion.currentText()
        # Gestion du critère de connexion (affichage ou log, à adapter selon l'intégration réelle)
        if connexion == "WiFi":
            print("Impression via WiFi")
        elif connexion == "Câble USB":
            print("Impression via câble USB")
        elif connexion == "Réseau":
            print("Impression via réseau")
        # Paramètres avancés : format, orientation, etc.
        if fmt == "PDF":
            # Correction : utiliser QPrinter.OutputFormat.PdfFormat
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
            file, _ = QFileDialog.getSaveFileName(self, "Enregistrer PDF", "", "PDF (*.pdf)")
            if file:
                printer.setOutputFileName(file)
        elif fmt == "A3":
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.A3))
        elif fmt == "A4":
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.A4))
        elif fmt == "Lettre":
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.Letter))
        elif fmt == "Paysage":
            from PySide6.QtGui import QPageLayout
            printer.setPageOrientation(QPageLayout.Orientation.Landscape)
        elif fmt == "Portrait":
            from PySide6.QtGui import QPageLayout
            printer.setPageOrientation(QPageLayout.Orientation.Portrait)
        dialog = QPrintDialog(printer, self)
        if dialog.exec() == 1:
            self._print_table(printer)
            QMessageBox.information(self, "Impression", "Impression envoyée à l'imprimante.\nCompatible avec la plupart des marques du marché.")
        else:
            QMessageBox.information(self, "Impression", "Impression annulée.")

    def apercu(self):
        printer = self._get_printer()
        preview = QPrintPreviewDialog(printer, self)
        # Correction : annotation explicite du type pour le paramètre du lambda
        preview.paintRequested.connect(lambda p: self._print_table(p))  # type: ignore
        preview.exec()

    def exporter_txt(self):
        file, _ = QFileDialog.getSaveFileName(self, "Exporter TXT", "", "Texte (*.txt)")
        if file:
            with open(file, "w", encoding="utf-8") as f:
                if self.chk_entete.isChecked():
                    f.write(
                        "\t".join([
                            (header.text() if header is not None else "")
                            for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
                        ]) + "\n"
                    )
                for row in range(self.table.rowCount()):
                    values: list[str] = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        values.append(item.text() if item else "")
                    f.write("\t".join(values) + "\n")
                if self.chk_pied.isChecked():
                    f.write("\n--- Fin du document ---\n")
            QMessageBox.information(self, "Export", "Export TXT terminé.")

    def exporter_csv(self):
        file, _ = QFileDialog.getSaveFileName(self, "Exporter CSV", "", "CSV (*.csv)")
        if file:
            import csv
            with open(file, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                writer.writerow([
                    (header.text() if header is not None else "")
                    for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
                ])
                for row in range(self.table.rowCount()):
                    writer.writerow([
                        (item.text() if item is not None else "")
                        for item in [self.table.item(row, col) for col in range(self.table.columnCount())]
                    ])
            QMessageBox.information(self, "Export", "Export CSV terminé.")

    def filtrer_table(self):
        filtre = self.filter_input.text().strip().lower()
        for row in range(self.table.rowCount()):
            visible = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and filtre in item.text().lower():
                    visible = True
                    break
            self.table.setRowHidden(row, not visible)

    def imprimer_selection(self):
        selected_ranges = self.table.selectedRanges()
        if not selected_ranges:
            QMessageBox.warning(self, "Sélection", "Sélectionnez au moins une cellule à imprimer.")
            return
        from PySide6.QtGui import QTextDocument
        html = "<html><body><table border='1' cellspacing='0' cellpadding='2'>"
        if self.chk_entete.isChecked():
            html += "<tr>"
            for i in range(self.table.columnCount()):
                header = self.table.horizontalHeaderItem(i)
                html += f"<th>{header.text() if header is not None else ''}</th>"
            html += "</tr>"
        for selection in selected_ranges:
            for row in range(selection.topRow(), selection.bottomRow() + 1):
                html += "<tr>"
                for col in range(selection.leftColumn(), selection.rightColumn() + 1):
                    item = self.table.item(row, col)
                    html += f"<td>{item.text() if item else ''}</td>"
                html += "</tr>"
        html += "</table></body></html>"
        doc = QTextDocument()
        doc.setHtml(html)
        printer = self._get_printer()
        dialog = QPrintDialog(printer, self)
        if dialog.exec() == 1:
            doc.print_(printer)
            QMessageBox.information(self, "Impression", "Impression de la sélection envoyée à l'imprimante.")
    def _get_printer(self):
        from PySide6.QtPrintSupport import QPrinter, QPrinterInfo
        printer = QPrinter()
        name = self.printer_select.currentText()
        if name != "Défaut":
            for p in QPrinterInfo.availablePrinters():
                if p.printerName() == name:
                    printer.setPrinterName(name)
                    break
        return printer

    def _print_table(self, printer: QPrinter):
        from PySide6.QtGui import QTextDocument
        html = "<html><body>"
        if self.chk_entete.isChecked():
            html += "<h2>Impression des données</h2>"
            html += "<table border='1' cellspacing='0' cellpadding='2'><tr>"
            for i in range(self.table.columnCount()):
                header = self.table.horizontalHeaderItem(i)
                html += f"<th>{header.text() if header is not None else ''}</th>"
            html += "</tr>"
        else:
            html += "<table border='1' cellspacing='0' cellpadding='2'>"
        for row in range(self.table.rowCount()):
            html += "<tr>"
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                html += f"<td>{item.text() if item else ''}</td>"
            html += "</tr>"
        html += "</table>"
        if self.chk_pied.isChecked():
            html += "<br><i>Document généré par le module Impression</i>"
        html += "</body></html>"
        doc = QTextDocument()
        doc.setHtml(html)
        doc.print_(printer)
