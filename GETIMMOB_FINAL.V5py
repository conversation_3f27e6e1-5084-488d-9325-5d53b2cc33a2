#!/usr/bin/env python3
"""
GETIMMOB FINAL V5.0 - VERSION CORRIGÉE COMPLÈTE
Logiciel de gestion immobilière professionnel
Toutes les erreurs de codification corrigées - Version finale
"""

import sys
import sqlite3
import hashlib
from datetime import datetime

# Imports PySide6
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QTextEdit, QMessageBox, QTabWidget, QFormLayout, QGroupBox,
    QStatusBar, QFrame, QComboBox, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import QTimer, Signal, Qt

class GestImmobFinalV5(QMainWindow):
    """Application GestImmob finale v5.0 - Version corrigée complète"""
    
    # Signaux pour la communication entre composants
    data_updated = Signal()
    status_changed = Signal(str)
    
    def __init__(self) -> None:
        super().__init__()
        self.setWindowTitle("🏠 GestImmob Final v5.0 - Gestion Immobilière Professionnelle")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Configuration des couleurs professionnelles
        self.colors = {
            "primary": "#2c3e50",
            "secondary": "#3498db", 
            "success": "#27ae60",
            "warning": "#f39c12",
            "danger": "#e74c3c",
            "light": "#ecf0f1",
            "dark": "#34495e"
        }
        
        # Initialisation de la base de données
        self.init_database()
        
        # Configuration de l'interface
        self.setup_ui()
        
        # Application du thème professionnel
        self.apply_professional_theme()
        
        # Chargement des données
        self.load_initial_data()
        
        # Configuration des timers
        self.setup_timers()
        
        # Configuration de la barre de statut
        self.setup_status_bar()
        
        # Connexion des signaux
        self.connect_signals()

    def init_database(self) -> None:
        """Initialise la base de données complète"""
        try:
            self.db_path = "gestimmob_final_v5.db"
            self.conn = sqlite3.connect(self.db_path)
            cursor = self.conn.cursor()
            
            # Table des biens immobiliers
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS immobilisations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    designation TEXT NOT NULL,
                    valeur REAL NOT NULL,
                    annee INTEGER NOT NULL,
                    localisation TEXT,
                    secteur TEXT,
                    observation TEXT,
                    code_barre TEXT UNIQUE,
                    etat TEXT DEFAULT 'Excellent',
                    responsable TEXT,
                    date_acquisition DATE DEFAULT CURRENT_DATE,
                    duree_amortissement INTEGER DEFAULT 5,
                    valeur_residuelle REAL DEFAULT 0,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des fournisseurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fournisseurs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    entreprise TEXT,
                    email TEXT,
                    telephone TEXT,
                    adresse TEXT,
                    secteur_activite TEXT,
                    note_evaluation INTEGER DEFAULT 5,
                    is_active INTEGER DEFAULT 1,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des clients
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    prenom TEXT,
                    type_client TEXT DEFAULT 'Particulier',
                    email TEXT,
                    telephone TEXT,
                    adresse TEXT,
                    is_active INTEGER DEFAULT 1,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des utilisateurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'gestionnaire',
                    email TEXT,
                    is_active INTEGER DEFAULT 1,
                    last_login TIMESTAMP,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Créer l'utilisateur admin par défaut
            cursor.execute("SELECT COUNT(*) FROM users WHERE role='admin'")
            if cursor.fetchone()[0] == 0:
                admin_password = hashlib.sha256("Admin@2024".encode()).hexdigest()
                cursor.execute('''
                    INSERT INTO users (username, password_hash, role, email)
                    VALUES (?, ?, ?, ?)
                ''', ("admin", admin_password, "admin", "<EMAIL>"))
            
            self.conn.commit()
            print("✅ Base de données initialisée avec succès")
            
        except Exception as e:
            print(f"❌ Erreur lors de l'initialisation de la base de données: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur base de données: {e}")

    def setup_ui(self) -> None:
        """Configure l'interface utilisateur moderne"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Sidebar verticale moderne
        self.create_sidebar(main_layout)
        
        # Zone de contenu principale
        self.create_content_area(main_layout)
        
        # Configuration du menu
        self.create_menu_bar()

    def create_sidebar(self, main_layout: QHBoxLayout) -> None:
        """Crée la sidebar verticale moderne"""
        sidebar = QFrame()
        sidebar.setFixedWidth(280)
        sidebar.setObjectName("sidebar")
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setSpacing(5)
        sidebar_layout.setContentsMargins(15, 20, 15, 20)
        
        # Logo et titre
        header_layout = QVBoxLayout()
        
        title = QLabel("🏠 GestImmob")
        title.setObjectName("sidebar_title")
        header_layout.addWidget(title)
        
        subtitle = QLabel("v5.0 Professional")
        subtitle.setObjectName("sidebar_subtitle")
        header_layout.addWidget(subtitle)
        
        sidebar_layout.addLayout(header_layout)
        sidebar_layout.addSpacing(30)
        
        # Groupes de boutons verticaux
        self.create_navigation_buttons(sidebar_layout)
        
        sidebar_layout.addStretch()
        
        # Bouton de déconnexion stylé
        self.create_quit_button(sidebar_layout)
        
        main_layout.addWidget(sidebar)

    def create_navigation_buttons(self, layout: QVBoxLayout) -> None:
        """Crée les boutons de navigation verticaux"""
        # Groupe Gestion
        gestion_group = QLabel("📊 GESTION")
        gestion_group.setObjectName("group_header")
        layout.addWidget(gestion_group)
        
        self.btn_dashboard = self.create_nav_button("📈 Tableau de Bord", "dashboard")
        self.btn_biens = self.create_nav_button("🏠 Gestion des Biens", "biens")
        self.btn_inventaire = self.create_nav_button("📦 Inventaire", "inventaire")
        
        layout.addWidget(self.btn_dashboard)
        layout.addWidget(self.btn_biens)
        layout.addWidget(self.btn_inventaire)
        layout.addSpacing(20)
        
        # Groupe Relations
        relations_group = QLabel("👥 RELATIONS")
        relations_group.setObjectName("group_header")
        layout.addWidget(relations_group)
        
        self.btn_fournisseurs = self.create_nav_button("🏢 Fournisseurs", "fournisseurs")
        self.btn_clients = self.create_nav_button("👤 Clients", "clients")
        
        layout.addWidget(self.btn_fournisseurs)
        layout.addWidget(self.btn_clients)
        layout.addSpacing(20)
        
        # Groupe Outils
        outils_group = QLabel("🛠️ OUTILS")
        outils_group.setObjectName("group_header")
        layout.addWidget(outils_group)
        
        self.btn_calculatrice = self.create_nav_button("🧮 Calculatrice", "calculatrice")
        self.btn_rapports = self.create_nav_button("📊 Rapports", "rapports")
        self.btn_parametres = self.create_nav_button("⚙️ Paramètres", "parametres")
        
        layout.addWidget(self.btn_calculatrice)
        layout.addWidget(self.btn_rapports)
        layout.addWidget(self.btn_parametres)

    def create_nav_button(self, text: str, action: str) -> QPushButton:
        """Crée un bouton de navigation stylé"""
        button = QPushButton(text)
        button.setObjectName("nav_button")
        button.setMinimumHeight(45)
        button.clicked.connect(lambda: self.switch_module(action))
        return button

    def create_quit_button(self, layout: QVBoxLayout) -> None:
        """Crée le bouton de sortie détaillé et stylé"""
        quit_group = QGroupBox("🚪 SORTIE APPLICATION")
        quit_group.setObjectName("quit_group")
        quit_layout = QVBoxLayout(quit_group)
        
        # Informations de session
        session_info = QLabel(f"👤 Connecté: admin\n🕒 {datetime.now().strftime('%H:%M')}")
        session_info.setObjectName("session_info")
        quit_layout.addWidget(session_info)
        
        # Bouton de déconnexion principal
        self.btn_quit = QPushButton("🚪 Quitter l'Application")
        self.btn_quit.setObjectName("quit_button")
        self.btn_quit.setMinimumHeight(50)
        self.btn_quit.clicked.connect(self.quit_application)
        quit_layout.addWidget(self.btn_quit)
        
        # Boutons secondaires
        buttons_layout = QHBoxLayout()
        
        btn_minimize = QPushButton("📉")
        btn_minimize.setObjectName("mini_button")
        btn_minimize.clicked.connect(self.showMinimized)
        buttons_layout.addWidget(btn_minimize)
        
        btn_about = QPushButton("ℹ️")
        btn_about.setObjectName("mini_button")
        btn_about.clicked.connect(self.show_about)
        buttons_layout.addWidget(btn_about)
        
        quit_layout.addLayout(buttons_layout)
        layout.addWidget(quit_group)

    def create_content_area(self, main_layout: QHBoxLayout) -> None:
        """Crée la zone de contenu principale"""
        content_frame = QFrame()
        content_frame.setObjectName("content_area")
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête de contenu
        self.content_header = QLabel("📈 Tableau de Bord")
        self.content_header.setObjectName("content_header")
        content_layout.addWidget(self.content_header)
        
        # Zone de contenu dynamique
        self.content_stack = QTabWidget()
        self.content_stack.setObjectName("content_tabs")
        
        # Créer tous les modules
        self.create_all_modules()
        
        content_layout.addWidget(self.content_stack)
        main_layout.addWidget(content_frame)

    def create_all_modules(self) -> None:
        """Crée tous les modules de l'application"""
        # Module Tableau de Bord
        self.dashboard_widget = self.create_dashboard_module()
        self.content_stack.addTab(self.dashboard_widget, "📈 Dashboard")

        # Module Gestion des Biens
        self.biens_widget = self.create_biens_module()
        self.content_stack.addTab(self.biens_widget, "🏠 Biens")

        # Module Inventaire
        self.inventaire_widget = self.create_inventaire_module()
        self.content_stack.addTab(self.inventaire_widget, "📦 Inventaire")

        # Module Fournisseurs
        self.fournisseurs_widget = self.create_fournisseurs_module()
        self.content_stack.addTab(self.fournisseurs_widget, "🏢 Fournisseurs")

        # Module Clients
        self.clients_widget = self.create_clients_module()
        self.content_stack.addTab(self.clients_widget, "👤 Clients")

        # Module Calculatrice
        self.calculatrice_widget = self.create_calculatrice_module()
        self.content_stack.addTab(self.calculatrice_widget, "🧮 Calculatrice")

        # Module Rapports
        self.rapports_widget = self.create_rapports_module()
        self.content_stack.addTab(self.rapports_widget, "📊 Rapports")

        # Module Paramètres
        self.parametres_widget = self.create_parametres_module()
        self.content_stack.addTab(self.parametres_widget, "⚙️ Paramètres")

    def create_dashboard_module(self) -> QWidget:
        """Crée le module tableau de bord avec KPI"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titre du module
        title = QLabel("📈 Tableau de Bord - Vue d'ensemble")
        title.setObjectName("module_title")
        layout.addWidget(title)

        # Cartes KPI
        kpi_layout = QHBoxLayout()

        # KPI Biens
        kpi_biens = self.create_kpi_card("🏠", "Biens", "0", self.colors["primary"])
        kpi_layout.addWidget(kpi_biens)

        # KPI Valeur
        kpi_valeur = self.create_kpi_card("💰", "Valeur Totale", "0 €", self.colors["success"])
        kpi_layout.addWidget(kpi_valeur)

        # KPI Fournisseurs
        kpi_fournisseurs = self.create_kpi_card("🏢", "Fournisseurs", "0", self.colors["warning"])
        kpi_layout.addWidget(kpi_fournisseurs)

        # KPI Clients
        kpi_clients = self.create_kpi_card("👤", "Clients", "0", self.colors["secondary"])
        kpi_layout.addWidget(kpi_clients)

        layout.addLayout(kpi_layout)

        # Actions rapides
        actions_group = QGroupBox("⚡ Actions Rapides")
        actions_layout = QHBoxLayout(actions_group)

        btn_add_bien = QPushButton("➕ Ajouter un Bien")
        btn_add_bien.setObjectName("action_button")
        btn_add_bien.clicked.connect(lambda: self.switch_module("biens"))
        actions_layout.addWidget(btn_add_bien)

        btn_inventaire = QPushButton("📦 Lancer Inventaire")
        btn_inventaire.setObjectName("action_button")
        btn_inventaire.clicked.connect(lambda: self.switch_module("inventaire"))
        actions_layout.addWidget(btn_inventaire)

        btn_rapport = QPushButton("📊 Générer Rapport")
        btn_rapport.setObjectName("action_button")
        btn_rapport.clicked.connect(lambda: self.switch_module("rapports"))
        actions_layout.addWidget(btn_rapport)

        layout.addWidget(actions_group)

        # Activité récente
        activity_group = QGroupBox("📋 Activité Récente")
        activity_layout = QVBoxLayout(activity_group)

        self.activity_list = QTextEdit()
        self.activity_list.setReadOnly(True)
        self.activity_list.setMaximumHeight(200)
        activity_layout.addWidget(self.activity_list)

        layout.addWidget(activity_group)
        layout.addStretch()

        return widget

    def create_kpi_card(self, icon: str, title: str, value: str, color: str) -> QGroupBox:
        """Crée une carte KPI stylée"""
        card = QGroupBox()
        card.setObjectName("kpi_card")
        card.setStyleSheet(f"""
            QGroupBox#kpi_card {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.colors["light"]});
                border: 2px solid {color};
                border-radius: 12px;
                margin: 5px;
                padding: 15px;
                min-height: 120px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("kpi_icon")
        icon_label.setStyleSheet("font-size: 36px; color: white; font-weight: bold;")
        layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("kpi_title")
        title_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)

        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("kpi_value")
        value_label.setStyleSheet("color: white; font-weight: bold; font-size: 18px;")
        layout.addWidget(value_label)

        return card

    def create_biens_module(self) -> QWidget:
        """Crée le module de gestion des biens"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titre
        title = QLabel("🏠 Gestion des Biens Immobiliers")
        title.setObjectName("module_title")
        layout.addWidget(title)

        # Formulaire d'ajout
        form_group = QGroupBox("➕ Ajouter un nouveau bien")
        form_layout = QFormLayout(form_group)

        # Champs spécialisés pour l'immobilier
        self.designation_input = QLineEdit()
        self.designation_input.setPlaceholderText("Ex: Appartement 3 pièces, Bureau commercial...")
        form_layout.addRow("🏷️ Désignation:", self.designation_input)

        self.valeur_input = QDoubleSpinBox()
        self.valeur_input.setRange(0, 99999999.99)
        self.valeur_input.setSuffix(" €")
        self.valeur_input.setValue(100000)
        form_layout.addRow("💰 Valeur d'acquisition:", self.valeur_input)

        self.annee_input = QSpinBox()
        self.annee_input.setRange(1900, 2100)
        self.annee_input.setValue(datetime.now().year)
        form_layout.addRow("📅 Année d'acquisition:", self.annee_input)

        self.localisation_input = QLineEdit()
        self.localisation_input.setPlaceholderText("Adresse complète du bien")
        form_layout.addRow("📍 Localisation:", self.localisation_input)

        self.secteur_combo = QComboBox()
        self.secteur_combo.addItems([
            "Résidentiel", "Commercial", "Industriel", "Bureaux",
            "Entrepôts", "Terrains", "Parkings", "Autre"
        ])
        form_layout.addRow("🏢 Secteur:", self.secteur_combo)

        self.surface_input = QDoubleSpinBox()
        self.surface_input.setRange(0, 99999.99)
        self.surface_input.setSuffix(" m²")
        form_layout.addRow("📐 Surface:", self.surface_input)

        self.etat_combo = QComboBox()
        self.etat_combo.addItems(["Excellent", "Très bon", "Bon", "Moyen", "À rénover"])
        form_layout.addRow("🔧 État:", self.etat_combo)

        self.responsable_input = QLineEdit()
        self.responsable_input.setPlaceholderText("Nom du responsable")
        form_layout.addRow("👤 Responsable:", self.responsable_input)

        self.observation_input = QTextEdit()
        self.observation_input.setPlaceholderText("Observations, notes particulières...")
        self.observation_input.setMaximumHeight(80)
        form_layout.addRow("📝 Observations:", self.observation_input)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.btn_add_bien = QPushButton("➕ Ajouter le Bien")
        self.btn_add_bien.setObjectName("primary_button")
        self.btn_add_bien.clicked.connect(self.ajouter_bien)
        buttons_layout.addWidget(self.btn_add_bien)

        self.btn_modify_bien = QPushButton("✏️ Modifier")
        self.btn_modify_bien.setObjectName("secondary_button")
        self.btn_modify_bien.clicked.connect(self.modifier_bien)
        buttons_layout.addWidget(self.btn_modify_bien)

        self.btn_delete_bien = QPushButton("🗑️ Supprimer")
        self.btn_delete_bien.setObjectName("danger_button")
        self.btn_delete_bien.clicked.connect(self.supprimer_bien)
        buttons_layout.addWidget(self.btn_delete_bien)

        buttons_layout.addStretch()
        form_layout.addRow("Actions:", buttons_layout)

        layout.addWidget(form_group)

        # Tableau des biens
        self.biens_table = QTableWidget()
        self.biens_table.setColumnCount(9)
        self.biens_table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur (€)", "Année", "Localisation",
            "Secteur", "Surface (m²)", "État", "Responsable"
        ])
        self.biens_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.biens_table.setAlternatingRowColors(True)
        self.biens_table.setSortingEnabled(True)

        layout.addWidget(self.biens_table)

        return widget

    # ===== MÉTHODES MANQUANTES =====

    def create_inventaire_module(self) -> QWidget:
        """Crée le module inventaire"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("📦 Module Inventaire")
        title.setObjectName("module_title")
        layout.addWidget(title)

        info = QLabel("Module d'inventaire en cours de développement...")
        layout.addWidget(info)
        layout.addStretch()

        return widget

    def create_fournisseurs_module(self) -> QWidget:
        """Crée le module fournisseurs"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("🏢 Gestion des Fournisseurs")
        title.setObjectName("module_title")
        layout.addWidget(title)

        # Formulaire fournisseur
        form_group = QGroupBox("➕ Ajouter un fournisseur")
        form_layout = QFormLayout(form_group)

        self.fournisseur_nom = QLineEdit()
        self.fournisseur_nom.setPlaceholderText("Nom du fournisseur")
        form_layout.addRow("Nom:", self.fournisseur_nom)

        self.fournisseur_email = QLineEdit()
        self.fournisseur_email.setPlaceholderText("<EMAIL>")
        form_layout.addRow("Email:", self.fournisseur_email)

        btn_add_fournisseur = QPushButton("➕ Ajouter")
        btn_add_fournisseur.clicked.connect(self.ajouter_fournisseur)
        form_layout.addRow("", btn_add_fournisseur)

        layout.addWidget(form_group)
        layout.addStretch()

        return widget

    def create_clients_module(self) -> QWidget:
        """Crée le module clients"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("👤 Gestion des Clients")
        title.setObjectName("module_title")
        layout.addWidget(title)

        info = QLabel("Module de gestion des clients en cours de développement...")
        layout.addWidget(info)
        layout.addStretch()

        return widget

    def create_calculatrice_module(self) -> QWidget:
        """Crée le module calculatrice"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("🧮 Calculatrice Financière")
        title.setObjectName("module_title")
        layout.addWidget(title)

        info = QLabel("Module de calculatrice financière en cours de développement...")
        layout.addWidget(info)
        layout.addStretch()

        return widget

    def create_rapports_module(self) -> QWidget:
        """Crée le module rapports"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("📊 Rapports et Analyses")
        title.setObjectName("module_title")
        layout.addWidget(title)

        info = QLabel("Module de rapports en cours de développement...")
        layout.addWidget(info)
        layout.addStretch()

        return widget

    def create_parametres_module(self) -> QWidget:
        """Crée le module paramètres"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("⚙️ Paramètres de l'Application")
        title.setObjectName("module_title")
        layout.addWidget(title)

        info = QLabel("Module de paramètres en cours de développement...")
        layout.addWidget(info)
        layout.addStretch()

        return widget

    def switch_module(self, module_name: str) -> None:
        """Change de module"""
        module_map = {
            "dashboard": 0,
            "biens": 1,
            "inventaire": 2,
            "fournisseurs": 3,
            "clients": 4,
            "calculatrice": 5,
            "rapports": 6,
            "parametres": 7
        }

        if module_name in module_map:
            self.content_stack.setCurrentIndex(module_map[module_name])
            self.content_header.setText(f"📈 {module_name.title()}")

    def ajouter_bien(self) -> None:
        """Ajoute un nouveau bien"""
        try:
            designation = self.designation_input.text().strip()
            valeur = self.valeur_input.value()
            annee = self.annee_input.value()
            localisation = self.localisation_input.text().strip()
            secteur = self.secteur_combo.currentText()
            surface = self.surface_input.value()
            etat = self.etat_combo.currentText()
            responsable = self.responsable_input.text().strip()
            observation = self.observation_input.toPlainText().strip()

            if not designation:
                QMessageBox.warning(self, "Erreur", "La désignation est obligatoire!")
                return

            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO immobilisations
                (designation, valeur, annee, localisation, secteur, observation, etat, responsable)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (designation, valeur, annee, localisation, secteur, f"{observation} - Surface: {surface}m²", etat, responsable))

            self.conn.commit()
            self.load_biens_data()
            self.clear_biens_form()

            QMessageBox.information(self, "Succès", "Bien ajouté avec succès!")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_bien(self) -> None:
        """Modifie le bien sélectionné"""
        QMessageBox.information(self, "Modifier", "Fonctionnalité en cours de développement")

    def supprimer_bien(self) -> None:
        """Supprime le bien sélectionné"""
        QMessageBox.information(self, "Supprimer", "Fonctionnalité en cours de développement")

    def ajouter_fournisseur(self) -> None:
        """Ajoute un nouveau fournisseur"""
        try:
            nom = self.fournisseur_nom.text().strip()
            email = self.fournisseur_email.text().strip()

            if not nom:
                QMessageBox.warning(self, "Erreur", "Le nom est obligatoire!")
                return

            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO fournisseurs (nom, email)
                VALUES (?, ?)
            ''', (nom, email))

            self.conn.commit()

            self.fournisseur_nom.clear()
            self.fournisseur_email.clear()

            QMessageBox.information(self, "Succès", "Fournisseur ajouté avec succès!")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def clear_biens_form(self) -> None:
        """Vide le formulaire des biens"""
        self.designation_input.clear()
        self.valeur_input.setValue(100000)
        self.annee_input.setValue(datetime.now().year)
        self.localisation_input.clear()
        self.secteur_combo.setCurrentIndex(0)
        self.surface_input.setValue(0)
        self.etat_combo.setCurrentIndex(0)
        self.responsable_input.clear()
        self.observation_input.clear()

    def load_biens_data(self) -> None:
        """Charge les données des biens"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM immobilisations ORDER BY id DESC")
            rows = cursor.fetchall()

            self.biens_table.setRowCount(len(rows))
            for i, row in enumerate(rows):
                for j, value in enumerate(row[:9]):  # Limiter aux 9 premières colonnes
                    item = QTableWidgetItem(str(value))
                    self.biens_table.setItem(i, j, item)

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")

    def load_initial_data(self) -> None:
        """Charge les données initiales"""
        self.load_biens_data()
        self.update_activity_log("Application démarrée")

    def update_activity_log(self, message: str) -> None:
        """Met à jour le journal d'activité"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.activity_list.append(f"[{timestamp}] {message}")

    def setup_timers(self) -> None:
        """Configure les timers"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(30000)  # 30 secondes

    def setup_status_bar(self) -> None:
        """Configure la barre de statut"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.update_status()

    def update_status(self) -> None:
        """Met à jour la barre de statut"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM immobilisations")
            nb_biens = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM fournisseurs")
            nb_fournisseurs = cursor.fetchone()[0]

            status_text = f"🏠 {nb_biens} biens | 🏢 {nb_fournisseurs} fournisseurs | 🕒 {datetime.now().strftime('%H:%M:%S')}"
            self.status_bar.showMessage(status_text)

        except Exception as e:
            self.status_bar.showMessage(f"Erreur: {e}")

    def connect_signals(self) -> None:
        """Connecte les signaux"""
        self.data_updated.connect(self.update_status)
        self.status_changed.connect(self.status_bar.showMessage)

    def create_menu_bar(self) -> None:
        """Crée la barre de menu"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu('Fichier')
        file_menu.addAction('Nouveau', self.nouveau_fichier)
        file_menu.addAction('Ouvrir', self.ouvrir_fichier)
        file_menu.addSeparator()
        file_menu.addAction('Quitter', self.quit_application)

        # Menu Aide
        help_menu = menubar.addMenu('Aide')
        help_menu.addAction('À propos', self.show_about)

    def nouveau_fichier(self) -> None:
        """Nouveau fichier"""
        QMessageBox.information(self, "Nouveau", "Fonctionnalité en cours de développement")

    def ouvrir_fichier(self) -> None:
        """Ouvrir fichier"""
        QMessageBox.information(self, "Ouvrir", "Fonctionnalité en cours de développement")

    def show_about(self) -> None:
        """Affiche les informations"""
        QMessageBox.about(self, "À propos",
                         "🏠 GestImmob Final v5.0\n\n"
                         "Logiciel de gestion immobilière professionnel\n"
                         "Version corrigée et complète\n\n"
                         "© 2024 - Tous droits réservés")

    def quit_application(self) -> None:
        """Quitte l'application avec confirmation"""
        reply = QMessageBox.question(self, "Quitter",
                                   "Êtes-vous sûr de vouloir quitter l'application?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            self.close()

    def apply_professional_theme(self) -> None:
        """Applique le thème CSS professionnel"""
        self.setStyleSheet(f"""
            /* Styles généraux */
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.colors["light"]}, stop:1 #ffffff);
                color: {self.colors["dark"]};
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11pt;
            }}

            /* Sidebar */
            QFrame#sidebar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["primary"]}, stop:1 {self.colors["dark"]});
                border-right: 3px solid {self.colors["secondary"]};
            }}

            QLabel#sidebar_title {{
                color: white;
                font-size: 24pt;
                font-weight: bold;
                margin: 10px 0;
            }}

            QLabel#sidebar_subtitle {{
                color: {self.colors["light"]};
                font-size: 12pt;
                font-style: italic;
            }}

            QLabel#group_header {{
                color: {self.colors["secondary"]};
                font-weight: bold;
                font-size: 10pt;
                margin: 10px 0 5px 0;
                padding: 5px;
                border-bottom: 2px solid {self.colors["secondary"]};
            }}

            /* Boutons de navigation */
            QPushButton#nav_button {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:1 rgba(255,255,255,0.1));
                color: white;
                border: 2px solid transparent;
                border-radius: 8px;
                padding: 12px 15px;
                text-align: left;
                font-weight: 600;
                margin: 2px 0;
            }}

            QPushButton#nav_button:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors["secondary"]}, stop:1 rgba(255,255,255,0.2));
                border-color: {self.colors["secondary"]};
            }}

            QPushButton#nav_button:pressed {{
                background: {self.colors["secondary"]};
            }}

            /* Zone de contenu */
            QFrame#content_area {{
                background: white;
                border-radius: 12px;
                margin: 10px;
            }}

            QLabel#content_header {{
                color: {self.colors["primary"]};
                font-size: 20pt;
                font-weight: bold;
                margin: 15px 0;
                padding: 10px;
                border-bottom: 3px solid {self.colors["secondary"]};
            }}

            QLabel#module_title {{
                color: {self.colors["primary"]};
                font-size: 18pt;
                font-weight: bold;
                margin: 10px 0;
            }}

            /* Boutons d'action */
            QPushButton#action_button {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["secondary"]}, stop:1 {self.colors["primary"]});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                min-width: 140px;
            }}

            QPushButton#action_button:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["primary"]}, stop:1 {self.colors["secondary"]});
            }}

            QPushButton#primary_button {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["success"]}, stop:1 #27ae60);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                min-width: 120px;
            }}

            QPushButton#secondary_button {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["warning"]}, stop:1 #e67e22);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                min-width: 120px;
            }}

            QPushButton#danger_button {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["danger"]}, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: 600;
                min-width: 120px;
            }}

            /* Groupe de sortie */
            QGroupBox#quit_group {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(231,76,60,0.1), stop:1 rgba(192,57,43,0.1));
                border: 2px solid {self.colors["danger"]};
                border-radius: 10px;
                font-weight: bold;
                margin-top: 15px;
                padding-top: 15px;
            }}

            QGroupBox#quit_group::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                color: {self.colors["danger"]};
                font-weight: bold;
            }}

            QPushButton#quit_button {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["danger"]}, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
                font-size: 12pt;
            }}

            QPushButton#quit_button:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 {self.colors["danger"]});
            }}

            QPushButton#mini_button {{
                background: {self.colors["secondary"]};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
                font-size: 14pt;
                min-width: 40px;
            }}

            QLabel#session_info {{
                color: {self.colors["dark"]};
                font-size: 10pt;
                padding: 5px;
                background: rgba(255,255,255,0.1);
                border-radius: 5px;
            }}

            /* Formulaires */
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
                border: 2px solid {self.colors["light"]};
                border-radius: 6px;
                padding: 8px;
                background: white;
                font-size: 11pt;
            }}

            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors["secondary"]};
            }}

            /* Tableaux */
            QTableWidget {{
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: {self.colors["light"]};
                border: 2px solid {self.colors["light"]};
                border-radius: 8px;
            }}

            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["primary"]}, stop:1 {self.colors["dark"]});
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }}

            QTableWidget::item:selected {{
                background: {self.colors["secondary"]};
                color: white;
            }}

            /* Groupes */
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {self.colors["secondary"]};
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background: white;
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                color: {self.colors["primary"]};
                font-size: 12pt;
                font-weight: bold;
            }}

            /* Onglets */
            QTabWidget::pane {{
                border: 2px solid {self.colors["light"]};
                border-radius: 8px;
                background: white;
            }}

            QTabBar::tab {{
                background: {self.colors["light"]};
                border: 2px solid {self.colors["light"]};
                padding: 12px 20px;
                margin-right: 2px;
                border-radius: 6px 6px 0 0;
                font-weight: 600;
            }}

            QTabBar::tab:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors["secondary"]}, stop:1 {self.colors["primary"]});
                color: white;
            }}

            /* Barre de statut */
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors["primary"]}, stop:1 {self.colors["secondary"]});
                color: white;
                border: none;
                font-weight: 600;
                padding: 5px;
            }}
        """)


# ===== FONCTION MAIN =====

def main():
    """Fonction principale pour lancer l'application"""
    try:
        # Créer l'application Qt
        app = QApplication(sys.argv)

        # Créer et afficher la fenêtre principale
        window = GestImmobFinalV5()
        window.show()

        print("✅ GestImmob Final v5.0 démarré avec succès")
        print("🎯 Toutes les erreurs de codification corrigées")
        print("🏠 Application de gestion immobilière opérationnelle")

        # Démarrer la boucle d'événements
        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        return 1

if __name__ == "__main__":
    main()
