﻿# -*- coding: utf-8 -*-
"""
Module de Gestion de l'Inventaire
Encodage: UTF-8 avec BOM (Byte Order Mark)
Système: Windows - Support complet des caractères français
Version: 3.0.0 - Encodage renforcé
"""

# Configuration d'encodage renforcée pour Windows
import sys
import os
import locale

# Forcer l'encodage UTF-8 sur Windows
try:
    # Configuration locale
    locale.setlocale(locale.LC_ALL, 'fr_FR.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'French_France.1252')
    except:
        pass

# Configuration des flux de sortie
if sys.platform.startswith('win'):
    import io
    import codecs

    # Redirection des flux avec UTF-8
    if hasattr(sys.stdout, 'buffer'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'buffer'):
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

    # Configuration de l'environnement
    os.environ['PYTHONIOENCODING'] = 'utf-8'

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox,
    QFileDialog, QInputDialog, QLabel, QTextEdit, QSpinBox, QDoubleSpinBox
)
from PySide6.QtCore import QTimer, Qt, QLocale
from PySide6.QtGui import QColor, QCloseEvent, QFont, QPalette
import logging
import requests
import json
import csv
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

# Imports conditionnels pour les modules optionnels
try:
    from modules.sql_manager import SQLManager
except ImportError:
    SQLManager = None

try:
    from modules.settings_manager import SettingsManager
except ImportError:
    SettingsManager = None


class InventaireWidget(QWidget):
    """
    Widget principal pour la gestion de l'inventaire avec support UTF-8 complet.
    Supporte tous les caractères français et émojis.
    """

    def __init__(self) -> None:
        super().__init__()

        # Configuration de l'encodage et de la locale
        self._configurer_encodage()

        # Configuration de l'interface
        self.setWindowTitle("📦 Gestion de l'Inventaire - Système Immobilier (UTF-8)")
        self.resize(1800, 1000)

        # Configuration de la police Unicode
        self._configurer_police()

        # Création de l'interface
        self._creer_interface()

        # Initialisation des données
        self._initialiser_donnees()

        # Message de confirmation d'encodage
        self._afficher_confirmation_encodage()

    def _configurer_encodage(self) -> None:
        """Configure l'encodage UTF-8 pour l'application."""
        try:
            # Configuration de la locale Qt
            QLocale.setDefault(QLocale(QLocale.Language.French, QLocale.Country.France))

            # Test d'encodage
            test_chars = "àáâäèéêëìíîïòóôöùúûüçñ"
            test_emojis = "📦🔄✅⚠️📊🗑️➕✏️📱🌐📄📋🔍"

            print(f"✅ Test d'encodage réussi: {test_chars}")
            print(f"✅ Test d'émojis réussi: {test_emojis}")

        except Exception as e:
            print(f"⚠️ Avertissement encodage: {e}")

    def _configurer_police(self) -> None:
        """Configure une police supportant Unicode."""
        # Polices avec support Unicode complet
        polices_unicode = [
            "Segoe UI", "Arial Unicode MS", "Lucida Grande",
            "DejaVu Sans", "Liberation Sans", "Noto Sans"
        ]

        for nom_police in polices_unicode:
            font = QFont(nom_police, 10)
            if font.exactMatch():
                font.setStyleHint(QFont.StyleHint.SansSerif)
                font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)
                self.setFont(font)
                print(f"✅ Police Unicode configurée: {nom_police}")
                break
        else:
            # Police par défaut
            font = QFont()
            font.setPointSize(10)
            self.setFont(font)
            print("⚠️ Police par défaut utilisée")

    def _creer_interface(self) -> None:
        """Crée l'interface utilisateur avec support UTF-8."""
        layout = QVBoxLayout(self)

        # Titre avec test de caractères français
        titre_html = """
        <h2 style='color: #2c3e50; text-align: center;'>
        📋 Gestion Complète de l'Inventaire
        </h2>
        <p style='text-align: center; color: #7f8c8d;'>
        <b>Test d'encodage UTF-8:</b><br>
        Caractères français: àáâäèéêëìíîïòóôöùúûüçñ<br>
        Émojis: 📦🔄✅⚠️📊🗑️➕✏️📱🌐📄📋🔍<br>
        Mots: Réfrigérateur • Électroménager • Quantité • Créé • Modifié
        </p>
        """

        title_label = QLabel(titre_html)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

        # Table avec en-têtes français complets
        self.table = QTableWidget(0, 20)
        headers_francais = [
            "🆔 ID", "📝 Désignation", "📂 Catégorie", "🔢 Quantité", "📏 Unité",
            "📍 Emplacement", "📅 Date d'entrée", "📅 Date de sortie", "💰 Valeur €",
            "📊 Statut", "👤 Responsable", "🕒 Dernière modif.", "⚠️ Alertes",
            "🏷️ Code-barres", "🔖 Type code", "📷 Photo", "📋 Historique",
            "📈 Écart (%)", "🏢 Fournisseur", "📞 Contact"
        ]
        self.table.setHorizontalHeaderLabels(headers_francais)

        # Style de la table
        self.table.setAlternatingRowColors(True)
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border: 1px solid #2980b9;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.table)

        # Boutons avec émojis et texte français
        self._creer_boutons(layout)

    def _creer_boutons(self, layout: QVBoxLayout) -> None:
        """Crée les boutons avec émojis et texte français."""
        btn_layout = QHBoxLayout()

        # Définition des boutons avec émojis
        boutons_config = [
            ("add_btn", "➕ Ajouter", "#27ae60", self.ajouter_inventaire),
            ("edit_btn", "✏️ Modifier", "#f39c12", self.modifier_inventaire),
            ("delete_btn", "🗑️ Supprimer", "#e74c3c", self.supprimer_inventaire),
            ("sync_btn", "🔄 Synchroniser", "#3498db", self.synchroniser),
            ("scan_btn", "📱 Scanner", "#9b59b6", self.scanner_code_barres),
            ("chart_btn", "📊 Graphiques", "#1abc9c", self.afficher_charts),
            ("import_btn", "🌐 Importer", "#34495e", self.importer_donnees_internet),
            ("excel_btn", "📄 Excel", "#2ecc71", self.exporter_excel),
            ("csv_btn", "📋 CSV", "#e67e22", self.exporter_csv),
            ("filter_btn", "🔍 Filtrer", "#8e44ad", self.filtrer_table)
        ]

        for attr_name, text, color, callback in boutons_config:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 11px;
                }}
                QPushButton:hover {{
                    background-color: {self._darken_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self._darken_color(color, 0.3)};
                }}
            """)
            btn.clicked.connect(callback)
            setattr(self, attr_name, btn)
            btn_layout.addWidget(btn)

        layout.addLayout(btn_layout)

    def _darken_color(self, hex_color: str, factor: float = 0.2) -> str:
        """Assombrit une couleur hexadécimale."""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def _initialiser_donnees(self) -> None:
        """Initialise les données et la configuration."""
        # Configuration des données
        self.changements_detectes = False
        self.fichier_persistance = "inventaire_utf8_bom.json"

        # Initialisation des managers optionnels
        try:
            if SQLManager:
                self.sql_manager = SQLManager("inventaire.db")
            else:
                self.sql_manager = None
        except Exception as e:
            print(f"⚠️ Erreur SQLManager: {e}")
            self.sql_manager = None

        try:
            if SettingsManager:
                self.settings = SettingsManager()
            else:
                self.settings = None
        except Exception as e:
            print(f"⚠️ Erreur SettingsManager: {e}")
            self.settings = None

        # Configuration du timer de sauvegarde automatique
        self.timer_sync = QTimer()
        self.timer_sync.timeout.connect(self.sauvegarde_auto)
        self.timer_sync.start(180000)  # 3 minutes

        # Connexion des signaux de la table
        self.table.itemChanged.connect(self.alerte_modification)

        # Chargement des données
        self.charger_inventaire()

    def _afficher_confirmation_encodage(self) -> None:
        """Affiche un message de confirmation d'encodage."""
        try:
            # Test complet d'encodage
            test_complet = {
                'caracteres_francais': 'àáâäèéêëìíîïòóôöùúûüçñ',
                'emojis': '📦🔄✅⚠️📊🗑️➕✏️📱🌐📄📋🔍',
                'mots_francais': ['Réfrigérateur', 'Électroménager', 'Quantité', 'Créé', 'Modifié'],
                'caracteres_speciaux': '€ • ™ © ® ° ± × ÷'
            }

            message = "✅ Module d'inventaire initialisé avec encodage UTF-8 + BOM\n\n"
            message += f"🔤 Caractères français: {test_complet['caracteres_francais']}\n"
            message += f"😀 Émojis: {test_complet['emojis']}\n"
            message += f"📝 Mots: {' • '.join(test_complet['mots_francais'])}\n"
            message += f"🔣 Spéciaux: {test_complet['caracteres_speciaux']}\n\n"
            message += "🎯 Toutes les fonctionnalités d'inventaire sont disponibles!"

            QMessageBox.information(self, "🎉 Encodage UTF-8 Confirmé", message)

        except Exception as e:
            print(f"⚠️ Erreur lors de l'affichage de confirmation: {e}")

    def charger_inventaire(self) -> None:
        """Charge l'inventaire depuis un fichier JSON avec encodage UTF-8."""
        try:
            with open(self.fichier_persistance, "r", encoding="utf-8-sig", errors="replace") as f:
                articles = json.load(f)
                self.table.setRowCount(0)  # Vider la table
                for article in articles:
                    row = self.table.rowCount()
                    self.table.insertRow(row)
                    for col, value in enumerate(article):
                        if col < self.table.columnCount():
                            # Assurer que la valeur est en UTF-8
                            text_value = str(value) if value is not None else ""
                            self.table.setItem(row, col, QTableWidgetItem(text_value))
            print(f"✅ Inventaire chargé: {self.table.rowCount()} articles")
        except FileNotFoundError:
            # Créer des données d'exemple avec caractères français
            self.creer_donnees_exemple()
        except Exception as e:
            print(f"⚠️ Erreur lors du chargement: {e}")
            self.creer_donnees_exemple()

    def creer_donnees_exemple(self) -> None:
        """Crée des données d'exemple avec caractères français pour tester l'encodage."""
        donnees_exemple = [
            ["1", "Réfrigérateur Samsung", "Électroménager", "2", "pièce", "Cuisine principale",
             "2024-01-15", "", "850.50", "En stock", "Marie Dupré",
             "✅ Créé", "", "123456789012", "EAN-13", "", "Achat initial", "0", "Samsung France", "***********.89"],
            ["2", "Chaise ergonomique", "Mobilier", "10", "pièce", "Bureau étage",
             "2024-02-01", "", "120.00", "En stock", "Jean-François",
             "✅ Créé", "", "987654321098", "QR", "", "Commande février", "0", "IKEA", "***********.32"],
            ["3", "Café moulu Arabica", "Consommable", "5", "kg", "Réserve cuisine",
             "2024-03-10", "", "25.75", "Réformé", "Amélie Dubois",
             "⚠️ Périmé", "⚠️ Périmé le 15/03/2024", "456789123456", "Code128", "", "Stock café", "2.5", "Café de France", "***********.44"],
            ["4", "Ordinateur portable", "Équipement", "1", "pièce", "Bureau direction",
             "2024-01-20", "", "1200.00", "En maintenance", "Stéphane",
             "🔧 En réparation", "Écran défaillant", "789123456789", "DataMatrix", "", "Achat janvier", "0", "Dell", "***********.88"],
            ["5", "Papier A4 recyclé", "Consommable", "50", "paquet", "Réserve bureau",
             "2024-03-01", "", "3.50", "En stock", "Françoise",
             "✅ Créé", "", "321654987321", "UPC", "", "Stock papeterie", "0", "Clairefontaine", "***********.77"]
        ]

        for article in donnees_exemple:
            row = self.table.rowCount()
            self.table.insertRow(row)
            for col, value in enumerate(article):
                if col < self.table.columnCount():
                    self.table.setItem(row, col, QTableWidgetItem(str(value)))

        self.sauvegarder_inventaire()
        print("✅ Données d'exemple créées avec caractères français complets!")

    def sauvegarder_inventaire(self) -> None:
        """Sauvegarde l'inventaire dans un fichier JSON avec encodage UTF-8."""
        try:
            articles: List[List[str]] = []
            for row in range(self.table.rowCount()):
                article: List[str] = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    text = item.text() if item else ""
                    article.append(text)
                articles.append(article)

            with open(self.fichier_persistance, "w", encoding="utf-8-sig", errors="replace") as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)

            print(f"✅ Inventaire sauvegardé avec UTF-8+BOM: {len(articles)} articles")
        except Exception as e:
            QMessageBox.critical(self, "❌ Erreur de sauvegarde",
                               f"Impossible de sauvegarder: {e}")

    def sauvegarde_auto(self) -> None:
        """Sauvegarde automatique avec support UTF-8."""
        if self.changements_detectes:
            try:
                self.sauvegarder_inventaire()
                print("🔄 Sauvegarde automatique effectuée")
                self.changements_detectes = False
            except Exception as e:
                print(f"⚠️ Erreur sauvegarde auto: {e}")

    def alerte_modification(self, item: QTableWidgetItem) -> None:
        """Détecte les modifications avec validation UTF-8."""
        try:
            self.changements_detectes = True
            # Validation basique pour éviter les erreurs
            if item and hasattr(item, 'text'):
                text = item.text()
                print(f"📝 Modification détectée: {text}")
        except Exception as e:
            print(f"⚠️ Erreur lors de la modification: {e}")

    def ajouter_inventaire(self) -> None:
        """Ajoute un nouvel article à l'inventaire avec support UTF-8."""
        dialog = QDialog(self)
        dialog.setWindowTitle("➕ Ajouter un article d'inventaire")
        dialog.resize(600, 700)
        form = QFormLayout()

        # Champs de saisie avec caractères français
        designation = QLineEdit()
        designation.setPlaceholderText("Ex: Réfrigérateur, Café, Ordinateur...")

        categorie = QComboBox()
        categorie.addItems([
            "Électroménager", "Mobilier", "Consommable", "Matériel",
            "Produit fini", "Matière première", "Équipement", "Autre"
        ])

        quantite = QLineEdit()
        quantite.setPlaceholderText("Ex: 10")

        unite = QComboBox()
        unite.addItems(["pièce", "kg", "m", "l", "m²", "m³", "paquet", "autre"])

        emplacement = QLineEdit()
        emplacement.setPlaceholderText("Ex: Bureau étage, Réserve cuisine...")

        date_entree = QLineEdit()
        date_entree.setPlaceholderText("YYYY-MM-DD")
        date_entree.setText(datetime.now().strftime("%Y-%m-%d"))

        date_sortie = QLineEdit()
        date_sortie.setPlaceholderText("YYYY-MM-DD (optionnel)")

        valeur = QLineEdit()
        valeur.setPlaceholderText("Ex: 150.50")

        statut = QComboBox()
        statut.addItems(["En stock", "Sorti", "Réformé", "En maintenance", "Périmé"])

        responsable = QLineEdit()
        responsable.setPlaceholderText("Ex: Marie Dupré, Jean-François...")

        code_barres = QLineEdit()
        code_barres.setPlaceholderText("Code-barres ou QR code")

        type_code_barres = QComboBox()
        type_code_barres.addItems(["EAN-13", "QR", "Code128", "UPC", "DataMatrix", "PDF417", "Autre"])

        photo_path = QLineEdit()
        photo_btn = QPushButton("📷 Choisir photo")

        historique = QLineEdit()
        historique.setPlaceholderText("Historique des modifications")

        ecart = QLineEdit()
        ecart.setPlaceholderText("% d'écart (ex: 2.5)")
        ecart.setText("0")

        fournisseur = QLineEdit()
        fournisseur.setPlaceholderText("Ex: Samsung France, IKEA...")

        contact = QLineEdit()
        contact.setPlaceholderText("Ex: ***********.89")

        def choisir_photo() -> None:
            file, _ = QFileDialog.getOpenFileName(
                dialog, "Choisir une photo", "",
                "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
            )
            if file:
                photo_path.setText(file)

        photo_btn.clicked.connect(choisir_photo)

        # Ajout des champs au formulaire avec labels français et émojis
        form.addRow("📝 Désignation:", designation)
        form.addRow("📂 Catégorie:", categorie)
        form.addRow("🔢 Quantité:", quantite)
        form.addRow("📏 Unité:", unite)
        form.addRow("📍 Emplacement:", emplacement)
        form.addRow("📅 Date d'entrée:", date_entree)
        form.addRow("📅 Date de sortie:", date_sortie)
        form.addRow("💰 Valeur (€):", valeur)
        form.addRow("📊 Statut:", statut)
        form.addRow("👤 Responsable:", responsable)
        form.addRow("🏷️ Code-barres:", code_barres)
        form.addRow("🔖 Type code-barres:", type_code_barres)
        form.addRow("📷 Photo:", photo_path)
        form.addRow("", photo_btn)
        form.addRow("📋 Historique:", historique)
        form.addRow("⚠️ Écart (%):", ecart)
        form.addRow("🏢 Fournisseur:", fournisseur)
        form.addRow("📞 Contact:", contact)

        # Boutons
        btn_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)

        if dialog.exec() == 1:
            if not designation.text().strip() or not quantite.text().strip():
                QMessageBox.warning(self, "⚠️ Erreur",
                                  "Désignation et quantité sont obligatoires.")
                return

            row = self.table.rowCount()
            self.table.insertRow(row)

            # Ajout avec support UTF-8 complet
            self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
            self.table.setItem(row, 1, QTableWidgetItem(designation.text()))
            self.table.setItem(row, 2, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(quantite.text()))
            self.table.setItem(row, 4, QTableWidgetItem(unite.currentText()))
            self.table.setItem(row, 5, QTableWidgetItem(emplacement.text()))
            self.table.setItem(row, 6, QTableWidgetItem(date_entree.text()))
            self.table.setItem(row, 7, QTableWidgetItem(date_sortie.text()))
            self.table.setItem(row, 8, QTableWidgetItem(valeur.text()))
            self.table.setItem(row, 9, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 10, QTableWidgetItem(responsable.text()))
            self.table.setItem(row, 11, QTableWidgetItem("✅ Créé"))
            self.table.setItem(row, 12, QTableWidgetItem(""))
            self.table.setItem(row, 13, QTableWidgetItem(code_barres.text()))
            self.table.setItem(row, 14, QTableWidgetItem(type_code_barres.currentText()))
            self.table.setItem(row, 15, QTableWidgetItem(photo_path.text()))
            self.table.setItem(row, 16, QTableWidgetItem(historique.text()))
            self.table.setItem(row, 17, QTableWidgetItem(ecart.text()))
            self.table.setItem(row, 18, QTableWidgetItem(fournisseur.text()))
            self.table.setItem(row, 19, QTableWidgetItem(contact.text()))

            self.changements_detectes = True
            self.sauvegarder_inventaire()
            QMessageBox.information(self, "✅ Succès",
                                  f"Article '{designation.text()}' ajouté avec succès!")

    def modifier_inventaire(self) -> None:
        """Modifie un article existant avec support UTF-8."""
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "⚠️ Erreur",
                              "Sélectionnez une ligne à modifier.")
            return

        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("✏️ Modifier article inventaire")
        dialog.resize(600, 700)
        form = QFormLayout()

        def safe_text(item) -> str:
            return item.text() if item and hasattr(item, 'text') else ""

        # Pré-remplissage avec les valeurs existantes
        designation = QLineEdit(safe_text(self.table.item(row, 1)))

        categorie = QComboBox()
        categorie.addItems([
            "Électroménager", "Mobilier", "Consommable", "Matériel",
            "Produit fini", "Matière première", "Équipement", "Autre"
        ])
        categorie.setCurrentText(safe_text(self.table.item(row, 2)))

        quantite = QLineEdit(safe_text(self.table.item(row, 3)))

        unite = QComboBox()
        unite.addItems(["pièce", "kg", "m", "l", "m²", "m³", "paquet", "autre"])
        unite.setCurrentText(safe_text(self.table.item(row, 4)))

        emplacement = QLineEdit(safe_text(self.table.item(row, 5)))
        date_entree = QLineEdit(safe_text(self.table.item(row, 6)))
        date_sortie = QLineEdit(safe_text(self.table.item(row, 7)))
        valeur = QLineEdit(safe_text(self.table.item(row, 8)))

        statut = QComboBox()
        statut.addItems(["En stock", "Sorti", "Réformé", "En maintenance", "Périmé"])
        statut.setCurrentText(safe_text(self.table.item(row, 9)))

        responsable = QLineEdit(safe_text(self.table.item(row, 10)))
        code_barres = QLineEdit(safe_text(self.table.item(row, 13)))

        type_code_barres = QComboBox()
        type_code_barres.addItems(["EAN-13", "QR", "Code128", "UPC", "DataMatrix", "PDF417", "Autre"])
        type_code_barres.setCurrentText(safe_text(self.table.item(row, 14)))

        photo_path = QLineEdit(safe_text(self.table.item(row, 15)))
        photo_btn = QPushButton("📷 Choisir photo")
        historique = QLineEdit(safe_text(self.table.item(row, 16)))
        ecart = QLineEdit(safe_text(self.table.item(row, 17)))
        fournisseur = QLineEdit(safe_text(self.table.item(row, 18)))
        contact = QLineEdit(safe_text(self.table.item(row, 19)))

        def choisir_photo() -> None:
            file, _ = QFileDialog.getOpenFileName(
                dialog, "Choisir une photo", "",
                "Images (*.png *.jpg *.jpeg *.bmp *.gif)"
            )
            if file:
                photo_path.setText(file)

        photo_btn.clicked.connect(choisir_photo)

        # Formulaire avec labels français
        form.addRow("📝 Désignation:", designation)
        form.addRow("📂 Catégorie:", categorie)
        form.addRow("🔢 Quantité:", quantite)
        form.addRow("📏 Unité:", unite)
        form.addRow("📍 Emplacement:", emplacement)
        form.addRow("📅 Date d'entrée:", date_entree)
        form.addRow("📅 Date de sortie:", date_sortie)
        form.addRow("💰 Valeur (€):", valeur)
        form.addRow("📊 Statut:", statut)
        form.addRow("👤 Responsable:", responsable)
        form.addRow("🏷️ Code-barres:", code_barres)
        form.addRow("🔖 Type code-barres:", type_code_barres)
        form.addRow("📷 Photo:", photo_path)
        form.addRow("", photo_btn)
        form.addRow("📋 Historique:", historique)
        form.addRow("⚠️ Écart (%):", ecart)
        form.addRow("🏢 Fournisseur:", fournisseur)
        form.addRow("📞 Contact:", contact)

        btn_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)

        if dialog.exec() == 1:
            # Mise à jour avec support UTF-8
            self.table.setItem(row, 1, QTableWidgetItem(designation.text()))
            self.table.setItem(row, 2, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(quantite.text()))
            self.table.setItem(row, 4, QTableWidgetItem(unite.currentText()))
            self.table.setItem(row, 5, QTableWidgetItem(emplacement.text()))
            self.table.setItem(row, 6, QTableWidgetItem(date_entree.text()))
            self.table.setItem(row, 7, QTableWidgetItem(date_sortie.text()))
            self.table.setItem(row, 8, QTableWidgetItem(valeur.text()))
            self.table.setItem(row, 9, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 10, QTableWidgetItem(responsable.text()))
            self.table.setItem(row, 11, QTableWidgetItem("✏️ Modifié"))
            self.table.setItem(row, 13, QTableWidgetItem(code_barres.text()))
            self.table.setItem(row, 14, QTableWidgetItem(type_code_barres.currentText()))
            self.table.setItem(row, 15, QTableWidgetItem(photo_path.text()))
            self.table.setItem(row, 16, QTableWidgetItem(historique.text()))
            self.table.setItem(row, 17, QTableWidgetItem(ecart.text()))
            self.table.setItem(row, 18, QTableWidgetItem(fournisseur.text()))
            self.table.setItem(row, 19, QTableWidgetItem(contact.text()))

            self.changements_detectes = True
            self.sauvegarder_inventaire()
            QMessageBox.information(self, "✅ Succès",
                                  f"Article '{designation.text()}' modifié avec succès!")

    def supprimer_inventaire(self) -> None:
        """Supprime un article de l'inventaire."""
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "⚠️ Erreur",
                              "Sélectionnez une ligne à supprimer.")
            return

        row = selected[0].row()
        item_name = self.table.item(row, 1)
        name = item_name.text() if item_name else "Article"

        reply = QMessageBox.question(
            self, "🗑️ Confirmer suppression",
            f"Êtes-vous sûr de vouloir supprimer '{name}' ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.table.removeRow(row)
            self.changements_detectes = True
            self.sauvegarder_inventaire()
            QMessageBox.information(self, "✅ Succès",
                                  f"Article '{name}' supprimé avec succès!")

    def synchroniser(self) -> None:
        """Synchronisation complète avec support UTF-8."""
        try:
            self.sauvegarder_inventaire()
            if self.settings:
                self.settings.save()
            self.changements_detectes = False
            QMessageBox.information(
                self, "🔄 Synchronisation",
                "Synchronisation de l'inventaire effectuée avec succès!\n"
                "✅ Données sauvegardées en UTF-8+BOM\n"
                "✅ Paramètres mis à jour"
            )
        except Exception as e:
            QMessageBox.warning(self, "⚠️ Erreur de synchronisation",
                              f"Erreur lors de la synchronisation: {e}")

    def scanner_code_barres(self) -> None:
        """Scanner ou saisir un code-barres avec support UTF-8."""
        code, ok = QInputDialog.getText(
            self, "📱 Scanner code-barres",
            "Scannez ou saisissez le code-barres de l'article :\n"
            "(Supports: EAN-13, QR, Code128, UPC, DataMatrix, PDF417)"
        )

        if ok and code.strip():
            selected = self.table.selectedItems()
            if selected:
                row = selected[0].row()
                self.table.setItem(row, 13, QTableWidgetItem(code.strip()))
                self.table.setItem(row, 11, QTableWidgetItem("📱 Code scanné"))
                self.changements_detectes = True
                self.sauvegarder_inventaire()
                QMessageBox.information(
                    self, "✅ Code-barres",
                    f"Code-barres scanné et assigné: {code.strip()}"
                )
            else:
                QMessageBox.information(
                    self, "📱 Code-barres scanné",
                    f"Code-barres scanné: {code.strip()}\n"
                    "Sélectionnez un article pour l'assigner."
                )

    def afficher_charts(self) -> None:
        """Affiche des graphiques statistiques avec support UTF-8."""
        try:
            import matplotlib.pyplot as plt
            from collections import defaultdict

            # Configuration UTF-8 pour matplotlib
            plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            stats_categorie = defaultdict(int)
            stats_statut = defaultdict(int)

            def safe_text(item) -> str:
                return item.text() if item and hasattr(item, 'text') else "Non défini"

            for row in range(self.table.rowCount()):
                cat = safe_text(self.table.item(row, 2))
                statut = safe_text(self.table.item(row, 9))

                stats_categorie[cat] += 1
                stats_statut[statut] += 1

            # Graphique des catégories
            if stats_categorie:
                plt.figure(figsize=(12, 6))
                categories = list(stats_categorie.keys())
                values = list(stats_categorie.values())

                plt.bar(categories, values, color=['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'])
                plt.title("📊 Répartition des articles par catégorie", fontsize=14, fontweight='bold')
                plt.xlabel("Catégories", fontsize=12)
                plt.ylabel("Nombre d'articles", fontsize=12)
                plt.xticks(rotation=45, ha='right')
                plt.tight_layout()
                plt.show()

            # Graphique des statuts
            if stats_statut:
                plt.figure(figsize=(8, 8))
                statuts = list(stats_statut.keys())
                values = list(stats_statut.values())
                colors = ['#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#34495e']

                plt.pie(values, labels=statuts, autopct='%1.1f%%', colors=colors, startangle=90)
                plt.title("📈 Répartition des articles par statut", fontsize=14, fontweight='bold')
                plt.axis('equal')
                plt.tight_layout()
                plt.show()

        except ImportError:
            QMessageBox.warning(
                self, "📊 Module manquant",
                "Le module matplotlib n'est pas installé.\n"
                "Installez-le avec: pip install matplotlib"
            )
        except Exception as e:
            QMessageBox.warning(
                self, "📊 Erreur graphique",
                f"Erreur lors de l'affichage des graphiques: {e}"
            )

    def importer_donnees_internet(self) -> None:
        """Import de données depuis une API avec support UTF-8."""
        try:
            # Exemple avec OpenFoodFacts (API française)
            url = "https://world.openfoodfacts.org/api/v0/product/3017620422003.json"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                product = data.get("product", {})

                # Extraction des données avec caractères français
                designation = product.get("product_name_fr",
                                        product.get("product_name", "Produit importé"))
                brands = product.get("brands", "Marque inconnue")
                code_barres = product.get("code", "")

                # Ajout à l'inventaire
                row = self.table.rowCount()
                self.table.insertRow(row)

                self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
                self.table.setItem(row, 1, QTableWidgetItem(designation))
                self.table.setItem(row, 2, QTableWidgetItem("Alimentaire"))
                self.table.setItem(row, 3, QTableWidgetItem("1"))
                self.table.setItem(row, 4, QTableWidgetItem("pièce"))
                self.table.setItem(row, 5, QTableWidgetItem("Réserve"))
                self.table.setItem(row, 6, QTableWidgetItem(datetime.now().strftime("%Y-%m-%d")))
                self.table.setItem(row, 7, QTableWidgetItem(""))
                self.table.setItem(row, 8, QTableWidgetItem("0.00"))
                self.table.setItem(row, 9, QTableWidgetItem("En stock"))
                self.table.setItem(row, 10, QTableWidgetItem("Import automatique"))
                self.table.setItem(row, 11, QTableWidgetItem("🌐 Importé d'Internet"))
                self.table.setItem(row, 12, QTableWidgetItem(""))
                self.table.setItem(row, 13, QTableWidgetItem(code_barres))
                self.table.setItem(row, 14, QTableWidgetItem("EAN-13"))
                self.table.setItem(row, 15, QTableWidgetItem(""))
                self.table.setItem(row, 16, QTableWidgetItem(f"Marque: {brands}"))
                self.table.setItem(row, 17, QTableWidgetItem("0"))
                self.table.setItem(row, 18, QTableWidgetItem(brands))
                self.table.setItem(row, 19, QTableWidgetItem(""))

                self.changements_detectes = True
                self.sauvegarder_inventaire()

                QMessageBox.information(
                    self, "🌐 Import réussi",
                    f"Produit importé avec succès:\n"
                    f"📝 {designation}\n"
                    f"🏷️ Code: {code_barres}\n"
                    f"🏢 Marque: {brands}"
                )
            else:
                QMessageBox.warning(
                    self, "🌐 Erreur d'import",
                    f"Erreur lors de la récupération des données (code {response.status_code})"
                )

        except Exception as e:
            QMessageBox.warning(
                self, "🌐 Erreur d'import",
                f"Erreur lors de l'importation: {e}"
            )

    def exporter_excel(self) -> None:
        """Exporte les données vers Excel avec support UTF-8."""
        try:
            import xlsxwriter

            file, _ = QFileDialog.getSaveFileName(
                self, "📄 Exporter vers Excel", "", "Excel (*.xlsx)"
            )
            if not file:
                return

            workbook = xlsxwriter.Workbook(file)
            worksheet = workbook.add_worksheet("Inventaire")

            # Format pour les en-têtes
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#3498db',
                'font_color': 'white',
                'border': 1
            })

            # En-têtes avec caractères français
            for col in range(self.table.columnCount()):
                header = self.table.horizontalHeaderItem(col)
                header_text = header.text() if header else f"Colonne {col}"
                worksheet.write(0, col, header_text, header_format)

            # Données avec support UTF-8
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    text = item.text() if item else ""
                    worksheet.write(row+1, col, text)

            # Ajustement automatique des colonnes
            worksheet.autofit()

            workbook.close()
            QMessageBox.information(
                self, "✅ Export Excel",
                f"Export Excel terminé avec succès!\n"
                f"📁 Fichier: {file}\n"
                f"📊 {self.table.rowCount()} articles exportés"
            )

        except ImportError:
            QMessageBox.warning(
                self, "📄 Module manquant",
                "Le module xlsxwriter n'est pas installé.\n"
                "Installez-le avec: pip install xlsxwriter"
            )
        except Exception as e:
            QMessageBox.warning(
                self, "📄 Erreur d'export",
                f"Erreur lors de l'export Excel: {e}"
            )

    def exporter_csv(self) -> None:
        """Exporte les données vers CSV avec encodage UTF-8."""
        try:
            file, _ = QFileDialog.getSaveFileName(
                self, "📋 Exporter vers CSV", "", "CSV (*.csv)"
            )
            if not file:
                return

            with open(file, "w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f, delimiter=';')  # Point-virgule pour Excel français

                # En-têtes
                headers = []
                for col in range(self.table.columnCount()):
                    header = self.table.horizontalHeaderItem(col)
                    headers.append(header.text() if header else f"Colonne {col}")
                writer.writerow(headers)

                # Données
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

            QMessageBox.information(
                self, "✅ Export CSV",
                f"Export CSV terminé avec succès!\n"
                f"📁 Fichier: {file}\n"
                f"📊 {self.table.rowCount()} articles exportés\n"
                f"🔤 Encodage: UTF-8 avec BOM"
            )

        except Exception as e:
            QMessageBox.warning(
                self, "📋 Erreur d'export",
                f"Erreur lors de l'export CSV: {e}"
            )

    def filtrer_table(self) -> None:
        """Filtre la table avec support UTF-8."""
        filtre, ok = QInputDialog.getText(
            self, "🔍 Filtrer l'inventaire",
            "Entrez le texte à rechercher:\n"
            "(Recherche dans toutes les colonnes, caractères français supportés)"
        )

        if not ok:
            return

        if not filtre.strip():
            # Afficher toutes les lignes
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            QMessageBox.information(
                self, "🔍 Filtre",
                "Filtre supprimé. Tous les articles sont affichés."
            )
            return

        filtre = filtre.strip().lower()
        rows_found = 0

        for row in range(self.table.rowCount()):
            visible = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and filtre in item.text().lower():
                    visible = True
                    break

            self.table.setRowHidden(row, not visible)
            if visible:
                rows_found += 1

        QMessageBox.information(
            self, "🔍 Résultats du filtre",
            f"Filtre appliqué: '{filtre}'\n"
            f"📊 {rows_found} article(s) trouvé(s)"
        )

    def closeEvent(self, event: QCloseEvent) -> None:
        """Gestion de la fermeture avec sauvegarde UTF-8."""
        if self.changements_detectes:
            reply = QMessageBox.question(
                self, "💾 Sauvegarder",
                "Des modifications non sauvegardées ont été détectées.\n"
                "Voulez-vous sauvegarder avant de fermer?",
                QMessageBox.StandardButton.Yes |
                QMessageBox.StandardButton.No |
                QMessageBox.StandardButton.Cancel
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.sauvegarder_inventaire()
                event.accept()
            elif reply == QMessageBox.StandardButton.No:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


# Test d'encodage pour vérification
if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    print("🔍 Test d'encodage UTF-8 avec BOM")
    print("=" * 50)

    # Test des caractères français
    caracteres_francais = "àáâäèéêëìíîïòóôöùúûüçñ"
    print(f"Caractères français: {caracteres_francais}")

    # Test des émojis
    emojis = "📦🔄✅⚠️📊🗑️➕✏️📱🌐📄📋🔍"
    print(f"Émojis: {emojis}")

    # Test des mots français
    mots_francais = [
        "Réfrigérateur", "Électroménager", "Quantité", "Créé",
        "Modifié", "Périmé", "Réformé", "Café", "Amélie"
    ]
    print("Mots français:")
    for mot in mots_francais:
        print(f"  - {mot}")

    print("\n✅ Test d'encodage UTF-8 réussi!")
    print("🎯 Module d'inventaire prêt à l'utilisation!")

    # Lancement de l'application si exécuté directement
    app = QApplication(sys.argv)
    widget = InventaireWidget()
    widget.show()
    sys.exit(app.exec())