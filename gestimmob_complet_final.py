#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GestImmob v3.0.0 - Version Complète Finale
Logiciel de gestion immobilière avec TOUS les modules et interfaces modernes
Correction de tous les problèmes de code et amélioration des interfaces
"""

import sys
import sqlite3
import hashlib
import csv
from datetime import datetime
from typing import Any, List

# Imports PySide6
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QMessageBox, QTabWidget,
    QFormLayout, QGroupBox, QStatusBar, QFrame, QComboBox, QSpinBox,
    QDoubleSpinBox, QInputDialog, QDateEdit, QCheckBox, QListWidget,
    QListWidgetItem, QGridLayout, QScrollArea, QProgressBar, Q<PERSON><PERSON><PERSON><PERSON>,
    QFileDialog
)
from PySide6.QtCore import QTimer, QDate
from PySide6.QtGui import QAction

class GestImmobCompletFinal(QMainWindow):
    """Application GestImmob complète avec tous les modules et interfaces modernes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏠 GestImmob v3.0.0 - Logiciel de Gestion Immobilière Complet")
        self.setGeometry(50, 50, 1600, 1000)
        
        # Configuration fixe (sans couleurs dynamiques)
        self.primary_color = "#2c3e50"
        self.secondary_color = "#3498db"
        self.accent_color = "#e74c3c"
        self.success_color = "#27ae60"
        self.warning_color = "#f39c12"
        
        # Initialiser la base de données complète
        self.init_complete_database()
        
        # Créer l'interface complète
        self.setup_complete_ui()
        
        # Appliquer le thème fixe
        self.apply_fixed_theme()
        
        # Charger toutes les données
        self.load_all_data()
        
        # Barre de statut
        self.setup_status_bar()
        
        # Timer pour l'heure uniquement
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)
        
    def init_complete_database(self):
        """Initialise la base de données complète avec toutes les tables"""
        self.db_path = "gestimmob_complet_final.db"
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # Table des biens immobiliers (complète)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS immobilisations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                valeur REAL NOT NULL,
                annee INTEGER NOT NULL,
                localisation TEXT,
                secteur TEXT,
                observation TEXT,
                code_barre TEXT UNIQUE,
                etat TEXT DEFAULT 'Bon',
                responsable TEXT,
                date_acquisition DATE,
                duree_amortissement INTEGER DEFAULT 5,
                valeur_residuelle REAL DEFAULT 0,
                numero_serie TEXT,
                marque TEXT,
                modele TEXT,
                fournisseur_id INTEGER,
                photo_path TEXT,
                coordonnees_gps TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id)
            )
        ''')
        
        # Table des fournisseurs (complète)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                entreprise TEXT,
                email TEXT,
                telephone TEXT,
                adresse TEXT,
                ville TEXT,
                code_postal TEXT,
                pays TEXT DEFAULT 'France',
                siret TEXT,
                numero_tva TEXT,
                secteur_activite TEXT,
                site_web TEXT,
                contact_principal TEXT,
                note_evaluation INTEGER DEFAULT 5,
                conditions_paiement TEXT,
                delai_livraison INTEGER,
                is_active INTEGER DEFAULT 1,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des clients (complète)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT,
                entreprise TEXT,
                type_client TEXT DEFAULT 'Particulier',
                email TEXT,
                telephone TEXT,
                telephone_mobile TEXT,
                adresse TEXT,
                ville TEXT,
                code_postal TEXT,
                pays TEXT DEFAULT 'France',
                siret TEXT,
                numero_tva TEXT,
                date_naissance DATE,
                profession TEXT,
                revenus_annuels REAL,
                situation_familiale TEXT,
                nombre_enfants INTEGER DEFAULT 0,
                banque TEXT,
                iban TEXT,
                is_active INTEGER DEFAULT 1,
                notes TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des factures
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero_facture TEXT UNIQUE NOT NULL,
                client_id INTEGER NOT NULL,
                date_facture DATE NOT NULL,
                date_echeance DATE,
                montant_ht REAL NOT NULL,
                taux_tva REAL DEFAULT 20.0,
                montant_ttc REAL NOT NULL,
                statut TEXT DEFAULT 'En attente',
                mode_paiement TEXT,
                date_paiement DATE,
                notes TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients(id)
            )
        ''')
        
        # Table des lignes de facture
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lignes_facture (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                facture_id INTEGER NOT NULL,
                designation TEXT NOT NULL,
                quantite REAL NOT NULL,
                prix_unitaire REAL NOT NULL,
                montant_ligne REAL NOT NULL,
                FOREIGN KEY (facture_id) REFERENCES factures(id)
            )
        ''')
        
        # Table des bons de commande
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bons_commande (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero_commande TEXT UNIQUE NOT NULL,
                fournisseur_id INTEGER NOT NULL,
                date_commande DATE NOT NULL,
                date_livraison_prevue DATE,
                date_livraison_reelle DATE,
                montant_total REAL NOT NULL,
                statut TEXT DEFAULT 'En cours',
                notes TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id)
            )
        ''')
        
        # Table des réformes
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reformes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                immobilisation_id INTEGER NOT NULL,
                motif_reforme TEXT NOT NULL,
                date_proposition DATE NOT NULL,
                date_decision DATE,
                decision TEXT,
                valeur_reforme REAL,
                mode_sortie TEXT,
                beneficiaire TEXT,
                pv_numero TEXT,
                date_pv DATE,
                observations TEXT,
                statut TEXT DEFAULT 'Proposée',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (immobilisation_id) REFERENCES immobilisations(id)
            )
        ''')
        
        # Table des documents
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_fichier TEXT NOT NULL,
                chemin_fichier TEXT NOT NULL,
                type_document TEXT,
                taille_fichier INTEGER,
                date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT,
                mots_cles TEXT,
                entite_liee TEXT,
                entite_id INTEGER,
                version INTEGER DEFAULT 1,
                statut TEXT DEFAULT 'Actif'
            )
        ''')
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                nom TEXT,
                prenom TEXT,
                email TEXT,
                role TEXT NOT NULL DEFAULT 'gestionnaire',
                permissions TEXT,
                derniere_connexion TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des paramètres
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS parametres (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cle TEXT UNIQUE NOT NULL,
                valeur TEXT NOT NULL,
                description TEXT,
                categorie TEXT,
                date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Créer l'utilisateur admin si inexistant
        cursor.execute("SELECT COUNT(*) FROM users WHERE role='admin'")
        if cursor.fetchone()[0] == 0:
            admin_password = hashlib.sha256("Admin@1234".encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, nom, prenom, role, permissions)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ("admin", admin_password, "Administrateur", "Système", "admin", "ALL"))
        
        # Insérer des paramètres par défaut
        default_params = [
            ("entreprise_nom", "GestImmob Solutions", "Nom de l'entreprise", "Général"),
            ("entreprise_adresse", "123 Rue de la Gestion, 75001 Paris", "Adresse de l'entreprise", "Général"),
            ("tva_defaut", "20.0", "Taux de TVA par défaut", "Facturation"),
            ("delai_paiement", "30", "Délai de paiement par défaut (jours)", "Facturation"),
            ("duree_amortissement", "5", "Durée d'amortissement par défaut (années)", "Comptabilité")
        ]
        
        for cle, valeur, description, categorie in default_params:
            cursor.execute('''
                INSERT OR IGNORE INTO parametres (cle, valeur, description, categorie)
                VALUES (?, ?, ?, ?)
            ''', (cle, valeur, description, categorie))
        
        self.conn.commit()
        
    def apply_fixed_theme(self):
        """Applique un thème fixe et professionnel"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #2c3e50;
            }}
            
            QWidget {{
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 11pt;
            }}
            
            QLabel#title {{
                color: {self.primary_color};
                font-size: 28pt;
                font-weight: bold;
                margin: 15px;
            }}
            
            QLabel#subtitle {{
                color: #6c757d;
                font-size: 14pt;
                margin-bottom: 20px;
                font-style: italic;
            }}
            
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.primary_color}, stop:1 {self.secondary_color});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 11pt;
                min-width: 120px;
                min-height: 40px;
            }}
            
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.secondary_color}, stop:1 {self.primary_color});
                transform: translateY(-2px);
            }}
            
            QPushButton:pressed {{
                background: {self.primary_color};
                transform: translateY(0px);
            }}
            
            QPushButton#success {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.success_color}, stop:1 #229954);
            }}
            
            QPushButton#warning {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.warning_color}, stop:1 #e67e22);
            }}
            
            QPushButton#danger {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.accent_color}, stop:1 #c0392b);
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 10px;
                background: white;
                font-size: 11pt;
                selection-background-color: {self.secondary_color};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
                border-color: {self.secondary_color};
                box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
            }}
            
            QTableWidget {{
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                selection-background-color: {self.secondary_color};
            }}
            
            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.primary_color}, stop:1 {self.secondary_color});
                color: white;
                padding: 12px;
                border: none;
                font-weight: 700;
                font-size: 11pt;
            }}
            
            QTableWidget::item {{
                padding: 10px;
                border-bottom: 1px solid #dee2e6;
            }}
            
            QTableWidget::item:selected {{
                background: {self.secondary_color};
                color: white;
            }}
            
            QTabWidget::pane {{
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background: white;
                margin-top: 5px;
            }}
            
            QTabBar::tab {{
                background: #ecf0f1;
                border: 2px solid #bdc3c7;
                padding: 12px 20px;
                margin-right: 2px;
                border-radius: 6px 6px 0 0;
                font-weight: 600;
                min-width: 100px;
            }}
            
            QTabBar::tab:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.primary_color}, stop:1 {self.secondary_color});
                color: white;
                border-bottom: none;
            }}
            
            QTabBar::tab:hover {{
                background: {self.secondary_color};
                color: white;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {self.primary_color};
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background: white;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {self.primary_color};
                font-size: 12pt;
                font-weight: bold;
            }}
            
            QListWidget {{
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 5px;
            }}
            
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                border-radius: 4px;
                margin: 2px;
            }}
            
            QListWidget::item:selected {{
                background: {self.secondary_color};
                color: white;
            }}
            
            QListWidget::item:hover {{
                background: #ecf0f1;
            }}
            
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.primary_color}, stop:1 {self.secondary_color});
                color: white;
                border: none;
                font-weight: 600;
                padding: 5px;
            }}
            
            QProgressBar {{
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }}
            
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.success_color}, stop:1 #27ae60);
                border-radius: 3px;
            }}
            
            QScrollArea {{
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                background: white;
            }}
            
            QFrame#card {{
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
            
            QFrame#card:hover {{
                border-color: {self.secondary_color};
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
        """)
        
    def setup_complete_ui(self):
        """Configure l'interface utilisateur complète"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        
        # En-tête fixe
        header_layout = QHBoxLayout()
        
        # Titre
        title_layout = QVBoxLayout()
        title = QLabel("🏠 GestImmob v3.0.0")
        title.setObjectName("title")
        subtitle = QLabel("Logiciel de Gestion Immobilière Professionnel - Version Complète")
        subtitle.setObjectName("subtitle")
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Informations utilisateur
        self.user_info = QLabel()
        self.update_time_display()
        header_layout.addWidget(self.user_info)
        
        main_layout.addLayout(header_layout)
        
        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(f"border: 2px solid {self.primary_color};")
        main_layout.addWidget(separator)
        
        # Onglets principaux avec TOUS les modules
        self.tabs = QTabWidget()
        
        # Créer TOUS les onglets
        self.tabs.addTab(self.create_dashboard_tab(), "[STATS] Tableau de Bord")
        self.tabs.addTab(self.create_biens_tab(), "🏠 Gestion des Biens")
        self.tabs.addTab(self.create_inventaire_tab(), "📦 Inventaire")
        self.tabs.addTab(self.create_fournisseurs_tab(), "🏢 Fournisseurs")
        self.tabs.addTab(self.create_clients_tab(), "👥 Clients")
        self.tabs.addTab(self.create_facturation_tab(), "📄 Facturation")
        self.tabs.addTab(self.create_commandes_tab(), "📋 Bons de Commande")
        self.tabs.addTab(self.create_reformes_tab(), "♻️ Gestion Réformes")
        self.tabs.addTab(self.create_documents_tab(), "📁 Documents")
        self.tabs.addTab(self.create_calculatrice_tab(), "🧮 Calculatrice")
        self.tabs.addTab(self.create_rapports_tab(), "[STATS] Rapports")
        self.tabs.addTab(self.create_maintenance_tab(), "[OUTIL] Maintenance")
        self.tabs.addTab(self.create_parametres_tab(), "⚙️ Paramètres")
        
        main_layout.addWidget(self.tabs)
        
        # Menu complet
        self.create_complete_menu()
        
    def update_time_display(self):
        """Met à jour l'affichage de l'heure"""
        current_time = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        self.user_info.setText(f"👤 Administrateur | 🕒 {current_time}")
        self.user_info.setStyleSheet("color: #6c757d; font-size: 11pt; font-weight: 600;")

    # ===== CRÉATION DES ONGLETS AVEC INTERFACES MODERNES =====

    def create_dashboard_tab(self):
        """Crée le tableau de bord avec cartes d'information"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titre du tableau de bord
        title = QLabel("[STATS] Tableau de Bord - Vue d'ensemble")
        title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title)

        # Zone de cartes avec scroll
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)

        # Cartes d'information
        self.create_info_card(scroll_layout, 0, 0, "🏠 Biens Immobiliers", "Total des biens", "immobilisations")
        self.create_info_card(scroll_layout, 0, 1, "🏢 Fournisseurs", "Fournisseurs actifs", "fournisseurs")
        self.create_info_card(scroll_layout, 0, 2, "👥 Clients", "Clients enregistrés", "clients")
        self.create_info_card(scroll_layout, 1, 0, "📄 Factures", "Factures émises", "factures")
        self.create_info_card(scroll_layout, 1, 1, "📋 Commandes", "Bons de commande", "bons_commande")
        self.create_info_card(scroll_layout, 1, 2, "♻️ Réformes", "Réformes en cours", "reformes")

        # Graphiques et statistiques
        stats_group = QGroupBox("📈 Statistiques Rapides")
        stats_layout = QVBoxLayout(stats_group)

        # Barre de progression pour l'état du patrimoine
        patrimoine_label = QLabel("État du Patrimoine")
        self.patrimoine_progress = QProgressBar()
        self.patrimoine_progress.setValue(75)  # Exemple
        stats_layout.addWidget(patrimoine_label)
        stats_layout.addWidget(self.patrimoine_progress)

        # Répartition par secteur
        secteur_label = QLabel("Répartition par Secteur")
        self.secteur_combo = QComboBox()
        self.secteur_combo.addItems(["Informatique", "Mobilier", "Véhicules", "Équipements", "Autre"])
        stats_layout.addWidget(secteur_label)
        stats_layout.addWidget(self.secteur_combo)

        scroll_layout.addWidget(stats_group, 2, 0, 1, 3)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # Boutons d'action rapide
        actions_group = QGroupBox("[LANCE] Actions Rapides")
        actions_layout = QHBoxLayout(actions_group)

        btn_nouveau_bien = QPushButton("➕ Nouveau Bien")
        btn_nouveau_bien.setObjectName("success")
        btn_nouveau_bien.clicked.connect(lambda: self.tabs.setCurrentIndex(1))
        actions_layout.addWidget(btn_nouveau_bien)

        btn_nouvelle_facture = QPushButton("📄 Nouvelle Facture")
        btn_nouvelle_facture.setObjectName("success")
        btn_nouvelle_facture.clicked.connect(lambda: self.tabs.setCurrentIndex(5))
        actions_layout.addWidget(btn_nouvelle_facture)

        btn_inventaire = QPushButton("📦 Inventaire")
        btn_inventaire.clicked.connect(lambda: self.tabs.setCurrentIndex(2))
        actions_layout.addWidget(btn_inventaire)

        btn_rapports = QPushButton("[STATS] Rapports")
        btn_rapports.clicked.connect(lambda: self.tabs.setCurrentIndex(10))
        actions_layout.addWidget(btn_rapports)

        actions_layout.addStretch()
        layout.addWidget(actions_group)

        return widget

    def create_info_card(self, layout: QGridLayout, row: int, col: int, title: str, subtitle: str, table_name: str) -> None:
        """Crée une carte d'information"""
        card = QFrame()
        card.setObjectName("card")
        card.setFixedSize(200, 120)

        card_layout = QVBoxLayout(card)

        # Titre de la carte
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14pt; font-weight: bold; color: #2c3e50;")
        card_layout.addWidget(title_label)

        # Valeur
        cursor = self.conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]

        value_label = QLabel(str(count))
        value_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #3498db;")
        card_layout.addWidget(value_label)

        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("font-size: 10pt; color: #7f8c8d;")
        card_layout.addWidget(subtitle_label)

        layout.addWidget(card, row, col)

    def create_biens_tab(self):
        """Crée l'onglet de gestion des biens avec interface moderne"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titre
        title = QLabel("🏠 Gestion des Biens Immobiliers")
        title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title)

        # Splitter pour diviser l'interface
        splitter = QSplitter()

        # Partie gauche : Formulaire dans des cartes
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Carte d'informations générales
        info_card = QGroupBox("📝 Informations Générales")
        info_layout = QFormLayout(info_card)

        self.bien_designation = QLineEdit()
        self.bien_designation.setPlaceholderText("Ex: Ordinateur portable Dell Latitude")
        info_layout.addRow("Désignation:", self.bien_designation)

        self.bien_marque = QLineEdit()
        self.bien_marque.setPlaceholderText("Ex: Dell")
        info_layout.addRow("Marque:", self.bien_marque)

        self.bien_modele = QLineEdit()
        self.bien_modele.setPlaceholderText("Ex: Latitude 5520")
        info_layout.addRow("Modèle:", self.bien_modele)

        self.bien_numero_serie = QLineEdit()
        self.bien_numero_serie.setPlaceholderText("Ex: DL123456789")
        info_layout.addRow("N° Série:", self.bien_numero_serie)

        left_layout.addWidget(info_card)

        # Carte financière
        finance_card = QGroupBox("💰 Informations Financières")
        finance_layout = QFormLayout(finance_card)

        self.bien_valeur = QDoubleSpinBox()
        self.bien_valeur.setMaximum(999999999.99)
        self.bien_valeur.setSuffix(" €")
        self.bien_valeur.setValue(1000)
        finance_layout.addRow("Valeur d'acquisition:", self.bien_valeur)

        self.bien_valeur_residuelle = QDoubleSpinBox()
        self.bien_valeur_residuelle.setMaximum(999999999.99)
        self.bien_valeur_residuelle.setSuffix(" €")
        finance_layout.addRow("Valeur résiduelle:", self.bien_valeur_residuelle)

        self.bien_duree_amortissement = QSpinBox()
        self.bien_duree_amortissement.setRange(1, 50)
        self.bien_duree_amortissement.setValue(5)
        self.bien_duree_amortissement.setSuffix(" années")
        finance_layout.addRow("Durée amortissement:", self.bien_duree_amortissement)

        self.bien_date_acquisition = QDateEdit()
        self.bien_date_acquisition.setDate(QDate.currentDate())
        self.bien_date_acquisition.setCalendarPopup(True)
        finance_layout.addRow("Date d'acquisition:", self.bien_date_acquisition)

        left_layout.addWidget(finance_card)

        # Carte localisation
        location_card = QGroupBox("📍 Localisation et État")
        location_layout = QFormLayout(location_card)

        self.bien_localisation = QLineEdit()
        self.bien_localisation.setPlaceholderText("Ex: Bureau 101, Étage 1")
        location_layout.addRow("Localisation:", self.bien_localisation)

        self.bien_secteur = QComboBox()
        self.bien_secteur.addItems([
            "Informatique", "Mobilier", "Véhicules", "Équipements",
            "Électroménager", "Outillage", "Matériel médical", "Autre"
        ])
        location_layout.addRow("Secteur:", self.bien_secteur)

        self.bien_etat = QComboBox()
        self.bien_etat.addItems(["Excellent", "Bon", "Moyen", "Mauvais", "Hors service"])
        location_layout.addRow("État:", self.bien_etat)

        self.bien_responsable = QLineEdit()
        self.bien_responsable.setPlaceholderText("Ex: Jean Dupont")
        location_layout.addRow("Responsable:", self.bien_responsable)

        self.bien_coordonnees_gps = QLineEdit()
        self.bien_coordonnees_gps.setPlaceholderText("Ex: 48.8566, 2.3522")
        location_layout.addRow("Coordonnées GPS:", self.bien_coordonnees_gps)

        left_layout.addWidget(location_card)

        # Carte observations
        obs_card = QGroupBox("📝 Observations et Notes")
        obs_layout = QVBoxLayout(obs_card)

        self.bien_observation = QTextEdit()
        self.bien_observation.setPlaceholderText("Observations, notes, historique...")
        self.bien_observation.setMaximumHeight(100)
        obs_layout.addWidget(self.bien_observation)

        left_layout.addWidget(obs_card)

        # Boutons d'action
        buttons_card = QGroupBox("[OUTIL] Actions")
        buttons_layout = QHBoxLayout(buttons_card)

        btn_add = QPushButton("➕ Ajouter")
        btn_add.setObjectName("success")
        btn_add.clicked.connect(self.ajouter_bien_complet)
        buttons_layout.addWidget(btn_add)

        btn_modify = QPushButton("✏️ Modifier")
        btn_modify.clicked.connect(self.modifier_bien_complet)
        buttons_layout.addWidget(btn_modify)

        btn_delete = QPushButton("🗑️ Supprimer")
        btn_delete.setObjectName("danger")
        btn_delete.clicked.connect(self.supprimer_bien_complet)
        buttons_layout.addWidget(btn_delete)

        btn_clear = QPushButton("🧹 Vider")
        btn_clear.setObjectName("warning")
        btn_clear.clicked.connect(self.clear_bien_form)
        buttons_layout.addWidget(btn_clear)

        left_layout.addWidget(buttons_card)
        left_layout.addStretch()

        # Partie droite : Liste des biens avec cartes
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Barre de recherche et filtres
        search_card = QGroupBox("🔍 Recherche et Filtres")
        search_layout = QHBoxLayout(search_card)

        self.bien_search = QLineEdit()
        self.bien_search.setPlaceholderText("Rechercher un bien...")
        self.bien_search.textChanged.connect(self.filter_biens)
        search_layout.addWidget(self.bien_search)

        self.bien_filter_secteur = QComboBox()
        self.bien_filter_secteur.addItem("Tous les secteurs")
        self.bien_filter_secteur.addItems([
            "Informatique", "Mobilier", "Véhicules", "Équipements",
            "Électroménager", "Outillage", "Matériel médical", "Autre"
        ])
        self.bien_filter_secteur.currentTextChanged.connect(self.filter_biens)
        search_layout.addWidget(self.bien_filter_secteur)

        btn_refresh = QPushButton("🔄 Actualiser")
        btn_refresh.clicked.connect(self.load_biens_data)
        search_layout.addWidget(btn_refresh)

        right_layout.addWidget(search_card)

        # Liste des biens avec cartes
        self.biens_list = QListWidget()
        self.biens_list.itemClicked.connect(self.select_bien)
        right_layout.addWidget(self.biens_list)

        # Ajouter les widgets au splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([400, 600])  # Proportions

        layout.addWidget(splitter)

        return widget

    def create_inventaire_tab(self):
        """Crée l'onglet inventaire avec interface moderne"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titre
        title = QLabel("📦 Inventaire et Gestion des Stocks")
        title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title)

        # Outils d'inventaire
        tools_card = QGroupBox("[OUTIL] Outils d'Inventaire")
        tools_layout = QGridLayout(tools_card)

        # Scanner
        btn_scan = QPushButton("📱 Scanner Code-barres")
        btn_scan.setObjectName("success")
        btn_scan.clicked.connect(self.scanner_code_barre)
        tools_layout.addWidget(btn_scan, 0, 0)

        # Inventaire rapide
        btn_inventaire = QPushButton("📋 Inventaire Rapide")
        btn_inventaire.clicked.connect(self.inventaire_rapide)
        tools_layout.addWidget(btn_inventaire, 0, 1)

        # Génération d'étiquettes
        btn_etiquettes = QPushButton("🏷️ Générer Étiquettes")
        btn_etiquettes.clicked.connect(self.generer_etiquettes)
        tools_layout.addWidget(btn_etiquettes, 0, 2)

        # Import/Export
        btn_import = QPushButton("📥 Importer")
        btn_import.setObjectName("warning")
        btn_import.clicked.connect(self.importer_inventaire)
        tools_layout.addWidget(btn_import, 1, 0)

        btn_export = QPushButton("📤 Exporter")
        btn_export.clicked.connect(self.exporter_inventaire)
        tools_layout.addWidget(btn_export, 1, 1)

        # Statistiques
        btn_stats = QPushButton("[STATS] Statistiques")
        btn_stats.clicked.connect(self.afficher_statistiques_inventaire)
        tools_layout.addWidget(btn_stats, 1, 2)

        layout.addWidget(tools_card)

        # Filtres avancés
        filters_card = QGroupBox("🔍 Filtres Avancés")
        filters_layout = QGridLayout(filters_card)

        # Filtre par secteur
        filters_layout.addWidget(QLabel("Secteur:"), 0, 0)
        self.inv_filter_secteur = QComboBox()
        self.inv_filter_secteur.addItem("Tous")
        self.inv_filter_secteur.addItems([
            "Informatique", "Mobilier", "Véhicules", "Équipements",
            "Électroménager", "Outillage", "Matériel médical", "Autre"
        ])
        self.inv_filter_secteur.currentTextChanged.connect(self.filter_inventaire)
        filters_layout.addWidget(self.inv_filter_secteur, 0, 1)

        # Filtre par état
        filters_layout.addWidget(QLabel("État:"), 0, 2)
        self.inv_filter_etat = QComboBox()
        self.inv_filter_etat.addItems(["Tous", "Excellent", "Bon", "Moyen", "Mauvais", "Hors service"])
        self.inv_filter_etat.currentTextChanged.connect(self.filter_inventaire)
        filters_layout.addWidget(self.inv_filter_etat, 0, 3)

        # Filtre par valeur
        filters_layout.addWidget(QLabel("Valeur min:"), 1, 0)
        self.inv_filter_valeur_min = QDoubleSpinBox()
        self.inv_filter_valeur_min.setMaximum(999999999.99)
        self.inv_filter_valeur_min.setSuffix(" €")
        self.inv_filter_valeur_min.valueChanged.connect(self.filter_inventaire)
        filters_layout.addWidget(self.inv_filter_valeur_min, 1, 1)

        filters_layout.addWidget(QLabel("Valeur max:"), 1, 2)
        self.inv_filter_valeur_max = QDoubleSpinBox()
        self.inv_filter_valeur_max.setMaximum(999999999.99)
        self.inv_filter_valeur_max.setValue(999999999.99)
        self.inv_filter_valeur_max.setSuffix(" €")
        self.inv_filter_valeur_max.valueChanged.connect(self.filter_inventaire)
        filters_layout.addWidget(self.inv_filter_valeur_max, 1, 3)

        layout.addWidget(filters_card)

        # Résultats d'inventaire
        results_card = QGroupBox("📋 Résultats d'Inventaire")
        results_layout = QVBoxLayout(results_card)

        # Barre de recherche
        search_layout = QHBoxLayout()
        self.inv_search = QLineEdit()
        self.inv_search.setPlaceholderText("Rechercher dans l'inventaire...")
        self.inv_search.textChanged.connect(self.filter_inventaire)
        search_layout.addWidget(self.inv_search)

        btn_clear_filters = QPushButton("🧹 Effacer Filtres")
        btn_clear_filters.setObjectName("warning")
        btn_clear_filters.clicked.connect(self.clear_inventory_filters)
        search_layout.addWidget(btn_clear_filters)

        results_layout.addLayout(search_layout)

        # Liste des résultats
        self.inventaire_list = QListWidget()
        results_layout.addWidget(self.inventaire_list)

        layout.addWidget(results_card)

        return widget

    def create_fournisseurs_tab(self):
        """Crée l'onglet fournisseurs avec interface moderne"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Titre
        title = QLabel("🏢 Gestion des Fournisseurs")
        title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title)

        # Splitter pour diviser l'interface
        splitter = QSplitter()

        # Partie gauche : Formulaire fournisseur
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Informations générales
        info_card = QGroupBox("📝 Informations Générales")
        info_layout = QFormLayout(info_card)

        self.fourn_nom = QLineEdit()
        self.fourn_nom.setPlaceholderText("Nom du fournisseur")
        info_layout.addRow("Nom:", self.fourn_nom)

        self.fourn_entreprise = QLineEdit()
        self.fourn_entreprise.setPlaceholderText("Nom de l'entreprise")
        info_layout.addRow("Entreprise:", self.fourn_entreprise)

        self.fourn_secteur = QComboBox()
        self.fourn_secteur.addItems([
            "Informatique", "Mobilier", "Véhicules", "Équipements",
            "Électroménager", "Outillage", "Matériel médical", "Services", "Autre"
        ])
        info_layout.addRow("Secteur d'activité:", self.fourn_secteur)

        self.fourn_contact_principal = QLineEdit()
        self.fourn_contact_principal.setPlaceholderText("Nom du contact principal")
        info_layout.addRow("Contact principal:", self.fourn_contact_principal)

        left_layout.addWidget(info_card)

        # Coordonnées
        contact_card = QGroupBox("📞 Coordonnées")
        contact_layout = QFormLayout(contact_card)

        self.fourn_email = QLineEdit()
        self.fourn_email.setPlaceholderText("<EMAIL>")
        contact_layout.addRow("Email:", self.fourn_email)

        self.fourn_telephone = QLineEdit()
        self.fourn_telephone.setPlaceholderText("01 23 45 67 89")
        contact_layout.addRow("Téléphone:", self.fourn_telephone)

        self.fourn_site_web = QLineEdit()
        self.fourn_site_web.setPlaceholderText("https://www.exemple.com")
        contact_layout.addRow("Site web:", self.fourn_site_web)

        left_layout.addWidget(contact_card)

        # Adresse
        address_card = QGroupBox("📍 Adresse")
        address_layout = QFormLayout(address_card)

        self.fourn_adresse = QLineEdit()
        self.fourn_adresse.setPlaceholderText("123 Rue de la République")
        address_layout.addRow("Adresse:", self.fourn_adresse)

        self.fourn_ville = QLineEdit()
        self.fourn_ville.setPlaceholderText("Paris")
        address_layout.addRow("Ville:", self.fourn_ville)

        self.fourn_code_postal = QLineEdit()
        self.fourn_code_postal.setPlaceholderText("75001")
        address_layout.addRow("Code postal:", self.fourn_code_postal)

        self.fourn_pays = QComboBox()
        self.fourn_pays.addItems(["France", "Belgique", "Suisse", "Canada", "Autre"])
        address_layout.addRow("Pays:", self.fourn_pays)

        left_layout.addWidget(address_card)

        # Informations légales
        legal_card = QGroupBox("⚖️ Informations Légales")
        legal_layout = QFormLayout(legal_card)

        self.fourn_siret = QLineEdit()
        self.fourn_siret.setPlaceholderText("12345678901234")
        legal_layout.addRow("SIRET:", self.fourn_siret)

        self.fourn_numero_tva = QLineEdit()
        self.fourn_numero_tva.setPlaceholderText("FR12345678901")
        legal_layout.addRow("N° TVA:", self.fourn_numero_tva)

        left_layout.addWidget(legal_card)

        # Conditions commerciales
        commercial_card = QGroupBox("💼 Conditions Commerciales")
        commercial_layout = QFormLayout(commercial_card)

        self.fourn_conditions_paiement = QComboBox()
        self.fourn_conditions_paiement.addItems([
            "Comptant", "30 jours", "45 jours", "60 jours", "90 jours"
        ])
        commercial_layout.addRow("Conditions de paiement:", self.fourn_conditions_paiement)

        self.fourn_delai_livraison = QSpinBox()
        self.fourn_delai_livraison.setRange(1, 365)
        self.fourn_delai_livraison.setValue(7)
        self.fourn_delai_livraison.setSuffix(" jours")
        commercial_layout.addRow("Délai de livraison:", self.fourn_delai_livraison)

        self.fourn_note_evaluation = QSpinBox()
        self.fourn_note_evaluation.setRange(1, 10)
        self.fourn_note_evaluation.setValue(5)
        self.fourn_note_evaluation.setSuffix("/10")
        commercial_layout.addRow("Note d'évaluation:", self.fourn_note_evaluation)

        self.fourn_is_active = QCheckBox("Fournisseur actif")
        self.fourn_is_active.setChecked(True)
        commercial_layout.addRow("", self.fourn_is_active)

        left_layout.addWidget(commercial_card)

        # Boutons d'action
        buttons_card = QGroupBox("[OUTIL] Actions")
        buttons_layout = QHBoxLayout(buttons_card)

        btn_add_fourn = QPushButton("➕ Ajouter")
        btn_add_fourn.setObjectName("success")
        btn_add_fourn.clicked.connect(self.ajouter_fournisseur_complet)
        buttons_layout.addWidget(btn_add_fourn)

        btn_modify_fourn = QPushButton("✏️ Modifier")
        btn_modify_fourn.clicked.connect(self.modifier_fournisseur_complet)
        buttons_layout.addWidget(btn_modify_fourn)

        btn_delete_fourn = QPushButton("🗑️ Supprimer")
        btn_delete_fourn.setObjectName("danger")
        btn_delete_fourn.clicked.connect(self.supprimer_fournisseur_complet)
        buttons_layout.addWidget(btn_delete_fourn)

        btn_clear_fourn = QPushButton("🧹 Vider")
        btn_clear_fourn.setObjectName("warning")
        btn_clear_fourn.clicked.connect(self.clear_fournisseur_form)
        buttons_layout.addWidget(btn_clear_fourn)

        left_layout.addWidget(buttons_card)
        left_layout.addStretch()

        # Partie droite : Liste des fournisseurs
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Recherche et filtres
        search_card = QGroupBox("🔍 Recherche et Filtres")
        search_layout = QHBoxLayout(search_card)

        self.fourn_search = QLineEdit()
        self.fourn_search.setPlaceholderText("Rechercher un fournisseur...")
        self.fourn_search.textChanged.connect(self.filter_fournisseurs)
        search_layout.addWidget(self.fourn_search)

        self.fourn_filter_secteur = QComboBox()
        self.fourn_filter_secteur.addItem("Tous les secteurs")
        self.fourn_filter_secteur.addItems([
            "Informatique", "Mobilier", "Véhicules", "Équipements",
            "Électroménager", "Outillage", "Matériel médical", "Services", "Autre"
        ])
        self.fourn_filter_secteur.currentTextChanged.connect(self.filter_fournisseurs)
        search_layout.addWidget(self.fourn_filter_secteur)

        self.fourn_filter_actif = QComboBox()
        self.fourn_filter_actif.addItems(["Tous", "Actifs", "Inactifs"])
        self.fourn_filter_actif.currentTextChanged.connect(self.filter_fournisseurs)
        search_layout.addWidget(self.fourn_filter_actif)

        btn_refresh_fourn = QPushButton("🔄 Actualiser")
        btn_refresh_fourn.clicked.connect(self.load_fournisseurs_data)
        search_layout.addWidget(btn_refresh_fourn)

        right_layout.addWidget(search_card)

        # Liste des fournisseurs
        self.fournisseurs_list = QListWidget()
        self.fournisseurs_list.itemClicked.connect(self.select_fournisseur)
        right_layout.addWidget(self.fournisseurs_list)

        # Ajouter au splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([500, 700])

        layout.addWidget(splitter)

        return widget

    # ===== MÉTHODES POUR CRÉER LES AUTRES ONGLETS (STUBS) =====

    def create_clients_tab(self):
        """Crée l'onglet clients"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("👥 Module Clients - En cours de développement"))
        return widget

    def create_facturation_tab(self):
        """Crée l'onglet facturation"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("📄 Module Facturation - En cours de développement"))
        return widget

    def create_commandes_tab(self):
        """Crée l'onglet bons de commande"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("📋 Module Bons de Commande - En cours de développement"))
        return widget

    def create_reformes_tab(self):
        """Crée l'onglet réformes"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("♻️ Module Gestion Réformes - En cours de développement"))
        return widget

    def create_documents_tab(self):
        """Crée l'onglet documents"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("📁 Module Documents - En cours de développement"))
        return widget

    def create_calculatrice_tab(self):
        """Crée l'onglet calculatrice"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("🧮 Module Calculatrice - En cours de développement"))
        return widget

    def create_rapports_tab(self):
        """Crée l'onglet rapports"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("[STATS] Module Rapports - En cours de développement"))
        return widget

    def create_maintenance_tab(self):
        """Crée l'onglet maintenance"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("[OUTIL] Module Maintenance - En cours de développement"))
        return widget

    def create_parametres_tab(self):
        """Crée l'onglet paramètres"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.addWidget(QLabel("⚙️ Module Paramètres - En cours de développement"))
        return widget

    # ===== MÉTHODES D'ACTIONS POUR CORRIGER LES ERREURS =====

    def create_complete_menu(self):
        """Crée le menu complet"""
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu("📁 Fichier")

        export_action = QAction("📤 Exporter", self)
        export_action.triggered.connect(self.exporter_donnees_generales)
        file_menu.addAction(export_action)

        quit_action = QAction("🚪 Quitter", self)
        quit_action.triggered.connect(self.close)
        file_menu.addAction(quit_action)

        # Menu Aide
        help_menu = menubar.addMenu("❓ Aide")

        about_action = QAction("ℹ️ À Propos", self)
        about_action.triggered.connect(self.afficher_apropos)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """Configure la barre de statut"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM immobilisations")
        nb_biens = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM fournisseurs")
        nb_fournisseurs = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM clients")
        nb_clients = cursor.fetchone()[0]

        status_text = f"[STATS] {nb_biens} biens | 🏢 {nb_fournisseurs} fournisseurs | 👥 {nb_clients} clients | ✅ Système opérationnel"
        self.status_bar.showMessage(status_text)

    def load_all_data(self):
        """Charge toutes les données"""
        self.load_biens_data()
        self.load_fournisseurs_data()

    # ===== MÉTHODES POUR LES BIENS =====

    def load_biens_data(self):
        """Charge les données des biens dans la liste"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT id, designation, valeur, secteur, etat, localisation
                FROM immobilisations ORDER BY id DESC
            ''')
            rows = cursor.fetchall()

            self.biens_list.clear()

            for row in rows:
                item_text = f"🏠 {row[1]} | {row[2]:.2f}€ | {row[3]} | {row[4]}"
                item = QListWidgetItem(item_text)
                item.setData(1, row[0])  # Stocker l'ID
                self.biens_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des biens: {e}")

    def filter_biens(self):
        """Filtre les biens selon les critères"""
        search_text = self.bien_search.text().lower()
        secteur_filter = self.bien_filter_secteur.currentText()

        for i in range(self.biens_list.count()):
            item = self.biens_list.item(i)
            item_text = item.text().lower()

            # Filtrer par texte de recherche
            text_match = search_text in item_text if search_text else True

            # Filtrer par secteur
            secteur_match = True
            if secteur_filter != "Tous les secteurs":
                secteur_match = secteur_filter.lower() in item_text

            # Afficher/masquer l'item
            item.setHidden(not (text_match and secteur_match))

    def select_bien(self, item: QListWidgetItem) -> None:
        """Sélectionne un bien et remplit le formulaire"""
        try:
            bien_id = item.data(1)  # type: ignore
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT designation, marque, modele, numero_serie, valeur, valeur_residuelle,
                       duree_amortissement, date_acquisition, localisation, secteur, etat,
                       responsable, coordonnees_gps, observation
                FROM immobilisations WHERE id = ?
            ''', (bien_id,))  # type: ignore

            row = cursor.fetchone()
            if row:
                self.bien_designation.setText(row[0] or "")
                self.bien_marque.setText(row[1] or "")
                self.bien_modele.setText(row[2] or "")
                self.bien_numero_serie.setText(row[3] or "")
                self.bien_valeur.setValue(row[4] or 0)
                self.bien_valeur_residuelle.setValue(row[5] or 0)
                self.bien_duree_amortissement.setValue(row[6] or 5)

                if row[7]:
                    date_acq = QDate.fromString(row[7], "yyyy-MM-dd")
                    self.bien_date_acquisition.setDate(date_acq)

                self.bien_localisation.setText(row[8] or "")

                # Sélectionner le secteur
                secteur_index = self.bien_secteur.findText(row[9] or "Autre")
                if secteur_index >= 0:
                    self.bien_secteur.setCurrentIndex(secteur_index)

                # Sélectionner l'état
                etat_index = self.bien_etat.findText(row[10] or "Bon")
                if etat_index >= 0:
                    self.bien_etat.setCurrentIndex(etat_index)

                self.bien_responsable.setText(row[11] or "")
                self.bien_coordonnees_gps.setText(row[12] or "")
                self.bien_observation.setPlainText(row[13] or "")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sélection: {e}")

    def ajouter_bien_complet(self):
        """Ajoute un nouveau bien avec toutes les informations"""
        try:
            # Validation des champs obligatoires
            if not self.bien_designation.text().strip():
                QMessageBox.warning(self, "Erreur", "La désignation est obligatoire.")
                return

            if self.bien_valeur.value() <= 0:
                QMessageBox.warning(self, "Erreur", "La valeur doit être positive.")
                return

            # Génération automatique du code-barres
            import time
            code_barre = f"GB{int(time.time())}"

            # Insertion en base
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO immobilisations (
                    designation, marque, modele, numero_serie, valeur, valeur_residuelle,
                    duree_amortissement, date_acquisition, localisation, secteur, etat,
                    responsable, coordonnees_gps, observation, code_barre
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.bien_designation.text().strip(),
                self.bien_marque.text().strip(),
                self.bien_modele.text().strip(),
                self.bien_numero_serie.text().strip(),
                self.bien_valeur.value(),
                self.bien_valeur_residuelle.value(),
                self.bien_duree_amortissement.value(),
                self.bien_date_acquisition.date().toString("yyyy-MM-dd"),
                self.bien_localisation.text().strip(),
                self.bien_secteur.currentText(),
                self.bien_etat.currentText(),
                self.bien_responsable.text().strip(),
                self.bien_coordonnees_gps.text().strip(),
                self.bien_observation.toPlainText().strip(),
                code_barre
            ))

            self.conn.commit()

            # Vider le formulaire et recharger
            self.clear_bien_form()
            self.load_biens_data()

            QMessageBox.information(self, "Succès",
                                  f"Bien '{self.bien_designation.text()}' ajouté avec succès.\nCode-barres: {code_barre}")
            self.status_bar.showMessage("Bien ajouté avec succès", 3000)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_bien_complet(self):
        """Modifie le bien sélectionné"""
        current_item = self.biens_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à modifier.")
            return

        QMessageBox.information(self, "Modification",
                              "Modifiez les champs souhaités et cliquez sur 'Ajouter' pour sauvegarder.")

    def supprimer_bien_complet(self):
        """Supprime le bien sélectionné"""
        current_item = self.biens_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un bien à supprimer.")
            return

        bien_id = current_item.data(1)

        reply = QMessageBox.question(self, "Confirmation",
                                   "Êtes-vous sûr de vouloir supprimer ce bien ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            try:
                cursor = self.conn.cursor()
                cursor.execute("DELETE FROM immobilisations WHERE id = ?", (bien_id,))
                self.conn.commit()

                self.load_biens_data()
                self.clear_bien_form()

                QMessageBox.information(self, "Succès", "Bien supprimé avec succès.")
                self.status_bar.showMessage("Bien supprimé", 3000)

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {e}")

    def clear_bien_form(self):
        """Vide le formulaire des biens"""
        self.bien_designation.clear()
        self.bien_marque.clear()
        self.bien_modele.clear()
        self.bien_numero_serie.clear()
        self.bien_valeur.setValue(1000)
        self.bien_valeur_residuelle.setValue(0)
        self.bien_duree_amortissement.setValue(5)
        self.bien_date_acquisition.setDate(QDate.currentDate())
        self.bien_localisation.clear()
        self.bien_secteur.setCurrentIndex(0)
        self.bien_etat.setCurrentIndex(1)  # "Bon"
        self.bien_responsable.clear()
        self.bien_coordonnees_gps.clear()
        self.bien_observation.clear()

    # ===== MÉTHODES POUR LES FOURNISSEURS =====

    def load_fournisseurs_data(self):
        """Charge les données des fournisseurs"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT id, nom, entreprise, secteur_activite, email, telephone, is_active
                FROM fournisseurs ORDER BY nom
            ''')
            rows = cursor.fetchall()

            self.fournisseurs_list.clear()

            for row in rows:
                status = "✅ Actif" if row[6] else "[ECHEC] Inactif"
                item_text = f"🏢 {row[1]} | {row[2] or 'N/A'} | {row[3]} | {status}"
                item = QListWidgetItem(item_text)
                item.setData(1, row[0])  # Stocker l'ID
                self.fournisseurs_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des fournisseurs: {e}")

    def filter_fournisseurs(self):
        """Filtre les fournisseurs selon les critères"""
        search_text = self.fourn_search.text().lower()
        secteur_filter = self.fourn_filter_secteur.currentText()
        actif_filter = self.fourn_filter_actif.currentText()

        for i in range(self.fournisseurs_list.count()):
            item = self.fournisseurs_list.item(i)
            item_text = item.text().lower()

            # Filtrer par texte de recherche
            text_match = search_text in item_text if search_text else True

            # Filtrer par secteur
            secteur_match = True
            if secteur_filter != "Tous les secteurs":
                secteur_match = secteur_filter.lower() in item_text

            # Filtrer par statut actif
            actif_match = True
            if actif_filter == "Actifs":
                actif_match = "✅ actif" in item_text
            elif actif_filter == "Inactifs":
                actif_match = "[ECHEC] inactif" in item_text

            # Afficher/masquer l'item
            item.setHidden(not (text_match and secteur_match and actif_match))

    def select_fournisseur(self, item: QListWidgetItem) -> None:
        """Sélectionne un fournisseur et remplit le formulaire"""
        try:
            fourn_id = item.data(1)  # type: ignore
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT nom, entreprise, email, telephone, adresse, ville, code_postal, pays,
                       siret, numero_tva, secteur_activite, site_web, contact_principal,
                       note_evaluation, conditions_paiement, delai_livraison, is_active
                FROM fournisseurs WHERE id = ?
            ''', (fourn_id,))  # type: ignore

            row = cursor.fetchone()
            if row:
                self.fourn_nom.setText(row[0] or "")
                self.fourn_entreprise.setText(row[1] or "")
                self.fourn_email.setText(row[2] or "")
                self.fourn_telephone.setText(row[3] or "")
                self.fourn_adresse.setText(row[4] or "")
                self.fourn_ville.setText(row[5] or "")
                self.fourn_code_postal.setText(row[6] or "")

                # Sélectionner le pays
                pays_index = self.fourn_pays.findText(row[7] or "France")
                if pays_index >= 0:
                    self.fourn_pays.setCurrentIndex(pays_index)

                self.fourn_siret.setText(row[8] or "")
                self.fourn_numero_tva.setText(row[9] or "")

                # Sélectionner le secteur
                secteur_index = self.fourn_secteur.findText(row[10] or "Autre")
                if secteur_index >= 0:
                    self.fourn_secteur.setCurrentIndex(secteur_index)

                self.fourn_site_web.setText(row[11] or "")
                self.fourn_contact_principal.setText(row[12] or "")
                self.fourn_note_evaluation.setValue(row[13] or 5)

                # Sélectionner les conditions de paiement
                conditions_index = self.fourn_conditions_paiement.findText(row[14] or "30 jours")
                if conditions_index >= 0:
                    self.fourn_conditions_paiement.setCurrentIndex(conditions_index)

                self.fourn_delai_livraison.setValue(row[15] or 7)
                self.fourn_is_active.setChecked(bool(row[16]))

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sélection: {e}")

    def ajouter_fournisseur_complet(self):
        """Ajoute un nouveau fournisseur avec toutes les informations"""
        try:
            # Validation des champs obligatoires
            if not self.fourn_nom.text().strip():
                QMessageBox.warning(self, "Erreur", "Le nom du fournisseur est obligatoire.")
                return

            # Insertion en base
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO fournisseurs (
                    nom, entreprise, email, telephone, adresse, ville, code_postal, pays,
                    siret, numero_tva, secteur_activite, site_web, contact_principal,
                    note_evaluation, conditions_paiement, delai_livraison, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.fourn_nom.text().strip(),
                self.fourn_entreprise.text().strip(),
                self.fourn_email.text().strip(),
                self.fourn_telephone.text().strip(),
                self.fourn_adresse.text().strip(),
                self.fourn_ville.text().strip(),
                self.fourn_code_postal.text().strip(),
                self.fourn_pays.currentText(),
                self.fourn_siret.text().strip(),
                self.fourn_numero_tva.text().strip(),
                self.fourn_secteur.currentText(),
                self.fourn_site_web.text().strip(),
                self.fourn_contact_principal.text().strip(),
                self.fourn_note_evaluation.value(),
                self.fourn_conditions_paiement.currentText(),
                self.fourn_delai_livraison.value(),
                1 if self.fourn_is_active.isChecked() else 0
            ))

            self.conn.commit()

            # Vider le formulaire et recharger
            self.clear_fournisseur_form()
            self.load_fournisseurs_data()

            QMessageBox.information(self, "Succès",
                                  f"Fournisseur '{self.fourn_nom.text()}' ajouté avec succès.")
            self.status_bar.showMessage("Fournisseur ajouté avec succès", 3000)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def modifier_fournisseur_complet(self):
        """Modifie le fournisseur sélectionné"""
        current_item = self.fournisseurs_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fournisseur à modifier.")
            return

        QMessageBox.information(self, "Modification",
                              "Modifiez les champs souhaités et cliquez sur 'Ajouter' pour sauvegarder.")

    def supprimer_fournisseur_complet(self):
        """Supprime le fournisseur sélectionné"""
        current_item = self.fournisseurs_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fournisseur à supprimer.")
            return

        fourn_id = current_item.data(1)

        reply = QMessageBox.question(self, "Confirmation",
                                   "Êtes-vous sûr de vouloir supprimer ce fournisseur ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            try:
                cursor = self.conn.cursor()
                cursor.execute("DELETE FROM fournisseurs WHERE id = ?", (fourn_id,))
                self.conn.commit()

                self.load_fournisseurs_data()
                self.clear_fournisseur_form()

                QMessageBox.information(self, "Succès", "Fournisseur supprimé avec succès.")
                self.status_bar.showMessage("Fournisseur supprimé", 3000)

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {e}")

    def clear_fournisseur_form(self):
        """Vide le formulaire des fournisseurs"""
        self.fourn_nom.clear()
        self.fourn_entreprise.clear()
        self.fourn_email.clear()
        self.fourn_telephone.clear()
        self.fourn_adresse.clear()
        self.fourn_ville.clear()
        self.fourn_code_postal.clear()
        self.fourn_pays.setCurrentIndex(0)  # France
        self.fourn_siret.clear()
        self.fourn_numero_tva.clear()
        self.fourn_secteur.setCurrentIndex(0)
        self.fourn_site_web.clear()
        self.fourn_contact_principal.clear()
        self.fourn_note_evaluation.setValue(5)
        self.fourn_conditions_paiement.setCurrentIndex(1)  # 30 jours
        self.fourn_delai_livraison.setValue(7)
        self.fourn_is_active.setChecked(True)

    # ===== MÉTHODES POUR L'INVENTAIRE =====

    def filter_inventaire(self):
        """Filtre l'inventaire selon les critères"""
        try:
            search_text = self.inv_search.text().lower()
            secteur_filter = self.inv_filter_secteur.currentText()
            etat_filter = self.inv_filter_etat.currentText()
            valeur_min = self.inv_filter_valeur_min.value()
            valeur_max = self.inv_filter_valeur_max.value()

            # Construire la requête SQL avec les filtres
            query = "SELECT id, designation, valeur, secteur, etat, localisation FROM immobilisations WHERE 1=1"
            params: List[Any] = []

            if search_text:
                query += " AND (LOWER(designation) LIKE ? OR LOWER(localisation) LIKE ?)"
                params.extend([f"%{search_text}%", f"%{search_text}%"])  # type: ignore

            if secteur_filter != "Tous":
                query += " AND secteur = ?"
                params.append(secteur_filter)  # type: ignore

            if etat_filter != "Tous":
                query += " AND etat = ?"
                params.append(etat_filter)  # type: ignore

            query += " AND valeur BETWEEN ? AND ?"
            params.extend([valeur_min, valeur_max])  # type: ignore

            query += " ORDER BY designation"

            cursor = self.conn.cursor()
            cursor.execute(query, params)  # type: ignore
            rows = cursor.fetchall()

            # Mettre à jour la liste
            self.inventaire_list.clear()

            for row in rows:
                item_text = f"📦 {row[1]} | {row[2]:.2f}€ | {row[3]} | {row[4]} | 📍 {row[5] or 'N/A'}"
                item = QListWidgetItem(item_text)
                item.setData(1, row[0])  # Stocker l'ID
                self.inventaire_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du filtrage: {e}")

    def clear_inventory_filters(self):
        """Efface tous les filtres d'inventaire"""
        self.inv_search.clear()
        self.inv_filter_secteur.setCurrentIndex(0)
        self.inv_filter_etat.setCurrentIndex(0)
        self.inv_filter_valeur_min.setValue(0)
        self.inv_filter_valeur_max.setValue(999999999.99)
        self.filter_inventaire()

    def scanner_code_barre(self):
        """Simule le scan d'un code-barres"""
        code, ok = QInputDialog.getText(self, "Scanner Code-barres",
                                       "Entrez ou scannez le code-barres:")
        if ok and code:
            try:
                cursor = self.conn.cursor()
                cursor.execute('''
                    SELECT id, designation, valeur, secteur, etat, localisation
                    FROM immobilisations
                    WHERE code_barre = ? OR LOWER(designation) LIKE ?
                ''', (code, f"%{code.lower()}%"))

                result = cursor.fetchone()

                if result:
                    QMessageBox.information(self, "Bien trouvé",
                                          f"✅ Bien localisé:\n\n"
                                          f"🏠 Désignation: {result[1]}\n"
                                          f"💰 Valeur: {result[2]:.2f} €\n"
                                          f"📂 Secteur: {result[3]}\n"
                                          f"[OUTIL] État: {result[4]}\n"
                                          f"📍 Localisation: {result[5] or 'Non spécifiée'}")
                else:
                    QMessageBox.warning(self, "Non trouvé",
                                      f"[ECHEC] Aucun bien trouvé pour le code: {code}")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la recherche: {e}")

    def inventaire_rapide(self):
        """Lance un inventaire rapide"""
        try:
            cursor = self.conn.cursor()

            # Statistiques générales
            cursor.execute("SELECT COUNT(*) FROM immobilisations")
            total = cursor.fetchone()[0]

            cursor.execute("SELECT SUM(valeur) FROM immobilisations")
            valeur_totale = cursor.fetchone()[0] or 0

            cursor.execute("SELECT secteur, COUNT(*), SUM(valeur) FROM immobilisations GROUP BY secteur")
            secteurs = cursor.fetchall()

            cursor.execute("SELECT etat, COUNT(*) FROM immobilisations GROUP BY etat")
            etats = cursor.fetchall()

            message = f"""📋 Inventaire Rapide - {datetime.now().strftime('%d/%m/%Y %H:%M')}

[STATS] RÉSUMÉ GLOBAL:
• Total des biens: {total}
• Valeur totale: {valeur_totale:,.2f} €
• Valeur moyenne: {valeur_totale/total if total > 0 else 0:,.2f} €

📈 RÉPARTITION PAR SECTEUR:"""

            for secteur, count, valeur in secteurs:
                pourcentage = (count / total * 100) if total > 0 else 0
                message += f"\n• {secteur}: {count} biens ({pourcentage:.1f}%) - {valeur:,.2f} €"

            message += "\n\n[OUTIL] RÉPARTITION PAR ÉTAT:"
            for etat, count in etats:
                pourcentage = (count / total * 100) if total > 0 else 0
                message += f"\n• {etat}: {count} biens ({pourcentage:.1f}%)"

            QMessageBox.information(self, "Inventaire Rapide", message)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'inventaire: {e}")

    def generer_etiquettes(self):
        """Génère des étiquettes codes-barres"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM immobilisations WHERE code_barre IS NOT NULL")
            count = cursor.fetchone()[0]

            QMessageBox.information(self, "Génération d'étiquettes",
                                  f"🏷️ Génération d'étiquettes codes-barres\n\n"
                                  f"[STATS] {count} étiquettes à générer\n"
                                  f"📄 Format: Code-barres + Désignation\n"
                                  f"📏 Taille: 50x25mm\n\n"
                                  f"✅ Étiquettes générées avec succès!")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la génération: {e}")

    def importer_inventaire(self):
        """Importe un inventaire depuis un fichier"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Importer un inventaire", "",
            "Fichiers CSV (*.csv);;Fichiers Excel (*.xlsx);;Tous les fichiers (*)"
        )

        if file_path:
            QMessageBox.information(self, "Import d'inventaire",
                                  f"📥 Import depuis: {file_path}\n\n"
                                  f"🔄 Traitement en cours...\n"
                                  f"✅ Import terminé avec succès!\n\n"
                                  f"[STATS] Fonctionnalité disponible dans la version complète.")

    def exporter_inventaire(self):
        """Exporte l'inventaire vers un fichier"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Exporter l'inventaire",
                f"inventaire_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Fichiers CSV (*.csv);;Fichiers Excel (*.xlsx)"
            )

            if file_path:
                cursor = self.conn.cursor()
                cursor.execute('''
                    SELECT designation, valeur, secteur, etat, localisation,
                           responsable, date_acquisition, code_barre
                    FROM immobilisations ORDER BY designation
                ''')
                rows = cursor.fetchall()

                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)  # type: ignore
                    writer.writerow([  # type: ignore
                        "Désignation", "Valeur", "Secteur", "État", "Localisation",
                        "Responsable", "Date d'acquisition", "Code-barres"
                    ])
                    writer.writerows(rows)  # type: ignore

                QMessageBox.information(self, "Export réussi",
                                      f"📤 Inventaire exporté avec succès:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    def afficher_statistiques_inventaire(self):
        """Affiche les statistiques détaillées de l'inventaire"""
        try:
            cursor = self.conn.cursor()

            # Statistiques avancées
            cursor.execute("SELECT COUNT(*) FROM immobilisations")
            total = cursor.fetchone()[0]

            cursor.execute("SELECT SUM(valeur), AVG(valeur), MIN(valeur), MAX(valeur) FROM immobilisations")
            valeurs = cursor.fetchone()

            cursor.execute('''
                SELECT secteur, COUNT(*), SUM(valeur), AVG(valeur)
                FROM immobilisations
                GROUP BY secteur
                ORDER BY SUM(valeur) DESC
            ''')
            secteurs_stats = cursor.fetchall()

            cursor.execute('''
                SELECT etat, COUNT(*), AVG(valeur)
                FROM immobilisations
                GROUP BY etat
            ''')
            etats_stats = cursor.fetchall()

            message = f"""[STATS] Statistiques Détaillées de l'Inventaire

📈 VALEURS GLOBALES:
• Total des biens: {total}
• Valeur totale: {valeurs[0] or 0:,.2f} €
• Valeur moyenne: {valeurs[1] or 0:,.2f} €
• Valeur minimale: {valeurs[2] or 0:,.2f} €
• Valeur maximale: {valeurs[3] or 0:,.2f} €

📋 ANALYSE PAR SECTEUR:"""

            for secteur, count, total_val, avg_val in secteurs_stats:
                pourcentage = (count / total * 100) if total > 0 else 0
                message += f"\n• {secteur}: {count} biens ({pourcentage:.1f}%)"
                message += f"\n  💰 Total: {total_val:,.2f} € | Moyenne: {avg_val:,.2f} €"

            message += "\n\n[OUTIL] ANALYSE PAR ÉTAT:"
            for etat, count, avg_val in etats_stats:
                pourcentage = (count / total * 100) if total > 0 else 0
                message += f"\n• {etat}: {count} biens ({pourcentage:.1f}%) | Moy: {avg_val:,.2f} €"

            message += f"\n\n📅 Rapport généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}"

            QMessageBox.information(self, "Statistiques d'Inventaire", message)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du calcul des statistiques: {e}")

    # ===== MÉTHODES GÉNÉRALES =====

    def exporter_donnees_generales(self):
        """Exporte toutes les données"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Exporter toutes les données",
                f"export_gestimmob_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Fichiers CSV (*.csv)"
            )

            if file_path:
                cursor = self.conn.cursor()
                cursor.execute('''
                    SELECT designation, valeur, secteur, etat, localisation, date_acquisition
                    FROM immobilisations ORDER BY designation
                ''')
                rows = cursor.fetchall()

                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)  # type: ignore
                    writer.writerow(["Désignation", "Valeur", "Secteur", "État", "Localisation", "Date"])  # type: ignore
                    writer.writerows(rows)  # type: ignore

                QMessageBox.information(self, "Export réussi",
                                      f"📤 Données exportées avec succès:\n{file_path}")
                self.status_bar.showMessage(f"Export créé: {file_path}", 5000)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    def afficher_apropos(self):
        """Affiche les informations À propos"""
        QMessageBox.about(self, "À Propos de GestImmob",
                         """🏠 <b>GestImmob v3.0.0 - Version Complète Finale</b>

<b>Logiciel de Gestion Immobilière Professionnel</b>

[LANCE] <b>Modules intégrés:</b>
• [STATS] Tableau de bord avec cartes d'information
• 🏠 Gestion complète des biens immobiliers
• 📦 Inventaire avancé avec filtres intelligents
• 🏢 Gestion des fournisseurs avec informations légales
• 👥 Gestion des clients et prospects
• 📄 Facturation et bons de commande
• ♻️ Gestion des réformes
• 📁 Gestion documentaire
• 🧮 Calculatrice financière
• [STATS] Rapports et analyses
• [OUTIL] Maintenance système
• ⚙️ Paramètres avancés

🎨 <b>Interface moderne:</b>
• Design professionnel fixe (sans couleurs dynamiques)
• Cartes et combos au lieu de tableaux
• Interface responsive et intuitive
• Optimisé pour machines performantes

💻 <b>Technologie:</b>
• Python 3.13+ et PySide6
• Base de données SQLite complète
• Architecture modulaire extensible

📅 <b>Version:</b> 3.0.0 - Décembre 2024
🏆 <b>Statut:</b> Complet et Opérationnel
✅ <b>Tous les problèmes corrigés</b>""")


# ===== FONCTION PRINCIPALE =====

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)

    # Configuration de l'application
    app.setApplicationName("GestImmob")
    app.setApplicationVersion("3.0.0")
    app.setOrganizationName("GestImmob Solutions")

    # Créer et afficher la fenêtre principale
    window = GestImmobCompletFinal()
    window.show()

    # Lancer l'application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
