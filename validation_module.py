#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de validation pour GestImmob
Valide la syntaxe, les types et la cohérence du code
"""

import ast
import sys
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class CodeValidator:
    """Validateur de code multi-langages"""

    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.suggestions: List[str] = []

    def validate_python_file(self, file_path: Path) -> bool:
        """Valide un fichier Python"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Validation syntaxique
            try:
                ast.parse(content)
                logger.info(f"Syntaxe valide: {file_path.name}")
            except SyntaxError as e:
                self.errors.append(f"Erreur syntaxe {file_path.name}: {e}")
                return False

            # Validation des imports
            self._validate_imports(content, file_path)

            # Validation des types
            self._validate_types(content, file_path)

            return len(self.errors) == 0

        except Exception as e:
            self.errors.append(f"Erreur validation {file_path.name}: {e}")
            return False

    def _validate_imports(self, content: str, file_path: Path):
        """Valide les imports"""
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                # Vérifier les imports inutilisés
                import_name = self._extract_import_name(line)
                if import_name and import_name not in content:
                    self.warnings.append(f"{file_path.name}:{i} Import inutilisé: {import_name}")

    def _validate_types(self, content: str, file_path: Path):
        """Valide les annotations de type"""
        if 'def ' in content and '->' not in content:
            self.suggestions.append(f"{file_path.name}: Ajouter des annotations de type")

    def _extract_import_name(self, line: str) -> str:
        """Extrait le nom d'un import"""
        if line.startswith('import '):
            return line.split()[1].split('.')[0]
        elif 'import' in line:
            parts = line.split('import')
            if len(parts) > 1:
                return parts[1].strip().split(',')[0].strip()
        return ""

    def get_report(self) -> Dict[str, Any]:
        """Retourne le rapport de validation"""
        return {
            'errors': self.errors,
            'warnings': self.warnings,
            'suggestions': self.suggestions,
            'total_issues': len(self.errors) + len(self.warnings),
            'is_valid': len(self.errors) == 0
        }

def validate_project() -> bool:
    """Valide tout le projet"""
    validator = CodeValidator()
    python_files = list(Path.cwd().glob("*.py"))

    all_valid = True
    for py_file in python_files:
        if not validator.validate_python_file(py_file):
            all_valid = False

    report = validator.get_report()
    logger.info(f"Validation terminée: {report['total_issues']} problèmes trouvés")

    return all_valid

if __name__ == "__main__":
    validate_project()
