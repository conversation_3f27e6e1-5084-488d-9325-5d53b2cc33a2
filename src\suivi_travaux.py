from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QFileDialog
)
from PySide6.QtCore import QTimer
from PySide6.QtGui import QColor

class SuiviTravauxWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Suivi des travaux et projets")
        self.resize(1400, 800)
        layout = QVBoxLayout(self)
        self.table = QTableWidget(0, 13)
        self.table.setHorizontalHeaderLabels([
            "ID", "Nom projet", "Type", "Responsable", "Date début", "Date fin", "Statut",
            "Coût estimé", "Coût réel", "Valeur ajoutée", "Du<PERSON>e (jours)", "Amortissement", "Observation"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter projet/travaux")
        self.edit_btn = QPushButton("Modifier")
        self.delete_btn = QPushButton("Supprimer")
        self.export_btn = QPushButton("Exporter Excel")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.export_btn)
        layout.addLayout(btn_layout)
        self.add_btn.clicked.connect(self.ajouter_travaux)
        self.edit_btn.clicked.connect(self.modifier_travaux)
        self.delete_btn.clicked.connect(self.supprimer_travaux)
        self.export_btn.clicked.connect(self.exporter_excel)
        self.changements_detectes = False

    def ajouter_travaux(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter projet/travaux")
        form = QFormLayout()
        nom_projet = QLineEdit()
        type_travaux = QComboBox(); type_travaux.addItems(["Construction", "Réhabilitation", "Maintenance", "Étude", "Autre"])
        responsable = QLineEdit()
        date_debut = QLineEdit()
        date_fin = QLineEdit()
        statut = QComboBox(); statut.addItems(["En cours", "Terminé", "En attente", "Suspendu"])
        cout_estime = QLineEdit()
        cout_reel = QLineEdit()
        valeur_ajoutee = QLineEdit()
        duree = QLineEdit()
        amortissement = QLineEdit()
        observation = QLineEdit()
        form.addRow("Nom projet:", nom_projet)
        form.addRow("Type:", type_travaux)
        form.addRow("Responsable:", responsable)
        form.addRow("Date début:", date_debut)
        form.addRow("Date fin:", date_fin)
        form.addRow("Statut:", statut)
        form.addRow("Coût estimé:", cout_estime)
        form.addRow("Coût réel:", cout_reel)
        form.addRow("Valeur ajoutée:", valeur_ajoutee)
        form.addRow("Durée (jours):", duree)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Observation:", observation)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
            self.table.setItem(row, 1, QTableWidgetItem(nom_projet.text()))
            self.table.setItem(row, 2, QTableWidgetItem(type_travaux.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(responsable.text()))
            self.table.setItem(row, 4, QTableWidgetItem(date_debut.text()))
            self.table.setItem(row, 5, QTableWidgetItem(date_fin.text()))
            self.table.setItem(row, 6, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 7, QTableWidgetItem(cout_estime.text()))
            self.table.setItem(row, 8, QTableWidgetItem(cout_reel.text()))
            self.table.setItem(row, 9, QTableWidgetItem(valeur_ajoutee.text()))
            self.table.setItem(row, 10, QTableWidgetItem(duree.text()))
            self.table.setItem(row, 11, QTableWidgetItem(amortissement.text()))
            self.table.setItem(row, 12, QTableWidgetItem(observation.text()))
            self.changements_detectes = True

    def modifier_travaux(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier projet/travaux")
        form = QFormLayout()
        def safe_text(item):
            return item.text() if item else ""
        nom_projet = QLineEdit(safe_text(self.table.item(row, 1)))
        type_travaux = QComboBox(); type_travaux.addItems(["Construction", "Réhabilitation", "Maintenance", "Étude", "Autre"])
        type_travaux.setCurrentText(safe_text(self.table.item(row, 2)))
        responsable = QLineEdit(safe_text(self.table.item(row, 3)))
        date_debut = QLineEdit(safe_text(self.table.item(row, 4)))
        date_fin = QLineEdit(safe_text(self.table.item(row, 5)))
        statut = QComboBox(); statut.addItems(["En cours", "Terminé", "En attente", "Suspendu"])
        statut.setCurrentText(safe_text(self.table.item(row, 6)))
        cout_estime = QLineEdit(safe_text(self.table.item(row, 7)))
        cout_reel = QLineEdit(safe_text(self.table.item(row, 8)))
        valeur_ajoutee = QLineEdit(safe_text(self.table.item(row, 9)))
        duree = QLineEdit(safe_text(self.table.item(row, 10)))
        amortissement = QLineEdit(safe_text(self.table.item(row, 11)))
        observation = QLineEdit(safe_text(self.table.item(row, 12)))
        form.addRow("Nom projet:", nom_projet)
        form.addRow("Type:", type_travaux)
        form.addRow("Responsable:", responsable)
        form.addRow("Date début:", date_debut)
        form.addRow("Date fin:", date_fin)
        form.addRow("Statut:", statut)
        form.addRow("Coût estimé:", cout_estime)
        form.addRow("Coût réel:", cout_reel)
        form.addRow("Valeur ajoutée:", valeur_ajoutee)
        form.addRow("Durée (jours):", duree)
        form.addRow("Amortissement:", amortissement)
        form.addRow("Observation:", observation)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            self.table.setItem(row, 1, QTableWidgetItem(nom_projet.text()))
            self.table.setItem(row, 2, QTableWidgetItem(type_travaux.currentText()))
            self.table.setItem(row, 3, QTableWidgetItem(responsable.text()))
            self.table.setItem(row, 4, QTableWidgetItem(date_debut.text()))
            self.table.setItem(row, 5, QTableWidgetItem(date_fin.text()))
            self.table.setItem(row, 6, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 7, QTableWidgetItem(cout_estime.text()))
            self.table.setItem(row, 8, QTableWidgetItem(cout_reel.text()))
            self.table.setItem(row, 9, QTableWidgetItem(valeur_ajoutee.text()))
            self.table.setItem(row, 10, QTableWidgetItem(duree.text()))
            self.table.setItem(row, 11, QTableWidgetItem(amortissement.text()))
            self.table.setItem(row, 12, QTableWidgetItem(observation.text()))
            self.changements_detectes = True

    def supprimer_travaux(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à supprimer.")
            return
        row = selected[0].row()
        self.table.removeRow(row)
        self.changements_detectes = True

    def exporter_excel(self):
        try:
            import xlsxwriter
            file, _ = QFileDialog.getSaveFileName(self, "Exporter Excel", "", "Excel (*.xlsx)")
            if not file:
                return
            workbook = xlsxwriter.Workbook(file)
            worksheet = workbook.add_worksheet()
            for col in range(self.table.columnCount()):
                worksheet.write(0, col, self.table.horizontalHeaderItem(col).text())
            for row in range(self.table.rowCount()):
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    worksheet.write(row+1, col, item.text() if item else "")
            workbook.close()
            QMessageBox.information(self, "Export Excel", "Export Excel terminé.")
        except ImportError:
            QMessageBox.warning(self, "Export Excel", "Le module xlsxwriter n'est pas installé.")
        except Exception as e:
            QMessageBox.warning(self, "Export Excel", f"Erreur lors de l'export : {e}")

    def synchroniser(self):
        # ...logique de synchronisation avec la base de données...
        QMessageBox.information(self, "Synchronisation", "Synchronisation des travaux effectuée.")
        self.changements_detectes = False

    def sauvegarde_auto(self):
        if self.changements_detectes:
            # ...logique de sauvegarde...
            self.changements_detectes = False

    def actualiser(self):
        # ...logique pour recharger les données...
        QMessageBox.information(self, "Actualisation", "Actualisation des travaux effectuée.")
