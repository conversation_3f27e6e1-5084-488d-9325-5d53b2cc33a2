<!-- Generated by SpecStory -->

# /fix L'expression de type "None" ne peut pas être  (2025-06-08 14:10:35)

_**User**_

/fix L'expression de type "None" ne peut pas être affectée au paramètre de type "Dict[str, Any]"
  « None » n’est pas assignable à « Dict[str, Any] »

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Le problème vient du fait que le paramètre `params` a pour type `Dict[str, Any]`, mais sa valeur par défaut est `None`. Il faut donc annoter le type comme étant optionnel (`Optional[Dict[str, Any]]`).

````python
# ...existing code...
    def execute_query(self, query_name: str, connection: Any, params: Optional[Dict[str, Any]] = None) -> Optional[List[Any]]:
# ...existing code...
````

---

