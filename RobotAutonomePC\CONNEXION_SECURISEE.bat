@echo off
title 🔐 ROBOT IA AUTONOME PC - CONNEXION SÉCURISÉE
color 0C
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🔐 ROBOT IA AUTONOME PC - CONNEXION SÉCURISÉE                          ██
echo ██    🛡️ AUTHENTIFICATION ULTRA-SÉCURISÉE REQUISE                            ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🔒 SYSTÈME DE SÉCURITÉ ACTIVÉ
echo 🛡️ Chiffrement AES-256 + SHA-512
echo 🔐 Authentification multi-niveaux
echo ⚠️  ACCÈS PERSONNEL UNIQUEMENT
echo.

REM Vérification Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python requis pour l'authentification !
    echo 📥 Installez Python depuis python.org
    pause
    exit /b 1
)

REM Vérification module authentification
if not exist "modules\ultra_secure_authentication.py" (
    echo ❌ Module authentification manquant !
    echo 🔧 Vérifiez l'installation du Robot IA
    pause
    exit /b 1
)

echo 🔄 Initialisation système sécurité...
timeout /t 2 >nul

REM Lancement authentification
echo 🔐 LANCEMENT AUTHENTIFICATION...
echo.
python modules\ultra_secure_authentication.py

REM Vérification succès authentification
if %errorLevel% equ 0 (
    echo.
    echo ✅ AUTHENTIFICATION RÉUSSIE !
    echo 🚀 Lancement Robot IA Autonome PC...
    echo.
    timeout /t 2 >nul
    
    REM Lancement application principale
    if exist "robot_autonome_pc.py" (
        python robot_autonome_pc.py
    ) else if exist "LANCEMENT_FINAL_UNIVERSEL.bat" (
        call "LANCEMENT_FINAL_UNIVERSEL.bat"
    ) else (
        echo ❌ Application principale non trouvée !
    )
) else (
    echo.
    echo ❌ AUTHENTIFICATION ÉCHOUÉE !
    echo 🔒 Accès refusé pour sécurité
    echo.
    timeout /t 3 >nul
)

echo.
echo 👋 Session terminée
pause
exit /b 0
