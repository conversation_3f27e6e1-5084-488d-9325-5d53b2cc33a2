#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Système de Bases de Données Révolutionnaire
Bases performantes • Import/Export multi-formats • Détection anomalies
IA Interne Ollama • Mise à jour automatique • Sauvegardes • Alertes
"""

import sys
import os
import json
import sqlite3
import requests
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path

# Imports conditionnels pour éviter les erreurs
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ Pandas non disponible - Fonctionnalités limitées")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ Psutil non disponible - Détection stockage limitée")

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QProgressBar, QTextEdit, QComboBox,
    QFileDialog, QMessageBox, QTableWidget, QTableWidgetItem,
    QTabWidget, QGroupBox, QFormLayout, QLineEdit, QSpinBox,
    QCheckBox, QListWidget, QSplitter, QScrollArea
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QColor

class OllamaAIManager:
    """Gestionnaire IA interne avec Ollama"""

    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.available_models = []
        self.current_model = "llama3"
        self.check_ollama_status()

    def check_ollama_status(self):
        """Vérifie si Ollama est disponible"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                self.available_models = [model['name'] for model in models_data.get('models', [])]
                return True
        except:
            pass
        return False

    def query_ai(self, prompt, context=""):
        """Envoie une requête à l'IA Ollama"""
        if not self.available_models:
            return "❌ Ollama non disponible. Installez avec: ollama run llama3"

        try:
            payload = {
                "model": self.current_model,
                "prompt": f"{context}\n\nQuestion: {prompt}",
                "stream": False
            }

            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('response', 'Pas de réponse')
            else:
                return f"❌ Erreur API: {response.status_code}"

        except Exception as e:
            return f"❌ Erreur connexion: {str(e)}"

    def analyze_data_anomalies(self, anomalies):
        """Analyse les anomalies avec l'IA"""
        if not anomalies:
            return "✅ Aucune anomalie détectée"

        context = "Tu es un expert en analyse de données. Analyse ces anomalies et propose des solutions:"
        anomalies_text = "\n".join([f"- {a.get('description', 'Anomalie')}" for a in anomalies])

        prompt = f"Anomalies détectées:\n{anomalies_text}\n\nDonne des recommandations précises pour corriger ces problèmes."

        return self.query_ai(prompt, context)

    def suggest_database_optimization(self, db_stats):
        """Suggère des optimisations de base de données"""
        context = "Tu es un expert en optimisation de bases de données."
        prompt = f"Statistiques de la base: {db_stats}\n\nComment optimiser les performances?"

        return self.query_ai(prompt, context)

class DatabasePerformanceManager:
    """Gestionnaire de performance des bases de données"""
    
    def __init__(self):
        self.storage_locations = {
            'internal': {
                'path': './databases/internal/',
                'type': 'SSD/HDD Interne',
                'performance': 'Haute',
                'available': True
            },
            'external_hdd': {
                'path': None,
                'type': 'Disque Dur Externe',
                'performance': 'Moyenne',
                'available': False
            },
            'removable': {
                'path': None,
                'type': 'Disque Amovible',
                'performance': 'Variable',
                'available': False
            },
            'network': {
                'path': None,
                'type': 'Stockage Réseau',
                'performance': 'Réseau',
                'available': False
            }
        }
        self.detect_storage_devices()
    
    def detect_storage_devices(self):
        """Détecte automatiquement les périphériques de stockage"""
        if not PSUTIL_AVAILABLE:
            print("⚠️ Psutil non disponible - Utilisation stockage interne uniquement")
            return

        try:
            import psutil
            # Détection des disques
            partitions = psutil.disk_partitions()

            for partition in partitions:
                try:
                    if 'removable' in partition.opts:
                        self.storage_locations['removable']['path'] = partition.mountpoint
                        self.storage_locations['removable']['available'] = True
                    elif partition.device.startswith('/dev/sd') or 'USB' in partition.device:
                        self.storage_locations['external_hdd']['path'] = partition.mountpoint
                        self.storage_locations['external_hdd']['available'] = True
                except Exception as e:
                    print(f"⚠️ Erreur détection partition {partition.device}: {e}")
        except Exception as e:
            print(f"⚠️ Erreur détection stockage: {e}")
    
    def get_optimal_storage(self, data_size_mb):
        """Détermine le stockage optimal selon la taille"""
        if data_size_mb < 100:  # < 100MB
            return 'internal'
        elif data_size_mb < 1000:  # < 1GB
            return 'external_hdd' if self.storage_locations['external_hdd']['available'] else 'internal'
        else:  # > 1GB
            return 'network' if self.storage_locations['network']['available'] else 'external_hdd'

class DataFormatConverter:
    """Convertisseur multi-formats automatique"""
    
    def __init__(self):
        self.supported_formats = {
            'excel': ['.xlsx', '.xls', '.xlsm', '.xlsb'],
            'csv': ['.csv', '.tsv'],
            'json': ['.json', '.jsonl'],
            'xml': ['.xml'],
            'database': ['.db', '.sqlite', '.sqlite3'],
            'text': ['.txt', '.dat'],
            'parquet': ['.parquet'],
            'feather': ['.feather'],
            'hdf5': ['.h5', '.hdf5'],
            'pickle': ['.pkl', '.pickle']
        }
    
    def detect_format(self, file_path):
        """Détecte automatiquement le format du fichier"""
        extension = Path(file_path).suffix.lower()
        
        for format_type, extensions in self.supported_formats.items():
            if extension in extensions:
                return format_type
        return 'unknown'
    
    def convert_to_dataframe(self, file_path, format_type=None):
        """Convertit n'importe quel format en DataFrame"""
        if not PANDAS_AVAILABLE:
            raise Exception("Pandas requis pour la conversion de données")

        if format_type is None:
            format_type = self.detect_format(file_path)

        try:
            import pandas as pd

            if format_type == 'excel':
                return pd.read_excel(file_path, sheet_name=None)  # Toutes les feuilles
            elif format_type == 'csv':
                return pd.read_csv(file_path, encoding='utf-8')
            elif format_type == 'json':
                return pd.read_json(file_path)
            elif format_type == 'database':
                conn = sqlite3.connect(file_path)
                tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table'", conn)
                data = {}
                for table in tables['name']:
                    data[table] = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                conn.close()
                return data
            else:
                # Fallback simple pour CSV
                return pd.read_csv(file_path, sep='\t')
        except Exception as e:
            raise Exception(f"Erreur conversion {format_type}: {str(e)}")
    
    def export_dataframe(self, df, file_path, format_type):
        """Exporte DataFrame vers n'importe quel format"""
        try:
            if format_type == 'excel':
                df.to_excel(file_path, index=False)
            elif format_type == 'csv':
                df.to_csv(file_path, index=False, encoding='utf-8')
            elif format_type == 'json':
                df.to_json(file_path, orient='records', indent=2)
            elif format_type == 'xml':
                df.to_xml(file_path)
            elif format_type == 'parquet':
                df.to_parquet(file_path)
            elif format_type == 'feather':
                df.to_feather(file_path)
            elif format_type == 'hdf5':
                df.to_hdf(file_path, key='data')
            elif format_type == 'pickle':
                df.to_pickle(file_path)
            return True
        except Exception as e:
            raise Exception(f"Erreur export {format_type}: {str(e)}")

class AnomalyDetector:
    """Détecteur d'anomalies avancé"""
    
    def __init__(self):
        self.anomaly_types = {
            'missing_values': 'Valeurs manquantes',
            'duplicates': 'Doublons/Redondances',
            'outliers': 'Valeurs aberrantes',
            'data_types': 'Types de données incohérents',
            'format_errors': 'Erreurs de format',
            'encoding_issues': 'Problèmes d\'encodage',
            'constraint_violations': 'Violations de contraintes',
            'referential_integrity': 'Intégrité référentielle'
        }
    
    def detect_all_anomalies(self, df, table_name=""):
        """Détecte toutes les anomalies dans un DataFrame"""
        anomalies = []
        
        # 1. Valeurs manquantes
        missing = self.detect_missing_values(df)
        if missing:
            anomalies.extend(missing)
        
        # 2. Doublons
        duplicates = self.detect_duplicates(df)
        if duplicates:
            anomalies.extend(duplicates)
        
        # 3. Valeurs aberrantes
        outliers = self.detect_outliers(df)
        if outliers:
            anomalies.extend(outliers)
        
        # 4. Types de données
        type_issues = self.detect_data_type_issues(df)
        if type_issues:
            anomalies.extend(type_issues)
        
        # 5. Erreurs de format
        format_errors = self.detect_format_errors(df)
        if format_errors:
            anomalies.extend(format_errors)
        
        return anomalies
    
    def detect_missing_values(self, df):
        """Détecte les valeurs manquantes"""
        anomalies = []
        missing_counts = df.isnull().sum()
        
        for column, count in missing_counts.items():
            if count > 0:
                percentage = (count / len(df)) * 100
                anomalies.append({
                    'type': 'missing_values',
                    'severity': 'high' if percentage > 50 else 'medium' if percentage > 10 else 'low',
                    'column': column,
                    'count': count,
                    'percentage': percentage,
                    'description': f"Colonne '{column}': {count} valeurs manquantes ({percentage:.1f}%)",
                    'solution': self.get_missing_values_solution(percentage)
                })
        
        return anomalies
    
    def detect_duplicates(self, df):
        """Détecte les doublons et redondances"""
        anomalies = []
        
        # Doublons complets
        duplicate_rows = df.duplicated().sum()
        if duplicate_rows > 0:
            anomalies.append({
                'type': 'duplicates',
                'severity': 'medium',
                'count': duplicate_rows,
                'description': f"{duplicate_rows} lignes dupliquées complètement",
                'solution': "Supprimer les doublons avec df.drop_duplicates()"
            })
        
        # Doublons par colonne clé
        for column in df.columns:
            if df[column].dtype == 'object' or 'id' in column.lower():
                duplicate_values = df[column].duplicated().sum()
                if duplicate_values > 0:
                    anomalies.append({
                        'type': 'duplicates',
                        'severity': 'high' if 'id' in column.lower() else 'low',
                        'column': column,
                        'count': duplicate_values,
                        'description': f"Colonne '{column}': {duplicate_values} valeurs dupliquées",
                        'solution': f"Vérifier l'unicité de la colonne '{column}'"
                    })
        
        return anomalies
    
    def detect_outliers(self, df):
        """Détecte les valeurs aberrantes"""
        anomalies = []
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            Q1 = df[column].quantile(0.25)
            Q3 = df[column].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]
            
            if len(outliers) > 0:
                anomalies.append({
                    'type': 'outliers',
                    'severity': 'medium',
                    'column': column,
                    'count': len(outliers),
                    'description': f"Colonne '{column}': {len(outliers)} valeurs aberrantes",
                    'details': f"Limites: [{lower_bound:.2f}, {upper_bound:.2f}]",
                    'solution': "Vérifier et corriger ou supprimer les valeurs aberrantes"
                })
        
        return anomalies
    
    def detect_data_type_issues(self, df):
        """Détecte les problèmes de types de données"""
        anomalies = []
        
        for column in df.columns:
            # Vérifier si une colonne numérique contient du texte
            if df[column].dtype == 'object':
                # Essayer de convertir en numérique
                numeric_convertible = pd.to_numeric(df[column], errors='coerce')
                non_numeric_count = numeric_convertible.isnull().sum() - df[column].isnull().sum()
                
                if non_numeric_count > 0 and non_numeric_count < len(df) * 0.9:
                    anomalies.append({
                        'type': 'data_types',
                        'severity': 'medium',
                        'column': column,
                        'count': non_numeric_count,
                        'description': f"Colonne '{column}': {non_numeric_count} valeurs non-numériques dans une colonne potentiellement numérique",
                        'solution': "Nettoyer les données ou changer le type de colonne"
                    })
        
        return anomalies
    
    def detect_format_errors(self, df):
        """Détecte les erreurs de format"""
        anomalies = []
        
        for column in df.columns:
            if df[column].dtype == 'object':
                # Vérifier les formats de date
                if 'date' in column.lower() or 'time' in column.lower():
                    try:
                        pd.to_datetime(df[column], errors='coerce')
                        invalid_dates = pd.to_datetime(df[column], errors='coerce').isnull().sum() - df[column].isnull().sum()
                        if invalid_dates > 0:
                            anomalies.append({
                                'type': 'format_errors',
                                'severity': 'high',
                                'column': column,
                                'count': invalid_dates,
                                'description': f"Colonne '{column}': {invalid_dates} formats de date invalides",
                                'solution': "Standardiser le format de date"
                            })
                    except:
                        pass
                
                # Vérifier les emails
                if 'email' in column.lower() or 'mail' in column.lower():
                    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                    invalid_emails = ~df[column].str.match(email_pattern, na=False)
                    invalid_count = invalid_emails.sum()
                    if invalid_count > 0:
                        anomalies.append({
                            'type': 'format_errors',
                            'severity': 'medium',
                            'column': column,
                            'count': invalid_count,
                            'description': f"Colonne '{column}': {invalid_count} formats d'email invalides",
                            'solution': "Corriger les formats d'email"
                        })
        
        return anomalies
    
    def get_missing_values_solution(self, percentage):
        """Propose une solution pour les valeurs manquantes"""
        if percentage > 50:
            return "Considérer supprimer la colonne ou collecter plus de données"
        elif percentage > 10:
            return "Imputer avec la médiane/mode ou utiliser des techniques avancées"
        else:
            return "Imputer avec la moyenne/médiane ou supprimer les lignes"

class AutoBackupManager:
    """Gestionnaire de sauvegardes automatiques"""
    
    def __init__(self, base_path="./backups/"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
        self.max_backups = 3
    
    def create_backup(self, source_path, backup_name=None):
        """Crée une sauvegarde avec rotation automatique"""
        if backup_name is None:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Créer le dossier de sauvegarde
        backup_dir = self.base_path / backup_name
        backup_dir.mkdir(exist_ok=True)
        
        # Copier les fichiers
        if os.path.isfile(source_path):
            shutil.copy2(source_path, backup_dir)
        else:
            shutil.copytree(source_path, backup_dir / Path(source_path).name, dirs_exist_ok=True)
        
        # Rotation des sauvegardes
        self.rotate_backups()
        
        return backup_dir
    
    def rotate_backups(self):
        """Garde seulement les 3 dernières sauvegardes"""
        backups = sorted([d for d in self.base_path.iterdir() if d.is_dir()], 
                        key=lambda x: x.stat().st_mtime, reverse=True)
        
        # Supprimer les anciennes sauvegardes
        for old_backup in backups[self.max_backups:]:
            shutil.rmtree(old_backup)
    
    def get_backup_list(self):
        """Retourne la liste des sauvegardes disponibles"""
        backups = []
        for backup_dir in sorted(self.base_path.iterdir(), key=lambda x: x.stat().st_mtime, reverse=True):
            if backup_dir.is_dir():
                backups.append({
                    'name': backup_dir.name,
                    'path': str(backup_dir),
                    'date': datetime.fromtimestamp(backup_dir.stat().st_mtime),
                    'size': sum(f.stat().st_size for f in backup_dir.rglob('*') if f.is_file())
                })
        return backups

class ProgressGauge(QWidget):
    """Jauge de progression graphique avancée"""
    
    def __init__(self, title="Progression", parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 14pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 8px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Barre de progression principale
        self.main_progress = QProgressBar()
        self.main_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #FFD700;
                border-radius: 8px;
                background: rgba(0,0,0,0.5);
                text-align: center;
                color: white;
                font-weight: bold;
                font-size: 12pt;
                height: 30px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFD700, stop:0.5 #FFA500, stop:1 #FF8C00);
                border-radius: 6px;
            }
        """)
        self.main_progress.setRange(0, 100)
        layout.addWidget(self.main_progress)
        
        # Informations détaillées
        self.info_label = QLabel("Prêt...")
        self.info_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(0,0,0,0.3);
                border: 1px solid #FFD700;
                border-radius: 6px;
                padding: 5px;
                margin-top: 5px;
            }
        """)
        layout.addWidget(self.info_label)
        
        # Statistiques
        stats_layout = QHBoxLayout()
        
        self.speed_label = QLabel("Vitesse: 0 MB/s")
        self.speed_label.setStyleSheet("color: white; font-size: 9pt;")
        stats_layout.addWidget(self.speed_label)
        
        self.eta_label = QLabel("ETA: --:--")
        self.eta_label.setStyleSheet("color: white; font-size: 9pt;")
        stats_layout.addWidget(self.eta_label)
        
        self.size_label = QLabel("Taille: 0 MB")
        self.size_label.setStyleSheet("color: white; font-size: 9pt;")
        stats_layout.addWidget(self.size_label)
        
        layout.addLayout(stats_layout)
    
    def update_progress(self, value, info="", speed=0, eta="", size=0):
        """Met à jour la jauge de progression"""
        self.main_progress.setValue(value)
        if info:
            self.info_label.setText(info)
        if speed > 0:
            self.speed_label.setText(f"Vitesse: {speed:.1f} MB/s")
        if eta:
            self.eta_label.setText(f"ETA: {eta}")
        if size > 0:
            self.size_label.setText(f"Taille: {size:.1f} MB")

class AlertSystem(QWidget):
    """Système d'alertes avancé avec solutions"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.alerts = []
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Titre
        title = QLabel("🚨 SYSTÈME D'ALERTES")
        title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Liste des alertes
        self.alerts_list = QListWidget()
        self.alerts_list.setStyleSheet("""
            QListWidget {
                background: rgba(0,0,0,0.5);
                border: 2px solid #FFD700;
                border-radius: 8px;
                color: white;
                font-size: 10pt;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255,215,0,0.3);
            }
            QListWidget::item:selected {
                background: rgba(255,215,0,0.3);
            }
        """)
        layout.addWidget(self.alerts_list)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        self.auto_fix_btn = QPushButton("🔧 Correction Automatique")
        self.auto_fix_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00AA00, stop:1 #008800);
                color: white;
                border: 2px solid #00FF00;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FF00, stop:1 #00CC00);
            }
        """)
        self.auto_fix_btn.clicked.connect(self.auto_fix_selected)
        buttons_layout.addWidget(self.auto_fix_btn)
        
        self.ignore_btn = QPushButton("❌ Ignorer")
        self.ignore_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #AA0000, stop:1 #880000);
                color: white;
                border: 2px solid #FF0000;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF0000, stop:1 #CC0000);
            }
        """)
        self.ignore_btn.clicked.connect(self.ignore_selected)
        buttons_layout.addWidget(self.ignore_btn)
        
        layout.addLayout(buttons_layout)
    
    def add_alert(self, anomaly):
        """Ajoute une alerte basée sur une anomalie"""
        severity_colors = {
            'high': '🔴',
            'medium': '🟡', 
            'low': '🟢'
        }
        
        severity_icon = severity_colors.get(anomaly.get('severity', 'medium'), '🟡')
        alert_text = f"{severity_icon} {anomaly['description']}"
        
        if 'solution' in anomaly:
            alert_text += f"\n💡 Solution: {anomaly['solution']}"
        
        self.alerts_list.addItem(alert_text)
        self.alerts.append(anomaly)
    
    def auto_fix_selected(self):
        """Applique la correction automatique"""
        current_row = self.alerts_list.currentRow()
        if current_row >= 0:
            anomaly = self.alerts[current_row]
            # Ici on implémenterait la correction automatique
            QMessageBox.information(self, "Correction", f"Correction automatique appliquée pour:\n{anomaly['description']}")
            self.alerts_list.takeItem(current_row)
            del self.alerts[current_row]
    
    def ignore_selected(self):
        """Ignore l'alerte sélectionnée"""
        current_row = self.alerts_list.currentRow()
        if current_row >= 0:
            self.alerts_list.takeItem(current_row)
            del self.alerts[current_row]
