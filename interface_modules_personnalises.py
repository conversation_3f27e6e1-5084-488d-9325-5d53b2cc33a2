#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique pour les modules personnalisés
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QDateEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox, QDialog,
    QDialogButtonBox, QScrollArea, QFrame, QColorDialog
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QColor
import json
from modules_personnalises import ModulesPersonnalises

class InterfaceModulesPersonnalises(QWidget):
    """Interface pour gérer les modules personnalisés"""
    
    def __init__(self, db_connection, parent=None):
        super().__init__(parent)
        self.conn = db_connection
        self.modules_manager = ModulesPersonnalises(db_connection)
        self.setup_ui()
        self.charger_secteurs()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # En-tête
        header = QLabel("🎯 Modules Personnalisés par Secteur")
        header.setStyleSheet("""
            font-size: 18pt;
            font-weight: bold;
            color: #1976d2;
            margin: 10px;
        """)
        layout.addWidget(header)
        
        # Onglets principaux
        self.tabs = QTabWidget()
        
        # Onglet gestion des secteurs
        self.tabs.addTab(self.create_secteurs_tab(), "🏢 Secteurs Disponibles")
        
        # Onglet création de secteur
        self.tabs.addTab(self.create_nouveau_secteur_tab(), "➕ Nouveau Secteur")
        
        # Onglet saisie par secteur
        self.tabs.addTab(self.create_saisie_secteur_tab(), "📝 Saisie par Secteur")
        
        # Onglet recherche avancée
        self.tabs.addTab(self.create_recherche_avancee_tab(), "🔍 Recherche Avancée")
        
        # Onglet rapports sectoriels
        self.tabs.addTab(self.create_rapports_tab(), "📊 Rapports Sectoriels")
        
        layout.addWidget(self.tabs)
    
    def create_secteurs_tab(self):
        """Crée l'onglet de gestion des secteurs"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Tableau des secteurs
        self.table_secteurs = QTableWidget()
        self.table_secteurs.setColumnCount(6)
        self.table_secteurs.setHorizontalHeaderLabels([
            "ID", "Icône", "Nom", "Description", "Couleur", "Actions"
        ])
        
        layout.addWidget(self.table_secteurs)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        btn_refresh = QPushButton("🔄 Actualiser")
        btn_refresh.clicked.connect(self.charger_secteurs)
        buttons_layout.addWidget(btn_refresh)
        
        btn_modifier = QPushButton("✏️ Modifier Secteur")
        btn_modifier.clicked.connect(self.modifier_secteur)
        buttons_layout.addWidget(btn_modifier)
        
        btn_criteres = QPushButton("⚙️ Gérer Critères")
        btn_criteres.clicked.connect(self.gerer_criteres)
        buttons_layout.addWidget(btn_criteres)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        return widget
    
    def create_nouveau_secteur_tab(self):
        """Crée l'onglet de création de nouveau secteur"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Formulaire de création
        form_group = QGroupBox("➕ Créer un Nouveau Secteur Personnalisé")
        form_layout = QFormLayout(form_group)
        
        # Champs de base
        self.nouveau_nom = QLineEdit()
        self.nouveau_nom.setPlaceholderText("Ex: Équipements Sportifs")
        form_layout.addRow("Nom du secteur:", self.nouveau_nom)
        
        self.nouveau_description = QTextEdit()
        self.nouveau_description.setMaximumHeight(80)
        self.nouveau_description.setPlaceholderText("Description détaillée du secteur...")
        form_layout.addRow("Description:", self.nouveau_description)
        
        # Couleur et icône
        color_layout = QHBoxLayout()
        
        self.nouveau_couleur = QPushButton("🎨 Choisir Couleur")
        self.nouveau_couleur.clicked.connect(self.choisir_couleur)
        self.couleur_selectionnee = "#1976d2"
        color_layout.addWidget(self.nouveau_couleur)
        
        self.nouveau_icone = QLineEdit()
        self.nouveau_icone.setText("🏢")
        self.nouveau_icone.setMaximumWidth(60)
        color_layout.addWidget(QLabel("Icône:"))
        color_layout.addWidget(self.nouveau_icone)
        
        color_layout.addStretch()
        form_layout.addRow("Apparence:", color_layout)
        
        layout.addWidget(form_group)
        
        # Section critères
        criteres_group = QGroupBox("⚙️ Critères Spécifiques")
        criteres_layout = QVBoxLayout(criteres_group)
        
        # Zone de scroll pour les critères
        scroll = QScrollArea()
        self.criteres_widget = QWidget()
        self.criteres_layout = QVBoxLayout(self.criteres_widget)
        scroll.setWidget(self.criteres_widget)
        scroll.setWidgetResizable(True)
        scroll.setMaximumHeight(300)
        
        criteres_layout.addWidget(scroll)
        
        # Boutons pour gérer les critères
        criteres_buttons = QHBoxLayout()
        
        btn_add_critere = QPushButton("➕ Ajouter Critère")
        btn_add_critere.clicked.connect(self.ajouter_critere)
        criteres_buttons.addWidget(btn_add_critere)
        
        btn_remove_critere = QPushButton("➖ Supprimer Dernier")
        btn_remove_critere.clicked.connect(self.supprimer_dernier_critere)
        criteres_buttons.addWidget(btn_remove_critere)
        
        criteres_buttons.addStretch()
        criteres_layout.addLayout(criteres_buttons)
        
        layout.addWidget(criteres_group)
        
        # Bouton de création
        btn_creer = QPushButton("🚀 Créer le Secteur")
        btn_creer.clicked.connect(self.creer_nouveau_secteur)
        btn_creer.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-weight: bold;
                font-size: 12pt;
            }
        """)
        layout.addWidget(btn_creer)
        
        # Initialiser avec un critère par défaut
        self.criteres_widgets = []
        self.ajouter_critere()
        
        return widget
    
    def create_saisie_secteur_tab(self):
        """Crée l'onglet de saisie par secteur"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Sélection du secteur
        secteur_group = QGroupBox("🎯 Sélection du Secteur")
        secteur_layout = QHBoxLayout(secteur_group)
        
        self.combo_secteur_saisie = QComboBox()
        self.combo_secteur_saisie.currentTextChanged.connect(self.charger_criteres_saisie)
        secteur_layout.addWidget(QLabel("Secteur:"))
        secteur_layout.addWidget(self.combo_secteur_saisie)
        
        secteur_layout.addStretch()
        layout.addWidget(secteur_group)
        
        # Zone de saisie dynamique
        self.saisie_scroll = QScrollArea()
        self.saisie_widget = QWidget()
        self.saisie_layout = QFormLayout(self.saisie_widget)
        self.saisie_scroll.setWidget(self.saisie_widget)
        self.saisie_scroll.setWidgetResizable(True)
        
        layout.addWidget(self.saisie_scroll)
        
        # Boutons d'action
        saisie_buttons = QHBoxLayout()
        
        btn_sauvegarder = QPushButton("💾 Sauvegarder")
        btn_sauvegarder.clicked.connect(self.sauvegarder_saisie)
        saisie_buttons.addWidget(btn_sauvegarder)
        
        btn_reset = QPushButton("🔄 Réinitialiser")
        btn_reset.clicked.connect(self.reset_saisie)
        saisie_buttons.addWidget(btn_reset)
        
        saisie_buttons.addStretch()
        layout.addLayout(saisie_buttons)
        
        return widget
    
    def create_recherche_avancee_tab(self):
        """Crée l'onglet de recherche avancée"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Sélection du secteur pour recherche
        search_group = QGroupBox("🔍 Recherche par Critères Spécifiques")
        search_layout = QVBoxLayout(search_group)
        
        # Secteur
        secteur_layout = QHBoxLayout()
        self.combo_secteur_recherche = QComboBox()
        self.combo_secteur_recherche.currentTextChanged.connect(self.charger_criteres_recherche)
        secteur_layout.addWidget(QLabel("Secteur:"))
        secteur_layout.addWidget(self.combo_secteur_recherche)
        secteur_layout.addStretch()
        search_layout.addLayout(secteur_layout)
        
        # Zone de critères de recherche
        self.recherche_scroll = QScrollArea()
        self.recherche_widget = QWidget()
        self.recherche_layout = QFormLayout(self.recherche_widget)
        self.recherche_scroll.setWidget(self.recherche_widget)
        self.recherche_scroll.setWidgetResizable(True)
        self.recherche_scroll.setMaximumHeight(200)
        
        search_layout.addWidget(self.recherche_scroll)
        
        # Bouton de recherche
        btn_rechercher = QPushButton("🔍 Rechercher")
        btn_rechercher.clicked.connect(self.effectuer_recherche)
        search_layout.addWidget(btn_rechercher)
        
        layout.addWidget(search_group)
        
        # Résultats de recherche
        resultats_group = QGroupBox("📋 Résultats de Recherche")
        resultats_layout = QVBoxLayout(resultats_group)
        
        self.table_resultats = QTableWidget()
        self.table_resultats.setColumnCount(4)
        self.table_resultats.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur", "Localisation"
        ])
        
        resultats_layout.addWidget(self.table_resultats)
        layout.addWidget(resultats_group)
        
        return widget
    
    def create_rapports_tab(self):
        """Crée l'onglet des rapports sectoriels"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Sélection du secteur pour rapport
        rapport_group = QGroupBox("📊 Génération de Rapport Sectoriel")
        rapport_layout = QVBoxLayout(rapport_group)
        
        secteur_layout = QHBoxLayout()
        self.combo_secteur_rapport = QComboBox()
        secteur_layout.addWidget(QLabel("Secteur:"))
        secteur_layout.addWidget(self.combo_secteur_rapport)
        
        btn_generer_rapport = QPushButton("📊 Générer Rapport")
        btn_generer_rapport.clicked.connect(self.generer_rapport)
        secteur_layout.addWidget(btn_generer_rapport)
        
        secteur_layout.addStretch()
        rapport_layout.addLayout(secteur_layout)
        
        layout.addWidget(rapport_group)
        
        # Zone d'affichage du rapport
        self.rapport_text = QTextEdit()
        self.rapport_text.setReadOnly(True)
        layout.addWidget(self.rapport_text)
        
        return widget
    
    def charger_secteurs(self):
        """Charge la liste des secteurs dans les différents composants"""
        secteurs = self.modules_manager.get_secteurs_disponibles()
        
        # Mettre à jour le tableau des secteurs
        self.table_secteurs.setRowCount(len(secteurs))
        
        for i, secteur in enumerate(secteurs):
            self.table_secteurs.setItem(i, 0, QTableWidgetItem(str(secteur['id'])))
            self.table_secteurs.setItem(i, 1, QTableWidgetItem(secteur['icone']))
            self.table_secteurs.setItem(i, 2, QTableWidgetItem(secteur['nom']))
            self.table_secteurs.setItem(i, 3, QTableWidgetItem(secteur['description']))
            
            # Couleur
            color_item = QTableWidgetItem(secteur['couleur'])
            color_item.setBackground(QColor(secteur['couleur']))
            self.table_secteurs.setItem(i, 4, color_item)
            
            # Bouton d'actions
            self.table_secteurs.setItem(i, 5, QTableWidgetItem("Gérer"))
        
        # Mettre à jour les combobox
        secteur_names = [f"{s['icone']} {s['nom']}" for s in secteurs]
        
        self.combo_secteur_saisie.clear()
        self.combo_secteur_saisie.addItems(secteur_names)
        
        self.combo_secteur_recherche.clear()
        self.combo_secteur_recherche.addItems(secteur_names)
        
        self.combo_secteur_rapport.clear()
        self.combo_secteur_rapport.addItems(secteur_names)
    
    def choisir_couleur(self):
        """Ouvre le sélecteur de couleur"""
        color = QColorDialog.getColor(QColor(self.couleur_selectionnee), self)
        if color.isValid():
            self.couleur_selectionnee = color.name()
            self.nouveau_couleur.setStyleSheet(f"background-color: {self.couleur_selectionnee}; color: white;")
            self.nouveau_couleur.setText(f"🎨 {self.couleur_selectionnee}")
    
    def ajouter_critere(self):
        """Ajoute un nouveau critère à la liste"""
        critere_frame = QFrame()
        critere_frame.setFrameStyle(QFrame.Shape.Box)
        critere_layout = QHBoxLayout(critere_frame)
        
        # Nom du critère
        nom_input = QLineEdit()
        nom_input.setPlaceholderText("Nom du critère")
        critere_layout.addWidget(QLabel("Nom:"))
        critere_layout.addWidget(nom_input)
        
        # Type du critère
        type_combo = QComboBox()
        type_combo.addItems(["text", "number", "date", "select", "boolean"])
        critere_layout.addWidget(QLabel("Type:"))
        critere_layout.addWidget(type_combo)
        
        # Valeurs possibles (pour select)
        valeurs_input = QLineEdit()
        valeurs_input.setPlaceholderText("Valeurs séparées par des virgules")
        critere_layout.addWidget(QLabel("Valeurs:"))
        critere_layout.addWidget(valeurs_input)
        
        # Obligatoire
        obligatoire_check = QCheckBox("Obligatoire")
        critere_layout.addWidget(obligatoire_check)
        
        self.criteres_layout.addWidget(critere_frame)
        
        # Stocker les widgets pour récupération
        self.criteres_widgets.append({
            'frame': critere_frame,
            'nom': nom_input,
            'type': type_combo,
            'valeurs': valeurs_input,
            'obligatoire': obligatoire_check
        })
    
    def supprimer_dernier_critere(self):
        """Supprime le dernier critère ajouté"""
        if self.criteres_widgets:
            critere = self.criteres_widgets.pop()
            critere['frame'].deleteLater()
    
    def creer_nouveau_secteur(self):
        """Crée un nouveau secteur avec ses critères"""
        nom = self.nouveau_nom.text().strip()
        description = self.nouveau_description.toPlainText().strip()
        icone = self.nouveau_icone.text().strip()
        
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du secteur est obligatoire")
            return
        
        # Récupérer les critères
        criteres = []
        for critere_widget in self.criteres_widgets:
            nom_critere = critere_widget['nom'].text().strip()
            if nom_critere:
                type_critere = critere_widget['type'].currentText()
                valeurs_text = critere_widget['valeurs'].text().strip()
                obligatoire = critere_widget['obligatoire'].isChecked()
                
                critere = {
                    'nom': nom_critere,
                    'type': type_critere,
                    'obligatoire': 1 if obligatoire else 0
                }
                
                if type_critere == 'select' and valeurs_text:
                    critere['valeurs'] = [v.strip() for v in valeurs_text.split(',')]
                
                criteres.append(critere)
        
        try:
            secteur_id = self.modules_manager.creer_secteur_personnalise(
                nom, description, self.couleur_selectionnee, icone, criteres
            )
            
            QMessageBox.information(self, "Succès", f"Secteur '{nom}' créé avec succès!")
            
            # Réinitialiser le formulaire
            self.nouveau_nom.clear()
            self.nouveau_description.clear()
            self.nouveau_icone.setText("🏢")
            self.couleur_selectionnee = "#1976d2"
            self.nouveau_couleur.setStyleSheet("")
            self.nouveau_couleur.setText("🎨 Choisir Couleur")
            
            # Supprimer tous les critères
            for critere_widget in self.criteres_widgets:
                critere_widget['frame'].deleteLater()
            self.criteres_widgets.clear()
            self.ajouter_critere()
            
            # Recharger les secteurs
            self.charger_secteurs()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la création: {str(e)}")
    
    def charger_criteres_saisie(self):
        """Charge les critères pour la saisie selon le secteur sélectionné"""
        # Implementation pour charger les critères de saisie
        pass
    
    def charger_criteres_recherche(self):
        """Charge les critères pour la recherche selon le secteur sélectionné"""
        # Implementation pour charger les critères de recherche
        pass
    
    def sauvegarder_saisie(self):
        """Sauvegarde la saisie des critères"""
        # Implementation pour sauvegarder
        pass
    
    def reset_saisie(self):
        """Réinitialise la saisie"""
        # Implementation pour reset
        pass
    
    def effectuer_recherche(self):
        """Effectue la recherche selon les critères"""
        # Implementation pour recherche
        pass
    
    def generer_rapport(self):
        """Génère un rapport pour le secteur sélectionné"""
        # Implementation pour rapport
        pass
    
    def modifier_secteur(self):
        """Modifie un secteur existant"""
        # Implementation pour modification
        pass
    
    def gerer_criteres(self):
        """Gère les critères d'un secteur"""
        # Implementation pour gestion des critères
        pass
