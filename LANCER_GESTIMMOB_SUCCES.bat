@echo off
title LANCER GESTIMMOB - SUCCES GARANTI
color 0A

echo.
echo ========================================================================
echo                      🎉 LANCER GESTIMMOB - SUCCES GARANTI 🎉
echo                        Demarrage du logiciel fonctionnel
echo ========================================================================
echo.

echo [%TIME%] Lancement de GestImmob avec succes garanti...

REM Variables
set "PYTHON_CMD="
set "LAUNCH_SUCCESS=0"

echo.
echo 🔍 DETECTION PYTHON OPTIMISEE...

REM Test avec py (recommandé pour Windows)
py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :launch_gestimmob
)

REM Test avec python
python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :launch_gestimmob
)

REM Test avec python3
python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    for /f "tokens=*" %%i in ('python3 --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :launch_gestimmob
)

echo ❌ Python non detecte
echo.
echo 🚨 RESOLUTION AUTOMATIQUE PYTHON...
if exist "SOLUTION_PYTHON_FINALE.bat" (
    echo Lancement de la solution Python automatique...
    call SOLUTION_PYTHON_FINALE.bat
    
    REM Re-test après résolution
    py --version >nul 2>&1
    if not errorlevel 1 (
        set "PYTHON_CMD=py"
        echo ✅ Python maintenant disponible !
        goto :launch_gestimmob
    )
    
    python --version >nul 2>&1
    if not errorlevel 1 (
        set "PYTHON_CMD=python"
        echo ✅ Python maintenant disponible !
        goto :launch_gestimmob
    )
) else (
    echo ❌ Solution Python non trouvee
)

echo.
echo ❌ Impossible de continuer sans Python
echo.
echo 📋 INSTALLATION MANUELLE:
echo 1. Allez sur https://python.org/downloads/
echo 2. Telechargez Python 3.8+
echo 3. Cochez "Add Python to PATH"
echo 4. Relancez ce script
goto :end

:launch_gestimmob
echo.
echo 📁 VERIFICATION FICHIER GESTIMMOB...

if exist "gestimmob_final.py" (
    echo ✅ GestImmob Final trouve: gestimmob_final.py
    set "MAIN_FILE=gestimmob_final.py"
    goto :prepare_launch
) else (
    echo ❌ Fichier gestimmob_final.py non trouve
    echo.
    echo 📋 FICHIERS ALTERNATIFS:
    
    if exist "src\main.py" (
        echo ✅ Alternative: src\main.py
        set "MAIN_FILE=src\main.py"
        goto :prepare_launch
    )
    
    if exist "gestimmob_complet_final.py" (
        echo ✅ Alternative: gestimmob_complet_final.py
        set "MAIN_FILE=gestimmob_complet_final.py"
        goto :prepare_launch
    )
    
    echo ❌ Aucun fichier GestImmob trouve
    goto :end
)

:prepare_launch
echo.
echo 📦 PREPARATION ENVIRONNEMENT...

REM Créer les répertoires nécessaires
if not exist "data" (
    mkdir data
    echo ✅ Repertoire data cree
)

if not exist "logs" (
    mkdir logs
    echo ✅ Repertoire logs cree
)

if not exist "backups" (
    mkdir backups
    echo ✅ Repertoire backups cree
)

echo.
echo 🔧 VERIFICATION DEPENDANCES CRITIQUES...

REM Vérifier PySide6
echo Test PySide6...
%PYTHON_CMD% -c "import PySide6; print('✅ PySide6: Disponible')" 2>nul
if errorlevel 1 (
    echo ❌ PySide6 manquant - Installation automatique...
    %PYTHON_CMD% -m pip install PySide6 --quiet
    if not errorlevel 1 (
        echo ✅ PySide6 installe avec succes
    ) else (
        echo ⚠️ Probleme installation PySide6 - on continue...
    )
) else (
    echo ✅ PySide6 OK
)

REM Vérifier SQLite (généralement inclus avec Python)
echo Test SQLite...
%PYTHON_CMD% -c "import sqlite3; print('✅ SQLite: Disponible')" 2>nul
if not errorlevel 1 (
    echo ✅ SQLite OK
) else (
    echo ⚠️ SQLite non disponible
)

echo.
echo ========================================================================
echo                           🚀 LANCEMENT GESTIMMOB 🚀
echo ========================================================================
echo.

echo [%TIME%] Demarrage de GestImmob...
echo.
echo Configuration finale:
echo   Python: %PYTHON_CMD%
echo   Fichier: %MAIN_FILE%
echo   Repertoire: %CD%
echo.

echo 🎯 Lancement en cours...
echo.

REM Lancement de l'application
%PYTHON_CMD% %MAIN_FILE%

set "EXIT_CODE=%ERRORLEVEL%"

echo.
echo ========================================================================
echo                              RESULTAT FINAL
echo ========================================================================
echo.

if %EXIT_CODE%==0 (
    echo [%TIME%] ✅ GESTIMMOB FERME NORMALEMENT
    echo.
    echo 🎉 LANCEMENT REUSSI !
    echo ✅ GestImmob a fonctionne parfaitement
    echo ✅ Configuration: %PYTHON_CMD% + %MAIN_FILE%
    echo ✅ Toutes les fonctionnalites operationnelles
    echo.
    echo 💡 Pour relancer GestImmob:
    echo    - Executez a nouveau ce script
    echo    - Ou tapez: %PYTHON_CMD% %MAIN_FILE%
    echo.
    set /a LAUNCH_SUCCESS=1
) else (
    echo [%TIME%] ❌ GESTIMMOB FERME AVEC ERREUR (Code: %EXIT_CODE%)
    echo.
    echo 🔧 DIAGNOSTIC AUTOMATIQUE:
    
    %PYTHON_CMD% -c "print('✅ Python de base: OK')" 2>nul || echo ❌ Python de base: Probleme
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul || echo ❌ PySide6: Probleme
    %PYTHON_CMD% -c "import sqlite3; print('✅ SQLite: OK')" 2>nul || echo ❌ SQLite: Probleme
    
    echo.
    echo 💡 SOLUTIONS RAPIDES:
    echo 1. Relancez ce script
    echo 2. Installez PySide6: pip install PySide6
    echo 3. Executez SOLUTION_PYTHON_FINALE.bat
    echo 4. Verifiez les erreurs ci-dessus
)

:end
echo.
echo ========================================================================
echo                              RESUME
echo ========================================================================
echo.

echo [%TIME%] Script de lancement termine

if %LAUNCH_SUCCESS%==1 (
    echo.
    echo 🏆 SUCCES TOTAL !
    echo ✅ GestImmob lance et ferme normalement
    echo ✅ Logiciel parfaitement fonctionnel
    echo ✅ Toutes les dependances OK
    echo.
    echo 🎯 GESTIMMOB EST PRET A L EMPLOI !
    echo.
    echo 📋 FONCTIONNALITES DISPONIBLES:
    echo   🏠 Gestion des biens immobiliers
    echo   📦 Inventaire complet
    echo   🏢 Gestion des fournisseurs
    echo   💼 Module ERP/Comptabilite
    echo   🧮 Calculatrice financiere
    echo   🔍 Recherche avancee
    echo   📊 Rapports et statistiques
    echo   ⚙️ Parametres personnalisables
    echo.
    echo 🎨 INTERFACE MODERNE:
    echo   ✅ Themes dynamiques avec changement automatique
    echo   ✅ Interface professionnelle et intuitive
    echo   ✅ Tous les modules operationnels
    echo.
) else (
    echo.
    echo ⚠️ PROBLEME DE LANCEMENT
    echo.
    echo 📋 VERIFICATIONS EFFECTUEES:
    echo   - Python: %PYTHON_CMD%
    echo   - Fichier: %MAIN_FILE%
    echo   - Dependances: Verifiees
    echo.
    echo 🔧 ACTIONS RECOMMANDEES:
    echo 1. Relancez ce script
    echo 2. Executez SOLUTION_PYTHON_FINALE.bat
    echo 3. Installez manuellement: pip install PySide6
    echo 4. Verifiez les messages d erreur
)

echo.
echo 💡 CONSEIL: Pour un lancement rapide, utilisez:
echo    %PYTHON_CMD% gestimmob_final.py
echo.

REM Proposer de relancer si échec
if %LAUNCH_SUCCESS%==0 (
    set /p relancer="Relancer GestImmob maintenant ? (o/n): "
    if /i "%relancer%"=="o" (
        echo.
        echo Nouveau lancement...
        goto :launch_gestimmob
    )
)

echo Appuyez sur une touche pour fermer...
pause >nul
