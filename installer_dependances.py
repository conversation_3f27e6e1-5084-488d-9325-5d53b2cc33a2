#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour installer automatiquement toutes les dépendances et outils externes
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class InstalleurDependances:
    def __init__(self):
        self.systeme = platform.system().lower()
        self.dependances_installees = []
        self.dependances_echouees = []
        self.outils_installes = []
        self.outils_echoues = []
        
    def verifier_python(self):
        """Vérifie la version de Python"""
        print("🐍 VÉRIFICATION DE PYTHON")
        print("=" * 40)
        
        version = sys.version_info
        print(f"Version Python: {version.major}.{version.minor}.{version.micro}")
        
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ requis")
            return False
        else:
            print("✅ Version Python compatible")
            return True
    
    def verifier_pip(self):
        """Vérifie et met à jour pip"""
        print(f"\n📦 VÉRIFICATION DE PIP")
        print("-" * 30)
        
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ pip disponible: {result.stdout.strip()}")
                
                # Mettre à jour pip
                print("🔄 Mise à jour de pip...")
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ pip mis à jour")
                else:
                    print("⚠️ Échec mise à jour pip (non critique)")
                
                return True
            else:
                print("❌ pip non disponible")
                return False
        except Exception as e:
            print(f"❌ Erreur vérification pip: {e}")
            return False
    
    def installer_dependances_python(self):
        """Installe toutes les dépendances Python"""
        print(f"\n📦 INSTALLATION DES DÉPENDANCES PYTHON")
        print("=" * 50)
        
        # Dépendances critiques
        dependances_critiques = [
            'PySide6>=6.5.0',
            'SQLAlchemy>=2.0.0',
            'cryptography>=41.0.0'
        ]
        
        # Dépendances optionnelles
        dependances_optionnelles = [
            'pandas>=2.0.0',
            'numpy>=1.24.0',
            'openpyxl>=3.1.0',
            'reportlab>=4.0.0',
            'matplotlib>=3.7.0',
            'qrcode>=7.4.0',
            'python-barcode>=0.14.0',
            'Pillow>=10.0.0',
            'python-dateutil>=2.8.0',
            'requests>=2.31.0'
        ]
        
        print("🔧 Installation des dépendances critiques...")
        for dependance in dependances_critiques:
            if self.installer_package_python(dependance, critique=True):
                self.dependances_installees.append(dependance)
            else:
                self.dependances_echouees.append(dependance)
        
        print(f"\n🔧 Installation des dépendances optionnelles...")
        for dependance in dependances_optionnelles:
            if self.installer_package_python(dependance, critique=False):
                self.dependances_installees.append(dependance)
            else:
                self.dependances_echouees.append(dependance)
        
        # Installer depuis requirements.txt si disponible
        if os.path.exists('requirements.txt'):
            print(f"\n📄 Installation depuis requirements.txt...")
            try:
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    print("✅ requirements.txt installé avec succès")
                else:
                    print(f"⚠️ Problème avec requirements.txt: {result.stderr}")
            except Exception as e:
                print(f"❌ Erreur requirements.txt: {e}")
    
    def installer_package_python(self, package: str, critique: bool = False) -> bool:
        """Installe un package Python individuel"""
        package_name = package.split('>=')[0].split('==')[0]
        
        try:
            # Vérifier si déjà installé
            result = subprocess.run([sys.executable, '-c', f'import {package_name.replace("-", "_")}'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ {package_name}: Déjà installé")
                return True
        except:
            pass
        
        try:
            print(f"  🔄 Installation de {package_name}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                                  capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"  ✅ {package_name}: Installé avec succès")
                return True
            else:
                print(f"  ❌ {package_name}: Échec installation")
                if critique:
                    print(f"     Erreur: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"  ⏱️ {package_name}: Timeout installation")
            return False
        except Exception as e:
            print(f"  ❌ {package_name}: Erreur {e}")
            return False
    
    def installer_outils_systeme(self):
        """Installe les outils système nécessaires"""
        print(f"\n🛠️ INSTALLATION DES OUTILS SYSTÈME")
        print("=" * 50)
        
        if self.systeme == 'windows':
            self.installer_outils_windows()
        elif self.systeme == 'linux':
            self.installer_outils_linux()
        elif self.systeme == 'darwin':  # macOS
            self.installer_outils_macos()
        else:
            print(f"⚠️ Système {self.systeme} non supporté pour l'installation automatique")
    
    def installer_outils_windows(self):
        """Installe les outils pour Windows"""
        print("🪟 Configuration pour Windows...")
        
        # Vérifier Git
        if self.verifier_outil('git'):
            print("✅ Git: Disponible")
            self.outils_installes.append('git')
        else:
            print("❌ Git: Non disponible")
            print("   💡 Installez Git depuis: https://git-scm.com/download/win")
            self.outils_echoues.append('git')
        
        # Vérifier SQLite
        if self.verifier_outil('sqlite3'):
            print("✅ SQLite: Disponible")
            self.outils_installes.append('sqlite3')
        else:
            print("✅ SQLite: Inclus avec Python")
            self.outils_installes.append('sqlite3')
    
    def installer_outils_linux(self):
        """Installe les outils pour Linux"""
        print("🐧 Configuration pour Linux...")
        
        outils_linux = {
            'git': 'git',
            'sqlite3': 'sqlite3',
            'python3-dev': 'python3-dev',
            'build-essential': 'build-essential'
        }
        
        for outil, package in outils_linux.items():
            if self.verifier_outil(outil):
                print(f"✅ {outil}: Disponible")
                self.outils_installes.append(outil)
            else:
                print(f"🔄 Installation de {outil}...")
                if self.installer_package_linux(package):
                    self.outils_installes.append(outil)
                else:
                    self.outils_echoues.append(outil)
    
    def installer_outils_macos(self):
        """Installe les outils pour macOS"""
        print("🍎 Configuration pour macOS...")
        
        # Vérifier Homebrew
        if self.verifier_outil('brew'):
            print("✅ Homebrew: Disponible")
            
            outils_macos = ['git', 'sqlite3']
            for outil in outils_macos:
                if self.verifier_outil(outil):
                    print(f"✅ {outil}: Disponible")
                    self.outils_installes.append(outil)
                else:
                    print(f"🔄 Installation de {outil}...")
                    if self.installer_package_homebrew(outil):
                        self.outils_installes.append(outil)
                    else:
                        self.outils_echoues.append(outil)
        else:
            print("❌ Homebrew: Non disponible")
            print("   💡 Installez Homebrew depuis: https://brew.sh")
    
    def verifier_outil(self, outil: str) -> bool:
        """Vérifie si un outil est disponible"""
        try:
            result = subprocess.run([outil, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False
    
    def installer_package_linux(self, package: str) -> bool:
        """Installe un package sur Linux"""
        try:
            result = subprocess.run(['sudo', 'apt', 'install', '-y', package], 
                                  capture_output=True, text=True, timeout=120)
            if result.returncode == 0:
                print(f"  ✅ {package}: Installé")
                return True
            else:
                print(f"  ❌ {package}: Échec installation")
                return False
        except Exception as e:
            print(f"  ❌ {package}: Erreur {e}")
            return False
    
    def installer_package_homebrew(self, package: str) -> bool:
        """Installe un package avec Homebrew"""
        try:
            result = subprocess.run(['brew', 'install', package], 
                                  capture_output=True, text=True, timeout=120)
            if result.returncode == 0:
                print(f"  ✅ {package}: Installé")
                return True
            else:
                print(f"  ❌ {package}: Échec installation")
                return False
        except Exception as e:
            print(f"  ❌ {package}: Erreur {e}")
            return False
    
    def configurer_environnement(self):
        """Configure l'environnement de développement"""
        print(f"\n⚙️ CONFIGURATION DE L'ENVIRONNEMENT")
        print("=" * 50)
        
        # Créer les répertoires nécessaires
        repertoires = ['data', 'data/backups', 'data/exports', 'logs']
        for repertoire in repertoires:
            Path(repertoire).mkdir(parents=True, exist_ok=True)
            print(f"✅ Répertoire créé: {repertoire}/")
        
        # Configurer les variables d'environnement
        if self.systeme == 'windows':
            print("💡 Variables d'environnement Windows:")
            print("   Ajoutez Python au PATH si nécessaire")
        else:
            print("💡 Variables d'environnement Unix:")
            print("   export PYTHONPATH=$PYTHONPATH:$(pwd)/src")
    
    def generer_rapport_installation(self):
        """Génère un rapport d'installation"""
        print(f"\n📊 RAPPORT D'INSTALLATION")
        print("=" * 50)
        
        print(f"✅ Dépendances Python installées: {len(self.dependances_installees)}")
        print(f"❌ Dépendances Python échouées: {len(self.dependances_echouees)}")
        print(f"✅ Outils système installés: {len(self.outils_installes)}")
        print(f"❌ Outils système échoués: {len(self.outils_echoues)}")
        
        if self.dependances_echouees:
            print(f"\n❌ DÉPENDANCES ÉCHOUÉES:")
            for dep in self.dependances_echouees:
                print(f"  - {dep}")
            print(f"\n💡 Installez manuellement avec: pip install {' '.join(self.dependances_echouees)}")
        
        if self.outils_echoues:
            print(f"\n❌ OUTILS ÉCHOUÉS:")
            for outil in self.outils_echoues:
                print(f"  - {outil}")
        
        # Tester l'installation
        print(f"\n🧪 TEST DE L'INSTALLATION:")
        try:
            import PySide6
            print("✅ PySide6: Importé avec succès")
        except ImportError:
            print("❌ PySide6: Échec import")
        
        try:
            import sqlalchemy
            print("✅ SQLAlchemy: Importé avec succès")
        except ImportError:
            print("❌ SQLAlchemy: Échec import")
        
        succes = len(self.dependances_echouees) == 0
        
        if succes:
            print(f"\n🎉 INSTALLATION TERMINÉE AVEC SUCCÈS !")
            print(f"✅ Toutes les dépendances critiques sont installées")
            print(f"✅ L'application peut être lancée")
        else:
            print(f"\n⚠️ INSTALLATION PARTIELLE")
            print(f"Certaines dépendances nécessitent une installation manuelle")
        
        return succes

def main():
    """Fonction principale d'installation"""
    print("📦 INSTALLATION AUTOMATIQUE DES DÉPENDANCES")
    print("=" * 80)
    print("Ce script installe automatiquement toutes les dépendances")
    print("et outils nécessaires pour GestImmob.")
    
    installeur = InstalleurDependances()
    
    # Vérifications préliminaires
    if not installeur.verifier_python():
        print("❌ Version Python incompatible")
        return False
    
    if not installeur.verifier_pip():
        print("❌ pip non disponible")
        return False
    
    # Installation
    installeur.installer_dependances_python()
    installeur.installer_outils_systeme()
    installeur.configurer_environnement()
    
    # Rapport final
    succes = installeur.generer_rapport_installation()
    
    if succes:
        print(f"\n🚀 PROCHAINES ÉTAPES:")
        print(f"1. Lancer l'application: python src/main.py")
        print(f"2. Ou utiliser le script complet: python ORGANISER_TOUT.py")
    
    return succes

if __name__ == "__main__":
    main()
