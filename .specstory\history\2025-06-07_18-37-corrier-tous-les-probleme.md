<!-- Generated by SpecStory -->

# corrier tous les probleme (2025-06-07 19:37:21)

_**User**_

corrier tous les probleme

---

_**Assistant**_

### [main.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/main.py)

Voici les corrections principales apportées :
- Suppression de la double définition de `modifier_immobilisation` dans la classe `ImmobilisationMainWindow`.
- Correction du nombre de colonnes du tableau principal pour inclure tous les champs (ID, Désignation, Valeur, Année, Localisation, Secteur, Observation).
- Correction de l'affichage et de la sauvegarde/chargement des biens pour gérer toutes les colonnes.
- Correction de la gestion des boutons et du menu contextuel.
- Correction de la gestion des utilisateurs (statut, affichage, etc.).
- Nettoyage de quelques imports et commentaires.

````python
# ...existing code...

class ImmobilisationMainWindow(QMainWindow):
    def __init__(self, db_connection, user_info):
        super().__init__()
        # ...existing code...

        # Correction : 7 colonnes pour tous les champs principaux
        self.table = QTableWidget(0, 7)
        self.table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur", "Année", "Localisation", "Secteur", "Observation"
        ])
        self.table.setEditTriggers(QAbstractItemView.EditTrigger.AllEditTriggers)
        layout.addWidget(self.table)
        # Correction : supprimer le double ajout du widget table
        # layout.addWidget(self.table)  # <-- supprimé

        # ...existing code...

    # ...existing code...

    def ajouter_bien(self):
        """Ajoute un bien dans le tableau (formulaire principal)"""
        if any(field is None or not field.text().strip() for field in [
            self.id_input, self.designation_input, self.valeur_input, self.annee_input, self.localisation_input, self.secteur_input
        ]):
            QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
            return
        try:
            float(self.valeur_input.text())
            int(self.annee_input.text())
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
            return
        row = self.table.rowCount()
        self.table.insertRow(row)
        self.table.setItem(row, 0, QTableWidgetItem(self.id_input.text()))
        self.table.setItem(row, 1, QTableWidgetItem(self.designation_input.text()))
        self.table.setItem(row, 2, QTableWidgetItem(self.valeur_input.text()))
        self.table.setItem(row, 3, QTableWidgetItem(self.annee_input.text()))
        self.table.setItem(row, 4, QTableWidgetItem(self.localisation_input.text()))
        self.table.setItem(row, 5, QTableWidgetItem(self.secteur_input.text()))
        self.table.setItem(row, 6, QTableWidgetItem(self.observation_input.toPlainText()))
        self.sauvegarder_biens()
        QMessageBox.information(self, "Ajout", "Bien ajouté et sauvegardé.")

    # ...existing code...

    # SUPPRIMER la DEUXIÈME définition de la méthode modifier_immobilisation (gardez-en UNE seule)
    # ...existing code...

    def modifier_immobilisation(self):
        """Modifie une immobilisation existante avec toutes les vérifications et la logique métier"""
        if any(field is None or not field.text().strip() for field in [
            self.id_input, self.designation_input, self.valeur_input, self.annee_input, self.localisation_input, self.secteur_input
        ]):
            QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
            return
        try:
            valeur = float(self.valeur_input.text())
            annee = int(self.annee_input.text())
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
            return
        id_bien = self.id_input.text()
        if not id_bien:
            QMessageBox.warning(self, "Erreur", "ID du bien requis")
            return

        try:
            bien = Immobilisation(
                id_bien,
                self.designation_input.text(),
                valeur,
                annee,
                self.localisation_input.text(),
                self.secteur_input.text(),
                self.observation_input.toPlainText() if self.observation_input else ""
            )

            if hasattr(self.db, "mettre_a_jour_immobilisation") and self.db.mettre_a_jour_immobilisation(bien):
                self.marquer_comme_modifie()
                self.maj_affichage()
                QMessageBox.information(self, "Succès", "Bien mis à jour avec succès")
            else:
                QMessageBox.warning(self, "Erreur", "Échec de la mise à jour")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification : {str(e)}")

    # ...existing code...

    def charger_biens(self):
        """Charge les données depuis le fichier CSV au démarrage (optimisé)"""
        try:
            if not os.path.exists("biens.csv"):
                return
            with open("biens.csv", "r", encoding="utf-8") as f:
                reader = csv.reader(f)
                rows = list(reader)
                self.table.setRowCount(0)
                for row_data in rows:
                    row = self.table.rowCount()
                    self.table.insertRow(row)
                    for col, value in enumerate(row_data):
                        if col < self.table.columnCount():
                            self.table.setItem(row, col, QTableWidgetItem(value))
        except Exception as e:
            logging.error(f"Erreur lors du chargement des biens : {e}")

    def sauvegarder_biens(self):
        """Sauvegarder toutes les données du tableau dans un fichier CSV (optimisé)"""
        try:
            with open("biens.csv", "w", newline='', encoding="utf-8") as f:
                writer = csv.writer(f)
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde : {e}")

    # ...existing code...

    def modifier_ligne_selectionnee(self):
        """Charge les données de la ligne sélectionnée dans le formulaire"""
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.information(self, "Info", "Veuillez sélectionner une ligne à modifier.")
            return
        row = selected[0].row()
        for col, widget in enumerate([
            self.id_input, self.designation_input, self.valeur_input, self.annee_input,
            self.localisation_input, self.secteur_input
        ]):
            item = self.table.item(row, col)
            if widget is not None and item is not None:
                widget.setText(item.text())
        # Correction : observation en colonne 6
        if self.table.columnCount() > 6:
            item_observation = self.table.item(row, 6)
            if item_observation is not None:
                self.observation_input.setPlainText(item_observation.text())

    def supprimer_ligne_selectionnee(self):
        """Supprime la ligne sélectionnée du tableau et de la base"""
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.information(self, "Info", "Veuillez sélectionner une ligne à supprimer.")
            return
        row = selected[0].row()
        item_id = self.table.item(row, 0)
        if item_id is None or not item_id.text().strip():
            QMessageBox.warning(self, "Erreur", "Impossible de trouver l'ID du bien à supprimer.")
            return
        id_bien = item_id.text()
        if hasattr(self.db, "supprimer_immobilisation"):
            self.db.supprimer_immobilisation(id_bien)
        self.table.removeRow(row)
        self.marquer_comme_modifie()
        self.sauvegarder_donnees()

    # ...existing code...

class UserManagementDialog(QDialog):
    def __init__(self, auth_manager, parent=None):
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.setWindowTitle("Gestion des utilisateurs")
        self.setFixedSize(500, 400)
        self.main_layout = QVBoxLayout()
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels(["ID", "Nom d'utilisateur", "Rôle", "Nom complet", "Statut"])
        self._load_users()
        self.btn_add = QPushButton("Ajouter")
        self.btn_edit = QPushButton("Modifier")
        self.btn_toggle = QPushButton("Activer/Désactiver")
        self.btn_add.clicked.connect(self._add_user)
        self.btn_edit.clicked.connect(self._edit_user)
        self.btn_toggle.clicked.connect(self._toggle_user)
        btn_layout = QHBoxLayout()
        btn_layout.addWidget(self.btn_add)
        btn_layout.addWidget(self.btn_edit)
        btn_layout.addWidget(self.btn_toggle)
        self.main_layout.addWidget(self.users_table)
        self.main_layout.addLayout(btn_layout)
        self.setLayout(self.main_layout)

    def _toggle_user(self):
        """Active ou désactive l'utilisateur sélectionné"""
        selected = self.users_table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un utilisateur à activer/désactiver")
            return
        row = selected[0].row()
        item_id = self.users_table.item(row, 0)
        item_statut = self.users_table.item(row, 4)
        if item_id is None or item_statut is None:
            QMessageBox.warning(self, "Erreur", "Impossible de lire les informations de l'utilisateur sélectionné.")
            return
        user_id = item_id.text()
        statut = item_statut.text()
        new_status = 0 if statut == "Actif" else 1
        try:
            cursor = self.auth_manager.conn.cursor()
            cursor.execute(
                "UPDATE users SET is_active=? WHERE id=?",
                (new_status, user_id)
            )
            self.auth_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Statut de l'utilisateur mis à jour")
            self._load_users()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def _load_users(self):
        """Charge la liste des utilisateurs avec indexation pour la performance"""
        cursor = self.auth_manager.conn.cursor()
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)")
        users = cursor.execute('''
            SELECT id, username, role, full_name, is_active FROM users
        ''').fetchall()
        self.users_table.setRowCount(len(users))
        for row, user in enumerate(users):
            for col, data in enumerate(user):
                item = QTableWidgetItem(str(data))
                if col == 4:
                    item.setText("Actif" if int(data) else "Inactif")
                self.users_table.setItem(row, col, item)

    # ...existing code...

# ...existing code...
````

---

_**User**_

/fix La déclaration de méthode « modifier_immobilisation » est masquée par une déclaration du même nom

---

_**Assistant**_

### [main.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/main.py)

Supprimez la duplication de la méthode `modifier_immobilisation` dans la classe `ImmobilisationMainWindow`. Gardez UNE SEULE définition (la plus complète ou la dernière). Voici comment corriger :

````python
# ...existing code...

class ImmobilisationMainWindow(QMainWindow):
    #

---

