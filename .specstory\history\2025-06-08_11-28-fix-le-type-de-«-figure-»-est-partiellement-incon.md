<!-- Generated by SpecStory -->

# /fix Le type de « figure » est partiellement incon (2025-06-08 12:28:45)

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l'avertissement Pylance sur le type de retour de `plt.figure`, il suffit de ne pas assigner ou utiliser la valeur de retour de `plt.figure`, car elle n'est pas utilisée.  
Aucune annotation de type n'est nécessaire ici.

````python
# ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))  # Pas besoin d'assigner le retour, ni de typer
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))  # Idem ici
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

Aucune modification de logique, il suffit de ne pas se soucier du type de retour de `plt.figure` si vous ne l'utilisez pas.  
L'avertissement Pylance peut être ignoré dans ce contexte.

---

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour supprimer l'avertissement Pylance sur le type de `plt.figure`, vous pouvez ajouter une annotation de type explicite pour ignorer le type de retour, ou utiliser un commentaire `# type: ignore` sur la ligne concernée.

Voici la solution la plus simple : ajoutez `# type: ignore` à la ligne problématique.

````python
# ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))  # type: ignore
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))  # type: ignore
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

Cela supprimera l'avertissement sans modifier la logique du code.

---

