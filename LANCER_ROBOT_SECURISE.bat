@echo off
title Robot IA SamNord - Terminal Securise
color 0A

echo.
echo ================================================
echo    ROBOT IA SAMNORD - TERMINAL SECURISE
echo    Ne fait pas crasher VS Code
echo    SamNord@110577
echo ================================================
echo.

echo Lancement Robot IA externe securise...
echo.

REM Lancement en terminal séparé sécurisé
start "Robot IA SamNord" cmd /k "color 0A && title Robot IA SamNord - SamNord@110577 && python ROBOT_IA_EXTERNE_STABLE.py"

echo Robot IA lance en terminal externe securise !
echo VS Code ne crashera pas.
echo.

echo Instructions:
echo 1. Robot IA ouvert en terminal separe
echo 2. VS Code reste stable
echo 3. Tous les modules disponibles
echo 4. Interface chat integree
echo.

timeout /t 3 /nobreak >nul
exit /b 0
