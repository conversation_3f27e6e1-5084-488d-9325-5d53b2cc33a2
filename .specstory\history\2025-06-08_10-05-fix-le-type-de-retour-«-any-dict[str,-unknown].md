<!-- Generated by SpecStory -->

# /fix Le type de retour « Any | dict[str, Unknown]  (2025-06-08 11:05:33)

_**User**_

/fix Le type de retour « Any | dict[str, Unknown] » est partiellement inconnu

---

_**Assistant**_

### [config.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/config.py)

Ajouter des annotations de type explicites pour la fonction `load_config` afin de corriger l'avertissement sur le type de retour partiellement inconnu.

````python
import os
import json
from typing import Any, Dict

CONFIG_FILE = "config.json"

def load_config() -> dict[str, Any]:
    # ...existing code...
````

---

