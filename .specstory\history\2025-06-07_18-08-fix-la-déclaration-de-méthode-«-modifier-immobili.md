<!-- Generated by SpecStory -->

# /fix La déclaration de méthode « modifier_immobili (2025-06-07 19:08:54)

_**User**_

/fix La déclaration de méthode « modifier_immobilisation » est masquée par une déclaration du même nom

---

_**Assistant**_

### [main.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/main.py)

Il y a deux définitions de la méthode `modifier_immobilisation` dans la classe `ImmobilisationMainWindow`, ce qui masque la première par la seconde. Il faut conserver une seule définition (la plus complète ou la plus récente) et supprimer l'autre.

Voici le correctif : supprimez la première occurrence de la méthode `modifier_immobilisation` (gardez la seconde).

````python
# ...existing code...

    # Supprimer cette première définition redondante
    # def modifier_immobilisation(self):
    #     """Modifie une immobilisation existante avec toutes les vérifications et la logique métier"""
    #     if any(field is None or not field.text().strip() for field in [
    #         self.id_input, self.designation_input, self.valeur_input, self.annee_input, self.localisation_input, self.secteur_input
    #     ]):
    #         QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
    #         return
    #     try:
    #         valeur = float(self.valeur_input.text())
    #         annee = int(self.annee_input.text())
    #     except ValueError:
    #         QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
    #         return
    #     id_bien = self.id_input.text()
    #     if not id_bien:
    #         QMessageBox.warning(self, "Erreur", "ID du bien requis")
    #         return
    #
    #     try:
    #         bien = Immobilisation(
    #             id_bien,
    #             self.designation_input.text(),
    #             valeur,
    #             annee,
    #             self.localisation_input.text(),
    #             self.secteur_input.text(),
    #             self.observation_input.toPlainText() if self.observation_input else ""
    #         )
    #
    #         if hasattr(self.db, "mettre_a_jour_immobilisation") and self.db.mettre_a_jour_immobilisation(bien):
    #             self.marquer_comme_modifie()
    #             self.maj_affichage()
    #             QMessageBox.information(self, "Succès", "Bien mis à jour avec succès")
    #         else:
    #             QMessageBox.warning(self, "Erreur", "Échec de la mise à jour")
    #     except Exception as e:
    #         QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification : {str(e)}")

# ...existing code...

    def modifier_immobilisation(self):
        """Modifie une immobilisation existante avec toutes les vérifications et la logique métier"""
        if any(field is None or not field.text().strip() for field in [
            self.id_input, self.designation_input, self.valeur_input, self.annee_input, self.localisation_input, self.secteur_input
        ]):
            QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
            return
        try:
            valeur = float(self.valeur_input.text())
            annee = int(self.annee_input.text())
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
            return
        id_bien = self.id_input.text()
        if not id_bien:
            QMessageBox.warning(self, "Erreur", "ID du bien requis")
            return

        try:
            bien = Immobilisation(
                id_bien,
                self.designation_input.text(),
                valeur,
                annee,
                self.localisation_input.text(),
                self.secteur_input.text(),
                self.observation_input.toPlainText() if self.observation_input else ""
            )

            if hasattr(self.db, "mettre_a_jour_immobilisation") and self.db.mettre_a_jour_immobilisation(bien):
                self.marquer_comme_modifie()
                self.maj_affichage()
                QMessageBox.information(self, "Succès", "Bien mis à jour avec succès")
            else:
                QMessageBox.warning(self, "Erreur", "Échec de la mise à jour")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification : {str(e)}")

# ...existing code...
````

---

