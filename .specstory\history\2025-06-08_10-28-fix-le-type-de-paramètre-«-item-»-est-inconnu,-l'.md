<!-- Generated by SpecStory -->

# /fix Le type de paramètre « item » est inconnu, L' (2025-06-08 11:28:19)

_**User**_

/fix Le type de paramètre « item » est inconnu, L'annotation de type est manquante pour le paramètre "item"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Ajouter une annotation de type explicite pour le paramètre `item` dans la fonction `safe_text`. Ici, le type attendu est probablement `QTableWidgetItem | None`.

````python
# ...existing code...
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QF<PERSON><PERSON>ialog, QInputDialog
)
# ...existing code...

    def modifier_inventaire(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier article inventaire")
        form = QFormLayout()
        from typing import Optional
        def safe_text(item: Optional[QTableWidgetItem]) -> str:
            if item is None:
                return ""
            text_attr = getattr(item, "text", None)
            if callable(text_attr):
                try:
                    v = text_attr()
                    if v is not None:
                        return str(v)
                except Exception:
                    return ""
            return ""
        # ...existing code...
````

---

