<!-- Generated by SpecStory -->

# /fix Le type de « figure » est partiellement incon (2025-06-08 12:27:00)

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Pour corriger l'avertissement sur le type de retour de `plt.figure`, il suffit d'ignorer la valeur de retour explicitement avec `_ = plt.figure(...)` ou d'ajouter un commentaire `# type: ignore` si vous ne souhaitez pas utiliser la variable. Cela clarifie l'intention et supprime l'avertissement de type.

Voici la modification à appliquer :

````python
# ...existing code...
            if stats_categorie:
                _ = plt.figure(figsize=(8, 4))  # On ignore explicitement le retour pour éviter l'avertissement de type
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                _ = plt.figure(figsize=(8, 4))  # Idem ici
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

---

