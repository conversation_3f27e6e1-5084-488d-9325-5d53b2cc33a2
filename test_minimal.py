#!/usr/bin/env python3
"""
Test minimal pour GestImmob - sans interface graphique
"""

import sys
import os

def test_basic():
    """Test de base"""
    print("=== Test Minimal GestImmob ===")
    print(f"Python version: {sys.version}")
    
    # Test SQLite
    try:
        import sqlite3
        print("[OK] SQLite3 disponible")
        
        # Test base de données
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        cursor.execute('CREATE TABLE test (id INTEGER, name TEXT)')
        cursor.execute('INSERT INTO test VALUES (1, "test")')
        result = cursor.fetchone()
        conn.close()
        print("[OK] Base de données fonctionne")
        
    except Exception as e:
        print(f"[ERREUR] Erreur SQLite: {e}")
        return False
    
    # Test modules standards
    try:
        import json
        import csv
        import datetime
        import logging
        print("[OK] Modules standards OK")
    except Exception as e:
        print(f"[ERREUR] Erreur modules: {e}")
        return False
    
    # Test PySide6 (sans créer d'interface)
    try:
        import PySide6
        print("[OK] PySide6 disponible")
    except ImportError:
        print("⚠ PySide6 non disponible")
    
    # Test psutil
    try:
        import psutil
        memory = psutil.virtual_memory()
        ram_gb = round(memory.total / (1024**3), 1)
        print(f"[OK] RAM détectée: {ram_gb} GB")
    except ImportError:
        print("⚠ psutil non disponible")
    
    # Test configuration
    try:
        from pathlib import Path
        config_file = Path("config_advanced.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("[OK] Configuration chargée")
        else:
            print("⚠ Fichier config manquant")
    except Exception as e:
        print(f"⚠ Erreur config: {e}")
    
    print("\n[SUCCES] Tests de base réussis !")
    return True

if __name__ == "__main__":
    test_basic()
