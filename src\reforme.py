# -*- coding: utf-8 -*-
"""
Gestion des Réformes - Module de gestion des réformes d'actifs
Encodage: UTF-8
Auteur: Système de Gestion Immobilière
Version: 1.0.0
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QLabel, QComboBox, QFileDialog, QInputDialog
)
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QColor
import logging
import os
import json
import csv
from datetime import datetime

class ReformeWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Gestion des Réformes")
        self.resize(1800, 950)
        layout = QVBoxLayout(self)
        # Table très détaillée pour la réforme
        self.table = QTableWidget(0, 22)
        self.table.setHorizontalHeaderLabels([
            "ID Bien", "Désignation", "Date Réforme", "Motif détaillé de la réforme (technique, économique, juridique...)", 
            "Commission (noms, fonctions, signatures, présence)", "Président de la commission", "Membres de la commission",
            "Fonction Membres", "Décision/Avis détaillé (PV, rapport, délibération...)", "PV N°", "Date PV", "Signataires PV",
            "Observation complémentaire (procès-verbal, pièces jointes...)", "Historique complet (toutes actions, mouvements, réformes précédentes...)", 
            "État Article (usure, panne, état physique, localisation...)", "Sort (Disparition, Vol, Sabotage, Détérioration, Non localisé, Autre)", 
            "Amortissement (Amorti/Non amorti, calcul, valeur nette comptable...)", "Durée Amort. (ans)", 
            "Montant réforme", "Montant comptable", "Pourcentage réforme/compta", "Date d'enregistrement comptable"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Nouvelle Réforme")
        self.edit_btn = QPushButton("Modifier")
        self.delete_btn = QPushButton("Supprimer")
        self.sync_btn = QPushButton("Synchroniser")
        self.chart_btn = QPushButton("Voir Statistiques Réformes")
        self.scan_doc_btn = QPushButton("Scanner/Joindre Document")
        self.doc_module_btn = QPushButton("Gestion des Documents")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.sync_btn)
        btn_layout.addWidget(self.chart_btn)
        btn_layout.addWidget(self.scan_doc_btn)
        btn_layout.addWidget(self.doc_module_btn)
        layout.addLayout(btn_layout)

        self.add_btn.clicked.connect(self.ajouter_reforme)
        self.edit_btn.clicked.connect(self.modifier_reforme)
        self.delete_btn.clicked.connect(self.supprimer_reforme)
        self.sync_btn.clicked.connect(self.synchroniser)
        self.chart_btn.clicked.connect(self.afficher_charts)
        self.scan_doc_btn.clicked.connect(self.scanner_document)
        self.doc_module_btn.clicked.connect(self.ouvrir_gestion_documents)
        self.table.itemChanged.connect(self.alerte_modification)

        self.changements_detectes = False
        self.timer_sync = QTimer()
        self.timer_sync.timeout.connect(self.sauvegarde_auto)
        self.timer_sync.start(180000)  # 3 min

    def ajouter_reforme(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Nouvelle réforme")
        form = QFormLayout()
        id_bien = QLineEdit()
        designation = QLineEdit()
        date_reforme = QLineEdit()
        motif = QLineEdit()
        commission = QLineEdit()
        president = QLineEdit()
        membres = QLineEdit()
        fonction_membres = QLineEdit()
        decision = QLineEdit()
        pv_num = QLineEdit()
        date_pv = QLineEdit()
        signataires = QLineEdit()
        observation = QLineEdit()
        historique = QLineEdit()
        etat_article = QComboBox(); etat_article.addItems([
            "Bon", "Moyen", "Mauvais", "HS", "Réparable", "Non localisé"
        ])
        sort_article = QComboBox(); sort_article.addItems([
            "Disparition", "Vol", "Sabotage", "Détérioration", "Non localisé", "Autre"
        ])
        amortissement = QComboBox(); amortissement.addItems([
            "Amorti", "Non amorti"
        ])
        duree_amort = QLineEdit()
        montant_reforme = QLineEdit()
        montant_comptable = QLineEdit()
        pourcentage = QLineEdit()
        date_enregistrement = QLineEdit()
        form.addRow("ID Bien:", id_bien)
        form.addRow("Désignation:", designation)
        form.addRow("Date Réforme:", date_reforme)
        form.addRow("Motif détaillé de la réforme (technique, économique, juridique...):", motif)
        form.addRow("Commission (noms, fonctions, signatures, présence):", commission)
        form.addRow("Président de la commission:", president)
        form.addRow("Membres de la commission:", membres)
        form.addRow("Fonction Membres:", fonction_membres)
        form.addRow("Décision/Avis détaillé (PV, rapport, délibération...):", decision)
        form.addRow("PV N°:", pv_num)
        form.addRow("Date PV:", date_pv)
        form.addRow("Signataires PV:", signataires)
        form.addRow("Observation complémentaire (procès-verbal, pièces jointes...):", observation)
        form.addRow("Historique complet (toutes actions, mouvements, réformes précédentes...):", historique)
        form.addRow("État de l'article (usure, panne, état physique, localisation...):", etat_article)
        form.addRow("Sort (Disparition, Vol, Sabotage, Détérioration, Non localisé, Autre):", sort_article)
        form.addRow("Amortissement (Amorti/Non amorti, calcul, valeur nette comptable...):", amortissement)
        form.addRow("Durée d'amortissement (ans):", duree_amort)
        form.addRow("Montant réforme:", montant_reforme)
        form.addRow("Montant comptable:", montant_comptable)
        form.addRow("Pourcentage réforme/compta:", pourcentage)
        form.addRow("Date d'enregistrement comptable:", date_enregistrement)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(id_bien.text()))
            self.table.setItem(row, 1, QTableWidgetItem(designation.text()))
            self.table.setItem(row, 2, QTableWidgetItem(date_reforme.text()))
            self.table.setItem(row, 3, QTableWidgetItem(motif.text()))
            self.table.setItem(row, 4, QTableWidgetItem(commission.text()))
            self.table.setItem(row, 5, QTableWidgetItem(president.text()))
            self.table.setItem(row, 6, QTableWidgetItem(membres.text()))
            self.table.setItem(row, 7, QTableWidgetItem(fonction_membres.text()))
            self.table.setItem(row, 8, QTableWidgetItem(decision.text()))
            self.table.setItem(row, 9, QTableWidgetItem(pv_num.text()))
            self.table.setItem(row, 10, QTableWidgetItem(date_pv.text()))
            self.table.setItem(row, 11, QTableWidgetItem(signataires.text()))
            self.table.setItem(row, 12, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 13, QTableWidgetItem(historique.text()))
            self.table.setItem(row, 14, QTableWidgetItem(etat_article.currentText()))
            self.table.setItem(row, 15, QTableWidgetItem(sort_article.currentText()))
            self.table.setItem(row, 16, QTableWidgetItem(amortissement.currentText()))
            self.table.setItem(row, 17, QTableWidgetItem(duree_amort.text()))
            self.table.setItem(row, 18, QTableWidgetItem(montant_reforme.text()))
            self.table.setItem(row, 19, QTableWidgetItem(montant_comptable.text()))
            self.table.setItem(row, 20, QTableWidgetItem(pourcentage.text()))
            self.table.setItem(row, 21, QTableWidgetItem(date_enregistrement.text()))
            self.changements_detectes = True

    def modifier_reforme(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier réforme")
        form = QFormLayout()
        def safe_text(item):
            if item is not None:
                text_attr = getattr(item, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            return str(v)
                    except Exception:
                        return ""
            return ""
        item_id_bien = self.table.item(row, 0)
        id_bien = QLineEdit(safe_text(item_id_bien))
        designation = QLineEdit(safe_text(self.table.item(row, 1)))
        date_reforme = QLineEdit(safe_text(self.table.item(row, 2)))
        motif = QLineEdit(safe_text(self.table.item(row, 3)))
        commission = QLineEdit(safe_text(self.table.item(row, 4)))
        president = QLineEdit(safe_text(self.table.item(row, 5)))
        membres = QLineEdit(safe_text(self.table.item(row, 6)))
        fonction_membres = QLineEdit(safe_text(self.table.item(row, 7)))
        decision = QLineEdit(safe_text(self.table.item(row, 8)))
        pv_num = QLineEdit(safe_text(self.table.item(row, 9)))
        date_pv = QLineEdit(safe_text(self.table.item(row, 10)))
        signataires = QLineEdit(safe_text(self.table.item(row, 11)))
        observation = QLineEdit(safe_text(self.table.item(row, 12)))
        historique = QLineEdit(safe_text(self.table.item(row, 13)))
        etat_article = QComboBox(); etat_article.addItems([
            "Bon", "Moyen", "Mauvais", "HS", "Réparable", "Non localisé"
        ])
        etat_article.setCurrentText(safe_text(self.table.item(row, 14)))
        sort_article = QComboBox(); sort_article.addItems([
            "Disparition", "Vol", "Sabotage", "Détérioration", "Non localisé", "Autre"
        ])
        sort_article.setCurrentText(safe_text(self.table.item(row, 15)))
        amortissement = QComboBox(); amortissement.addItems([
            "Amorti", "Non amorti"
        ])
        amortissement.setCurrentText(safe_text(self.table.item(row, 16)))
        duree_amort = QLineEdit(safe_text(self.table.item(row, 17)))
        montant_reforme = QLineEdit(safe_text(self.table.item(row, 18)))
        montant_comptable = QLineEdit(safe_text(self.table.item(row, 19)))
        pourcentage = QLineEdit(safe_text(self.table.item(row, 20)))
        date_enregistrement = QLineEdit(safe_text(self.table.item(row, 21)))
        form.addRow("ID Bien:", id_bien)
        form.addRow("Désignation:", designation)
        form.addRow("Date Réforme:", date_reforme)
        form.addRow("Motif détaillé de la réforme (technique, économique, juridique...):", motif)
        form.addRow("Commission (noms, fonctions, signatures, présence):", commission)
        form.addRow("Président de la commission:", president)
        form.addRow("Membres de la commission:", membres)
        form.addRow("Fonction Membres:", fonction_membres)
        form.addRow("Décision/Avis détaillé (PV, rapport, délibération...):", decision)
        form.addRow("PV N°:", pv_num)
        form.addRow("Date PV:", date_pv)
        form.addRow("Signataires PV:", signataires)
        form.addRow("Observation complémentaire (procès-verbal, pièces jointes...):", observation)
        form.addRow("Historique complet (toutes actions, mouvements, réformes précédentes...):", historique)
        form.addRow("État de l'article (usure, panne, état physique, localisation...):", etat_article)
        form.addRow("Sort (Disparition, Vol, Sabotage, Détérioration, Non localisé, Autre):", sort_article)
        form.addRow("Amortissement (Amorti/Non amorti, calcul, valeur nette comptable...):", amortissement)
        form.addRow("Durée d'amortissement (ans):", duree_amort)
        form.addRow("Montant réforme:", montant_reforme)
        form.addRow("Montant comptable:", montant_comptable)
        form.addRow("Pourcentage réforme/compta:", pourcentage)
        form.addRow("Date d'enregistrement comptable:", date_enregistrement)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            self.table.setItem(row, 0, QTableWidgetItem(id_bien.text()))
            self.table.setItem(row, 1, QTableWidgetItem(designation.text()))
            self.table.setItem(row, 2, QTableWidgetItem(date_reforme.text()))
            self.table.setItem(row, 3, QTableWidgetItem(motif.text()))
            self.table.setItem(row, 4, QTableWidgetItem(commission.text()))
            self.table.setItem(row, 5, QTableWidgetItem(president.text()))
            self.table.setItem(row, 6, QTableWidgetItem(membres.text()))
            self.table.setItem(row, 7, QTableWidgetItem(fonction_membres.text()))
            self.table.setItem(row, 8, QTableWidgetItem(decision.text()))
            self.table.setItem(row, 9, QTableWidgetItem(pv_num.text()))
            self.table.setItem(row, 10, QTableWidgetItem(date_pv.text()))
            self.table.setItem(row, 11, QTableWidgetItem(signataires.text()))
            self.table.setItem(row, 12, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 13, QTableWidgetItem(historique.text()))
            self.table.setItem(row, 14, QTableWidgetItem(etat_article.currentText()))
            self.table.setItem(row, 15, QTableWidgetItem(sort_article.currentText()))
            self.table.setItem(row, 16, QTableWidgetItem(amortissement.currentText()))
            self.table.setItem(row, 17, QTableWidgetItem(duree_amort.text()))
            self.table.setItem(row, 18, QTableWidgetItem(montant_reforme.text()))
            self.table.setItem(row, 19, QTableWidgetItem(montant_comptable.text()))
            self.table.setItem(row, 20, QTableWidgetItem(pourcentage.text()))
            self.table.setItem(row, 21, QTableWidgetItem(date_enregistrement.text()))
            self.changements_detectes = True

    def supprimer_reforme(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à supprimer.")
            return
        row = selected[0].row()
        self.table.removeRow(row)
        self.changements_detectes = True

    def alerte_modification(self, item):
        # Détection d'anomalie simple : date PV vide ou décision vide
        if item.column() == 10 and not item.text():
            item.setBackground(QColor("red"))
            QMessageBox.warning(self, "Alerte", "Date PV manquante.")
        elif item.column() == 8 and not item.text():
            item.setBackground(QColor("red"))
            QMessageBox.warning(self, "Alerte", "Décision/Avis manquant.")
        elif item.column() == 14 and item.text() == "Non localisé":
            item.setBackground(QColor("red"))
            QMessageBox.warning(self, "Alerte", "Article non localisé.")
        elif item.column() == 15 and item.text() in ["Disparition", "Vol", "Sabotage"]:
            item.setBackground(QColor("red"))
            QMessageBox.warning(self, "Alerte", f"Sort critique détecté : {item.text()}")
        elif item.column() == 16 and item.text() == "Non amorti":
            item.setBackground(QColor("yellow"))
            QMessageBox.information(self, "Info", "Article non encore amorti.")
        else:
            item.setBackground(QColor("white"))
        self.changements_detectes = True

    def synchroniser(self):
        # ...logique de synchronisation multi-base ou API...
        QMessageBox.information(self, "Synchronisation", "Synchronisation des réformes effectuée avec succès.")
        self.changements_detectes = False

    def sauvegarde_auto(self):
        """Sauvegarde automatique des données avec encodage UTF-8"""
        if self.changements_detectes:
            try:
                # Sauvegarde en JSON avec encodage UTF-8
                data = []
                for row in range(self.table.rowCount()):
                    row_data = {}
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        header = self.table.horizontalHeaderItem(col)
                        if header and item:
                            row_data[header.text()] = item.text()
                    data.append(row_data)

                filename = f"reforme_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                logging.info(f"Sauvegarde automatique des réformes: {filename}")
                self.changements_detectes = False
            except Exception as e:
                logging.error(f"Erreur lors de la sauvegarde: {e}")

    def exporter_csv(self):
        """Exporte les données en CSV avec encodage UTF-8"""
        try:
            filename = f"reforme_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # En-têtes
                headers = []
                for col in range(self.table.columnCount()):
                    header = self.table.horizontalHeaderItem(col)
                    if header:
                        headers.append(header.text())
                writer.writerow(headers)

                # Données
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

            QMessageBox.information(self, "Export", f"Données exportées vers: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {e}")

    def closeEvent(self, event):
        if self.changements_detectes:
            self.sauvegarde_auto()
        event.accept()

    def afficher_charts(self):
        # Statistiques réforme par mois/année et pourcentage par rapport à la comptabilité
        from collections import defaultdict
        import matplotlib.pyplot as plt
        import datetime
        stats_mois = defaultdict(int)
        stats_annee = defaultdict(int)
        total_reforme = 0
        total_compta = 0
        for row in range(self.table.rowCount()):
            date_str = self.table.item(row, 2).text() if self.table.item(row, 2) else ""
            montant_reforme = float(self.table.item(row, 18).text() or 0)
            montant_compta = float(self.table.item(row, 19).text() or 0)
            total_reforme += montant_reforme
            total_compta += montant_compta
            try:
                date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d")
                mois = date_obj.strftime("%Y-%m")
                annee = date_obj.strftime("%Y")
                stats_mois[mois] += 1
                stats_annee[annee] += 1
            except Exception:
                continue
        # Affichage graphique
        if stats_mois:
            plt.figure(figsize=(10, 4))
            plt.bar(stats_mois.keys(), stats_mois.values())
            plt.title("Réformes par mois")
            plt.xlabel("Mois")
            plt.ylabel("Nombre de réformes")
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.show()
        if stats_annee:
            plt.figure(figsize=(6, 4))
            plt.bar(stats_annee.keys(), stats_annee.values())
            plt.title("Réformes par année")
            plt.xlabel("Année")
            plt.ylabel("Nombre de réformes")
            plt.tight_layout()
            plt.show()
        if total_compta > 0:
            pourcentage = (total_reforme / total_compta) * 100
            QMessageBox.information(self, "Pourcentage réforme/compta", f"Total réforme: {total_reforme}\nTotal comptable: {total_compta}\nPourcentage: {pourcentage:.2f}%")
        else:
            QMessageBox.information(self, "Pourcentage réforme/compta", "Aucune donnée comptable pour calculer le pourcentage.")

    def scanner_document(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Document", "Sélectionnez une ligne pour joindre un document.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Joindre un document à la réforme")
        form = QFormLayout()  # Correction : pas de parent ici
        doc_date = QLineEdit()
        doc_objet = QLineEdit()
        doc_reference = QLineEdit()
        doc_type = QComboBox(); doc_type.addItems(["PV", "Rapport", "Facture", "Photo", "Courrier", "Autre"])
        doc_file = QLineEdit()
        btn_file = QPushButton("Choisir fichier")
        def choisir_fichier():
            file, _ = QFileDialog.getOpenFileName(dialog, "Sélectionner un document", "", "Tous les fichiers (*)")
            if file:
                doc_file.setText(file)
        btn_file.clicked.connect(choisir_fichier)
        form.addRow("Date du document:", doc_date)
        form.addRow("Objet:", doc_objet)
        form.addRow("Référence:", doc_reference)
        form.addRow("Type de document:", doc_type)
        form.addRow("Fichier:", doc_file)
        form.addRow("", btn_file)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == QDialog.Accepted:
            # Enregistrement du chemin du document dans l'observation ou historique
            doc_info = f"Doc [{doc_type.currentText()}] {doc_date.text()} | {doc_objet.text()} | {doc_reference.text()} | {os.path.basename(doc_file.text())}"
            old_obs = self.table.item(row, 12).text() if self.table.item(row, 12) else ""
            new_obs = old_obs + "\n" + doc_info if old_obs else doc_info
            self.table.setItem(row, 12, QTableWidgetItem(new_obs))
            # Synchronisation du fichier dans la base ou le dossier associé (à implémenter selon votre backend)
            QMessageBox.information(self, "Document", "Document joint et synchronisé (à compléter selon la base de données).")
            self.changements_detectes = True

    def ouvrir_gestion_documents(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Gestion des Documents liés à la réforme")
        layout = QVBoxLayout(dialog)
        table = QTableWidget(0, 8)
        table.setHorizontalHeaderLabels([
            "Type doc", "Date doc", "Objet", "Référence", "Fichier", "Code-barres", "Synchronisé", "Observation"
        ])
        layout.addWidget(table)
        btn_layout = QHBoxLayout()
        add_btn = QPushButton("Ajouter Document")
        scan_btn = QPushButton("Scanner Code-barres")
        sync_btn = QPushButton("Synchroniser Documents")
        btn_layout.addWidget(add_btn)
        btn_layout.addWidget(scan_btn)
        btn_layout.addWidget(sync_btn)
        layout.addLayout(btn_layout)

        def ajouter_document():
            doc_dialog = QDialog(dialog)
            doc_dialog.setWindowTitle("Ajouter un document")
            form = QFormLayout(doc_dialog)
            doc_type = QComboBox(); doc_type.addItems(["PV", "Rapport", "Facture", "Photo", "Courrier", "Autre"])
            doc_date = QLineEdit()
            doc_objet = QLineEdit()
            doc_ref = QLineEdit()
            doc_file = QLineEdit()
            btn_file = QPushButton("Choisir fichier")
            doc_codebarre = QLineEdit()
            doc_obs = QLineEdit()
            def choisir_fichier():
                file, _ = QFileDialog.getOpenFileName(doc_dialog, "Sélectionner un document", "", "Tous les fichiers (*)")
                if file:
                    doc_file.setText(file)
            btn_file.clicked.connect(choisir_fichier)
            form.addRow("Type de document:", doc_type)
            form.addRow("Date du document:", doc_date)
            form.addRow("Objet:", doc_objet)
            form.addRow("Référence:", doc_ref)
            form.addRow("Fichier:", doc_file)
            form.addRow("", btn_file)
            form.addRow("Code-barres:", doc_codebarre)
            form.addRow("Observation:", doc_obs)
            btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            btn_box.accepted.connect(doc_dialog.accept)
            btn_box.rejected.connect(doc_dialog.reject)
            form.addWidget(btn_box)
            if doc_dialog.exec() == QDialog.Accepted:
                row = table.rowCount()
                table.insertRow(row)
                table.setItem(row, 0, QTableWidgetItem(doc_type.currentText()))
                table.setItem(row, 1, QTableWidgetItem(doc_date.text()))
                table.setItem(row, 2, QTableWidgetItem(doc_objet.text()))
                table.setItem(row, 3, QTableWidgetItem(doc_ref.text()))
                table.setItem(row, 4, QTableWidgetItem(os.path.basename(doc_file.text())))
                table.setItem(row, 5, QTableWidgetItem(doc_codebarre.text()))
                table.setItem(row, 6, QTableWidgetItem("Oui"))  # Simuler synchronisation
                table.setItem(row, 7, QTableWidgetItem(doc_obs.text()))
                QMessageBox.information(doc_dialog, "Document", "Document ajouté et synchronisé (à compléter selon la base de données).")

        def scanner_codebarre():
            code, ok = QInputDialog.getText(dialog, "Scan code-barres", "Scannez ou saisissez le code-barres du document :")
            if ok and code:
                QMessageBox.information(dialog, "Code-barres", f"Code-barres scanné : {code}")

        def synchroniser_documents():
            QMessageBox.information(dialog, "Synchronisation", "Synchronisation des documents effectuée (à compléter selon la base de données).")

        add_btn.clicked.connect(ajouter_document)
        scan_btn.clicked.connect(scanner_codebarre)
        sync_btn.clicked.connect(synchroniser_documents)
        dialog.setLayout(layout)
        dialog.exec()
