#!/bin/bash

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction de logging avec timestamp
log() {
    echo -e "[$(date '+%H:%M:%S')] $1"
}

# Fonction pour afficher les titres
title() {
    echo -e "\n${CYAN}========================================================================${NC}"
    echo -e "${CYAN}                    $1${NC}"
    echo -e "${CYAN}========================================================================${NC}\n"
}

# Fonction pour afficher les sous-titres
subtitle() {
    echo -e "\n${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' $(seq 1 ${#1}))${NC}\n"
}

clear
title "GESTIMMOB - LANCEMENT AUTOMATIQUE COMPLET"
echo -e "${YELLOW}Utilise TOUS les moyens nécessaires${NC}"

log "${GREEN}Démarrage du processus d'automatisation complète...${NC}"

# Vérification de Python
log "Vérification de Python..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}ERREUR: Python non trouvé!${NC}"
        echo "Installez Python 3.8+ depuis https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

PYTHON_VERSION=$($PYTHON_CMD --version 2>&1)
log "${GREEN}Python détecté: $PYTHON_VERSION${NC}"

# Vérification des fichiers
log "Vérification des fichiers..."
if [ ! -f "src/main.py" ]; then
    echo -e "${RED}ERREUR: src/main.py non trouvé!${NC}"
    exit 1
fi
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}ERREUR: requirements.txt non trouvé!${NC}"
    exit 1
fi
log "${GREEN}Fichiers principaux: OK${NC}"

# Mise à jour de pip
log "Mise à jour de pip..."
$PYTHON_CMD -m pip install --upgrade pip --quiet

# Installation des dépendances de base
log "Installation des dépendances de base..."
$PYTHON_CMD -m pip install -r requirements.txt --quiet

# Installation des outils de développement
log "Installation des outils de développement..."
$PYTHON_CMD -m pip install flake8 pylint mypy black autopep8 bandit safety pytest --quiet

# Création des répertoires nécessaires
log "Création des répertoires..."
mkdir -p logs backups exports external_tools java_tools sql_tools bash_tools nodejs_tools

# Permissions d'exécution
log "Configuration des permissions..."
chmod +x *.py 2>/dev/null || true
chmod +x bash_tools/*.sh 2>/dev/null || true

title "AUTOMATISATION COMPLÈTE"

# Lancement de l'automatisation complète
log "Lancement du système d'automatisation maître..."
if $PYTHON_CMD master_automation.py; then
    log "${GREEN}Automatisation maître: SUCCÈS${NC}"
else
    log "${YELLOW}ATTENTION: Erreur dans l'automatisation maître${NC}"
    log "${YELLOW}Continuation avec les outils disponibles...${NC}"
fi

# Lancement de l'orchestrateur
log "Lancement de l'orchestrateur..."
if [ -f "master_orchestrator.py" ]; then
    if $PYTHON_CMD master_orchestrator.py; then
        log "${GREEN}Orchestrateur: SUCCÈS${NC}"
    else
        log "${YELLOW}ATTENTION: Erreur dans l'orchestrateur${NC}"
    fi
else
    log "${YELLOW}ATTENTION: Orchestrateur non créé${NC}"
fi

# Correction automatique du code
log "Correction automatique du code..."
if [ -f "auto_fix_main.py" ]; then
    $PYTHON_CMD auto_fix_main.py
else
    log "${YELLOW}ATTENTION: Script de correction non trouvé${NC}"
fi

# Validation complète
log "Validation complète..."
if [ -f "ultimate_validator.py" ]; then
    $PYTHON_CMD ultimate_validator.py
else
    log "${YELLOW}ATTENTION: Validateur non trouvé${NC}"
fi

# Optimisation de la base de données
log "Optimisation de la base de données..."
if [ -f "immobilisations.db" ]; then
    if [ -f "sql_tools/optimize_database.sql" ]; then
        sqlite3 immobilisations.db < sql_tools/optimize_database.sql
        log "${GREEN}Base de données optimisée${NC}"
    fi
else
    log "${YELLOW}Base de données sera créée au premier démarrage${NC}"
fi

title "LANCEMENT FINAL"

# Lancement de GestImmob
log "Lancement de GestImmob..."
if [ -f "start_gestimmob.py" ]; then
    log "Utilisation du script de démarrage optimisé..."
    nohup $PYTHON_CMD start_gestimmob.py > logs/gestimmob.log 2>&1 &
    GESTIMMOB_PID=$!
    log "${GREEN}GestImmob démarré (PID: $GESTIMMOB_PID)${NC}"
else
    log "Démarrage direct..."
    nohup $PYTHON_CMD src/main.py > logs/gestimmob.log 2>&1 &
    GESTIMMOB_PID=$!
    log "${GREEN}GestImmob démarré (PID: $GESTIMMOB_PID)${NC}"
fi

# Démarrage du monitoring
log "Démarrage du monitoring (si disponible)..."
if [ -f "bash_tools/system_monitor.sh" ]; then
    nohup bash bash_tools/system_monitor.sh > logs/monitor.log 2>&1 &
    MONITOR_PID=$!
    log "${GREEN}Monitoring système démarré (PID: $MONITOR_PID)${NC}"
fi

if [ -f "nodejs_tools/tools/monitor.js" ] && [ -d "nodejs_tools/node_modules" ]; then
    cd nodejs_tools
    nohup node tools/monitor.js > ../logs/node_monitor.log 2>&1 &
    NODE_MONITOR_PID=$!
    cd ..
    log "${GREEN}Monitoring Node.js démarré (PID: $NODE_MONITOR_PID)${NC}"
fi

title "TERMINÉ"

log "${GREEN}Processus d'automatisation complète terminé!${NC}"

echo -e "\n${CYAN}OUTILS DISPONIBLES:${NC}"
echo -e "  - ${GREEN}GestImmob${NC}: Lancé automatiquement"
echo -e "  - ${GREEN}Monitoring${NC}: Actif si disponible"
echo -e "  - ${GREEN}Logs${NC}: Répertoire logs/"
echo -e "  - ${GREEN}Rapports${NC}: launch_report.json, validation_report.json"

echo -e "\n${CYAN}COMMANDES UTILES:${NC}"
echo -e "  ${YELLOW}$PYTHON_CMD ultimate_validator.py${NC}    - Validation complète"
echo -e "  ${YELLOW}$PYTHON_CMD auto_fix_main.py${NC}        - Correction automatique"
echo -e "  ${YELLOW}$PYTHON_CMD diagnostic.py${NC}           - Diagnostic système"
echo -e "  ${YELLOW}$PYTHON_CMD master_orchestrator.py${NC}  - Orchestration manuelle"

echo -e "\n${CYAN}PROCESSUS ACTIFS:${NC}"
if [ ! -z "$GESTIMMOB_PID" ]; then
    echo -e "  - ${GREEN}GestImmob${NC}: PID $GESTIMMOB_PID"
fi
if [ ! -z "$MONITOR_PID" ]; then
    echo -e "  - ${GREEN}Monitoring${NC}: PID $MONITOR_PID"
fi
if [ ! -z "$NODE_MONITOR_PID" ]; then
    echo -e "  - ${GREEN}Node Monitor${NC}: PID $NODE_MONITOR_PID"
fi

echo -e "\n${YELLOW}Consultez README_TOOLS.md pour plus d'informations.${NC}"

# Attendre un peu pour voir les messages
sleep 3

# Ouvrir la documentation si disponible
if [ -f "README_TOOLS.md" ]; then
    log "Ouverture de la documentation..."
    if command -v xdg-open &> /dev/null; then
        xdg-open README_TOOLS.md &
    elif command -v open &> /dev/null; then
        open README_TOOLS.md &
    fi
fi

echo -e "\n${GREEN}Lancement terminé! GestImmob fonctionne en arrière-plan.${NC}"
echo -e "${YELLOW}Appuyez sur Entrée pour continuer...${NC}"
read
