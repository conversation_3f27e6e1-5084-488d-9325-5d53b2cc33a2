{"name": "robot-ia-performance", "displayName": "🚀 Robot IA Performance", "description": "Robot IA Ultra-Performant - Gratuit et Indépendant", "version": "2.0.0", "publisher": "Sam<PERSON>ord", "engines": {"vscode": "^1.60.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "robot", "chat", "assistant", "performance"], "activationEvents": ["onStartupFinished", "onCommand:robotIA.chat", "onCommand:robotIA.quickChat"], "main": "./extension.js", "contributes": {"commands": [{"command": "robotIA.chat", "title": "🤖 Robot IA Chat", "category": "Robot IA"}, {"command": "robotIA.quickChat", "title": "⚡ Chat Rapide", "category": "Robot IA"}, {"command": "robotIA.codeAssist", "title": "💻 Assistant Code", "category": "Robot IA"}, {"command": "robotIA.explainCode", "title": "📖 Expliquer Code", "category": "Robot IA"}, {"command": "robotIA.optimizeCode", "title": "⚡ Optimiser Code", "category": "Robot IA"}], "keybindings": [{"command": "robotIA.chat", "key": "ctrl+shift+r"}, {"command": "robotIA.quickChat", "key": "ctrl+alt+r"}], "menus": {"editor/context": [{"command": "robotIA.explainCode", "when": "editorHasSelection", "group": "robotIA"}, {"command": "robotIA.optimizeCode", "when": "editorHasSelection", "group": "robotIA"}]}}}