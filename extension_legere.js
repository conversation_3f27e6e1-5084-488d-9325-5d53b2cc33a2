const vscode = require('vscode');

function activate(context) {
    console.log('Robot IA SamNord activé (version légère)');
    
    // Status bar simple
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = "🤖 Robot IA";
    statusBarItem.tooltip = "Robot IA SamNord - Cliquez pour lancer";
    statusBarItem.command = "robotIA.launch";
    statusBarItem.show();
    
    // Commande simple de lancement
    const launchCommand = vscode.commands.registerCommand('robotIA.launch', () => {
        // Message simple
        vscode.window.showInformationMessage('🤖 Robot IA SamNord - Lancement externe sécurisé');
        
        // Lancement externe sécurisé (ne fait pas crasher VS Code)
        const { exec } = require('child_process');
        exec('LANCER_ROBOT_SECURISE.bat', { cwd: vscode.workspace.workspaceFolders[0].uri.fsPath });
    });
    
    // Chat simple
    const chatCommand = vscode.commands.registerCommand('robotIA.chat', () => {
        const panel = vscode.window.createWebviewPanel(
            'robotIAChat',
            '🤖 Robot IA SamNord',
            vscode.ViewColumn.One,
            { enableScripts: false }
        );
        
        panel.webview.html = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { 
                    background: #1e1e1e; 
                    color: white; 
                    font-family: 'Segoe UI'; 
                    padding: 20px; 
                }
                .header { 
                    text-align: center; 
                    border-bottom: 2px solid #007acc; 
                    padding-bottom: 15px; 
                }
                .message { 
                    background: #2d2d30; 
                    padding: 15px; 
                    margin: 10px 0; 
                    border-radius: 8px; 
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 Robot IA SamNord</h1>
                <p>👤 SamNord@110577 | Terminal Externe Sécurisé</p>
            </div>
            
            <div class="message">
                <strong>🤖 Robot IA:</strong> Bonjour SamNord@110577 ! 
                Je suis maintenant disponible en terminal externe sécurisé qui ne fait pas crasher VS Code.
            </div>
            
            <div class="message">
                <strong>🚀 Utilisation:</strong><br>
                • Cliquez sur "🤖 Robot IA" dans la barre de statut<br>
                • Terminal externe lancé automatiquement<br>
                • VS Code reste stable<br>
                • Tous les modules disponibles
            </div>
            
            <div class="message">
                <strong>📦 Modules disponibles:</strong><br>
                🧠 Intelligence IA Transcendante<br>
                📡 WiFi Manager Avancé<br>
                📱 Bluetooth Manager<br>
                🛡️ Cybersécurité Éthique<br>
                🌍 Traduction Temps Réel<br>
                🗣️ Communication Multilingue<br>
                💬 Chat interactif intégré
            </div>
        </body>
        </html>`;
    });
    
    context.subscriptions.push(launchCommand, chatCommand, statusBarItem);
    
    // Message d'activation
    vscode.window.showInformationMessage('🤖 Robot IA SamNord prêt ! Version sécurisée');
}

function deactivate() {}

module.exports = { activate, deactivate };
