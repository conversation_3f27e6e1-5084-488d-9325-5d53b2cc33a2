<!-- Generated by SpecStory -->

# Untitled (2025-06-08 11:43:24)

_**User**_

/fix Le type de retour « Unknown | Literal[''] » est partiellement inconnu

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Le problème vient de la fonction `safe_text` qui n'a pas d'annotation de type de retour explicite et dont le type de retour n'est pas clair pour l'analyseur statique.  
Pour corriger cela, il faut :
1. Importer `Optional` depuis `typing` si ce n'est pas déjà fait.
2. Annoter explicitement le type de retour de la fonction comme `str`.
3. S'assurer que la fonction retourne toujours une chaîne (`str`).

Voici la correction :

````python
# ...existing code...
        from typing import Optional
        def safe_text(item: Optional[QTableWidgetItem]) -> str:
            if item is None:
                return ""
            text_attr = getattr(item, "text", None)
            if callable(text_attr):
                try:
                    v = text_attr()
                    if v is not None:
                        return str(v)
                except Exception:
                    return ""
            return ""
# ...existing code...
````

---

