# RÉSOLUTION PYTHON AVEC POWERSHELL
# Solution avancée pour Windows

param(
    [switch]$Force,
    [switch]$Quiet
)

Write-Host "🐍 RÉSOLUTION PYTHON AVEC POWERSHELL" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

# Fonction pour écrire avec couleur
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Fonction pour tester Python
function Test-PythonInstallation {
    $pythonCommands = @("python", "python3", "py")
    
    foreach ($cmd in $pythonCommands) {
        try {
            $version = & $cmd --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Python trouvé avec '$cmd': $version" "Green"
                return $cmd
            }
        }
        catch {
            continue
        }
    }
    
    Write-ColorOutput "❌ Aucune installation Python détectée" "Red"
    return $null
}

# Fonction pour installer Python automatiquement
function Install-PythonAutomatic {
    Write-ColorOutput "📦 Installation automatique de Python..." "Yellow"
    
    # URL de téléchargement Python 3.11
    $pythonVersion = "3.11.7"
    $architecture = if ([Environment]::Is64BitOperatingSystem) { "amd64" } else { "win32" }
    $downloadUrl = "https://www.python.org/ftp/python/$pythonVersion/python-$pythonVersion-$architecture.exe"
    $installerPath = "$env:TEMP\python_installer.exe"
    
    try {
        Write-ColorOutput "📥 Téléchargement de Python $pythonVersion..." "Cyan"
        
        # Télécharger avec progress
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($downloadUrl, $installerPath)
        
        if (Test-Path $installerPath) {
            Write-ColorOutput "✅ Téléchargement réussi" "Green"
            
            Write-ColorOutput "🔧 Installation en cours..." "Yellow"
            
            # Installer Python silencieusement
            $installArgs = @(
                "/quiet",
                "InstallAllUsers=1",
                "PrependPath=1",
                "Include_test=0",
                "Include_doc=0"
            )
            
            $process = Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -PassThru
            
            if ($process.ExitCode -eq 0) {
                Write-ColorOutput "✅ Installation Python réussie !" "Green"
                
                # Nettoyer
                Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
                
                # Rafraîchir les variables d'environnement
                $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
                
                return $true
            }
            else {
                Write-ColorOutput "❌ Échec de l'installation (Code: $($process.ExitCode))" "Red"
                return $false
            }
        }
        else {
            Write-ColorOutput "❌ Échec du téléchargement" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Erreur lors de l'installation: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Fonction pour corriger le PATH
function Fix-PythonPath {
    Write-ColorOutput "🛤️ Correction du PATH Python..." "Yellow"
    
    # Chercher les installations Python
    $pythonPaths = @()
    
    # Emplacements standards
    $searchPaths = @(
        "C:\Python*",
        "C:\Program Files\Python*",
        "C:\Program Files (x86)\Python*",
        "$env:LOCALAPPDATA\Programs\Python*"
    )
    
    foreach ($searchPath in $searchPaths) {
        $found = Get-ChildItem -Path $searchPath -Directory -ErrorAction SilentlyContinue
        foreach ($dir in $found) {
            if (Test-Path "$($dir.FullName)\python.exe") {
                $pythonPaths += $dir.FullName
                $pythonPaths += "$($dir.FullName)\Scripts"
            }
        }
    }
    
    if ($pythonPaths.Count -gt 0) {
        Write-ColorOutput "📁 Installations Python trouvées:" "Cyan"
        $pythonPaths | ForEach-Object { Write-ColorOutput "  - $_" "White" }
        
        # Obtenir le PATH actuel
        $currentPath = [Environment]::GetEnvironmentVariable("Path", "User")
        $pathsToAdd = @()
        
        foreach ($path in $pythonPaths) {
            if ($currentPath -notlike "*$path*") {
                $pathsToAdd += $path
            }
        }
        
        if ($pathsToAdd.Count -gt 0) {
            $newPath = $currentPath + ";" + ($pathsToAdd -join ";")
            [Environment]::SetEnvironmentVariable("Path", $newPath, "User")
            
            Write-ColorOutput "✅ PATH mis à jour avec $($pathsToAdd.Count) nouveau(x) chemin(s)" "Green"
            
            # Mettre à jour la session actuelle
            $env:Path = $newPath
            
            return $true
        }
        else {
            Write-ColorOutput "✅ PATH déjà correct" "Green"
            return $true
        }
    }
    else {
        Write-ColorOutput "❌ Aucune installation Python trouvée" "Red"
        return $false
    }
}

# Fonction pour installer pip
function Install-Pip {
    param([string]$PythonCommand)
    
    Write-ColorOutput "📦 Installation de pip..." "Yellow"
    
    try {
        # Méthode 1: ensurepip
        $result = & $PythonCommand -m ensurepip --upgrade 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ pip installé avec ensurepip" "Green"
            return $true
        }
        
        # Méthode 2: get-pip.py
        $getPipUrl = "https://bootstrap.pypa.io/get-pip.py"
        $getPipPath = "$env:TEMP\get-pip.py"
        
        Invoke-WebRequest -Uri $getPipUrl -OutFile $getPipPath
        
        if (Test-Path $getPipPath) {
            $result = & $PythonCommand $getPipPath
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ pip installé avec get-pip.py" "Green"
                Remove-Item $getPipPath -Force -ErrorAction SilentlyContinue
                return $true
            }
        }
        
        Write-ColorOutput "❌ Échec installation pip" "Red"
        return $false
    }
    catch {
        Write-ColorOutput "❌ Erreur installation pip: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Fonction pour tester l'installation complète
function Test-PythonComplete {
    param([string]$PythonCommand)
    
    Write-ColorOutput "🧪 Test complet de l'installation..." "Yellow"
    
    $tests = @()
    
    # Test 1: Version Python
    try {
        $version = & $PythonCommand --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $tests += @{ Name = "Version Python"; Status = "✅"; Details = $version }
        }
        else {
            $tests += @{ Name = "Version Python"; Status = "❌"; Details = "Échec" }
        }
    }
    catch {
        $tests += @{ Name = "Version Python"; Status = "❌"; Details = "Erreur" }
    }
    
    # Test 2: pip
    try {
        $pipVersion = & $PythonCommand -m pip --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $tests += @{ Name = "pip"; Status = "✅"; Details = $pipVersion }
        }
        else {
            $tests += @{ Name = "pip"; Status = "❌"; Details = "Non disponible" }
        }
    }
    catch {
        $tests += @{ Name = "pip"; Status = "❌"; Details = "Erreur" }
    }
    
    # Test 3: Import modules
    try {
        $importTest = & $PythonCommand -c "import os, sys, json; print('Modules OK')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            $tests += @{ Name = "Import modules"; Status = "✅"; Details = "OK" }
        }
        else {
            $tests += @{ Name = "Import modules"; Status = "❌"; Details = "Échec" }
        }
    }
    catch {
        $tests += @{ Name = "Import modules"; Status = "❌"; Details = "Erreur" }
    }
    
    # Test 4: Installation package
    try {
        $packageTest = & $PythonCommand -m pip install --quiet requests 2>$null
        if ($LASTEXITCODE -eq 0) {
            $importTest = & $PythonCommand -c "import requests; print('Package OK')" 2>$null
            if ($LASTEXITCODE -eq 0) {
                $tests += @{ Name = "Installation package"; Status = "✅"; Details = "OK" }
            }
            else {
                $tests += @{ Name = "Installation package"; Status = "❌"; Details = "Import échec" }
            }
        }
        else {
            $tests += @{ Name = "Installation package"; Status = "❌"; Details = "Installation échec" }
        }
    }
    catch {
        $tests += @{ Name = "Installation package"; Status = "❌"; Details = "Erreur" }
    }
    
    # Afficher les résultats
    Write-ColorOutput "`n📊 RÉSULTATS DES TESTS:" "Cyan"
    foreach ($test in $tests) {
        Write-ColorOutput "  $($test.Status) $($test.Name): $($test.Details)" "White"
    }
    
    $successCount = ($tests | Where-Object { $_.Status -eq "✅" }).Count
    $totalCount = $tests.Count
    $percentage = [math]::Round(($successCount / $totalCount) * 100, 1)
    
    Write-ColorOutput "`n📈 Score: $successCount/$totalCount ($percentage%)" "Cyan"
    
    return $percentage -ge 75
}

# Fonction principale
function Main {
    Write-ColorOutput "🔍 Diagnostic initial..." "Cyan"
    
    # Test initial
    $pythonCmd = Test-PythonInstallation
    
    if ($pythonCmd) {
        Write-ColorOutput "🧪 Test de l'installation existante..." "Yellow"
        
        if (Test-PythonComplete -PythonCommand $pythonCmd) {
            Write-ColorOutput "`n🎉 PYTHON DÉJÀ PARFAITEMENT CONFIGURÉ !" "Green"
            Write-ColorOutput "✅ Aucune action nécessaire" "Green"
            return $true
        }
        else {
            Write-ColorOutput "`n⚠️ Python détecté mais problématique" "Yellow"
            Write-ColorOutput "🔧 Tentative de correction..." "Yellow"
            
            # Corriger le PATH
            Fix-PythonPath | Out-Null
            
            # Réinstaller pip si nécessaire
            Install-Pip -PythonCommand $pythonCmd | Out-Null
            
            # Test après correction
            if (Test-PythonComplete -PythonCommand $pythonCmd) {
                Write-ColorOutput "`n🎉 PYTHON CORRIGÉ AVEC SUCCÈS !" "Green"
                return $true
            }
        }
    }
    
    # Installation nécessaire
    Write-ColorOutput "`n🔧 Installation de Python nécessaire..." "Yellow"
    
    if (Install-PythonAutomatic) {
        # Attendre un peu pour que l'installation se finalise
        Start-Sleep -Seconds 5
        
        # Tester la nouvelle installation
        $newPythonCmd = Test-PythonInstallation
        
        if ($newPythonCmd) {
            if (Test-PythonComplete -PythonCommand $newPythonCmd) {
                Write-ColorOutput "`n🎉 PYTHON INSTALLÉ ET CONFIGURÉ AVEC SUCCÈS !" "Green"
                return $true
            }
        }
    }
    
    # Échec - guide manuel
    Write-ColorOutput "`n❌ Installation automatique échouée" "Red"
    Write-ColorOutput "`n📋 GUIDE D'INSTALLATION MANUELLE:" "Yellow"
    Write-ColorOutput "1. Allez sur https://www.python.org/downloads/" "White"
    Write-ColorOutput "2. Téléchargez Python 3.11 ou plus récent" "White"
    Write-ColorOutput "3. ✅ COCHEZ 'Add Python to PATH' lors de l'installation" "Green"
    Write-ColorOutput "4. Redémarrez votre ordinateur" "White"
    Write-ColorOutput "5. Relancez ce script" "White"
    
    return $false
}

# Exécution principale
try {
    $success = Main
    
    Write-ColorOutput "`n" + ("=" * 60) "Green"
    
    if ($success) {
        Write-ColorOutput "🏆 RÉSOLUTION PYTHON RÉUSSIE !" "Green"
        Write-ColorOutput "✅ Python est maintenant parfaitement fonctionnel" "Green"
        Write-ColorOutput "🚀 Vous pouvez utiliser Python sans problème" "Green"
        
        # Test avec GestImmob si présent
        if (Test-Path "src\main.py") {
            Write-ColorOutput "`n🏠 Projet GestImmob détecté !" "Cyan"
            $testGestImmob = Read-Host "Tester le lancement de GestImmob ? (o/n)"
            
            if ($testGestImmob -eq "o") {
                Write-ColorOutput "🚀 Test de lancement GestImmob..." "Yellow"
                try {
                    python src\main.py
                    Write-ColorOutput "✅ GestImmob lancé avec succès !" "Green"
                }
                catch {
                    Write-ColorOutput "⚠️ GestImmob nécessite peut-être des dépendances" "Yellow"
                    Write-ColorOutput "Utilisez: python -m pip install -r requirements.txt" "White"
                }
            }
        }
    }
    else {
        Write-ColorOutput "⚠️ RÉSOLUTION PARTIELLE" "Yellow"
        Write-ColorOutput "Suivez le guide d'installation manuelle ci-dessus" "White"
    }
    
    Write-ColorOutput "`n" + ("=" * 60) "Green"
}
catch {
    Write-ColorOutput "❌ Erreur inattendue: $($_.Exception.Message)" "Red"
}

Write-Host "`nAppuyez sur Entrée pour continuer..."
Read-Host
