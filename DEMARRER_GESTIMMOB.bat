@echo off
title DEMARRER GESTIMMOB - Lancement Optimise
color 0A

echo.
echo ========================================================================
echo                         DEMARRER GESTIMMOB
echo                    Lancement optimise du logiciel
echo ========================================================================
echo.

REM Variables
set "PYTHON_CMD="
set "MAIN_FILE="
set "LAUNCH_SUCCESS=0"

echo [%TIME%] Initialisation du lancement de GestImmob...

REM Détection automatique de Python
echo.
echo 🔍 DETECTION PYTHON...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo ✅ Python trouve: %%i
    goto :python_ok
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python trouve: %%i
    goto :python_ok
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    for /f "tokens=*" %%i in ('python3 --version 2^>^&1') do echo ✅ Python trouve: %%i
    goto :python_ok
)

echo ❌ Python non detecte
echo.
echo 🔧 RESOLUTION AUTOMATIQUE:
if exist "SOLUTION_PYTHON_FINALE.bat" (
    echo Lancement de la solution Python automatique...
    call SOLUTION_PYTHON_FINALE.bat
    
    REM Re-test après résolution
    py --version >nul 2>&1
    if not errorlevel 1 (
        set "PYTHON_CMD=py"
        echo ✅ Python maintenant disponible
        goto :python_ok
    )
) else (
    echo ❌ Solution Python non trouvee
    echo.
    echo 📋 INSTALLATION MANUELLE REQUISE:
    echo 1. Allez sur https://python.org/downloads/
    echo 2. Telechargez et installez Python 3.8+
    echo 3. Cochez "Add Python to PATH"
    echo 4. Relancez ce script
    goto :end
)

:python_ok
echo Commande Python: %PYTHON_CMD%

REM Détection automatique du fichier principal
echo.
echo 📁 DETECTION FICHIER PRINCIPAL...

REM Ordre de priorité des fichiers
if exist "src\main.py" (
    set "MAIN_FILE=src\main.py"
    echo ✅ Fichier principal: src\main.py
    goto :file_found
)

if exist "gestimmob_final.py" (
    set "MAIN_FILE=gestimmob_final.py"
    echo ✅ Fichier alternatif: gestimmob_final.py
    goto :file_found
)

if exist "gestimmob_complet_final.py" (
    set "MAIN_FILE=gestimmob_complet_final.py"
    echo ✅ Fichier alternatif: gestimmob_complet_final.py
    goto :file_found
)

if exist "LANCER_GESTIMMOB_FINAL_V5.py" (
    set "MAIN_FILE=LANCER_GESTIMMOB_FINAL_V5.py"
    echo ✅ Fichier alternatif: LANCER_GESTIMMOB_FINAL_V5.py
    goto :file_found
)

if exist "gestimmob_enterprise_v5.py" (
    set "MAIN_FILE=gestimmob_enterprise_v5.py"
    echo ✅ Fichier alternatif: gestimmob_enterprise_v5.py
    goto :file_found
)

echo ❌ Aucun fichier principal trouve
echo.
echo 📋 FICHIERS RECHERCHES:
echo   - src\main.py
echo   - gestimmob_final.py
echo   - gestimmob_complet_final.py
echo   - LANCER_GESTIMMOB_FINAL_V5.py
echo   - gestimmob_enterprise_v5.py
goto :end

:file_found
echo Fichier a lancer: %MAIN_FILE%

REM Vérification et installation des dépendances
echo.
echo 📦 VERIFICATION DEPENDANCES...

REM Test PySide6
echo Verification PySide6...
%PYTHON_CMD% -c "import PySide6; print('✅ PySide6 disponible')" 2>nul
if errorlevel 1 (
    echo ❌ PySide6 manquant - Installation...
    %PYTHON_CMD% -m pip install PySide6 --quiet
    if not errorlevel 1 (
        echo ✅ PySide6 installe avec succes
    ) else (
        echo ⚠️ Echec installation PySide6
    )
) else (
    echo ✅ PySide6 OK
)

REM Test SQLAlchemy
echo Verification SQLAlchemy...
%PYTHON_CMD% -c "import sqlalchemy; print('✅ SQLAlchemy disponible')" 2>nul
if errorlevel 1 (
    echo ❌ SQLAlchemy manquant - Installation...
    %PYTHON_CMD% -m pip install SQLAlchemy --quiet
    if not errorlevel 1 (
        echo ✅ SQLAlchemy installe avec succes
    ) else (
        echo ⚠️ Echec installation SQLAlchemy
    )
) else (
    echo ✅ SQLAlchemy OK
)

REM Installation depuis requirements.txt si disponible
if exist "requirements.txt" (
    echo Installation dependances depuis requirements.txt...
    %PYTHON_CMD% -m pip install -r requirements.txt --quiet >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Dependances requirements.txt installees
    )
)

REM Préparation de l'environnement
echo.
echo 🔧 PREPARATION ENVIRONNEMENT...

REM Créer les répertoires nécessaires
if not exist "data" (
    mkdir data
    echo ✅ Repertoire data cree
)

if not exist "logs" (
    mkdir logs
    echo ✅ Repertoire logs cree
)

if not exist "backups" (
    mkdir backups
    echo ✅ Repertoire backups cree
)

if not exist "temp" (
    mkdir temp
    echo ✅ Repertoire temp cree
)

REM Test de syntaxe du fichier principal
echo.
echo 🧪 TEST SYNTAXE...
%PYTHON_CMD% -m py_compile %MAIN_FILE% 2>nul
if not errorlevel 1 (
    echo ✅ Syntaxe du fichier principale: OK
) else (
    echo ⚠️ Probleme de syntaxe detecte
    echo Tentative de correction automatique...
    
    if exist "corriger_tous_problemes.py" (
        %PYTHON_CMD% corriger_tous_problemes.py >nul 2>&1
        echo ✅ Correction automatique tentee
    )
)

REM Lancement de l'application
echo.
echo ========================================================================
echo                           LANCEMENT GESTIMMOB
echo ========================================================================
echo.

echo [%TIME%] 🚀 DEMARRAGE DE GESTIMMOB...
echo.
echo Configuration:
echo   Python: %PYTHON_CMD%
echo   Fichier: %MAIN_FILE%
echo   Repertoire: %CD%
echo.

echo Lancement en cours...
echo.

REM Lancer l'application avec gestion d'erreur
%PYTHON_CMD% %MAIN_FILE%
set "EXIT_CODE=%ERRORLEVEL%"

echo.
if %EXIT_CODE%==0 (
    echo ✅ GESTIMMOB FERME NORMALEMENT
    set /a LAUNCH_SUCCESS=1
) else (
    echo ❌ GESTIMMOB FERME AVEC ERREUR (Code: %EXIT_CODE%)
    
    echo.
    echo 🔧 DIAGNOSTIC RAPIDE:
    
    REM Test rapide des imports
    %PYTHON_CMD% -c "import sys; print(f'Python {sys.version}')" 2>nul
    if errorlevel 1 (
        echo ❌ Probleme Python de base
    ) else (
        echo ✅ Python de base OK
    )
    
    %PYTHON_CMD% -c "import PySide6" 2>nul
    if errorlevel 1 (
        echo ❌ Probleme PySide6
    ) else (
        echo ✅ PySide6 OK
    )
    
    echo.
    echo 💡 SOLUTIONS POSSIBLES:
    echo 1. Relancez ce script
    echo 2. Executez: SOLUTION_PYTHON_FINALE.bat
    echo 3. Verifiez les erreurs dans les logs
    echo 4. Installez manuellement: pip install PySide6 SQLAlchemy
)

:end
echo.
echo ========================================================================
echo                              RESUME FINAL
echo ========================================================================
echo.

echo [%TIME%] Fin du script de lancement

if %LAUNCH_SUCCESS%==1 (
    echo.
    echo 🎉 LANCEMENT REUSSI !
    echo ✅ GestImmob a ete lance et ferme normalement
    echo ✅ Configuration: %PYTHON_CMD% + %MAIN_FILE%
    echo.
    echo 💡 Pour relancer GestImmob, executez a nouveau ce script
) else (
    echo.
    echo ⚠️ PROBLEME DE LANCEMENT
    echo.
    echo 📋 VERIFICATIONS EFFECTUEES:
    echo   - Detection Python: %PYTHON_CMD%
    echo   - Fichier principal: %MAIN_FILE%
    echo   - Installation dependances: Tentee
    echo   - Preparation environnement: OK
    echo.
    echo 🔧 ACTIONS RECOMMANDEES:
    echo 1. Executez SOLUTION_PYTHON_FINALE.bat
    echo 2. Verifiez l installation de Python
    echo 3. Installez manuellement les dependances
    echo 4. Consultez les logs d erreur
    echo 5. Relancez ce script
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul
