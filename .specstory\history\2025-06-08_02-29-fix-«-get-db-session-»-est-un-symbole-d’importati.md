<!-- Generated by SpecStory -->

# /fix « get_db_session » est un symbole d’importati (2025-06-08 03:29:20)

_**User**_

/fix « get_db_session » est un symbole d’importation inconnu, Le type de « get_db_session » est inconnu

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Corrigez l'import de `get_db_session` en utilisant un import relatif (si le module `db_connection.py` existe bien dans `src/database/`). Supprimez les vérifications et commentaires superflus liés à l'import et au type de `get_db_session` pour alléger le code et éviter les erreurs d'importation inconnue.

````python
# ...existing code...
from sqlalchemy.orm import Session  # Import pour vérifier le type de session

Base = declarative_base()

if TYPE_CHECKING:
    pass  # Suppression de l'import inutile de User

# Correction de l'import : utilisez un import relatif si le module existe dans src/database/
from ..database.db_connection import get_db_session

from encryption import encrypt_password, verify_password

class User(Base):
    # ...existing code...
    pass
# ...existing code...
````

---

