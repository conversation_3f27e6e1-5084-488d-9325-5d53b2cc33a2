#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💼 ROBOT IA - MASTER COMPTA GÉNÉRAL
Système comptabilité complète avec 11 modules
Créé par Augment Agent pour SamNord@110577
"""

import sys
import os
import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
import decimal

class MasterComptaGeneral:
    """💼 Système Master Comptabilité Général complet"""
    
    def __init__(self):
        self.user = "SamNord@110577"
        self.db_path = "master_compta.db"
        self.exercice_actuel = datetime.now().year
        
        # 11 Modules comptabilité
        self.modules_compta = {
            1: {"nom": "Saisie Écritures", "icon": "📝", "desc": "Saisie écritures comptables"},
            2: {"nom": "Plan Comptable", "icon": "📋", "desc": "Gestion plan comptable"},
            3: {"nom": "Grand Livre", "icon": "📚", "desc": "Grand livre général"},
            4: {"nom": "Balance", "icon": "⚖️", "desc": "Balance générale"},
            5: {"nom": "Bilan", "icon": "📊", "desc": "Bilan comptable"},
            6: {"nom": "Compte Résultat", "icon": "💹", "desc": "Compte de résultat"},
            7: {"nom": "Trésorerie", "icon": "💰", "desc": "Gestion trésorerie"},
            8: {"nom": "Immobilisations", "icon": "🏢", "desc": "Gestion immobilisations"},
            9: {"nom": "TVA", "icon": "🧾", "desc": "Gestion TVA"},
            10: {"nom": "Paie", "icon": "💵", "desc": "Gestion paie"},
            11: {"nom": "Rapports", "icon": "📋", "desc": "Rapports comptables"}
        }
        
        # Initialisation base de données
        self._init_database_compta()
        
        print(f"💼 Master Compta Général initialisé pour {self.user}")
        print(f"📅 Exercice: {self.exercice_actuel}")
        print(f"📦 {len(self.modules_compta)} modules comptables")
    
    def _init_database_compta(self):
        """Initialisation base de données comptable"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Table plan comptable
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS plan_comptable (
                    numero_compte TEXT PRIMARY KEY,
                    intitule TEXT NOT NULL,
                    classe INTEGER,
                    type_compte TEXT,
                    debit_credit TEXT,
                    actif BOOLEAN DEFAULT 1
                )
            ''')
            
            # Table écritures
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ecritures (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date_ecriture DATE,
                    numero_piece TEXT,
                    libelle TEXT,
                    compte_debit TEXT,
                    compte_credit TEXT,
                    montant DECIMAL(15,2),
                    exercice INTEGER,
                    journal TEXT,
                    timestamp TEXT
                )
            ''')
            
            # Table journaux
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS journaux (
                    code_journal TEXT PRIMARY KEY,
                    libelle TEXT,
                    type_journal TEXT
                )
            ''')
            
            # Table immobilisations
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS immobilisations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    designation TEXT,
                    date_acquisition DATE,
                    valeur_acquisition DECIMAL(15,2),
                    duree_amortissement INTEGER,
                    taux_amortissement DECIMAL(5,2),
                    valeur_nette DECIMAL(15,2)
                )
            ''')
            
            # Insertion plan comptable de base
            self._insert_plan_comptable_base(cursor)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur init database: {e}")
    
    def _insert_plan_comptable_base(self, cursor):
        """Insertion plan comptable de base"""
        comptes_base = [
            ("101000", "Capital", 1, "Capitaux", "Credit"),
            ("106000", "Réserves", 1, "Capitaux", "Credit"),
            ("120000", "Résultat", 1, "Capitaux", "Credit"),
            ("164000", "Emprunts", 1, "Dettes", "Credit"),
            ("211000", "Terrains", 2, "Immobilisations", "Debit"),
            ("213000", "Constructions", 2, "Immobilisations", "Debit"),
            ("215000", "Matériel", 2, "Immobilisations", "Debit"),
            ("281000", "Amort. Constructions", 2, "Amortissements", "Credit"),
            ("285000", "Amort. Matériel", 2, "Amortissements", "Credit"),
            ("370000", "Stocks", 3, "Stocks", "Debit"),
            ("411000", "Clients", 4, "Créances", "Debit"),
            ("401000", "Fournisseurs", 4, "Dettes", "Credit"),
            ("445660", "TVA Déductible", 4, "TVA", "Debit"),
            ("445710", "TVA Collectée", 4, "TVA", "Credit"),
            ("512000", "Banque", 5, "Trésorerie", "Debit"),
            ("530000", "Caisse", 5, "Trésorerie", "Debit"),
            ("601000", "Achats", 6, "Charges", "Debit"),
            ("621000", "Personnel", 6, "Charges", "Debit"),
            ("701000", "Ventes", 7, "Produits", "Credit")
        ]
        
        for compte in comptes_base:
            cursor.execute('''
                INSERT OR IGNORE INTO plan_comptable 
                (numero_compte, intitule, classe, type_compte, debit_credit)
                VALUES (?, ?, ?, ?, ?)
            ''', compte)
    
    def afficher_menu_principal(self):
        """Menu principal Master Compta"""
        while True:
            print(f"\n💼 MASTER COMPTA GÉNÉRAL - {self.exercice_actuel}")
            print("=" * 50)
            print(f"👤 {self.user}")
            print()
            
            print("📦 MODULES COMPTABLES:")
            for num, module in self.modules_compta.items():
                print(f"   {num:2d}. {module['icon']} {module['nom']} - {module['desc']}")
            
            print("\n🎯 FONCTIONS GÉNÉRALES:")
            print("   90. 📊 Tableau de bord")
            print("   91. 🔄 Clôture exercice")
            print("   92. 📋 Rapport complet")
            print("   93. 💾 Sauvegarde")
            print("   94. 📤 Export données")
            print("   0.  ❌ Quitter")
            
            choix = input(f"\n➤ Choisir module (1-11) ou fonction (90-94): ").strip()
            
            if choix == "0":
                print("👋 Master Compta fermé")
                break
            elif choix.isdigit():
                num_choix = int(choix)
                if 1 <= num_choix <= 11:
                    self._executer_module(num_choix)
                elif num_choix == 90:
                    self._tableau_de_bord()
                elif num_choix == 91:
                    self._cloture_exercice()
                elif num_choix == 92:
                    self._rapport_complet()
                elif num_choix == 93:
                    self._sauvegarde_donnees()
                elif num_choix == 94:
                    self._export_donnees()
                else:
                    print("❌ Numéro invalide")
            else:
                print("❌ Choix invalide")
    
    def _executer_module(self, num_module):
        """Exécution module comptable"""
        module = self.modules_compta[num_module]
        
        print(f"\n{module['icon']} MODULE: {module['nom'].upper()}")
        print("=" * 40)
        print(f"📝 {module['desc']}")
        print()
        
        if num_module == 1:
            self._module_saisie_ecritures()
        elif num_module == 2:
            self._module_plan_comptable()
        elif num_module == 3:
            self._module_grand_livre()
        elif num_module == 4:
            self._module_balance()
        elif num_module == 5:
            self._module_bilan()
        elif num_module == 6:
            self._module_compte_resultat()
        elif num_module == 7:
            self._module_tresorerie()
        elif num_module == 8:
            self._module_immobilisations()
        elif num_module == 9:
            self._module_tva()
        elif num_module == 10:
            self._module_paie()
        elif num_module == 11:
            self._module_rapports()
    
    def _module_saisie_ecritures(self):
        """Module 1: Saisie écritures"""
        print("📝 SAISIE ÉCRITURES COMPTABLES")
        print("=" * 35)
        
        print("🎯 Options:")
        print("1. ➕ Nouvelle écriture")
        print("2. 📋 Liste écritures")
        print("3. ✏️ Modifier écriture")
        print("4. 🗑️ Supprimer écriture")
        
        choix = input("\n➤ Votre choix (1-4): ").strip()
        
        if choix == "1":
            self._saisir_nouvelle_ecriture()
        elif choix == "2":
            self._lister_ecritures()
        elif choix == "3":
            self._modifier_ecriture()
        elif choix == "4":
            self._supprimer_ecriture()
    
    def _saisir_nouvelle_ecriture(self):
        """Saisie nouvelle écriture"""
        print("➕ NOUVELLE ÉCRITURE")
        print("=" * 20)
        
        # Saisie données
        date_ecriture = input("📅 Date (YYYY-MM-DD): ").strip()
        if not date_ecriture:
            date_ecriture = datetime.now().strftime("%Y-%m-%d")
        
        numero_piece = input("📄 Numéro pièce: ").strip()
        libelle = input("📝 Libellé: ").strip()
        
        # Affichage comptes disponibles
        print("\n📋 Comptes disponibles:")
        comptes = self._get_comptes_actifs()
        for i, (numero, intitule) in enumerate(comptes[:10], 1):
            print(f"   {numero} - {intitule}")
        
        compte_debit = input("\n💳 Compte débit: ").strip()
        compte_credit = input("💳 Compte crédit: ").strip()
        
        try:
            montant = float(input("💰 Montant: ").strip())
        except ValueError:
            print("❌ Montant invalide")
            return
        
        journal = input("📖 Journal (défaut VTE): ").strip() or "VTE"
        
        # Validation
        if not all([libelle, compte_debit, compte_credit, montant > 0]):
            print("❌ Données incomplètes")
            return
        
        # Enregistrement
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO ecritures 
                (date_ecriture, numero_piece, libelle, compte_debit, compte_credit, 
                 montant, exercice, journal, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                date_ecriture, numero_piece, libelle, compte_debit, compte_credit,
                montant, self.exercice_actuel, journal, datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            print("✅ Écriture enregistrée avec succès")
            
        except Exception as e:
            print(f"❌ Erreur enregistrement: {e}")
    
    def _module_plan_comptable(self):
        """Module 2: Plan comptable"""
        print("📋 PLAN COMPTABLE")
        print("=" * 20)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT numero_compte, intitule, classe, type_compte, debit_credit
                FROM plan_comptable 
                WHERE actif = 1
                ORDER BY numero_compte
            ''')
            
            comptes = cursor.fetchall()
            conn.close()
            
            print(f"\n📊 {len(comptes)} comptes actifs:")
            print("-" * 60)
            print("Compte    | Intitulé                    | Classe | Type")
            print("-" * 60)
            
            for compte, intitule, classe, type_compte, debit_credit in comptes:
                print(f"{compte:<9} | {intitule:<26} | {classe:<6} | {type_compte}")
            
        except Exception as e:
            print(f"❌ Erreur lecture plan: {e}")
    
    def _module_balance(self):
        """Module 4: Balance générale"""
        print("⚖️ BALANCE GÉNÉRALE")
        print("=" * 25)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Calcul soldes par compte
            cursor.execute('''
                SELECT 
                    COALESCE(d.compte_debit, c.compte_credit) as compte,
                    COALESCE(SUM(d.montant), 0) as total_debit,
                    COALESCE(SUM(c.montant), 0) as total_credit
                FROM 
                    (SELECT compte_debit, montant FROM ecritures WHERE exercice = ?) d
                FULL OUTER JOIN 
                    (SELECT compte_credit, montant FROM ecritures WHERE exercice = ?) c
                ON d.compte_debit = c.compte_credit
                GROUP BY COALESCE(d.compte_debit, c.compte_credit)
                ORDER BY compte
            ''', (self.exercice_actuel, self.exercice_actuel))
            
            soldes = cursor.fetchall()
            conn.close()
            
            print(f"\n📊 Balance au {datetime.now().strftime('%d/%m/%Y')}:")
            print("-" * 70)
            print("Compte    | Débit        | Crédit       | Solde")
            print("-" * 70)
            
            total_debit = 0
            total_credit = 0
            
            for compte, debit, credit in soldes:
                solde = debit - credit
                total_debit += debit
                total_credit += credit
                
                print(f"{compte:<9} | {debit:>11,.2f} | {credit:>11,.2f} | {solde:>11,.2f}")
            
            print("-" * 70)
            print(f"{'TOTAUX':<9} | {total_debit:>11,.2f} | {total_credit:>11,.2f} | {total_debit-total_credit:>11,.2f}")
            
        except Exception as e:
            print(f"❌ Erreur calcul balance: {e}")
    
    def _tableau_de_bord(self):
        """Tableau de bord comptable"""
        print("📊 TABLEAU DE BORD COMPTABLE")
        print("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Statistiques générales
            cursor.execute('SELECT COUNT(*) FROM ecritures WHERE exercice = ?', (self.exercice_actuel,))
            nb_ecritures = cursor.fetchone()[0]
            
            cursor.execute('SELECT SUM(montant) FROM ecritures WHERE exercice = ?', (self.exercice_actuel,))
            total_mouvements = cursor.fetchone()[0] or 0
            
            cursor.execute('SELECT COUNT(DISTINCT journal) FROM ecritures WHERE exercice = ?', (self.exercice_actuel,))
            nb_journaux = cursor.fetchone()[0]
            
            # Dernières écritures
            cursor.execute('''
                SELECT date_ecriture, libelle, montant 
                FROM ecritures 
                WHERE exercice = ? 
                ORDER BY timestamp DESC 
                LIMIT 5
            ''', (self.exercice_actuel,))
            
            dernieres_ecritures = cursor.fetchall()
            conn.close()
            
            print(f"📈 STATISTIQUES EXERCICE {self.exercice_actuel}:")
            print(f"   📝 Écritures: {nb_ecritures:,}")
            print(f"   💰 Total mouvements: {total_mouvements:,.2f} €")
            print(f"   📖 Journaux utilisés: {nb_journaux}")
            
            print(f"\n📋 DERNIÈRES ÉCRITURES:")
            for date_ecr, libelle, montant in dernieres_ecritures:
                print(f"   {date_ecr} | {libelle[:30]:<30} | {montant:>10,.2f} €")
            
        except Exception as e:
            print(f"❌ Erreur tableau de bord: {e}")
    
    def _get_comptes_actifs(self):
        """Récupération comptes actifs"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT numero_compte, intitule 
                FROM plan_comptable 
                WHERE actif = 1 
                ORDER BY numero_compte
            ''')
            
            comptes = cursor.fetchall()
            conn.close()
            
            return comptes
            
        except Exception as e:
            print(f"❌ Erreur lecture comptes: {e}")
            return []

def main():
    """Fonction principale"""
    print("💼 MASTER COMPTA GÉNÉRAL")
    print("=" * 30)
    print(f"👤 Opérateur: SamNord@110577")
    print("📊 Système comptable complet")
    print()
    
    compta = MasterComptaGeneral()
    compta.afficher_menu_principal()

if __name__ == "__main__":
    main()
