<!-- Generated by SpecStory -->

# /fix L’importation « QLabel » n’a pas été accédée (2025-06-08 11:24:31)

_**User**_

/fix L’importation « QLabel » n’a pas été accédée

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimer l'import inutile de `QLabel` car il n'est pas utilisé dans le code.

````python
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QFileDialog, QInputDialog
)
# ...existing code...
````

---

_**User**_

/fix L’importation « Qt » n’a pas été accédée

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimer l'import inutile de `Qt` car il n'est pas utilisé dans le code.

````python
from PySide6.QtCore import QTimer
# ...existing code...
````

---

