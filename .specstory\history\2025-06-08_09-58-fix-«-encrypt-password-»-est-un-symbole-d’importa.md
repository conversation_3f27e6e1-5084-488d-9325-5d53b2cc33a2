<!-- Generated by SpecStory -->

# /fix « encrypt_password » est un symbole d’importa (2025-06-08 10:58:31)

_**User**_

/fix « encrypt_password » est un symbole d’importation inconnu, Le type de « encrypt_password » est inconnu

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Le problème vient du fait que le module `encryption` ou les fonctions `encrypt_password` et `verify_password` n'existent pas ou ne sont pas accessibles dans le chemin relatif utilisé.  
Voici les étapes à suivre pour corriger ce problème :

1. **Vérifier l’existence du fichier `encryption.py` dans le dossier `src/auth/`.**
2. **S’assurer que les fonctions `encrypt_password` et `verify_password` sont bien définies dans ce fichier.**
3. **Si le fichier ou les fonctions n’existent pas, les créer.**

Supposons que le fichier `encryption.py` n’existe pas ou ne contient pas ces fonctions.  
Voici un exemple minimal à ajouter dans `src/auth/encryption.py` :

````python
import hashlib

def encrypt_password(password: str) -> str:
    # Simple hash pour l'exemple, à remplacer par une vraie méthode de hachage sécurisé en production
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    return encrypt_password(password) == hashed
````

Ensuite, l’import dans `user_management.py` fonctionnera correctement.  
Aucune modification n’est nécessaire dans le fichier sélectionné si le module et les fonctions existent bien.  
Sinon, créez le fichier ci-dessus.

---

