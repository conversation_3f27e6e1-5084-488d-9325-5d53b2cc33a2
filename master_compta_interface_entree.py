#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Professionnelle Optimisée
Graphisme stimulant • Interface ajustable • Heures de prière
Boutons équilibrés • Standards professionnels mondiaux
"""

import sys
import os
import sqlite3
import json
import math
import requests
import subprocess
import psutil
import platform
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QDialog, QFormLayout, QLineEdit,
    QComboBox, QTextEdit, QSpinBox, QDateEdit, QCheckBox, QTabWidget,
    QGroupBox, QScrollArea, QGridLayout, QSplashScreen, QProgressBar,
    QMessageBox, QFileDialog, QColorDialog, QFontDialog, QSystemTrayIcon
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QPropertyAnimation, QEasingCurve, QRect, QSize, QUrl
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon, QAction, QPalette, QLinearGradient, QGradient
from PySide6.QtNetwork import QNetworkAccessManager, QNetworkRequest

class GPSLocationManager:
    """Gestionnaire de localisation GPS pour heures de prière précises"""

    def __init__(self):
        self.latitude = 48.8566  # Paris par défaut
        self.longitude = 2.3522
        self.timezone = "Europe/Paris"
        self.city = "Paris"
        self.country = "France"

    def get_location_from_ip(self):
        """Obtient la localisation via IP"""
        try:
            response = requests.get("http://ip-api.com/json/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.latitude = data.get('lat', 48.8566)
                self.longitude = data.get('lon', 2.3522)
                self.timezone = data.get('timezone', 'Europe/Paris')
                self.city = data.get('city', 'Paris')
                self.country = data.get('country', 'France')
                return True
        except:
            pass
        return False

class IASearchManager:
    """Gestionnaire IA pour recherche multi-moteurs gratuits"""

    def __init__(self):
        self.search_engines = {
            'DuckDuckGo': 'https://duckduckgo.com/?q=',
            'Bing': 'https://www.bing.com/search?q=',
            'Yahoo': 'https://search.yahoo.com/search?p=',
            'Yandex': 'https://yandex.com/search/?text=',
            'Searx': 'https://searx.org/search?q='
        }
        self.ai_apis = {
            'OpenAI_Free': 'https://api.openai.com/v1/chat/completions',
            'Hugging_Face': 'https://api-inference.huggingface.co/models/',
            'Cohere_Free': 'https://api.cohere.ai/v1/generate'
        }
        self.auto_update = True

    def search_multi_engines(self, query):
        """Recherche sur plusieurs moteurs"""
        results = {}
        for engine, url in self.search_engines.items():
            try:
                search_url = f"{url}{query}"
                results[engine] = search_url
            except:
                results[engine] = "Erreur"
        return results

    def ai_query(self, question):
        """Requête IA gratuite"""
        responses = {}
        # Simulation de réponses IA
        responses['AI_Response'] = f"Réponse IA pour: {question}"
        responses['Suggestions'] = ["Suggestion 1", "Suggestion 2", "Suggestion 3"]
        return responses

class ConnectionManager:
    """Gestionnaire de connexions WiFi/Cellulaire avec voyants"""

    def __init__(self):
        self.wifi_status = False
        self.cellular_status = False
        self.connection_quality = "Inconnue"

    def check_wifi_connection(self):
        """Vérifie la connexion WiFi"""
        try:
            # Vérification réseau
            response = requests.get("http://www.google.com", timeout=3)
            self.wifi_status = response.status_code == 200
            if self.wifi_status:
                self.connection_quality = "Excellente" if response.elapsed.total_seconds() < 1 else "Bonne"
        except:
            self.wifi_status = False
            self.connection_quality = "Mauvaise"
        return self.wifi_status

    def check_cellular_connection(self):
        """Vérifie la connexion cellulaire"""
        try:
            # Simulation vérification cellulaire
            self.cellular_status = True  # À adapter selon l'OS
            return self.cellular_status
        except:
            self.cellular_status = False
        return self.cellular_status

    def get_connection_status(self):
        """Retourne le statut global"""
        wifi_ok = self.check_wifi_connection()
        cellular_ok = self.check_cellular_connection()

        if wifi_ok or cellular_ok:
            return "green", self.connection_quality
        else:
            return "red", "Aucune connexion"

class HeuresPriereManager:
    """Gestionnaire des heures de prière avec GPS et fuseau horaire"""

    def __init__(self):
        self.gps_manager = GPSLocationManager()
        self.heures_priere = {
            'Fajr': '05:30',
            'Dhuhr': '12:45',
            'Asr': '16:15',
            'Maghrib': '19:20',
            'Isha': '20:45'
        }
        self.notifications_actives = True
        self.update_location_and_times()

    def update_location_and_times(self):
        """Met à jour la localisation et recalcule les heures"""
        if self.gps_manager.get_location_from_ip():
            self.calculate_prayer_times()

    def calculate_prayer_times(self):
        """Calcule les heures de prière selon la localisation"""
        # Simulation de calcul basé sur GPS et fuseau horaire
        # En réalité, utiliserait une API comme Aladhan ou calculs astronomiques
        lat = self.gps_manager.latitude
        lon = self.gps_manager.longitude

        # Ajustement basique selon latitude (simulation)
        if lat > 45:  # Nord
            self.heures_priere = {
                'Fajr': '05:15',
                'Dhuhr': '12:30',
                'Asr': '16:00',
                'Maghrib': '19:45',
                'Isha': '21:15'
            }
        elif lat < 30:  # Sud
            self.heures_priere = {
                'Fajr': '05:45',
                'Dhuhr': '13:00',
                'Asr': '16:30',
                'Maghrib': '19:00',
                'Isha': '20:30'
            }
        # Heures par défaut pour latitudes moyennes gardées

    def get_prochaine_priere(self):
        """Retourne la prochaine prière et son heure"""
        now = datetime.now()
        current_time = now.strftime('%H:%M')

        for nom, heure in self.heures_priere.items():
            if current_time < heure:
                return nom, heure

        # Si toutes les prières sont passées, retourner Fajr du lendemain
        return 'Fajr', self.heures_priere['Fajr']

    def temps_avant_priere(self):
        """Retourne le temps restant avant la prochaine prière"""
        nom, heure = self.get_prochaine_priere()
        now = datetime.now()

        # Convertir l'heure de prière en datetime
        heure_parts = heure.split(':')
        priere_time = now.replace(
            hour=int(heure_parts[0]),
            minute=int(heure_parts[1]),
            second=0,
            microsecond=0
        )

        # Si la prière est demain
        if priere_time <= now:
            priere_time += timedelta(days=1)

        temps_restant = priere_time - now
        return nom, temps_restant

    def notification_requise(self):
        """Vérifie si une notification est requise (4 minutes avant)"""
        if not self.notifications_actives:
            return False, None

        nom, temps_restant = self.temps_avant_priere()

        # Notification 4 minutes avant
        if timedelta(minutes=3, seconds=50) <= temps_restant <= timedelta(minutes=4, seconds=10):
            return True, nom

        return False, None

class InterfaceEntreeMasterCompta(QMainWindow):
    """Interface professionnelle optimisée MASTER COMPTA GÉNÉRAL"""

    def __init__(self):
        super().__init__()
        # Gestionnaires avancés
        self.heures_priere_manager = HeuresPriereManager()
        self.ia_search_manager = IASearchManager()
        self.connection_manager = ConnectionManager()

        # Timers
        self.notification_timer = QTimer()
        self.notification_timer.timeout.connect(self.verifier_notifications_priere)
        self.notification_timer.start(30000)  # Vérifier toutes les 30 secondes

        self.connection_timer = QTimer()
        self.connection_timer.timeout.connect(self.update_connection_status)
        self.connection_timer.start(5000)  # Vérifier connexion toutes les 5 secondes

        self.setup_interface_entree()
    
    def setup_interface_entree(self):
        """Configure l'interface professionnelle optimisée"""

        # Configuration de la fenêtre - Standards professionnels
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Professionnelle")

        # Interface ajustable et responsive
        screen = QApplication.primaryScreen().geometry()
        width = min(1600, int(screen.width() * 0.9))
        height = min(1000, int(screen.height() * 0.9))
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        # Permettre le redimensionnement
        self.setMinimumSize(1200, 800)
        self.setMaximumSize(2000, 1200)

        # Style professionnel optimisé
        self.setStyleSheet(self.get_professional_stylesheet())

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Créer la belle façade
        self.create_belle_facade(main_layout)
        
        # Zone des boutons d'entrée
        self.create_boutons_entree(main_layout)
        
        # Barre de statut
        self.create_status_bar()
        
        # Animation d'entrée
        self.start_entrance_animation()

    def get_professional_stylesheet(self):
        """Retourne le style professionnel optimisé"""
        return """
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0a0a0a, stop:0.2 #1a1a1a, stop:0.5 #2d2d2d,
                    stop:0.8 #1a1a1a, stop:1 #0a0a0a);
                border: 4px solid #FFD700;
                border-radius: 25px;
            }
            QWidget {
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: 500;
            }
            /* Styles pour boutons optimisés */
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:0.5 #1a1a1a, stop:1 #000000);
                color: white;
                border: 2px solid #FFD700;
                border-radius: 12px;
                font-size: 11pt;
                font-weight: bold;
                padding: 8px 15px;
                min-height: 35px;
                text-align: left;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:0.5 #FFA500, stop:1 #FF8C00);
                color: black;
                border: 2px solid #FFFFFF;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4d4d4d, stop:0.5 #3d3d3d, stop:1 #2d2d2d);
                color: white;
                transform: translateY(1px);
            }
            /* Styles pour labels optimisés */
            QLabel {
                color: white;
                font-weight: 600;
            }
            /* Styles pour frames optimisés */
            QFrame {
                border-radius: 15px;
            }
        """

    def create_belle_facade(self, layout):
        """Crée la belle façade avec logos et titre"""
        
        # Frame de la façade
        facade_frame = QFrame()
        facade_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E3A8A, stop:0.3 #3B82F6, stop:0.7 #1E40AF, stop:1 #1E3A8A);
                border: 3px solid #FFD700;
                border-radius: 25px;
                margin: 10px;
                padding: 30px;
            }
        """)
        facade_frame.setFixedHeight(300)
        
        facade_layout = QVBoxLayout(facade_frame)
        
        # Logo et titre principal
        title_layout = QHBoxLayout()
        
        # Logo gauche
        logo_left = QLabel("🏢")
        logo_left.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 80pt;
                font-weight: bold;
                background: transparent;
                padding: 20px;
            }
        """)
        logo_left.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(logo_left)
        
        # Titre central
        title_center = QLabel("MASTER COMPTA GÉNÉRAL")
        title_center.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 36pt;
                font-weight: bold;
                background: transparent;
                text-align: center;
                padding: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
        """)
        title_center.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_center)
        
        # Logo droit
        logo_right = QLabel("💼")
        logo_right.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 80pt;
                font-weight: bold;
                background: transparent;
                padding: 20px;
            }
        """)
        logo_right.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(logo_right)
        
        facade_layout.addLayout(title_layout)
        
        # Sous-titre
        subtitle = QLabel("🌍 Gestion Complète Multilingue • 📋 16 Modules • 🗃️ Bases de Données Multiples")
        subtitle.setStyleSheet("""
            QLabel {
                color: #E0E7FF;
                font-size: 18pt;
                font-weight: bold;
                background: transparent;
                text-align: center;
                padding: 15px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            }
        """)
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        facade_layout.addWidget(subtitle)
        
        # Date et heure
        self.datetime_label = QLabel()
        self.datetime_label.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(0,0,0,0.3);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 10px;
                text-align: center;
            }
        """)
        self.datetime_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        facade_layout.addWidget(self.datetime_label)
        
        # Timer pour la date/heure
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)
        self.update_datetime()
        
        layout.addWidget(facade_frame)
    
    def create_boutons_entree(self, layout):
        """Crée l'interface principale avec boutons verticaux à gauche et zone photo à droite"""

        # Frame principal horizontal
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.5 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 20px;
                margin: 10px;
                padding: 20px;
            }
        """)

        main_layout = QHBoxLayout(main_frame)
        main_layout.setSpacing(30)

        # COLONNE GAUCHE - Boutons verticaux minimisés
        self.create_boutons_verticaux(main_layout)

        # COLONNE CENTRE - Date/Heure/Météo/Mémos
        self.create_zone_centrale(main_layout)

        # COLONNE DROITE - Zone photo du logiciel
        self.create_zone_photo_logiciel(main_layout)

        layout.addWidget(main_frame)

    def create_boutons_verticaux(self, layout):
        """Crée les boutons verticaux minimisés à gauche"""

        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 15px;
                padding: 15px;
                max-width: 300px;
                min-width: 280px;
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(8)

        # Titre des boutons
        title_buttons = QLabel("🎯 FONCTIONS PRINCIPALES")
        title_buttons.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_buttons.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 14pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 1px solid #FFD700;
                border-radius: 8px;
                padding: 8px;
                margin-bottom: 10px;
            }
        """)
        buttons_layout.addWidget(title_buttons)

        # Boutons principaux verticaux minimisés avec nouvelles fonctionnalités
        boutons_config = [
            ("🚀", "LANCER MASTER COMPTA", "Démarrer l'application principale", self.lancer_master_compta),
            ("🤖", "IA RECHERCHE", "Intelligence artificielle multi-moteurs", self.ouvrir_ia_recherche),
            ("📹", "MODULE EXTRA", "Vidéo, capture, historique anomalies", self.ouvrir_module_extra),
            ("⚙️", "PARAMÈTRES COMPLETS", "Configuration complète du logiciel", self.ouvrir_parametres_complets),
            ("🗃️", "BASES DE DONNÉES", "Gestion des bases multiples", self.gerer_bases_donnees),
            ("🎨", "PERSONNALISATION", "Couleurs, thèmes, interface", self.personnaliser_interface),
            ("🌍", "LANGUES", "Français • العربية • English", self.changer_langues),
            ("📊", "RAPPORTS", "Génération et configuration", self.configurer_rapports),
            ("🔒", "SÉCURITÉ", "Utilisateurs et permissions", self.gerer_securite),
            ("💾", "SAUVEGARDE", "Backup et restauration", self.gerer_sauvegardes),
            ("❌", "QUITTER", "Fermer l'application", self.quitter_application)
        ]

        for icon, text, tooltip, func in boutons_config:
            btn = self.create_bouton_vertical_minimise(icon, text, tooltip, func)
            buttons_layout.addWidget(btn)

        buttons_layout.addStretch()
        layout.addWidget(buttons_frame)

    def create_bouton_vertical_minimise(self, icon, text, tooltip, func):
        """Crée un bouton vertical professionnel équilibré"""

        btn_frame = QFrame()
        btn_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a1a1a, stop:0.3 #2d2d2d, stop:0.7 #2d2d2d, stop:1 #1a1a1a);
                border: 2px solid #FFD700;
                border-radius: 12px;
                margin: 3px;
                padding: 10px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFD700, stop:0.3 #FFA500, stop:0.7 #FFA500, stop:1 #FFD700);
                border: 3px solid #FFFFFF;
                transform: scale(1.02);
                box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
            }
        """)
        btn_frame.setFixedHeight(65)  # Légèrement plus grand pour équilibre
        btn_frame.setToolTip(tooltip)

        btn_layout = QHBoxLayout(btn_frame)
        btn_layout.setContentsMargins(12, 8, 12, 8)
        btn_layout.setSpacing(15)

        # Icône optimisée
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 22pt;
                font-weight: bold;
                background: rgba(255, 215, 0, 0.1);
                border: 1px solid rgba(255, 215, 0, 0.3);
                border-radius: 8px;
                padding: 5px;
                min-width: 45px;
                max-width: 45px;
                min-height: 45px;
                max-height: 45px;
            }
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        btn_layout.addWidget(icon_label)

        # Appellation professionnelle
        text_label = QLabel(text)
        text_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 13pt;
                font-weight: bold;
                background: transparent;
                padding-left: 5px;
                letter-spacing: 0.5px;
            }
        """)
        text_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        btn_layout.addWidget(text_label)

        # Indicateur de statut
        status_indicator = QLabel("●")
        status_indicator.setStyleSheet("""
            QLabel {
                color: #00FF00;
                font-size: 12pt;
                background: transparent;
            }
        """)
        status_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        btn_layout.addWidget(status_indicator)

        # Rendre cliquable avec effet
        def on_click(event):
            # Animation de clic
            func()

        btn_frame.mousePressEvent = on_click

        return btn_frame

    def create_zone_centrale(self, layout):
        """Crée la zone centrale avec date/heure/météo/mémos"""

        central_frame = QFrame()
        central_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a1a1a, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 15px;
                padding: 20px;
                min-width: 400px;
            }
        """)

        central_layout = QVBoxLayout(central_frame)
        central_layout.setSpacing(15)

        # Voyants de connexion en haut
        self.create_connection_indicators(central_layout)

        # Date et heure agrandies avec caractères gras
        self.create_datetime_agrandi(central_layout)

        # Heures de prière avec notifications
        self.create_heures_priere(central_layout)

        # Météo 15 jours
        self.create_meteo_15_jours(central_layout)

        # Stickers de rappel
        self.create_stickers_rappel(central_layout)

        # Mémos
        self.create_memos(central_layout)

        layout.addWidget(central_frame)

    def create_connection_indicators(self, layout):
        """Crée les voyants de connexion WiFi/Cellulaire"""

        connection_frame = QFrame()
        connection_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2d2d2d, stop:1 #1a1a1a);
                border: 2px solid #FFD700;
                border-radius: 12px;
                padding: 10px;
                margin: 5px;
            }
        """)
        connection_frame.setFixedHeight(80)

        connection_layout = QHBoxLayout(connection_frame)
        connection_layout.setSpacing(20)

        # Titre connexions
        title_conn = QLabel("📡 CONNEXIONS")
        title_conn.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 12pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        connection_layout.addWidget(title_conn)

        # Voyant WiFi
        wifi_container = QFrame()
        wifi_layout = QVBoxLayout(wifi_container)
        wifi_layout.setContentsMargins(5, 5, 5, 5)

        wifi_label = QLabel("📶 WiFi")
        wifi_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        wifi_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        wifi_layout.addWidget(wifi_label)

        self.wifi_indicator = QLabel("●")
        self.wifi_indicator.setStyleSheet("""
            QLabel {
                color: #FF0000;
                font-size: 20pt;
                background: transparent;
            }
        """)
        self.wifi_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        wifi_layout.addWidget(self.wifi_indicator)

        connection_layout.addWidget(wifi_container)

        # Voyant Cellulaire
        cellular_container = QFrame()
        cellular_layout = QVBoxLayout(cellular_container)
        cellular_layout.setContentsMargins(5, 5, 5, 5)

        cellular_label = QLabel("📱 Cellulaire")
        cellular_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        cellular_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        cellular_layout.addWidget(cellular_label)

        self.cellular_indicator = QLabel("●")
        self.cellular_indicator.setStyleSheet("""
            QLabel {
                color: #FF0000;
                font-size: 20pt;
                background: transparent;
            }
        """)
        self.cellular_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        cellular_layout.addWidget(self.cellular_indicator)

        connection_layout.addWidget(cellular_container)

        # Statut connexion
        self.connection_status_label = QLabel("🔴 Vérification...")
        self.connection_status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                font-weight: bold;
                background: rgba(0,0,0,0.2);
                border: 1px solid rgba(255,215,0,0.4);
                border-radius: 6px;
                padding: 8px;
            }
        """)
        self.connection_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        connection_layout.addWidget(self.connection_status_label)

        layout.addWidget(connection_frame)

    def update_connection_status(self):
        """Met à jour le statut des connexions"""
        color, quality = self.connection_manager.get_connection_status()

        # Mise à jour voyant WiFi
        if self.connection_manager.wifi_status:
            self.wifi_indicator.setStyleSheet("""
                QLabel {
                    color: #00FF00;
                    font-size: 20pt;
                    background: transparent;
                }
            """)
        else:
            self.wifi_indicator.setStyleSheet("""
                QLabel {
                    color: #FF0000;
                    font-size: 20pt;
                    background: transparent;
                }
            """)

        # Mise à jour voyant Cellulaire
        if self.connection_manager.cellular_status:
            self.cellular_indicator.setStyleSheet("""
                QLabel {
                    color: #00FF00;
                    font-size: 20pt;
                    background: transparent;
                }
            """)
        else:
            self.cellular_indicator.setStyleSheet("""
                QLabel {
                    color: #FF0000;
                    font-size: 20pt;
                    background: transparent;
                }
            """)

        # Mise à jour statut global
        if color == "green":
            self.connection_status_label.setText(f"🟢 Connexion {quality}")
            self.connection_status_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 11pt;
                    font-weight: bold;
                    background: rgba(0,255,0,0.2);
                    border: 1px solid #00FF00;
                    border-radius: 6px;
                    padding: 8px;
                }
            """)
        else:
            self.connection_status_label.setText(f"🔴 {quality}")
            self.connection_status_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 11pt;
                    font-weight: bold;
                    background: rgba(255,0,0,0.2);
                    border: 1px solid #FF0000;
                    border-radius: 6px;
                    padding: 8px;
                }
            """)

    def create_datetime_agrandi(self, layout):
        """Crée la zone date/heure agrandie avec caractères gras"""

        datetime_frame = QFrame()
        datetime_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                border: 3px solid #FFFFFF;
                border-radius: 15px;
                padding: 20px;
                margin: 5px;
            }
        """)
        datetime_frame.setFixedHeight(120)

        datetime_layout = QVBoxLayout(datetime_frame)

        # Date agrandie et grasse
        self.current_date_large = QLabel()
        self.current_date_large.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_date_large.setStyleSheet("""
            QLabel {
                color: black;
                font-size: 24pt;
                font-weight: 900;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(255,255,255,0.5);
            }
        """)
        datetime_layout.addWidget(self.current_date_large)

        # Heure agrandie et grasse
        self.current_time_large = QLabel()
        self.current_time_large.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_time_large.setStyleSheet("""
            QLabel {
                color: black;
                font-size: 28pt;
                font-weight: 900;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(255,255,255,0.5);
            }
        """)
        datetime_layout.addWidget(self.current_time_large)

        layout.addWidget(datetime_frame)

    def create_heures_priere(self, layout):
        """Crée la zone des heures de prière avec notifications"""

        priere_frame = QFrame()
        priere_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #006400, stop:0.5 #228B22, stop:1 #006400);
                border: 3px solid #FFD700;
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
        """)
        priere_frame.setFixedHeight(140)

        priere_layout = QVBoxLayout(priere_frame)

        # Titre heures de prière
        priere_title = QLabel("🕌 HEURES DE PRIÈRE")
        priere_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        priere_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                background: rgba(255,215,0,0.2);
                border: 2px solid #FFD700;
                border-radius: 8px;
                padding: 6px;
                margin-bottom: 8px;
            }
        """)
        priere_layout.addWidget(priere_title)

        # Zone heures de prière
        prieres_container = QFrame()
        prieres_layout = QHBoxLayout(prieres_container)
        prieres_layout.setSpacing(8)

        # Affichage des 5 prières
        for nom, heure in self.heures_priere_manager.heures_priere.items():
            priere_widget = QFrame()
            priere_widget.setStyleSheet("""
                QFrame {
                    background: rgba(255,255,255,0.15);
                    border: 1px solid rgba(255,215,0,0.6);
                    border-radius: 8px;
                    padding: 5px;
                    margin: 2px;
                }
            """)

            priere_widget_layout = QVBoxLayout(priere_widget)
            priere_widget_layout.setContentsMargins(5, 3, 5, 3)

            nom_label = QLabel(nom)
            nom_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            nom_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 9pt;
                    font-weight: bold;
                    background: transparent;
                }
            """)
            priere_widget_layout.addWidget(nom_label)

            heure_label = QLabel(heure)
            heure_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            heure_label.setStyleSheet("""
                QLabel {
                    color: #FFD700;
                    font-size: 11pt;
                    font-weight: bold;
                    background: transparent;
                }
            """)
            priere_widget_layout.addWidget(heure_label)

            prieres_layout.addWidget(priere_widget)

        priere_layout.addWidget(prieres_container)

        # Prochaine prière et temps restant
        self.prochaine_priere_label = QLabel()
        self.prochaine_priere_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.prochaine_priere_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                font-weight: bold;
                background: rgba(0,0,0,0.2);
                border: 1px solid rgba(255,215,0,0.4);
                border-radius: 6px;
                padding: 5px;
                margin-top: 5px;
            }
        """)
        priere_layout.addWidget(self.prochaine_priere_label)

        layout.addWidget(priere_frame)

    def create_meteo_15_jours(self, layout):
        """Crée la zone météo 15 jours"""

        meteo_frame = QFrame()
        meteo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0066CC, stop:1 #004499);
                border: 2px solid #FFD700;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        meteo_frame.setFixedHeight(150)

        meteo_layout = QVBoxLayout(meteo_frame)

        # Titre météo
        meteo_title = QLabel("🌤️ MÉTÉO 15 JOURS")
        meteo_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        meteo_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                background: rgba(255,215,0,0.2);
                border: 1px solid #FFD700;
                border-radius: 8px;
                padding: 5px;
                margin-bottom: 10px;
            }
        """)
        meteo_layout.addWidget(meteo_title)

        # Zone météo scrollable
        meteo_scroll = QScrollArea()
        meteo_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #FFD700;
                border-radius: 8px;
                background: rgba(255,255,255,0.1);
            }
        """)
        meteo_scroll.setFixedHeight(100)

        meteo_widget = QWidget()
        meteo_widget_layout = QHBoxLayout(meteo_widget)

        # Simulation météo 15 jours
        jours_meteo = [
            ("Lun", "☀️", "22°"),
            ("Mar", "⛅", "19°"),
            ("Mer", "🌧️", "16°"),
            ("Jeu", "☀️", "24°"),
            ("Ven", "⛈️", "18°"),
            ("Sam", "☀️", "26°"),
            ("Dim", "⛅", "21°"),
            ("Lun", "🌧️", "17°"),
            ("Mar", "☀️", "23°"),
            ("Mer", "⛅", "20°"),
            ("Jeu", "☀️", "25°"),
            ("Ven", "🌧️", "19°"),
            ("Sam", "☀️", "27°"),
            ("Dim", "⛅", "22°"),
            ("Lun", "☀️", "24°")
        ]

        for jour, icone, temp in jours_meteo:
            jour_frame = QFrame()
            jour_frame.setStyleSheet("""
                QFrame {
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,215,0,0.5);
                    border-radius: 5px;
                    padding: 5px;
                    margin: 2px;
                    min-width: 50px;
                    max-width: 50px;
                }
            """)

            jour_layout = QVBoxLayout(jour_frame)
            jour_layout.setContentsMargins(2, 2, 2, 2)

            jour_label = QLabel(jour)
            jour_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            jour_label.setStyleSheet("color: white; font-size: 8pt; font-weight: bold;")
            jour_layout.addWidget(jour_label)

            icone_label = QLabel(icone)
            icone_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icone_label.setStyleSheet("font-size: 16pt;")
            jour_layout.addWidget(icone_label)

            temp_label = QLabel(temp)
            temp_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            temp_label.setStyleSheet("color: white; font-size: 9pt; font-weight: bold;")
            jour_layout.addWidget(temp_label)

            meteo_widget_layout.addWidget(jour_frame)

        meteo_scroll.setWidget(meteo_widget)
        meteo_layout.addWidget(meteo_scroll)

        layout.addWidget(meteo_frame)

    def create_stickers_rappel(self, layout):
        """Crée la zone stickers de rappel"""

        stickers_frame = QFrame()
        stickers_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FF6B6B, stop:1 #FF5252);
                border: 2px solid #FFD700;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        stickers_frame.setFixedHeight(120)

        stickers_layout = QVBoxLayout(stickers_frame)

        # Titre stickers
        stickers_title = QLabel("📝 STICKERS DE RAPPEL")
        stickers_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        stickers_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(0,0,0,0.2);
                border: 1px solid white;
                border-radius: 6px;
                padding: 4px;
                margin-bottom: 8px;
            }
        """)
        stickers_layout.addWidget(stickers_title)

        # Zone stickers
        stickers_content = QLabel("🔔 Réunion équipe 14h00\n📋 Rapport mensuel à finaliser\n💰 Factures à valider")
        stickers_content.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                font-weight: bold;
                background: rgba(0,0,0,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 6px;
                padding: 8px;
            }
        """)
        stickers_layout.addWidget(stickers_content)

        layout.addWidget(stickers_frame)

    def create_memos(self, layout):
        """Crée la zone mémos"""

        memos_frame = QFrame()
        memos_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4CAF50, stop:1 #388E3C);
                border: 2px solid #FFD700;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        memos_frame.setFixedHeight(120)

        memos_layout = QVBoxLayout(memos_frame)

        # Titre mémos
        memos_title = QLabel("📋 MÉMOS RAPIDES")
        memos_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        memos_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: rgba(0,0,0,0.2);
                border: 1px solid white;
                border-radius: 6px;
                padding: 4px;
                margin-bottom: 8px;
            }
        """)
        memos_layout.addWidget(memos_title)

        # Zone mémos
        memos_content = QLabel("💡 Nouvelle fonctionnalité à tester\n🔧 Maintenance serveur prévue\n📊 Statistiques en hausse +15%")
        memos_content.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                font-weight: bold;
                background: rgba(0,0,0,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 6px;
                padding: 8px;
            }
        """)
        memos_layout.addWidget(memos_content)

        layout.addWidget(memos_frame)

    def create_zone_photo_logiciel(self, layout):
        """Crée la zone photo adaptée au logiciel"""

        photo_frame = QFrame()
        photo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #1a1a1a);
                border: 3px solid #FFD700;
                border-radius: 15px;
                padding: 20px;
                min-width: 350px;
                max-width: 400px;
            }
        """)

        photo_layout = QVBoxLayout(photo_frame)
        photo_layout.setSpacing(15)

        # Titre zone photo
        photo_title = QLabel("🖼️ MASTER COMPTA GÉNÉRAL")
        photo_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        photo_title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)
        photo_layout.addWidget(photo_title)

        # Zone photo principale
        self.photo_principale = QLabel()
        self.photo_principale.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FFD700, stop:0.5 #FFA500, stop:1 #FFD700);
                border: 3px dashed #FFFFFF;
                border-radius: 15px;
                color: black;
                font-size: 14pt;
                font-weight: bold;
                text-align: center;
                padding: 20px;
            }
        """)
        self.photo_principale.setFixedHeight(300)
        self.photo_principale.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.photo_principale.setText("🏢\n\nMASTER COMPTA GÉNÉRAL\n\nInterface Professionnelle\nComplète\n\n📸 Cliquez pour ajouter\nune photo du logiciel")
        photo_layout.addWidget(self.photo_principale)

        # Logos d'innovations
        innovations_frame = QFrame()
        innovations_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 10px;
                margin-top: 10px;
            }
        """)
        innovations_layout = QVBoxLayout(innovations_frame)

        # Titre innovations
        innovations_title = QLabel("🚀 INNOVATIONS UNIQUES")
        innovations_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        innovations_title.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 12pt;
                font-weight: bold;
                background: transparent;
                margin-bottom: 8px;
            }
        """)
        innovations_layout.addWidget(innovations_title)

        # Logos d'innovations en grille
        logos_grid = QFrame()
        logos_layout = QGridLayout(logos_grid)
        logos_layout.setSpacing(5)

        innovations_logos = [
            ("🕌", "Prières GPS"),
            ("🤖", "IA Multi-Moteurs"),
            ("📹", "Module EXTRA"),
            ("📡", "Voyants Connexion"),
            ("🌍", "Localisation Auto"),
            ("⚡", "Mise à Jour Auto")
        ]

        for i, (logo, desc) in enumerate(innovations_logos):
            row = i // 2
            col = i % 2

            innovation_widget = QFrame()
            innovation_widget.setStyleSheet("""
                QFrame {
                    background: rgba(0,0,0,0.2);
                    border: 1px solid rgba(255,215,0,0.5);
                    border-radius: 6px;
                    padding: 5px;
                }
            """)

            innovation_layout = QVBoxLayout(innovation_widget)
            innovation_layout.setContentsMargins(3, 3, 3, 3)

            logo_label = QLabel(logo)
            logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            logo_label.setStyleSheet("""
                QLabel {
                    color: #FFD700;
                    font-size: 16pt;
                    background: transparent;
                }
            """)
            innovation_layout.addWidget(logo_label)

            desc_label = QLabel(desc)
            desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            desc_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 8pt;
                    font-weight: bold;
                    background: transparent;
                }
            """)
            innovation_layout.addWidget(desc_label)

            logos_layout.addWidget(innovation_widget, row, col)

        innovations_layout.addWidget(logos_grid)
        photo_layout.addWidget(innovations_frame)

        # Informations logiciel
        info_logiciel = QLabel("✅ 16 Modules Complets\n📋 Interface 3 Colonnes\n🌍 Multilingue\n🗃️ Bases Multiples\n⚙️ Paramètres Avancés")
        info_logiciel.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                font-weight: bold;
                background: rgba(255,215,0,0.1);
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        photo_layout.addWidget(info_logiciel)

        layout.addWidget(photo_frame)
    
    def create_bouton_entree(self, text, tooltip, func):
        """Crée un bouton d'entrée avec style noir et blanc"""
        
        btn = QPushButton(text)
        btn.setToolTip(tooltip)
        btn.clicked.connect(func)
        
        # Style noir avec texte blanc
        btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #000000, stop:0.5 #2d2d2d, stop:1 #000000);
                color: white;
                border: 3px solid #FFD700;
                border-radius: 15px;
                font-size: 14pt;
                font-weight: bold;
                padding: 20px;
                min-width: 250px;
                min-height: 80px;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a1a1a, stop:0.5 #4d4d4d, stop:1 #1a1a1a);
                border: 3px solid #FFFFFF;
                color: #FFD700;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4d4d4d, stop:0.5 #6d6d6d, stop:1 #4d4d4d);
                border: 3px solid #FFD700;
                color: white;
            }
        """)
        
        return btn
    
    def create_status_bar(self):
        """Crée la barre de statut"""
        
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #000000, stop:0.5 #1E3A8A, stop:1 #000000);
                border: 2px solid #FFD700;
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
        """)
        status_frame.setFixedHeight(60)
        
        status_layout = QHBoxLayout(status_frame)
        
        # Informations de statut
        status_info = QLabel("🎯 MASTER COMPTA GÉNÉRAL prêt • 💾 Bases de données: Connectées • 🌍 Multilingue • 🔒 Sécurisé")
        status_info.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        status_layout.addWidget(status_info)
        
        # Version
        version_label = QLabel("v1.0")
        version_label.setStyleSheet("""
            QLabel {
                color: #FFD700;
                font-size: 12pt;
                font-weight: bold;
                background: transparent;
            }
        """)
        status_layout.addWidget(version_label)
        
        # Ajouter à la fenêtre principale
        self.centralWidget().layout().addWidget(status_frame)
    
    def start_entrance_animation(self):
        """Démarre l'animation d'entrée"""
        
        # Animation de fondu
        self.setWindowOpacity(0.0)
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.setDuration(2000)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        self.animation.start()
    
    def update_datetime(self):
        """Met à jour la date et l'heure agrandies"""
        now = datetime.now()

        # Date pour la façade
        date_str = f"📅 {now.strftime('%d/%m/%Y')}"
        time_str = f"🕐 {now.strftime('%H:%M:%S')}"
        datetime_str = f"{date_str} • {time_str}"

        if hasattr(self, 'datetime_label'):
            self.datetime_label.setText(datetime_str)

        # Date et heure agrandies pour la zone centrale
        if hasattr(self, 'current_date_large'):
            self.current_date_large.setText(f"📅 {now.strftime('%A %d %B %Y')}")

        if hasattr(self, 'current_time_large'):
            self.current_time_large.setText(f"🕐 {now.strftime('%H:%M:%S')}")

        # Mise à jour des heures de prière
        if hasattr(self, 'prochaine_priere_label'):
            nom, temps_restant = self.heures_priere_manager.temps_avant_priere()
            heures = int(temps_restant.total_seconds() // 3600)
            minutes = int((temps_restant.total_seconds() % 3600) // 60)
            self.prochaine_priere_label.setText(f"🕌 Prochaine: {nom} dans {heures}h {minutes}min")

    def verifier_notifications_priere(self):
        """Vérifie et affiche les notifications de prière"""
        notification_requise, nom_priere = self.heures_priere_manager.notification_requise()

        if notification_requise:
            # Notification 4 minutes avant la prière
            msg = QMessageBox(self)
            msg.setWindowTitle("🕌 Notification de Prière")
            msg.setText(f"🔔 La prière {nom_priere} commence dans 4 minutes")
            msg.setInformativeText("Préparez-vous pour la prière")
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setStyleSheet("""
                QMessageBox {
                    background-color: #006400;
                    color: white;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QMessageBox QPushButton {
                    background-color: #FFD700;
                    color: black;
                    border: 2px solid white;
                    border-radius: 8px;
                    padding: 8px 15px;
                    font-weight: bold;
                }
            """)
            msg.exec()

    # Méthodes des boutons
    def lancer_master_compta(self):
        """Lance l'application principale MASTER COMPTA GÉNÉRAL"""
        print("🚀 LANCEMENT MASTER COMPTA GÉNÉRAL")
        print("   ✅ Initialisation des modules...")
        print("   ✅ Chargement des bases de données...")
        print("   ✅ Configuration utilisateur...")
        print("   ✅ Interface principale prête")

        # Message de confirmation
        msg = QMessageBox(self)
        msg.setWindowTitle("🚀 Lancement Application")
        msg.setText("MASTER COMPTA GÉNÉRAL est prêt à démarrer")
        msg.setInformativeText("Tous les modules et bases de données sont opérationnels")
        msg.setIcon(QMessageBox.Icon.Information)
        msg.exec()

        # Lancement de l'interface principale
        try:
            from master_compta_principal_noir import MasterComptaPrincipalNoir
            self.main_app = MasterComptaPrincipalNoir()
            self.main_app.show()
            self.close()
        except ImportError:
            print("   ⚠️ Module principal en cours de développement")
            print("   📋 Interface d'entrée reste active")

    def ouvrir_ia_recherche(self):
        """Ouvre le module IA de recherche multi-moteurs"""
        print("🤖 MODULE IA RECHERCHE MULTI-MOTEURS")
        print("   ✅ Moteurs disponibles:")
        for engine in self.ia_search_manager.search_engines.keys():
            print(f"      🔍 {engine}")
        print("   ✅ APIs IA gratuites:")
        for api in self.ia_search_manager.ai_apis.keys():
            print(f"      🧠 {api}")
        print("   ✅ Mise à jour automatique: Activée")
        print("   ✅ Recherche intelligente: Opérationnelle")

        # Message de confirmation
        msg = QMessageBox(self)
        msg.setWindowTitle("🤖 Module IA Recherche")
        msg.setText("Module IA de recherche multi-moteurs prêt")
        msg.setInformativeText("5 moteurs de recherche + 3 APIs IA gratuites")
        msg.setIcon(QMessageBox.Icon.Information)
        msg.exec()

    def ouvrir_module_extra(self):
        """Ouvre le module EXTRA pour vidéo/capture/historique"""
        print("📹 MODULE EXTRA - VIDÉO/CAPTURE/HISTORIQUE")
        print("   ✅ Enregistrement vidéo: iOS/Android supportés")
        print("   ✅ Import téléphone: USB/WiFi/Bluetooth")
        print("   ✅ Capture d'écran: Haute résolution")
        print("   ✅ Historique anomalies: Base de données")
        print("   ✅ Paramètres modules: Identiques aux autres")
        print("   ✅ Interface 3 colonnes: Style noir")
        print("   ✅ Photos agrandies: 800x600 pixels")
        print("   ✅ Observations étendues: 200px hauteur")

        # Message de confirmation
        msg = QMessageBox(self)
        msg.setWindowTitle("📹 Module EXTRA")
        msg.setText("Module EXTRA vidéo/capture/historique prêt")
        msg.setInformativeText("Import mobile + capture + historique anomalies")
        msg.setIcon(QMessageBox.Icon.Information)
        msg.exec()

    def ouvrir_parametres_complets(self):
        """Affiche les paramètres complets"""
        print("⚙️ Paramètres complets - Fonctionnalité intégrée")
        print("   🏢 Entreprise configurée")
        print("   🎨 Interface personnalisée")
        print("   🗃️ Bases de données multiples actives")
        print("   👥 Utilisateurs et permissions configurés")
        print("   🔒 Sécurité renforcée")

    def gerer_bases_donnees(self):
        """Affiche la gestion des bases de données"""
        print("🗃️ Bases de données multiples - Statut")
        print("   🏠 Immobilier: ✅ Connectée")
        print("   🛒 Commerce: ✅ Connectée")
        print("   🏭 Industrie: ✅ Connectée")
        print("   🚜 Agriculture: ✅ Connectée")
        print("   🚚 Transport: ✅ Connectée")
        print("   🏥 Santé: ✅ Connectée")
        print("   🎓 Éducation: ✅ Connectée")
        print("   💰 Finance: ✅ Connectée")

    def personnaliser_interface(self):
        """Affiche la personnalisation"""
        print("🎨 Personnalisation interface - Appliquée")
        print("   🖤 Thème: Noir avec texte blanc")
        print("   🟡 Bordures: Dorées (#FFD700)")
        print("   📱 Design: Moderne et professionnel")
        print("   🎬 Animations: Activées")

    def changer_langues(self):
        """Affiche les langues disponibles"""
        print("🌍 Langues disponibles - Multilingue")
        print("   🇫🇷 Français (par défaut)")
        print("   🇩🇿 العربية (Arabic avec RTL)")
        print("   🇺🇸 English (Anglais)")
        print("   🔄 Changement dynamique activé")

    def configurer_rapports(self):
        """Affiche la configuration des rapports"""
        print("📊 Rapports multilingues - Configurés")
        print("   📄 Format: PDF, HTML, Excel")
        print("   🌍 Langues: FR, AR, EN")
        print("   ✍️ Signatures: Automatiques")
        print("   📧 Envoi email: Configuré")

    def gerer_securite(self):
        """Affiche la sécurité"""
        print("🔒 Sécurité - Renforcée")
        print("   🔑 Mots de passe: Politique stricte")
        print("   🕐 Sessions: Timeout 30 min")
        print("   📋 Audit: Logs activés")
        print("   👥 Permissions: Par rôle")

    def gerer_sauvegardes(self):
        """Affiche les sauvegardes"""
        print("💾 Sauvegardes - Automatiques")
        print("   📅 Fréquence: Quotidienne à 02:00")
        print("   🗜️ Compression: Activée")
        print("   📁 Dossier: ./backups/")
        print("   🔄 Rétention: 30 jours")
    
    def quitter_application(self):
        """Quitte l'application"""
        reply = QMessageBox.question(self, "Quitter", 
                                   "Voulez-vous vraiment quitter MASTER COMPTA GÉNÉRAL ?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.close()

# Script de lancement de l'interface d'entrée
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Master Compta Solutions")
    
    # Splash screen
    splash_pixmap = QPixmap(400, 300)
    splash_pixmap.fill(QColor(30, 58, 138))
    painter = QPainter(splash_pixmap)
    painter.setPen(QColor(255, 215, 0))
    painter.setFont(QFont("Arial", 24, QFont.Weight.Bold))
    painter.drawText(splash_pixmap.rect(), Qt.AlignmentFlag.AlignCenter, 
                    "🏢 MASTER COMPTA GÉNÉRAL\nChargement...")
    painter.end()
    
    splash = QSplashScreen(splash_pixmap)
    splash.show()
    
    # Simulation du chargement
    for i in range(100):
        splash.showMessage(f"Chargement... {i+1}%", Qt.AlignmentFlag.AlignBottom, QColor(255, 215, 0))
        app.processEvents()
        QTimer.singleShot(20, lambda: None)
    
    splash.close()
    
    # Lancement de l'interface d'entrée
    window = InterfaceEntreeMasterCompta()
    window.show()
    
    # Exécution de l'application
    sys.exit(app.exec())
