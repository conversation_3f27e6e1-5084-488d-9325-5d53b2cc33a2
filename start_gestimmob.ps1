# Script PowerShell pour démarrer GestImmob
# Exécution: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
# Puis: .\start_gestimmob.ps1

param(
    [switch]$Debug,
    [switch]$NoUpdate,
    [switch]$Clean,
    [string]$Environment = "production"
)

# Configuration
$AppName = "GestImmob"
$AppVersion = "2.0.0"
$MinPythonVersion = [Version]"3.8.0"

# Couleurs pour l'affichage
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Header {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    $AppName v$AppVersion" -ForegroundColor Green
    Write-Host "    Logiciel de Gestion Immobilière" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

function Test-PythonInstallation {
    Write-Host "Vérification de Python..." -ForegroundColor Yellow
    
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Python non trouvé"
        }
        
        $versionString = $pythonVersion -replace "Python ", ""
        $currentVersion = [Version]$versionString
        
        if ($currentVersion -lt $MinPythonVersion) {
            throw "Version Python $currentVersion trop ancienne (minimum: $MinPythonVersion)"
        }
        
        Write-Host "✓ Python $currentVersion détecté" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Erreur Python: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Solutions:" -ForegroundColor Yellow
        Write-Host "1. Installez Python $MinPythonVersion+ depuis https://python.org" -ForegroundColor White
        Write-Host "2. Ajoutez Python au PATH système" -ForegroundColor White
        Write-Host "3. Redémarrez PowerShell après installation" -ForegroundColor White
        return $false
    }
}

function Install-Dependencies {
    if ($NoUpdate) {
        Write-Host "Mise à jour des dépendances ignorée (--NoUpdate)" -ForegroundColor Yellow
        return $true
    }
    
    if (-not (Test-Path "requirements.txt")) {
        Write-Host "⚠ Fichier requirements.txt non trouvé" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "Installation/mise à jour des dépendances..." -ForegroundColor Yellow
    
    try {
        $output = pip install -r requirements.txt --upgrade --quiet 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠ Certaines dépendances n'ont pas pu être installées" -ForegroundColor Yellow
            Write-Host "Sortie pip: $output" -ForegroundColor Gray
        } else {
            Write-Host "✓ Dépendances installées avec succès" -ForegroundColor Green
        }
        return $true
    }
    catch {
        Write-Host "✗ Erreur lors de l'installation des dépendances: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Initialize-Database {
    Write-Host "Initialisation de la base de données..." -ForegroundColor Yellow
    
    if (-not (Test-Path "immobilisations.db")) {
        Write-Host "Création de la base de données..." -ForegroundColor Cyan
        try {
            python -c "import sqlite3; sqlite3.connect('immobilisations.db').close()"
            Write-Host "✓ Base de données créée" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ Erreur lors de la création de la base de données" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "✓ Base de données existante détectée" -ForegroundColor Green
    }
    
    return $true
}

function Initialize-Directories {
    Write-Host "Création des répertoires nécessaires..." -ForegroundColor Yellow
    
    $directories = @("logs", "backups", "data", "temp", "exports", "imports")
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "✓ Répertoire créé: $dir" -ForegroundColor Green
        }
    }
    
    return $true
}

function Clean-TempFiles {
    if (-not $Clean) {
        return
    }
    
    Write-Host "Nettoyage des fichiers temporaires..." -ForegroundColor Yellow
    
    $tempPaths = @("temp", "__pycache__", "*.pyc", "*.pyo", "*.log")
    
    foreach ($path in $tempPaths) {
        if (Test-Path $path) {
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "✓ Nettoyé: $path" -ForegroundColor Green
        }
    }
}

function Start-Application {
    Write-Host ""
    Write-Host "Démarrage de $AppName..." -ForegroundColor Cyan
    Write-Host ""
    
    $env:GESTIMMOB_ENVIRONMENT = $Environment
    if ($Debug) {
        $env:GESTIMMOB_DEBUG = "1"
        Write-Host "Mode debug activé" -ForegroundColor Yellow
    }
    
    try {
        python src/main.py
        $exitCode = $LASTEXITCODE
        
        Write-Host ""
        if ($exitCode -eq 0) {
            Write-Host "$AppName fermé normalement" -ForegroundColor Green
        } else {
            Write-Host "⚠ $AppName fermé avec le code d'erreur: $exitCode" -ForegroundColor Yellow
            Show-ErrorHelp
        }
        
        return $exitCode
    }
    catch {
        Write-Host "✗ Erreur lors du démarrage: $($_.Exception.Message)" -ForegroundColor Red
        Show-ErrorHelp
        return 1
    }
}

function Show-ErrorHelp {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "AIDE AU DÉPANNAGE" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions possibles:" -ForegroundColor Yellow
    Write-Host "1. Réinstallez les dépendances:" -ForegroundColor White
    Write-Host "   pip install -r requirements.txt --force-reinstall" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Nettoyez et redémarrez:" -ForegroundColor White
    Write-Host "   .\start_gestimmob.ps1 -Clean" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Mode debug pour plus d'informations:" -ForegroundColor White
    Write-Host "   .\start_gestimmob.ps1 -Debug" -ForegroundColor Gray
    Write-Host ""
    Write-Host "4. Vérifiez les logs dans le dossier 'logs'" -ForegroundColor White
    Write-Host ""
    Write-Host "5. Contactez le support: <EMAIL>" -ForegroundColor White
    Write-Host ""
}

function Show-Help {
    Write-Host "Usage: .\start_gestimmob.ps1 [OPTIONS]" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Debug          Active le mode debug" -ForegroundColor White
    Write-Host "  -NoUpdate       Ignore la mise à jour des dépendances" -ForegroundColor White
    Write-Host "  -Clean          Nettoie les fichiers temporaires avant démarrage" -ForegroundColor White
    Write-Host "  -Environment    Spécifie l'environnement (development, staging, production)" -ForegroundColor White
    Write-Host ""
    Write-Host "Exemples:" -ForegroundColor Yellow
    Write-Host "  .\start_gestimmob.ps1" -ForegroundColor Gray
    Write-Host "  .\start_gestimmob.ps1 -Debug -Clean" -ForegroundColor Gray
    Write-Host "  .\start_gestimmob.ps1 -Environment development" -ForegroundColor Gray
}

# Fonction principale
function Main {
    # Vérifier les paramètres d'aide
    if ($args -contains "-h" -or $args -contains "--help" -or $args -contains "/?") {
        Show-Help
        return 0
    }
    
    # Afficher l'en-tête
    Write-Header
    
    # Nettoyage si demandé
    Clean-TempFiles
    
    # Vérifications préliminaires
    if (-not (Test-PythonInstallation)) {
        return 1
    }
    
    # Installation des dépendances
    if (-not (Install-Dependencies)) {
        return 1
    }
    
    # Initialisation de la base de données
    if (-not (Initialize-Database)) {
        return 1
    }
    
    # Création des répertoires
    if (-not (Initialize-Directories)) {
        return 1
    }
    
    # Démarrage de l'application
    $exitCode = Start-Application
    
    return $exitCode
}

# Point d'entrée
try {
    $exitCode = Main
    exit $exitCode
}
catch {
    Write-Host "Erreur fatale: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    if (-not $Debug) {
        Write-Host ""
        Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
}
