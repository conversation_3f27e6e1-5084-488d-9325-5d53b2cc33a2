<!-- Generated by SpecStory -->

# /fix Le type de paramètre « requete » est inconnu, (2025-06-08 11:04:52)

_**User**_

/fix Le type de paramètre « requete » est inconnu, L'annotation de type est manquante pour le paramètre "requete"

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Ajouter une annotation de type explicite pour le paramètre `requete` dans la méthode `enrichir_avec_bigdata`. Ici, le type approprié est `str`.

````python
# ...existing code...
    def enrichir_avec_bigdata(self, requete: str):
        """
        Exemple d'enrichissement avec des données externes (Big Data, API, modèles IA multilingues)
        """
        # ...existing code...
# ...existing code...
````

---

