#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour annuler complètement le clonage et restaurer l'état original
"""

import os
import sqlite3
import shutil
from pathlib import Path
from datetime import datetime

class AnnuleurClonage:
    def __init__(self):
        self.fichiers_clonage = [
            "configuration_gestimmob_importee.json",
            "extraire_parametres_sql.py",
            "cloner_configurations_gestimmob.py",
            "analyser_configs_bureau.py",
            "explorer_contenu_dossiers.py",
            "LANCER_GESTIMMOB_CLONE.bat",
            "CLONAGE_CONFIGURATIONS_SUCCES.md",
            "integration_finale.sql"
        ]
        
        self.tables_clonage = [
            "parametres_clones",
            "utilisateurs_clones", 
            "immobilisations_clones"
        ]
        
        self.db_gestimmob = "gestimmob_final.db"
        
    def annuler_clonage_complet(self):
        """Annule complètement le clonage et restaure l'état original"""
        print("🔄 ANNULATION COMPLÈTE DU CLONAGE")
        print("=" * 60)
        
        # 1. Créer une sauvegarde avant annulation
        self.creer_sauvegarde()
        
        # 2. Nettoyer la base de données
        self.nettoyer_base_donnees()
        
        # 3. Supprimer les fichiers de clonage
        self.supprimer_fichiers_clonage()
        
        # 4. Restaurer la configuration originale
        self.restaurer_configuration_originale()
        
        # 5. Générer le rapport d'annulation
        self.generer_rapport_annulation()
        
        print(f"\n✅ ANNULATION TERMINÉE AVEC SUCCÈS !")
        print(f"GestImmob est maintenant restauré à son état original")
    
    def creer_sauvegarde(self):
        """Crée une sauvegarde avant annulation"""
        print(f"\n💾 CRÉATION DE SAUVEGARDE")
        print("-" * 40)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dossier_sauvegarde = f"sauvegarde_avant_annulation_{timestamp}"
        
        try:
            os.makedirs(dossier_sauvegarde, exist_ok=True)
            
            # Sauvegarder la base de données
            if os.path.exists(self.db_gestimmob):
                shutil.copy2(self.db_gestimmob, 
                           os.path.join(dossier_sauvegarde, f"gestimmob_avec_clonage_{timestamp}.db"))
                print(f"✅ Base de données sauvegardée")
            
            # Sauvegarder les fichiers de clonage
            fichiers_sauvegardes = 0
            for fichier in self.fichiers_clonage:
                if os.path.exists(fichier):
                    shutil.copy2(fichier, dossier_sauvegarde)
                    fichiers_sauvegardes += 1
            
            print(f"✅ {fichiers_sauvegardes} fichier(s) de clonage sauvegardé(s)")
            print(f"📁 Sauvegarde créée dans: {dossier_sauvegarde}")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
    
    def nettoyer_base_donnees(self):
        """Nettoie la base de données des éléments clonés"""
        print(f"\n🗃️ NETTOYAGE DE LA BASE DE DONNÉES")
        print("-" * 40)
        
        if not os.path.exists(self.db_gestimmob):
            print(f"⚠️ Base de données non trouvée: {self.db_gestimmob}")
            return
        
        try:
            conn = sqlite3.connect(self.db_gestimmob)
            cursor = conn.cursor()
            
            # Supprimer les tables de clonage
            tables_supprimees = 0
            for table in self.tables_clonage:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS {table}")
                    tables_supprimees += 1
                    print(f"✅ Table supprimée: {table}")
                except Exception as e:
                    print(f"⚠️ Erreur suppression table {table}: {e}")
            
            # Nettoyer les secteurs clonés (garder seulement les originaux)
            try:
                # Identifier les secteurs originaux (les 4 premiers créés)
                cursor.execute('''
                    SELECT COUNT(*) FROM secteurs_personnalises
                ''')
                nb_secteurs = cursor.fetchone()[0]
                
                if nb_secteurs > 4:
                    # Supprimer les secteurs clonés (au-delà des 4 originaux)
                    cursor.execute('''
                        DELETE FROM criteres_secteur 
                        WHERE secteur_id IN (
                            SELECT id FROM secteurs_personnalises 
                            WHERE id > 4
                        )
                    ''')
                    
                    cursor.execute('''
                        DELETE FROM secteurs_personnalises 
                        WHERE id > 4
                    ''')
                    
                    secteurs_supprimes = nb_secteurs - 4
                    print(f"✅ {secteurs_supprimes} secteur(s) cloné(s) supprimé(s)")
                else:
                    print(f"✅ Secteurs originaux préservés")
                    
            except Exception as e:
                print(f"⚠️ Erreur nettoyage secteurs: {e}")
            
            # Nettoyer les valeurs de critères clonées
            try:
                cursor.execute('''
                    DELETE FROM valeurs_criteres 
                    WHERE critere_id NOT IN (
                        SELECT id FROM criteres_secteur
                    )
                ''')
                print(f"✅ Valeurs de critères orphelines supprimées")
            except Exception as e:
                print(f"⚠️ Erreur nettoyage valeurs critères: {e}")
            
            # Optimiser la base après nettoyage
            cursor.execute("VACUUM")
            cursor.execute("ANALYZE")
            
            conn.commit()
            conn.close()
            
            print(f"✅ Base de données nettoyée et optimisée")
            
        except Exception as e:
            print(f"❌ Erreur nettoyage base: {e}")
    
    def supprimer_fichiers_clonage(self):
        """Supprime tous les fichiers liés au clonage"""
        print(f"\n🗑️ SUPPRESSION DES FICHIERS DE CLONAGE")
        print("-" * 40)
        
        fichiers_supprimes = 0
        
        for fichier in self.fichiers_clonage:
            try:
                if os.path.exists(fichier):
                    os.remove(fichier)
                    fichiers_supprimes += 1
                    print(f"✅ Supprimé: {fichier}")
                else:
                    print(f"⚠️ Déjà absent: {fichier}")
            except Exception as e:
                print(f"❌ Erreur suppression {fichier}: {e}")
        
        # Supprimer aussi les fichiers de sauvegarde temporaires
        fichiers_temp = [
            "configurations_extraites.json",
            "explorer_dossiers_bureau.py",
            "LISTER_DOSSIERS_BUREAU.bat"
        ]
        
        for fichier in fichiers_temp:
            try:
                if os.path.exists(fichier):
                    os.remove(fichier)
                    fichiers_supprimes += 1
                    print(f"✅ Supprimé (temp): {fichier}")
            except Exception as e:
                print(f"⚠️ Erreur suppression temp {fichier}: {e}")
        
        print(f"📊 {fichiers_supprimes} fichier(s) supprimé(s)")
    
    def restaurer_configuration_originale(self):
        """Restaure la configuration originale de GestImmob"""
        print(f"\n🔄 RESTAURATION CONFIGURATION ORIGINALE")
        print("-" * 40)
        
        try:
            conn = sqlite3.connect(self.db_gestimmob)
            cursor = conn.cursor()
            
            # Vérifier que les tables principales existent
            tables_principales = [
                "immobilisations",
                "fournisseurs", 
                "utilisateurs",
                "secteurs_personnalises",
                "criteres_secteur"
            ]
            
            for table in tables_principales:
                cursor.execute(f'''
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='{table}'
                ''')
                
                if cursor.fetchone():
                    print(f"✅ Table principale OK: {table}")
                else:
                    print(f"⚠️ Table principale manquante: {table}")
            
            # Vérifier les secteurs de base
            cursor.execute("SELECT COUNT(*) FROM secteurs_personnalises")
            nb_secteurs = cursor.fetchone()[0]
            
            if nb_secteurs >= 4:
                print(f"✅ Secteurs de base présents: {nb_secteurs}")
            else:
                print(f"⚠️ Secteurs de base insuffisants: {nb_secteurs}")
                # Recréer les secteurs de base si nécessaire
                self.recreer_secteurs_base(cursor)
            
            conn.commit()
            conn.close()
            
            print(f"✅ Configuration originale restaurée")
            
        except Exception as e:
            print(f"❌ Erreur restauration: {e}")
    
    def recreer_secteurs_base(self, cursor):
        """Recrée les secteurs de base si nécessaire"""
        print(f"🔧 Recréation des secteurs de base...")
        
        secteurs_base = [
            {
                'nom': 'Informatique & Technologies',
                'description': 'Équipements informatiques et technologies',
                'couleur': '#2196F3',
                'icone': '💻'
            },
            {
                'nom': 'Véhicules & Transport', 
                'description': 'Véhicules et moyens de transport',
                'couleur': '#FF9800',
                'icone': '🚗'
            },
            {
                'nom': 'Mobilier & Aménagement',
                'description': 'Mobilier et aménagement des espaces',
                'couleur': '#8BC34A', 
                'icone': '🪑'
            },
            {
                'nom': 'Équipements Industriels',
                'description': 'Machines et équipements industriels',
                'couleur': '#F44336',
                'icone': '⚙️'
            }
        ]
        
        for secteur in secteurs_base:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO secteurs_personnalises
                    (nom, description, couleur, icone, is_active)
                    VALUES (?, ?, ?, ?, 1)
                ''', (
                    secteur['nom'],
                    secteur['description'], 
                    secteur['couleur'],
                    secteur['icone']
                ))
                print(f"✅ Secteur de base recréé: {secteur['nom']}")
            except Exception as e:
                print(f"❌ Erreur secteur {secteur['nom']}: {e}")
    
    def generer_rapport_annulation(self):
        """Génère un rapport de l'annulation"""
        print(f"\n📊 RAPPORT D'ANNULATION")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_gestimmob)
            cursor = conn.cursor()
            
            # Compter les éléments restants
            cursor.execute("SELECT COUNT(*) FROM secteurs_personnalises")
            nb_secteurs = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM immobilisations")
            nb_immobilisations = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM fournisseurs")
            nb_fournisseurs = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ État après annulation:")
            print(f"  📊 Secteurs: {nb_secteurs}")
            print(f"  🏠 Immobilisations: {nb_immobilisations}")
            print(f"  🏢 Fournisseurs: {nb_fournisseurs}")
            
            # Vérifier les fichiers supprimés
            fichiers_restants = []
            for fichier in self.fichiers_clonage:
                if os.path.exists(fichier):
                    fichiers_restants.append(fichier)
            
            if fichiers_restants:
                print(f"⚠️ Fichiers non supprimés: {len(fichiers_restants)}")
                for fichier in fichiers_restants:
                    print(f"  📄 {fichier}")
            else:
                print(f"✅ Tous les fichiers de clonage supprimés")
            
            print(f"\n🎯 ANNULATION RÉUSSIE !")
            print(f"GestImmob est maintenant dans son état original")
            print(f"Toutes les modifications du clonage ont été annulées")
            
        except Exception as e:
            print(f"❌ Erreur rapport: {e}")
    
    def creer_script_verification(self):
        """Crée un script pour vérifier l'état après annulation"""
        script_content = '''
-- Script de vérification après annulation du clonage
-- Exécutez ce script pour vérifier l'état de la base

SELECT 'Tables principales' as verification, COUNT(*) as nombre
FROM sqlite_master 
WHERE type='table' AND name IN ('immobilisations', 'fournisseurs', 'utilisateurs')

UNION ALL

SELECT 'Secteurs personnalisés' as verification, COUNT(*) as nombre
FROM secteurs_personnalises

UNION ALL

SELECT 'Tables de clonage restantes' as verification, COUNT(*) as nombre
FROM sqlite_master 
WHERE type='table' AND name LIKE '%clone%'

UNION ALL

SELECT 'Immobilisations' as verification, COUNT(*) as nombre
FROM immobilisations;
'''
        
        with open('verification_annulation.sql', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"\n📄 Script de vérification créé: verification_annulation.sql")

def main():
    """Fonction principale"""
    print("🔄 ANNULATION COMPLÈTE DU CLONAGE")
    print("=" * 60)
    print("Ce script va:")
    print("✅ Créer une sauvegarde de sécurité")
    print("✅ Supprimer toutes les tables clonées")
    print("✅ Nettoyer les secteurs clonés")
    print("✅ Supprimer tous les fichiers de clonage")
    print("✅ Restaurer la configuration originale")
    print()
    
    confirmation = input("Voulez-vous vraiment annuler tout le clonage ? (oui/non): ")
    
    if confirmation.lower() in ['oui', 'o', 'yes', 'y']:
        annuleur = AnnuleurClonage()
        annuleur.annuler_clonage_complet()
        annuleur.creer_script_verification()
        
        print(f"\n🎉 ANNULATION TERMINÉE !")
        print(f"GestImmob est maintenant restauré à son état original")
        print(f"Toutes les modifications du clonage ont été supprimées")
    else:
        print("❌ Annulation annulée par l'utilisateur")

if __name__ == "__main__":
    main()
