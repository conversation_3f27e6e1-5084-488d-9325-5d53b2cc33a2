#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic ultra-précis de TOUS les problèmes
"""

import ast
import os
import sys
import traceback
from pathlib import Path

def test_file_ultra_precise(file_path):
    """Test ultra-précis d'un fichier Python"""
    print(f"\n{'='*80}")
    print(f"DIAGNOSTIC ULTRA-PRÉCIS: {file_path}")
    print(f"{'='*80}")
    
    if not os.path.exists(file_path):
        print(f"❌ ERREUR: Fichier non trouvé: {file_path}")
        return False, [f"Fichier non trouvé: {file_path}"]
    
    errors = []
    
    try:
        # Lecture du fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ Fichier lu: {len(content)} caractères, {len(content.splitlines())} lignes")
        
        if not content.strip():
            error = "Fichier vide"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        
        # Test 1: AST parsing ligne par ligne pour localiser l'erreur exacte
        print(f"\n🔍 TEST AST (Abstract Syntax Tree):")
        try:
            tree = ast.parse(content, filename=file_path)
            print(f"✅ AST parsing: RÉUSSI")
        except SyntaxError as e:
            error = f"ERREUR AST ligne {e.lineno}, colonne {e.offset}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            
            # Afficher le contexte de l'erreur
            lines = content.splitlines()
            if e.lineno and e.lineno <= len(lines):
                print(f"📍 CONTEXTE DE L'ERREUR:")
                start = max(0, e.lineno - 3)
                end = min(len(lines), e.lineno + 3)
                for i in range(start, end):
                    marker = " >>> " if i == e.lineno - 1 else "     "
                    print(f"{marker}{i+1:4d}: {lines[i]}")
                
                if e.text:
                    print(f"📍 Code problématique: {repr(e.text.strip())}")
            
            return False, errors
        except Exception as e:
            error = f"ERREUR AST inattendue: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
            traceback.print_exc()
            return False, errors
        
        # Test 2: Compilation
        print(f"\n🔍 TEST COMPILATION:")
        try:
            compile(content, file_path, 'exec')
            print(f"✅ Compilation: RÉUSSIE")
        except SyntaxError as e:
            error = f"ERREUR COMPILATION ligne {e.lineno}, colonne {e.offset}: {e.msg}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        except Exception as e:
            error = f"ERREUR COMPILATION inattendue: {type(e).__name__}: {e}"
            print(f"❌ {error}")
            errors.append(error)
            return False, errors
        
        # Test 3: Analyse des problèmes spécifiques
        print(f"\n🔍 ANALYSE DES PROBLÈMES SPÉCIFIQUES:")
        lines = content.splitlines()
        
        specific_issues = []
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Problème 1: dict[...] au lieu de Dict[...]
            if 'dict[' in line and 'Dict[' not in line:
                issue = f"Ligne {i}: 'dict[...]' devrait être 'Dict[...]'"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
                print(f"    Code: {line.strip()}")
            
            # Problème 2: list[...] au lieu de List[...]
            if 'list[' in line and 'List[' not in line:
                issue = f"Ligne {i}: 'list[...]' devrait être 'List[...]'"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
                print(f"    Code: {line.strip()}")
            
            # Problème 3: any au lieu de Any
            if ': any =' in line.lower():
                issue = f"Ligne {i}: 'any' devrait être 'Any'"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
                print(f"    Code: {line.strip()}")
            
            # Problème 4: Imports manquants
            if ('Dict[' in line or 'List[' in line or 'Any' in line) and 'from typing import' not in content:
                if not any('Import typing manquant' in issue for issue in specific_issues):
                    issue = "Import typing manquant pour les annotations de type"
                    specific_issues.append(issue)
                    print(f"⚠️ {issue}")
            
            # Problème 5: Fonctions appelées mais non définies
            if 'encrypt_password(' in line and 'def encrypt_password' not in content:
                issue = f"Ligne {i}: Fonction 'encrypt_password' appelée mais non définie"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
            
            if 'get_db_session(' in line and 'def get_db_session' not in content:
                issue = f"Ligne {i}: Fonction 'get_db_session' appelée mais non définie"
                specific_issues.append(issue)
                print(f"⚠️ {issue}")
        
        if specific_issues:
            errors.extend(specific_issues)
            print(f"\n📊 TOTAL PROBLÈMES SPÉCIFIQUES: {len(specific_issues)}")
        else:
            print(f"✅ Aucun problème spécifique détecté")
        
        # Test 4: Statistiques du fichier
        print(f"\n📊 STATISTIQUES:")
        print(f"   Classes: {content.count('class ')}")
        print(f"   Fonctions: {content.count('def ')}")
        print(f"   Imports: {content.count('import ')}")
        print(f"   Lignes de code: {len([l for l in lines if l.strip() and not l.strip().startswith('#')])}")
        
        # Résultat final
        if errors:
            print(f"\n❌ FICHIER AVEC PROBLÈMES: {len(errors)} erreur(s)")
            return False, errors
        else:
            print(f"\n✅ FICHIER PARFAIT: Aucun problème détecté")
            return True, []
            
    except Exception as e:
        error = f"ERREUR INATTENDUE: {type(e).__name__}: {e}"
        print(f"❌ {error}")
        errors.append(error)
        traceback.print_exc()
        return False, errors

def main():
    """Diagnostic ultra-précis de tous les fichiers"""
    print("🔍 DIAGNOSTIC ULTRA-PRÉCIS DE TOUS LES FICHIERS")
    print("=" * 80)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Liste exhaustive des fichiers à analyser
    files_to_analyze = [
        'src/main.py',
        'src/config.py',
        'src/database/db_connection.py',
        'src/database/models.py',
        'src/views/admin_view.py',
        'src/auth/user_management.py',
        'src/auth/authentication.py',
        'src/auth/encryption.py',
        'src/utils/config.py'
    ]
    
    results = {}
    total_errors = []
    
    for file_path in files_to_analyze:
        is_valid, errors = test_file_ultra_precise(file_path)
        results[file_path] = {
            'valid': is_valid,
            'errors': errors
        }
        if errors:
            total_errors.extend([f"{file_path}: {error}" for error in errors])
    
    # Résumé final ultra-détaillé
    print(f"\n{'='*80}")
    print("📋 RÉSUMÉ ULTRA-DÉTAILLÉ")
    print(f"{'='*80}")
    
    valid_files = sum(1 for result in results.values() if result['valid'])
    total_files = len(results)
    
    print(f"📁 Fichiers analysés: {total_files}")
    print(f"✅ Fichiers valides: {valid_files}")
    print(f"❌ Fichiers avec erreurs: {total_files - valid_files}")
    print(f"🚨 Total erreurs: {len(total_errors)}")
    
    if total_errors:
        print(f"\n🚨 LISTE COMPLÈTE DES ERREURS:")
        for i, error in enumerate(total_errors, 1):
            print(f"  {i:2d}. {error}")
    
    print(f"\n📊 DÉTAIL PAR FICHIER:")
    for file_path, result in results.items():
        status = "✅ VALIDE" if result['valid'] else "❌ ERREURS"
        error_count = len(result['errors'])
        print(f"  {status:12} {file_path} ({error_count} erreur(s))")
        
        if result['errors']:
            for error in result['errors'][:3]:  # Limiter à 3 erreurs par fichier
                print(f"    - {error}")
            if len(result['errors']) > 3:
                print(f"    ... et {len(result['errors']) - 3} autres erreurs")
    
    if valid_files == total_files:
        print(f"\n🎉 TOUS LES FICHIERS SONT PARFAITS!")
        print(f"✅ Aucune erreur détectée")
        print(f"✅ Tous les fichiers compilent correctement")
        return True
    else:
        print(f"\n🚨 {total_files - valid_files} FICHIER(S) AVEC DES ERREURS")
        print(f"❌ {len(total_errors)} erreur(s) au total")
        print(f"🔧 Corrections nécessaires avant le lancement")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat final: {'SUCCÈS' if success else 'ÉCHEC'}")
    sys.exit(0 if success else 1)
