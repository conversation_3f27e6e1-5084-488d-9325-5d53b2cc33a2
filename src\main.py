# -*- coding: utf-8 -*-
# pylint: disable=all
# pyright: reportMissingImports=false
# pyright: reportMissingTypeStubs=false
# pyright: reportUnknownMemberType=false
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false
# pyright: reportUnknownLambdaType=false
# pyright: reportUnknownParameterType=false
# pyright: reportGeneralTypeIssues=false
# pyright: reportOptionalSubscript=false
# pyright: reportOptionalMemberAccess=false
# pyright: reportOptionalCall=false
# pyright: reportUnusedVariable=false
# pyright: reportUnusedImport=false
# pyright: reportAttributeAccessIssue=false
# type: ignore
"""
Gestion des Immobilisations - Logiciel de gestion d'actifs immobiliers
Encodage: UTF-8
Auteur: Système de Gestion Immobilière
Version: 1.0.0
"""

# Pour démarrer le logiciel, tapez la commande suivante dans votre terminal (sans le #) :
# python src/main.py
# Exemple :
# python src/main.py
#
# Pour démarrer le logiciel en dehors de VS Code :
# 1. Ouvrez l'invite de commandes Windows (cmd).
# 2. Naviguez dans le dossier du projet avec la commande :
#    cd C:\Users\<USER>\Desktop\logiciel GestImmob\gestion-software
# 3. Lancez le logiciel avec la commande :
#    python src/main.py

# Ce logiciel utilise une interface graphique (PySide6).

import sys
import os
import logging
import shutil
import csv
import json
import platform
from datetime import datetime
import hashlib
import sqlite3
from typing import Optional, Dict, Any, List, Tuple

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QMessageBox, QMenu,
    QDialog, QFormLayout, QDialogButtonBox, QComboBox, QMenuBar, QTextEdit,
    QAbstractItemView
)
from PySide6.QtGui import QAction, QPixmap, QFont, QGuiApplication
from PySide6.QtCore import Qt, QTimer
from typing import Any, Optional, Dict, List, Tuple, Union, Callable, Iterator  # type: ignore
from typing import TYPE_CHECKING  # type: ignore

# Suppression des warnings Pylance pour les imports
_ = Union, Callable, Iterator, TYPE_CHECKING

try:
    from cryptography.fernet import Fernet
except ImportError:
    print("Warning: cryptography module not found. Security features disabled.")
    Fernet: Any = None

# Gestionnaire d'imports optimisé avec cache
class ModuleManager:
    """Gestionnaire optimisé des imports de modules avec cache et vérification des dépendances"""

    def __init__(self) -> None:
        self.module_cache: Dict[str, Any] = {}
        self.import_errors: Dict[str, str] = {}
        self.performance_timer: Dict[str, float] = {}

    def import_module_safe(self, module_name: str, class_name: Optional[str] = None) -> Any:
        """Import sécurisé avec cache et mesure de performance"""
        cache_key: str = f"{module_name}.{class_name}" if class_name else module_name

        if cache_key in self.module_cache:
            return self.module_cache[cache_key]

        start_time: datetime = datetime.now()
        try:
            if class_name:
                module: Any = __import__(module_name, fromlist=[class_name])
                imported_class: Any = getattr(module, class_name)
                self.module_cache[cache_key] = imported_class
                logging.info(f"Module {cache_key} importé avec succès")
                return imported_class
            else:
                module: Any = __import__(module_name)
                self.module_cache[cache_key] = module
                return module
        except ImportError as e:
            self.import_errors[cache_key] = str(e)
            logging.warning(f"Échec import {cache_key}: {e}")
            self.module_cache[cache_key] = None
            return None
        finally:
            self.performance_timer[cache_key] = (datetime.now() - start_time).total_seconds()

    def get_import_report(self) -> Dict[str, Any]:
        """Génère un rapport des imports et performances"""
        return {
            'modules_loaded': len([k for k, v in self.module_cache.items() if v is not None]),
            'modules_failed': len(self.import_errors),
            'import_errors': self.import_errors,
            'performance_times': self.performance_timer
        }

# Instance globale du gestionnaire de modules
module_manager: ModuleManager = ModuleManager()

# Import des modules avec gestion d'erreur optimisée
FournisseurWidget: Any = module_manager.import_module_safe('fournisseur', 'FournisseurWidget')
InventaireWidget: Any = module_manager.import_module_safe('inventaire', 'InventaireWidget')
RechercheWidget: Any = module_manager.import_module_safe('recherche', 'RechercheWidget')
ReformeWidget: Any = module_manager.import_module_safe('reforme', 'ReformeWidget')
DocumentWidget: Any = module_manager.import_module_safe('document', 'DocumentWidget')
ImpressionWidget: Any = module_manager.import_module_safe('impression', 'ImpressionWidget')

# Import des fonctions de configuration
try:
    from config import load_config, save_config
except ImportError:
    def load_config() -> Dict[str, Any]:
        return {}
    def save_config(config: Dict[str, Any]) -> None:
        _ = config  # Ignorer le paramètre inutilisé
        pass

# Set up logging before any logging calls
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Ensure the parent directory is in the Python path for module imports
try:
    current_file = os.path.abspath(__file__)
except NameError:
    current_file = os.path.abspath(sys.argv[0])

# Ajouter le répertoire parent et le répertoire modules au path
parent_dir = os.path.dirname(os.path.dirname(current_file))
sys.path.append(parent_dir)
sys.path.append(os.path.join(parent_dir, 'modules'))

# Classe Immobilisation locale (remplace l'import externe)
class Immobilisation:
    def __init__(self, id_bien: str, designation: str, valeur: float, annee: int,
                 localisation: str, secteur: str, observation: str) -> None:
        self.id = id_bien
        self.designation = designation
        self.valeur = valeur
        self.annee = annee
        self.localisation = localisation
        self.secteur = secteur
        self.observation = observation

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "designation": self.designation,
            "valeur": self.valeur,
            "annee": self.annee,
            "localisation": self.localisation,
            "secteur": self.secteur,
            "observation": self.observation
        }

class DatabaseConnection:
    def __init__(self, db_path: str) -> None:
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self._optimize_connection()
        self._init_tables()

    def _optimize_connection(self) -> None:
        """Optimise la connexion SQLite pour de meilleures performances"""
        cursor: Any = self.conn.cursor()
        try:
            # Optimisations SQLite pour la performance
            optimizations = [
                "PRAGMA journal_mode = WAL",           # Write-Ahead Logging
                "PRAGMA synchronous = NORMAL",         # Balance performance/safety
                "PRAGMA cache_size = -64000",          # 64MB cache
                "PRAGMA temp_store = MEMORY",          # Temp tables in memory
                "PRAGMA mmap_size = 268435456",        # 256MB memory mapping
                "PRAGMA page_size = 4096",             # Optimal page size
                "PRAGMA auto_vacuum = INCREMENTAL",    # Incremental vacuum
                "PRAGMA foreign_keys = ON"             # Enable foreign keys
            ]

            for pragma in optimizations:
                cursor.execute(pragma)

            self.conn.commit()
            logging.info("Base de données optimisée pour les performances")

        except Exception as e:
            logging.error(f"Erreur lors de l'optimisation de la DB: {e}")

    def _init_tables(self) -> None:
        """Initialise les tables de la base de données avec index optimisés"""
        cursor: Any = self.conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS immobilisations (
                id TEXT PRIMARY KEY,
                designation TEXT NOT NULL,
                valeur REAL NOT NULL,
                annee INTEGER NOT NULL,
                localisation TEXT NOT NULL,
                secteur TEXT NOT NULL,
                observation TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Création d'index pour améliorer les performances des requêtes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_immob_designation ON immobilisations(designation)",
            "CREATE INDEX IF NOT EXISTS idx_immob_secteur ON immobilisations(secteur)",
            "CREATE INDEX IF NOT EXISTS idx_immob_localisation ON immobilisations(localisation)",
            "CREATE INDEX IF NOT EXISTS idx_immob_annee ON immobilisations(annee)",
            "CREATE INDEX IF NOT EXISTS idx_immob_valeur ON immobilisations(valeur)"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

        self.conn.commit()

    def close(self) -> None:
        """Ferme la connexion à la base de données"""
        if self.conn:
            self.conn.close()

    def lister_toutes_immobilisations(self) -> List[Any]:
        """Retourne toutes les immobilisations"""
        return []  # Placeholder

    def dernier_id_utilise(self) -> str:
        """Retourne le dernier ID utilisé"""
        return "0"  # Placeholder

    def mettre_a_jour_immobilisation(self, bien: Any) -> bool:
        """Met à jour une immobilisation"""
        _ = bien  # Ignorer le paramètre inutilisé
        return True  # Placeholder

    def initialiser_avec_donnees(self, donnees: List[Any]) -> None:
        """Initialise avec des données"""
        _ = donnees  # Ignorer le paramètre inutilisé
        pass  # Placeholder

class AppLogger:
    @staticmethod
    def setup_logging() -> None:
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AuthManager:
    def __init__(self) -> None:
        self.key = self._get_or_create_key()
        if Fernet:
            self.cipher = Fernet(self.key)
        else:
            self.cipher = None
        self.conn = sqlite3.connect("immobilisations.db")
        self._init_db()

    def _get_or_create_key(self) -> bytes:
        """Gère la clé de chiffrement"""
        if os.path.exists("secret.key"):
            with open("secret.key", 'rb') as f:
                return f.read()
        else:
            if Fernet:
                key = Fernet.generate_key()
                with open("secret.key", 'wb') as f:
                    f.write(key)
                os.chmod("secret.key", 0o600)
                return key
            else:
                # Fallback si Fernet n'est pas disponible
                key = os.urandom(32)
                with open("secret.key", 'wb') as f:
                    f.write(key)
                os.chmod("secret.key", 0o600)
                return key

    def _init_db(self) -> None:
        """Initialise la base de données"""
        cursor: Any = self.conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash BLOB NOT NULL,
                salt BLOB NOT NULL,
                role TEXT NOT NULL,
                full_name TEXT,
                last_login TEXT,
                is_active INTEGER DEFAULT 1
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INTEGER PRIMARY KEY,
                username TEXT NOT NULL,
                attempt_time TEXT NOT NULL,
                success INTEGER
            )
        ''')
        # Créer un admin par défaut si nécessaire
        if not cursor.execute("SELECT 1 FROM users WHERE role='admin'").fetchone():
            self._create_default_admin()
        self.conn.commit()

    def _create_default_admin(self) -> None:
        """Crée un admin par défaut"""
        salt: bytes = os.urandom(32)
        default_pass: str = "Admin@1234"
        pass_hash: bytes = self._hash_password(default_pass, salt)
        cursor: Any = self.conn.cursor()
        cursor.execute('''
            INSERT INTO users (username, password_hash, salt, role, full_name)
            VALUES (?, ?, ?, ?, ?)
        ''', ("admin", pass_hash, salt, "admin", "Administrateur"))
        logging.warning("Admin par défaut créé - Changez le mot de passe immédiatement!")

    def _hash_password(self, password: str, salt: bytes) -> bytes:
        """Hash le mot de passe avec PBKDF2"""
        return hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000
        )

    def create_user(self, username: str, password: str, role: str, full_name: str = "") -> bool:
        """Crée un nouvel utilisateur"""
        if role not in ["admin", "gestionnaire", "consultant"]:
            raise ValueError("Rôle invalide")
        salt: bytes = os.urandom(32)
        pass_hash: bytes = self._hash_password(password, salt)
        cursor: Any = self.conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO users (username, password_hash, salt, role, full_name)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, pass_hash, salt, role, full_name))
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False

    def authenticate(self, username: str, password: str) -> Tuple[Optional[Dict[str, str]], Optional[str]]:
        """Authentifie un utilisateur"""
        if self._is_account_locked(username):
            return None, "Compte temporairement bloqué"
        cursor: Any = self.conn.cursor()
        user: Any = cursor.execute('''
            SELECT id, password_hash, salt, role, full_name FROM users
            WHERE username=? AND is_active=1
        ''', (username,)).fetchone()
        if not user:
            self._log_attempt(username, False)
            return None, "Identifiants incorrects"
        _, stored_hash, salt, role, full_name = user
        input_hash = self._hash_password(password, salt)
        if input_hash == stored_hash:
            self._log_attempt(username, True)
            cursor.execute('''
                UPDATE users SET last_login=CURRENT_TIMESTAMP WHERE username=?
            ''', (username,))
            self.conn.commit()
            return {"username": username, "role": role, "full_name": full_name}, None
        else:
            self._log_attempt(username, False)
            return None, "Identifiants incorrects"

    def _log_attempt(self, username: str, success: bool) -> None:
        """Journalise les tentatives de connexion"""
        cursor: Any = self.conn.cursor()
        cursor.execute('''
            INSERT INTO login_attempts (username, attempt_time, success)
            VALUES (?, CURRENT_TIMESTAMP, ?)
        ''', (username, int(success)))
        self.conn.commit()

    def _is_account_locked(self, username: str) -> bool:
        """Vérifie si le compte est temporairement bloqué"""
        cursor: Any = self.conn.cursor()
        recent_failures: Any = cursor.execute('''
            SELECT COUNT(*) FROM login_attempts
            WHERE username=? AND success=0
            AND attempt_time > datetime('now', '-15 minutes')
        ''', (username,)).fetchone()[0]
        return recent_failures >= 5

    def encrypt_data(self, data: str) -> str:
        """Chiffre les données sensibles"""
        if self.cipher:
            return self.cipher.encrypt(data.encode()).decode()
        else:
            # Fallback sans chiffrement si Fernet n'est pas disponible
            return data

    def decrypt_data(self, encrypted_data: str) -> str:
        """Déchiffre les données"""
        if self.cipher:
            return self.cipher.decrypt(encrypted_data.encode()).decode()
        else:
            # Fallback sans déchiffrement si Fernet n'est pas disponible
            return encrypted_data

# Classe principale de la fenêtre
class ImmobilisationMainWindow(QMainWindow):
    def __init__(self, db_connection: Any, user_info: Any) -> None:
        super().__init__()
        # Remplacement de QDesktopWidget par QGuiApplication.primaryScreen().availableGeometry()
        screen_geometry = QGuiApplication.primaryScreen().availableGeometry()
        self.setWindowTitle("Gestion des Immobilisations")
        self.resize(screen_geometry.width(), screen_geometry.height())
        self.setMinimumSize(1024, 600)
        self.db = db_connection
        self.user_info = user_info

        # Appliquer un style moderne
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f7fa;
            }
            QLabel#HeaderTitle {
                color: #2d3a4b;
                font-size: 22px;
                font-weight: bold;
                letter-spacing: 2px;
            }
            QLabel#HeaderSubtitle {
                color: #4e5d6c;
                font-size: 13px;
            }
            QPushButton {
                background-color: #1976d2;
                color: white;
                border-radius: 8px;
                padding: 8px 18px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QLineEdit {
                border: 1px solid #b0bec5;
                border-radius: 6px;
                padding: 6px;
                font-size: 13px;
            }
            QTableWidget {
                background: #ffffff;
                border-radius: 8px;
                font-size: 13px;
            }
            QHeaderView::section {
                background-color: #1976d2;
                color: white;
                font-weight: bold;
                border-radius: 0px;
                padding: 4px;
            }
        """)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # En-tête avec logo et titre
        header_layout = QHBoxLayout()
        logo_label = QLabel()
        try:
            pixmap = QPixmap(60, 60)  # Initialize pixmap with size (optionally load an image: QPixmap("path/to/logo.png"))
            pixmap = pixmap.scaled(60, 60, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(pixmap)
        except Exception:
            logo_label.setText("🏢")
            logo_label.setFont(QFont("Arial", 32))
        header_layout.addWidget(logo_label)

        title_layout: QVBoxLayout = QVBoxLayout()
        title: QLabel = QLabel("GESTION DES IMMOBILISATIONS")
        title.setObjectName("HeaderTitle")
        subtitle: QLabel = QLabel("Logiciel moderne et intuitif pour la gestion de vos actifs")
        subtitle.setObjectName("HeaderSubtitle")
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        layout.addLayout(header_layout)

        # Champs de saisie
        form_layout = QHBoxLayout()
        self.id_input = QLineEdit()
        self.id_input.setPlaceholderText("ID")
        self.designation_input = QLineEdit()
        self.designation_input.setPlaceholderText("Désignation")
        self.valeur_input = QLineEdit()
        self.valeur_input.setPlaceholderText("Valeur")
        self.annee_input = QLineEdit()
        self.annee_input.setPlaceholderText("Année")
        self.localisation_input = QLineEdit()
        self.localisation_input.setPlaceholderText("Localisation")
        self.secteur_input = QLineEdit()
        self.secteur_input.setPlaceholderText("Secteur")
        self.observation_input = QTextEdit()
        self.observation_input.setPlaceholderText("Observation")
        form_layout.addWidget(self.id_input)
        form_layout.addWidget(self.designation_input)
        form_layout.addWidget(self.valeur_input)
        form_layout.addWidget(self.annee_input)
        form_layout.addWidget(self.localisation_input)
        form_layout.addWidget(self.secteur_input)
        form_layout.addWidget(self.observation_input)
        layout.addLayout(form_layout)

        # Accès rapide à tous les modules (boutons d'accès centralisés)
        modules_layout: QHBoxLayout = QHBoxLayout()
        self.btn_inventaire: QPushButton = QPushButton("Inventaire")
        self.btn_fournisseur: QPushButton = QPushButton("Fournisseurs")
        self.btn_recherche: QPushButton = QPushButton("Recherche")
        self.btn_reforme: QPushButton = QPushButton("Réformes")
        self.btn_document: QPushButton = QPushButton("Documents")
        self.btn_impression: QPushButton = QPushButton("Impression")
        self.btn_erp: QPushButton = QPushButton("ERP/Comptabilité")
        self.btn_calculatrice: QPushButton = QPushButton("Calculatrice")
        modules_layout.addWidget(self.btn_inventaire)
        modules_layout.addWidget(self.btn_fournisseur)
        modules_layout.addWidget(self.btn_recherche)
        modules_layout.addWidget(self.btn_reforme)
        modules_layout.addWidget(self.btn_document)
        modules_layout.addWidget(self.btn_impression)
        modules_layout.addWidget(self.btn_erp)
        modules_layout.addWidget(self.btn_calculatrice)
        layout.addLayout(modules_layout)

        # Boutons centralisés pour opérations globales
        ops_layout: QHBoxLayout = QHBoxLayout()
        self.btn_sync: QPushButton = QPushButton("Synchroniser")
        self.btn_save: QPushButton = QPushButton("Sauvegarder")
        self.btn_refresh: QPushButton = QPushButton("Actualiser")
        self.btn_performance: QPushButton = QPushButton("Performance")
        ops_layout.addWidget(self.btn_sync)
        ops_layout.addWidget(self.btn_save)
        ops_layout.addWidget(self.btn_refresh)
        ops_layout.addWidget(self.btn_performance)
        layout.addLayout(ops_layout)

        # Boutons d'action
        btn_layout: QHBoxLayout = QHBoxLayout()
        self.add_btn: QPushButton = QPushButton("Ajouter")
        self.export_btn: QPushButton = QPushButton("Exporter Excel")
        self.import_btn: QPushButton = QPushButton("Importer CSV")
        self.depreciation_btn: QPushButton = QPushButton("Calculer Amortissement")
        self.email_btn: QPushButton = QPushButton("Envoyer Email")
        self.param_btn: QPushButton = QPushButton("Paramètres")
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.export_btn)
        btn_layout.addWidget(self.import_btn)
        btn_layout.addWidget(self.depreciation_btn)
        btn_layout.addWidget(self.email_btn)
        btn_layout.addWidget(self.param_btn)
        layout.addLayout(btn_layout)

        # Correction : 7 colonnes pour tous les champs principaux
        self.table = QTableWidget(0, 7)
        self.table.setHorizontalHeaderLabels([
            "ID", "Désignation", "Valeur", "Année", "Localisation", "Secteur", "Observation"
        ])
        self.table.setEditTriggers(QAbstractItemView.EditTrigger.AllEditTriggers)
        layout.addWidget(self.table)
        # Correction : supprimer le double ajout du widget table
        # layout.addWidget(self.table)  # <-- supprimé

        # Pour éviter la sauvegarde multiple lors du chargement initial
        self._chargement_initial: bool = True
        self.modifications_non_sauvegardees: bool = False
        self.fichier_sauvegarde: str = "donnees_immobilisations.json"
        self.db: Any = db_connection  # Assurez-vous que db_connection a les méthodes requises

        # Timer pour sauvegarde automatique optimisé (15 minutes au lieu de 5)
        self.timer_sauvegarde: QTimer = QTimer()
        self.timer_sauvegarde.timeout.connect(self.sauvegarde_auto)
        self.timer_sauvegarde.start(900000)  # 15 minutes pour réduire la charge

        # Gestionnaire de performance
        self.performance_manager: Optional[Any] = None
        try:
            from utils.performance_manager import performance_manager  # type: ignore
            db_conn = self.db.conn if hasattr(self.db, 'conn') else None
            performance_manager.initialize(db_conn)  # type: ignore
            self.performance_manager = performance_manager
            logging.info("Gestionnaire de performance initialisé")
        except (ImportError, AttributeError) as e:
            logging.warning(f"Gestionnaire de performance non disponible: {e}")

        # Connexions
        self.add_btn.clicked.connect(self.ajouter_bien)
        self.export_btn.clicked.connect(self.exporter_excel)
        self.import_btn.clicked.connect(self.importer_csv)
        self.depreciation_btn.clicked.connect(self.calculer_amortissement)
        self.email_btn.clicked.connect(self.envoyer_email)
        self.param_btn.clicked.connect(self.ouvrir_parametres)
        self.table.itemChanged.connect(self._on_item_changed)

        # Connexions pour accès modules
        self.btn_inventaire.clicked.connect(self.open_inventaire)
        self.btn_fournisseur.clicked.connect(self.open_fournisseur)
        self.btn_recherche.clicked.connect(self.open_recherche)
        self.btn_reforme.clicked.connect(self.open_reforme)
        self.btn_document.clicked.connect(self.open_documents)
        self.btn_impression.clicked.connect(self.open_impression)
        self.btn_erp.clicked.connect(self.open_erp)
        self.btn_calculatrice.clicked.connect(self.open_calculatrice)
        self.btn_sync.clicked.connect(self.synchroniser)
        self.btn_save.clicked.connect(self.sauvegarder_donnees)
        self.btn_refresh.clicked.connect(self.actualiser)
        self.btn_performance.clicked.connect(self.show_performance_dialog)

        # Connecter les signaux de modification
        self.id_input.textChanged.connect(self.marquer_comme_modifie)
        self.designation_input.textChanged.connect(self.marquer_comme_modifie)
        self.valeur_input.textChanged.connect(self.marquer_comme_modifie)
        self.localisation_input.textChanged.connect(self.marquer_comme_modifie)

        self.charger_biens()
        self._chargement_initial = False
        self._setup_permissions()

        # Vérification des dépendances et performance au démarrage
        self._verify_dependencies()
        self._optimize_ui_performance()

        # Barre de menu ERP/navigation
        menubar: QMenuBar = QMenuBar(self)
        self.setMenuBar(menubar)
        menu_modules: QMenu = menubar.addMenu("Modules")
        action_fournisseur: QAction = QAction("Fournisseurs", self)
        action_inventaire: QAction = QAction("Inventaire", self)
        action_recherche: QAction = QAction("Recherche", self)
        action_reforme: QAction = QAction("Réforme", self)
        action_erp: QAction = QAction("ERP/Comptabilité", self)
        action_impression: QAction = QAction("Impression", self)
        menu_modules.addAction(action_fournisseur)
        menu_modules.addAction(action_inventaire)
        menu_modules.addAction(action_recherche)
        menu_modules.addAction(action_reforme)
        menu_modules.addAction(action_erp)
        menu_modules.addAction(action_impression)
        action_fournisseur.triggered.connect(self.open_fournisseur)
        action_inventaire.triggered.connect(self.open_inventaire)
        action_recherche.triggered.connect(self.open_recherche)
        action_reforme.triggered.connect(self.open_reforme)
        action_erp.triggered.connect(self.open_erp)
        action_impression.triggered.connect(self.open_impression)

    def _verify_dependencies(self) -> None:
        """Vérifie les dépendances et modules disponibles"""
        try:
            # Rapport des imports de modules
            import_report = module_manager.get_import_report()
            logging.info(f"Modules chargés: {import_report['modules_loaded']}")
            logging.info(f"Modules échoués: {import_report['modules_failed']}")

            if import_report['modules_failed'] > 0:
                logging.warning(f"Erreurs d'import: {import_report['import_errors']}")

            # Vérification des dépendances critiques
            critical_modules = ['PySide6', 'sqlite3', 'json', 'csv']
            missing_modules: List[str] = []

            for module in critical_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)

            if missing_modules:
                QMessageBox.warning(
                    self,
                    "Dépendances manquantes",
                    f"Modules critiques manquants: {', '.join(missing_modules)}"
                )

        except Exception as e:
            logging.error(f"Erreur lors de la vérification des dépendances: {e}")

    def _optimize_ui_performance(self) -> None:
        """Optimise les performances de l'interface utilisateur"""
        try:
            # Optimisation du tableau principal
            self.table.setUpdatesEnabled(False)
            self.table.setSortingEnabled(False)
            self.table.setAlternatingRowColors(True)

            # Optimisation des widgets de saisie
            for widget in [self.id_input, self.designation_input, self.valeur_input,
                          self.annee_input, self.localisation_input, self.secteur_input]:
                widget.setMaxLength(255)  # Limite la longueur pour éviter les surcharges

            # Réactivation des fonctionnalités
            self.table.setUpdatesEnabled(True)
            self.table.setSortingEnabled(True)

            # Optimisation avec le gestionnaire de performance si disponible
            if self.performance_manager:
                self.performance_manager.ui_optimizer.optimize_table_performance(self.table)  # type: ignore

            logging.info("Interface utilisateur optimisée")

        except Exception as e:
            logging.error(f"Erreur lors de l'optimisation UI: {e}")

    def open_fournisseur(self) -> None:
        if FournisseurWidget:
            self.fournisseur_win: Any = FournisseurWidget()
            self.fournisseur_win.show()
        else:
            QMessageBox.warning(self, "Module indisponible", "Le module Fournisseur n'est pas disponible.")

    def open_inventaire(self) -> None:
        if InventaireWidget:
            self.inventaire_win: Any = InventaireWidget()
            self.inventaire_win.show()
        else:
            QMessageBox.warning(self, "Module indisponible", "Le module Inventaire n'est pas disponible.")

    def open_recherche(self) -> None:
        if RechercheWidget:
            self.recherche_win: Any = RechercheWidget()
            self.recherche_win.show()
        else:
            QMessageBox.warning(self, "Module indisponible", "Le module Recherche n'est pas disponible.")

    def open_reforme(self) -> None:
        # Ouvre la fenêtre de gestion des réformes (détail commission, PV, membres, etc.)
        if ReformeWidget:
            self.reforme_win: Any = ReformeWidget()
            self.reforme_win.show()
        else:
            QMessageBox.warning(self, "Module indisponible", "Le module Réforme n'est pas disponible.")

    def open_documents(self) -> None:
        # Ouvre le module de gestion/scanner de documents (multi-module, synchronisé, code-barres)
        if DocumentWidget:
            self.doc_win: Any = DocumentWidget()
            self.doc_win.show()
        else:
            QMessageBox.warning(self, "Module indisponible", "Le module Documents n'est pas disponible.")

    def open_erp(self) -> None:
        # À compléter avec un widget ERP complet (clients, factures, achats, ventes, etc.)
        QMessageBox.information(self, "ERP", "Module ERP/Comptabilité à implémenter.")

    def open_impression(self) -> None:
        """Ouvre le module d'impression avancée compatible multi-marques et multi-formats"""
        if ImpressionWidget:
            self.impression_win: Any = ImpressionWidget()
            self.impression_win.show()
        else:
            QMessageBox.warning(self, "Module indisponible", "Le module Impression n'est pas disponible.")

    def open_calculatrice(self) -> None:
        try:
            from calculatrice import CalculatriceWidget  # type: ignore
            self.calculatrice_win: Any = CalculatriceWidget()
            self.calculatrice_win.show()
        except Exception as e:
            QMessageBox.warning(self, "Erreur", f"Module Calculatrice non disponible : {e}")

    def _setup_permissions(self) -> None:
        """Désactive les boutons selon le rôle utilisateur"""
        role = self.user_info.get("role", "")
        if role == "consultant":
            self.add_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
            self.import_btn.setEnabled(False)
            self.depreciation_btn.setEnabled(False)
            self.email_btn.setEnabled(False)
            self.param_btn.setEnabled(False)
        elif role == "gestionnaire":
            self.add_btn.setEnabled(True)
            self.export_btn.setEnabled(True)
            self.import_btn.setEnabled(True)
            self.depreciation_btn.setEnabled(True)
            self.email_btn.setEnabled(True)
            self.param_btn.setEnabled(True)
        # Pour admin, tout reste activé

    def ajouter_bien(self) -> None:
        """Ajoute un bien dans le tableau (formulaire principal)"""
        if any(not field.text().strip() for field in [
            self.id_input, self.designation_input, self.valeur_input, self.annee_input, self.localisation_input, self.secteur_input
        ]):
            QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
            return
        try:
            float(self.valeur_input.text())
            int(self.annee_input.text())
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
            return
        row = self.table.rowCount()
        self.table.insertRow(row)
        self.table.setItem(row, 0, QTableWidgetItem(self.id_input.text()))
        self.table.setItem(row, 1, QTableWidgetItem(self.designation_input.text()))
        self.table.setItem(row, 2, QTableWidgetItem(self.valeur_input.text()))
        self.table.setItem(row, 3, QTableWidgetItem(self.annee_input.text()))
        self.table.setItem(row, 4, QTableWidgetItem(self.localisation_input.text()))
        self.table.setItem(row, 5, QTableWidgetItem(self.secteur_input.text()))
        self.table.setItem(row, 6, QTableWidgetItem(self.observation_input.toPlainText()))
        self.sauvegarder_biens()
        QMessageBox.information(self, "Ajout", "Bien ajouté et sauvegardé.")

    def marquer_comme_modifie(self, *args: Any, **kwargs: Any) -> None:
        """Marque les données comme modifiées"""
        # Ignorer les paramètres inutilisés
        _ = args, kwargs
        self.modifications_non_sauvegardees = True
        self.update_title()

    def update_title(self) -> None:
        """Met à jour le titre de la fenêtre"""
        base_title: str = "Gestion des Immobilisations"
        if self.modifications_non_sauvegardees:
            self.setWindowTitle(f"{base_title} *")
        else:
            self.setWindowTitle(base_title)

    def _on_item_changed(self, item: Any) -> None:
        # Sauvegarde automatique à chaque modification, sauf lors du chargement initial
        _ = item  # Ignorer le paramètre inutilisé
        if not getattr(self, "_chargement_initial", False):
            self.sauvegarder_biens()

    def exporter_excel(self) -> None:
        # TODO: Exporter les données du tableau vers un fichier Excel
        QMessageBox.information(self, "Export", "Export Excel à implémenter.")

    def importer_csv(self) -> None:
        # TODO: Importer des biens depuis un fichier CSV
        QMessageBox.information(self, "Import", "Import CSV à implémenter.")

    def calculer_amortissement(self) -> None:
        # TODO: Calculer l'amortissement selon les paramètres
        QMessageBox.information(self, "Amortissement", "Calcul d'amortissement à implémenter.")

    def envoyer_email(self) -> None:
        # TODO: Envoyer un email avec pièce jointe
        QMessageBox.information(self, "Email", "Envoi d'email à implémenter.")

    def ouvrir_parametres(self) -> None:
        # TODO: Ouvrir une fenêtre de configuration des paramètres
        QMessageBox.information(self, "Paramètres", "Gestion des paramètres à implémenter.")

    def modifier_immobilisation(self) -> None:
        """Modifie une immobilisation existante avec vérifications"""
        if any(not field.text().strip() for field in [
            self.id_input, self.designation_input, self.valeur_input, self.annee_input,
            self.localisation_input, self.secteur_input
        ]):
            QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
            return
        try:
            valeur = float(self.valeur_input.text())
            annee = int(self.annee_input.text())
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
            return
        id_bien = self.id_input.text().strip()
        if not id_bien:
            QMessageBox.warning(self, "Erreur", "ID du bien requis")
            return

        try:
            bien = Immobilisation(
                id_bien,
                self.designation_input.text().strip(),
                valeur,
                annee,
                self.localisation_input.text().strip(),
                self.secteur_input.text().strip(),
                self.observation_input.toPlainText().strip() if self.observation_input else ""
            )
            if hasattr(self.db, "mettre_a_jour_immobilisation") and self.db.mettre_a_jour_immobilisation(bien):
                self.marquer_comme_modifie()
                self.charger_biens()  # Utiliser charger_biens au lieu de maj_affichage
                QMessageBox.information(self, "Succès", "Bien mis à jour avec succès")
            else:
                QMessageBox.warning(self, "Erreur", "Échec de la mise à jour")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification : {str(e)}")

    def supprimer_ligne_selectionnee(self) -> None:
        """Supprime la ligne sélectionnée du tableau et de la base"""
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.information(self, "Info", "Veuillez sélectionner une ligne à supprimer.")
            return
        row = selected[0].row()
        item_id = self.table.item(row, 0)
        if item_id is None:
            QMessageBox.warning(self, "Erreur", "Impossible de trouver l'ID du bien à supprimer.")
            return
        # ...existing code...

    def modifier_ligne_selectionnee(self) -> None:
        """Charge les données de la ligne sélectionnée dans le formulaire"""
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.information(self, "Info", "Veuillez sélectionner une ligne à modifier.")
            return
        row = selected[0].row()
        for col, widget in enumerate([
            self.id_input, self.designation_input, self.valeur_input, self.annee_input,
            self.localisation_input, self.secteur_input
        ]):
            item = self.table.item(row, col)
            value: str = ""
            if item is not None:
                text_attr = getattr(item, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            value = str(v)
                    except Exception:
                        value = ""
            set_text_attr = getattr(widget, "setText", None)
            if callable(set_text_attr):
                set_text_attr(value)
        # Correction : observation en colonne 6
        if self.table.columnCount() > 6:
            item_observation = self.table.item(row, 6)
            obs_value: str = ""
            if item_observation is not None:
                text_attr = getattr(item_observation, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            obs_value = str(v)
                    except Exception:
                        obs_value = ""
            set_plain_text_attr = getattr(self.observation_input, "setPlainText", None)
            if callable(set_plain_text_attr):
                set_plain_text_attr(obs_value)

    def charger_biens(self) -> None:
        """Charge les données depuis le fichier CSV au démarrage (optimisé)"""
        try:
            if not os.path.exists("biens.csv"):
                return
            with open("biens.csv", "r", encoding="utf-8") as f:
                reader = csv.reader(f)
                rows = list(reader)
                self.table.setRowCount(0)
                for row_data in rows:
                    row = self.table.rowCount()
                    self.table.insertRow(row)
                    for col, value in enumerate(row_data):
                        if col < self.table.columnCount():
                            self.table.setItem(row, col, QTableWidgetItem(value))
        except Exception as e:
            logging.error(f"Erreur lors du chargement des biens : {e}")

    def sauvegarder_biens(self) -> None:
        """Sauvegarder toutes les données du tableau dans un fichier CSV (optimisé)"""
        try:
            with open("biens.csv", "w", newline='', encoding="utf-8") as f:
                writer = csv.writer(f)
                for row in range(self.table.rowCount()):
                    row_data: List[str] = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item is not None else "")
                    writer.writerow(row_data)
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde : {e}")

    def closeEvent(self, event: Any) -> None:
        """Sauvegarde automatique avant fermeture"""
        try:
            self.sauvegarder_donnees()
            event.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur sauvegarde", f"Échec de la sauvegarde : {str(e)}")
            event.ignore()  # Empêche la fermeture si échec

    def sauvegarder_donnees(self) -> bool:
        """Sauvegarde avec système de rotation et indexation DB"""
        try:
            # Indexation pour la performance sur la table immobilisations
            cursor: Any = self.db.conn.cursor()
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_immob_id ON immobilisations(id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_immob_designation ON immobilisations(designation)")
            # Rotation des sauvegardes (5 dernières)
            for i in range(4, 0, -1):
                old_file: str = f"{self.fichier_sauvegarde}.{i}"
                new_file: str = f"{self.fichier_sauvegarde}.{i+1}"
                if os.path.exists(old_file):
                    os.rename(old_file, new_file)
            if os.path.exists(self.fichier_sauvegarde):
                os.rename(self.fichier_sauvegarde, f"{self.fichier_sauvegarde}.1")

            # Sauvegarde actuelle
            donnees: Dict[str, Any] = {
                'immobilisations': [b.__dict__ for b in self.db.lister_toutes_immobilisations()],
                'dernier_id': self.db.dernier_id_utilise(),
                'date_sauvegarde': datetime.now().isoformat()
            }
            with open(self.fichier_sauvegarde, 'w', encoding='utf-8') as f:
                json.dump(donnees, f, indent=4, ensure_ascii=False)

            self.modifications_non_sauvegardees = False
            self.update_title()
            return True

        except Exception as e:
            QMessageBox.critical(self, "Erreur sauvegarde", str(e))
            return False

    def charger_donnees(self) -> None:
        """Charge les données depuis le fichier JSON"""
        try:
            if os.path.exists(self.fichier_sauvegarde):
                with open(self.fichier_sauvegarde, 'r', encoding='utf-8') as f:
                    donnees = json.load(f)
                    self.db.initialiser_avec_donnees(donnees['immobilisations'])
                    self.charger_biens()  # Utiliser charger_biens au lieu de maj_affichage
        except Exception as e:
            QMessageBox.warning(self, "Chargement échoué", f"Chargement des données échoué : {str(e)}")

    def maj_affichage(self) -> None:
        """Met à jour l'affichage du tableau (optimisé) - Compatible avec 7 colonnes"""
        try:
            self.table.setUpdatesEnabled(False)
            self.table.setRowCount(0)
            for bien in self.db.lister_toutes_immobilisations():
                row = self.table.rowCount()
                self.table.insertRow(row)
                # Adapter aux 7 colonnes: ID, Désignation, Valeur, Année, Localisation, Secteur, Observation
                items = [
                    QTableWidgetItem(str(bien.id)),
                    QTableWidgetItem(bien.designation),
                    QTableWidgetItem(f"{bien.valeur:.2f}"),
                    QTableWidgetItem(str(bien.annee)),
                    QTableWidgetItem(bien.localisation),
                    QTableWidgetItem(bien.secteur),
                    QTableWidgetItem(getattr(bien, 'observation', ''))
                ]
                for col, item in enumerate(items):
                    if col < self.table.columnCount():  # Sécurité pour éviter les débordements
                        self.table.setItem(row, col, item)
            self.table.setUpdatesEnabled(True)
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur d'affichage : {str(e)}")

    def ajouter(self) -> None:
        """Ajoute une nouvelle immobilisation (base de données)"""
        # Vérification stricte des champs
        if any(not field.text().strip() for field in [
            self.id_input, self.designation_input, self.valeur_input, self.annee_input, self.localisation_input, self.secteur_input
        ]):
            QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
            return
        try:
            float(self.valeur_input.text())
            int(self.annee_input.text())
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Valeur ou année invalide")
            return
        # Ajoutez ici la logique métier pour insérer l'immobilisation dans la base ou la liste
        # ...existing code...

    def sauvegarde_auto(self) -> None:
        """Sauvegarde automatique si modifications"""
        if self.modifications_non_sauvegardees:
            if self.sauvegarder_donnees():
                print("Sauvegarde automatique réussie")
            else:
                print("Échec sauvegarde automatique")

    def menu_contextuel(self, position: Any) -> None:
        """Affiche un menu contextuel pour les actions rapides"""
        menu = QMenu()
        action_modifier = menu.addAction("Modifier")
        action_supprimer = menu.addAction("Supprimer")
        action_copie = menu.addAction("Copier les données")
        action = menu.exec_(self.table.viewport().mapToGlobal(position))  # type: ignore
        if action == action_modifier:
            self.modifier_ligne_selectionnee()
        elif action == action_supprimer:
            self.supprimer_ligne_selectionnee()
        elif action == action_copie:
            self.copier_ligne_selectionnee()

    def copier_ligne_selectionnee(self) -> None:
        """Copie les données de la ligne sélectionnée dans le presse-papier"""
        selected = self.table.selectedItems()
        if selected:
            row = selected[0].row()
            values: List[str] = []
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                values.append(item.text() if item is not None else "")
            QApplication.clipboard().setText('\t'.join(values))
            QMessageBox.information(self, "Copie", "Ligne copiée dans le presse-papier.")
        else:
            QMessageBox.information(self, "Info", "Veuillez sélectionner une ligne à copier.")

    def synchroniser(self) -> None:
        """Synchronise les données avec les modules externes"""
        try:
            QMessageBox.information(self, "Synchronisation", "Synchronisation des données en cours...")
            # Ici, ajouter la logique de synchronisation avec les modules
            self.charger_biens()
            QMessageBox.information(self, "Synchronisation", "Synchronisation terminée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la synchronisation : {str(e)}")

    def actualiser(self) -> None:
        """Actualise l'affichage des données"""
        try:
            self.charger_biens()
            QMessageBox.information(self, "Actualisation", "Données actualisées avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'actualisation : {str(e)}")

    def get_performance_report(self) -> Dict[str, Any]:
        """Génère un rapport de performance complet"""
        try:
            report: Dict[str, Any] = {
                'timestamp': datetime.now().isoformat(),
                'module_imports': module_manager.get_import_report(),
                'database_status': {
                    'path': self.db.db_path if hasattr(self.db, 'db_path') else 'Unknown',
                    'connected': hasattr(self.db, 'conn') and self.db.conn is not None
                },
                'ui_status': {
                    'table_rows': self.table.rowCount(),
                    'table_columns': self.table.columnCount(),
                    'modifications_pending': self.modifications_non_sauvegardees
                }
            }

            # Ajouter les métriques du gestionnaire de performance si disponible
            if self.performance_manager:
                performance_metrics = self.performance_manager.get_performance_report()
                report['system_metrics'] = performance_metrics

            return report

        except Exception as e:
            logging.error(f"Erreur lors de la génération du rapport de performance: {e}")
            return {'error': str(e)}

    def show_performance_dialog(self) -> None:
        """Affiche une boîte de dialogue avec les informations de performance"""
        try:
            report = self.get_performance_report()

            dialog = QDialog(self)
            dialog.setWindowTitle("Rapport de Performance")
            dialog.setFixedSize(600, 400)

            layout = QVBoxLayout(dialog)

            # Affichage du rapport en format texte
            text_edit = QTextEdit()
            text_edit.setReadOnly(True)

            report_text: str = "=== RAPPORT DE PERFORMANCE ===\n\n"
            report_text += f"Timestamp: {report.get('timestamp', 'N/A')}\n\n"

            # Modules
            module_info = report.get('module_imports', {})
            report_text += f"Modules chargés: {module_info.get('modules_loaded', 0)}\n"
            report_text += f"Modules échoués: {module_info.get('modules_failed', 0)}\n\n"

            # Base de données
            db_info = report.get('database_status', {})
            report_text += f"Base de données: {db_info.get('path', 'N/A')}\n"
            report_text += f"Connectée: {'Oui' if db_info.get('connected', False) else 'Non'}\n\n"

            # Interface
            ui_info = report.get('ui_status', {})
            report_text += f"Lignes dans le tableau: {ui_info.get('table_rows', 0)}\n"
            report_text += f"Modifications en attente: {'Oui' if ui_info.get('modifications_pending', False) else 'Non'}\n\n"

            # Métriques système si disponibles
            if 'system_metrics' in report:
                metrics = report['system_metrics']
                if 'metrics' in metrics:
                    sys_metrics = metrics['metrics']
                    report_text += "=== MÉTRIQUES SYSTÈME ===\n"
                    report_text += f"CPU: {sys_metrics.get('cpu_percent', 'N/A')}%\n"
                    report_text += f"Mémoire: {sys_metrics.get('memory_percent', 'N/A')}%\n"
                    report_text += f"Mémoire app: {sys_metrics.get('app_memory_mb', 'N/A')} MB\n"
                    report_text += f"Threads: {sys_metrics.get('threads_count', 'N/A')}\n"

            text_edit.setPlainText(report_text)
            layout.addWidget(text_edit)

            # Boutons
            btn_layout: QHBoxLayout = QHBoxLayout()
            close_btn: QPushButton = QPushButton("Fermer")
            export_btn: QPushButton = QPushButton("Exporter")

            close_btn.clicked.connect(dialog.accept)
            export_btn.clicked.connect(lambda: self._export_performance_report(report))

            btn_layout.addWidget(export_btn)
            btn_layout.addWidget(close_btn)
            layout.addLayout(btn_layout)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'affichage du rapport: {str(e)}")

    def _export_performance_report(self, report: Dict[str, Any]) -> None:
        """Exporte le rapport de performance en JSON"""
        try:
            filename: str = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=4, ensure_ascii=False)
            QMessageBox.information(self, "Export", f"Rapport exporté: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")

class LoginDialog(QDialog):
    def __init__(self, auth_manager: Any) -> None:
        super().__init__()
        self.auth_manager = auth_manager
        self.setWindowTitle("Authentification")
        self.setFixedSize(400, 250)
        self.main_layout = QVBoxLayout()
        self.form = QFormLayout()
        self.username_input = QLineEdit()
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.form.addRow("Nom d'utilisateur:", self.username_input)
        self.form.addRow("Mot de passe:", self.password_input)
        self.login_button: QPushButton = QPushButton("Connexion")
        self.login_button.clicked.connect(self.authenticate)
        self.main_layout.addLayout(self.form)
        self.main_layout.addWidget(self.login_button)
        self.setLayout(self.main_layout)
        self.user_info = None

    def authenticate(self) -> None:
        username = self.username_input.text()
        password = self.password_input.text()
        if not username or not password:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir un nom d'utilisateur et un mot de passe")
            return
        user_info, error = self.auth_manager.authenticate(username, password)
        if error:
            QMessageBox.critical(self, "Erreur", error)
        else:
            self.user_info = user_info
            self.accept()



class UserManagementDialog(QDialog):
    def __init__(self, auth_manager: Any, parent: Optional[Any] = None) -> None:
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.setWindowTitle("Gestion des utilisateurs")
        self.setFixedSize(500, 400)
        self.main_layout = QVBoxLayout()
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels(["ID", "Nom d'utilisateur", "Rôle", "Nom complet", "Statut"])
        self._load_users()
        self.btn_add: QPushButton = QPushButton("Ajouter")
        self.btn_edit: QPushButton = QPushButton("Modifier")
        self.btn_toggle: QPushButton = QPushButton("Activer/Désactiver")
        self.btn_add.clicked.connect(self._add_user)
        self.btn_edit.clicked.connect(self._edit_user)
        self.btn_toggle.clicked.connect(self._toggle_user)
        btn_layout: QHBoxLayout = QHBoxLayout()
        btn_layout.addWidget(self.btn_add)
        btn_layout.addWidget(self.btn_edit)
        btn_layout.addWidget(self.btn_toggle)
        self.main_layout.addWidget(self.users_table)
        self.main_layout.addLayout(btn_layout)
        self.setLayout(self.main_layout)

    def _toggle_user(self) -> None:
        """Active ou désactive l'utilisateur sélectionné"""
        selected = self.users_table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un utilisateur à activer/désactiver")
            return
        row = selected[0].row()
        item_id = self.users_table.item(row, 0)
        item_statut = self.users_table.item(row, 4)
        if item_id is None or item_statut is None:
            QMessageBox.warning(self, "Erreur", "Impossible de lire les informations de l'utilisateur sélectionné.")
            return
        user_id = item_id.text()
        statut = item_statut.text()
        new_status = 0 if statut == "Actif" else 1
        try:
            cursor = self.auth_manager.conn.cursor()
            cursor.execute(
                "UPDATE users SET is_active=? WHERE id=?",
                (new_status, user_id)
            )
            self.auth_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Statut de l'utilisateur mis à jour")
            self._load_users()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def _load_users(self) -> None:
        """Charge la liste des utilisateurs avec indexation pour la performance"""
        cursor = self.auth_manager.conn.cursor()
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)")
        users = cursor.execute('''
            SELECT id, username, role, full_name, is_active FROM users
        ''').fetchall()
        self.users_table.setRowCount(len(users))
        for row, user in enumerate(users):
            for col, data in enumerate(user):
                item = QTableWidgetItem(str(data))
                if col == 4:
                    item.setText("Actif" if int(data) else "Inactif")
                self.users_table.setItem(row, col, item)

    def _add_user(self) -> None:
        """Ajoute un nouvel utilisateur"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Nouvel utilisateur")
        form = QFormLayout()
        username = QLineEdit()
        password = QLineEdit()
        password.setEchoMode(QLineEdit.EchoMode.Password)
        confirm_pass = QLineEdit()
        confirm_pass.setEchoMode(QLineEdit.EchoMode.Password)
        role = QComboBox()
        role.addItems(["admin", "gestionnaire", "consultant"])
        full_name = QLineEdit()
        form.addRow("Nom d'utilisateur:", username)
        form.addRow("Mot de passe:", password)
        form.addRow("Confirmer mot de passe:", confirm_pass)
        form.addRow("Rôle:", role)
        form.addRow("Nom complet:", full_name)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        layout = QVBoxLayout()
        layout.addLayout(form)
        layout.addWidget(btn_box)
        dialog.setLayout(layout)
        if dialog.exec() == 1:
            # Vérification stricte des champs
            if any(not field.text().strip() for field in [username, password, confirm_pass, full_name]) or role.currentText() == "":
                QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
                return
            if password.text() != confirm_pass.text():
                QMessageBox.warning(self, "Erreur", "Les mots de passe ne correspondent pas")
                return
            if len(password.text()) < 10:
                QMessageBox.warning(self, "Erreur", "Le mot de passe doit faire au moins 10 caractères")
                return
            try:
                success = self.auth_manager.create_user(
                    username.text(),
                    password.text(),
                    role.currentText(),
                    full_name.text()
                )
                if success:
                    QMessageBox.information(self, "Succès", "Utilisateur créé avec succès")
                    self._load_users()
                else:
                    QMessageBox.warning(self, "Erreur", "Ce nom d'utilisateur existe déjà")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", str(e))

    def _edit_user(self) -> None:
        """Modifie les informations d'un utilisateur sélectionné"""
        selected = self.users_table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un utilisateur à modifier")
            return
        row = selected[0].row()
        # Vérification stricte pour éviter None
        item_id = self.users_table.item(row, 0)
        item_username = self.users_table.item(row, 1)
        item_role = self.users_table.item(row, 2)
        item_full_name = self.users_table.item(row, 3)
        if any(x is None for x in [item_id, item_username, item_role, item_full_name]):
            QMessageBox.warning(self, "Erreur", "Impossible de lire les informations de l'utilisateur sélectionné.")
            return
        # Maintenant on sait que les items ne sont pas None
        user_id = item_id.text() if item_id else ""
        username = item_username.text() if item_username else ""
        role = item_role.text() if item_role else ""
        full_name = item_full_name.text() if item_full_name else ""

        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier utilisateur")
        form = QFormLayout()
        username_edit = QLineEdit(username)
        username_edit.setReadOnly(True)
        role_edit = QComboBox()
        role_edit.addItems(["admin", "gestionnaire", "consultant"])
        role_edit.setCurrentText(role)
        full_name_edit = QLineEdit(full_name)
        form.addRow("Nom d'utilisateur:", username_edit)
        form.addRow("Rôle:", role_edit)
        form.addRow("Nom complet:", full_name_edit)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        layout = QVBoxLayout()
        layout.addLayout(form)
        layout.addWidget(btn_box)
        dialog.setLayout(layout)
        if dialog.exec() == 1:
            # Vérification stricte des champs
            if not full_name_edit.text().strip() or role_edit.currentText() == "":
                QMessageBox.warning(self, "Erreur", "Tous les champs doivent être remplis")
                return
            try:
                cursor = self.auth_manager.conn.cursor()
                cursor.execute(
                    "UPDATE users SET role=?, full_name=? WHERE id=?",
                    (role_edit.currentText(), full_name_edit.text(), user_id)
                )
                self.auth_manager.conn.commit()
                QMessageBox.information(self, "Succès", "Utilisateur modifié avec succès")
                self._load_users()
            except Exception as e:
                QMessageBox.critical(self, "Erreur", str(e))

    # Pourquoi la méthode `modifier_immobilisation` peut être masquée :
    # En Python, si vous définissez plusieurs fois une méthode du même nom dans une même classe,
    # seule la dernière définition sera utilisée. Les précédentes sont écrasées (masquées).
    # Cela peut arriver par erreur lors de copier-coller ou de refactoring.
    # Pour éviter des bugs ou des comportements inattendus, gardez UNE SEULE définition de chaque méthode par classe.

class MainWindow(QWidget):
    def __init__(self, user_info: Any, auth_manager: Any) -> None:
        super().__init__()
        self.user_info = user_info
        self.auth_manager = auth_manager
        self.setWindowTitle(f"Gestion Immobilisations - Connecté en tant que {user_info['full_name']}")
        self.db_conn = sqlite3.connect("immobilisations.db", isolation_level=None)
        self.db_conn.execute("PRAGMA journal_mode = WAL")
        self.db_conn.execute("PRAGMA synchronous = NORMAL")
        self.db_conn.execute("PRAGMA cache_size = -10000")
        self._init_ui()
        # self._setup_permissions()  # Supprimé car la gestion des permissions est déplacée dans ImmobilisationMainWindow

    def _init_ui(self) -> None:
        main_layout = QVBoxLayout()
        self.status_bar = QLabel()
        self.status_bar.setText(f"Connecté en tant que: {self.user_info['full_name']} ({self.user_info['role']})")
        main_layout.addWidget(self.status_bar)
        self.setLayout(main_layout)

    def closeEvent(self, event: Any) -> None:
        self.db_conn.close()
        event.accept()

# Au démarrage, proposer la sélection de la base de données
def select_database() -> str:
    config = load_config()
    from PySide6.QtWidgets import QInputDialog
    dbs = config.get("DATABASES", ["immobilisations.db"])
    db, ok = QInputDialog.getItem(None, "Sélection de la base de données", "Choisissez une base :", dbs, 0, False)
    if ok:
        config["DEFAULT_DB"] = db
        save_config(config)
        return db
    return config.get("DEFAULT_DB", "immobilisations.db")

# Paramètres et configuration avancés pour le logiciel (optimisé, multilingue, IA, Big Data)
class AppConfig:
    DEFAULTS: Dict[str, Any] = {
        "langue": "fr",
        "theme": "clair",
        "sauvegarde_auto": True,
        "sauvegarde_intervalle_min": 5,
        "synchronisation_auto": True,
        "synchronisation_intervalle_min": 10,
        "performance_mode": "optimise",
        "bigdata_mode": True,
        "ia_modele": "DeepSeek",
        "multi_langues": ["fr", "en", "ar", "es", "de", "zh", "ru"],
        "api_github": "https://api.github.com",
        "api_wikipedia": "https://fr.wikipedia.org/api/rest_v1/page/summary/",
        "derniere_version": "1.0.0",
        "modules_actifs": [
            "inventaire", "fournisseur", "recherche", "reforme", "document", "impression", "erp", "calculatrice"
        ],
        "historique_max": 1000,
        "modele_ia_disponibles": ["DeepSeek", "OpenAI", "GoogleGemini", "Mistral", "Llama2"],
        "modele_ia_defaut": "DeepSeek",
        "cloud_sync": True,
        "cloud_provider": "aws",
        "cloud_bucket": "immob-sync",
        "crypto_mode": "fernet",
        "log_level": "INFO"
    }

    def __init__(self, config_file: str = "config.json") -> None:
        self.config_file = config_file
        self.config = self.load()
        self.synchroniser_config()

    def load(self) -> Dict[str, Any]:
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    data: Dict[str, Any] = json.load(f)
                    for k, v in self.DEFAULTS.items():
                        if k not in data:
                            data[k] = v
                    return data
        except Exception as e:
            logging.warning(f"Erreur chargement config : {e}")
        return self.DEFAULTS.copy()

    def save(self) -> None:
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Erreur sauvegarde config : {e}")

    def get(self, key: str, default: Any = None) -> Any:
        return self.config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        self.config[key] = value
        self.save()

    def get_langues(self) -> List[str]:
        return self.config.get("multi_langues", ["fr"])

    def get_theme(self) -> str:
        return self.config.get("theme", "clair")

    def get_ia_modele(self) -> str:
        return self.config.get("ia_modele", "DeepSeek")

    def get_modules(self) -> List[str]:
        return self.config.get("modules_actifs", [])

    def synchroniser_config(self) -> None:
        # Synchronisation professionnelle des paramètres avec les modules principaux
        # Peut être enrichi pour synchroniser avec une base centrale, cloud, etc.
        logging.info("Synchronisation des paramètres de configuration avec tous les modules.")
        # ... ici, synchroniser avec les modules si besoin ...

    def recherche_github(self, mot_cle: str) -> List[str]:
        """Recherche sur GitHub pour enrichir le logiciel avec les dernières technos"""
        try:
            import requests  # type: ignore
            url: str = f"{self.get('api_github')}/search/repositories?q={mot_cle}&sort=stars"
            resp: Any = requests.get(url, timeout=10)
            if resp.status_code == 200:
                data: Dict[str, Any] = resp.json()
                return [item["full_name"] for item in data.get("items", [])[:5]]
        except ImportError:
            logging.warning("Module requests non disponible pour la recherche GitHub")
        except Exception as e:
            logging.warning(f"Erreur recherche GitHub : {e}")
        return []

    def enrichir_par_wikipedia(self, sujet: str, langue: str = "fr") -> str:
        """Recherche sur Wikipedia pour enrichir les paramètres ou la doc"""
        try:
            import requests  # type: ignore
            url: str = f"https://{langue}.wikipedia.org/api/rest_v1/page/summary/{sujet}"
            resp: Any = requests.get(url, timeout=10)
            if resp.status_code == 200:
                data: Dict[str, Any] = resp.json()
                return data.get("extract", "")
        except ImportError:
            logging.warning("Module requests non disponible pour Wikipedia")
        except Exception as e:
            logging.warning(f"Erreur Wikipedia : {e}")
        return ""

    def synchroniser_cloud(self) -> str:
        """Synchronisation cloud (exemple AWS S3, à adapter selon provider)"""
        if not self.get("cloud_sync"):
            return "Sync cloud désactivée"
        # Placeholder : à compléter avec boto3 ou autre SDK cloud
        return "Synchronisation cloud simulée"

    def appliquer_performance(self) -> None:
        """Optimisation des performances (cache, index, mode DB, etc.)"""
        logging.info("Mode performance appliqué : " + self.get("performance_mode", "optimise"))

    def set_langue(self, langue: str) -> None:
        self.set("langue", langue)

    def set_theme(self, theme: str) -> None:
        self.set("theme", theme)

    def set_ia_modele(self, modele: str) -> None:
        self.set("ia_modele", modele)

# Détection et adaptation pour Windows 10/11

def is_windows_compatible() -> None:
    if platform.system() != "Windows":
        QMessageBox.critical(None, "Erreur Système", "Ce logiciel est conçu pour Windows 10/11 uniquement.")
        sys.exit(1)
    version = platform.version()
    major = int(platform.release())
    if major < 10:
        QMessageBox.critical(None, "Erreur Système", "Windows 10 ou supérieur requis.")
        sys.exit(1)
    # Optionnel : adaptation UI pour Windows 11
    if "10.0.22000" in version or major >= 11:
        logging.info("Mode Windows 11 détecté : adaptation graphique possible.")
    else:
        logging.info("Mode Windows 10 détecté.")

def check_system_dependencies() -> Dict[str, Any]:
    """Vérifie les dépendances système et retourne un rapport"""
    dependencies_report: Dict[str, Any] = {
        'python_version': platform.python_version(),
        'platform': platform.platform(),
        'missing_modules': [],
        'available_modules': [],
        'performance_info': {}
    }

    # Liste des modules requis avec leurs versions minimales
    required_modules: Dict[str, Optional[str]] = {
        'PySide6': '6.0.0',
        'sqlite3': None,  # Module standard
        'json': None,     # Module standard
        'csv': None,      # Module standard
        'cryptography': '3.0.0',
        'psutil': '5.0.0'
    }

    for module_name, min_version in required_modules.items():
        try:
            module = __import__(module_name)
            dependencies_report['available_modules'].append({
                'name': module_name,
                'version': getattr(module, '__version__', 'Unknown'),
                'required_version': min_version
            })
        except ImportError:
            dependencies_report['missing_modules'].append(module_name)

    # Informations de performance système
    try:
        import psutil  # type: ignore
        dependencies_report['performance_info'] = {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'disk_free_gb': round(psutil.disk_usage('.').free / (1024**3), 2)
        }
    except ImportError:
        dependencies_report['performance_info'] = {'error': 'psutil not available'}

    return dependencies_report

def main() -> None:
    is_windows_compatible()

    # Vérification des dépendances au démarrage
    deps_report = check_system_dependencies()
    logging.info(f"Vérification des dépendances: {len(deps_report['available_modules'])} modules disponibles, "
                f"{len(deps_report['missing_modules'])} manquants")

    if deps_report['missing_modules']:
        logging.warning(f"Modules manquants: {', '.join(deps_report['missing_modules'])}")

    logging.info("Démarrage du logiciel de gestion immobilière (interface graphique PySide6)...")
    try:
        # Chargement et application de la configuration avancée
        app_config = AppConfig()
        config = app_config.config
        # Application du thème global
        app = QApplication(sys.argv)
        if config.get("theme") == "sombre":
            try:
                import qdarkstyle  # type: ignore
                app.setStyleSheet(qdarkstyle.load_stylesheet())  # type: ignore
            except ImportError:
                logging.warning("qdarkstyle non disponible, utilisation du thème par défaut")
                # Appliquer un thème sombre basique
                app.setStyleSheet("""
                    QMainWindow { background-color: #2b2b2b; color: #ffffff; }
                    QWidget { background-color: #2b2b2b; color: #ffffff; }
                    QPushButton { background-color: #404040; color: #ffffff; border: 1px solid #555555; }
                    QPushButton:hover { background-color: #505050; }
                """)
        # Multi-langue (exemple)
        langue = config.get("langue", "fr")
        if langue == "en":
            QApplication.setApplicationDisplayName("Real Estate Asset Management")
        elif langue == "ar":
            QApplication.setApplicationDisplayName("إدارة الأصول العقارية")
        elif langue == "zh":
            QApplication.setApplicationDisplayName("资产管理系统")
        else:
            QApplication.setApplicationDisplayName("Gestion des Immobilisations")

        # Recherche et enrichissement automatique (Big Data, IA, GitHub, Wikipedia)
        logging.info("Recherche des dernières technos sur GitHub et Wikipedia...")
        top_repos = app_config.recherche_github("asset management")
        doc_amort = app_config.enrichir_par_wikipedia("Amortissement", langue)
        logging.info(f"Top projets GitHub : {top_repos}")
        logging.info(f"Résumé Wikipedia Amortissement : {doc_amort[:200]}...")

        # Synchronisation cloud et optimisation
        app_config.synchroniser_cloud()
        app_config.appliquer_performance()

        db_path = select_database()
        db_connection = DatabaseConnection(db_path)
        auth_manager = AuthManager()
        login_dialog = LoginDialog(auth_manager)
        if login_dialog.exec() != 1:
            return

        user_info = login_dialog.user_info
        window = ImmobilisationMainWindow(db_connection, user_info)
        window.show()
        sys.exit(app.exec())

    except Exception as e:
        logging.error(f"Une erreur est survenue dans main: {e}")
    finally:
        # Nettoyage et déplacement du fichier de configuration
        try:
            current_file = os.path.abspath(__file__)
            src_config = os.path.join(os.path.dirname(current_file), 'config.json')
            dest_config = os.path.join(os.getcwd(), 'config.json')
            if os.path.exists(src_config):
                shutil.move(src_config, dest_config)
                logging.info("Fichier de configuration déplacé dans le répertoire courant.")
        except Exception as e:
            logging.error(f"Échec du déplacement du fichier de configuration: {e}")

if __name__ == "__main__":
    main()
