<!-- Generated by SpecStory -->

# /fix Le type de « append » est partiellement incon (2025-06-08 11:22:44)

_**User**_

/fix Le type de « append » est partiellement inconnu
  Le type de "append" est "(object: Unknown, /) -> None"

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème de type partiellement inconnu pour `append` vient probablement d'une absence d'annotation de type pour la variable `values`. Pour aider les outils de typage (comme Pyright ou MyPy), il est recommandé d'annoter explicitement la variable `values` comme une liste de chaînes (`list[str]`).

Voici le correctif à appliquer :

````python
# ...existing code...
for row in range(self.table.rowCount()):
    values: list[str] = []
    for col in range(self.table.columnCount()):
        item = self.table.item(row, col)
        values.append(item.text() if item else "")
    f.write("\t".join(values) + "\n")
# ...existing code...
````

Ce changement explicite le type de `values` et résout l'avertissement de typage.

---

