from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    SV<PERSON><PERSON><PERSON><PERSON>, SGSExclusive, DISPID_SREmulateRecognition,
    DISPID_SGRClear, DISPID_SLWsItem, IUnknown, eLEXTYPE_PRIVATE11,
    ISpeechPhraseElement, SDKLCurrentConfig, SGRSTTEpsilon,
    DISPID_SASCurrentSeekPosition, SVPNormal, DISPID_SOTRemove,
    DISPID_SOTCSetId, ISpeechAudioBufferInfo, SpInProcRecoContext,
    DISPID_SGRsDynamic, DISPID_SPRuleEngineConfidence,
    SpeechCategoryRecoProfiles, SAFT12kHz8BitStereo, DISPID_SOTsCount,
    DISPID_SPEPronunciation, e<PERSON><PERSON><PERSON>E_RESERVED6, SAFTNoAssignedFormat,
    Sp<PERSON><PERSON>Token, DISPID_SVEPhoneme, SPPS_RESERVED4,
    SAFTADPCM_22kHzStereo, eLEXTYPE_PRIVATE10, SAFT44kHz8BitMono,
    ISpeechFileStream, SPEI_RECOGNITION, eWORDTYPE_ADDED,
    SPSHORTCUTPAIR, DISPID_SRGSetTextSelection, ISpLexicon,
    DISPID_SPEActualConfidence, SRATopLevel, DISPID_SPIReplacements,
    DISPID_SDKDeleteValue, SPGS_ENABLED, LONG_PTR,
    SAFTGSM610_44kHzMono, SpeechAudioVolume,
    DISPIDSPTSI_SelectionLength, SVP_8, ISpeechRecoResult2,
    SPEI_TTS_PRIVATE, DISPID_SFSOpen, SAFT16kHz8BitStereo,
    DISPID_SRCRequestedUIType, SpeechTokenIdUserLexicon,
    DISPID_SRSCurrentStreamPosition, SpeechGrammarTagWildcard,
    SP_VISEME_0, SRESoundStart, SAFT32kHz8BitMono, SRTSMLTimeout,
    DISPID_SRProfile, SPPHRASEELEMENT, DISPID_SABIMinNotification,
    SRTStandard, DISPID_SMSAMMHandle, SpeechTokenValueCLSID,
    SPSMF_SAPI_PROPERTIES, DISPID_SWFEChannels,
    DISPID_SRGCmdLoadFromObject, SpeechRecoProfileProperties,
    SAFTCCITT_ALaw_22kHzMono, ISpeechRecoResultTimes, SSTTTextBuffer,
    SPEI_VOICE_CHANGE, DISPID_SRCVoicePurgeEvent, SpSharedRecognizer,
    SPRST_INACTIVE_WITH_PURGE, SREStreamEnd,
    SAFTCCITT_ALaw_11kHzStereo, eLEXTYPE_PRIVATE2,
    SPINTERFERENCE_NOSIGNAL, DISPID_SRCreateRecoContext, SVP_21,
    SAFT11kHz8BitMono, DISPID_SRRGetXMLResult, DISPID_SGRsCount,
    DISPID_SDKEnumKeys, DISPID_SRAudioInputStream,
    SPINTERFERENCE_TOOSLOW, SpTextSelectionInformation,
    DISPID_SVSInputSentenceLength, ISpeechRecoContext,
    DISPID_SRCEHypothesis, DISPID_SADefaultFormat, wireHWND,
    SPSHT_Unknown, SPFM_CREATE_ALWAYS, SRAInterpreter,
    DISPID_SPRuleParent, DISPID_SWFEAvgBytesPerSec,
    DISPID_SDKSetStringValue, SpCompressedLexicon,
    DISPID_SRCEAdaptation, DISPID_SPANumberOfElementsInResult,
    SPAR_Medium, SGSDisabled, SPEI_INTERFERENCE, DISPID_SDKEnumValues,
    SAFT24kHz16BitMono, SGDSActiveWithAutoPause, SP_VISEME_14,
    SSFMOpenForRead, ISpeechGrammarRuleStateTransition,
    DISPID_SRGetRecognizers, DISPID_SPRuleNumberOfElements,
    __MIDL___MIDL_itf_sapi_0000_0020_0001, DISPID_SRIsShared,
    ISpeechPhraseReplacements, SAFT48kHz8BitStereo, STCRemoteServer,
    DISPID_SPEAudioSizeTime, SPDKL_DefaultLocation, SVEVoiceChange,
    STSF_FlagCreate, SREHypothesis, SAFT22kHz16BitMono, ISpeechAudio,
    ISpeechBaseStream, DISPID_SVDisplayUI, SAFT16kHz16BitMono,
    SAFTGSM610_8kHzMono, ISpeechPhraseRules, SP_VISEME_18,
    DISPID_SRIsUISupported, SPSHT_OTHER, SAFTGSM610_22kHzMono,
    VARIANT, SAFT11kHz8BitStereo, SDA_Two_Trailing_Spaces,
    ISpGrammarBuilder, ISpEventSource, DISPID_SVEBookmark,
    SVSFParseSapi, DISPID_SRRGetXMLErrorInfo, IServiceProvider,
    DISPID_SLAddPronunciation, SDTAlternates, UINT_PTR,
    DISPID_SRSetPropertyNumber, SPPS_LMA, SRADefaultToActive,
    DISPID_SPRText, SVP_20, DISPID_SOTDataKey, eLEXTYPE_RESERVED7,
    DISPID_SVGetAudioInputs, SRSActive, SGDisplay, ISpeechPhraseRule,
    _LARGE_INTEGER, SP_VISEME_21, DISPID_SRCERequestUI,
    SPSERIALIZEDRESULT, SECLowConfidence, SWTAdded,
    DISPID_SAEventHandle, DISPID_SRRTTickCount, DISPID_SBSFormat,
    SP_VISEME_10, SECNormalConfidence, SRERecognition,
    DISPID_SRSCurrentStreamNumber, SVF_Emphasis,
    DISPID_SRCCmdMaxAlternates, DISPID_SWFEBlockAlign,
    DISPID_SGRsItem, SECFDefault, DISPID_SGRsCommit,
    DISPID_SOTCategory, ISpRecoGrammar2, DISPID_SRGDictationLoad,
    DISPID_SPPChildren, SP_VISEME_20, SECFNoSpecialChars,
    DISPID_SLPLangId, ISpStreamFormatConverter, DISPID_SPRuleChildren,
    SPPS_RESERVED2, SPCS_ENABLED, SPDKL_CurrentConfig,
    DISPID_SPPParent, ISpeechCustomStream, SPCT_SUB_COMMAND,
    SPEI_MAX_TTS, SPWORDPRONUNCIATIONLIST, SGDSInactive,
    DISPID_SRCEPropertyStringChange, SDTPronunciation,
    SpNullPhoneConverter, SP_VISEME_15, DISPID_SLPPhoneIds,
    SAFT22kHz8BitMono, SAFTDefault, SPAS_PAUSE, SVP_2,
    DISPID_SVGetProfiles, SAFTCCITT_uLaw_8kHzStereo,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, SLTApp,
    SVEPhoneme, __MIDL_IWinTypes_0009, SGLexicalNoSpecialChars,
    SAFTADPCM_22kHzMono, DISPID_SVIsUISupported, SGRSTTWord,
    DISPID_SPAsItem, SDTAll, ISpeechRecoResultDispatch,
    DISPID_SRGCmdSetRuleIdState, SPINTERFERENCE_TOOQUIET,
    SPAUDIOSTATUS, DISPID_SRCRetainedAudio, SPXRO_SML,
    SVSFParseAutodetect, SRAExport, DISPIDSPTSI_ActiveLength,
    SpeechPropertyResourceUsage, DISPID_SRGState,
    DISPID_SRGDictationSetState, SPSHT_NotOverriden,
    DISPID_SRGetFormat, SECFIgnoreCase, SVP_12, DISPID_SPPsItem,
    _RemotableHandle, STSF_LocalAppData, DISPID_SAVolume,
    DISPID_SVSkip, SPEI_MIN_SR, DISPID_SFSClose, ISpResourceManager,
    DISPID_SABIEventBias, SPPHRASEREPLACEMENT, ISpeechXMLRecoResult,
    DISPID_SRGRules, SpeechUserTraining, SVESentenceBoundary,
    SVEPrivate, SGRSTTDictation, ISpeechWaveFormatEx,
    eLEXTYPE_PRIVATE16, SpStream, SPINTERFERENCE_TOOLOUD,
    DISPID_SPEsCount, dispid, DISPID_SPPEngineConfidence,
    SPRST_INACTIVE, SSSPTRelativeToCurrentPosition, ISpNotifySink,
    DISPID_SPIAudioSizeBytes, SPEI_RESERVED3, DISPID_SPAsCount,
    SAFT44kHz8BitStereo, eLEXTYPE_PRIVATE6, ISpeechRecoResult,
    SPPROPERTYINFO, SVP_9, SWPKnownWordPronounceable,
    DISPID_SRCEAudioLevel, DISPID_SBSRead, DISPID_SDKGetBinaryValue,
    SPEI_HYPOTHESIS, DISPID_SLGetPronunciations, SPWT_PRONUNCIATION,
    SPSSuppressWord, DISPID_SASNonBlockingIO, SPPS_Noncontent,
    ISpeechPhraseInfo, SpLexicon, DISPID_SPEDisplayAttributes,
    DISPID_SRCEPropertyNumberChange, SP_VISEME_13, SpeechAllElements,
    DISPID_SRCCreateResultFromMemory, SSTTWildcard,
    SpeechCategoryVoices, SWPUnknownWordPronounceable, SPPS_RESERVED3,
    SBOPause, SPAS_STOP, DISPID_SRSAudioStatus, DISPID_SVSPhonemeId,
    SPEI_RESERVED1, DISPID_SPERetainedSizeBytes, SPPS_RESERVED1,
    ISpObjectToken, DISPID_SRSSupportedLanguages, SPAO_RETAIN_AUDIO,
    ISpeechTextSelectionInformation, SpShortcut,
    DISPID_SLGenerationId, SPSHT_EMAIL, SPFM_CREATE, _lcid,
    DISPID_SPRuleFirstElement, DISPID_SRSNumberOfActiveRules,
    ISpeechLexiconWords, ISpeechRecognizerStatus,
    ISpeechPhraseProperty, DISPID_SRGCommit, SVEEndInputStream,
    ISpPhoneticAlphabetConverter, SPCT_SLEEP, COMMETHOD,
    SPEI_SR_BOOKMARK, DISPID_SRCERecognitionForOtherContext,
    DISPID_SOTDisplayUI, ISpeechVoiceStatus,
    DISPID_SVSInputWordLength, DISPID_SVEVoiceChange,
    DISPID_SRRSaveToMemory, DISPID_SGRAddState, SGRSTTWildcard,
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C, DISPID_SOTsItem,
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS, SPSEMANTICERRORINFO,
    DISPID_SVSInputSentencePosition, DISPID_SPERetainedStreamOffset,
    DISPID_SOTGetDescription, SP_VISEME_16, SPEI_SR_RETAINEDAUDIO,
    DISPID_SPIAudioStreamPosition, SPDKL_LocalMachine, SLODynamic,
    DISPID_SPILanguageId, SAFTADPCM_8kHzMono, eLEXTYPE_PRIVATE8,
    SWTDeleted, SpeechCategoryRecognizers, SVP_4, SAFT12kHz8BitMono,
    SP_VISEME_2, SPWORD, DISPID_SRGSetWordSequenceData, SRCS_Enabled,
    eLEXTYPE_PRIVATE9, SDA_One_Trailing_Space,
    DISPID_SOTGetStorageFileName, SDTAudio, SAFT32kHz8BitStereo,
    DISPID_SRRAlternates, SP_VISEME_8, eLEXTYPE_RESERVED9, SPAR_Low,
    Speech_Max_Pron_Length, DISPID_SPEs_NewEnum, DISPID_SOTCId,
    SPVPRI_OVER, SRAImport, DISPID_SPIProperties,
    SpeechEngineProperties, SITooSlow, ISpeechGrammarRule,
    SRSEIsSpeaking, DISPID_SPPId, ISpXMLRecoResult,
    SpeechCategoryAudioOut, SpPhoneticAlphabetConverter,
    SpUnCompressedLexicon, IEnumString, DISPID_SVGetAudioOutputs,
    Speech_StreamPos_RealTime, SAFTCCITT_uLaw_44kHzMono,
    ISpeechLexiconPronunciation, STCAll, SVP_5, DISPID_SDKOpenKey,
    DISPID_SLWType, SVSFPurgeBeforeSpeak, SDKLDefaultLocation,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, DISPID_SRGCmdSetRuleState,
    SpeechGrammarTagDictation, ISpNotifyTranslator, ISpPhoneConverter,
    SpeechTokenKeyFiles, DISPID_SPRulesCount, DISPID_SGRName,
    SPGS_DISABLED, SPSMF_UPS, SRTEmulated, HRESULT, DISPID_SLWsCount,
    SPAR_Unknown, ISpObjectTokenCategory, DISPID_SPCIdToPhone,
    SP_VISEME_9, SPINTERFERENCE_LATENCY_TRUNCATE_END,
    DISPID_SDKSetLongValue, DISPID_SGRSTRule, SREFalseRecognition,
    SP_VISEME_7, DISPID_SRSClsidEngine, DISPID_SWFEBitsPerSample,
    __MIDL___MIDL_itf_sapi_0000_0020_0002, eLEXTYPE_USER,
    SRTExtendableParse, DISPID_SGRSAddRuleTransition, SVP_16,
    DISPID_SLWWord, DISPID_SVSRunningState, SPVPRI_NORMAL,
    DISPID_SVSInputWordPosition, DISPID_SDKSetBinaryValue, SGSEnabled,
    DISPID_SOTRemoveStorageFileName, ISpeechGrammarRuleState,
    SGRSTTTextBuffer, SpeechPropertyNormalConfidenceThreshold,
    SpMemoryStream, DISPID_SPARecoResult, DISPID_SDKGetlongValue,
    DISPID_SAStatus, Speech_Max_Word_Length,
    SpeechCategoryPhoneConverters, DISPID_SASState, SpFileStream,
    SAFT24kHz8BitStereo, SpMMAudioIn, SRSActiveAlways,
    SGDSActiveUserDelimited, DISPID_SGRSTNextState,
    DISPID_SGRAddResource, DISPID_SGRSTWeight, SLOStatic,
    ISpeechPhraseReplacement, DISPID_SRGetPropertyString,
    DISPID_SVStatus, ISpeechPhoneConverter, eLEXTYPE_RESERVED8,
    SECFEmulateResult, SAFT22kHz16BitStereo, DISPID_SLGetWords,
    ISpeechRecognizer, GUID, SPINTERFERENCE_NONE,
    SpeechPropertyResponseSpeed, SPTEXTSELECTIONINFO,
    DISPID_SOTCEnumerateTokens, SECFIgnoreWidth,
    DISPID_SRRDiscardResultInfo, STSF_CommonAppData, SP_VISEME_17,
    SpeechTokenKeyAttributes, SAFT11kHz16BitMono, DISPID_SOTs_NewEnum,
    SVP_11, DISPID_SASFreeBufferSpace, DISPID_SGRSTPropertyName,
    DISPID_SPPConfidence, SBONone, SPGS_EXCLUSIVE,
    eLEXTYPE_MORPHOLOGY, DISPID_SRCEInterference, SINone,
    SpeechPropertyHighConfidenceThreshold,
    ISpeechLexiconPronunciations, eLEXTYPE_PRIVATE19,
    DISPID_SPEEngineConfidence, SVEBookmark, SDKLLocalMachine,
    SASStop, SVPOver, DISPID_SRGCmdLoadFromMemory,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, SINoSignal,
    SpNotifyTranslator, DISPID_SLWs_NewEnum, SpMMAudioOut,
    SPEI_VISEME, SVEAllEvents, SPRS_ACTIVE_USER_DELIMITED,
    SPWT_LEXICAL, DISPID_SRGId, ISpRecognizer2,
    DISPID_SRCEStartStream, DISPID_SRRAudio, SPWF_INPUT,
    SPEI_ADAPTATION, eLEXTYPE_PRIVATE18, SPSVerb,
    DISPID_SRGDictationUnload, SAFTADPCM_11kHzStereo,
    DISPID_SPCLangId, DISPID_SLGetGenerationChange,
    DISPID_SGRSAddWordTransition, DISPID_SRCRetainedAudioFormat,
    SRERequestUI, SAFT48kHz16BitStereo, ISpPhoneticAlphabetSelection,
    SASPause, DISPID_SRGIsPronounceable, SPBINARYGRAMMAR,
    DISPID_SPEsItem, DISPID_SVEStreamEnd, STCLocalServer,
    DISPID_SABufferNotifySize, ISpeechObjectToken, SAFT8kHz16BitMono,
    SPEVENT, DISPID_SVSLastStreamNumberQueued, SPINTERFERENCE_TOOFAST,
    SPRECOCONTEXTSTATUS, SP_VISEME_11, SPAS_RUN, SAFT44kHz16BitStereo,
    DISPID_SVAudioOutputStream, eLEXTYPE_PRIVATE1,
    SAFTTrueSpeech_8kHz1BitMono, DISPID_SGRSTText, SREStateChange,
    SDTRule, SITooQuiet, WSTRING, SITooFast, eLEXTYPE_PRIVATE14,
    ISpRecoCategory, DISPID_SPRsItem, DISPID_SPPsCount,
    ISpObjectWithToken, SPPHRASE, ISpeechObjectTokens,
    SPEI_PROPERTY_NUM_CHANGE, DISPID_SPIAudioSizeTime,
    eLEXTYPE_PRIVATE3, DISPID_SRAllowAudioInputFormatChangesOnNextSet,
    SPCT_DICTATION, SPSModifier, SPAS_CLOSED, SPEI_SENTENCE_BOUNDARY,
    SPEI_RESERVED2, SVSFParseSsml, SASClosed, SSSPTRelativeToStart,
    DISPID_SPIElements, IInternetSecurityManager, SPWORDPRONUNCIATION,
    SSFMCreateForWrite, DISPID_SRGetPropertyNumber, DISPID_SLPsItem,
    SSFMOpenReadWrite, SVP_17, DISPID_SPIEnginePrivateData,
    DISPID_SVSpeakCompleteEvent, STCInprocHandler, ISpSerializeState,
    _check_version, SAFT48kHz16BitMono, typelib_path,
    SAFTCCITT_uLaw_11kHzMono, DISPID_SPIGetText, ISpeechAudioFormat,
    SPSNotOverriden, DISPID_SPIRule, SVSFUnusedFlags,
    DISPID_SPPBRestorePhraseFromMemory, eLEXTYPE_PRIVATE13,
    DISPID_SASetState, ISpShortcut, ISpDataKey, ISpeechResourceLoader,
    DISPID_SABufferInfo, SpeechAddRemoveWord, SPEI_TTS_BOOKMARK,
    DISPID_SDKCreateKey, SITooLoud, SAFT48kHz8BitMono,
    SREInterference, DISPID_SMSADeviceId, DISPID_SPACommit, SVP_19,
    DISPID_SRCEventInterests, ISpeechLexicon, SINoise, SpWaveFormatEx,
    DISPID_SPRDisplayAttributes, DISPID_SRRSpeakAudio,
    ISpeechPhraseProperties, SpeechAudioFormatGUIDWave,
    DISPID_SRCResume, SPEI_FALSE_RECOGNITION, SpeechTokenKeyUI,
    ISpeechObjectTokenCategory, ISpAudio, SPRULE, SPAO_NONE,
    DISPID_SRCESoundStart, DISPID_SLPType, DISPID_SPAPhraseInfo,
    SVP_0, DISPID_SPEAudioTimeOffset, SDTDisplayText,
    SAFTCCITT_ALaw_22kHzStereo,
    DISPID_SRGCmdLoadFromProprietaryGrammar, SPXRO_Alternates_SML,
    BSTR, SPBO_NONE, SAFTADPCM_8kHzStereo, SPPS_Noun,
    SAFTCCITT_uLaw_44kHzStereo, SP_VISEME_19, DISPID_SVPriority,
    SRARoot, IEnumSpObjectTokens, DISPID_SGRSTsCount,
    SpResourceManager, SPWP_UNKNOWN_WORD_PRONOUNCEABLE,
    DISPID_SRRTimes, SPEI_TTS_AUDIO_LEVEL, SDTLexicalForm,
    SPEI_SR_AUDIO_LEVEL, SpMMAudioEnum, SVSFIsNotXML, SP_VISEME_5,
    DISPID_SLPSymbolic, ISequentialStream, DISPID_SRCVoice,
    DISPID_SCSBaseStream, SPEI_PHRASE_START,
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, SRESoundEnd,
    SPSMF_SRGS_SAPIPROPERTIES, SPEI_REQUEST_UI, SPRST_ACTIVE_ALWAYS,
    SFTSREngine, Speech_StreamPos_Asap, DISPID_SOTCreateInstance,
    SPEI_RECO_STATE_CHANGE, SGDSActive, SPSUnknown,
    SSSPTRelativeToEnd, DISPID_SRCEEndStream,
    DISPID_SVESentenceBoundary, ISpeechRecoGrammar,
    DISPID_SPIGrammarId, DISPID_SPPValue, SPEI_START_SR_STREAM,
    SAFT8kHz16BitStereo, DISPMETHOD, _ISpeechRecoContextEvents,
    eLEXTYPE_RESERVED4, DISPID_SRCEEnginePrivate,
    DISPID_SVSLastResult, SP_VISEME_4, ISpeechVoice, SRTReSent,
    SRADynamic, ISpeechAudioStatus, DISPID_SGRsCommitAndSave,
    SPVPRI_ALERT, SPSFunction, VARIANT_BOOL, eLEXTYPE_LETTERTOSOUND,
    DISPID_SASCurrentDevicePosition, SDA_No_Trailing_Space, SVPAlert,
    DISPID_SRSetPropertyString, DISPID_SRCERecognition,
    DISPID_SRCState, DISPID_SGRSAddSpecialTransition,
    DISPID_SVSpeakStream, DISPID_SWFEExtraData, DISPID_SRCBookmark,
    DISPID_SPPNumberOfElements, SAFTGSM610_11kHzMono, SPLO_STATIC,
    SpPhraseInfoBuilder, DISPID_SPISaveToMemory, SPDKL_CurrentUser,
    SVP_7, SVEStartInputStream, SPWP_KNOWN_WORD_PRONOUNCEABLE,
    DISPID_SPRsCount, ISpeechGrammarRuleStateTransitions,
    DISPID_SLAddPronunciationByPhoneIds, SREStreamStart,
    SPSERIALIZEDPHRASE, SRSEDone, SpeechMicTraining,
    SPEI_WORD_BOUNDARY, SPPS_Verb, DISPID_SWFESamplesPerSec,
    SpeechAudioProperties, SAFT8kHz8BitStereo, SPEI_UNDEFINED,
    SREAudioLevel, DISPID_SVEAudioLevel, SREPropertyNumChange,
    SPCT_COMMAND, SAFT24kHz8BitMono, ISpeechDataKey, SVF_Stressed,
    SpAudioFormat, DISPID_SGRSRule, DISPID_SRRRecoContext,
    DISPID_SOTMatchesAttributes, SVSFPersistXML,
    DISPID_SVSLastBookmarkId, SPRS_ACTIVE, DISPID_SVSLastBookmark,
    SGRSTTRule, DISPID_SRGReset, DISPID_SPRuleConfidence,
    SpeechPropertyAdaptationOn, DISPID_SPRFirstElement,
    DISPID_SAFGetWaveFormatEx, eLEXTYPE_PRIVATE4, SVSFNLPMask,
    SPINTERFERENCE_LATENCY_WARNING, SP_VISEME_1,
    DISPID_SRCEFalseRecognition, DISPID_SOTSetId, DISPID_SRRTLength,
    SAFTExtendedAudioFormat, DISPID_SRGRecoContext,
    SpeechCategoryAudioIn, _ULARGE_INTEGER, DISPID_SLPPartOfSpeech,
    SVF_None, DISPID_SLRemovePronunciationByPhoneIds,
    SAFT32kHz16BitStereo, DISPID_SRRPhraseInfo, DISPID_SWFEFormatTag,
    ISpeechMMSysAudio, ISpRecoContext, DISPIDSPTSI_ActiveOffset,
    helpstring, tagSPTEXTSELECTIONINFO, SSFMCreate,
    DISPID_SPIGetDisplayAttributes, SPBO_AHEAD, DISPID_SVEViseme,
    SPFM_NUM_MODES, ISpRecoContext2, SLTUser, DISPID_SVRate,
    DISPID_SPEAudioStreamOffset, SREAllEvents, eLEXTYPE_PRIVATE5,
    SVSFNLPSpeakPunc, SDTReplacement, DISPID_SVEventInterests,
    SVEAudioLevel, DISPID_SGRSTransitions, SpVoice,
    SRSInactiveWithPurge, SPINTERFERENCE_NOISE,
    SAFTCCITT_uLaw_8kHzMono, DISPID_SRCSetAdaptationData,
    SPRECOGNIZERSTATUS, eLEXTYPE_APP, SAFTADPCM_44kHzStereo,
    SAFTCCITT_ALaw_8kHzMono, DISPID_SLPsCount, IStream,
    DISPID_SBSSeek, DISPID_SRCAudioInInterferenceStatus,
    SVSFlagsAsync, ISpPhraseAlt, DISPID_SOTId, DISPID_SRCESoundEnd,
    DISPID_SPIStartTime, SPEI_RESERVED5, DISPID_SVVolume,
    DISPID_SVEWord, DISPID_SDKDeleteKey, SPPHRASERULE,
    DISPID_SPERequiredConfidence, SRERecoOtherContext, SREPrivate,
    DISPID_SRDisplayUI, DISPID_SGRSTs_NewEnum, ISpEventSink,
    eLEXTYPE_PRIVATE15, DISPID_SRGCmdLoadFromResource, SPPS_Unknown,
    eLEXTYPE_RESERVED10, eLEXTYPE_PRIVATE17,
    SAFTCCITT_ALaw_44kHzStereo, SpeechGrammarTagUnlimitedDictation,
    DISPID_SRRTStreamTime, DISPID_SRCCreateGrammar, SPEI_MIN_TTS,
    DISPID_SVResume, DISPID_SLWLangId, SpCustomStream,
    SGPronounciation, DISPID_SVPause,
    SpeechPropertyLowConfidenceThreshold, SAFTCCITT_ALaw_8kHzStereo,
    ISpeechPhraseInfoBuilder, SpeechVoiceSkipTypeSentence,
    DISPID_SGRId, DISPID_SGRsAdd, DISPID_SMSALineId,
    DISPID_SRCEPhraseStart, DISPID_SGRSTPropertyValue,
    DISPID_SDKGetStringValue, SAFTCCITT_uLaw_22kHzMono,
    SAFTNonStandardFormat, DISPID_SRAllowVoiceFormatMatchingOnNextSet,
    ISpeechPhraseElements, SREBookmark, eLEXTYPE_USER_SHORTCUT,
    STCInprocServer, SRCS_Disabled, DISPID_SVSCurrentStreamNumber,
    SpPhoneConverter, IInternetSecurityMgrSite, SRSInactive,
    SGLexical, SPRS_INACTIVE, SDA_Consume_Leading_Spaces,
    DISPID_SGRsFindRule, SPPS_Modifier, SpObjectTokenCategory,
    DISPID_SRCPause, SVEViseme, SpeechCategoryAppLexicons, SPWORDLIST,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, eLEXTYPE_PRIVATE12,
    DISPID_SRGCmdLoadFromFile, DISPID_SPCPhoneToId,
    SPEI_RECO_OTHER_CONTEXT, SpeechPropertyComplexResponseSpeed,
    DISPID_SVAlertBoundary, DISPID_SPRNumberOfElements,
    DISPID_SVEEnginePrivate, SPEI_SR_PRIVATE, ISpStreamFormat,
    DISPID_SRRecognizer, SpeechDictationTopicSpelling, WAVEFORMATEX,
    DISPID_SGRs_NewEnum, SpInprocRecognizer,
    SPEI_ACTIVE_CATEGORY_CHANGED, DISPID_SOTGetAttribute,
    SPEI_RESERVED6, SWPUnknownWordUnpronounceable,
    SpeechAudioFormatGUIDText, SREPhraseStart, SPRST_ACTIVE,
    DISPID_SVAudioOutput, ISpeechMemoryStream, SVSFVoiceMask,
    SVSFParseMask, DISPID_SRAudioInput, DISPID_SPELexicalForm,
    SDKLCurrentUser, SpSharedRecoContext, ISpMMSysAudio,
    SPEI_SOUND_START, DISPID_SRState, DISPID_SOTCDefault,
    SPPS_SuppressWord, DISPID_SPEAudioSizeBytes, ISpVoice,
    Speech_Default_Weight, ISpeechPhraseAlternate, SRTAutopause,
    SpeechRegistryLocalMachineRoot, SAFT12kHz16BitStereo,
    SPFM_OPEN_READWRITE, ISpStream, eWORDTYPE_DELETED, DISPID_SPPName,
    DISPID_SRRSetTextFeedback, SASRun, SAFT8kHz8BitMono,
    DISPID_SAFType, tagSPPROPERTYINFO, SAFTCCITT_uLaw_22kHzStereo,
    DISPID_SGRSTPropertyId, DISPID_SPRs_NewEnum, SPEI_SOUND_END,
    SP_VISEME_12, SSTTDictation, SpeechVoiceCategoryTTSRate,
    DISPID_SVGetVoices, DISPID_SRRAudioFormat, DISPID_SPPFirstElement,
    SPCS_DISABLED, DISPID_SPRuleId, CoClass, _FILETIME,
    SPEVENTSOURCEINFO, SAFT22kHz8BitStereo, ISpPhrase, SVSFIsFilename,
    DISPID_SOTIsUISupported, SAFT12kHz16BitMono, DISPID_SPRuleName,
    SPEI_PROPERTY_STRING_CHANGE, DISPID_SPAStartElementInResult,
    SAFT11kHz16BitStereo, SVEWordBoundary, SPEI_END_INPUT_STREAM,
    DISPID_SGRSTsItem, SPPHRASEPROPERTY, DISPID_SVEStreamStart,
    DISPID_SVWaitUntilDone, DISPID_SPRulesItem,
    DISPID_SLRemovePronunciation, SPWF_SRENGINE, DISPID_SVSpeak,
    SPBO_TIME_UNITS, DISPID_SRRTOffsetFromStart,
    DISPID_SAFSetWaveFormatEx, DISPID_SPAs_NewEnum, SPPS_NotOverriden,
    SPBO_PAUSE, ISpRecognizer, SECFIgnoreKanaType,
    DISPID_SPEDisplayText, SPCT_SUB_DICTATION, SPAUDIOBUFFERINFO,
    SPWT_DISPLAY, DISPID_SPIEngineId, DISPID_SLWPronunciations,
    SPLO_DYNAMIC, ISpeechGrammarRules, SPRST_NUM_STATES, SFTInput,
    SVP_6, SVSFIsXML, SAFT24kHz16BitStereo, DISPID_SRStatus,
    DISPID_SPPs_NewEnum, tagSTATSTG, SVP_13, SPSLMA, SRAORetainAudio,
    DISPID_SMSSetData, DISPID_SRCRecognizer, SAFTADPCM_11kHzMono,
    DISPID_SRCERecognizerStateChange, SPEI_PHONEME, SVP_14, SVP_3,
    SVP_15, SPSHORTCUTPAIRLIST, DISPID_SGRAttributes, ULONG_PTR,
    SVP_18, DISPID_SVSVisemeId, SP_VISEME_6, DISPID_SABIBufferSize,
    SpStreamFormatConverter, SPEI_START_INPUT_STREAM, SPAR_High,
    ISpRecognizer3, SPEI_END_SR_STREAM, SAFTCCITT_ALaw_11kHzMono,
    SPPS_Interjection, DISPID_SBSWrite, DISPID_SVVoice, STSF_AppData,
    DISPID_SGRSTType, SAFTCCITT_uLaw_11kHzStereo, SRAONone,
    DISPID_SPIRetainedSizeBytes, SPVOICESTATUS, SPSNoun, SPEI_MAX_SR,
    SAFT44kHz16BitMono, ISpRecoGrammar,
    DISPID_SVSyncronousSpeakTimeout, SDTProperty, ISpRecoResult,
    DISPID_SAFGuid, eLEXTYPE_PRIVATE7, SAFTCCITT_ALaw_44kHzMono,
    SREAdaptation, SAFT32kHz16BitMono, ISpeechPhraseAlternates,
    SPRECORESULTTIMES, SAFT16kHz8BitMono, Library,
    DISPID_SLPs_NewEnum, ISpeechLexiconWord, ISpProperties,
    DISPID_SGRInitialState, _ISpeechVoiceEvents,
    DISPID_SPRules_NewEnum, SpeechRegistryUserRoot, SAFTText,
    SPSInterjection, SP_VISEME_3, SREPropertyStringChange,
    DISPID_SMSGetData, eLEXTYPE_PRIVATE20,
    DISPIDSPTSI_SelectionOffset, SECHighConfidence, ISpNotifySource,
    DISPID_SRCEBookmark, SPPS_Function, SVP_1, SVP_10,
    eLEXTYPE_VENDORLEXICON, DISPID_SOTCGetDataKey,
    SAFT16kHz16BitStereo, SAFTADPCM_44kHzMono, SPFM_OPEN_READONLY
)


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'SVSFParseSsml', 'SpeechRecoEvents', 'SpeechDataKeyLocation',
    'SASClosed', 'SSSPTRelativeToStart', 'DISPID_SPIElements',
    'IInternetSecurityManager', 'SVSFDefault', 'SPVISEMES',
    'SPWORDPRONUNCIATION', 'SGSExclusive', 'SSFMCreateForWrite',
    'DISPID_SRGetPropertyNumber', 'DISPID_SLPsItem',
    'DISPID_SREmulateRecognition', 'DISPID_SGRClear',
    'SpeechTokenContext', 'DISPID_SpeechGrammarRuleStateTransitions',
    'DISPID_SLWsItem', 'DISPID_SPIEnginePrivateData',
    'SPGRAMMARWORDTYPE', 'SSFMOpenReadWrite', 'eLEXTYPE_PRIVATE11',
    'DISPID_SVSpeakCompleteEvent', 'SVP_17', 'ISpeechPhraseElement',
    'SpeechInterference', 'SDKLCurrentConfig', 'SGRSTTEpsilon',
    'STCInprocHandler', 'DISPID_SASCurrentSeekPosition', 'SVPNormal',
    'ISpSerializeState', 'DISPID_SOTRemove', 'DISPID_SOTCSetId',
    'ISpeechAudioBufferInfo', 'SpInProcRecoContext',
    'SAFT48kHz16BitMono', 'typelib_path', 'SAFTCCITT_uLaw_11kHzMono',
    'DISPID_SPIGetText', 'DISPID_SpeechObjectToken',
    'DISPID_SGRsDynamic', 'ISpeechAudioFormat', 'SPSNotOverriden',
    'DISPID_SPRuleEngineConfidence', 'DISPID_SPIRule',
    'SVSFUnusedFlags', 'SpeechCategoryRecoProfiles',
    'SAFT12kHz8BitStereo', 'SpeechFormatType', 'DISPID_SOTsCount',
    'DISPID_SPPBRestorePhraseFromMemory', 'DISPID_SPEPronunciation',
    'eLEXTYPE_PRIVATE13', 'eLEXTYPE_RESERVED6', 'DISPID_SASetState',
    'DISPID_SpeechLexiconPronunciation', 'ISpShortcut', 'ISpDataKey',
    'ISpeechResourceLoader', 'SAFTNoAssignedFormat',
    'DISPID_SpeechObjectTokens', 'SpObjectToken', 'DISPID_SVEPhoneme',
    'DISPID_SABufferInfo', 'SpeechAddRemoveWord', 'SPEI_TTS_BOOKMARK',
    'DISPID_SDKCreateKey', 'SPPS_RESERVED4', 'SAFTADPCM_22kHzStereo',
    'eLEXTYPE_PRIVATE10', 'SITooLoud', 'SVP_0', 'SREInterference',
    'SAFT44kHz8BitMono', 'ISpeechFileStream', 'DISPID_SMSADeviceId',
    'SAFT48kHz8BitMono', 'SPEI_RECOGNITION',
    'DISPID_SpeechRecoResultTimes', 'DISPID_SPACommit',
    'eWORDTYPE_ADDED', 'SPSHORTCUTPAIR', 'SpeechStreamFileMode',
    'DISPID_SRCEventInterests', 'DISPID_SRGSetTextSelection',
    'SVP_19', 'ISpeechLexicon', 'ISpLexicon',
    'DISPID_SPEActualConfidence', 'SINoise', 'SpWaveFormatEx',
    'DISPID_SPIReplacements', 'DISPID_SDKDeleteValue', 'SPGS_ENABLED',
    'SRATopLevel', 'LONG_PTR', 'SAFTGSM610_44kHzMono',
    'DISPID_SPRDisplayAttributes', 'DISPID_SRRSpeakAudio',
    'SpeechAudioVolume', 'SpeechVoiceEvents',
    'SpeechAudioFormatGUIDWave', 'ISpeechPhraseProperties',
    'DISPIDSPTSI_SelectionLength', 'SVP_8', 'ISpeechRecoResult2',
    'SpeechWordPronounceable', 'DISPID_SRCResume',
    'SPEI_FALSE_RECOGNITION', 'SpeechTokenKeyUI',
    'ISpeechObjectTokenCategory', 'ISpAudio', 'SPEI_TTS_PRIVATE',
    'SPRULE', 'SpeechRuleState', 'DISPID_SFSOpen', 'SPAO_NONE',
    'SAFT16kHz8BitStereo', 'DISPID_SRCRequestedUIType',
    'SpeechTokenIdUserLexicon', 'DISPID_SpeechPhraseInfo',
    'DISPID_SRSCurrentStreamPosition', 'SpeechGrammarTagWildcard',
    'SP_VISEME_0', 'SRESoundStart', 'SAFT32kHz8BitMono',
    'SRTSMLTimeout', 'DISPID_SRCESoundStart', 'DISPID_SRProfile',
    'DISPID_SLPType', 'DISPID_SPAPhraseInfo', 'SPPHRASEELEMENT',
    'DISPID_SABIMinNotification', 'SRTStandard',
    'DISPID_SMSAMMHandle', 'SpeechTokenValueCLSID',
    'DISPID_SPEAudioTimeOffset', 'SPSMF_SAPI_PROPERTIES',
    'DISPID_SWFEChannels', 'SDTDisplayText',
    'SpeechRecoProfileProperties', 'DISPID_SRGCmdLoadFromObject',
    'SAFTCCITT_ALaw_22kHzMono', 'ISpeechRecoResultTimes',
    'SAFTCCITT_ALaw_22kHzStereo', 'SSTTTextBuffer',
    'SAFTGSM610_11kHzMono', 'DISPID_SRGCmdLoadFromProprietaryGrammar',
    'SPXRO_Alternates_SML', 'SPEI_VOICE_CHANGE',
    'DISPID_SRCVoicePurgeEvent', 'SpSharedRecognizer',
    'SPRST_INACTIVE_WITH_PURGE', 'SpPhraseInfoBuilder',
    'SREStreamEnd', 'SAFTCCITT_ALaw_11kHzStereo', 'SPBO_NONE',
    'SPWAVEFORMATTYPE', 'SAFTADPCM_8kHzStereo', 'SPPS_Noun',
    'eLEXTYPE_PRIVATE2', 'SAFTCCITT_uLaw_44kHzStereo',
    'SPINTERFERENCE_NOSIGNAL', 'DISPID_SRCreateRecoContext', 'SVP_21',
    'SAFT11kHz8BitMono', 'SP_VISEME_19', 'DISPID_SVPriority',
    'SPGRAMMARSTATE', 'DISPID_SRRGetXMLResult', 'DISPID_SGRsCount',
    'SRARoot', 'DISPID_SpeechPhraseRule', 'DISPID_SDKEnumKeys',
    'SPDATAKEYLOCATION', 'IEnumSpObjectTokens',
    'DISPID_SpeechBaseStream', 'DISPID_SRAudioInputStream',
    'SPINTERFERENCE_TOOSLOW', 'SpTextSelectionInformation',
    'DISPID_SGRSTsCount', 'DISPID_SVSInputSentenceLength',
    'ISpeechRecoContext', 'SpResourceManager',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'DISPID_SRRTimes',
    'SPEI_TTS_AUDIO_LEVEL', 'SDTLexicalForm', 'DISPID_SRCEHypothesis',
    'DISPID_SADefaultFormat', 'DISPID_SpeechLexiconWord',
    'SPEI_SR_AUDIO_LEVEL', 'SPSHT_Unknown', 'SPFM_CREATE_ALWAYS',
    'SRAInterpreter', 'DISPID_SPRuleParent', 'SpMMAudioEnum',
    'DISPID_SWFEAvgBytesPerSec', 'DISPID_SpeechRecoContextEvents',
    'SVSFIsNotXML', 'SpCompressedLexicon', 'DISPID_SDKSetStringValue',
    'SP_VISEME_5', 'DISPID_SRCEAdaptation', 'DISPID_SLPSymbolic',
    'DISPID_SpeechMMSysAudio', 'DISPID_SRCVoice',
    'DISPID_SPANumberOfElementsInResult', 'SPAR_Medium',
    'DISPID_SCSBaseStream', 'SGSDisabled', 'SPEI_PHRASE_START',
    'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE', 'SPEI_INTERFERENCE',
    'DISPID_SDKEnumValues', 'SRESoundEnd', 'SGDSActiveWithAutoPause',
    'SP_VISEME_14', 'DISPID_SpeechAudioFormat',
    'DISPID_SpeechObjectTokenCategory', 'SAFT24kHz16BitMono',
    'SSFMOpenForRead', 'ISpeechGrammarRuleStateTransition',
    'DISPID_SpeechPhraseProperty', 'DISPID_SRGetRecognizers',
    'SpeechAudioFormatType', 'DISPID_SPRuleNumberOfElements',
    'SPSEMANTICFORMAT', '__MIDL___MIDL_itf_sapi_0000_0020_0001',
    'SPSMF_SRGS_SAPIPROPERTIES', 'DISPID_SRIsShared',
    'ISpeechPhraseReplacements', 'SAFT48kHz8BitStereo',
    'STCRemoteServer', 'DISPID_SPEAudioSizeTime',
    'SPDKL_DefaultLocation', 'SPEI_REQUEST_UI', 'SPRST_ACTIVE_ALWAYS',
    'SFTSREngine', 'Speech_StreamPos_Asap',
    'DISPID_SOTCreateInstance', 'SpeechRuleAttributes',
    'SPEI_RECO_STATE_CHANGE', 'SGDSActive', 'SVEVoiceChange',
    'SSSPTRelativeToEnd', 'SpeechEngineConfidence', 'SPSUnknown',
    'DISPID_SRCEEndStream', 'DISPID_SVESentenceBoundary',
    'SREHypothesis', 'STSF_FlagCreate', 'ISpeechRecoGrammar',
    'SAFT22kHz16BitMono', 'DISPID_SpeechRecognizer',
    'DISPID_SPIGrammarId', 'SpeechEmulationCompareFlags',
    'DISPID_SPPValue', 'SPEI_START_SR_STREAM', 'SpeechPartOfSpeech',
    'ISpeechAudio', 'SAFT8kHz16BitStereo', 'ISpeechBaseStream',
    'SPVPRIORITY', '_ISpeechRecoContextEvents', 'DISPID_SVDisplayUI',
    'eLEXTYPE_RESERVED4', 'SAFT16kHz16BitMono', 'SAFTGSM610_8kHzMono',
    'DISPID_SRCEEnginePrivate', 'ISpeechPhraseRules', 'SP_VISEME_18',
    'DISPID_SVSLastResult', 'SP_VISEME_4', 'ISpeechVoice',
    'SRTReSent', 'SpeechRunState', 'DISPID_SRIsUISupported',
    'SRADynamic', 'ISpeechAudioStatus', 'DISPID_SGRsCommitAndSave',
    'SPVPRI_ALERT', 'SPSFunction', 'eLEXTYPE_LETTERTOSOUND',
    'SPSHT_OTHER', 'SAFTGSM610_22kHzMono',
    'DISPID_SASCurrentDevicePosition', 'SDA_No_Trailing_Space',
    'SVPAlert', 'SPLOADOPTIONS', 'DISPID_SRSetPropertyString',
    'SAFT11kHz8BitStereo', 'SDA_Two_Trailing_Spaces',
    'ISpGrammarBuilder', 'ISpEventSource', 'DISPID_SVEBookmark',
    'SVSFParseSapi', 'DISPID_SRRGetXMLErrorInfo',
    'DISPID_SLAddPronunciation', 'SDTAlternates', 'UINT_PTR',
    'DISPID_SpeechPhraseAlternate', 'DISPID_SRSetPropertyNumber',
    'SPPS_LMA', 'SRADefaultToActive', 'DISPID_SPRText',
    'DISPID_SRCERecognition', 'SpeechSpecialTransitionType', 'SVP_20',
    'DISPID_SRCState', 'DISPID_SOTDataKey',
    'DISPID_SGRSAddSpecialTransition', 'eLEXTYPE_RESERVED7',
    'DISPID_SVSpeakStream', 'DISPID_SVGetAudioInputs',
    'DISPID_SWFEExtraData', 'SRSActive', 'DISPID_SRCBookmark',
    'SGDisplay', 'ISpeechPhraseRule', 'DISPID_SPPNumberOfElements',
    'SPLO_STATIC', 'SP_VISEME_21', 'DISPID_SRCERequestUI',
    'DISPID_SPISaveToMemory', 'SPSERIALIZEDRESULT',
    'SPDKL_CurrentUser', 'SECLowConfidence', 'SWTAdded',
    'SVEStartInputStream', 'DISPID_SRRTTickCount', 'DISPID_SPRsCount',
    'SPWP_KNOWN_WORD_PRONOUNCEABLE', 'SP_VISEME_10',
    'SECNormalConfidence', 'DISPID_SBSFormat', 'SRERecognition',
    'DISPID_SAEventHandle', 'ISpeechGrammarRuleStateTransitions',
    'DISPID_SpeechRecoResult2', 'DISPID_SRSCurrentStreamNumber',
    'SVF_Emphasis', 'DISPID_SRCCmdMaxAlternates',
    'DISPID_SWFEBlockAlign', 'DISPID_SGRsItem',
    'DISPID_SLAddPronunciationByPhoneIds', 'SREStreamStart',
    'SECFDefault', 'DISPID_SGRsCommit', 'SPSERIALIZEDPHRASE',
    'SRSEDone', 'SpeechMicTraining', 'SPEI_WORD_BOUNDARY',
    'SPPS_Verb', 'DISPID_SOTCategory', 'DISPID_SWFESamplesPerSec',
    'SpeechAudioProperties', 'ISpRecoGrammar2', 'SAFT8kHz8BitStereo',
    'SPEI_UNDEFINED', 'SREAudioLevel', 'DISPID_SRGDictationLoad',
    'DISPID_SVEAudioLevel', 'DISPID_SPPChildren', 'SP_VISEME_20',
    'SECFNoSpecialChars', 'DISPID_SLPLangId',
    'ISpStreamFormatConverter', 'DISPID_SPRuleChildren',
    'SPPS_RESERVED2', 'SPCS_ENABLED', 'SPDKL_CurrentConfig',
    'DISPID_SPPParent', 'DISPID_SpeechVoiceStatus',
    'ISpeechCustomStream', 'SREPropertyNumChange', 'SPCT_SUB_COMMAND',
    'SPCT_COMMAND', 'SAFT24kHz8BitMono', 'SPEI_MAX_TTS',
    'ISpeechDataKey', 'SpeechStreamSeekPositionType',
    'SPWORDPRONUNCIATIONLIST', 'SVF_Stressed', 'SGDSInactive',
    'DISPID_SRCEPropertyStringChange', 'SpAudioFormat',
    'SDTPronunciation', 'SpNullPhoneConverter', 'DISPID_SGRSRule',
    'DISPID_SRRRecoContext', 'DISPID_SLPPhoneIds', 'SP_VISEME_15',
    'DISPID_SOTMatchesAttributes', 'SAFT22kHz8BitMono',
    'SVSFPersistXML', 'DISPID_SVSLastBookmarkId', 'SPAS_PAUSE',
    'SVP_2', 'DISPID_SVGetProfiles', 'SAFTDefault',
    'DISPID_SpeechRecognizerStatus', 'SpeechWordType',
    'SpeechVisemeFeature', 'SAFTCCITT_uLaw_8kHzStereo', 'SPRS_ACTIVE',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'DISPID_SVSLastBookmark', 'DISPID_SpeechAudioBufferInfo',
    'SLTApp', 'SGRSTTRule', 'SVEPhoneme', 'DISPID_SRGReset',
    '__MIDL_IWinTypes_0009', 'SGLexicalNoSpecialChars',
    'SAFTADPCM_22kHzMono', 'DISPID_SVIsUISupported',
    'DISPID_SPRuleConfidence', 'SpeechPropertyAdaptationOn',
    'DISPID_SPRFirstElement', 'SGRSTTWord',
    'DISPID_SAFGetWaveFormatEx', 'DISPID_SPAsItem',
    'eLEXTYPE_PRIVATE4', 'SVSFNLPMask', 'SDTAll',
    'SPINTERFERENCE_LATENCY_WARNING', 'DISPID_SpeechPhraseElements',
    'ISpeechRecoResultDispatch', 'SP_VISEME_1',
    'DISPID_SRCEFalseRecognition', 'DISPID_SOTSetId',
    'DISPID_SRRTLength', 'DISPID_SRGCmdSetRuleIdState',
    'SPINTERFERENCE_TOOQUIET', 'SAFTExtendedAudioFormat',
    'SPAUDIOSTATUS', 'DISPID_SRCRetainedAudio', 'SPXRO_SML',
    'DISPID_SRGRecoContext', 'SpeechCategoryAudioIn',
    'DISPID_SLPPartOfSpeech', 'SVSFParseAutodetect',
    'DISPID_SLRemovePronunciationByPhoneIds', 'SVF_None',
    'SAFT32kHz16BitStereo', 'DISPID_SRRPhraseInfo',
    'SPADAPTATIONRELEVANCE', 'DISPID_SWFEFormatTag',
    'SpeechGrammarRuleStateTransitionType', 'ISpeechMMSysAudio',
    'ISpRecoContext', 'DISPIDSPTSI_ActiveOffset',
    'tagSPTEXTSELECTIONINFO', 'SSFMCreate',
    'DISPID_SPIGetDisplayAttributes', 'DISPIDSPTSI_ActiveLength',
    'SPBO_AHEAD', 'SPFM_NUM_MODES', 'ISpRecoContext2',
    'DISPID_SVEViseme', 'SpeechPropertyResourceUsage',
    'DISPID_SRGState', 'DISPID_SRGDictationSetState',
    'SpeechRecognitionType', 'SLTUser', 'DISPID_SVRate',
    'DISPID_SpeechPhraseReplacements', 'SPSHT_NotOverriden',
    'DISPID_SRGetFormat', 'SECFIgnoreCase', 'SVP_12',
    'DISPID_SPPsItem', 'DISPID_SPEAudioStreamOffset', 'SREAllEvents',
    'eLEXTYPE_PRIVATE5', 'SVSFNLPSpeakPunc', 'SDTReplacement',
    'DISPID_SVEventInterests', '_RemotableHandle', 'SVEAudioLevel',
    'STSF_LocalAppData', 'DISPID_SAVolume', 'DISPID_SGRSTransitions',
    'SpVoice', 'DISPID_SVSkip', 'DISPID_SpeechGrammarRule',
    'SPEI_MIN_SR', 'DISPID_SFSClose', 'ISpResourceManager',
    'DISPID_SABIEventBias', 'SPPHRASEREPLACEMENT',
    'ISpeechXMLRecoResult', 'SPINTERFERENCE', 'DISPID_SRGRules',
    'SpeechUserTraining', 'SVESentenceBoundary',
    'SpeechDisplayAttributes', 'SVEPrivate', 'SRSInactiveWithPurge',
    'SGRSTTDictation', 'ISpeechWaveFormatEx', 'SPINTERFERENCE_NOISE',
    'eLEXTYPE_PRIVATE16', 'SpStream', 'SPSTREAMFORMATTYPE',
    'SPINTERFERENCE_TOOLOUD', 'SAFTCCITT_uLaw_8kHzMono',
    'DISPID_SpeechXMLRecoResult', 'DISPID_SPEsCount',
    'DISPID_SpeechDataKey', 'DISPID_SRCSetAdaptationData',
    'DISPID_SPPEngineConfidence', 'SPRST_INACTIVE',
    'SSSPTRelativeToCurrentPosition', 'SPRECOGNIZERSTATUS',
    'eLEXTYPE_APP', 'SAFTADPCM_44kHzStereo',
    'SAFTCCITT_ALaw_8kHzMono', 'DISPID_SLPsCount', 'ISpNotifySink',
    'IStream', 'DISPID_SPIAudioSizeBytes', 'DISPID_SBSSeek',
    'SPEI_RESERVED3', 'DISPID_SRCAudioInInterferenceStatus',
    'SVSFlagsAsync', 'DISPID_SPAsCount', 'ISpPhraseAlt',
    'DISPID_SOTId', 'DISPID_SRCESoundEnd', 'DISPID_SPIStartTime',
    'SAFT44kHz8BitStereo', 'DISPID_SpeechFileStream',
    'SPEI_RESERVED5', 'eLEXTYPE_PRIVATE6', 'SpeechBookmarkOptions',
    'DISPID_SpeechWaveFormatEx', 'DISPID_SVVolume', 'DISPID_SVEWord',
    'ISpeechRecoResult', 'SPPROPERTYINFO', 'DISPID_SDKDeleteKey',
    'SVP_9', 'SWPKnownWordPronounceable', 'DISPID_SRCEAudioLevel',
    'SPPHRASERULE', 'SPXMLRESULTOPTIONS', 'SpeechRecoContextState',
    'DISPID_SPERequiredConfidence', 'DISPID_SBSRead',
    'DISPID_SDKGetBinaryValue', 'SPEI_HYPOTHESIS',
    'DISPID_SLGetPronunciations', 'SPWT_PRONUNCIATION',
    'SPSSuppressWord', 'DISPID_SASNonBlockingIO', 'SPPS_Noncontent',
    'SRERecoOtherContext', 'SREPrivate', 'SpeechRetainedAudioOptions',
    'ISpeechPhraseInfo', 'DISPID_SRDisplayUI', 'SpLexicon',
    'DISPID_SGRSTs_NewEnum', 'SPEVENTENUM',
    'DISPID_SPEDisplayAttributes', 'eLEXTYPE_PRIVATE15',
    'ISpEventSink', 'DISPID_SRCEPropertyNumberChange', 'SP_VISEME_13',
    'SpeechAllElements', 'DISPID_SRCCreateResultFromMemory',
    'DISPID_SRGCmdLoadFromResource', 'SPPS_Unknown', 'SSTTWildcard',
    'DISPID_SpeechLexicon', 'eLEXTYPE_RESERVED10',
    'eLEXTYPE_PRIVATE17', 'SpeechCategoryVoices',
    'SWPUnknownWordPronounceable', 'SPPS_RESERVED3', 'SBOPause',
    'SPAS_STOP', 'DISPID_SRSAudioStatus',
    'SAFTCCITT_ALaw_44kHzStereo', 'DISPID_SVSPhonemeId',
    'SpeechGrammarTagUnlimitedDictation', 'SPEI_RESERVED1',
    'DISPID_SPERetainedSizeBytes', 'DISPID_SRRTStreamTime',
    'SPPS_RESERVED1', 'ISpObjectToken',
    'DISPID_SRSSupportedLanguages', 'SPAO_RETAIN_AUDIO',
    'ISpeechTextSelectionInformation', 'SpShortcut',
    'DISPID_SRCCreateGrammar', 'DISPID_SLGenerationId',
    'DISPID_SpeechPhraseElement', 'SPEI_MIN_TTS', 'SPSHT_EMAIL',
    'DISPID_SVResume', 'DISPID_SLWLangId', 'SpCustomStream',
    'SPFM_CREATE', 'SGPronounciation', 'DISPID_SVPause',
    'DISPID_SPRuleFirstElement', 'DISPID_SRSNumberOfActiveRules',
    'ISpeechLexiconWords', 'ISpeechRecognizerStatus',
    'ISpeechPhraseProperty', 'SpeechPropertyLowConfidenceThreshold',
    'SPRECOSTATE', 'SAFTCCITT_ALaw_8kHzStereo',
    'ISpeechPhraseInfoBuilder', 'SpeechVoiceSkipTypeSentence',
    'DISPID_SpeechGrammarRuleStateTransition', 'DISPID_SGRId',
    'DISPID_SRGCommit', 'DISPID_SGRsAdd', 'SVEEndInputStream',
    'ISpPhoneticAlphabetConverter', 'SPCT_SLEEP', 'DISPID_SMSALineId',
    'SPEI_SR_BOOKMARK', 'DISPID_SGRSTPropertyValue',
    'DISPID_SDKGetStringValue', 'DISPID_SRCEPhraseStart',
    'DISPID_SRCERecognitionForOtherContext',
    'SAFTCCITT_uLaw_22kHzMono', 'DISPID_SpeechPhraseBuilder',
    'SAFTNonStandardFormat',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'DISPID_SOTDisplayUI', 'ISpeechPhraseElements', 'SREBookmark',
    'ISpeechVoiceStatus', 'DISPID_SVSInputWordLength',
    'eLEXTYPE_USER_SHORTCUT', 'DISPID_SVEVoiceChange',
    'STCInprocServer', 'SRCS_Disabled',
    'DISPID_SVSCurrentStreamNumber', 'SpPhoneConverter',
    'IInternetSecurityMgrSite', 'DISPID_SpeechGrammarRules',
    'DISPID_SRRSaveToMemory', 'SPWORDPRONOUNCEABLE',
    'DISPID_SGRAddState', 'SRSInactive', 'SGRSTTWildcard',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'DISPID_SOTsItem',
    'SPRS_INACTIVE', 'SPSMF_SRGS_SEMANTICINTERPRETATION_MS',
    'SDA_Consume_Leading_Spaces', 'SGLexical', 'DISPID_SGRsFindRule',
    'SPPS_Modifier', 'SPSEMANTICERRORINFO',
    'DISPID_SVSInputSentencePosition',
    'DISPID_SPERetainedStreamOffset', 'DISPID_SOTGetDescription',
    'SpObjectTokenCategory', 'DISPID_SRCPause', 'SVEViseme',
    'SP_VISEME_16', 'SPEI_SR_RETAINEDAUDIO',
    'DISPID_SPIAudioStreamPosition', 'SpeechVoicePriority',
    '_SPAUDIOSTATE', 'SPDKL_LocalMachine', 'SLODynamic',
    'DISPID_SPILanguageId', 'SAFTADPCM_8kHzMono',
    'SpeechCategoryAppLexicons', 'SPWORDLIST', 'eLEXTYPE_PRIVATE8',
    'SWTDeleted', 'SpeechCategoryRecognizers',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'SVP_4', 'SAFT12kHz8BitMono',
    'SP_VISEME_2', 'eLEXTYPE_PRIVATE12', 'DISPID_SRGCmdLoadFromFile',
    'SPWORD', 'DISPID_SPCPhoneToId', 'SPEI_RECO_OTHER_CONTEXT',
    'SpeechPropertyComplexResponseSpeed',
    'DISPID_SRGSetWordSequenceData', 'DISPID_SVAlertBoundary',
    'DISPID_SPRNumberOfElements', 'DISPID_SVEEnginePrivate',
    'SPEI_SR_PRIVATE', 'SRCS_Enabled', 'eLEXTYPE_PRIVATE9',
    'SDA_One_Trailing_Space', 'ISpStreamFormat', 'SpeechLexiconType',
    'SDTAudio', 'DISPID_SOTGetStorageFileName', 'DISPID_SRRecognizer',
    'WAVEFORMATEX', 'DISPID_SGRs_NewEnum',
    'SpeechDictationTopicSpelling', 'SAFT32kHz8BitStereo',
    'SpInprocRecognizer', 'SPEI_ACTIVE_CATEGORY_CHANGED',
    'DISPID_SOTGetAttribute', 'DISPID_SRRAlternates', 'SP_VISEME_8',
    'SPEI_RESERVED6', 'SWPUnknownWordUnpronounceable', 'SPAR_Low',
    'eLEXTYPE_RESERVED9', 'SpeechAudioFormatGUIDText',
    'Speech_Max_Pron_Length', 'SVP_7', 'SREPhraseStart',
    'SPRST_ACTIVE', 'DISPID_SVAudioOutput', 'ISpeechMemoryStream',
    'DISPID_SPEs_NewEnum', 'SVSFVoiceMask', 'DISPID_SOTCId',
    'DISPID_SpeechVoice', 'SVSFParseMask', 'DISPIDSPRG',
    'DISPID_SRAudioInput', 'DISPID_SPELexicalForm', 'SPVPRI_OVER',
    'SDKLCurrentUser', 'SpSharedRecoContext', 'SRAImport',
    'DISPID_SPIProperties', 'DISPID_SpeechRecoContext',
    'ISpMMSysAudio', 'SPEI_SOUND_START', 'SITooSlow',
    'SpeechEngineProperties', 'DISPID_SOTCDefault',
    'SPPS_SuppressWord', 'DISPID_SRState', 'ISpeechGrammarRule',
    'DISPID_SPEAudioSizeBytes', 'SRSEIsSpeaking', 'ISpVoice',
    'Speech_Default_Weight', 'ISpeechPhraseAlternate', 'SRTAutopause',
    'DISPID_SPPId', 'ISpXMLRecoResult',
    'SpeechRegistryLocalMachineRoot', 'SpeechCategoryAudioOut',
    'SPFM_OPEN_READWRITE', 'SAFT12kHz16BitStereo',
    'SpPhoneticAlphabetConverter', 'ISpStream', 'eWORDTYPE_DELETED',
    'SpUnCompressedLexicon', 'IEnumString', 'DISPID_SPPName',
    'DISPID_SRRSetTextFeedback', 'DISPID_SVGetAudioOutputs',
    'Speech_StreamPos_RealTime', 'SAFTCCITT_uLaw_44kHzMono', 'SASRun',
    'SAFT8kHz8BitMono', 'ISpeechLexiconPronunciation',
    'DISPID_SAFType', 'tagSPPROPERTYINFO', 'STCAll',
    'SAFTCCITT_uLaw_22kHzStereo', 'DISPID_SGRSTPropertyId',
    'DISPID_SPRs_NewEnum', 'SVP_5', 'DISPID_SDKOpenKey',
    'SPEI_SOUND_END', 'SP_VISEME_12', 'SSTTDictation',
    'SpeechVoiceCategoryTTSRate', 'DISPID_SLWType',
    'SVSFPurgeBeforeSpeak', 'SDKLDefaultLocation',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'DISPID_SVGetVoices',
    'DISPID_SRGCmdSetRuleState', 'DISPID_SRRAudioFormat',
    'SpeechGrammarTagDictation', 'DISPID_SPPFirstElement',
    'SPLEXICONTYPE', 'SPCS_DISABLED', 'DISPID_SPRuleId',
    'ISpNotifyTranslator', 'ISpPhoneConverter', 'SpeechTokenKeyFiles',
    'DISPID_SPRulesCount', 'DISPID_SGRName', 'SPGS_DISABLED',
    'SPEVENTSOURCEINFO', 'SPSMF_UPS', 'SRTEmulated',
    'SAFT22kHz8BitStereo', 'ISpPhrase', 'DISPID_SLWsCount',
    'DISPID_SpeechLexiconWords', 'SPAR_Unknown',
    'ISpObjectTokenCategory', 'SVSFIsFilename', 'DISPIDSPTSI',
    'DISPID_SOTIsUISupported', 'SAFT12kHz16BitMono',
    'DISPID_SPCIdToPhone', 'SP_VISEME_9', 'DISPID_SPRuleName',
    'SPINTERFERENCE_LATENCY_TRUNCATE_END', 'DISPID_SDKSetLongValue',
    'DISPID_SGRSTRule', 'SREFalseRecognition',
    'SPEI_PROPERTY_STRING_CHANGE', 'DISPID_SPAStartElementInResult',
    'SP_VISEME_7', 'DISPID_SRSClsidEngine',
    'DISPID_SWFEBitsPerSample',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'eLEXTYPE_USER',
    'SAFT11kHz16BitStereo', 'SRTExtendableParse',
    'DISPID_SGRSAddRuleTransition', 'SVEWordBoundary',
    'SPEI_END_INPUT_STREAM', 'SVP_16', 'DISPID_SLWWord',
    'DISPID_SGRSTsItem', 'SPPHRASEPROPERTY', 'DISPID_SVSRunningState',
    'SPVPRI_NORMAL', 'DISPID_SVSInputWordPosition',
    'DISPID_SVEStreamStart', 'DISPID_SDKSetBinaryValue', 'SGSEnabled',
    'SpeechLoadOption', 'DISPID_SOTRemoveStorageFileName',
    'SPCATEGORYTYPE', 'DISPID_SVWaitUntilDone',
    'DISPID_SpeechCustomStream', 'ISpeechGrammarRuleState',
    'DISPID_SPRulesItem', 'DISPID_SLRemovePronunciation',
    'SGRSTTTextBuffer', 'SPWF_SRENGINE',
    'SpeechPropertyNormalConfidenceThreshold', 'DISPID_SVSpeak',
    'SpMemoryStream', 'DISPID_SPARecoResult',
    'DISPID_SRRTOffsetFromStart', 'SPBO_TIME_UNITS',
    'Speech_Max_Word_Length', 'DISPID_SDKGetlongValue',
    'DISPID_SAFSetWaveFormatEx', 'DISPID_SAStatus',
    'SpeechCategoryPhoneConverters', 'SRAExport', 'SPBOOKMARKOPTIONS',
    'SpFileStream', 'DISPID_SpeechPhraseAlternates',
    'DISPID_SASState', 'DISPID_SpeechPhoneConverter',
    'DISPID_SPAs_NewEnum', 'SAFT24kHz8BitStereo', 'SPPS_NotOverriden',
    'SpMMAudioIn', 'SRSActiveAlways', 'SPBO_PAUSE', 'ISpRecognizer',
    'SGDSActiveUserDelimited', 'SECFIgnoreKanaType',
    'DISPID_SGRSTNextState', 'DISPID_SpeechLexiconProns',
    'DISPID_SPEDisplayText', 'SPCT_SUB_DICTATION',
    'SPAUDIOBUFFERINFO', 'SPWT_DISPLAY', 'DISPID_SPIEngineId',
    'DISPID_SGRAddResource', 'DISPID_SGRSTWeight', 'SLOStatic',
    'DISPID_SLWPronunciations', 'ISpeechPhraseReplacement',
    'DISPID_SRGetPropertyString', 'SPLO_DYNAMIC', 'DISPID_SVStatus',
    'ISpeechPhoneConverter', 'ISpeechGrammarRules',
    'eLEXTYPE_RESERVED8', 'SECFEmulateResult', 'SPRST_NUM_STATES',
    'SFTInput', 'DISPID_SLGetWords', 'ISpeechRecognizer', 'SVP_6',
    'SAFT22kHz16BitStereo', 'SVSFIsXML', 'SPINTERFERENCE_NONE',
    'SpeechPropertyResponseSpeed', 'SAFT24kHz16BitStereo',
    'SPTEXTSELECTIONINFO', 'DISPID_SOTCEnumerateTokens',
    'DISPID_SRStatus', 'SECFIgnoreWidth', 'DISPID_SPPs_NewEnum',
    'DISPID_SpeechMemoryStream', 'DISPID_SRRDiscardResultInfo',
    'tagSTATSTG', 'STSF_CommonAppData', 'SP_VISEME_17',
    'SpeechTokenKeyAttributes', 'SAFT11kHz16BitMono',
    'DISPID_SOTs_NewEnum', 'SpeechVisemeType', 'SVP_13', 'SPSLMA',
    'SRAORetainAudio', 'SpeechAudioState', 'DISPID_SMSSetData',
    'SVP_11', 'DISPID_SRCRecognizer', 'DISPID_SASFreeBufferSpace',
    'SAFTADPCM_11kHzMono', 'DISPID_SRCERecognizerStateChange',
    'SPEI_PHONEME', 'SVP_14', 'DISPID_SGRSTPropertyName',
    'DISPID_SPPConfidence', 'SVP_3', 'SPSHORTCUTPAIRLIST',
    'DISPID_SpeechGrammarRuleState', 'DISPID_SGRAttributes', 'SVP_15',
    'SVP_18', 'SBONone', 'SPGS_EXCLUSIVE', 'eLEXTYPE_MORPHOLOGY',
    'DISPID_SVSVisemeId', 'DISPID_SRCEInterference', 'SINone',
    'SpeechPropertyHighConfidenceThreshold', 'SP_VISEME_6',
    'ISpeechLexiconPronunciations', 'eLEXTYPE_PRIVATE19',
    'DISPID_SPEEngineConfidence', 'SVEBookmark',
    'SpeechGrammarWordType', 'SDKLLocalMachine', 'SASStop',
    'DISPID_SABIBufferSize', 'SpStreamFormatConverter', 'SVPOver',
    'SPEI_START_INPUT_STREAM',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'SINoSignal',
    'DISPID_SRGCmdLoadFromMemory', 'DISPID_SpeechPhraseRules',
    'SPAR_High', 'SpeechDiscardType', 'DISPID_SLWs_NewEnum',
    'SpMMAudioOut', 'SpNotifyTranslator', 'SPEI_VISEME',
    'DISPID_SpeechPhraseReplacement', 'SpeechGrammarState',
    'ISpRecognizer3', 'SPEI_END_SR_STREAM', 'SVEAllEvents',
    'SPRS_ACTIVE_USER_DELIMITED', 'SPWT_LEXICAL',
    'DISPID_SpeechAudioStatus', 'DISPID_SRGId', 'ISpRecognizer2',
    'DISPID_SRCEStartStream', 'SAFTCCITT_ALaw_11kHzMono',
    'SPAUDIOSTATE', 'DISPID_SRRAudio', 'SpeechRecognizerState',
    'SPWF_INPUT', 'SPEI_ADAPTATION', 'SPPS_Interjection',
    'DISPID_SBSWrite', 'DISPID_SVVoice', 'eLEXTYPE_PRIVATE18',
    'SPSVerb', 'DISPID_SGRSTType', 'DISPID_SRGDictationUnload',
    'STSF_AppData', 'SAFTADPCM_11kHzStereo',
    'DISPID_SpeechPhraseProperties', 'DISPID_SPCLangId',
    'SAFTCCITT_uLaw_11kHzStereo', 'DISPID_SLGetGenerationChange',
    'DISPID_SGRSAddWordTransition', 'DISPID_SPIRetainedSizeBytes',
    'DISPID_SRCRetainedAudioFormat', 'SRAONone', 'SPVOICESTATUS',
    'SPSNoun', 'SPEI_MAX_SR', 'SAFT44kHz16BitMono', 'SPAUDIOOPTIONS',
    'SpeechTokenShellFolder', 'ISpRecoGrammar',
    'DISPID_SVSyncronousSpeakTimeout', 'SDTProperty', 'SRERequestUI',
    'SpeechVoiceSpeakFlags', 'SAFT48kHz16BitStereo',
    'ISpPhoneticAlphabetSelection', 'SASPause',
    'DISPID_SRGIsPronounceable', 'SPBINARYGRAMMAR', 'ISpRecoResult',
    'DISPID_SPEsItem', 'DISPID_SAFGuid', 'eLEXTYPE_PRIVATE7',
    'DISPID_SVEStreamEnd', 'SAFTCCITT_ALaw_44kHzMono',
    'STCLocalServer', 'SREAdaptation', 'DISPID_SABufferNotifySize',
    'SAFT32kHz16BitMono', 'ISpeechPhraseAlternates',
    'ISpeechObjectToken', 'DISPID_SpeechRecoResult',
    'SPRECORESULTTIMES', 'SAFT8kHz16BitMono', 'SAFT16kHz8BitMono',
    'SPEVENT', 'SPSModifier', 'DISPID_SVSLastStreamNumberQueued',
    'Library', 'SPINTERFERENCE_TOOFAST', 'SPRECOCONTEXTSTATUS',
    'SP_VISEME_11', 'SPAS_RUN', 'SPCONTEXTSTATE',
    'DISPID_SLPs_NewEnum', 'DISPID_SpeechAudio', 'ISpeechLexiconWord',
    'SAFT44kHz16BitStereo', 'DISPID_SVAudioOutputStream',
    'ISpProperties', 'eLEXTYPE_PRIVATE1', 'DISPID_SGRInitialState',
    '_ISpeechVoiceEvents', 'DISPID_SPRules_NewEnum',
    'SpeechRegistryUserRoot', 'DISPID_SpeechVoiceEvent',
    'SAFTTrueSpeech_8kHz1BitMono', 'DISPID_SGRSTText', 'SAFTText',
    'SPSInterjection', 'SREStateChange', 'SDTRule', 'SP_VISEME_3',
    'SREPropertyStringChange', 'SITooQuiet', 'SPFILEMODE',
    'SPRULESTATE', 'SPWORDTYPE', 'SITooFast', 'DISPID_SMSGetData',
    'eLEXTYPE_PRIVATE20', 'DISPIDSPTSI_SelectionOffset',
    'eLEXTYPE_PRIVATE14', 'ISpRecoCategory', 'SECHighConfidence',
    'ISpNotifySource', 'DISPID_SPRsItem', 'DISPID_SPPsCount',
    'ISpObjectWithToken', 'SPPHRASE', 'ISpeechObjectTokens',
    'DISPID_SRCEBookmark', 'SPPS_Function', 'SVP_1',
    'SPEI_PROPERTY_NUM_CHANGE', 'SPSHORTCUTTYPE', 'SVP_10',
    'eLEXTYPE_VENDORLEXICON', 'DISPID_SPIAudioSizeTime',
    'DISPID_SOTCGetDataKey', 'SPPARTOFSPEECH', 'eLEXTYPE_PRIVATE3',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'SAFT16kHz16BitStereo', 'SAFTADPCM_44kHzMono',
    'SPFM_OPEN_READONLY', 'SPCT_DICTATION', 'SPAS_CLOSED',
    'SPEI_SENTENCE_BOUNDARY', 'SPEI_RESERVED2'
]

