#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyseur complet utilisant tous les moyens nécessaires pour identifier et corriger
TOUS les problèmes dans main.py
"""

import ast
import re
import sys
import os
import subprocess
import json
from typing import List, Dict, Any, Tuple

class ComprehensiveAnalyzer:
    def __init__(self):
        self.issues = []
        self.fixes_applied = []
        
    def run_syntax_check(self):
        """Vérification syntaxe avec compilation directe"""
        print("🔍 1. Vérification syntaxe Python...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test de compilation
            compile(content, 'src/main.py', 'exec')
            
            # Test AST
            ast.parse(content)
            
            print("✅ Syntaxe correcte")
            return True
        except SyntaxError as e:
            issue = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
            self.issues.append(issue)
            print(f"❌ {issue}")
            return False
        except Exception as e:
            issue = f"Erreur compilation: {e}"
            self.issues.append(issue)
            print(f"❌ {issue}")
            return False
    
    def analyze_with_flake8(self):
        """Analyse avec flake8 (si disponible)"""
        print("\n🔍 2. Analyse avec flake8...")
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'flake8', 'src/main.py', 
                '--max-line-length=120', '--ignore=E501,W503,E203'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Flake8: Aucun problème détecté")
                return True
            else:
                issues = result.stdout.strip().split('\n')
                for issue in issues:
                    if issue.strip():
                        self.issues.append(f"Flake8: {issue}")
                        print(f"⚠️ {issue}")
                return False
        except FileNotFoundError:
            print("⚠️ Flake8 non disponible")
            return True
        except Exception as e:
            print(f"⚠️ Erreur flake8: {e}")
            return True
    
    def analyze_type_annotations(self):
        """Analyse détaillée des annotations de type"""
        print("\n🔍 3. Analyse des annotations de type...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            # Analyser les fonctions sans annotations
            function_pattern = r'def\s+(\w+)\s*\([^)]*\)\s*:'
            functions_without_return = []
            
            for i, line in enumerate(lines, 1):
                if re.match(function_pattern, line):
                    if '->' not in line and '__init__' not in line:
                        func_name = re.match(function_pattern, line).group(1)
                        functions_without_return.append(f"Ligne {i}: Fonction '{func_name}' sans annotation de retour")
            
            # Analyser les variables sans type
            untyped_vars = []
            for i, line in enumerate(lines, 1):
                stripped = line.strip()
                if ' = ' in stripped and not stripped.startswith('#'):
                    if ':' not in stripped.split(' = ')[0]:
                        if not any(x in stripped for x in ['self.', 'def ', 'class ', 'import', 'from', '__']):
                            var_match = re.match(r'\s*(\w+)\s*=', stripped)
                            if var_match:
                                var_name = var_match.group(1)
                                untyped_vars.append(f"Ligne {i}: Variable '{var_name}' sans type")
            
            total_issues = len(functions_without_return) + len(untyped_vars)
            
            if total_issues == 0:
                print("✅ Annotations de type complètes")
                return True
            else:
                print(f"⚠️ {total_issues} problèmes d'annotations:")
                for issue in (functions_without_return + untyped_vars)[:10]:
                    self.issues.append(issue)
                    print(f"  - {issue}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur analyse types: {e}")
            return False
    
    def analyze_imports(self):
        """Analyse des problèmes d'imports"""
        print("\n🔍 4. Analyse des imports...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            import_issues = []
            in_function = False
            
            for i, line in enumerate(lines, 1):
                stripped = line.strip()
                
                if stripped.startswith('def '):
                    in_function = True
                elif stripped.startswith('class ') or (not stripped.startswith(' ') and stripped):
                    in_function = False
                
                if in_function and 'import ' in stripped and not stripped.startswith('#'):
                    if 'type: ignore' not in stripped:
                        import_issues.append(f"Ligne {i}: Import dans fonction sans type: ignore")
            
            if import_issues:
                print(f"⚠️ {len(import_issues)} problèmes d'imports:")
                for issue in import_issues:
                    self.issues.append(issue)
                    print(f"  - {issue}")
                return False
            else:
                print("✅ Imports corrects")
                return True
                
        except Exception as e:
            print(f"❌ Erreur analyse imports: {e}")
            return False
    
    def check_missing_dependencies(self):
        """Vérification des dépendances manquantes"""
        print("\n🔍 5. Vérification des dépendances...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Modules requis détectés dans le code
            required_modules = set()
            
            # Détecter les imports
            import_patterns = [
                r'from\s+(\w+)',
                r'import\s+(\w+)',
                r'from\s+(\w+\.\w+)',
            ]
            
            for pattern in import_patterns:
                matches = re.findall(pattern, content)
                required_modules.update(matches)
            
            # Modules critiques pour le bon fonctionnement
            critical_modules = {
                'PySide6': 'Interface graphique Qt',
                'sqlite3': 'Base de données SQLite',
                'cryptography': 'Chiffrement des données',
                'requests': 'Requêtes HTTP',
                'psutil': 'Informations système'
            }
            
            missing_critical = []
            for module, description in critical_modules.items():
                try:
                    __import__(module)
                except ImportError:
                    missing_critical.append(f"Module critique manquant: {module} ({description})")
            
            if missing_critical:
                print(f"⚠️ {len(missing_critical)} modules critiques manquants:")
                for issue in missing_critical:
                    self.issues.append(issue)
                    print(f"  - {issue}")
                return False
            else:
                print("✅ Toutes les dépendances critiques sont présentes")
                return True
                
        except Exception as e:
            print(f"❌ Erreur vérification dépendances: {e}")
            return False
    
    def analyze_code_quality(self):
        """Analyse de la qualité générale du code"""
        print("\n🔍 6. Analyse de la qualité du code...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            metrics = {
                'total_lines': len(lines),
                'functions': len(re.findall(r'def\s+\w+', content)),
                'classes': len(re.findall(r'class\s+\w+', content)),
                'type_annotations': content.count('->') + content.count(': str') + content.count(': int'),
                'error_handling': content.count('try:') + content.count('except'),
                'logging_calls': content.count('logging.'),
                'comments': len([l for l in lines if l.strip().startswith('#')]),
                'docstrings': content.count('"""') // 2,
                'todo_fixme': content.count('TODO') + content.count('FIXME') + content.count('XXX')
            }
            
            print("📊 Métriques de qualité:")
            for metric, value in metrics.items():
                print(f"  - {metric}: {value}")
            
            # Calcul du score de qualité
            quality_score = 0
            if metrics['type_annotations'] >= 40:
                quality_score += 25
            if metrics['error_handling'] >= 20:
                quality_score += 25
            if metrics['logging_calls'] >= 10:
                quality_score += 20
            if metrics['docstrings'] >= 5:
                quality_score += 15
            if metrics['todo_fixme'] == 0:
                quality_score += 15
            
            print(f"\n📈 Score de qualité: {quality_score}/100")
            
            if quality_score >= 80:
                print("✅ Qualité du code excellente")
                return True
            else:
                issue = f"Score de qualité insuffisant: {quality_score}/100"
                self.issues.append(issue)
                print(f"⚠️ {issue}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur analyse qualité: {e}")
            return False
    
    def generate_requirements_txt(self):
        """Génère un fichier requirements.txt pour les dépendances"""
        print("\n🔧 Génération du fichier requirements.txt...")
        
        requirements = [
            "PySide6>=6.5.0",
            "cryptography>=3.4.8",
            "requests>=2.28.0",
            "psutil>=5.9.0",
            "openpyxl>=3.0.10",
            "Pillow>=9.0.0"
        ]
        
        try:
            with open('requirements.txt', 'w', encoding='utf-8') as f:
                for req in requirements:
                    f.write(f"{req}\n")
            
            print("✅ Fichier requirements.txt créé")
            self.fixes_applied.append("Fichier requirements.txt généré")
            return True
        except Exception as e:
            print(f"❌ Erreur génération requirements.txt: {e}")
            return False
    
    def run_comprehensive_analysis(self):
        """Exécute l'analyse complète"""
        print("🚀 ANALYSE COMPLÈTE AVEC TOUS LES MOYENS NÉCESSAIRES")
        print("=" * 70)
        
        checks = [
            ("Syntaxe Python", self.run_syntax_check),
            ("Analyse Flake8", self.analyze_with_flake8),
            ("Annotations de type", self.analyze_type_annotations),
            ("Problèmes d'imports", self.analyze_imports),
            ("Dépendances manquantes", self.check_missing_dependencies),
            ("Qualité du code", self.analyze_code_quality)
        ]
        
        passed_checks = 0
        
        for check_name, check_func in checks:
            try:
                if check_func():
                    passed_checks += 1
            except Exception as e:
                error_msg = f"Erreur dans {check_name}: {e}"
                self.issues.append(error_msg)
                print(f"❌ {error_msg}")
        
        # Génération des outils d'aide
        self.generate_requirements_txt()
        
        # Résumé final
        print("\n" + "="*70)
        print("📋 RÉSUMÉ DE L'ANALYSE COMPLÈTE")
        print("="*70)
        
        print(f"✅ Vérifications réussies: {passed_checks}/{len(checks)}")
        print(f"❌ Problèmes détectés: {len(self.issues)}")
        print(f"🔧 Corrections appliquées: {len(self.fixes_applied)}")
        
        if self.issues:
            print("\n🔍 PROBLÈMES IDENTIFIÉS:")
            for i, issue in enumerate(self.issues, 1):
                print(f"{i:3d}. {issue}")
        
        if self.fixes_applied:
            print("\n🔧 CORRECTIONS APPLIQUÉES:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"{i:3d}. {fix}")
        
        success_rate = (passed_checks / len(checks)) * 100
        
        if success_rate >= 90 and len(self.issues) <= 5:
            print("\n🎉 EXCELLENT ! Le code est de très haute qualité")
            return True
        elif success_rate >= 70:
            print("\n✅ BIEN ! Quelques améliorations mineures possibles")
            return True
        else:
            print("\n⚠️ Des améliorations importantes sont nécessaires")
            return False

def main():
    """Fonction principale"""
    if not os.path.exists('src/main.py'):
        print("❌ Fichier src/main.py non trouvé")
        return 1
    
    analyzer = ComprehensiveAnalyzer()
    success = analyzer.run_comprehensive_analysis()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
