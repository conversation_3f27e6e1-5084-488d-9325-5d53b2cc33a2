<!-- Generated by SpecStory -->

# /fix Le type de « title » est partiellement inconn (2025-06-08 12:34:35)

_**User**_

/fix Le type de « title » est partiellement inconnu
  Le type de "title" est "(label: str, fontdict: dict[str, Any] | None = None, loc: Literal['left', 'center', 'right'] | None = None, pad: float | None = None, *, y: float | None = None, **kwargs: Unknown) -> Text"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

**Résumé du correctif :**  
La fonction `plt.title` attend le titre comme premier argument positionnel (`str`), pas comme argument nommé `label`. Il suffit donc de remplacer `plt.title(label="Articles par catégorie")` par `plt.title("Articles par catégorie")`.

````python
# ...existing code...
                plt.figure(figsize=(8, 4))  # type: ignore
                plt.bar([str(k) for k in stats_categorie.keys()], [int(v) for v in stats_categorie.values()])  # type: ignore
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

---

