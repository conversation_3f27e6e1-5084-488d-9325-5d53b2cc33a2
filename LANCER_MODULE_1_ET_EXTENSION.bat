@echo off
title Lancement Module 1 + Extension Robot IA
color 0A

echo.
echo ================================================
echo    LANCEMENT MODULE 1 + EXTENSION VS CODE
echo    Robot IA SamNord - SamNord@110577
echo ================================================
echo.

echo Etape 1: Installation extension VS Code...
mkdir %USERPROFILE%\.vscode\extensions\robot-ia-samnord-visible-1.0.0 2>nul
copy package_extension_visible.json %USERPROFILE%\.vscode\extensions\robot-ia-samnord-visible-1.0.0\package.json >nul 2>&1
copy extension_robot_ia_visible.js %USERPROFILE%\.vscode\extensions\robot-ia-samnord-visible-1.0.0\extension.js >nul 2>&1
echo Extension installee !

echo.
echo Etape 2: Lancement Module 1 Intelligence IA en externe...
start "Intelligence IA Transcendante" cmd /k "color 0B && title Intelligence IA Transcendante - SamNord@110577 && python MODULE_1_INTELLIGENCE_IA_EXTERNE.py"
echo Module 1 lance en externe !

echo.
echo Etape 3: Redemarrage VS Code pour voir l'extension...
echo.

echo ================================================
echo    LANCEMENT TERMINE !
echo ================================================
echo.
echo INSTRUCTIONS:
echo 1. Module 1 Intelligence IA lance en externe
echo 2. Extension Robot IA installee dans VS Code
echo 3. Redemarrez VS Code pour voir l'extension
echo 4. Cliquez sur l'icone Robot dans VS Code
echo 5. Ou utilisez Ctrl+Shift+R
echo.
echo IDENTIFIANTS:
echo Utilisateur: SamNord@110577
echo Mot de passe: NorDine@22
echo.

pause
exit /b 0
