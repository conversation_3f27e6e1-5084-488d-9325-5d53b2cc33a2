const vscode = require('vscode');
const { exec } = require('child_process');

/**
 * 🤖 ROBOT IA SAMNORD - EXTENSION VS CODE
 * Interface comme Augment Agent
 */

let chatPanel = null;

function activate(context) {
    console.log('🤖 Robot IA SamNord activé');
    
    // Commande Chat
    const chatCommand = vscode.commands.registerCommand('robotIA.chat', () => {
        showChatPanel();
    });
    
    // Commande Modules
    const modulesCommand = vscode.commands.registerCommand('robotIA.modules', () => {
        showModules();
    });
    
    // Provider pour les vues
    const chatProvider = new ChatViewProvider(context.extensionUri);
    const modulesProvider = new ModulesProvider();
    
    context.subscriptions.push(
        chatCommand,
        modulesCommand,
        vscode.window.registerWebviewViewProvider('robotIA.chat', chatProvider),
        vscode.window.registerTreeDataProvider('robotIA.modules', modulesProvider)
    );
    
    // Auto-activation
    vscode.window.showInformationMessage('🤖 Robot IA SamNord activé ! Ctrl+Shift+R pour chat');
}

function showChatPanel() {
    if (chatPanel) {
        chatPanel.reveal(vscode.ViewColumn.One);
        return;
    }

    chatPanel = vscode.window.createWebviewPanel(
        'robotIAChat',
        '🤖 Robot IA SamNord - Chat',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            retainContextWhenHidden: true
        }
    );
    
    chatPanel.webview.html = getChatHTML();
    
    chatPanel.webview.onDidReceiveMessage(message => {
        switch (message.command) {
            case 'sendMessage':
                handleMessage(message.text);
                break;
            case 'executeModule':
                executeModule(message.module);
                break;
        }
    });
    
    chatPanel.onDidDispose(() => {
        chatPanel = null;
    });
}

function getChatHTML() {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { 
                font-family: 'Segoe UI', sans-serif;
                background: #1e1e1e;
                color: #ffffff;
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }
            .header {
                background: #2d2d30;
                border-bottom: 2px solid #007acc;
                padding: 15px;
                text-align: center;
            }
            .chat-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            .messages {
                flex: 1;
                overflow-y: auto;
                padding: 20px;
            }
            .message {
                margin-bottom: 15px;
                padding: 12px;
                border-radius: 8px;
                max-width: 80%;
            }
            .user-message {
                background: #007acc;
                margin-left: auto;
                text-align: right;
            }
            .robot-message {
                background: #2d2d30;
                border-left: 4px solid #00d4ff;
            }
            .input-area {
                background: #2d2d30;
                border-top: 1px solid #007acc;
                padding: 15px;
                display: flex;
                gap: 10px;
            }
            .message-input {
                flex: 1;
                background: #1e1e1e;
                border: 1px solid #007acc;
                border-radius: 6px;
                padding: 12px;
                color: #ffffff;
            }
            .send-btn {
                background: #007acc;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                cursor: pointer;
            }
            .send-btn:hover {
                background: #005a9e;
            }
            .modules-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 10px;
                margin: 15px 0;
            }
            .module-btn {
                background: #2d2d30;
                border: 1px solid #007acc;
                border-radius: 6px;
                padding: 10px;
                cursor: pointer;
                text-align: center;
                transition: all 0.3s;
                color: #ffffff;
                font-size: 12px;
            }
            .module-btn:hover {
                background: #3e3e42;
                border-color: #00d4ff;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>🤖 Robot IA SamNord - Assistant Transcendant</h2>
            <p>👤 SamNord@110577 | Interface comme Augment Agent</p>
        </div>
        
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message robot-message">
                    <strong>🤖 Robot IA:</strong> Bonjour SamNord@110577 ! Je suis votre Robot IA Transcendant, intégré directement dans VS Code comme Augment Agent. Comment puis-je vous aider ?
                </div>
                
                <div class="message robot-message">
                    <strong>📦 Modules disponibles:</strong>
                    <div class="modules-grid">
                        <div class="module-btn" onclick="executeModule('intelligence')">🧠 Intelligence IA</div>
                        <div class="module-btn" onclick="executeModule('wifi')">📡 WiFi Manager</div>
                        <div class="module-btn" onclick="executeModule('bluetooth')">📱 Bluetooth</div>
                        <div class="module-btn" onclick="executeModule('cybersecurity')">🛡️ Cybersécurité</div>
                        <div class="module-btn" onclick="executeModule('translation')">🌍 Traduction</div>
                        <div class="module-btn" onclick="executeModule('voice')">🗣️ Communication</div>
                        <div class="module-btn" onclick="executeModule('memory')">🧠 Mémoire</div>
                        <div class="module-btn" onclick="executeModule('tracking')">📱 Tracking</div>
                        <div class="module-btn" onclick="executeModule('compta')">💼 Comptabilité</div>
                        <div class="module-btn" onclick="executeModule('video')">📱 Vidéo</div>
                    </div>
                </div>
                
                <div class="message robot-message">
                    <strong>🎯 Commandes rapides:</strong><br>
                    • "modules" - Voir tous les modules<br>
                    • "wifi scan" - Scanner réseaux WiFi<br>
                    • "bluetooth scan" - Scanner appareils Bluetooth<br>
                    • "traduire [texte]" - Traduction instantanée<br>
                    • "sécurité" - Audit cybersécurité<br>
                    • "aide" - Liste complète des commandes
                </div>
            </div>
            
            <div class="input-area">
                <input type="text" class="message-input" id="messageInput" placeholder="Tapez votre message ou commande..." onkeypress="handleKeyPress(event)">
                <button class="send-btn" onclick="sendMessage()">Envoyer</button>
            </div>
        </div>
        
        <script>
            const vscode = acquireVsCodeApi();
            
            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (message) {
                    addMessage(message, 'user');
                    input.value = '';
                    
                    // Traitement du message
                    processMessage(message);
                }
            }
            
            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }
            
            function addMessage(text, sender) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${sender}-message\`;
                
                if (sender === 'user') {
                    messageDiv.innerHTML = \`<strong>👤 Vous:</strong> \${text}\`;
                } else {
                    messageDiv.innerHTML = \`<strong>🤖 Robot IA:</strong> \${text}\`;
                }
                
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
            }
            
            function processMessage(message) {
                const msg = message.toLowerCase();
                let response = '';
                
                if (msg.includes('bonjour') || msg.includes('salut')) {
                    response = 'Bonjour SamNord@110577 ! Je suis prêt à vous assister. Que puis-je faire pour vous ?';
                } else if (msg.includes('modules')) {
                    response = 'J\\'ai 40+ modules : Intelligence IA, WiFi, Bluetooth, Cybersécurité, Traduction, Communication, Mémoire, Tracking, Comptabilité, Vidéo, etc. Lequel vous intéresse ?';
                } else if (msg.includes('wifi')) {
                    response = '📡 Module WiFi activé ! Je peux scanner les réseaux, gérer les connexions, créer des hotspots. Commandes: "wifi scan", "wifi connect", "wifi hotspot"';
                    executeModule('wifi');
                } else if (msg.includes('bluetooth')) {
                    response = '📱 Module Bluetooth activé ! Scan d\\'appareils, appairage, transferts disponibles. Commandes: "bluetooth scan", "bluetooth pair"';
                    executeModule('bluetooth');
                } else if (msg.includes('sécurité') || msg.includes('cyber')) {
                    response = '🛡️ Cybersécurité activée ! Audit de sécurité, détection vulnérabilités, protection impénétrable. Lancement scan...';
                    executeModule('cybersecurity');
                } else if (msg.includes('traduire')) {
                    const text = message.replace(/traduire/i, '').trim();
                    response = \`🌍 Traduction activée ! Texte: "\${text}" - Traduction disponible en 100+ langues.\`;
                    executeModule('translation');
                } else if (msg.includes('intelligence') || msg.includes('ia')) {
                    response = '🧠 Intelligence IA Transcendante activée ! Niveau divin, raisonnement avancé, créativité transcendante disponible.';
                    executeModule('intelligence');
                } else if (msg.includes('mémoire')) {
                    response = '🧠 Mémoire Parfaite activée ! Je me souviens de toutes vos demandes et préférences.';
                    executeModule('memory');
                } else if (msg.includes('aide') || msg.includes('help')) {
                    response = '🎯 Commandes disponibles:\\n• modules - Liste modules\\n• wifi scan - Scanner WiFi\\n• bluetooth scan - Scanner Bluetooth\\n• traduire [texte] - Traduction\\n• sécurité - Audit sécurité\\n• intelligence - IA transcendante\\n• mémoire - Rappel données';
                } else if (msg.includes('codage') || msg.includes('code')) {
                    response = '💻 Je peux vous aider avec le codage ! Génération de code, débogage, optimisation, snippets. Quel langage ou problème ?';
                } else {
                    response = 'Intéressant ! Je peux vous aider avec mes 40+ modules. Tapez "aide" pour voir les commandes ou "modules" pour la liste complète.';
                }
                
                setTimeout(() => {
                    addMessage(response, 'robot');
                }, 500);
            }
            
            function executeModule(module) {
                vscode.postMessage({
                    command: 'executeModule',
                    module: module
                });
                
                addMessage(\`✅ Module \${module} lancé avec succès !\`, 'robot');
            }
        </script>
    </body>
    </html>`;
}

function handleMessage(message) {
    console.log('Message reçu:', message);
}

function executeModule(module) {
    const moduleMap = {
        'intelligence': 'modules/intelligence_ia_transcendante.py',
        'wifi': 'modules/wifi_avance_complet.py',
        'bluetooth': 'modules/bluetooth_complet.py',
        'cybersecurity': 'modules/cybersecurite_ethique.py',
        'translation': 'modules/traduction_temps_reel.py',
        'voice': 'modules/communication_multilingue.py',
        'memory': 'modules/memoire_parfaite_robot.py',
        'tracking': 'modules/tracking_telephones.py',
        'compta': 'modules/master_compta_general.py',
        'video': 'modules/enregistrement_video_telephones.py'
    };
    
    const modulePath = moduleMap[module];
    if (modulePath) {
        exec(\`python \${modulePath}\`, (error, stdout, stderr) => {
            if (error) {
                console.error(\`Erreur module \${module}:\`, error);
                vscode.window.showErrorMessage(\`Erreur module \${module}: \${error.message}\`);
            } else {
                vscode.window.showInformationMessage(\`Module \${module} exécuté avec succès\`);
            }
        });
    }
}

function showModules() {
    const modules = [
        '🧠 Intelligence IA Transcendante',
        '📡 WiFi Avancé Complet',
        '📱 Bluetooth Complet',
        '🛡️ Cybersécurité Éthique',
        '🌍 Traduction Temps Réel',
        '🗣️ Communication Multilingue',
        '🧠 Mémoire Parfaite',
        '📱 Tracking Téléphones',
        '💼 Master Compta Général',
        '📱 Enregistrement Vidéo'
    ];
    
    vscode.window.showQuickPick(modules, {
        placeHolder: '🤖 Choisir un module Robot IA'
    }).then(selection => {
        if (selection) {
            vscode.window.showInformationMessage(\`Module sélectionné: \${selection}\`);
        }
    });
}

// Providers pour les vues
class ChatViewProvider {
    constructor(extensionUri) {
        this.extensionUri = extensionUri;
    }
    
    resolveWebviewView(webviewView) {
        webviewView.webview.options = {
            enableScripts: true
        };
        webviewView.webview.html = getChatHTML();
    }
}

class ModulesProvider {
    getTreeItem(element) {
        return element;
    }
    
    getChildren(element) {
        if (!element) {
            return [
                new vscode.TreeItem('🧠 Intelligence IA', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📡 WiFi Manager', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📱 Bluetooth', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🛡️ Cybersécurité', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🌍 Traduction', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🗣️ Communication', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('🧠 Mémoire', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📱 Tracking', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('💼 Comptabilité', vscode.TreeItemCollapsibleState.None),
                new vscode.TreeItem('📱 Vidéo', vscode.TreeItemCollapsibleState.None)
            ];
        }
        return [];
    }
}

function deactivate() {}

module.exports = {
    activate,
    deactivate
};
