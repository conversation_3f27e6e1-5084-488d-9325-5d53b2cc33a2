#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GESTIONNAIRE IA DUAL - OLLAMA + DEEPSEEK
Gestion automatique des mises à jour et fonctionnalités complètes
Installation et configuration automatique
"""

import os
import sys
import json
import requests
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path
import logging
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QFrame, QTextEdit, QLineEdit, QGroupBox, QTabWidget,
    QComboBox, QProgressBar, QMessageBox, QSplitter, QInputDialog
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QColor

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ia_dual.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IAUpdateWorker(QThread):
    """Worker pour les mises à jour automatiques des IA"""
    update_progress = Signal(str, int)
    update_finished = Signal(str, bool)
    
    def __init__(self, ia_type):
        super().__init__()
        self.ia_type = ia_type  # "ollama" ou "deepseek"
    
    def run(self):
        """Exécute la mise à jour"""
        try:
            if self.ia_type == "ollama":
                self.update_ollama()
            elif self.ia_type == "deepseek":
                self.update_deepseek()
        except Exception as e:
            logger.error(f"Erreur mise à jour {self.ia_type}: {str(e)}")
            self.update_finished.emit(self.ia_type, False)
    
    def update_ollama(self):
        """Met à jour Ollama"""
        self.update_progress.emit("ollama", 10)
        
        # Vérification version actuelle
        try:
            result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
            current_version = result.stdout.strip() if result.returncode == 0 else "Non installé"
            self.update_progress.emit("ollama", 30)
        except:
            current_version = "Non installé"
        
        # Installation/Mise à jour Ollama
        if current_version == "Non installé":
            self.update_progress.emit("ollama", 50)
            # Installation Ollama
            if os.name == 'nt':  # Windows
                # Téléchargement Ollama pour Windows
                url = "https://ollama.ai/download/windows"
                self.download_and_install_ollama(url)
            else:  # Linux/Mac
                subprocess.run(["curl", "-fsSL", "https://ollama.ai/install.sh", "|", "sh"], shell=True)
        else:
            # Mise à jour des modèles
            self.update_progress.emit("ollama", 60)
            subprocess.run(["ollama", "pull", "llama3.2"], check=False)
            subprocess.run(["ollama", "pull", "llama3.1"], check=False)
            subprocess.run(["ollama", "pull", "codellama"], check=False)
        
        self.update_progress.emit("ollama", 100)
        self.update_finished.emit("ollama", True)
    
    def update_deepseek(self):
        """Met à jour DeepSeek"""
        self.update_progress.emit("deepseek", 10)
        
        # Installation DeepSeek via pip
        try:
            # Mise à jour pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
            self.update_progress.emit("deepseek", 30)
            
            # Installation/Mise à jour DeepSeek
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", 
                "openai", "deepseek-api", "transformers", "torch"
            ], check=True)
            self.update_progress.emit("deepseek", 70)
            
            # Configuration DeepSeek
            self.configure_deepseek()
            self.update_progress.emit("deepseek", 100)
            self.update_finished.emit("deepseek", True)
            
        except Exception as e:
            logger.error(f"Erreur installation DeepSeek: {str(e)}")
            self.update_finished.emit("deepseek", False)
    
    def download_and_install_ollama(self, url):
        """Télécharge et installe Ollama sur Windows"""
        try:
            # Téléchargement
            response = requests.get(url, stream=True)
            installer_path = "ollama_installer.exe"
            
            with open(installer_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Installation silencieuse
            subprocess.run([installer_path, "/S"], check=True)
            os.remove(installer_path)
            
        except Exception as e:
            logger.error(f"Erreur téléchargement Ollama: {str(e)}")
    
    def configure_deepseek(self):
        """Configure DeepSeek"""
        config = {
            "api_key": "YOUR_DEEPSEEK_API_KEY",
            "model": "deepseek-chat",
            "base_url": "https://api.deepseek.com/v1",
            "max_tokens": 4000,
            "temperature": 0.7
        }
        
        config_dir = Path.home() / ".deepseek"
        config_dir.mkdir(exist_ok=True)
        
        with open(config_dir / "config.json", "w") as f:
            json.dump(config, f, indent=2)

class OllamaManager:
    """Gestionnaire Ollama"""
    
    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.models = ["llama3.2", "llama3.1", "codellama", "mistral"]
    
    def is_available(self):
        """Vérifie si Ollama est disponible"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_models(self):
        """Récupère la liste des modèles"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return [model["name"] for model in data.get("models", [])]
        except:
            pass
        return []
    
    def generate(self, prompt, model="llama3.2"):
        """Génère une réponse avec Ollama"""
        try:
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 2000
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                return response.json().get("response", "Erreur de génération")
            else:
                return f"Erreur HTTP Ollama: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return "⏱️ Timeout Ollama - Requête trop longue"
        except Exception as e:
            return f"❌ Erreur Ollama: {str(e)}"

class DeepSeekManager:
    """Gestionnaire DeepSeek"""
    
    def __init__(self):
        self.config_path = Path.home() / ".deepseek" / "config.json"
        self.load_config()
    
    def load_config(self):
        """Charge la configuration DeepSeek"""
        try:
            if self.config_path.exists():
                with open(self.config_path, "r") as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "api_key": "",
                    "model": "deepseek-chat",
                    "base_url": "https://api.deepseek.com/v1",
                    "max_tokens": 4000,
                    "temperature": 0.7
                }
        except:
            self.config = {}
    
    def is_available(self):
        """Vérifie si DeepSeek est disponible"""
        return bool(self.config.get("api_key"))
    
    def generate(self, prompt, model=None):
        """Génère une réponse avec DeepSeek"""
        if not self.is_available():
            return "❌ DeepSeek non configuré - Clé API manquante"
        
        try:
            import openai
            
            # Configuration client OpenAI pour DeepSeek
            client = openai.OpenAI(
                api_key=self.config["api_key"],
                base_url=self.config["base_url"]
            )
            
            response = client.chat.completions.create(
                model=model or self.config["model"],
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config["max_tokens"],
                temperature=self.config["temperature"]
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"❌ Erreur DeepSeek: {str(e)}"
    
    def set_api_key(self, api_key):
        """Configure la clé API DeepSeek"""
        self.config["api_key"] = api_key
        
        # Sauvegarde
        self.config_path.parent.mkdir(exist_ok=True)
        with open(self.config_path, "w") as f:
            json.dump(self.config, f, indent=2)

class IADualInterface(QMainWindow):
    """Interface pour les deux IA"""
    
    def __init__(self):
        super().__init__()
        self.ollama = OllamaManager()
        self.deepseek = DeepSeekManager()
        self.setup_ui()
        self.check_updates_on_startup()
    
    def setup_ui(self):
        """Configure l'interface dual IA"""
        self.setWindowTitle("🤖 IA DUAL - OLLAMA + DEEPSEEK")
        self.setFixedSize(1400, 900)
        
        # Style VSCode
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #d4d4d4;
            }
            QWidget {
                background-color: #1e1e1e;
                color: #d4d4d4;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                background-color: #252526;
                border: 1px solid #0066ff;
                border-radius: 6px;
                margin: 8px;
                padding-top: 15px;
                font-weight: bold;
            }
            QGroupBox::title {
                color: #d4d4d4;
                background-color: #2d2d30;
                border: 1px solid #0066ff;
                border-radius: 4px;
                padding: 5px 10px;
                margin-left: 10px;
            }
            QPushButton {
                background-color: #0e639c;
                color: white;
                border: 1px solid #0066ff;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1177bb;
            }
            QLineEdit, QComboBox {
                background-color: #3c3c3c;
                color: #d4d4d4;
                border: 1px solid #0066ff;
                border-radius: 4px;
                padding: 6px;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #0066ff;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11pt;
                line-height: 1.4;
            }
            QTabWidget::pane {
                background-color: #252526;
                border: 1px solid #0066ff;
            }
            QTabBar::tab {
                background-color: #2d2d30;
                color: #d4d4d4;
                border: 1px solid #0066ff;
                padding: 8px 16px;
                margin: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0e639c;
            }
            QProgressBar {
                background-color: #3c3c3c;
                border: 1px solid #0066ff;
                border-radius: 4px;
                text-align: center;
                color: #d4d4d4;
            }
            QProgressBar::chunk {
                background-color: #0e639c;
                border-radius: 2px;
            }
        """)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        self.create_header(layout)
        
        # Zone de mise à jour
        self.create_update_zone(layout)
        
        # Onglets IA
        self.create_ia_tabs(layout)
        
        # Barre de statut
        self.statusBar().showMessage("🤖 IA Dual - Ollama + DeepSeek - Prêt")
    
    def create_header(self, layout):
        """En-tête avec logos"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #001122, stop:0.5 #002200, stop:1 #001122);
                border: 2px solid #0066ff;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Logo Ollama
        ollama_logo = QLabel("🦙 OLLAMA")
        ollama_logo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        ollama_logo.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #00ff00;
                background-color: #2a2a2a;
                border: 2px solid #00ff00;
                border-radius: 6px;
                padding: 10px;
                margin: 5px;
            }
        """)
        header_layout.addWidget(ollama_logo)
        
        # Titre central
        titre = QLabel("🤖 IA DUAL SYSTEM\nOLLAMA + DEEPSEEK")
        titre.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titre.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #ffffff;
                background-color: #2a2a2a;
                border: 2px solid #0066ff;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
        """)
        header_layout.addWidget(titre, 1)
        
        # Logo DeepSeek
        deepseek_logo = QLabel("🧠 DEEPSEEK")
        deepseek_logo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        deepseek_logo.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #ff6600;
                background-color: #2a2a2a;
                border: 2px solid #ff6600;
                border-radius: 6px;
                padding: 10px;
                margin: 5px;
            }
        """)
        header_layout.addWidget(deepseek_logo)
        
        layout.addWidget(header_frame)

    def create_update_zone(self, layout):
        """Zone de mise à jour automatique"""
        update_group = QGroupBox("🔄 MISES À JOUR AUTOMATIQUES")
        update_layout = QVBoxLayout(update_group)

        # Statut des IA
        status_layout = QHBoxLayout()

        # Statut Ollama
        self.ollama_status = QLabel("🦙 OLLAMA: Vérification...")
        self.ollama_status.setStyleSheet("color: #ffaa00; font-weight: bold;")
        status_layout.addWidget(self.ollama_status)

        # Statut DeepSeek
        self.deepseek_status = QLabel("🧠 DEEPSEEK: Vérification...")
        self.deepseek_status.setStyleSheet("color: #ffaa00; font-weight: bold;")
        status_layout.addWidget(self.deepseek_status)

        update_layout.addLayout(status_layout)

        # Barres de progression
        progress_layout = QHBoxLayout()

        self.ollama_progress = QProgressBar()
        self.ollama_progress.setVisible(False)
        progress_layout.addWidget(QLabel("Ollama:"))
        progress_layout.addWidget(self.ollama_progress)

        self.deepseek_progress = QProgressBar()
        self.deepseek_progress.setVisible(False)
        progress_layout.addWidget(QLabel("DeepSeek:"))
        progress_layout.addWidget(self.deepseek_progress)

        update_layout.addLayout(progress_layout)

        # Boutons de mise à jour manuelle
        buttons_layout = QHBoxLayout()

        btn_update_ollama = QPushButton("🔄 METTRE À JOUR OLLAMA")
        btn_update_ollama.clicked.connect(self.manual_update_ollama)
        buttons_layout.addWidget(btn_update_ollama)

        btn_update_deepseek = QPushButton("🔄 METTRE À JOUR DEEPSEEK")
        btn_update_deepseek.clicked.connect(self.manual_update_deepseek)
        buttons_layout.addWidget(btn_update_deepseek)

        btn_config_deepseek = QPushButton("⚙️ CONFIGURER DEEPSEEK")
        btn_config_deepseek.clicked.connect(self.configure_deepseek_ui)
        buttons_layout.addWidget(btn_config_deepseek)

        update_layout.addLayout(buttons_layout)

        layout.addWidget(update_group)

    def create_ia_tabs(self, layout):
        """Onglets pour les deux IA"""
        self.tabs = QTabWidget()

        # Onglet Ollama
        self.tabs.addTab(self.create_ollama_tab(), "🦙 OLLAMA")

        # Onglet DeepSeek
        self.tabs.addTab(self.create_deepseek_tab(), "🧠 DEEPSEEK")

        # Onglet Comparaison
        self.tabs.addTab(self.create_comparison_tab(), "⚖️ COMPARAISON")

        layout.addWidget(self.tabs)

    def create_ollama_tab(self):
        """Onglet Ollama"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Configuration Ollama
        config_group = QGroupBox("⚙️ CONFIGURATION OLLAMA")
        config_layout = QHBoxLayout(config_group)

        config_layout.addWidget(QLabel("Modèle:"))
        self.ollama_model = QComboBox()
        self.ollama_model.addItems(["llama3.2", "llama3.1", "codellama", "mistral"])
        config_layout.addWidget(self.ollama_model)

        config_layout.addWidget(QLabel("URL:"))
        self.ollama_url = QLineEdit("http://localhost:11434")
        config_layout.addWidget(self.ollama_url)

        layout.addWidget(config_group)

        # Chat Ollama
        chat_group = QGroupBox("💬 CONVERSATION OLLAMA")
        chat_layout = QVBoxLayout(chat_group)

        self.ollama_history = QTextEdit()
        self.ollama_history.setMinimumHeight(300)
        self.ollama_history.setReadOnly(True)
        chat_layout.addWidget(self.ollama_history)

        # Zone saisie Ollama
        input_layout = QHBoxLayout()
        self.ollama_input = QLineEdit()
        self.ollama_input.setPlaceholderText("Votre question pour Ollama...")
        self.ollama_input.returnPressed.connect(self.send_to_ollama)
        input_layout.addWidget(self.ollama_input)

        btn_send_ollama = QPushButton("🚀 ENVOYER")
        btn_send_ollama.clicked.connect(self.send_to_ollama)
        input_layout.addWidget(btn_send_ollama)

        chat_layout.addLayout(input_layout)
        layout.addWidget(chat_group)

        return widget

    def create_deepseek_tab(self):
        """Onglet DeepSeek"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Configuration DeepSeek
        config_group = QGroupBox("⚙️ CONFIGURATION DEEPSEEK")
        config_layout = QHBoxLayout(config_group)

        config_layout.addWidget(QLabel("Modèle:"))
        self.deepseek_model = QComboBox()
        self.deepseek_model.addItems(["deepseek-chat", "deepseek-coder", "deepseek-math"])
        config_layout.addWidget(self.deepseek_model)

        config_layout.addWidget(QLabel("API Key:"))
        self.deepseek_key = QLineEdit()
        self.deepseek_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.deepseek_key.setPlaceholderText("Votre clé API DeepSeek...")
        config_layout.addWidget(self.deepseek_key)

        layout.addWidget(config_group)

        # Chat DeepSeek
        chat_group = QGroupBox("💬 CONVERSATION DEEPSEEK")
        chat_layout = QVBoxLayout(chat_group)

        self.deepseek_history = QTextEdit()
        self.deepseek_history.setMinimumHeight(300)
        self.deepseek_history.setReadOnly(True)
        chat_layout.addWidget(self.deepseek_history)

        # Zone saisie DeepSeek
        input_layout = QHBoxLayout()
        self.deepseek_input = QLineEdit()
        self.deepseek_input.setPlaceholderText("Votre question pour DeepSeek...")
        self.deepseek_input.returnPressed.connect(self.send_to_deepseek)
        input_layout.addWidget(self.deepseek_input)

        btn_send_deepseek = QPushButton("🚀 ENVOYER")
        btn_send_deepseek.clicked.connect(self.send_to_deepseek)
        input_layout.addWidget(btn_send_deepseek)

        chat_layout.addLayout(input_layout)
        layout.addWidget(chat_group)

        return widget

    def create_comparison_tab(self):
        """Onglet comparaison des IA"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Zone de comparaison
        comp_group = QGroupBox("⚖️ COMPARAISON OLLAMA VS DEEPSEEK")
        comp_layout = QVBoxLayout(comp_group)

        # Question commune
        question_layout = QHBoxLayout()
        question_layout.addWidget(QLabel("Question:"))
        self.comparison_input = QLineEdit()
        self.comparison_input.setPlaceholderText("Posez la même question aux deux IA...")
        question_layout.addWidget(self.comparison_input)

        btn_compare = QPushButton("⚖️ COMPARER")
        btn_compare.clicked.connect(self.compare_ias)
        question_layout.addWidget(btn_compare)

        comp_layout.addLayout(question_layout)

        # Résultats côte à côte
        results_layout = QHBoxLayout()

        # Résultat Ollama
        ollama_result_group = QGroupBox("🦙 RÉPONSE OLLAMA")
        ollama_result_layout = QVBoxLayout(ollama_result_group)
        self.ollama_comparison_result = QTextEdit()
        self.ollama_comparison_result.setMaximumHeight(250)
        ollama_result_layout.addWidget(self.ollama_comparison_result)
        results_layout.addWidget(ollama_result_group)

        # Résultat DeepSeek
        deepseek_result_group = QGroupBox("🧠 RÉPONSE DEEPSEEK")
        deepseek_result_layout = QVBoxLayout(deepseek_result_group)
        self.deepseek_comparison_result = QTextEdit()
        self.deepseek_comparison_result.setMaximumHeight(250)
        deepseek_result_layout.addWidget(self.deepseek_comparison_result)
        results_layout.addWidget(deepseek_result_group)

        comp_layout.addLayout(results_layout)

        # Analyse comparative
        analysis_group = QGroupBox("📊 ANALYSE COMPARATIVE")
        analysis_layout = QVBoxLayout(analysis_group)
        self.comparison_analysis = QTextEdit()
        self.comparison_analysis.setMaximumHeight(150)
        self.comparison_analysis.setPlaceholderText("L'analyse comparative apparaîtra ici...")
        analysis_layout.addWidget(self.comparison_analysis)
        comp_layout.addLayout(analysis_layout)

        layout.addWidget(comp_group)

        return widget

    def check_updates_on_startup(self):
        """Vérifie les mises à jour au démarrage"""
        # Vérification Ollama
        QTimer.singleShot(1000, self.check_ollama_status)

        # Vérification DeepSeek
        QTimer.singleShot(2000, self.check_deepseek_status)

        # Mise à jour automatique si nécessaire
        QTimer.singleShot(3000, self.auto_update_if_needed)

    def check_ollama_status(self):
        """Vérifie le statut d'Ollama"""
        if self.ollama.is_available():
            models = self.ollama.get_models()
            self.ollama_status.setText(f"🦙 OLLAMA: ✅ Actif ({len(models)} modèles)")
            self.ollama_status.setStyleSheet("color: #00ff00; font-weight: bold;")
        else:
            self.ollama_status.setText("🦙 OLLAMA: ❌ Non disponible")
            self.ollama_status.setStyleSheet("color: #ff0000; font-weight: bold;")

    def check_deepseek_status(self):
        """Vérifie le statut de DeepSeek"""
        if self.deepseek.is_available():
            self.deepseek_status.setText("🧠 DEEPSEEK: ✅ Configuré")
            self.deepseek_status.setStyleSheet("color: #00ff00; font-weight: bold;")
        else:
            self.deepseek_status.setText("🧠 DEEPSEEK: ⚠️ Non configuré")
            self.deepseek_status.setStyleSheet("color: #ffaa00; font-weight: bold;")

    def auto_update_if_needed(self):
        """Mise à jour automatique si nécessaire"""
        # Mise à jour Ollama si non disponible
        if not self.ollama.is_available():
            self.manual_update_ollama()

        # Configuration DeepSeek si non configuré
        if not self.deepseek.is_available():
            self.configure_deepseek_ui()

    def manual_update_ollama(self):
        """Mise à jour manuelle d'Ollama"""
        self.ollama_progress.setVisible(True)
        self.ollama_progress.setValue(0)

        self.ollama_updater = IAUpdateWorker("ollama")
        self.ollama_updater.update_progress.connect(self.on_ollama_progress)
        self.ollama_updater.update_finished.connect(self.on_ollama_finished)
        self.ollama_updater.start()

    def manual_update_deepseek(self):
        """Mise à jour manuelle de DeepSeek"""
        self.deepseek_progress.setVisible(True)
        self.deepseek_progress.setValue(0)

        self.deepseek_updater = IAUpdateWorker("deepseek")
        self.deepseek_updater.update_progress.connect(self.on_deepseek_progress)
        self.deepseek_updater.update_finished.connect(self.on_deepseek_finished)
        self.deepseek_updater.start()

    def on_ollama_progress(self, ia_type, progress):
        """Mise à jour progression Ollama"""
        self.ollama_progress.setValue(progress)

    def on_ollama_finished(self, ia_type, success):
        """Fin mise à jour Ollama"""
        self.ollama_progress.setVisible(False)
        if success:
            QMessageBox.information(self, "Succès", "🦙 Ollama mis à jour avec succès!")
            self.check_ollama_status()
        else:
            QMessageBox.critical(self, "Erreur", "❌ Échec mise à jour Ollama")

    def on_deepseek_progress(self, ia_type, progress):
        """Mise à jour progression DeepSeek"""
        self.deepseek_progress.setValue(progress)

    def on_deepseek_finished(self, ia_type, success):
        """Fin mise à jour DeepSeek"""
        self.deepseek_progress.setVisible(False)
        if success:
            QMessageBox.information(self, "Succès", "🧠 DeepSeek mis à jour avec succès!")
            self.check_deepseek_status()
        else:
            QMessageBox.critical(self, "Erreur", "❌ Échec mise à jour DeepSeek")

    def configure_deepseek_ui(self):
        """Interface de configuration DeepSeek"""
        api_key, ok = QInputDialog.getText(
            self,
            "Configuration DeepSeek",
            "Entrez votre clé API DeepSeek:\n(Obtenez-la sur https://platform.deepseek.com)",
            QLineEdit.EchoMode.Password
        )

        if ok and api_key:
            self.deepseek.set_api_key(api_key)
            self.deepseek_key.setText(api_key)
            QMessageBox.information(self, "Succès", "🧠 DeepSeek configuré avec succès!")
            self.check_deepseek_status()

    def send_to_ollama(self):
        """Envoie une question à Ollama"""
        question = self.ollama_input.text().strip()
        if not question:
            return

        self.ollama_history.append(f"👤 VOUS: {question}")
        self.ollama_input.clear()

        # Génération réponse
        model = self.ollama_model.currentText()
        response = self.ollama.generate(question, model)

        self.ollama_history.append(f"🦙 OLLAMA ({model}): {response}")
        self.ollama_history.append("─" * 50)

    def send_to_deepseek(self):
        """Envoie une question à DeepSeek"""
        question = self.deepseek_input.text().strip()
        if not question:
            return

        # Sauvegarde clé API si modifiée
        if self.deepseek_key.text():
            self.deepseek.set_api_key(self.deepseek_key.text())

        self.deepseek_history.append(f"👤 VOUS: {question}")
        self.deepseek_input.clear()

        # Génération réponse
        model = self.deepseek_model.currentText()
        response = self.deepseek.generate(question, model)

        self.deepseek_history.append(f"🧠 DEEPSEEK ({model}): {response}")
        self.deepseek_history.append("─" * 50)

    def compare_ias(self):
        """Compare les réponses des deux IA"""
        question = self.comparison_input.text().strip()
        if not question:
            return

        # Réponse Ollama
        ollama_response = self.ollama.generate(question, self.ollama_model.currentText())
        self.ollama_comparison_result.setText(ollama_response)

        # Réponse DeepSeek
        deepseek_response = self.deepseek.generate(question, self.deepseek_model.currentText())
        self.deepseek_comparison_result.setText(deepseek_response)

        # Analyse comparative
        analysis = f"""
📊 ANALYSE COMPARATIVE - {datetime.now().strftime('%H:%M:%S')}
═══════════════════════════════════════════════════════════════

📝 QUESTION: {question}

🦙 OLLAMA ({self.ollama_model.currentText()}):
• Longueur: {len(ollama_response)} caractères
• Style: {"Technique" if "code" in ollama_response.lower() else "Général"}

🧠 DEEPSEEK ({self.deepseek_model.currentText()}):
• Longueur: {len(deepseek_response)} caractères
• Style: {"Technique" if "code" in deepseek_response.lower() else "Général"}

💡 RECOMMANDATION:
• Ollama: Meilleur pour les tâches générales et créatives
• DeepSeek: Excellent pour le code et les mathématiques
        """

        self.comparison_analysis.setText(analysis)


def main():
    """Lance l'interface IA Dual"""
    app = QApplication(sys.argv)

    # Configuration application
    app.setApplicationName("IA Dual - Ollama + DeepSeek")
    app.setApplicationVersion("1.0")

    # Lancement interface
    window = IADualInterface()
    window.show()

    print("🤖 IA Dual System lancé !")
    print("🦙 Ollama + 🧠 DeepSeek")
    print("🔄 Mises à jour automatiques activées")
    print("⚖️ Comparaison des IA disponible")

    return app.exec()


if __name__ == "__main__":
    main()
