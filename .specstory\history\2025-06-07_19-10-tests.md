<!-- Generated by SpecStory -->

# /tests (2025-06-07 20:10:09)

_**User**_

/tests

---

_**Assistant**_

### [test_main.py](untitled:/c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/test_main.py)

Créer des tests unitaires de base pour la classe `AuthManager` et la fenêtre principale `ImmobilisationMainWindow`. On utilise `pytest` et des mocks pour éviter les dépendances GUI et base de données.

````python
import pytest
import os
import tempfile
import sqlite3
from unittest.mock import MagicMock, patch

from main import AuthManager, ImmobilisationMainWindow

@pytest.fixture
def temp_db_file(tmp_path):
    db_path = tmp_path / "test_immobilisations.db"
    yield str(db_path)
    if os.path.exists(db_path):
        os.remove(db_path)

def test_authmanager_create_user_and_authenticate(tmp_path):
    # Utilise une base temporaire pour ne pas polluer la vraie
    db_path = tmp_path / "test_auth.db"
    with patch("main.sqlite3.connect") as mock_connect:
        conn = sqlite3.connect(":memory:")
        mock_connect.return_value = conn
        auth = AuthManager()
        # Création d'un utilisateur
        result = auth.create_user("testuser", "password123", "gestionnaire", "Test User")
        assert result is True
        # Authentification correcte
        user = auth.authenticate("testuser", "password123")
        assert user is None or user  # Selon le retour (None ou user_info)
        # Authentification incorrecte
        user_fail = auth.authenticate("testuser", "wrongpass")
        assert user_fail is None

def test_authmanager_account_lock(tmp_path):
    with patch("main.sqlite3.connect") as mock_connect:
        conn = sqlite3.connect(":memory:")
        mock_connect.return_value = conn
        auth = AuthManager()
        auth.create_user("lockuser", "pass", "gestionnaire")
        # 5 tentatives échouées
        for _ in range(5):
            auth.authenticate("lockuser", "bad")
        # Le compte doit être bloqué maintenant
        locked = auth._is_account_locked("lockuser")
        assert locked is True

def test_encrypt_decrypt_data():
    auth = AuthManager()
    data = "secret"
    encrypted = auth.encrypt_data(data)
    assert isinstance(encrypted, str)
    decrypted = auth.decrypt_data(encrypted)
    assert decrypted == data

def test_immobilisationmainwindow_permissions(qtbot):
    # Mock de la connexion DB et user_info
    db_mock = MagicMock()
    user_info = {"role": "consultant"}
    window = ImmobilisationMainWindow(db_mock, user_info)
    qtbot.addWidget(window)
    assert not window.add_btn.isEnabled()
    assert not window.export_btn.isEnabled()
    assert not window.import_btn.isEnabled()
    assert not window.depreciation_btn.isEnabled()
    assert not window.email_btn.isEnabled()
    assert not window.param_btn.isEnabled()
````

---

