<!-- Generated by SpecStory -->

# /fix L’importation « Figure » n’a pas été accédée (2025-06-08 11:45:58)

_**User**_

/fix L’importation « Figure » n’a pas été accédée

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimer l'import inutile de `Figure` de matplotlib, car il n'est pas utilisé dans la fonction.

````python
        try:
            import matplotlib.pyplot as plt
            # from matplotlib.figure import Figure  # Suppression : import non utilisé
            from collections import defaultdict
            from typing import DefaultDict
            # ...existing code...
````

---

