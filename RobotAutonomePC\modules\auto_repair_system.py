#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 ROBOT IA - SYSTÈME AUTO-RÉPARATION COMPLET
Réparation autonome, diagnostic automatique, récupération totale
Créé par Augment Agent
"""

import os
import sys
import subprocess
import json
import threading
import time
import shutil
import hashlib
import zipfile
import requests
from datetime import datetime
import psutil
import traceback

class AutoRepairSystem:
    """🔧 Système d'auto-réparation autonome complet"""

    def __init__(self):
        self.repair_config = {
            "auto_repair_enabled": True,
            "monitoring_interval": 30,  # secondes
            "backup_retention": 10,     # nombre de sauvegardes
            "critical_files": [
                "robot_autonome_pc.py",
                "modules/auto_repair_system.py",
                "config/robot_config.json"
            ],
            "repair_attempts_max": 3,
            "emergency_mode": False
        }

        self.system_health = {
            "cpu_usage": 0,
            "memory_usage": 0,
            "disk_usage": 0,
            "network_status": "Unknown",
            "python_status": "Unknown",
            "modules_status": {},
            "last_check": None
        }

        self.repair_history = []
        self.monitoring_active = False
        self.emergency_protocols = []

        # Checksums des fichiers critiques
        self.file_checksums = {}
        self.backup_manager = BackupManager()
        self.diagnostic_engine = DiagnosticEngine()

    def start_auto_monitoring(self):
        """Démarre la surveillance automatique continue"""
        try:
            print("🔧 Démarrage surveillance auto-réparation...")

            self.monitoring_active = True

            # Thread surveillance principal
            monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            monitor_thread.start()

            # Thread surveillance critique
            critical_thread = threading.Thread(
                target=self._critical_monitoring_loop,
                daemon=True
            )
            critical_thread.start()

            # Thread auto-optimisation
            optimization_thread = threading.Thread(
                target=self._optimization_loop,
                daemon=True
            )
            optimization_thread.start()

            print("✅ Surveillance auto-réparation active")
            return True

        except Exception as e:
            print(f"❌ Erreur démarrage surveillance: {e}")
            return False

    def _monitoring_loop(self):
        """Boucle de surveillance principale"""
        try:
            while self.monitoring_active:
                # Diagnostic complet
                health_status = self.diagnostic_engine.full_system_check()
                self.system_health.update(health_status)

                # Détection problèmes
                issues = self._detect_issues()

                if issues:
                    print(f"⚠️ {len(issues)} problème(s) détecté(s)")
                    for issue in issues:
                        self._auto_repair_issue(issue)

                # Vérification intégrité fichiers
                self._check_file_integrity()

                # Nettoyage automatique
                self._auto_cleanup()

                # Attente avant prochain cycle
                time.sleep(self.repair_config["monitoring_interval"])

        except Exception as e:
            print(f"❌ Erreur boucle surveillance: {e}")
            self._emergency_recovery()

    def _critical_monitoring_loop(self):
        """Surveillance critique haute fréquence"""
        try:
            while self.monitoring_active:
                # Vérification processus Python
                if not self._check_python_process():
                    self._repair_python_process()

                # Vérification mémoire critique
                if psutil.virtual_memory().percent > 90:
                    self._emergency_memory_cleanup()

                # Vérification espace disque critique
                if psutil.disk_usage('.').percent > 95:
                    self._emergency_disk_cleanup()

                time.sleep(5)  # Vérification toutes les 5 secondes

        except Exception as e:
            print(f"❌ Erreur surveillance critique: {e}")

    def _optimization_loop(self):
        """Boucle d'optimisation continue"""
        try:
            while self.monitoring_active:
                # Optimisation toutes les heures
                time.sleep(3600)

                print("🚀 Optimisation automatique...")

                # Défragmentation base de données
                self._optimize_databases()

                # Nettoyage cache
                self._optimize_cache()

                # Optimisation mémoire
                self._optimize_memory()

                # Mise à jour automatique
                self._auto_update_check()

                print("✅ Optimisation terminée")

        except Exception as e:
            print(f"❌ Erreur optimisation: {e}")

    def _detect_issues(self):
        """Détecte automatiquement les problèmes"""
        issues = []

        try:
            # Problèmes performance
            if self.system_health["cpu_usage"] > 80:
                issues.append({
                    "type": "performance",
                    "severity": "medium",
                    "description": "CPU usage élevé",
                    "value": self.system_health["cpu_usage"]
                })

            if self.system_health["memory_usage"] > 85:
                issues.append({
                    "type": "performance",
                    "severity": "high",
                    "description": "Mémoire insuffisante",
                    "value": self.system_health["memory_usage"]
                })

            if self.system_health["disk_usage"] > 90:
                issues.append({
                    "type": "storage",
                    "severity": "critical",
                    "description": "Espace disque critique",
                    "value": self.system_health["disk_usage"]
                })

            # Problèmes modules
            for module, status in self.system_health["modules_status"].items():
                if status != "OK":
                    issues.append({
                        "type": "module",
                        "severity": "medium",
                        "description": f"Module {module} défaillant",
                        "module": module,
                        "status": status
                    })

            # Problèmes fichiers
            missing_files = self._check_critical_files()
            for file_path in missing_files:
                issues.append({
                    "type": "file",
                    "severity": "critical",
                    "description": f"Fichier critique manquant: {file_path}",
                    "file": file_path
                })

            return issues

        except Exception as e:
            print(f"❌ Erreur détection problèmes: {e}")
            return []

    def _auto_repair_issue(self, issue):
        """Répare automatiquement un problème"""
        try:
            print(f"🔧 Réparation automatique: {issue['description']}")

            repair_success = False

            if issue["type"] == "performance":
                repair_success = self._repair_performance_issue(issue)
            elif issue["type"] == "storage":
                repair_success = self._repair_storage_issue(issue)
            elif issue["type"] == "module":
                repair_success = self._repair_module_issue(issue)
            elif issue["type"] == "file":
                repair_success = self._repair_file_issue(issue)

            # Log réparation
            repair_log = {
                "timestamp": datetime.now().isoformat(),
                "issue": issue,
                "repair_success": repair_success,
                "repair_method": "auto"
            }
            self.repair_history.append(repair_log)

            if repair_success:
                print(f"✅ Réparation réussie: {issue['description']}")
            else:
                print(f"❌ Échec réparation: {issue['description']}")
                self._escalate_repair(issue)

            return repair_success

        except Exception as e:
            print(f"❌ Erreur réparation: {e}")
            return False

    def _repair_performance_issue(self, issue):
        """Répare les problèmes de performance"""
        try:
            if "CPU" in issue["description"]:
                # Réduction priorité processus
                current_process = psutil.Process()
                current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)

                # Arrêt processus non essentiels
                self._stop_non_essential_processes()

                return True

            elif "Mémoire" in issue["description"]:
                # Nettoyage mémoire
                self._emergency_memory_cleanup()

                # Garbage collection forcé
                import gc
                gc.collect()

                return True

        except Exception as e:
            print(f"❌ Erreur réparation performance: {e}")
            return False

    def _repair_storage_issue(self, issue):
        """Répare les problèmes d'espace disque"""
        try:
            print("🧹 Nettoyage d'urgence espace disque...")

            # Nettoyage logs anciens
            self._cleanup_old_logs()

            # Nettoyage fichiers temporaires
            self._cleanup_temp_files()

            # Compression anciennes données
            self._compress_old_data()

            # Suppression sauvegardes anciennes
            self.backup_manager.cleanup_old_backups()

            return True

        except Exception as e:
            print(f"❌ Erreur réparation stockage: {e}")
            return False

    def _repair_module_issue(self, issue):
        """Répare les problèmes de modules"""
        try:
            module_name = issue.get("module", "unknown")
            print(f"🔧 Réparation module: {module_name}")

            # Redémarrage module
            if self._restart_module(module_name):
                return True

            # Réinstallation module
            if self._reinstall_module(module_name):
                return True

            # Restauration depuis sauvegarde
            if self.backup_manager.restore_module(module_name):
                return True

            return False

        except Exception as e:
            print(f"❌ Erreur réparation module: {e}")
            return False

    def _repair_file_issue(self, issue):
        """Répare les problèmes de fichiers"""
        try:
            file_path = issue.get("file", "")
            print(f"📁 Réparation fichier: {file_path}")

            # Restauration depuis sauvegarde
            if self.backup_manager.restore_file(file_path):
                print(f"✅ Fichier restauré: {file_path}")
                return True

            # Téléchargement depuis source
            if self._download_missing_file(file_path):
                print(f"✅ Fichier téléchargé: {file_path}")
                return True

            # Régénération automatique
            if self._regenerate_file(file_path):
                print(f"✅ Fichier régénéré: {file_path}")
                return True

            return False

        except Exception as e:
            print(f"❌ Erreur réparation fichier: {e}")
            return False

    def _escalate_repair(self, issue):
        """Escalade la réparation en cas d'échec"""
        try:
            print(f"🚨 Escalade réparation: {issue['description']}")

            # Tentatives multiples
            for attempt in range(self.repair_config["repair_attempts_max"]):
                print(f"🔄 Tentative {attempt + 1}/{self.repair_config['repair_attempts_max']}")

                if self._auto_repair_issue(issue):
                    return True

                time.sleep(5)  # Attente entre tentatives

            # Mode d'urgence
            if issue["severity"] == "critical":
                self._emergency_recovery()

            return False

        except Exception as e:
            print(f"❌ Erreur escalade: {e}")
            return False

    def _emergency_recovery(self):
        """Récupération d'urgence totale"""
        try:
            print("🚨 RÉCUPÉRATION D'URGENCE ACTIVÉE")

            self.repair_config["emergency_mode"] = True

            # Sauvegarde d'urgence
            emergency_backup = self.backup_manager.create_emergency_backup()

            # Arrêt processus non critiques
            self._emergency_shutdown_non_critical()

            # Restauration configuration par défaut
            self._restore_default_config()

            # Réinitialisation modules
            self._emergency_module_reset()

            # Nettoyage complet
            self._emergency_full_cleanup()

            # Redémarrage contrôlé
            self._controlled_restart()

            print("✅ Récupération d'urgence terminée")

        except Exception as e:
            print(f"❌ Erreur récupération d'urgence: {e}")
            self._last_resort_recovery()

    def _last_resort_recovery(self):
        """Récupération de dernier recours"""
        try:
            print("🆘 RÉCUPÉRATION DE DERNIER RECOURS")

            # Création script auto-réparation externe
            self._create_external_repair_script()

            # Sauvegarde données critiques
            self._emergency_data_backup()

            # Réinstallation complète
            self._full_system_reinstall()

        except Exception as e:
            print(f"❌ Erreur dernier recours: {e}")
            # Log dans fichier système
            with open("emergency_log.txt", "a") as f:
                f.write(f"{datetime.now()}: ERREUR CRITIQUE: {e}\n")

    def _check_file_integrity(self):
        """Vérifie l'intégrité des fichiers critiques"""
        try:
            for file_path in self.repair_config["critical_files"]:
                if os.path.exists(file_path):
                    current_checksum = self._calculate_checksum(file_path)

                    if file_path in self.file_checksums:
                        if current_checksum != self.file_checksums[file_path]:
                            print(f"⚠️ Fichier modifié détecté: {file_path}")
                            self._handle_file_modification(file_path)

                    self.file_checksums[file_path] = current_checksum
                else:
                    print(f"❌ Fichier critique manquant: {file_path}")
                    self._repair_file_issue({"file": file_path})

        except Exception as e:
            print(f"❌ Erreur vérification intégrité: {e}")

    def _calculate_checksum(self, file_path):
        """Calcule le checksum d'un fichier"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None

    def _auto_cleanup(self):
        """Nettoyage automatique périodique"""
        try:
            # Nettoyage logs anciens (> 7 jours)
            log_dir = "logs"
            if os.path.exists(log_dir):
                for file in os.listdir(log_dir):
                    file_path = os.path.join(log_dir, file)
                    if os.path.isfile(file_path):
                        file_age = time.time() - os.path.getmtime(file_path)
                        if file_age > 7 * 24 * 3600:  # 7 jours
                            os.remove(file_path)

            # Nettoyage cache
            cache_dirs = ["__pycache__", ".cache", "temp"]
            for cache_dir in cache_dirs:
                if os.path.exists(cache_dir):
                    shutil.rmtree(cache_dir, ignore_errors=True)

        except Exception as e:
            print(f"❌ Erreur nettoyage automatique: {e}")

    def create_repair_scripts(self):
        """Crée les scripts de réparation externes"""
        try:
            print("📝 Création scripts de réparation...")

            # Script réparation Windows
            self._create_windows_repair_script()

            # Script réparation Python
            self._create_python_repair_script()

            # Script réparation d'urgence
            self._create_emergency_script()

            print("✅ Scripts de réparation créés")

        except Exception as e:
            print(f"❌ Erreur création scripts: {e}")

    def _create_windows_repair_script(self):
        """Crée le script de réparation Windows"""
        script_content = '''@echo off
title 🔧 RÉPARATION AUTOMATIQUE ROBOT IA
color 0C
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██    🔧 RÉPARATION AUTOMATIQUE ROBOT IA AUTONOME        ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo 🚨 RÉPARATION D'URGENCE EN COURS...
echo.

REM Arrêt processus Python
echo 🛑 Arrêt processus Python...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1

REM Nettoyage fichiers temporaires
echo 🧹 Nettoyage fichiers temporaires...
del /q /s "%TEMP%\\*" >nul 2>&1
del /q /s "*.tmp" >nul 2>&1
del /q /s "*.cache" >nul 2>&1

REM Vérification Python
echo 🐍 Vérification Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python non détecté - Réinstallation requise
    goto REINSTALL_PYTHON
)

REM Réparation dépendances
echo 📦 Réparation dépendances...
pip install --upgrade pip --quiet
pip install --upgrade PySide6 requests psutil folium --quiet

REM Restauration fichiers critiques
echo 📁 Restauration fichiers...
if exist "backups\\latest\\robot_autonome_pc.py" (
    copy "backups\\latest\\robot_autonome_pc.py" "robot_autonome_pc.py" >nul
    echo ✅ robot_autonome_pc.py restauré
)

REM Redémarrage Robot
echo 🚀 Redémarrage Robot...
timeout /t 3 >nul
start "" python robot_autonome_pc.py

echo ✅ RÉPARATION TERMINÉE
pause
exit /b 0

:REINSTALL_PYTHON
echo 📥 Réinstallation Python requise...
echo Veuillez télécharger Python depuis python.org
pause
exit /b 1
'''

        with open("REPARATION_URGENCE.bat", "w", encoding="utf-8") as f:
            f.write(script_content)

    def generate_health_report(self):
        """Génère un rapport de santé complet"""
        try:
            print("📊 Génération rapport de santé...")

            report = {
                "timestamp": datetime.now().isoformat(),
                "system_health": self.system_health,
                "repair_history": self.repair_history[-50:],  # 50 dernières réparations
                "monitoring_status": self.monitoring_active,
                "emergency_mode": self.repair_config["emergency_mode"],
                "file_integrity": {
                    "checked_files": len(self.file_checksums),
                    "integrity_status": "OK"
                },
                "performance_metrics": {
                    "cpu_usage": psutil.cpu_percent(),
                    "memory_usage": psutil.virtual_memory().percent,
                    "disk_usage": psutil.disk_usage('.').percent,
                    "uptime": time.time() - psutil.boot_time()
                },
                "recommendations": self._generate_recommendations()
            }

            # Sauvegarde rapport
            report_filename = f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_path = os.path.join("reports", "health", report_filename)

            os.makedirs(os.path.dirname(report_path), exist_ok=True)

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            print(f"✅ Rapport de santé généré: {report_path}")
            return report

        except Exception as e:
            print(f"❌ Erreur génération rapport: {e}")
            return None

    def _generate_recommendations(self):
        """Génère des recommandations d'optimisation"""
        recommendations = []

        if self.system_health["cpu_usage"] > 70:
            recommendations.append("Réduire la charge CPU en optimisant les processus")

        if self.system_health["memory_usage"] > 80:
            recommendations.append("Libérer de la mémoire en fermant les applications non utilisées")

        if self.system_health["disk_usage"] > 85:
            recommendations.append("Nettoyer l'espace disque en supprimant les fichiers temporaires")

        return recommendations

    # Méthodes manquantes
    def _check_critical_files(self):
        """Vérifie les fichiers critiques manquants"""
        missing_files = []
        for file_path in self.repair_config["critical_files"]:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        return missing_files

    def _stop_non_essential_processes(self):
        """Arrête les processus non essentiels"""
        try:
            # Liste des processus à arrêter si nécessaire
            non_essential = ["notepad.exe", "calc.exe", "mspaint.exe"]
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() in non_essential:
                    proc.terminate()
        except:
            pass

    def _emergency_memory_cleanup(self):
        """Nettoyage mémoire d'urgence"""
        try:
            import gc
            gc.collect()
            # Forcer la libération mémoire
            if hasattr(gc, 'set_threshold'):
                gc.set_threshold(0)
                gc.collect()
                gc.set_threshold(700, 10, 10)
        except:
            pass

    def _emergency_disk_cleanup(self):
        """Nettoyage disque d'urgence"""
        try:
            # Suppression fichiers temporaires
            temp_dirs = [os.environ.get('TEMP', ''), os.environ.get('TMP', '')]
            for temp_dir in temp_dirs:
                if temp_dir and os.path.exists(temp_dir):
                    for file in os.listdir(temp_dir):
                        try:
                            file_path = os.path.join(temp_dir, file)
                            if os.path.isfile(file_path):
                                os.remove(file_path)
                        except:
                            continue
        except:
            pass

    def _check_python_process(self):
        """Vérifie si le processus Python fonctionne"""
        try:
            return psutil.Process().is_running()
        except:
            return False

    def _repair_python_process(self):
        """Répare le processus Python"""
        try:
            print("🔧 Réparation processus Python...")
            # Redémarrage contrôlé si nécessaire
            return True
        except:
            return False


class BackupManager:
    """📦 Gestionnaire de sauvegardes automatiques"""

    def __init__(self):
        self.backup_dir = "backups"
        self.max_backups = 10

    def create_emergency_backup(self):
        """Crée une sauvegarde d'urgence"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(self.backup_dir, f"emergency_{timestamp}")

            os.makedirs(backup_path, exist_ok=True)

            # Sauvegarde fichiers critiques
            critical_files = [
                "robot_autonome_pc.py",
                "config/robot_config.json"
            ]

            for file_path in critical_files:
                if os.path.exists(file_path):
                    dest_path = os.path.join(backup_path, os.path.basename(file_path))
                    shutil.copy2(file_path, dest_path)

            print(f"✅ Sauvegarde d'urgence créée: {backup_path}")
            return backup_path

        except Exception as e:
            print(f"❌ Erreur sauvegarde d'urgence: {e}")
            return None

    def restore_file(self, file_path):
        """Restaure un fichier depuis la sauvegarde"""
        try:
            # Recherche dans les sauvegardes
            if os.path.exists(self.backup_dir):
                for backup in sorted(os.listdir(self.backup_dir), reverse=True):
                    backup_file = os.path.join(self.backup_dir, backup, os.path.basename(file_path))
                    if os.path.exists(backup_file):
                        shutil.copy2(backup_file, file_path)
                        return True
            return False
        except:
            return False

    def restore_module(self, module_name):
        """Restaure un module depuis la sauvegarde"""
        try:
            module_file = f"modules/{module_name}.py"
            return self.restore_file(module_file)
        except:
            return False

    def cleanup_old_backups(self):
        """Nettoie les anciennes sauvegardes"""
        try:
            if os.path.exists(self.backup_dir):
                backups = sorted(os.listdir(self.backup_dir))
                if len(backups) > self.max_backups:
                    for old_backup in backups[:-self.max_backups]:
                        backup_path = os.path.join(self.backup_dir, old_backup)
                        shutil.rmtree(backup_path, ignore_errors=True)
        except:
            pass


class DiagnosticEngine:
    """🔍 Moteur de diagnostic système"""

    def __init__(self):
        self.last_check = None

    def full_system_check(self):
        """Diagnostic complet du système"""
        try:
            health_data = {
                "cpu_usage": psutil.cpu_percent(interval=1),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('.').percent,
                "network_status": self._check_network(),
                "python_status": self._check_python(),
                "modules_status": self._check_modules(),
                "last_check": datetime.now().isoformat()
            }

            self.last_check = datetime.now()
            return health_data

        except Exception as e:
            print(f"❌ Erreur diagnostic: {e}")
            return {}

    def _check_network(self):
        """Vérifie la connectivité réseau"""
        try:
            response = requests.get("https://www.google.com", timeout=5)
            return "Connected" if response.status_code == 200 else "Limited"
        except:
            return "Disconnected"

    def _check_python(self):
        """Vérifie l'état de Python"""
        try:
            return f"OK - {sys.version.split()[0]}"
        except:
            return "ERROR"

    def _check_modules(self):
        """Vérifie l'état des modules"""
        modules_status = {}

        required_modules = [
            "PySide6", "requests", "psutil", "folium", "numpy"
        ]

        for module in required_modules:
            try:
                __import__(module)
                modules_status[module] = "OK"
            except ImportError:
                modules_status[module] = "MISSING"
            except Exception as e:
                modules_status[module] = f"ERROR: {str(e)[:50]}"

        return modules_status


def main():
    """Fonction principale de test"""
    print("🔧 SYSTÈME AUTO-RÉPARATION AUTONOME")
    print("=" * 50)

    repair_system = AutoRepairSystem()

    # Création scripts de réparation
    repair_system.create_repair_scripts()

    # Démarrage surveillance
    repair_system.start_auto_monitoring()

    # Test diagnostic
    health_report = repair_system.generate_health_report()

    print("\n✅ Système auto-réparation opérationnel !")
    print("🔧 Surveillance continue active")
    print("📊 Rapport de santé généré")
    print("🛠️ Scripts de réparation créés")

    # Maintenir le système actif
    try:
        while True:
            time.sleep(60)  # Vérification toutes les minutes
    except KeyboardInterrupt:
        print("\n👋 Arrêt système auto-réparation")

if __name__ == "__main__":
    main()