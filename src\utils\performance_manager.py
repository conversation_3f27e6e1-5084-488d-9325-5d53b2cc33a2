"""
Gestionnaire de performance pour GestImmob
Optimise les performances de l'interface et de la base de données
"""

import time
import threading
import psutil
import gc
from typing import Dict, Any, Callable, Optional
from functools import wraps, lru_cache
from PySide6.QtCore import QObject, Signal, QTimer, QThread, QMutex
from PySide6.QtWidgets import QApplication
import logging

logger = logging.getLogger(__name__)

class PerformanceMonitor(QObject):
    """Moniteur de performance en temps réel"""
    
    performance_updated = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_metrics)
        self.metrics = {}
        self.monitoring = False
        
    def start_monitoring(self, interval_ms: int = 5000):
        """Démarre le monitoring de performance"""
        self.timer.start(interval_ms)
        self.monitoring = True
        logger.info("Monitoring de performance démarré")
        
    def stop_monitoring(self):
        """Arrête le monitoring de performance"""
        self.timer.stop()
        self.monitoring = False
        logger.info("Monitoring de performance arrêté")
        
    def _update_metrics(self):
        """Met à jour les métriques de performance"""
        try:
            # Métriques système
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Métriques application
            process = psutil.Process()
            app_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            self.metrics = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / 1024 / 1024 / 1024,
                'app_memory_mb': app_memory,
                'threads_count': threading.active_count(),
                'timestamp': time.time()
            }
            
            self.performance_updated.emit(self.metrics)
            
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour des métriques: {e}")

class CacheManager:
    """Gestionnaire de cache pour optimiser les performances"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = {}
        self.access_times = {}
        self.mutex = QMutex()
        
    def get(self, key: str) -> Any:
        """Récupère une valeur du cache"""
        with self.mutex:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
            
    def set(self, key: str, value: Any):
        """Stocke une valeur dans le cache"""
        with self.mutex:
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
            
    def _evict_oldest(self):
        """Supprime l'élément le plus ancien du cache"""
        if not self.access_times:
            return
            
        oldest_key = min(self.access_times.keys(), 
                        key=lambda k: self.access_times[k])
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
        
    def clear(self):
        """Vide le cache"""
        with self.mutex:
            self.cache.clear()
            self.access_times.clear()
            
    def size(self) -> int:
        """Retourne la taille actuelle du cache"""
        return len(self.cache)

class DatabaseOptimizer:
    """Optimiseur de base de données"""
    
    def __init__(self, connection):
        self.connection = connection
        self.query_cache = CacheManager(max_size=500)
        
    def optimize_connection(self):
        """Optimise la connexion à la base de données"""
        try:
            cursor = self.connection.cursor()
            
            # Configuration SQLite pour la performance
            optimizations = [
                "PRAGMA journal_mode = WAL",
                "PRAGMA synchronous = NORMAL", 
                "PRAGMA cache_size = -64000",  # 64MB cache
                "PRAGMA temp_store = MEMORY",
                "PRAGMA mmap_size = 268435456",  # 256MB mmap
                "PRAGMA optimize"
            ]
            
            for pragma in optimizations:
                cursor.execute(pragma)
                
            self.connection.commit()
            logger.info("Base de données optimisée")
            
        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation de la DB: {e}")
            
    def execute_cached_query(self, query: str, params: tuple = ()) -> Any:
        """Exécute une requête avec mise en cache"""
        cache_key = f"{query}:{str(params)}"
        
        # Vérifier le cache
        cached_result = self.query_cache.get(cache_key)
        if cached_result is not None:
            return cached_result
            
        # Exécuter la requête
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            result = cursor.fetchall()
            
            # Mettre en cache le résultat
            self.query_cache.set(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur lors de l'exécution de la requête: {e}")
            return None

class UIOptimizer:
    """Optimiseur d'interface utilisateur"""
    
    def __init__(self):
        self.update_timers = {}
        
    def debounce_updates(self, widget, delay_ms: int = 300):
        """Limite la fréquence de mise à jour d'un widget"""
        widget_id = id(widget)
        
        if widget_id in self.update_timers:
            self.update_timers[widget_id].stop()
            
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(lambda: self._perform_update(widget))
        timer.start(delay_ms)
        
        self.update_timers[widget_id] = timer
        
    def _perform_update(self, widget):
        """Effectue la mise à jour du widget"""
        try:
            if hasattr(widget, 'update'):
                widget.update()
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour du widget: {e}")
            
    def optimize_table_performance(self, table_widget):
        """Optimise les performances d'un QTableWidget"""
        try:
            # Désactiver les mises à jour pendant le chargement
            table_widget.setUpdatesEnabled(False)
            
            # Optimisations de performance
            table_widget.setSortingEnabled(False)
            table_widget.setAlternatingRowColors(True)
            
            # Réactiver les mises à jour
            table_widget.setUpdatesEnabled(True)
            table_widget.setSortingEnabled(True)
            
        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation du tableau: {e}")

def performance_timer(func: Callable) -> Callable:
    """Décorateur pour mesurer le temps d'exécution"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logger.debug(f"{func.__name__} exécuté en {execution_time:.4f}s")
        
        return result
    return wrapper

def memory_efficient(func: Callable) -> Callable:
    """Décorateur pour optimiser l'utilisation mémoire"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Forcer le garbage collection avant l'exécution
        gc.collect()
        
        result = func(*args, **kwargs)
        
        # Forcer le garbage collection après l'exécution
        gc.collect()
        
        return result
    return wrapper

class BackgroundWorker(QThread):
    """Worker pour les tâches en arrière-plan"""
    
    task_completed = Signal(object)
    task_error = Signal(str)
    
    def __init__(self, task_func: Callable, *args, **kwargs):
        super().__init__()
        self.task_func = task_func
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        """Exécute la tâche en arrière-plan"""
        try:
            result = self.task_func(*self.args, **self.kwargs)
            self.task_completed.emit(result)
        except Exception as e:
            self.task_error.emit(str(e))
            logger.error(f"Erreur dans le worker: {e}")

class PerformanceManager:
    """Gestionnaire principal de performance"""
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.cache_manager = CacheManager()
        self.ui_optimizer = UIOptimizer()
        self.db_optimizer = None
        
    def initialize(self, db_connection=None):
        """Initialise le gestionnaire de performance"""
        if db_connection:
            self.db_optimizer = DatabaseOptimizer(db_connection)
            self.db_optimizer.optimize_connection()
            
        # Démarrer le monitoring
        self.monitor.start_monitoring()
        
        logger.info("Gestionnaire de performance initialisé")
        
    def optimize_application(self):
        """Optimise l'application globalement"""
        try:
            # Optimisations générales
            app = QApplication.instance()
            if app:
                app.setAttribute(app.AA_DontCreateNativeWidgetSiblings, True)
                app.setAttribute(app.AA_NativeWindows, False)
                
            # Configuration du garbage collector
            gc.set_threshold(700, 10, 10)
            
            # Forcer une collecte initiale
            gc.collect()
            
            logger.info("Application optimisée")
            
        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation: {e}")
            
    def get_performance_report(self) -> Dict[str, Any]:
        """Génère un rapport de performance"""
        return {
            'metrics': self.monitor.metrics,
            'cache_size': self.cache_manager.size(),
            'memory_usage': psutil.virtual_memory().percent,
            'cpu_usage': psutil.cpu_percent(),
            'threads': threading.active_count()
        }
        
    def cleanup(self):
        """Nettoie les ressources"""
        self.monitor.stop_monitoring()
        self.cache_manager.clear()
        gc.collect()
        logger.info("Nettoyage des ressources terminé")

# Instance globale
performance_manager = PerformanceManager()
