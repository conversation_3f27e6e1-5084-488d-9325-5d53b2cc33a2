<!-- Generated by SpecStory -->

# /fix Le type de « valeur » est partiellement incon (2025-06-08 11:09:22)

_**User**_

/fix Le type de « valeur » est partiellement inconnu
  Le type de "valeur" est "Column[Unknown]"

---

_**Assistant**_

### [models.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/database/models.py)

Pour corriger l'erreur de typage sur la colonne `valeur`, il faut utiliser l'annotation de type `Mapped[float]` et `mapped_column` pour que SQLAlchemy puisse correctement inférer le type Python correspondant à la colonne DECIMAL.

Voici la modification à apporter dans la classe `Immobilisation` :

````python
# ...existing code...
class Immobilisation(Base):
    __tablename__ = 'immobilisations'
    id = Column(Integer, primary_key=True, autoincrement=True)
    designation = Column(String(255), nullable=False)
    valeur: Mapped[float] = mapped_column(DECIMAL(18,2), nullable=False)
    annee = Column(Integer)
    localisation = Column(String(255))
    secteur = Column(String(255))
    observation = Column(Text)
    fournisseur_id = Column(Integer, ForeignKey('fournisseurs.id'))
    date_achat = Column(DateTime)
    duree_vie = Column(Integer)
    statut = Column(String(50))
    code_barres = Column(String(100))
    langue = Column(String(10), default="fr")
    fournisseur = relationship("Fournisseur")
# ...existing code...
````

---

