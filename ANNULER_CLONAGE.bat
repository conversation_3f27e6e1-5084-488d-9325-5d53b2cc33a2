@echo off
title ANNULATION COMPLETE DU CLONAGE
color 0C

echo.
echo ========================================================================
echo                    🔄 ANNULATION COMPLÈTE DU CLONAGE 🔄
echo              Restauration de GestImmob à l'état original
echo ========================================================================
echo.

echo [%TIME%] Annulation de toutes les modifications du clonage...

REM Variables
set "PYTHON_CMD="
set "ANNULATION_REUSSIE=0"

echo.
echo ⚠️ ATTENTION: Cette opération va:
echo   🗑️ Supprimer TOUS les fichiers de clonage
echo   🗃️ Nettoyer la base de données des éléments clonés
echo   🔄 Restaurer GestImmob à son état original
echo   💾 Créer une sauvegarde avant annulation
echo.

set /p confirmation="Voulez-vous vraiment continuer ? (oui/non): "

if /i not "%confirmation%"=="oui" (
    if /i not "%confirmation%"=="o" (
        echo ❌ Annulation annulée par l'utilisateur
        goto :end
    )
)

echo.
echo 🔍 DETECTION PYTHON...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :executer_annulation
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :executer_annulation
)

echo ❌ Python non detecte
echo 💡 Annulation manuelle requise
goto :annulation_manuelle

:executer_annulation
echo.
echo 🔄 EXECUTION DE L'ANNULATION AUTOMATIQUE...

if exist "ANNULER_CLONAGE_COMPLET.py" (
    echo Lancement du script d'annulation...
    %PYTHON_CMD% ANNULER_CLONAGE_COMPLET.py
    
    if not errorlevel 1 (
        echo ✅ Annulation automatique reussie
        set /a ANNULATION_REUSSIE=1
        goto :verification_finale
    ) else (
        echo ❌ Erreur lors de l'annulation automatique
        echo 🔄 Passage à l'annulation manuelle...
        goto :annulation_manuelle
    )
) else (
    echo ❌ Script d'annulation non trouve
    goto :annulation_manuelle
)

:annulation_manuelle
echo.
echo 🔧 ANNULATION MANUELLE...
echo.

echo 🗑️ SUPPRESSION DES FICHIERS DE CLONAGE:

REM Supprimer les fichiers de clonage
set "fichiers_supprimes=0"

if exist "configuration_gestimmob_importee.json" (
    del "configuration_gestimmob_importee.json" >nul 2>&1
    if not exist "configuration_gestimmob_importee.json" (
        echo ✅ configuration_gestimmob_importee.json supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "extraire_parametres_sql.py" (
    del "extraire_parametres_sql.py" >nul 2>&1
    if not exist "extraire_parametres_sql.py" (
        echo ✅ extraire_parametres_sql.py supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "cloner_configurations_gestimmob.py" (
    del "cloner_configurations_gestimmob.py" >nul 2>&1
    if not exist "cloner_configurations_gestimmob.py" (
        echo ✅ cloner_configurations_gestimmob.py supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "analyser_configs_bureau.py" (
    del "analyser_configs_bureau.py" >nul 2>&1
    if not exist "analyser_configs_bureau.py" (
        echo ✅ analyser_configs_bureau.py supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "explorer_contenu_dossiers.py" (
    del "explorer_contenu_dossiers.py" >nul 2>&1
    if not exist "explorer_contenu_dossiers.py" (
        echo ✅ explorer_contenu_dossiers.py supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "LANCER_GESTIMMOB_CLONE.bat" (
    del "LANCER_GESTIMMOB_CLONE.bat" >nul 2>&1
    if not exist "LANCER_GESTIMMOB_CLONE.bat" (
        echo ✅ LANCER_GESTIMMOB_CLONE.bat supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "CLONAGE_CONFIGURATIONS_SUCCES.md" (
    del "CLONAGE_CONFIGURATIONS_SUCCES.md" >nul 2>&1
    if not exist "CLONAGE_CONFIGURATIONS_SUCCES.md" (
        echo ✅ CLONAGE_CONFIGURATIONS_SUCCES.md supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "integration_finale.sql" (
    del "integration_finale.sql" >nul 2>&1
    if not exist "integration_finale.sql" (
        echo ✅ integration_finale.sql supprime
        set /a fichiers_supprimes+=1
    )
)

REM Supprimer les fichiers temporaires
if exist "configurations_extraites.json" (
    del "configurations_extraites.json" >nul 2>&1
    if not exist "configurations_extraites.json" (
        echo ✅ configurations_extraites.json supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "explorer_dossiers_bureau.py" (
    del "explorer_dossiers_bureau.py" >nul 2>&1
    if not exist "explorer_dossiers_bureau.py" (
        echo ✅ explorer_dossiers_bureau.py supprime
        set /a fichiers_supprimes+=1
    )
)

if exist "LISTER_DOSSIERS_BUREAU.bat" (
    del "LISTER_DOSSIERS_BUREAU.bat" >nul 2>&1
    if not exist "LISTER_DOSSIERS_BUREAU.bat" (
        echo ✅ LISTER_DOSSIERS_BUREAU.bat supprime
        set /a fichiers_supprimes+=1
    )
)

echo.
echo 📊 %fichiers_supprimes% fichier(s) de clonage supprime(s)

REM Nettoyer la base de données si Python est disponible
if defined PYTHON_CMD (
    echo.
    echo 🗃️ NETTOYAGE DE LA BASE DE DONNEES...
    
    REM Créer un script de nettoyage rapide
    echo import sqlite3 > nettoyage_rapide.py
    echo try: >> nettoyage_rapide.py
    echo     conn = sqlite3.connect('gestimmob_final.db') >> nettoyage_rapide.py
    echo     cursor = conn.cursor() >> nettoyage_rapide.py
    echo     cursor.execute('DROP TABLE IF EXISTS parametres_clones') >> nettoyage_rapide.py
    echo     cursor.execute('DROP TABLE IF EXISTS utilisateurs_clones') >> nettoyage_rapide.py
    echo     cursor.execute('DROP TABLE IF EXISTS immobilisations_clones') >> nettoyage_rapide.py
    echo     conn.commit() >> nettoyage_rapide.py
    echo     conn.close() >> nettoyage_rapide.py
    echo     print('✅ Base de donnees nettoyee') >> nettoyage_rapide.py
    echo except Exception as e: >> nettoyage_rapide.py
    echo     print(f'❌ Erreur: {e}') >> nettoyage_rapide.py
    
    %PYTHON_CMD% nettoyage_rapide.py
    
    REM Supprimer le script temporaire
    del nettoyage_rapide.py >nul 2>&1
)

set /a ANNULATION_REUSSIE=1

:verification_finale
echo.
echo ========================================================================
echo                              VERIFICATION FINALE
echo ========================================================================
echo.

echo [%TIME%] Verification de l'annulation...

REM Vérifier que les fichiers de clonage sont supprimés
set "fichiers_restants=0"

if exist "configuration_gestimmob_importee.json" set /a fichiers_restants+=1
if exist "extraire_parametres_sql.py" set /a fichiers_restants+=1
if exist "cloner_configurations_gestimmob.py" set /a fichiers_restants+=1
if exist "LANCER_GESTIMMOB_CLONE.bat" set /a fichiers_restants+=1

if %fichiers_restants%==0 (
    echo ✅ Tous les fichiers de clonage supprimes
) else (
    echo ⚠️ %fichiers_restants% fichier(s) de clonage restant(s)
)

REM Vérifier que GestImmob fonctionne encore
echo.
echo 🧪 TEST DE FONCTIONNEMENT GESTIMMOB...

if exist "gestimmob_final.py" (
    echo ✅ Fichier principal GestImmob present
    
    if defined PYTHON_CMD (
        echo Test de syntaxe...
        %PYTHON_CMD% -m py_compile gestimmob_final.py >nul 2>&1
        if not errorlevel 1 (
            echo ✅ Syntaxe GestImmob correcte
        ) else (
            echo ⚠️ Probleme de syntaxe detecte
        )
    )
) else (
    echo ❌ Fichier principal GestImmob manquant
)

:end
echo.
echo ========================================================================
echo                              RESULTAT FINAL
echo ========================================================================
echo.

echo [%TIME%] Annulation terminee

if %ANNULATION_REUSSIE%==1 (
    echo.
    echo 🎉 ANNULATION REUSSIE !
    echo ✅ Toutes les modifications du clonage ont ete annulees
    echo ✅ GestImmob est restaure a son etat original
    echo ✅ Fichiers de clonage supprimes
    echo ✅ Base de donnees nettoyee
    echo.
    echo 🎯 GESTIMMOB RESTAURE !
    echo.
    echo 📋 ETAT APRES ANNULATION:
    echo   ✅ Configuration originale restauree
    echo   ✅ Secteurs de base preserves
    echo   ✅ Donnees principales intactes
    echo   ✅ Aucune trace du clonage
    echo.
    echo 💡 Pour lancer GestImmob normal:
    echo    LANCER_GESTIMMOB_SUCCES.bat
    echo    ou: py gestimmob_final.py
    echo.
) else (
    echo.
    echo ⚠️ ANNULATION PARTIELLE
    echo.
    echo 📋 VERIFICATIONS EFFECTUEES:
    echo   - Suppression fichiers: Tentee
    echo   - Nettoyage base: Tente
    echo   - Restauration: Tentee
    echo.
    echo 🔧 ACTIONS RECOMMANDEES:
    echo 1. Relancez ce script
    echo 2. Supprimez manuellement les fichiers restants
    echo 3. Verifiez que GestImmob fonctionne
)

echo.
echo 🔄 ANNULATION DU CLONAGE TERMINEE
echo Toutes les modifications ont ete annulees
echo GestImmob est maintenant dans son etat original
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
