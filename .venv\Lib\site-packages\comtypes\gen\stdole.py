from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    IFontEventsDisp, IEnumVARIANT, BSTR, FontEvents, OLE_YSIZE_PIXELS,
    IUnknown, FONTITALIC, Gray, OLE_HANDLE, O<PERSON>_XSIZE_PIXELS,
    OLE_OPTEXCLUSIVE, FONTBOLD, OLE_XSIZE_CONTAINER,
    OLE_XPOS_HIMETRIC, _check_version, Default, Monochrome,
    FONTUNDERSCORE, typelib_path, _lcid, OLE_YPOS_CONTAINER, FONTNAME,
    IPictureDisp, FONTSIZE, OLE_XPOS_CONTAINER, DISPPROPERTY,
    COMMETHOD, <PERSON><PERSON>_Y<PERSON>ZE_HIMETRIC, Checked, EXCEPINFO, Std<PERSON><PERSON>, <PERSON>ont,
    GUID, StdPicture, <PERSON>LE_ENABLEDEFAULTBOOL, OLE_COLOR, <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>_XSIZE_HIMETRIC, <PERSON><PERSON>_CANCELBOOL, Library, OLE_XPOS_PIXELS,
    OLE_YPOS_HIMETRIC, dispid, CoClass, OLE_YSIZE_CONTAINER,
    IFontDisp, IPicture, VgaColor, Color, OLE_YPOS_PIXELS, HRESULT,
    IFont, Picture, Unchecked, DISPMETHOD, DISPPARAMS, VARIANT_BOOL,
    FONTSTRIKETHROUGH
)


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


__all__ = [
    'IFontEventsDisp', 'StdPicture', 'OLE_COLOR',
    'LoadPictureConstants', 'OLE_XSIZE_HIMETRIC', 'OLE_CANCELBOOL',
    'Library', 'OLE_XPOS_PIXELS', 'FontEvents', 'OLE_YSIZE_PIXELS',
    'OLE_TRISTATE', 'FONTITALIC', 'Gray', 'OLE_HANDLE',
    'OLE_YPOS_HIMETRIC', 'OLE_XSIZE_PIXELS', 'OLE_OPTEXCLUSIVE',
    'OLE_YSIZE_CONTAINER', 'IFontDisp', 'FONTBOLD',
    'OLE_XSIZE_CONTAINER', 'OLE_XPOS_HIMETRIC', 'Default', 'IPicture',
    'FONTUNDERSCORE', 'Monochrome', 'VgaColor', 'Color',
    'OLE_YPOS_PIXELS', 'typelib_path', 'OLE_YPOS_CONTAINER', 'IFont',
    'FONTNAME', 'Picture', 'Unchecked', 'IPictureDisp', 'FONTSIZE',
    'OLE_XPOS_CONTAINER', 'OLE_YSIZE_HIMETRIC', 'Checked', 'StdFont',
    'Font', 'OLE_ENABLEDEFAULTBOOL', 'FONTSTRIKETHROUGH'
]

