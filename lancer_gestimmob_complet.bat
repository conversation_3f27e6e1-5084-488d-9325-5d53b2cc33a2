@echo off
title GestImmob v2.0.0 Complet - Logiciel de Gestion Immobilière Professionnel
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏠 GestImmob v2.0.0 Complet              ║
echo ║              Logiciel de Gestion Immobilière                 ║
echo ║                     Professionnel                            ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║                                                              ║
echo ║  🚀 TOUS LES MODULES OPÉRATIONNELS                          ║
echo ║                                                              ║
echo ║  📋 Gestion des Biens Immobiliers                          ║
echo ║  📦 Inventaire avec Scanner Code-barres                    ║
echo ║  🏢 Gestion des Fournisseurs                               ║
echo ║  💼 ERP/Comptabilité Complet                               ║
echo ║  🧮 Calculatrice Financière Avancée                        ║
echo ║  🔍 Recherche et Filtres Intelligents                      ║
echo ║  ♻️ Gestion des Réformes                                   ║
echo ║  📄 Gestion Documentaire                                   ║
echo ║  🖨️ Impression Multi-formats                               ║
echo ║  📊 Rapports et Analyses                                   ║
echo ║                                                              ║
echo ║  🎨 Interface moderne avec thèmes dynamiques                ║
echo ║  ⚡ Optimisé pour machines performantes                     ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Vérification de l'environnement...
echo.

REM Vérifier que Python est installé
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python n'est pas installé ou pas dans le PATH
    echo.
    echo 📥 Veuillez installer Python depuis https://python.org
    echo    Assurez-vous de cocher "Add Python to PATH" lors de l'installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python détecté
python --version

echo.
echo 🚀 Lancement de GestImmob Complet...
echo.

REM Lancer l'application via le script Python
python lancer_gestimmob_complet.py

REM Vérifier le code de sortie
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Erreur lors du lancement de GestImmob
    echo.
    echo 🔧 Solutions possibles:
    echo    1. Vérifiez que tous les fichiers sont présents
    echo    2. Installez les dépendances manquantes:
    echo       pip install PySide6 cryptography xlsxwriter psutil
    echo    3. Exécutez: python lancer_gestimmob_complet.py
    echo.
    echo 📞 Support: Consultez la documentation ou contactez le support
    echo.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo 👋 GestImmob fermé normalement
echo    Merci d'avoir utilisé GestImmob v2.0.0 !
echo.
pause
