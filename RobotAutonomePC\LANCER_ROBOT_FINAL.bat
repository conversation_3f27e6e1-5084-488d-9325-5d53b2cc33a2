@echo off
title 🚀 Robot IA Transcendant - Lancement Final
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🚀 ROBOT IA TRANSCENDANT - LANCEMENT FINAL                             ██
echo ██    👤 SamNord@110577 - SYSTÈME COMPLET                                    ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🚀 LANCEMENT ROBOT IA TRANSCENDANT
echo ==================================
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo 🤖 Tous modules intégrés
echo.

echo 🔄 Vérification système...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python requis pour lancement
    echo 📥 Installez Python depuis python.org
    pause
    exit /b 1
)
echo ✅ Python détecté

echo.
echo 🚀 LANCEMENT EN COURS...
echo.

REM Lancement Robot IA
python robot_ia_universel.py

echo.
echo 👋 Robot IA fermé
pause
exit /b 0
