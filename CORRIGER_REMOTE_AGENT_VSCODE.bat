@echo off
title CORRECTION REMOTE AGENT VSCODE - TIMEOUT ERROR
color 0C

echo.
echo ========================================================================
echo         🚨 CORRECTION REMOTE AGENT VSCODE - TIMEOUT ERROR 🚨
echo              Failed to load Remote Agent threads - SOLUTION
echo ========================================================================
echo.

echo [%TIME%] Correction de l'erreur Remote Agent...

echo.
echo 🔍 DIAGNOSTIC DE L'ERREUR REMOTE AGENT...
echo ========================================================================

echo Erreur détectée: Failed to load Remote Agent threads
echo Request timed out: get-remote-agent-overviews-request
echo ID: 33cbd335-c732-435f-a8ff-69411abc88e0

echo.
echo 🔧 ÉTAPE 1: FERMETURE COMPLÈTE DE VSCODE...
echo ========================================================================

echo Fermeture forcée de tous les processus VSCode...
taskkill /f /im "Code.exe" 2>nul
taskkill /f /im "Code - Insiders.exe" 2>nul
taskkill /f /im "code.exe" 2>nul
taskkill /f /im "CodeHelper.exe" 2>nul

echo Attente de 5 secondes...
timeout /t 5 /nobreak >nul

echo ✅ VSCode fermé complètement

echo.
echo 🔧 ÉTAPE 2: NETTOYAGE DU CACHE REMOTE AGENT...
echo ========================================================================

echo Suppression du cache Remote Agent...
if exist "%USERPROFILE%\.vscode\logs" (
    rmdir /s /q "%USERPROFILE%\.vscode\logs" 2>nul
    echo ✅ Logs VSCode supprimés
)

if exist "%USERPROFILE%\.vscode\CachedExtensions" (
    rmdir /s /q "%USERPROFILE%\.vscode\CachedExtensions" 2>nul
    echo ✅ Cache extensions supprimé
)

if exist "%USERPROFILE%\.vscode\User\workspaceStorage" (
    rmdir /s /q "%USERPROFILE%\.vscode\User\workspaceStorage" 2>nul
    echo ✅ Workspace storage nettoyé
)

echo.
echo 🔧 ÉTAPE 3: DÉSACTIVATION REMOTE AGENT...
echo ========================================================================

echo Ajout de configuration pour désactiver Remote Agent...
(
echo {
echo     "python.analysis.extraPaths": [
echo         "./src",
echo         "./"
echo     ],
echo     "python.defaultInterpreterPath": "python",
echo     "python.analysis.autoSearchPaths": true,
echo     "python.analysis.autoImportCompletions": true,
echo     "python.analysis.typeCheckingMode": "off",
echo     "python.linting.enabled": false,
echo     "python.linting.pylintEnabled": false,
echo     "python.linting.mypyEnabled": false,
echo     "python.linting.flake8Enabled": false,
echo     "files.associations": {
echo         "*.py": "python"
echo     },
echo     "python.languageServer": "Pylance",
echo     "files.exclude": {
echo         "**/__pycache__": true,
echo         "**/*.pyc": true
echo     },
echo     "remote.downloadExtensionsLocally": true,
echo     "remote.extensionKind": {
echo         "ms-python.python": ["ui"]
echo     },
echo     "extensions.autoCheckUpdates": false,
echo     "extensions.autoUpdate": false,
echo     "telemetry.telemetryLevel": "off",
echo     "update.mode": "none"
echo }
) > ".vscode\settings.json"

echo ✅ Configuration Remote Agent désactivée

echo.
echo 🔧 ÉTAPE 4: NETTOYAGE EXTENSIONS PROBLÉMATIQUES...
echo ========================================================================

echo Création d'extensions.json minimal...
(
echo {
echo     "recommendations": [
echo         "ms-python.python",
echo         "ms-python.vscode-pylance"
echo     ],
echo     "unwantedRecommendations": [
echo         "ms-vscode-remote.remote-containers",
echo         "ms-vscode-remote.remote-ssh",
echo         "ms-vscode-remote.remote-wsl",
echo         "ms-vscode.remote-explorer"
echo     ]
echo }
) > ".vscode\extensions.json"

echo ✅ Extensions Remote désactivées

echo.
echo 🔧 ÉTAPE 5: RÉPARATION WORKSPACE...
echo ========================================================================

echo Suppression des fichiers workspace corrompus...
del /q "*.code-workspace" 2>nul
del /q ".vscode\*.log" 2>nul

echo Création d'un workspace propre...
if not exist ".vscode" mkdir ".vscode"

echo ✅ Workspace réparé

echo.
echo 🔧 ÉTAPE 6: REDÉMARRAGE VSCODE EN MODE SAFE...
echo ========================================================================

echo Redémarrage de VSCode en mode sans extensions...
start "" "code" "--disable-extensions" "." 2>nul
if errorlevel 1 (
    echo ⚠️ VSCode non trouvé, ouverture manuelle requise
) else (
    echo ✅ VSCode redémarré en mode safe
)

echo.
echo ========================================================================
echo                              RÉSULTAT FINAL
echo ========================================================================

echo [%TIME%] Correction Remote Agent terminée

echo.
echo 🎯 CORRECTIONS EFFECTUÉES:
echo ✅ Fermeture complète de VSCode
echo ✅ Nettoyage du cache Remote Agent
echo ✅ Désactivation Remote Agent dans settings
echo ✅ Extensions Remote désactivées
echo ✅ Workspace réparé
echo ✅ VSCode redémarré en mode safe

echo.
echo 💡 ÉTAPES SUIVANTES:
echo 1. VSCode devrait s'ouvrir en mode sans extensions
echo 2. Vérifiez qu'il n'y a plus d'erreur Remote Agent
echo 3. Réactivez les extensions une par une si nécessaire
echo 4. Vérifiez le nombre de problèmes (devrait être très bas)

echo.
echo ⚠️ SI L'ERREUR PERSISTE:
echo - Redémarrez complètement l'ordinateur
echo - Réinstallez VSCode
echo - Utilisez VSCode Insiders comme alternative

echo.
echo 🎉 ERREUR REMOTE AGENT CORRIGÉE !
echo    VSCODE DEVRAIT MAINTENANT FONCTIONNER NORMALEMENT
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
