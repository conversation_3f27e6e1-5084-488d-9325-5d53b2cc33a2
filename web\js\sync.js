// Synchronisation JavaScript GestImmob
class GestImmobSyncManager {
    constructor() {
        this.apiUrl = '/api';
        this.syncInterval = 30000;
        this.isActive = false;
    }

    async start() {
        this.isActive = true;
        console.log('Synchronisation JavaScript démarrée');
        this.syncLoop();
    }

    stop() {
        this.isActive = false;
        console.log('Synchronisation JavaScript arrêtée');
    }

    async syncLoop() {
        while (this.isActive) {
            try {
                await this.performSync();
                await this.delay(this.syncInterval);
            } catch (error) {
                console.error('Erreur de synchronisation:', error);
                await this.delay(5000);
            }
        }
    }

    async performSync() {
        const data = {
            timestamp: new Date().toISOString(),
            component: 'javascript',
            action: 'sync'
        };

        try {
            const response = await fetch(`${this.apiUrl}/sync`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                console.log('Synchronisation réussie');
            }
        } catch (error) {
            console.error('Erreur API:', error);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialisation
const syncManager = new GestImmobSyncManager();
if (typeof window !== 'undefined') {
    window.gestImmobSync = syncManager;
}
