<!-- Generated by SpecStory -->

# /fix Le type de retour « Unknown | Literal[''] » e (2025-06-08 11:29:11)

_**User**_

/fix Le type de retour « Unknown | Literal[''] » est partiellement inconnu

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Corriger la fonction `safe_text` dans la méthode `traiter_ecarts_automatique` pour garantir un type de retour toujours `str` et éviter le type partiellement inconnu.

````python
    def traiter_ecarts_automatique(self):
        """
        Analyse automatique des écarts physiques/théoriques après chaque inventaire.
        Affiche les différences, repositionne les lignes concernées et enrichit la rubrique des écarts.
        """
        def safe_text(item) -> str:
            return str(item.text()) if item is not None and hasattr(item, "text") else ""
        # ...existing code...
````

---

