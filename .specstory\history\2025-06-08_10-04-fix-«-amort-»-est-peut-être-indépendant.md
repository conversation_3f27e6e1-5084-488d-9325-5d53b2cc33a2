<!-- Generated by SpecStory -->

# /fix « amort » est peut-être indépendant (2025-06-08 11:04:11)

_**User**_

/fix « amort » est peut-être indépendant

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Le problème « amort est peut-être indépendant » vient du fait que la variable `amort` n'est définie que dans la boucle `for`, donc en dehors de la boucle, elle n'existe pas si la boucle ne s'exécute pas. Pour corriger cela, il faut soit initialiser `amort` avant la boucle, soit afficher le dernier amortissement calculé de façon sûre (par exemple, en utilisant le dernier élément de la liste `details`).

Voici une correction qui affiche le dernier montant d'amortissement calculé (ou 0 si la durée est 0) :

````python
# ...existing code...
elif expr.lower().startswith("amortdeg(") and expr.endswith(")"):
    # Syntaxe : amortdeg(valeur, taux, duree_mois)
    params = expr[9:-1].split(",")
    if len(params) == 3:
        valeur = float(params[0])
        taux = float(params[1]) / 100
        duree = int(params[2])
        montant = valeur
        details: list[str] = []  # Correction : annotation explicite du type
        amort = 0  # Initialisation pour éviter l'indépendance
        for mois in range(1, duree + 1):
            amort = montant * taux
            montant -= amort
            details.append(f"Mois {mois}: {amort:.2f}")
        msg = " | ".join(details)
        self.calculs.append(expr + " = " + msg)
        self.display.setText(str(amort))
        self._update_history()
        return
# ...existing code...
````

---

