#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface 3D avec Formulaires Complets
Formulaires fond noir • Texte blanc • Logos • Date française • Événements
Interface 3D • Bas bleu turquoise • Tous critères détaillés
"""

import sys
import os
import json
import locale
import calendar
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QTabWidget, QSplitter, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox, QGridLayout, QMessageBox,
    QMenuBar, QStatusBar, QProgressBar, QTextEdit, QLineEdit, QCheckBox,
    QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit, QList<PERSON><PERSON>t, Q<PERSON><PERSON><PERSON><PERSON>t,
    QTreeWidgetItem, QFormLayout, QScrollArea, QSlider, QDial, QButtonGroup,
    QRadioButton, QTextBrowser, QCalendarWidget
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QDate, QTime, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QPainter, QLinearGradient, QBrush

# Configuration locale française
try:
    locale.setlocale(locale.LC_TIME, 'fr_FR.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_TIME, 'French_France.1252')
    except:
        pass

class FormulaireVehicule(QWidget):
    """Formulaire complet véhicule avec fond noir et critères détaillés"""

    def __init__(self):
        super().__init__()
        self.setup_formulaire_vehicule()

    def setup_formulaire_vehicule(self):
        self.setWindowTitle("🚗 MASTER COMPTA - FORMULAIRE VÉHICULE COMPLET")
        self.setGeometry(150, 150, 1200, 800)

        # Style fond noir avec texte blanc
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #000000, stop:0.3 #1a1a1a, stop:0.7 #1a1a1a, stop:1 #000000);
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 3px solid #00bcd4;
                border-radius: 15px;
                margin: 10px;
                padding-top: 20px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0,188,212,0.2), stop:1 rgba(0,188,212,0.1));
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 15px;
                background: #00bcd4;
                border-radius: 8px;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a2a2a, stop:1 #1a1a1a);
                border: 2px solid #00bcd4;
                border-radius: 8px;
                padding: 10px;
                color: white;
                font-size: 11pt;
                min-height: 20px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
                border: 3px solid #4dd0e1;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3a3a3a, stop:1 #2a2a2a);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00bcd4, stop:0.5 #0097a7, stop:1 #00838f);
                color: white;
                border: 3px solid #00bcd4;
                border-radius: 12px;
                font-size: 12pt;
                font-weight: bold;
                padding: 12px 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4dd0e1, stop:0.5 #26c6da, stop:1 #00acc1);
                border: 3px solid #4dd0e1;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #006064, stop:0.5 #00838f, stop:1 #0097a7);
            }
        """)

        layout = QVBoxLayout(self)

        # Titre avec logo
        title_layout = QHBoxLayout()
        logo_label = QLabel("🚗")
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 48pt;
                color: #00bcd4;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0,188,212,0.3), stop:1 rgba(0,188,212,0.1));
                border: 3px solid #00bcd4;
                border-radius: 25px;
                padding: 20px;
                margin: 10px;
            }
        """)

        title_label = QLabel("FORMULAIRE VÉHICULE COMPLET")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24pt;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00bcd4, stop:0.5 #4dd0e1, stop:1 #00bcd4);
                border: 3px solid #00bcd4;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)

        title_layout.addWidget(logo_label)
        title_layout.addWidget(title_label, 1)
        layout.addLayout(title_layout)

        # Zone de défilement pour le formulaire
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid #00bcd4;
                border-radius: 10px;
                background: transparent;
            }
        """)

        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)

        # Groupe Informations Générales
        self.create_general_info_group(form_layout)

        # Groupe Caractéristiques Techniques
        self.create_technical_specs_group(form_layout)

        # Groupe Informations Financières
        self.create_financial_info_group(form_layout)

        # Groupe Maintenance et Suivi
        self.create_maintenance_group(form_layout)

        # Groupe Assurance et Légal
        self.create_insurance_legal_group(form_layout)

        # Groupe Observations et Documents
        self.create_observations_group(form_layout)

        scroll_area.setWidget(form_widget)
        layout.addWidget(scroll_area)

        # Boutons d'action
        self.create_action_buttons(layout)

    def create_general_info_group(self, layout):
        """Crée le groupe informations générales"""
        group = QGroupBox("📋 INFORMATIONS GÉNÉRALES")
        group_layout = QGridLayout(group)

        # Code véhicule avec logo
        code_layout = QHBoxLayout()
        code_logo = QLabel("🏷️")
        code_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("Ex: V001, V002...")
        code_layout.addWidget(code_logo)
        code_layout.addWidget(self.code_edit)
        group_layout.addLayout(code_layout, 0, 0)

        # Marque avec logo
        marque_layout = QHBoxLayout()
        marque_logo = QLabel("🚗")
        marque_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.marque_combo = QComboBox()
        self.marque_combo.addItems([
            "Sélectionner...", "Peugeot", "Renault", "Citroën", "Ford",
            "Volkswagen", "BMW", "Mercedes", "Audi", "Toyota", "Nissan",
            "Opel", "Fiat", "Hyundai", "Kia", "Mazda", "Honda", "Volvo"
        ])
        marque_layout.addWidget(marque_logo)
        marque_layout.addWidget(self.marque_combo)
        group_layout.addLayout(marque_layout, 0, 1)

        # Modèle avec logo
        modele_layout = QHBoxLayout()
        modele_logo = QLabel("🔧")
        modele_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.modele_edit = QLineEdit()
        self.modele_edit.setPlaceholderText("Ex: 308, Clio, Focus...")
        modele_layout.addWidget(modele_logo)
        modele_layout.addWidget(self.modele_edit)
        group_layout.addLayout(modele_layout, 1, 0)

        # Immatriculation avec logo
        immat_layout = QHBoxLayout()
        immat_logo = QLabel("🔢")
        immat_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.immat_edit = QLineEdit()
        self.immat_edit.setPlaceholderText("Ex: AB-123-CD")
        immat_layout.addWidget(immat_logo)
        immat_layout.addWidget(self.immat_edit)
        group_layout.addLayout(immat_layout, 1, 1)

        # Année avec logo
        annee_layout = QHBoxLayout()
        annee_logo = QLabel("📅")
        annee_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.annee_spin = QSpinBox()
        self.annee_spin.setRange(1990, 2025)
        self.annee_spin.setValue(2020)
        annee_layout.addWidget(annee_logo)
        annee_layout.addWidget(self.annee_spin)
        group_layout.addLayout(annee_layout, 2, 0)

        # Couleur avec logo
        couleur_layout = QHBoxLayout()
        couleur_logo = QLabel("🎨")
        couleur_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.couleur_combo = QComboBox()
        self.couleur_combo.addItems([
            "Sélectionner...", "Blanc", "Noir", "Gris", "Rouge", "Bleu",
            "Vert", "Jaune", "Orange", "Violet", "Marron", "Beige"
        ])
        couleur_layout.addWidget(couleur_logo)
        couleur_layout.addWidget(self.couleur_combo)
        group_layout.addLayout(couleur_layout, 2, 1)

        layout.addWidget(group)

    def create_technical_specs_group(self, layout):
        """Crée le groupe caractéristiques techniques"""
        group = QGroupBox("⚙️ CARACTÉRISTIQUES TECHNIQUES")
        group_layout = QGridLayout(group)

        # Carburant avec logo
        carburant_layout = QHBoxLayout()
        carburant_logo = QLabel("⛽")
        carburant_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.carburant_combo = QComboBox()
        self.carburant_combo.addItems([
            "Sélectionner...", "Essence", "Diesel", "Hybride",
            "Électrique", "GPL", "Éthanol E85", "Hydrogène"
        ])
        carburant_layout.addWidget(carburant_logo)
        carburant_layout.addWidget(self.carburant_combo)
        group_layout.addLayout(carburant_layout, 0, 0)

        # Puissance avec logo
        puissance_layout = QHBoxLayout()
        puissance_logo = QLabel("⚡")
        puissance_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.puissance_spin = QSpinBox()
        self.puissance_spin.setRange(50, 1000)
        self.puissance_spin.setSuffix(" CV")
        puissance_layout.addWidget(puissance_logo)
        puissance_layout.addWidget(self.puissance_spin)
        group_layout.addLayout(puissance_layout, 0, 1)

        # Kilométrage avec logo
        km_layout = QHBoxLayout()
        km_logo = QLabel("📏")
        km_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.km_spin = QSpinBox()
        self.km_spin.setRange(0, 999999)
        self.km_spin.setSuffix(" km")
        km_layout.addWidget(km_logo)
        km_layout.addWidget(self.km_spin)
        group_layout.addLayout(km_layout, 1, 0)

        # Transmission avec logo
        transmission_layout = QHBoxLayout()
        transmission_logo = QLabel("🔄")
        transmission_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.transmission_combo = QComboBox()
        self.transmission_combo.addItems([
            "Sélectionner...", "Manuelle", "Automatique",
            "Semi-automatique", "CVT", "Robotisée"
        ])
        transmission_layout.addWidget(transmission_logo)
        transmission_layout.addWidget(self.transmission_combo)
        group_layout.addLayout(transmission_layout, 1, 1)

        # Nombre de places avec logo
        places_layout = QHBoxLayout()
        places_logo = QLabel("👥")
        places_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.places_spin = QSpinBox()
        self.places_spin.setRange(1, 9)
        self.places_spin.setValue(5)
        self.places_spin.setSuffix(" places")
        places_layout.addWidget(places_logo)
        places_layout.addWidget(self.places_spin)
        group_layout.addLayout(places_layout, 2, 0)

        # Nombre de portes avec logo
        portes_layout = QHBoxLayout()
        portes_logo = QLabel("🚪")
        portes_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.portes_spin = QSpinBox()
        self.portes_spin.setRange(2, 5)
        self.portes_spin.setValue(4)
        self.portes_spin.setSuffix(" portes")
        portes_layout.addWidget(portes_logo)
        portes_layout.addWidget(self.portes_spin)
        group_layout.addLayout(portes_layout, 2, 1)

        layout.addWidget(group)

    def create_financial_info_group(self, layout):
        """Crée le groupe informations financières"""
        group = QGroupBox("💰 INFORMATIONS FINANCIÈRES")
        group_layout = QGridLayout(group)

        # Prix d'achat avec logo
        prix_achat_layout = QHBoxLayout()
        prix_logo = QLabel("💵")
        prix_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.prix_achat_spin = QSpinBox()
        self.prix_achat_spin.setRange(0, 999999)
        self.prix_achat_spin.setSuffix(" €")
        prix_achat_layout.addWidget(prix_logo)
        prix_achat_layout.addWidget(self.prix_achat_spin)
        group_layout.addLayout(prix_achat_layout, 0, 0)

        # Coût mensuel avec logo
        cout_layout = QHBoxLayout()
        cout_logo = QLabel("📊")
        cout_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.cout_mensuel_spin = QSpinBox()
        self.cout_mensuel_spin.setRange(0, 5000)
        self.cout_mensuel_spin.setSuffix(" €/mois")
        cout_layout.addWidget(cout_logo)
        cout_layout.addWidget(self.cout_mensuel_spin)
        group_layout.addLayout(cout_layout, 0, 1)

        # Valeur actuelle avec logo
        valeur_layout = QHBoxLayout()
        valeur_logo = QLabel("💎")
        valeur_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.valeur_actuelle_spin = QSpinBox()
        self.valeur_actuelle_spin.setRange(0, 999999)
        self.valeur_actuelle_spin.setSuffix(" €")
        valeur_layout.addWidget(valeur_logo)
        valeur_layout.addWidget(self.valeur_actuelle_spin)
        group_layout.addLayout(valeur_layout, 1, 0)

        # Statut avec logo
        statut_layout = QHBoxLayout()
        statut_logo = QLabel("📈")
        statut_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.statut_combo = QComboBox()
        self.statut_combo.addItems([
            "Sélectionner...", "Actif", "Maintenance", "En panne",
            "Réservé", "Vendu", "En vente", "Hors service"
        ])
        statut_layout.addWidget(statut_logo)
        statut_layout.addWidget(self.statut_combo)
        group_layout.addLayout(statut_layout, 1, 1)

        layout.addWidget(group)

    def create_maintenance_group(self, layout):
        """Crée le groupe maintenance et suivi"""
        group = QGroupBox("🔧 MAINTENANCE ET SUIVI")
        group_layout = QGridLayout(group)

        # Dernière révision avec logo
        derniere_revision_layout = QHBoxLayout()
        revision_logo = QLabel("🔧")
        revision_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.derniere_revision_edit = QDateEdit()
        self.derniere_revision_edit.setDate(QDate.currentDate())
        derniere_revision_layout.addWidget(revision_logo)
        derniere_revision_layout.addWidget(self.derniere_revision_edit)
        group_layout.addLayout(derniere_revision_layout, 0, 0)

        # Prochaine révision avec logo
        prochaine_revision_layout = QHBoxLayout()
        prochaine_logo = QLabel("📅")
        prochaine_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.prochaine_revision_edit = QDateEdit()
        self.prochaine_revision_edit.setDate(QDate.currentDate().addMonths(6))
        prochaine_revision_layout.addWidget(prochaine_logo)
        prochaine_revision_layout.addWidget(self.prochaine_revision_edit)
        group_layout.addLayout(prochaine_revision_layout, 0, 1)

        # Conducteur avec logo
        conducteur_layout = QHBoxLayout()
        conducteur_logo = QLabel("👤")
        conducteur_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.conducteur_combo = QComboBox()
        self.conducteur_combo.addItems([
            "Sélectionner...", "Martin Jean", "Durand Marie", "Leblanc Pierre",
            "Moreau Sophie", "Bernard Luc", "Petit Anne", "Roux Thomas",
            "Direction", "Pool véhicules", "Service commercial"
        ])
        conducteur_layout.addWidget(conducteur_logo)
        conducteur_layout.addWidget(self.conducteur_combo)
        group_layout.addLayout(conducteur_layout, 1, 0)

        # Garage avec logo
        garage_layout = QHBoxLayout()
        garage_logo = QLabel("🏪")
        garage_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.garage_edit = QLineEdit()
        self.garage_edit.setPlaceholderText("Ex: Garage Central, Peugeot Service...")
        garage_layout.addWidget(garage_logo)
        garage_layout.addWidget(self.garage_edit)
        group_layout.addLayout(garage_layout, 1, 1)

        layout.addWidget(group)

    def create_insurance_legal_group(self, layout):
        """Crée le groupe assurance et légal"""
        group = QGroupBox("🛡️ ASSURANCE ET LÉGAL")
        group_layout = QGridLayout(group)

        # Type assurance avec logo
        assurance_layout = QHBoxLayout()
        assurance_logo = QLabel("🛡️")
        assurance_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.assurance_combo = QComboBox()
        self.assurance_combo.addItems([
            "Sélectionner...", "Tous risques", "Tiers étendu", "Tiers",
            "Temporaire", "Professionnelle", "Flotte"
        ])
        assurance_layout.addWidget(assurance_logo)
        assurance_layout.addWidget(self.assurance_combo)
        group_layout.addLayout(assurance_layout, 0, 0)

        # Compagnie assurance avec logo
        compagnie_layout = QHBoxLayout()
        compagnie_logo = QLabel("🏢")
        compagnie_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.compagnie_edit = QLineEdit()
        self.compagnie_edit.setPlaceholderText("Ex: AXA, Allianz, MAIF...")
        compagnie_layout.addWidget(compagnie_logo)
        compagnie_layout.addWidget(self.compagnie_edit)
        group_layout.addLayout(compagnie_layout, 0, 1)

        # Numéro police avec logo
        police_layout = QHBoxLayout()
        police_logo = QLabel("📋")
        police_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.police_edit = QLineEdit()
        self.police_edit.setPlaceholderText("Numéro de police d'assurance")
        police_layout.addWidget(police_logo)
        police_layout.addWidget(self.police_edit)
        group_layout.addLayout(police_layout, 1, 0)

        # Date expiration avec logo
        expiration_layout = QHBoxLayout()
        expiration_logo = QLabel("⏰")
        expiration_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        self.expiration_edit = QDateEdit()
        self.expiration_edit.setDate(QDate.currentDate().addYears(1))
        expiration_layout.addWidget(expiration_logo)
        expiration_layout.addWidget(self.expiration_edit)
        group_layout.addLayout(expiration_layout, 1, 1)

        layout.addWidget(group)

    def create_observations_group(self, layout):
        """Crée le groupe observations et documents"""
        group = QGroupBox("📝 OBSERVATIONS ET DOCUMENTS")
        group_layout = QVBoxLayout(group)

        # Observations avec logo
        obs_layout = QHBoxLayout()
        obs_logo = QLabel("📝")
        obs_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        obs_layout.addWidget(obs_logo)

        self.observations_edit = QTextEdit()
        self.observations_edit.setPlaceholderText("Observations, remarques, historique...")
        self.observations_edit.setMaximumHeight(100)
        self.observations_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a2a2a, stop:1 #1a1a1a);
                border: 2px solid #00bcd4;
                border-radius: 8px;
                padding: 10px;
                color: white;
                font-size: 11pt;
            }
        """)
        obs_layout.addWidget(self.observations_edit)
        group_layout.addLayout(obs_layout)

        # Documents avec logo
        docs_layout = QHBoxLayout()
        docs_logo = QLabel("📄")
        docs_logo.setStyleSheet("font-size: 16pt; color: #00bcd4;")
        docs_layout.addWidget(docs_logo)

        self.documents_edit = QLineEdit()
        self.documents_edit.setPlaceholderText("Carte grise, factures, contrôle technique...")
        docs_layout.addWidget(self.documents_edit)
        group_layout.addLayout(docs_layout)

        layout.addWidget(group)

    def create_action_buttons(self, layout):
        """Crée les boutons d'action"""
        buttons_layout = QHBoxLayout()

        # Boutons avec logos et styles 3D
        buttons = [
            ("💾", "ENREGISTRER", "#4caf50"),
            ("🔄", "MODIFIER", "#2196f3"),
            ("🗑️", "SUPPRIMER", "#f44336"),
            ("📋", "DUPLIQUER", "#ff9800"),
            ("📊", "RAPPORT", "#9c27b0"),
            ("❌", "ANNULER", "#607d8b")
        ]

        for logo, text, color in buttons:
            btn = QPushButton(f"{logo} {text}")
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:0.5 rgba(255,255,255,0.3), stop:1 {color});
                    color: white;
                    border: 3px solid {color};
                    border-radius: 15px;
                    font-size: 14pt;
                    font-weight: bold;
                    padding: 15px 25px;
                    min-width: 150px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.4), stop:0.5 {color}, stop:1 rgba(255,255,255,0.4));
                    border: 3px solid white;
                    transform: scale(1.1);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0,0,0,0.4), stop:0.5 {color}, stop:1 rgba(0,0,0,0.4));
                }}
            """)
            buttons_layout.addWidget(btn)

        layout.addLayout(buttons_layout)

class DateTimeWidget3D(QWidget):
    """Widget date/heure 3D avec événements en français"""

    def __init__(self):
        super().__init__()
        self.setup_datetime_3d()

        # Timer pour mise à jour
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)

    def setup_datetime_3d(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # Style 3D avec effet de profondeur
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff5722, stop:0.3 #ff7043, stop:0.7 #ff6f00, stop:1 #ff5722);
                border: 4px solid #ff5722;
                border-radius: 20px;
                color: white;
            }
            QLabel {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
                color: white;
                font-weight: bold;
            }
        """)

        # Date française agrandie
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.date_label.setStyleSheet("""
            QLabel {
                font-size: 20pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.3), stop:1 rgba(255,255,255,0.1));
                border: 3px solid rgba(255,255,255,0.5);
                border-radius: 15px;
                padding: 20px;
                min-height: 60px;
            }
        """)
        layout.addWidget(self.date_label)

        # Heure agrandie
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                font-size: 28pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.4), stop:1 rgba(255,255,255,0.2));
                border: 3px solid rgba(255,255,255,0.6);
                border-radius: 15px;
                padding: 25px;
                min-height: 80px;
            }
        """)
        layout.addWidget(self.time_label)

        # Événements du jour
        self.events_label = QLabel()
        self.events_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.events_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 2px solid rgba(255,255,255,0.4);
                border-radius: 10px;
                padding: 15px;
                min-height: 100px;
            }
        """)
        layout.addWidget(self.events_label)

        self.update_datetime()

    def update_datetime(self):
        """Met à jour date/heure en français avec événements"""
        now = datetime.now()

        # Date en français
        jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
        mois = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']

        jour_semaine = jours[now.weekday()]
        mois_nom = mois[now.month - 1]

        self.date_label.setText(f"📅 {jour_semaine} {now.day} {mois_nom} {now.year}")
        self.time_label.setText(f"🕐 {now.strftime('%H:%M:%S')}")

        # Événements du jour
        events = self.get_daily_events(now)
        self.events_label.setText(f"📋 ÉVÉNEMENTS DU JOUR:\n{events}")

    def get_daily_events(self, date):
        """Récupère les événements du jour"""
        events = []

        # Événements fixes
        if date.weekday() == 0:  # Lundi
            events.append("📊 Réunion équipe 9h00")
            events.append("📋 Rapport hebdomadaire")
        elif date.weekday() == 2:  # Mercredi
            events.append("🔧 Maintenance véhicules")
            events.append("📞 Appels clients")
        elif date.weekday() == 4:  # Vendredi
            events.append("💾 Sauvegarde données")
            events.append("📊 Bilan semaine")

        # Événements mensuels
        if date.day == 1:
            events.append("📈 Rapport mensuel")
        elif date.day == 15:
            events.append("💰 Paie employés")
        elif date.day == 30 or date.day == 31:
            events.append("📋 Clôture mois")

        # Événements saisonniers
        if date.month == 12 and date.day == 25:
            events.append("🎄 Noël - Fermé")
        elif date.month == 1 and date.day == 1:
            events.append("🎊 Nouvel An - Fermé")

        if not events:
            events.append("📅 Aucun événement prévu")
            events.append("✅ Journée normale")

        return '\n'.join(events)

class MasterCompta3DInterface(QMainWindow):
    """Interface MASTER COMPTA 3D avec formulaires et bas bleu turquoise"""

    def __init__(self):
        super().__init__()
        self.setup_3d_interface()

    def setup_3d_interface(self):
        """Configure l'interface 3D complète"""
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - INTERFACE 3D RÉVOLUTIONNAIRE")

        # Taille et position
        screen = QApplication.primaryScreen().geometry()
        width = min(1900, int(screen.width() * 0.98))
        height = min(1300, int(screen.height() * 0.98))
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        # Style 3D principal avec dégradés
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.3 #2a5298, stop:0.7 #1e3c72, stop:1 #0d47a1);
                color: white;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:0.5 #1976d2, stop:1 #0d47a1);
                border-bottom: 4px solid #00bcd4;
                padding: 12px;
                font-size: 16pt;
                font-weight: bold;
                color: white;
                border-radius: 0px 0px 15px 15px;
            }
            QMenuBar::item {
                background: transparent;
                padding: 12px 25px;
                border-radius: 10px;
                color: white;
                margin: 3px;
            }
            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.4), stop:1 rgba(255,255,255,0.2));
                border: 2px solid #00bcd4;
                color: white;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00bcd4, stop:0.3 #26c6da, stop:0.7 #4dd0e1, stop:1 #00bcd4);
                border-top: 4px solid #0097a7;
                color: white;
                font-size: 14pt;
                font-weight: bold;
                padding: 12px;
                border-radius: 15px 15px 0px 0px;
                min-height: 40px;
            }
        """)

        # Menu 3D
        self.create_3d_menubar()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Zone supérieure avec widgets 3D
        self.create_3d_widgets_area(main_layout)

        # Zone centrale avec modules 3D
        self.create_3d_modules_area(main_layout)

        # Zone inférieure avec outils 3D
        self.create_3d_tools_area(main_layout)

        # Barre de statut bleu turquoise
        self.create_turquoise_statusbar()

    def create_3d_menubar(self):
        """Crée le menu 3D"""
        menubar = self.menuBar()

        # Menu Fichier 3D
        file_menu = menubar.addMenu("📁 FICHIERS")
        file_menu.addAction("🆕 Nouveau Dossier", self.nouveau_dossier)
        file_menu.addAction("📂 Ouvrir Dossier", self.ouvrir_dossier)
        file_menu.addAction("💾 Enregistrer", self.enregistrer)
        file_menu.addAction("🖨️ Imprimer", self.imprimer)
        file_menu.addAction("❌ Quitter", self.close)

        # Menu Modules 3D
        modules_menu = menubar.addMenu("🏢 MODULES")
        modules_menu.addAction("🚗 Parc Auto", self.ouvrir_parc_auto_3d)
        modules_menu.addAction("🏢 Immobilier", self.ouvrir_immobilier_3d)
        modules_menu.addAction("👥 Personnel", self.ouvrir_personnel_3d)
        modules_menu.addAction("📊 Comptabilité", self.ouvrir_comptabilite_3d)

        # Menu Outils 3D
        tools_menu = menubar.addMenu("🔧 OUTILS")
        tools_menu.addAction("🗃️ Bases Données", self.ouvrir_bases_donnees)
        tools_menu.addAction("🤖 IA Ollama", self.ouvrir_ia_ollama)
        tools_menu.addAction("📊 Rapports", self.generer_rapports)

        # Menu Paramètres 3D
        settings_menu = menubar.addMenu("⚙️ PARAMÈTRES")
        settings_menu.addAction("🎨 Interface", self.configurer_interface)
        settings_menu.addAction("🌍 Langues", self.configurer_langues)
        settings_menu.addAction("🔔 Notifications", self.configurer_notifications)

        # Menu Aide 3D
        help_menu = menubar.addMenu("❓ AIDE")
        help_menu.addAction("📖 Documentation", self.afficher_documentation)
        help_menu.addAction("🆘 Support", self.contacter_support)
        help_menu.addAction("ℹ️ À Propos", self.afficher_a_propos)

    def create_3d_widgets_area(self, layout):
        """Crée la zone supérieure avec widgets 3D"""
        widgets_frame = QFrame()
        widgets_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:0.5 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 4px solid rgba(255,255,255,0.4);
                border-radius: 25px;
                margin: 15px;
                padding: 25px;
            }
        """)

        widgets_layout = QHBoxLayout(widgets_frame)
        widgets_layout.setSpacing(25)

        # Widget date/heure 3D français
        self.datetime_3d_widget = DateTimeWidget3D()
        self.datetime_3d_widget.setMinimumWidth(350)
        widgets_layout.addWidget(self.datetime_3d_widget)

        # Autres widgets 3D (simplifiés pour l'exemple)
        for i, (title, icon, color) in enumerate([
            ("PRIÈRES GPS", "🕌", "#4caf50"),
            ("MÉTÉO 15J", "🌤️", "#2196f3"),
            ("CONNEXIONS", "📡", "#9c27b0"),
            ("MÉMOS", "📝", "#e91e63")
        ]):
            widget = self.create_simple_3d_widget(title, icon, color)
            widget.setMinimumWidth(280)
            widgets_layout.addWidget(widget)

        layout.addWidget(widgets_frame)

    def create_simple_3d_widget(self, title, icon, color):
        """Crée un widget 3D simple"""
        widget = QWidget()
        widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:0.3 rgba(255,255,255,0.3), stop:0.7 {color}, stop:1 rgba(0,0,0,0.2));
                border: 4px solid {color};
                border-radius: 20px;
                color: white;
            }}
            QLabel {{
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
                color: white;
                font-weight: bold;
            }}
        """)

        layout = QVBoxLayout(widget)

        # Titre avec icône
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                background: rgba(255,255,255,0.2);
                border: 3px solid rgba(255,255,255,0.4);
                border-radius: 15px;
                padding: 20px;
                min-height: 40px;
            }
        """)
        layout.addWidget(title_label)

        # Contenu
        content_label = QLabel("Contenu détaillé\ndu widget\navec informations\ncomplètes")
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                padding: 15px;
                min-height: 100px;
            }
        """)
        layout.addWidget(content_label)

        return widget

    def create_3d_modules_area(self, layout):
        """Crée la zone centrale avec modules 3D"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:0.5 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 4px solid rgba(255,255,255,0.4);
                border-radius: 25px;
                margin: 15px;
                padding: 25px;
            }
        """)

        modules_layout = QVBoxLayout(modules_frame)

        # Titre modules 3D
        modules_title = QLabel("🏢 MODULES MASTER COMPTA 3D")
        modules_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        modules_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 32pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b35, stop:0.3 #f7931e, stop:0.7 #ff9800, stop:1 #ff6b35);
                border: 5px solid #ff6b35;
                border-radius: 25px;
                padding: 30px;
                margin: 20px;
            }
        """)
        modules_layout.addWidget(modules_title)

        # Grille des modules 3D
        modules_grid = QGridLayout()
        modules_grid.setSpacing(30)

        # Modules avec effets 3D
        modules = [
            ("🚗", "PARC AUTO", "Formulaire complet", "#4caf50", self.ouvrir_parc_auto_3d),
            ("🏢", "IMMOBILIER", "Gestion biens", "#2196f3", self.ouvrir_immobilier_3d),
            ("👥", "PERSONNEL", "Gestion employés", "#ff9800", self.ouvrir_personnel_3d),
            ("📊", "COMPTABILITÉ", "Compta générale", "#9c27b0", self.ouvrir_comptabilite_3d),
            ("💰", "FINANCES", "Trésorerie", "#f44336", self.ouvrir_finances_3d),
            ("📋", "INVENTAIRE", "Stock articles", "#00bcd4", self.ouvrir_inventaire_3d),
            ("🏭", "PRODUCTION", "Gestion production", "#795548", self.ouvrir_production_3d),
            ("🛒", "ACHATS", "Fournisseurs", "#607d8b", self.ouvrir_achats_3d)
        ]

        for i, (icon, title, desc, color, callback) in enumerate(modules):
            row = i // 4
            col = i % 4

            module_btn = QPushButton()
            module_btn.setMinimumSize(300, 160)
            module_btn.clicked.connect(callback)

            module_btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}, stop:0.3 rgba(255,255,255,0.4),
                        stop:0.7 {color}, stop:1 rgba(0,0,0,0.3));
                    color: white;
                    border: 5px solid {color};
                    border-radius: 25px;
                    font-size: 18pt;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255,255,255,0.5), stop:0.3 {color},
                        stop:0.7 rgba(255,255,255,0.5), stop:1 {color});
                    border: 5px solid white;
                    transform: scale(1.1);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(0,0,0,0.5), stop:0.3 {color},
                        stop:0.7 rgba(0,0,0,0.5), stop:1 {color});
                }}
            """)

            module_btn.setText(f"{icon}\n{title}\n{desc}")

            modules_grid.addWidget(module_btn, row, col)

        modules_layout.addLayout(modules_grid)
        layout.addWidget(modules_frame)

    def create_3d_tools_area(self, layout):
        """Crée la zone inférieure avec outils 3D"""
        tools_frame = QFrame()
        tools_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.1), stop:0.5 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
                border: 4px solid rgba(255,255,255,0.4);
                border-radius: 25px;
                margin: 15px;
                padding: 25px;
            }
        """)

        tools_layout = QHBoxLayout(tools_frame)
        tools_layout.setSpacing(25)

        # Outils 3D
        tools = [
            ("📋", "COPIER", "#37474f"),
            ("📄", "COLLER", "#455a64"),
            ("✂️", "COUPER", "#546e7a"),
            ("↩️", "ANNULER", "#607d8b"),
            ("🔍", "RECHERCHER", "#78909c"),
            ("💾", "SAUVEGARDER", "#90a4ae"),
            ("🖨️", "IMPRIMER", "#b0bec5"),
            ("🔄", "SYNCHRONISER", "#cfd8dc")
        ]

        for icon, text, color in tools:
            tool_btn = QPushButton(f"{icon}\n{text}")
            tool_btn.setMinimumSize(200, 100)
            tool_btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:0.5 rgba(255,255,255,0.3), stop:1 {color});
                    color: white;
                    border: 4px solid {color};
                    border-radius: 20px;
                    font-size: 14pt;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.4), stop:0.5 {color}, stop:1 rgba(255,255,255,0.4));
                    border: 4px solid white;
                    transform: scale(1.08);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0,0,0,0.4), stop:0.5 {color}, stop:1 rgba(0,0,0,0.4));
                }}
            """)
            tools_layout.addWidget(tool_btn)

        layout.addWidget(tools_frame)

    def create_turquoise_statusbar(self):
        """Crée la barre de statut bleu turquoise"""
        statusbar = self.statusBar()

        # Message principal
        statusbar.showMessage("🏢 MASTER COMPTA GÉNÉRAL 3D - Interface Révolutionnaire avec Formulaires Complets • Date Française • Événements • Bas Bleu Turquoise")

        # Widgets permanents
        # Heure actuelle
        self.status_time = QLabel()
        self.status_time.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                padding: 10px;
                font-size: 14pt;
                background: rgba(255,255,255,0.2);
                border: 2px solid rgba(255,255,255,0.4);
                border-radius: 8px;
                margin: 5px;
            }
        """)
        statusbar.addPermanentWidget(self.status_time)

        # Statut système
        self.status_system = QLabel("🟢 Système 3D Optimal")
        self.status_system.setStyleSheet("""
            QLabel {
                color: #4caf50;
                font-weight: bold;
                padding: 10px;
                font-size: 14pt;
                background: rgba(76,175,80,0.2);
                border: 2px solid #4caf50;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        statusbar.addPermanentWidget(self.status_system)

        # Mode 3D
        self.status_mode = QLabel("MODE 3D ACTIVÉ")
        self.status_mode.setStyleSheet("""
            QLabel {
                color: #ff9800;
                font-weight: bold;
                padding: 10px;
                font-size: 14pt;
                background: rgba(255,152,0,0.2);
                border: 2px solid #ff9800;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        statusbar.addPermanentWidget(self.status_mode)

        # Timer pour mise à jour
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_statusbar)
        self.status_timer.start(1000)

    def update_statusbar(self):
        """Met à jour la barre de statut"""
        now = datetime.now()
        self.status_time.setText(f"🕐 {now.strftime('%H:%M:%S')}")

    # Méthodes d'ouverture des modules avec formulaires
    def ouvrir_parc_auto_3d(self):
        """Ouvre le formulaire Parc Auto complet"""
        self.formulaire_vehicule = FormulaireVehicule()
        self.formulaire_vehicule.show()

    def ouvrir_immobilier_3d(self):
        """Ouvre le formulaire Immobilier"""
        print("🏢 Ouverture formulaire Immobilier 3D...")
        # Ici on ouvrirait un formulaire similaire pour l'immobilier

    def ouvrir_personnel_3d(self):
        """Ouvre le formulaire Personnel"""
        print("👥 Ouverture formulaire Personnel 3D...")
        # Ici on ouvrirait un formulaire similaire pour le personnel

    def ouvrir_comptabilite_3d(self):
        """Ouvre le formulaire Comptabilité"""
        print("📊 Ouverture formulaire Comptabilité 3D...")
        # Ici on ouvrirait un formulaire similaire pour la comptabilité

    # Autres méthodes (stubs)
    def nouveau_dossier(self): print("🆕 Nouveau dossier")
    def ouvrir_dossier(self): print("📂 Ouvrir dossier")
    def enregistrer(self): print("💾 Enregistrer")
    def imprimer(self): print("🖨️ Imprimer")
    def ouvrir_bases_donnees(self): print("🗃️ Bases données")
    def ouvrir_ia_ollama(self): print("🤖 IA Ollama")
    def generer_rapports(self): print("📊 Rapports")
    def configurer_interface(self): print("🎨 Interface")
    def configurer_langues(self): print("🌍 Langues")
    def configurer_notifications(self): print("🔔 Notifications")
    def afficher_documentation(self): print("📖 Documentation")
    def contacter_support(self): print("🆘 Support")
    def afficher_a_propos(self): print("ℹ️ À propos")
    def ouvrir_finances_3d(self): print("💰 Finances 3D")
    def ouvrir_inventaire_3d(self): print("📋 Inventaire 3D")
    def ouvrir_production_3d(self): print("🏭 Production 3D")
    def ouvrir_achats_3d(self): print("🛒 Achats 3D")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Configuration MASTER COMPTA 3D
    app.setApplicationName("MASTER COMPTA GÉNÉRAL 3D")
    app.setApplicationVersion("Interface 3D Révolutionnaire")
    app.setOrganizationName("MASTER COMPTA")

    window = MasterCompta3DInterface()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()