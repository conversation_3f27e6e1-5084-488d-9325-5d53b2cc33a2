-- Base de données SQLite compatible
-- Correction de SERIAL vers INTEGER PRIMARY KEY AUTOINCREMENT

CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE
);

CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role_id INTEGER REFERENCES roles(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);

-- Table des fournisseurs
CREATE TABLE fournisseurs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nom VARCHAR(255) NOT NULL,
    adresse TEXT,
    telephone VARCHAR(50),
    email VARCHAR(255),
    type VARCHAR(50),
    siret VARCHAR(50),
    registre_commerce VARCHAR(50),
    nif VARCHAR(50),
    compte_bancaire VARCHAR(100),
    banque VARCHAR(100),
    contact VARCHAR(255),
    categorie VARCHAR(50),
    statut VARCHAR(50),
    historique_achats TEXT,
    historique_factures TEXT,
    impaye DECIMAL(18,2),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_fournisseurs_nom ON fournisseurs(nom);

-- Table des immobilisations
CREATE TABLE immobilisations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    designation VARCHAR(255) NOT NULL,
    valeur DECIMAL(18,2) NOT NULL,
    annee INT,
    localisation VARCHAR(255),
    secteur VARCHAR(255),
    observation TEXT,
    fournisseur_id INTEGER REFERENCES fournisseurs(id),
    date_achat DATE,
    duree_vie INT,
    statut VARCHAR(50),
    code_barres VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr'
);

CREATE INDEX idx_immobilisations_designation ON immobilisations(designation);

-- Table des inventaires
CREATE TABLE inventaires (
    id SERIAL PRIMARY KEY,
    designation VARCHAR(255) NOT NULL,
    categorie VARCHAR(100),
    quantite INT,
    unite VARCHAR(50),
    emplacement VARCHAR(255),
    date_entree DATE,
    date_sortie DATE,
    valeur DECIMAL(18,2),
    statut VARCHAR(50),
    responsable VARCHAR(255),
    code_barres VARCHAR(100),
    type_code_barres VARCHAR(50),
    photo VARCHAR(255),
    historique TEXT,
    ecart DECIMAL(8,2),
    langue VARCHAR(10) DEFAULT 'fr'
);

CREATE INDEX idx_inventaires_designation ON inventaires(designation);

-- Table des documents (multimodules, multilingue)
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    module_lie VARCHAR(100),
    type_doc VARCHAR(100),
    date_doc DATE,
    objet VARCHAR(255),
    reference VARCHAR(255),
    fichier VARCHAR(255),
    code_barres VARCHAR(100),
    observation TEXT,
    synchronise BOOLEAN DEFAULT FALSE,
    utilisateur VARCHAR(255),
    langue VARCHAR(10) DEFAULT 'fr'
);

CREATE INDEX idx_documents_module ON documents(module_lie);

-- Table des réformes
CREATE TABLE reformes (
    id SERIAL PRIMARY KEY,
    immobilisation_id INTEGER REFERENCES immobilisations(id),
    date_reforme DATE,
    motif TEXT,
    pv_reforme VARCHAR(255),
    commission TEXT,
    statut VARCHAR(50),
    observation TEXT
);

-- Table des logs d'audit
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    utilisateur VARCHAR(255),
    action VARCHAR(255),
    table_cible VARCHAR(100),
    id_cible INTEGER,
    date_action TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details TEXT
);

-- Table de paramétrage multilingue
CREATE TABLE traductions (
    id SERIAL PRIMARY KEY,
    table_cible VARCHAR(100),
    colonne VARCHAR(100),
    id_ligne INTEGER,
    langue VARCHAR(10),
    valeur TEXT
);

CREATE INDEX idx_traductions ON traductions(table_cible, colonne, id_ligne, langue);

-- Table de synchronisation externe (interopérabilité)
CREATE TABLE synchronisations (
    id SERIAL PRIMARY KEY,
    table_cible VARCHAR(100),
    id_ligne INTEGER,
    systeme_externe VARCHAR(100),
    date_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    statut VARCHAR(50),
    details TEXT
);

-- Table générique pour import/export (interopérabilité)
CREATE TABLE imports_exports (
    id SERIAL PRIMARY KEY,
    type_operation VARCHAR(50),
    format VARCHAR(50),
    fichier VARCHAR(255),
    date_operation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50),
    details TEXT
);

-- Table pour stocker les fichiers (photos, documents, dossiers, tout format)
CREATE TABLE fichiers (
    id SERIAL PRIMARY KEY,
    nom_original VARCHAR(255) NOT NULL,
    chemin_stockage VARCHAR(500) NOT NULL,
    type_mime VARCHAR(100),
    taille BIGINT,
    extension VARCHAR(20),
    date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    module_lie VARCHAR(100),
    id_ligne INTEGER,
    description TEXT
);

CREATE INDEX idx_fichiers_module ON fichiers(module_lie, id_ligne);

-- Table pour journaliser les analyses et sauvegardes automatiques
CREATE TABLE analyses_sauvegardes (
    id SERIAL PRIMARY KEY,
    table_cible VARCHAR(100),
    id_ligne INTEGER,
    type_operation VARCHAR(50), -- analyse, sauvegarde, synchronisation
    date_operation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    resultat TEXT,
    details TEXT
);

CREATE INDEX idx_analyses_sauvegardes_table ON analyses_sauvegardes(table_cible, id_ligne);

-- Table pour référencer des sources externes (open data, API, etc.)
CREATE TABLE sources_externes (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    type_source VARCHAR(100), -- API, CSV, Web, etc.
    url TEXT,
    description TEXT,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    actif BOOLEAN DEFAULT TRUE
);

-- Table pour journaliser les imports/recherches internet
CREATE TABLE recherches_internet (
    id SERIAL PRIMARY KEY,
    source_id INTEGER REFERENCES sources_externes(id),
    table_cible VARCHAR(100),
    id_ligne INTEGER,
    date_recherche TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resultat TEXT,
    utilisateur VARCHAR(255)
);

-- Module d'automatisation (tâches planifiées, synchronisation, enrichissement)
CREATE TABLE automatisations (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(255),
    type_tache VARCHAR(100), -- import, export, enrichissement, analyse, etc.
    frequence VARCHAR(50), -- ex: 'daily', 'hourly'
    derniere_execution TIMESTAMP,
    statut VARCHAR(50),
    details TEXT
);

-- Table pour modules/plugins additionnels (extensibilité)
CREATE TABLE modules (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50),
    auteur VARCHAR(255),
    actif BOOLEAN DEFAULT TRUE,
    date_install TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour accélérer les recherches sur les modules et automatisations
CREATE INDEX idx_modules_nom ON modules(nom);
CREATE INDEX idx_automatisations_type ON automatisations(type_tache);

-- Index pour performance sur les grandes tables
CREATE INDEX idx_immobilisations_code_barres ON immobilisations(code_barres);
CREATE INDEX idx_inventaires_code_barres ON inventaires(code_barres);
CREATE INDEX idx_documents_code_barres ON documents(code_barres);

-- Table des paramètres globaux du logiciel
CREATE TABLE parametres_globaux (
    id SERIAL PRIMARY KEY,
    cle VARCHAR(100) NOT NULL UNIQUE,
    valeur TEXT,
    description TEXT,
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table pour journaliser toutes les synchronisations globales
CREATE TABLE synchronisations_globales (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    type_sync VARCHAR(50), -- ex: auto, manuel, planifiée
    date_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50),
    details TEXT
);

-- Table pour journaliser toutes les sauvegardes globales
CREATE TABLE sauvegardes_globales (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    type_sauvegarde VARCHAR(50), -- auto, manuelle, planifiée
    date_sauvegarde TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    chemin_fichier VARCHAR(500),
    statut VARCHAR(50),
    details TEXT
);

-- Table pour l'historique des mises à jour du logiciel
CREATE TABLE mises_a_jour (
    id SERIAL PRIMARY KEY,
    version VARCHAR(50) NOT NULL,
    description TEXT,
    date_mise_a_jour TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50)
);

-- Table centrale pour import/export multi-modules
CREATE TABLE import_export_global (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    type_operation VARCHAR(50), -- import, export
    format VARCHAR(50),
    fichier VARCHAR(255),
    date_operation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50),
    details TEXT
);

CREATE INDEX idx_import_export_global_module ON import_export_global(module, type_operation);

-- Table pour journaliser les actualisations automatiques
CREATE TABLE actualisations_automatiques (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    type_actualisation VARCHAR(50), -- ex: données, paramètres, structure
    date_actualisation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50),
    details TEXT
);

-- Table des méthodes et paramètres du logiciel (multi-langues, multi-logiciels)
CREATE TABLE parametres_methods (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    nom_parametre VARCHAR(100) NOT NULL,
    valeur TEXT,
    type_parametre VARCHAR(50), -- string, int, bool, json, etc.
    logiciel VARCHAR(100), -- ex: "GestImmob", "ERP", "Mobile", etc.
    langue VARCHAR(10) DEFAULT 'fr',
    description TEXT,
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_parametres_methods_module ON parametres_methods(module, logiciel, langue);

-- Table des méthodes/fonctionnalités disponibles (multi-langues, multi-logiciels)
CREATE TABLE methodes_fonctionnalites (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    nom_methode VARCHAR(100) NOT NULL,
    type_methode VARCHAR(50), -- CRUD, export, import, sync, analyse, etc.
    logiciel VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    description TEXT,
    actif BOOLEAN DEFAULT TRUE,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_methodes_fonctionnalites_module ON methodes_fonctionnalites(module, logiciel, langue);

-- Table pour la documentation multilingue des modules et paramètres
CREATE TABLE documentation_modules (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    titre VARCHAR(255),
    contenu TEXT,
    type_doc VARCHAR(50), -- manuel, aide, API, etc.
    date_maj TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_documentation_modules ON documentation_modules(module, langue, type_doc);

-- Table pour la gestion des langues supportées par le logiciel
CREATE TABLE langues_supportees (
    id SERIAL PRIMARY KEY,
    code VARCHAR(10) NOT NULL UNIQUE, -- ex: fr, en, ar, es, de, etc.
    nom VARCHAR(100) NOT NULL,
    actif BOOLEAN DEFAULT TRUE,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table pour la gestion des logiciels/plateformes interopérables
CREATE TABLE logiciels_supportes (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(100) NOT NULL UNIQUE,
    version VARCHAR(50),
    type VARCHAR(50), -- Desktop, Web, Mobile, API, etc.
    description TEXT,
    actif BOOLEAN DEFAULT TRUE,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table pour journaliser les synchronisations inter-logiciels
CREATE TABLE sync_interlogiciels (
    id SERIAL PRIMARY KEY,
    logiciel_source VARCHAR(100),
    logiciel_cible VARCHAR(100),
    module VARCHAR(100),
    type_sync VARCHAR(50),
    date_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50),
    details TEXT
);

CREATE INDEX idx_sync_interlogiciels ON sync_interlogiciels(logiciel_source, logiciel_cible, module);

-- Table pour journaliser les erreurs multi-langues et multi-logiciels
CREATE TABLE erreurs_logs (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    logiciel VARCHAR(100),
    langue VARCHAR(10),
    code_erreur VARCHAR(50),
    message TEXT,
    date_erreur TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    details TEXT
);

CREATE INDEX idx_erreurs_logs ON erreurs_logs(module, logiciel, langue, code_erreur);

-- Table pour la gestion des templates d'import/export multi-langues et multi-logiciels
CREATE TABLE templates_import_export (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    logiciel VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    type_template VARCHAR(50), -- import, export
    format VARCHAR(50), -- CSV, XLSX, JSON, XML, etc.
    contenu TEXT,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_templates_import_export ON templates_import_export(module, logiciel, langue, type_template);

-- Table pour la gestion des notifications multi-langues et multi-logiciels
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    logiciel VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    type_notification VARCHAR(50), -- info, alerte, erreur, etc.
    titre VARCHAR(255),
    message TEXT,
    utilisateur VARCHAR(255),
    date_notification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    lu BOOLEAN DEFAULT FALSE
);

CREATE INDEX idx_notifications ON notifications(module, logiciel, langue, type_notification);

-- Table pour la gestion des utilisateurs multi-logiciels et multi-langues
CREATE TABLE utilisateurs_multi (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    logiciel VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    role VARCHAR(50),
    email VARCHAR(255),
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_utilisateurs_multi ON utilisateurs_multi(username, logiciel, langue);

-- Table pour la gestion des droits d'accès multi-logiciels et multi-langues
CREATE TABLE droits_acces_multi (
    id SERIAL PRIMARY KEY,
    utilisateur_id INTEGER REFERENCES utilisateurs_multi(id),
    module VARCHAR(100),
    logiciel VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    droit VARCHAR(50), -- lecture, écriture, admin, etc.
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_droits_acces_multi ON droits_acces_multi(utilisateur_id, module, logiciel, langue, droit);

-- Table pour le suivi du matériel de travaux
CREATE TABLE materiel_travaux (
    id SERIAL PRIMARY KEY,
    designation VARCHAR(255) NOT NULL,
    categorie VARCHAR(100),
    marque VARCHAR(100),
    modele VARCHAR(100),
    num_serie VARCHAR(100),
    date_acquisition DATE,
    valeur DECIMAL(18,2),
    statut VARCHAR(50),
    emplacement VARCHAR(255),
    affectation VARCHAR(255),
    amortissement DECIMAL(18,2),
    observation TEXT,
    photo VARCHAR(255),
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_materiel_travaux_designation ON materiel_travaux(designation);

-- Table pour le suivi des travaux et projets
CREATE TABLE suivi_travaux (
    id SERIAL PRIMARY KEY,
    nom_projet VARCHAR(255) NOT NULL,
    type_travaux VARCHAR(100),
    responsable VARCHAR(255),
    date_debut DATE,
    date_fin DATE,
    statut VARCHAR(50),
    cout_estime DECIMAL(18,2),
    cout_reel DECIMAL(18,2),
    valeur_ajoutee DECIMAL(18,2),
    duree INT,
    amortissement DECIMAL(18,2),
    observation TEXT,
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_suivi_travaux_nom_projet ON suivi_travaux(nom_projet);

-- Table pour le suivi des animaux (enrichie)
CREATE TABLE suivi_animaux (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    espece VARCHAR(100),
    race VARCHAR(100),
    sexe VARCHAR(20),
    date_naissance DATE,
    valeur DECIMAL(18,2),
    statut VARCHAR(50),
    dernier_controle DATE,
    observations_medicales TEXT,
    localisation VARCHAR(255),
    deplacements TEXT,
    pays VARCHAR(100),
    parc VARCHAR(100),
    photo VARCHAR(255),
    observation TEXT,
    etat_sante VARCHAR(50),
    veterinaire VARCHAR(255),
    vaccins TEXT,
    medicaments TEXT,
    documents_medicaux TEXT,
    valeur_comptable DECIMAL(18,2),
    amortissement DECIMAL(18,2),
    cause_mortalite TEXT,
    consommation_mensuelle DECIMAL(18,2),
    consommation_annuelle DECIMAL(18,2),
    unite_conso VARCHAR(20),
    duree_vie INT,
    regime VARCHAR(50),
    poids DECIMAL(18,2),
    infos_internet TEXT,
    plateforme_partage VARCHAR(100),
    diagnostic_partage TEXT,
    parc_origine VARCHAR(100),
    pays_origine VARCHAR(100),
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_suivi_animaux_nom ON suivi_animaux(nom);

-- Table de configuration globale et paramètres dynamiques (optimisation, IA, Big Data, multilingue, sécurité, synchronisation)
CREATE TABLE configuration_globale (
    id SERIAL PRIMARY KEY,
    cle VARCHAR(100) NOT NULL UNIQUE,
    valeur TEXT,
    type_parametre VARCHAR(50), -- string, int, bool, json, etc.
    description TEXT,
    langue VARCHAR(10) DEFAULT 'fr',
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_configuration_globale_cle ON configuration_globale(cle);

-- Table pour journaliser les synchronisations, enrichissements IA, Big Data, multilingue, cloud
CREATE TABLE journal_synchronisation (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    type_sync VARCHAR(50), -- auto, manuel, cloud, IA, BigData, etc.
    date_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    utilisateur VARCHAR(255),
    statut VARCHAR(50),
    details TEXT,
    langue VARCHAR(10) DEFAULT 'fr'
);

CREATE INDEX idx_journal_synchronisation_module ON journal_synchronisation(module, type_sync, langue);

-- Table pour la gestion des modèles IA et multilingues utilisés
CREATE TABLE modeles_ia (
    id SERIAL PRIMARY KEY,
    nom_modele VARCHAR(100) NOT NULL,
    version VARCHAR(50),
    fournisseur VARCHAR(100),
    langue VARCHAR(10) DEFAULT 'fr',
    type_modele VARCHAR(50), -- DeepSeek, OpenAI, GoogleGemini, etc.
    actif BOOLEAN DEFAULT TRUE,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

CREATE INDEX idx_modeles_ia_nom ON modeles_ia(nom_modele, langue);

-- Table pour la gestion des paramètres de performance, cache, pool, etc.
CREATE TABLE performance_parametres (
    id SERIAL PRIMARY KEY,
    cle VARCHAR(100) NOT NULL UNIQUE,
    valeur TEXT,
    type_parametre VARCHAR(50),
    description TEXT,
    date_modif TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_performance_parametres_cle ON performance_parametres(cle);

-- Table pour la gestion des langues et traductions avancées (optimisation multilingue)
CREATE TABLE langues_traductions (
    id SERIAL PRIMARY KEY,
    code_langue VARCHAR(10) NOT NULL,
    nom_langue VARCHAR(100) NOT NULL,
    actif BOOLEAN DEFAULT TRUE,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

CREATE INDEX idx_langues_traductions_code ON langues_traductions(code_langue);

-- Table pour la gestion des logs de sécurité, audit, conformité (RGPD, etc.)
CREATE TABLE logs_securite (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    action VARCHAR(255),
    utilisateur VARCHAR(255),
    date_action TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    niveau VARCHAR(50), -- info, warning, error, audit, etc.
    details TEXT
);

CREATE INDEX idx_logs_securite_module ON logs_securite(module, niveau);

-- Table pour la gestion des connexions cloud, API, interopérabilité
CREATE TABLE connexions_externes (
    id SERIAL PRIMARY KEY,
    type_connexion VARCHAR(50), -- cloud, api, sso, etc.
    fournisseur VARCHAR(100),
    url TEXT,
    cle_api VARCHAR(255),
    actif BOOLEAN DEFAULT TRUE,
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

CREATE INDEX idx_connexions_externes_type ON connexions_externes(type_connexion, fournisseur);

-- Table pour la gestion des tâches planifiées, automatisations, triggers IA/Big Data
CREATE TABLE taches_planifiees (
    id SERIAL PRIMARY KEY,
    nom_tache VARCHAR(255),
    module VARCHAR(100),
    type_tache VARCHAR(100), -- sync, analyse, enrichissement, sauvegarde, etc.
    frequence VARCHAR(50), -- ex: 'daily', 'hourly'
    derniere_execution TIMESTAMP,
    prochaine_execution TIMESTAMP,
    statut VARCHAR(50),
    details TEXT
);

CREATE INDEX idx_taches_planifiees_module ON taches_planifiees(module, type_tache);

-- Table pour la gestion des notifications avancées (multilingue, IA, cloud)
CREATE TABLE notifications_avancees (
    id SERIAL PRIMARY KEY,
    module VARCHAR(100),
    type_notification VARCHAR(50), -- info, alerte, erreur, IA, etc.
    titre VARCHAR(255),
    message TEXT,
    utilisateur VARCHAR(255),
    langue VARCHAR(10) DEFAULT 'fr',
    date_notification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    lu BOOLEAN DEFAULT FALSE
);

CREATE INDEX idx_notifications_avancees_module ON notifications_avancees(module, type_notification, langue);