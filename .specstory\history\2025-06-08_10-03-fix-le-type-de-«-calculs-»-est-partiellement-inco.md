<!-- Generated by SpecStory -->

# /fix Le type de « calculs » est partiellement inco (2025-06-08 11:03:37)

_**User**_

/fix Le type de « calculs » est partiellement inconnu
  Le type de "calculs" est "list[Unknown]", Le type de « append » est partiellement inconnu
  Le type de "append" est "(object: Unknown, /) -> None"

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Déclare explicitement le type de `self.calculs` comme une liste de chaînes de caractères (`List[str]`) pour résoudre les avertissements de typage.

````python
# ...existing code...
        self.setLayout(layout)
        self.calculs: List[str] = []
# ...existing code...
````

---

