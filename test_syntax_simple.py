#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de syntaxe pour main.py
"""

import ast
import sys
import os

def test_syntax():
    """Test la syntaxe du fichier main.py"""
    print("🔍 Test de syntaxe de main.py...")
    
    try:
        # Lire le fichier
        with open('src/main.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parser le code
        ast.parse(source)
        print("✅ Syntaxe correcte - Aucune erreur de compilation")
        return True
        
    except SyntaxError as e:
        print(f"❌ Erreur de syntaxe à la ligne {e.lineno}: {e.msg}")
        print(f"   Texte: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_basic_import():
    """Test l'import de base du module"""
    print("\n🔍 Test d'import de base...")
    
    try:
        # Ajouter src au path
        src_path = os.path.join(os.path.dirname(__file__), 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        
        # Test import sans exécution
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "src/main.py")
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            print("✅ Module main peut être importé")
            return True
        else:
            print("❌ Impossible de créer le spec du module")
            return False
            
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def count_issues():
    """Compte les problèmes potentiels dans le code"""
    print("\n🔍 Analyse des problèmes potentiels...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        
        # Vérifier les lignes avec instructions multiples
        for i, line in enumerate(lines, 1):
            if '; ' in line and not line.strip().startswith('#'):
                # Ignorer les CSS et commentaires
                if 'color:' not in line and 'background-color:' not in line:
                    issues.append(f"Ligne {i}: Instructions multiples")
        
        # Vérifier les imports dans les fonctions
        in_function = False
        for i, line in enumerate(lines, 1):
            if line.strip().startswith('def '):
                in_function = True
            elif line.strip().startswith('class '):
                in_function = False
            elif in_function and 'import ' in line and not line.strip().startswith('#'):
                if 'type: ignore' not in line:
                    issues.append(f"Ligne {i}: Import dans fonction sans type: ignore")
        
        print(f"📊 Problèmes détectés: {len(issues)}")
        
        if len(issues) <= 3:  # Acceptable si <= 3 problèmes mineurs
            print("✅ Qualité du code acceptable")
            return True
        else:
            print("⚠️ Plusieurs problèmes détectés:")
            for issue in issues[:5]:  # Afficher les 5 premiers
                print(f"  - {issue}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test Simple des Corrections de main.py")
    print("=" * 45)
    
    tests = [
        ("Syntaxe", test_syntax),
        ("Import de base", test_basic_import),
        ("Analyse qualité", count_issues)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n📋 Résumé:")
    print("-" * 25)
    
    passed = 0
    for test_name, result in results:
        status = "✅ OK" if result else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Score: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Tous les tests sont passés!")
        print("✅ Le fichier main.py semble correct")
        return 0
    elif passed >= 2:
        print("✅ La plupart des tests sont passés")
        print("⚠️ Quelques problèmes mineurs subsistent")
        return 0
    else:
        print("❌ Des problèmes importants subsistent")
        return 1

if __name__ == "__main__":
    sys.exit(main())
