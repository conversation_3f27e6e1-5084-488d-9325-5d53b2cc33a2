#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST FINAL COMPLET - VALIDATION DE TOUS LES OUTILS
Vérifie que tous les moyens créés fonctionnent correctement
"""

import os
import sys
import ast
import subprocess
import platform
from pathlib import Path
from typing import Dict, List, Any

class FinalTestSuite:
    def __init__(self):
        self.system = platform.system()
        self.results: Dict[str, bool] = {}
        self.tools_found: List[str] = []
        self.errors: List[str] = []
        
    def test_main_py_syntax(self) -> bool:
        """Test syntaxe de main.py"""
        print("🔍 Test syntaxe main.py...")
        
        try:
            with open('src/main.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test AST
            ast.parse(content)
            
            # Compter les améliorations
            metrics = {
                'annotations': content.count('-> None:') + content.count('-> str:') + content.count('-> bool:'),
                'typed_vars': content.count(': str =') + content.count(': Any =') + content.count(': bool ='),
                'type_ignore': content.count('# type: ignore'),
                'error_handling': content.count('try:'),
                'logging': content.count('logging.')
            }
            
            total_score = sum(min(count, 10) for count in metrics.values())
            
            print(f"✅ Syntaxe parfaite - Score: {total_score}/50")
            return True
            
        except Exception as e:
            print(f"❌ Erreur syntaxe: {e}")
            self.errors.append(f"Syntaxe main.py: {e}")
            return False
    
    def test_python_tools(self) -> bool:
        """Test des outils Python"""
        print("\n🐍 Test outils Python...")
        
        python_tools = [
            'master_automation.py',
            'master_orchestrator.py', 
            'universal_launcher.py',
            'auto_fix_main.py',
            'ultimate_validator.py',
            'setup_tools.py',
            'diagnostic.py'
        ]
        
        found = 0
        for tool in python_tools:
            if Path(tool).exists():
                found += 1
                self.tools_found.append(f"Python: {tool}")
                
                # Test syntaxe
                try:
                    with open(tool, 'r', encoding='utf-8') as f:
                        ast.parse(f.read())
                except Exception as e:
                    self.errors.append(f"Erreur syntaxe {tool}: {e}")
        
        success = found >= len(python_tools) * 0.7
        print(f"{'✅' if success else '⚠️'} Outils Python: {found}/{len(python_tools)}")
        return success
    
    def test_java_tools(self) -> bool:
        """Test des outils Java"""
        print("\n☕ Test outils Java...")
        
        java_dir = Path('java_tools')
        if not java_dir.exists():
            print("⚠️ Répertoire Java non créé")
            return False
        
        java_file = java_dir / 'PythonCodeOptimizer.java'
        if java_file.exists():
            self.tools_found.append("Java: PythonCodeOptimizer.java")
            
            # Vérifier si compilé
            class_file = java_dir / 'PythonCodeOptimizer.class'
            if class_file.exists():
                self.tools_found.append("Java: PythonCodeOptimizer.class (compilé)")
                print("✅ Outils Java créés et compilés")
                return True
            else:
                print("⚠️ Outils Java créés mais non compilés")
                return True
        else:
            print("❌ Outils Java non créés")
            return False
    
    def test_sql_tools(self) -> bool:
        """Test des scripts SQL"""
        print("\n🗄️ Test scripts SQL...")
        
        sql_dir = Path('sql_tools')
        if not sql_dir.exists():
            print("⚠️ Répertoire SQL non créé")
            return False
        
        sql_scripts = [
            'optimize_database.sql',
            'backup_strategy.sql',
            'performance_monitoring.sql'
        ]
        
        found = 0
        for script in sql_scripts:
            script_path = sql_dir / script
            if script_path.exists():
                found += 1
                self.tools_found.append(f"SQL: {script}")
        
        success = found >= len(sql_scripts) * 0.7
        print(f"{'✅' if success else '⚠️'} Scripts SQL: {found}/{len(sql_scripts)}")
        return success
    
    def test_system_scripts(self) -> bool:
        """Test des scripts système"""
        print(f"\n⚡ Test scripts système ({self.system})...")
        
        if self.system == 'Windows':
            ps_dir = Path('powershell_tools')
            if ps_dir.exists():
                ps_scripts = ['AutoFix.ps1', 'SystemMonitor.ps1']
                found = sum(1 for script in ps_scripts if (ps_dir / script).exists())
                
                for script in ps_scripts:
                    if (ps_dir / script).exists():
                        self.tools_found.append(f"PowerShell: {script}")
                
                success = found >= len(ps_scripts) * 0.5
                print(f"{'✅' if success else '⚠️'} Scripts PowerShell: {found}/{len(ps_scripts)}")
                return success
        else:
            bash_dir = Path('bash_tools')
            if bash_dir.exists():
                bash_scripts = ['auto_fix.sh', 'system_monitor.sh']
                found = sum(1 for script in bash_scripts if (bash_dir / script).exists())
                
                for script in bash_scripts:
                    if (bash_dir / script).exists():
                        self.tools_found.append(f"Bash: {script}")
                
                success = found >= len(bash_scripts) * 0.5
                print(f"{'✅' if success else '⚠️'} Scripts Bash: {found}/{len(bash_scripts)}")
                return success
        
        print("⚠️ Scripts système non créés")
        return False
    
    def test_nodejs_tools(self) -> bool:
        """Test des outils Node.js"""
        print("\n🟢 Test outils Node.js...")
        
        nodejs_dir = Path('nodejs_tools')
        if not nodejs_dir.exists():
            print("⚠️ Répertoire Node.js non créé")
            return False
        
        package_json = nodejs_dir / 'package.json'
        monitor_js = nodejs_dir / 'tools' / 'monitor.js'
        
        found = 0
        if package_json.exists():
            found += 1
            self.tools_found.append("Node.js: package.json")
        
        if monitor_js.exists():
            found += 1
            self.tools_found.append("Node.js: monitor.js")
        
        success = found >= 1
        print(f"{'✅' if success else '⚠️'} Outils Node.js: {found}/2")
        return success
    
    def test_launch_scripts(self) -> bool:
        """Test des scripts de lancement"""
        print("\n🚀 Test scripts de lancement...")
        
        launch_scripts = ['LAUNCH_ALL.bat', 'LAUNCH_ALL.sh']
        found = 0
        
        for script in launch_scripts:
            if Path(script).exists():
                found += 1
                self.tools_found.append(f"Launcher: {script}")
        
        success = found >= 1
        print(f"{'✅' if success else '⚠️'} Scripts lancement: {found}/{len(launch_scripts)}")
        return success
    
    def test_documentation(self) -> bool:
        """Test de la documentation"""
        print("\n📚 Test documentation...")
        
        docs = [
            'README_TOOLS.md',
            'README_COMPLETE_AUTOMATION.md',
            'requirements.txt'
        ]
        
        found = 0
        for doc in docs:
            if Path(doc).exists():
                found += 1
                self.tools_found.append(f"Doc: {doc}")
        
        success = found >= len(docs) * 0.7
        print(f"{'✅' if success else '⚠️'} Documentation: {found}/{len(docs)}")
        return success
    
    def test_dependencies(self) -> bool:
        """Test des dépendances critiques"""
        print("\n📦 Test dépendances critiques...")
        
        critical_deps = ['PySide6', 'sqlite3', 'cryptography']
        available = 0
        
        for dep in critical_deps:
            try:
                __import__(dep)
                available += 1
            except ImportError:
                self.errors.append(f"Dépendance manquante: {dep}")
        
        success = available >= len(critical_deps) * 0.7
        print(f"{'✅' if success else '⚠️'} Dépendances: {available}/{len(critical_deps)}")
        return success
    
    def run_complete_test(self) -> bool:
        """Exécute tous les tests"""
        print("🧪 TEST FINAL COMPLET - TOUS LES OUTILS")
        print("=" * 50)
        
        tests = [
            ("Syntaxe main.py", self.test_main_py_syntax),
            ("Outils Python", self.test_python_tools),
            ("Outils Java", self.test_java_tools),
            ("Scripts SQL", self.test_sql_tools),
            ("Scripts système", self.test_system_scripts),
            ("Outils Node.js", self.test_nodejs_tools),
            ("Scripts lancement", self.test_launch_scripts),
            ("Documentation", self.test_documentation),
            ("Dépendances", self.test_dependencies)
        ]
        
        passed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    self.results[test_name] = True
                else:
                    self.results[test_name] = False
            except Exception as e:
                print(f"❌ Erreur dans {test_name}: {e}")
                self.results[test_name] = False
                self.errors.append(f"Test {test_name}: {e}")
        
        # Résumé final
        print("\n" + "="*50)
        print("📋 RÉSUMÉ DU TEST FINAL")
        print("="*50)
        
        print(f"✅ Tests réussis: {passed}/{len(tests)}")
        print(f"🔧 Outils trouvés: {len(self.tools_found)}")
        print(f"❌ Erreurs: {len(self.errors)}")
        
        if self.tools_found:
            print("\n🔧 OUTILS DISPONIBLES:")
            for i, tool in enumerate(self.tools_found, 1):
                print(f"{i:2d}. {tool}")
        
        if self.errors:
            print("\n❌ ERREURS:")
            for i, error in enumerate(self.errors, 1):
                print(f"{i:2d}. {error}")
        
        success_rate = (passed / len(tests)) * 100
        
        if success_rate >= 90:
            print("\n🎉 PARFAIT ! TOUS LES OUTILS FONCTIONNENT")
            print("✅ Automatisation complète réussie")
            print("🚀 Système prêt pour la production")
            return True
        elif success_rate >= 75:
            print("\n✅ EXCELLENT ! La plupart des outils fonctionnent")
            print("⚠️ Quelques outils optionnels manquants")
            return True
        else:
            print("\n⚠️ Système partiellement fonctionnel")
            print("📝 Vérifiez les erreurs ci-dessus")
            return False

def main():
    """Fonction principale"""
    if not Path('src/main.py').exists():
        print("❌ Fichier src/main.py non trouvé")
        return 1
    
    test_suite = FinalTestSuite()
    success = test_suite.run_complete_test()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
