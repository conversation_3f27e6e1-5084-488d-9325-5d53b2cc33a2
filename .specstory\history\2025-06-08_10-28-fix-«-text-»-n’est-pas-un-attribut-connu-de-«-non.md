<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:28:44)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Corriger l'accès à l'attribut `.text()` sur un objet potentiellement `None` en utilisant une fonction utilitaire pour éviter l'erreur.

````python
    def traiter_ecarts_automatique(self):
        """
        Analyse automatique des écarts physiques/théoriques après chaque inventaire.
        Affiche les différences, repositionne les lignes concernées et enrichit la rubrique des écarts.
        """
        def safe_text(item):
            return item.text() if item is not None else ""
        for row in range(self.table.rowCount()):
            quantite = safe_text(self.table.item(row, 3))
            ecart = safe_text(self.table.item(row, 17))
            try:
                quantite_val = float(quantite)
                ecart_val = float(ecart)
                # ...existing code...
````

---

