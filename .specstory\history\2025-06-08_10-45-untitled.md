<!-- Generated by SpecStory -->

# Untitled (2025-06-08 11:45:27)

_**User**_

/fix Le type de « figure » est partiellement inconnu
  Le type de "figure" est "(num: int | str | Figure | SubFigure | None = None, figsize: Buffer | _SupportsArray[dtype[Any]] | _NestedSequence[_SupportsArray[dtype[Any]]] | bool | int | float | complex | str | bytes | _NestedSequence[bool | int | float | complex | str | bytes] | None = None, dpi: float | None = None, *, facecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, edgecolor: tuple[float, float, float] | str | tuple[float, float, float, float] | tuple[tuple[float, float, float] | str, float] | tuple[tuple[float, float, float, float], float] | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs: Unknown) -> Figure"

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

**Résumé du correctif :**  
Pour résoudre l'avertissement sur le type de `figure` de matplotlib, il suffit d'importer explicitement la classe `Figure` depuis `matplotlib.figure`. Cela permet à l'analyseur de type (Pylance) de reconnaître correctement le type retourné par `plt.figure`.  
Aucune modification fonctionnelle n'est nécessaire, seulement l'ajout de l'import.

````python
# ...existing code...
    def afficher_charts(self):
        try:
            import matplotlib.pyplot as plt
            from matplotlib.figure import Figure  # Ajout pour l'annotation de type correcte
            from collections import defaultdict
            from typing import DefaultDict
            # ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))
                # ...existing code...
            if stats_statut:
                plt.figure(figsize=(8, 4))
                # ...existing code...
        except Exception as e:
            # ...existing code...
# ...existing code...
````

---

