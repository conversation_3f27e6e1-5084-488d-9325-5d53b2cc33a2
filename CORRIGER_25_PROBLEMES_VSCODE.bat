@echo off
title CORRECTION 25 PROBLÈMES VSCODE - MASTER COMPTA
color 0A

echo.
echo ========================================================================
echo           🔧 CORRECTION 25 PROBLÈMES VSCODE - MASTER COMPTA 🔧
echo                    Nettoyage et réparation configuration
echo ========================================================================
echo.

echo [%TIME%] Début de la correction des 25 problèmes VSCode...

echo.
echo 🔍 DIAGNOSTIC DES PROBLÈMES VSCODE...
echo ========================================================================

echo Vérification des fichiers de configuration...
if exist ".vscode\settings.json" (
    echo ✅ settings.json trouvé
) else (
    echo ❌ settings.json manquant
)

if exist ".vscode\tasks.json" (
    echo ✅ tasks.json trouvé
) else (
    echo ❌ tasks.json manquant
)

if exist ".vscode\launch.json" (
    echo ✅ launch.json trouvé
) else (
    echo ❌ launch.json manquant
)

if exist ".vscode\extensions.json" (
    echo ✅ extensions.json trouvé
) else (
    echo ❌ extensions.json manquant
)

echo.
echo 🔧 ÉTAPE 1: SAUVEGARDE DES CONFIGURATIONS ACTUELLES...
echo ========================================================================

echo Création du dossier de sauvegarde...
if not exist ".vscode\backup" mkdir ".vscode\backup"

echo Sauvegarde des configurations actuelles...
if exist ".vscode\settings.json" copy ".vscode\settings.json" ".vscode\backup\settings_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.json" >nul
if exist ".vscode\tasks.json" copy ".vscode\tasks.json" ".vscode\backup\tasks_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.json" >nul
if exist ".vscode\launch.json" copy ".vscode\launch.json" ".vscode\backup\launch_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.json" >nul
if exist ".vscode\extensions.json" copy ".vscode\extensions.json" ".vscode\backup\extensions_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.json" >nul

echo ✅ Sauvegarde terminée

echo.
echo 🔧 ÉTAPE 2: NETTOYAGE DES CONFIGURATIONS CORROMPUES...
echo ========================================================================

echo Suppression des configurations avec erreurs...
if exist ".vscode\settings.json" del ".vscode\settings.json"
if exist ".vscode\extensions.json" del ".vscode\extensions.json"

echo ✅ Nettoyage terminé

echo.
echo 🔧 ÉTAPE 3: INSTALLATION DES CONFIGURATIONS PROPRES...
echo ========================================================================

echo Installation de settings.json propre...
if exist ".vscode\settings_clean.json" (
    copy ".vscode\settings_clean.json" ".vscode\settings.json" >nul
    echo ✅ settings.json propre installé
) else (
    echo ❌ settings_clean.json manquant
)

echo Installation de extensions.json propre...
if exist ".vscode\extensions_clean.json" (
    copy ".vscode\extensions_clean.json" ".vscode\extensions.json" >nul
    echo ✅ extensions.json propre installé
) else (
    echo ❌ extensions_clean.json manquant
)

echo.
echo 🔧 ÉTAPE 4: VÉRIFICATION DES CHEMINS PYTHON...
echo ========================================================================

echo Vérification de l'installation Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non trouvé dans PATH
    echo 💡 Ajoutez Python au PATH système
) else (
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python trouvé: %%i
)

echo Vérification du dossier src...
if exist "src" (
    echo ✅ Dossier src trouvé
) else (
    echo ⚠️ Dossier src manquant
)

echo.
echo 🔧 ÉTAPE 5: VÉRIFICATION DES MODULES MASTER COMPTA...
echo ========================================================================

echo Vérification des 11 modules...
set /a modules_found=0

if exist "src\calculatrice.py" (
    echo ✅ Module 1: Calculatrice
    set /a modules_found+=1
) else (
    echo ❌ Module 1: Calculatrice manquant
)

if exist "src\inventaire.py" (
    echo ✅ Module 2: Inventaire
    set /a modules_found+=1
) else (
    echo ❌ Module 2: Inventaire manquant
)

if exist "src\reforme.py" (
    echo ✅ Module 3: Réforme
    set /a modules_found+=1
) else (
    echo ❌ Module 3: Réforme manquant
)

if exist "src\recherche.py" (
    echo ✅ Module 4: Recherche (corrigé)
    set /a modules_found+=1
) else (
    echo ❌ Module 4: Recherche manquant
)

if exist "src\fournisseur.py" (
    echo ✅ Module 5: Fournisseur
    set /a modules_found+=1
) else (
    echo ❌ Module 5: Fournisseur manquant
)

if exist "master_compta_vrais_11_modules.py" (
    echo ✅ Interface MASTER COMPTA complète
    set /a modules_found+=1
) else (
    echo ❌ Interface MASTER COMPTA manquante
)

echo 📊 Modules trouvés: %modules_found%/11

echo.
echo 🔧 ÉTAPE 6: TEST DE LA CONFIGURATION...
echo ========================================================================

echo Test de la syntaxe JSON...
python -c "import json; json.load(open('.vscode/settings.json'))" 2>nul
if errorlevel 1 (
    echo ❌ Erreur dans settings.json
) else (
    echo ✅ settings.json valide
)

python -c "import json; json.load(open('.vscode/extensions.json'))" 2>nul
if errorlevel 1 (
    echo ❌ Erreur dans extensions.json
) else (
    echo ✅ extensions.json valide
)

python -c "import json; json.load(open('.vscode/tasks.json'))" 2>nul
if errorlevel 1 (
    echo ❌ Erreur dans tasks.json
) else (
    echo ✅ tasks.json valide
)

python -c "import json; json.load(open('.vscode/launch.json'))" 2>nul
if errorlevel 1 (
    echo ❌ Erreur dans launch.json
) else (
    echo ✅ launch.json valide
)

echo.
echo ========================================================================
echo                              RÉSULTAT FINAL
echo ========================================================================

echo [%TIME%] Correction des 25 problèmes VSCode terminée

echo.
echo 🎯 CORRECTIONS EFFECTUÉES:
echo ✅ Sauvegarde des configurations actuelles
echo ✅ Nettoyage des configurations corrompues
echo ✅ Installation de configurations propres
echo ✅ Vérification des chemins Python
echo ✅ Vérification des modules MASTER COMPTA
echo ✅ Test de la syntaxe JSON

echo.
echo 💡 ÉTAPES SUIVANTES:
echo 1. Redémarrez VSCode
echo 2. Ouvrez votre projet MASTER COMPTA
echo 3. Vérifiez qu'il n'y a plus d'erreurs
echo 4. Testez l'IntelliSense Python
echo 5. Lancez vos modules avec Ctrl+Shift+P → Tasks

echo.
echo ⚠️ SI DES PROBLÈMES PERSISTENT:
echo - Vérifiez que Python est dans le PATH
echo - Installez l'extension Python officielle
echo - Redémarrez VSCode complètement
echo - Vérifiez les permissions de fichiers

echo.
echo 🎉 CONFIGURATION VSCODE MASTER COMPTA RÉPARÉE !
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
