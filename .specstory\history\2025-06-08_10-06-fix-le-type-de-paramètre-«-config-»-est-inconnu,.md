<!-- Generated by SpecStory -->

# /fix Le type de paramètre « config » est inconnu,  (2025-06-08 11:06:01)

_**User**_

/fix Le type de paramètre « config » est inconnu, L'annotation de type est manquante pour le paramètre "config"

---

_**Assistant**_

### [config.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/config.py)

Ajouter une annotation de type pour le paramètre `config` dans la fonction `save_config`. Ici, on utilise `dict[str, Any]` pour correspondre à la structure attendue.

````python
# ...existing code...
def save_config(config: dict[str, Any]):
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
# ...existing code...
````

---

