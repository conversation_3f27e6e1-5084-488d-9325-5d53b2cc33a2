<!-- Generated by SpecStory -->

# Untitled (2025-06-08 11:16:36)

_**User**_

/fix Désolé... Nous ne pouvons pas accéder à l’attribut « A3 » pour la classe « type[QPrinter] »
  L’attribut « A3 » est inconnu, Le type de « A3 » est inconnu, Le type d’argument est inconnu
  L’argument correspond au paramètre « size » dans la fonction « setPageSize »

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème vient du fait que dans PySide6, les constantes de format de page comme `A3`, `A4`, etc. ne sont pas des attributs directs de `QPrinter`, mais sont accessibles via l'énumération `QPageSize.PageSizeId`. Il faut donc utiliser `printer.setPageSize(QPageSize(QPageSize.PageSizeId.A3))` ou, plus simplement, utiliser `setPageSizeId` si disponible.

Voici la correction à apporter :

````python
# ...existing code...
from PySide6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PySide6.QtGui import QPageSize
# ...existing code...
    def imprimer(self):
        printer = self._get_printer()
        fmt = self.combo_format.currentText()
        # Paramètres avancés : format, orientation, etc.
        if fmt == "PDF":
            # Correction : utiliser QPrinter.OutputFormat.PdfFormat
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
            file, _ = QFileDialog.getSaveFileName(self, "Enregistrer PDF", "", "PDF (*.pdf)")
            if file:
                printer.setOutputFileName(file)
        elif fmt == "A3":
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.A3))
        elif fmt == "A4":
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.A4))
        elif fmt == "Lettre":
            printer.setPageSize(QPageSize(QPageSize.PageSizeId.Letter))
        elif fmt == "Paysage":
            printer.setOrientation(QPrinter.Orientation.Landscape)
        elif fmt == "Portrait":
            printer.setOrientation(QPrinter.Orientation.Portrait)
        dialog = QPrintDialog(printer, self)
        # ...existing code...
````

---

