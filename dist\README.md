# GestImmob - Logiciel de Gestion Immobilière Professionnel

![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-green.svg)
![License](https://img.shields.io/badge/license-Propriétaire-red.svg)

## 🎯 Vue d'Ensemble
GestImmob est un logiciel de gestion immobilière moderne, complet et performant avec interface fluide et fonctionnalités avancées. Conçu pour les professionnels de l'immobilier, il offre une solution complète pour la gestion des biens, des fournisseurs, de la comptabilité et bien plus encore.

## Features
- User authentication with secure login/logout functionality.
- Role-based access control for administrators and regular users.
- Secure storage of user passwords and sensitive information using encryption.
- Logging utility for monitoring and debugging.
- Configurable settings for database connections and security parameters.
- Input validation and protection against common vulnerabilities.

## Project Structure
```
gestion-software
├── src
│   ├── main.py                # Entry point of the application
│   ├── auth                   # Authentication related functionalities
│   │   ├── authentication.py   # User login/logout and session management
│   │   ├── encryption.py       # Functions for encrypting/decrypting data
│   │   └── user_management.py  # User account management
│   ├── database                # Database connection and models
│   │   ├── db_connection.py    # Database connection setup
│   │   ├── models.py           # Database models using ORM
│   │   └── migrations          # SQL commands for initializing the database
│   │       └── init.sql
│   ├── utils                   # Utility functions
│   │   ├── logger.py           # Logging utility
│   │   ├── config.py           # Application configuration settings
│   │   └── security.py         # Security-related functions
│   └── views                   # User interface views
│       ├── admin_view.py       # Admin interface methods
│       └── user_view.py        # User interface methods
├── requirements.txt            # Project dependencies
├── README.md                   # Project documentation
└── .gitignore                  # Files to ignore in version control
```

## Installation
1. Clone the repository:
   ```
   git clone <repository-url>
   cd gestion-software
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Initialize the database:
   - Run the SQL commands in `src/database/migrations/init.sql` to set up the database schema.

## Usage
- Run the application:
  ```
  python src/main.py
  ```

- Follow the prompts to log in or manage user accounts.

## Security
This application implements several security measures, including:
- Password encryption to protect user credentials.
- Input validation to prevent SQL injection and XSS attacks.
- Role-based access control to restrict functionalities based on user roles.

## Contributing
Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License
This project is licensed under the MIT License. See the LICENSE file for details.