#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌍 ROBOT IA - TRADUCTION TEMPS RÉEL UNIVERSELLE
Support 100+ langues, reconnaissance vocale, synthèse vocale
Créé par Augment Agent pour SamNord@110577
"""

import sys
import os
import json
import threading
import time
from datetime import datetime
import requests
import speech_recognition as sr
import pyttsx3
from googletrans import Translator
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

class TraductionTempsReel:
    """🌍 Système de traduction temps réel universel"""
    
    def __init__(self):
        self.translator = Translator()
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.tts_engine = pyttsx3.init()
        
        # Configuration
        self.config = {
            "langue_source": "auto",
            "langue_cible": "fr",
            "mode_vocal": True,
            "mode_continu": False,
            "vitesse_parole": 150,
            "volume": 0.8
        }
        
        # Langues supportées (100+)
        self.langues = {
            "auto": "Détection automatique",
            "fr": "Français", "en": "Anglais", "es": "<PERSON>spagnol", "de": "Allemand",
            "it": "Italien", "pt": "Portugais", "ru": "Russe", "zh": "Chinois",
            "ja": "Japonais", "ko": "Coréen", "ar": "Arabe", "hi": "Hindi",
            "tr": "Turc", "pl": "Polonais", "nl": "Néerlandais", "sv": "Suédois",
            "da": "Danois", "no": "Norvégien", "fi": "Finnois", "cs": "Tchèque",
            "hu": "Hongrois", "ro": "Roumain", "bg": "Bulgare", "hr": "Croate",
            "sk": "Slovaque", "sl": "Slovène", "et": "Estonien", "lv": "Letton",
            "lt": "Lituanien", "mt": "Maltais", "ga": "Irlandais", "cy": "Gallois",
            "eu": "Basque", "ca": "Catalan", "gl": "Galicien", "is": "Islandais",
            "mk": "Macédonien", "sq": "Albanais", "sr": "Serbe", "bs": "Bosniaque",
            "me": "Monténégrin", "uk": "Ukrainien", "be": "Biélorusse", "kk": "Kazakh",
            "ky": "Kirghize", "uz": "Ouzbek", "tg": "Tadjik", "mn": "Mongol",
            "ka": "Géorgien", "hy": "Arménien", "az": "Azéri", "he": "Hébreu",
            "fa": "Persan", "ur": "Ourdou", "bn": "Bengali", "ta": "Tamoul",
            "te": "Télougou", "ml": "Malayalam", "kn": "Kannada", "gu": "Gujarati",
            "pa": "Pendjabi", "ne": "Népalais", "si": "Cinghalais", "my": "Birman",
            "th": "Thaï", "lo": "Lao", "km": "Khmer", "vi": "Vietnamien",
            "id": "Indonésien", "ms": "Malais", "tl": "Tagalog", "haw": "Hawaïen",
            "mg": "Malgache", "sm": "Samoan", "to": "Tongien", "fj": "Fidjien",
            "mi": "Maori", "sw": "Swahili", "zu": "Zoulou", "xh": "Xhosa",
            "af": "Afrikaans", "st": "Sotho", "tn": "Tswana", "ss": "Swati",
            "ve": "Venda", "ts": "Tsonga", "nr": "Ndébélé", "nso": "Sotho du Nord",
            "am": "Amharique", "ti": "Tigrinya", "om": "Oromo", "so": "Somali",
            "rw": "Kinyarwanda", "rn": "Kirundi", "lg": "Luganda", "ak": "Akan",
            "tw": "Twi", "ee": "Ewe", "ha": "Haoussa", "ig": "Igbo", "yo": "Yoruba"
        }
        
        self.historique = []
        self.interface_active = False
        
        # Configuration TTS
        self._configurer_tts()
        
    def _configurer_tts(self):
        """Configuration synthèse vocale"""
        try:
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Préférer voix française si disponible
                for voice in voices:
                    if 'french' in voice.name.lower() or 'fr' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            self.tts_engine.setProperty('rate', self.config["vitesse_parole"])
            self.tts_engine.setProperty('volume', self.config["volume"])
            
        except Exception as e:
            print(f"⚠️ Erreur configuration TTS: {e}")
    
    def traduire_texte(self, texte, langue_source="auto", langue_cible="fr"):
        """Traduction texte simple"""
        try:
            if not texte.strip():
                return ""
            
            # Traduction avec Google Translate
            resultat = self.translator.translate(
                texte, 
                src=langue_source, 
                dest=langue_cible
            )
            
            traduction = {
                "texte_original": texte,
                "langue_detectee": resultat.src,
                "langue_cible": langue_cible,
                "traduction": resultat.text,
                "confiance": getattr(resultat, 'confidence', 1.0),
                "timestamp": datetime.now().isoformat()
            }
            
            # Ajout à l'historique
            self.historique.append(traduction)
            
            return traduction
            
        except Exception as e:
            print(f"❌ Erreur traduction: {e}")
            return {
                "texte_original": texte,
                "traduction": f"Erreur: {e}",
                "erreur": True
            }
    
    def ecouter_microphone(self, duree=5):
        """Écoute microphone et reconnaissance vocale"""
        try:
            print("🎤 Écoute en cours...")
            
            with self.microphone as source:
                # Ajustement bruit ambiant
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                # Écoute
                audio = self.recognizer.listen(source, timeout=duree, phrase_time_limit=duree)
            
            print("🔄 Reconnaissance vocale...")
            
            # Reconnaissance avec Google Speech
            texte = self.recognizer.recognize_google(
                audio, 
                language=self.config["langue_source"] if self.config["langue_source"] != "auto" else "fr-FR"
            )
            
            print(f"✅ Texte reconnu: {texte}")
            return texte
            
        except sr.WaitTimeoutError:
            print("⏰ Timeout - Aucun son détecté")
            return None
        except sr.UnknownValueError:
            print("❌ Impossible de comprendre l'audio")
            return None
        except sr.RequestError as e:
            print(f"❌ Erreur service reconnaissance: {e}")
            return None
        except Exception as e:
            print(f"❌ Erreur microphone: {e}")
            return None
    
    def parler_texte(self, texte, langue="fr"):
        """Synthèse vocale du texte"""
        try:
            if not texte.strip():
                return
            
            print(f"🔊 Synthèse vocale: {texte}")
            
            # Configuration voix selon langue
            self._configurer_voix_langue(langue)
            
            # Synthèse
            self.tts_engine.say(texte)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            print(f"❌ Erreur synthèse vocale: {e}")
    
    def _configurer_voix_langue(self, langue):
        """Configure la voix selon la langue"""
        try:
            voices = self.tts_engine.getProperty('voices')
            if not voices:
                return
            
            # Mapping langues vers voix
            langue_mapping = {
                "fr": ["french", "fr"],
                "en": ["english", "en"],
                "es": ["spanish", "es"],
                "de": ["german", "de"],
                "it": ["italian", "it"]
            }
            
            if langue in langue_mapping:
                for voice in voices:
                    for keyword in langue_mapping[langue]:
                        if keyword in voice.name.lower() or keyword in voice.id.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            return
            
        except Exception as e:
            print(f"⚠️ Erreur configuration voix: {e}")
    
    def traduction_vocale_temps_reel(self):
        """Traduction vocale en temps réel"""
        print("🎤 TRADUCTION VOCALE TEMPS RÉEL")
        print("=" * 40)
        print("🗣️ Parlez dans le microphone...")
        print("⏹️ Appuyez sur Ctrl+C pour arrêter")
        print()
        
        try:
            while True:
                # Écoute
                texte_original = self.ecouter_microphone(duree=10)
                
                if texte_original:
                    print(f"📝 Original: {texte_original}")
                    
                    # Traduction
                    traduction = self.traduire_texte(
                        texte_original,
                        self.config["langue_source"],
                        self.config["langue_cible"]
                    )
                    
                    if not traduction.get("erreur"):
                        print(f"🌍 Traduit ({traduction['langue_detectee']} → {traduction['langue_cible']}): {traduction['traduction']}")
                        
                        # Synthèse vocale de la traduction
                        if self.config["mode_vocal"]:
                            self.parler_texte(traduction['traduction'], self.config["langue_cible"])
                    
                    print("-" * 50)
                
                if not self.config["mode_continu"]:
                    break
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ Traduction vocale arrêtée")
    
    def interface_graphique(self):
        """Interface graphique de traduction"""
        if self.interface_active:
            return
        
        self.interface_active = True
        
        # Fenêtre principale
        self.root = tk.Tk()
        self.root.title("🌍 Traduction Temps Réel - SamNord@110577")
        self.root.geometry("800x600")
        self.root.configure(bg="#1a1a1a")
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Dark.TFrame', background='#1a1a1a')
        style.configure('Dark.TLabel', background='#1a1a1a', foreground='white')
        style.configure('Dark.TButton', background='#333333', foreground='white')
        
        self._creer_interface()
        
        # Démarrage interface
        self.root.protocol("WM_DELETE_WINDOW", self._fermer_interface)
        self.root.mainloop()
    
    def _creer_interface(self):
        """Création interface graphique"""
        # Frame principal
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Titre
        title_label = ttk.Label(
            main_frame, 
            text="🌍 TRADUCTION TEMPS RÉEL UNIVERSELLE",
            font=("Arial", 16, "bold"),
            style='Dark.TLabel'
        )
        title_label.pack(pady=(0, 20))
        
        # Frame langues
        lang_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        lang_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Langue source
        ttk.Label(lang_frame, text="🗣️ Langue source:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.combo_source = ttk.Combobox(lang_frame, values=list(self.langues.values()), width=15)
        self.combo_source.set("Détection automatique")
        self.combo_source.pack(side=tk.LEFT, padx=(5, 20))
        
        # Langue cible
        ttk.Label(lang_frame, text="🎯 Langue cible:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.combo_cible = ttk.Combobox(lang_frame, values=list(self.langues.values()), width=15)
        self.combo_cible.set("Français")
        self.combo_cible.pack(side=tk.LEFT, padx=5)
        
        # Frame texte
        text_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        text_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Texte source
        ttk.Label(text_frame, text="📝 Texte à traduire:", style='Dark.TLabel').pack(anchor=tk.W)
        self.text_source = scrolledtext.ScrolledText(
            text_frame, 
            height=8, 
            bg="#2a2a2a", 
            fg="white", 
            insertbackground="white"
        )
        self.text_source.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        # Texte traduit
        ttk.Label(text_frame, text="🌍 Traduction:", style='Dark.TLabel').pack(anchor=tk.W)
        self.text_cible = scrolledtext.ScrolledText(
            text_frame, 
            height=8, 
            bg="#2a2a2a", 
            fg="#00ff00", 
            insertbackground="white",
            state=tk.DISABLED
        )
        self.text_cible.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Frame boutons
        btn_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        btn_frame.pack(fill=tk.X, pady=10)
        
        # Boutons
        ttk.Button(btn_frame, text="🔄 Traduire", command=self._traduire_interface).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🎤 Écouter", command=self._ecouter_interface).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🔊 Parler", command=self._parler_interface).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗑️ Effacer", command=self._effacer_interface).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="💾 Sauvegarder", command=self._sauvegarder_interface).pack(side=tk.LEFT, padx=5)
        
        # Frame options
        opt_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        opt_frame.pack(fill=tk.X, pady=5)
        
        # Options
        self.var_vocal = tk.BooleanVar(value=True)
        ttk.Checkbutton(opt_frame, text="🔊 Mode vocal", variable=self.var_vocal, style='Dark.TLabel').pack(side=tk.LEFT, padx=10)
        
        self.var_continu = tk.BooleanVar(value=False)
        ttk.Checkbutton(opt_frame, text="🔄 Mode continu", variable=self.var_continu, style='Dark.TLabel').pack(side=tk.LEFT, padx=10)
    
    def _traduire_interface(self):
        """Traduction depuis interface"""
        try:
            texte = self.text_source.get("1.0", tk.END).strip()
            if not texte:
                return
            
            # Récupération langues
            langue_source = self._get_code_langue(self.combo_source.get())
            langue_cible = self._get_code_langue(self.combo_cible.get())
            
            # Traduction
            traduction = self.traduire_texte(texte, langue_source, langue_cible)
            
            # Affichage résultat
            self.text_cible.config(state=tk.NORMAL)
            self.text_cible.delete("1.0", tk.END)
            
            if not traduction.get("erreur"):
                self.text_cible.insert("1.0", traduction['traduction'])
                
                # Synthèse vocale si activée
                if self.var_vocal.get():
                    threading.Thread(
                        target=self.parler_texte,
                        args=(traduction['traduction'], langue_cible),
                        daemon=True
                    ).start()
            else:
                self.text_cible.insert("1.0", traduction['traduction'])
            
            self.text_cible.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur traduction: {e}")
    
    def _ecouter_interface(self):
        """Écoute microphone depuis interface"""
        try:
            # Écoute en thread séparé
            def ecouter():
                texte = self.ecouter_microphone(duree=10)
                if texte:
                    self.text_source.delete("1.0", tk.END)
                    self.text_source.insert("1.0", texte)
                    # Traduction automatique
                    self._traduire_interface()
            
            threading.Thread(target=ecouter, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur écoute: {e}")
    
    def _parler_interface(self):
        """Synthèse vocale depuis interface"""
        try:
            texte = self.text_cible.get("1.0", tk.END).strip()
            if texte:
                langue_cible = self._get_code_langue(self.combo_cible.get())
                threading.Thread(
                    target=self.parler_texte,
                    args=(texte, langue_cible),
                    daemon=True
                ).start()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur synthèse: {e}")
    
    def _effacer_interface(self):
        """Effacement textes"""
        self.text_source.delete("1.0", tk.END)
        self.text_cible.config(state=tk.NORMAL)
        self.text_cible.delete("1.0", tk.END)
        self.text_cible.config(state=tk.DISABLED)
    
    def _sauvegarder_interface(self):
        """Sauvegarde historique"""
        try:
            filename = f"traductions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.historique, f, indent=2, ensure_ascii=False)
            messagebox.showinfo("Succès", f"Historique sauvegardé: {filename}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur sauvegarde: {e}")
    
    def _get_code_langue(self, nom_langue):
        """Récupère code langue depuis nom"""
        for code, nom in self.langues.items():
            if nom == nom_langue:
                return code
        return "auto"
    
    def _fermer_interface(self):
        """Fermeture interface"""
        self.interface_active = False
        self.root.destroy()
    
    def installer_dependances(self):
        """Installation dépendances traduction"""
        print("📦 INSTALLATION DÉPENDANCES TRADUCTION")
        print("=" * 45)
        
        dependances = [
            "googletrans==4.0.0rc1",
            "SpeechRecognition",
            "pyttsx3",
            "pyaudio",
            "requests"
        ]
        
        for dep in dependances:
            try:
                print(f"📦 Installation {dep}...")
                import subprocess
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             capture_output=True, text=True, check=True)
                print(f"✅ {dep} installé")
            except subprocess.CalledProcessError as e:
                print(f"⚠️ Erreur installation {dep}: {e}")
        
        print("✅ Installation dépendances terminée !")

def main():
    """Fonction principale"""
    print("🌍 TRADUCTION TEMPS RÉEL UNIVERSELLE")
    print("=" * 45)
    print("👤 SamNord@110577")
    print("🗣️ Support 100+ langues")
    print("🎤 Reconnaissance vocale")
    print("🔊 Synthèse vocale")
    print()
    
    # Création instance
    traducteur = TraductionTempsReel()
    
    # Menu principal
    while True:
        print("\n🎯 MENU TRADUCTION:")
        print("1. 🌍 Interface graphique")
        print("2. 🎤 Traduction vocale temps réel")
        print("3. 📝 Traduction texte simple")
        print("4. 📦 Installer dépendances")
        print("5. ⚙️ Configuration")
        print("0. ❌ Quitter")
        
        choix = input("\n➤ Votre choix (0-5): ").strip()
        
        if choix == "1":
            traducteur.interface_graphique()
        elif choix == "2":
            traducteur.traduction_vocale_temps_reel()
        elif choix == "3":
            texte = input("📝 Texte à traduire: ")
            if texte:
                resultat = traducteur.traduire_texte(texte)
                if not resultat.get("erreur"):
                    print(f"🌍 Traduction: {resultat['traduction']}")
                    print(f"🔍 Langue détectée: {resultat['langue_detectee']}")
        elif choix == "4":
            traducteur.installer_dependances()
        elif choix == "5":
            print("⚙️ Configuration disponible dans interface graphique")
        elif choix == "0":
            print("👋 Au revoir !")
            break
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
