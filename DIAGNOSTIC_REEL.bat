@echo off
title DIAGNOSTIC REEL - Test d Execution Effective
color 0A

echo.
echo ========================================================================
echo                    DIAGNOSTIC REEL - TEST D EXECUTION EFFECTIVE
echo                   Identification des vrais problemes d execution
echo ========================================================================
echo.

REM Variables
set "PYTHON_CMD="
set "REAL_ERRORS=0"

echo [%TIME%] Demarrage du diagnostic reel...

REM Détection Python
echo.
echo [%TIME%] Detection de Python...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    echo ✅ Python detecte: py
    goto :python_found
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    echo ✅ Python detecte: python
    goto :python_found
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python3"
    echo ✅ Python detecte: python3
    goto :python_found
)

echo ❌ Python non trouve
goto :manual_diagnosis

:python_found
echo Python executable: %PYTHON_CMD%

REM Vérification du répertoire
echo.
echo [%TIME%] Verification du repertoire...
echo Repertoire: %CD%

if exist "src\main.py" (
    echo ✅ Structure projet detectee
) else (
    if exist "..\src\main.py" (
        cd ..
        echo Remonte au repertoire parent: %CD%
    )
)

REM Test d'exécution directe de l'application
echo.
echo ========================================================================
echo                           TEST D EXECUTION DIRECTE
echo ========================================================================
echo.

echo [%TIME%] Test d execution directe de l application...

if exist "test_execution_directe.py" (
    echo Execution du test d execution directe...
    %PYTHON_CMD% test_execution_directe.py
    if not errorlevel 1 (
        echo ✅ Test d execution directe: REUSSI
        echo ✅ L application peut etre executee
    ) else (
        echo ❌ Test d execution directe: ECHEC
        echo ❌ Des problemes empechent l execution
        set /a REAL_ERRORS+=1
    )
) else (
    echo Test manuel d execution...
    
    echo Tentative de lancement de l application...
    timeout /t 2 >nul
    
    start /wait /b %PYTHON_CMD% src\main.py
    if not errorlevel 1 (
        echo ✅ Application lancee avec succes
    ) else (
        echo ❌ Echec du lancement de l application
        set /a REAL_ERRORS+=1
    )
)

REM Diagnostic réel avec le script spécialisé
echo.
echo ========================================================================
echo                           DIAGNOSTIC REEL APPROFONDI
echo ========================================================================
echo.

if exist "diagnostic_reel.py" (
    echo Execution du diagnostic reel approfondi...
    %PYTHON_CMD% diagnostic_reel.py
    if not errorlevel 1 (
        echo ✅ Diagnostic reel: TOUS LES FICHIERS VALIDES
    ) else (
        echo ❌ Diagnostic reel: PROBLEMES DETECTES
        set /a REAL_ERRORS+=1
    )
) else (
    echo Diagnostic manuel...
    goto :manual_diagnosis
)

goto :final_results

:manual_diagnosis
echo.
echo ========================================================================
echo                           DIAGNOSTIC MANUEL
echo ========================================================================
echo.

echo Diagnostic manuel des problemes...

REM Test manuel des dépendances
echo Test des dependances critiques...

if defined PYTHON_CMD (
    echo Test de PySide6...
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul
    if not errorlevel 1 (
        echo ✅ PySide6: Disponible
    ) else (
        echo ❌ PySide6: MANQUANT
        set /a REAL_ERRORS+=1
    )
    
    echo Test de sqlalchemy...
    %PYTHON_CMD% -c "import sqlalchemy; print('✅ SQLAlchemy: OK')" 2>nul
    if not errorlevel 1 (
        echo ✅ SQLAlchemy: Disponible
    ) else (
        echo ❌ SQLAlchemy: MANQUANT
        set /a REAL_ERRORS+=1
    )
    
    echo Test de typing...
    %PYTHON_CMD% -c "import typing; print('✅ Typing: OK')" 2>nul
    if not errorlevel 1 (
        echo ✅ Typing: Disponible
    ) else (
        echo ❌ Typing: MANQUANT
        set /a REAL_ERRORS+=1
    )
)

REM Test manuel des fichiers critiques
echo.
echo Test des fichiers critiques...

if exist "src\main.py" (
    echo Test de src\main.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/main.py', 'r', encoding='utf-8').read()); print('✅ main.py: Syntaxe OK')" 2>nul
        if not errorlevel 1 (
            echo ✅ main.py: Syntaxe correcte
        ) else (
            echo ❌ main.py: Erreur de syntaxe
            set /a REAL_ERRORS+=1
        )
    )
)

if exist "src\config.py" (
    echo Test de src\config.py...
    if defined PYTHON_CMD (
        %PYTHON_CMD% -c "import ast; ast.parse(open('src/config.py', 'r', encoding='utf-8').read()); print('✅ config.py: Syntaxe OK')" 2>nul
        if not errorlevel 1 (
            echo ✅ config.py: Syntaxe correcte
        ) else (
            echo ❌ config.py: Erreur de syntaxe
            set /a REAL_ERRORS+=1
        )
    )
)

REM Test du fichier SQL
echo.
echo Test du fichier SQL...

if exist "src\database\migrations\init.sql" (
    findstr /C:"SERIAL PRIMARY KEY" "src\database\migrations\init.sql" >nul
    if not errorlevel 1 (
        echo ❌ SQL: Occurrences SERIAL PRIMARY KEY detectees
        set /a REAL_ERRORS+=1
    ) else (
        echo ✅ SQL: Aucune occurrence SERIAL PRIMARY KEY
    )
) else (
    echo ❌ Fichier init.sql non trouve
    set /a REAL_ERRORS+=1
)

:final_results
REM Résultats finaux du diagnostic réel
echo.
echo ========================================================================
echo                              RESULTATS DIAGNOSTIC REEL
echo ========================================================================
echo.

if %REAL_ERRORS%==0 (
    echo [%TIME%] 🎉 DIAGNOSTIC REEL: AUCUN PROBLEME DETECTE !
    echo.
    echo ✅ TOUS LES TESTS D EXECUTION: REUSSIS
    echo ✅ TOUTES LES DEPENDANCES: DISPONIBLES
    echo ✅ TOUS LES FICHIERS: SYNTAXE CORRECTE
    echo ✅ FICHIER SQL: COMPATIBLE SQLITE
    echo ✅ APPLICATION: PRETE POUR UTILISATION
    echo.
    echo 🏆 CONCLUSION: L APPLICATION FONCTIONNE PARFAITEMENT !
    echo.
    echo 🚀 COMMANDE DE LANCEMENT:
    if defined PYTHON_CMD (
        echo   %PYTHON_CMD% src\main.py
    ) else (
        echo   python src\main.py
    )
    echo.
    echo Tentative de lancement automatique...
    if defined PYTHON_CMD (
        start "GestImmob - Application Fonctionnelle" %PYTHON_CMD% src\main.py
        echo ✅ Application lancee automatiquement !
    )
) else (
    echo [%TIME%] 🚨 DIAGNOSTIC REEL: %REAL_ERRORS% PROBLEME(S) REEL(S) DETECTE(S)
    echo.
    echo ❌ Des problemes reels empechent l execution de l application
    echo.
    echo TYPES DE PROBLEMES POSSIBLES:
    echo   - Dependances manquantes (PySide6, SQLAlchemy)
    echo   - Erreurs de syntaxe dans les fichiers Python
    echo   - Problemes de configuration
    echo   - Fichier SQL incompatible
    echo.
    echo ACTIONS RECOMMANDEES:
    echo   1. Installer les dependances manquantes:
    echo      pip install PySide6 sqlalchemy
    echo   2. Corriger les erreurs de syntaxe detectees
    echo   3. Corriger le fichier SQL si necessaire
    echo   4. Re-executer ce diagnostic
    echo.
    echo OUTILS DE CORRECTION DISPONIBLES:
    if exist "fix_sql_complete.py" echo   - python fix_sql_complete.py
    if exist "diagnostic_reel.py" echo   - python diagnostic_reel.py
    if exist "test_execution_directe.py" echo   - python test_execution_directe.py
)

echo.
echo FICHIERS DE DIAGNOSTIC CREES:
if exist "diagnostic_reel.py" echo   🔧 diagnostic_reel.py: Diagnostic approfondi
if exist "test_execution_directe.py" echo   🔧 test_execution_directe.py: Test d execution
echo   📄 DIAGNOSTIC_REEL.bat: Script de diagnostic complet

echo.
echo [%TIME%] Diagnostic reel termine.
echo.
echo Appuyez sur une touche pour fermer...
pause >nul
