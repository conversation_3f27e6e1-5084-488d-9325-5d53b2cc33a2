<!-- Generated by SpecStory -->

# /fix La déclaration de méthode « scanner_code_barr (2025-06-08 12:14:35)

_**User**_

/fix La déclaration de méthode « scanner_code_barres » est masquée par une déclaration du même nom

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez la duplication de la méthode `scanner_code_barres` pour n'en garder qu'une seule. Faites de même pour toute duplication de `afficher_charts` si nécessaire.

````python
# ...existing code...

    # --- Gardez UNE SEULE définition de scanner_code_barres ---
    def scanner_code_barres(self):
        code, ok = QInputDialog.getText(self, "Scan code-barres", "<PERSON><PERSON>z ou saisissez le code-barres de l'article :")
        if ok and code:
            QMessageBox.information(self, "Code-barres", f"Code-barres scanné : {code}")
            selected = self.table.selectedItems()
            if selected:
                row = selected[0].row()
                self.table.setItem(row, 13, QTableWidgetItem(code))
                self.changements_detectes = True
                self.sauvegarder_inventaire()
    # --- Fin scanner_code_barres ---

# ...supprimez la seconde définition de scanner_code_barres...

# ...gardez UNE SEULE définition de afficher_charts si besoin...
# ...existing code...
````

---

