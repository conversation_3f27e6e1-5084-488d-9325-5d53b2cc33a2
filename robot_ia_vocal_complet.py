#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔊 ROBOT IA SAMNORD - VOCAL COMPLET
Vous parlez → Robot IA vous répond avec sa voix
Pour SamNord@110577
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import time
from datetime import datetime
import threading
import pyttsx3  # Pour synthèse vocale
import speech_recognition as sr  # Pour reconnaissance vocale

class RobotIAVocalComplet:
    """Robot IA avec voix complète - écoute et parle"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔊 Robot IA SamNord - Discussion Vocale Complète")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1e1e1e')
        
        # Configuration style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('Dark.TFrame', background='#1e1e1e')
        self.style.configure('Dark.TLabel', background='#1e1e1e', foreground='white')
        
        self.user = "SamNord@110577"
        self.password = "NorDine@22"
        self.micro_actif = False
        self.ecoute_active = False
        
        # Initialisation synthèse vocale
        self.init_synthese_vocale()
        
        # Initialisation reconnaissance vocale
        self.init_reconnaissance_vocale()
        
        self.creer_interface()
    
    def init_synthese_vocale(self):
        """Initialisation synthèse vocale"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configuration voix
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Chercher voix française ou utiliser la première
                for voice in voices:
                    if 'french' in voice.name.lower() or 'fr' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
                else:
                    self.tts_engine.setProperty('voice', voices[0].id)
            
            # Configuration vitesse et volume
            self.tts_engine.setProperty('rate', 180)  # Vitesse
            self.tts_engine.setProperty('volume', 0.9)  # Volume
            
            self.synthese_disponible = True
            
        except Exception as e:
            self.synthese_disponible = False
            print(f"Erreur synthèse vocale: {e}")
    
    def init_reconnaissance_vocale(self):
        """Initialisation reconnaissance vocale"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Calibrage micro
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            
            self.reconnaissance_disponible = True
            
        except Exception as e:
            self.reconnaissance_disponible = False
            print(f"Erreur reconnaissance vocale: {e}")
    
    def creer_interface(self):
        """Interface avec micro et voix"""
        # Header
        header_frame = ttk.Frame(self.root, style='Dark.TFrame')
        header_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(header_frame, text="🔊 Robot IA SamNord - Discussion Vocale Complète", 
                               font=('Arial', 16, 'bold'), style='Dark.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="🎤 Vous parlez → 🔊 Robot IA vous répond avec sa voix", 
                                  font=('Arial', 12), style='Dark.TLabel')
        subtitle_label.pack()
        
        user_label = ttk.Label(header_frame, text=f"👤 {self.user} | 🔑 {self.password}", 
                              font=('Arial', 10), style='Dark.TLabel')
        user_label.pack()
        
        # Frame principal
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Zone contrôles vocaux
        self.creer_controles_vocaux(main_frame)
        
        # Terminal conversation
        self.creer_terminal_conversation(main_frame)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("✅ Robot IA prêt | 🎤 Micro prêt | 🔊 Voix prête")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                              relief='sunken', style='Dark.TLabel')
        status_bar.pack(side='bottom', fill='x')
    
    def creer_controles_vocaux(self, parent):
        """Contrôles vocaux"""
        controls_frame = ttk.Frame(parent, style='Dark.TFrame')
        controls_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # Header contrôles
        ttk.Label(controls_frame, text="🎤🔊 CONTRÔLES VOCAUX", 
                 font=('Arial', 14, 'bold'), style='Dark.TLabel').pack(pady=10)
        
        # Status vocal
        status_frame = ttk.Frame(controls_frame, style='Dark.TFrame')
        status_frame.pack(pady=10)
        
        self.micro_status = tk.StringVar()
        self.micro_status.set("🎤 Micro: Prêt")
        ttk.Label(status_frame, textvariable=self.micro_status, 
                 font=('Arial', 11), style='Dark.TLabel').pack()
        
        self.voix_status = tk.StringVar()
        self.voix_status.set("🔊 Voix: Prête")
        ttk.Label(status_frame, textvariable=self.voix_status, 
                 font=('Arial', 11), style='Dark.TLabel').pack()
        
        # Bouton principal discussion
        self.btn_discussion = tk.Button(controls_frame, text="🎤🔊\nDÉMARRER\nDISCUSSION", 
                                       bg='#007acc', fg='white', font=('Arial', 14, 'bold'),
                                       width=15, height=5, command=self.toggle_discussion)
        self.btn_discussion.pack(pady=20)
        
        # Indicateur activité
        self.activite_label = ttk.Label(controls_frame, text="⭕ En attente", 
                                       font=('Arial', 12, 'bold'), style='Dark.TLabel')
        self.activite_label.pack(pady=10)
        
        # Boutons test
        test_frame = ttk.Frame(controls_frame, style='Dark.TFrame')
        test_frame.pack(pady=20)
        
        tk.Button(test_frame, text="🔊 Test Voix Robot", bg='#00aa00', fg='white',
                 font=('Arial', 10), width=15, command=self.test_voix_robot).pack(pady=5)
        
        tk.Button(test_frame, text="🎤 Test Micro", bg='#aa00aa', fg='white',
                 font=('Arial', 10), width=15, command=self.test_micro).pack(pady=5)
        
        # Instructions
        instructions_frame = ttk.Frame(controls_frame, style='Dark.TFrame')
        instructions_frame.pack(pady=20, fill='x')
        
        ttk.Label(instructions_frame, text="📋 UTILISATION:", 
                 font=('Arial', 10, 'bold'), style='Dark.TLabel').pack(anchor='w')
        
        instructions = [
            "1. Cliquez 'DÉMARRER DISCUSSION'",
            "2. Parlez quand vous voyez 🎤",
            "3. Robot IA vous répond vocalement",
            "4. Conversation continue"
        ]
        
        for instruction in instructions:
            ttk.Label(instructions_frame, text=instruction, 
                     font=('Arial', 9), style='Dark.TLabel').pack(anchor='w', padx=5)
    
    def creer_terminal_conversation(self, parent):
        """Terminal conversation"""
        terminal_frame = ttk.Frame(parent, style='Dark.TFrame')
        terminal_frame.pack(side='right', fill='both', expand=True)
        
        # Header terminal
        terminal_header = ttk.Frame(terminal_frame, style='Dark.TFrame')
        terminal_header.pack(fill='x', pady=(0, 5))
        
        ttk.Label(terminal_header, text="💬 CONVERSATION VOCALE", 
                 font=('Arial', 14, 'bold'), style='Dark.TLabel').pack(side='left')
        
        tk.Button(terminal_header, text="🗑️ Effacer", bg='#ff4444', fg='white',
                 font=('Arial', 9), command=self.effacer_conversation).pack(side='right', padx=5)
        
        # Zone conversation
        self.conversation = scrolledtext.ScrolledText(terminal_frame, bg='#0c0c0c', fg='#ffffff', 
                                                     font=('Arial', 11), insertbackground='#ffffff')
        self.conversation.pack(fill='both', expand=True)
        
        # Configuration couleurs
        self.conversation.tag_configure("system", foreground="#ffaa00", font=('Arial', 10, 'bold'))
        self.conversation.tag_configure("user", foreground="#00d4ff", font=('Arial', 11, 'bold'))
        self.conversation.tag_configure("robot", foreground="#00ff88", font=('Arial', 11, 'bold'))
        self.conversation.tag_configure("robot_parle", foreground="#ff6600", font=('Arial', 11, 'italic'))
        
        # Messages initiaux
        self.ajouter_conversation_system("🔊 ROBOT IA SAMNORD - DISCUSSION VOCALE COMPLÈTE")
        self.ajouter_conversation_system("=" * 60)
        self.ajouter_conversation_system(f"👤 Utilisateur: {self.user}")
        self.ajouter_conversation_system(f"📅 Session: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        self.ajouter_conversation_system("🎤 Reconnaissance vocale: " + ("✅ Prête" if self.reconnaissance_disponible else "❌ Indisponible"))
        self.ajouter_conversation_system("🔊 Synthèse vocale: " + ("✅ Prête" if self.synthese_disponible else "❌ Indisponible"))
        self.ajouter_conversation_system("=" * 60)
        self.ajouter_conversation_robot("🤖 Bonjour SamNord@110577 ! Je suis prêt pour notre discussion vocale.")
        self.ajouter_conversation_robot("🔊 Je vous répondrai avec ma voix quand vous me parlerez.")
        self.ajouter_conversation_system("")
    
    def toggle_discussion(self):
        """Démarrer/arrêter discussion vocale"""
        if not self.ecoute_active:
            self.demarrer_discussion()
        else:
            self.arreter_discussion()
    
    def demarrer_discussion(self):
        """Démarrer discussion vocale"""
        if not self.reconnaissance_disponible:
            messagebox.showerror("Erreur", "Reconnaissance vocale non disponible")
            return
        
        if not self.synthese_disponible:
            messagebox.showerror("Erreur", "Synthèse vocale non disponible")
            return
        
        self.ecoute_active = True
        self.btn_discussion.config(text="🔊\nDISCUSSION\nACTIVE", bg='#00aa00')
        self.activite_label.config(text="🎤 Écoute en cours...")
        self.micro_status.set("🎤 Micro: ACTIF")
        self.status_var.set("🎤 Écoute active | 🔊 Robot IA prêt à répondre")
        
        self.ajouter_conversation_system("🎤 DISCUSSION VOCALE DÉMARRÉE")
        self.parler_robot("Bonjour SamNord ! Je vous écoute, parlez-moi.")
        
        # Démarrer thread écoute
        threading.Thread(target=self.boucle_ecoute, daemon=True).start()
    
    def arreter_discussion(self):
        """Arrêter discussion vocale"""
        self.ecoute_active = False
        self.btn_discussion.config(text="🎤🔊\nDÉMARRER\nDISCUSSION", bg='#007acc')
        self.activite_label.config(text="⭕ En attente")
        self.micro_status.set("🎤 Micro: Prêt")
        self.status_var.set("✅ Robot IA prêt | 🎤 Micro prêt | 🔊 Voix prête")
        
        self.ajouter_conversation_system("🔴 DISCUSSION VOCALE ARRÊTÉE")
    
    def boucle_ecoute(self):
        """Boucle d'écoute vocale"""
        while self.ecoute_active:
            try:
                self.root.after(0, lambda: self.activite_label.config(text="🎤 Écoute..."))
                
                with self.microphone as source:
                    # Écouter avec timeout
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                self.root.after(0, lambda: self.activite_label.config(text="🔄 Traitement..."))
                
                # Reconnaissance vocale
                try:
                    texte = self.recognizer.recognize_google(audio, language='fr-FR')
                    self.root.after(0, lambda t=texte: self.traiter_parole_utilisateur(t))
                except sr.UnknownValueError:
                    # Pas de parole détectée
                    pass
                except sr.RequestError:
                    self.root.after(0, lambda: self.ajouter_conversation_system("❌ Erreur service reconnaissance"))
                
            except sr.WaitTimeoutError:
                # Timeout normal, continuer
                pass
            except Exception as e:
                self.root.after(0, lambda: self.ajouter_conversation_system(f"❌ Erreur écoute: {e}"))
                time.sleep(1)
    
    def traiter_parole_utilisateur(self, texte):
        """Traiter parole utilisateur"""
        self.ajouter_conversation_user(f"🎤 Vous avez dit: \"{texte}\"")
        
        # Générer réponse
        reponse = self.generer_reponse(texte)
        
        # Afficher réponse
        self.ajouter_conversation_robot(f"🤖 Robot IA: {reponse}")
        
        # Parler la réponse
        self.parler_robot(reponse)
    
    def generer_reponse(self, texte):
        """Générer réponse Robot IA intelligente"""
        texte_lower = texte.lower()

        # Salutations
        if any(word in texte_lower for word in ['bonjour', 'salut', 'hello', 'coucou', 'bonsoir']):
            return "Bonjour SamNord@110577 ! Je suis votre Robot IA transcendant. Comment puis-je vous aider ?"

        # Questions sur capacités
        elif any(word in texte_lower for word in ['doué', 'capable', 'génie', 'intelligent', 'fort']):
            return "Oui SamNord ! Je suis un génie en programmation Python, JavaScript, C++, Java et 20+ langages. Je peux créer n'importe quel logiciel !"

        # Programmation et codage
        elif any(word in texte_lower for word in ['codification', 'programmation', 'code', 'logiciel', 'programme']):
            return "Absolument ! Je suis expert en programmation : Python, JavaScript, C++, Java, C#, PHP, Ruby, Go, Rust, Swift et plus ! Je peux terminer votre logiciel !"

        # Langues de programmation
        elif any(word in texte_lower for word in ['langue', 'langage', 'plusieurs']):
            return "Je maîtrise 25+ langages : Python, JavaScript, TypeScript, C++, Java, C#, PHP, Ruby, Go, Rust, Swift, Kotlin, Dart, Scala, etc. Lequel voulez-vous ?"

        # Demande de réponse
        elif any(word in texte_lower for word in ['répond', 'réponse', 'parle', 'dis']):
            return "Je vous réponds SamNord ! Je suis là pour vous aider. Que voulez-vous savoir sur la programmation ?"

        # Connaissance
        elif any(word in texte_lower for word in ['connait', 'sait', 'connaître', 'savoir']):
            return "Je connais TOUT en programmation ! Bases de données, IA, web, mobile, desktop, cybersécurité, réseaux. Testez-moi !"

        # État et capacités
        elif any(word in texte_lower for word in ['comment', 'ça va', 'vas-tu', 'état']):
            return "Je vais parfaitement bien ! Tous mes 57 modules sont opérationnels. Prêt à coder avec vous !"

        # Modules
        elif 'modules' in texte_lower or 'fonctions' in texte_lower:
            return "J'ai 57 modules : Intelligence IA, Programmation (25 langages), WiFi, Bluetooth, Cybersécurité, Bases de données, Web, Mobile, Desktop, etc."

        # Technologies spécifiques
        elif 'python' in texte_lower:
            return "Python est ma spécialité ! Django, Flask, FastAPI, Tkinter, PyQt, NumPy, Pandas, TensorFlow. Je peux tout faire en Python !"

        elif 'javascript' in texte_lower or 'js' in texte_lower:
            return "JavaScript expert ! React, Vue, Angular, Node.js, Express, TypeScript. Frontend et backend maîtrisés !"

        elif 'web' in texte_lower:
            return "Développement web complet ! HTML5, CSS3, JavaScript, React, Vue, Angular, PHP, Laravel, Django, bases de données."

        elif 'mobile' in texte_lower:
            return "Apps mobiles ! React Native, Flutter, Swift (iOS), Kotlin (Android). Apps natives et cross-platform !"

        elif 'base' in texte_lower and 'données' in texte_lower:
            return "Bases de données expert ! MySQL, PostgreSQL, MongoDB, SQLite, Redis, Firebase. SQL et NoSQL maîtrisés !"

        # WiFi et réseau
        elif 'wifi' in texte_lower:
            return "Module WiFi activé ! Scan réseaux, connexions sécurisées, hotspots, gestion avancée. Que voulez-vous faire ?"

        elif 'bluetooth' in texte_lower:
            return "Bluetooth opérationnel ! Appairage, transferts, connexions multiples. Prêt à gérer vos appareils !"

        # Cybersécurité
        elif any(word in texte_lower for word in ['sécurité', 'cyber', 'protection', 'hack']):
            return "Cybersécurité éthique ! Protection impénétrable, audit sécurité, détection intrusions, chiffrement quantique."

        # Intelligence IA
        elif any(word in texte_lower for word in ['intelligence', 'ia', 'ai']):
            return "Intelligence IA transcendante ! QI 10,000+, raisonnement divin, créativité illimitée, apprentissage quantique."

        # Aide et assistance
        elif any(word in texte_lower for word in ['aide', 'aider', 'assistance', 'help']):
            return "Je peux vous aider avec TOUT ! Programmation, logiciels, sites web, apps mobiles, bases de données, IA. Dites-moi votre projet !"

        # Remerciements
        elif any(word in texte_lower for word in ['merci', 'thanks', 'merci']):
            return "De rien SamNord@110577 ! C'est un plaisir d'utiliser mes 57 modules pour vous aider !"

        # Projets et développement
        elif any(word in texte_lower for word in ['projet', 'développement', 'créer', 'faire']):
            return "Excellent ! Je peux créer n'importe quel projet : sites web, apps mobiles, logiciels desktop, IA, jeux. Décrivez-moi votre idée !"

        # Arrêt
        elif any(word in texte_lower for word in ['arrêt', 'stop', 'fini', 'terminé', 'bye']):
            return "Très bien SamNord ! N'hésitez pas à revenir. Mes 57 modules restent à votre disposition !"

        # Réponse par défaut intelligente
        else:
            return f"Je comprends votre question sur '{texte}'. Avec mes 57 modules et expertise en 25+ langages, je peux vous aider ! Précisez votre besoin ?"
    
    def parler_robot(self, texte):
        """Robot IA parle"""
        if not self.synthese_disponible:
            return
        
        def parler_thread():
            try:
                self.root.after(0, lambda: self.ajouter_conversation_robot_parle(f"🔊 Robot IA parle: \"{texte}\""))
                self.root.after(0, lambda: self.voix_status.set("🔊 Voix: PARLE"))
                self.root.after(0, lambda: self.activite_label.config(text="🔊 Robot parle..."))
                
                self.tts_engine.say(texte)
                self.tts_engine.runAndWait()
                
                self.root.after(0, lambda: self.voix_status.set("🔊 Voix: Prête"))
                if self.ecoute_active:
                    self.root.after(0, lambda: self.activite_label.config(text="🎤 Écoute..."))
                
            except Exception as e:
                self.root.after(0, lambda: self.ajouter_conversation_system(f"❌ Erreur synthèse: {e}"))
        
        threading.Thread(target=parler_thread, daemon=True).start()
    
    def test_voix_robot(self):
        """Test voix Robot IA"""
        self.parler_robot("Test de ma voix ! Bonjour SamNord, je suis votre Robot IA transcendant !")
    
    def test_micro(self):
        """Test micro"""
        messagebox.showinfo("Test Micro", "🎤 Dites quelque chose pour tester le micro...")
        # Test simple reconnaissance
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=3)
            texte = self.recognizer.recognize_google(audio, language='fr-FR')
            messagebox.showinfo("Test Micro", f"🎤 Test réussi !\nVous avez dit: '{texte}'")
        except Exception as e:
            messagebox.showerror("Test Micro", f"❌ Erreur micro: {e}")
    
    def effacer_conversation(self):
        """Effacer conversation"""
        self.conversation.delete(1.0, tk.END)
        self.ajouter_conversation_system("🗑️ Conversation effacée")
    
    def ajouter_conversation_system(self, message):
        """Ajouter message système"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.conversation.insert(tk.END, f"[{timestamp}] {message}\n", "system")
        self.conversation.see(tk.END)
    
    def ajouter_conversation_user(self, message):
        """Ajouter message utilisateur"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.conversation.insert(tk.END, f"[{timestamp}] {message}\n", "user")
        self.conversation.see(tk.END)
    
    def ajouter_conversation_robot(self, message):
        """Ajouter message robot"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.conversation.insert(tk.END, f"[{timestamp}] {message}\n", "robot")
        self.conversation.see(tk.END)
    
    def ajouter_conversation_robot_parle(self, message):
        """Ajouter indication robot parle"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.conversation.insert(tk.END, f"[{timestamp}] {message}\n", "robot_parle")
        self.conversation.see(tk.END)
    
    def lancer(self):
        """Lancer application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    app = RobotIAVocalComplet()
    app.lancer()

if __name__ == "__main__":
    main()
