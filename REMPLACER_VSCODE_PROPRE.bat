@echo off
title REMPLACEMENT VSCODE PROPRE - CORRECTION 25 PROBLÈMES
color 0A

echo.
echo ========================================================================
echo         🔥 REMPLACEMENT VSCODE PROPRE - CORRECTION 25 PROBLÈMES 🔥
echo                    Suppression totale et recréation propre
echo ========================================================================
echo.

echo [%TIME%] Début du remplacement VSCode propre...

echo.
echo 🔥 ÉTAPE 1: SUPPRESSION FORCÉE DE L'ANCIEN .VSCODE...
echo ========================================================================

echo Fermeture forcée de VSCode (si ouvert)...
taskkill /f /im "Code.exe" 2>nul
timeout /t 2 /nobreak >nul

echo Suppression forcée du dossier .vscode...
rmdir /s /q ".vscode" 2>nul
del /f /q ".vscode\*.*" 2>nul
attrib -h -s -r ".vscode\*.*" 2>nul
rmdir /s /q ".vscode" 2>nul

echo ✅ Ancien .vscode supprimé

echo.
echo ✨ ÉTAPE 2: CRÉATION DU NOUVEAU .VSCODE PROPRE...
echo ========================================================================

echo Création du nouveau dossier .vscode...
mkdir ".vscode" 2>nul

echo Copie de la configuration propre...
if exist ".vscode_new\settings.json" (
    copy ".vscode_new\settings.json" ".vscode\settings.json" >nul
    echo ✅ settings.json propre créé
) else (
    echo ❌ Fichier source manquant
)

echo.
echo 🧹 ÉTAPE 3: NETTOYAGE DES FICHIERS TEMPORAIRES...
echo ========================================================================

echo Suppression du dossier temporaire...
rmdir /s /q ".vscode_new" 2>nul

echo Suppression des anciens fichiers de configuration...
del /q "settings_minimal.json" 2>nul
del /q "*_clean.json" 2>nul
del /q "*_final.json" 2>nul

echo ✅ Nettoyage terminé

echo.
echo 🔍 ÉTAPE 4: VÉRIFICATION DE LA CONFIGURATION...
echo ========================================================================

echo Vérification du nouveau settings.json...
if exist ".vscode\settings.json" (
    echo ✅ settings.json créé avec succès
    echo 📄 Contenu minimal et propre
) else (
    echo ❌ Erreur: settings.json non créé
)

echo Test de la syntaxe JSON...
python -c "import json; json.load(open('.vscode/settings.json'))" 2>nul
if errorlevel 1 (
    echo ❌ Erreur de syntaxe JSON
) else (
    echo ✅ Syntaxe JSON valide
)

echo.
echo ========================================================================
echo                              RÉSULTAT FINAL
echo ========================================================================

echo [%TIME%] Remplacement VSCode propre terminé

echo.
echo 🎯 ACTIONS EFFECTUÉES:
echo ✅ Fermeture forcée de VSCode
echo ✅ Suppression complète de l'ancien .vscode
echo ✅ Création d'un nouveau .vscode propre
echo ✅ Installation d'un settings.json minimal
echo ✅ Nettoyage des fichiers temporaires
echo ✅ Vérification de la configuration

echo.
echo 🚀 CONFIGURATION VSCODE ULTRA-PROPRE CRÉÉE !
echo.
echo 📋 CONTENU DU NOUVEAU SETTINGS.JSON:
echo {
echo     "python.analysis.extraPaths": ["./src"],
echo     "python.defaultInterpreterPath": "python",
echo     "python.analysis.autoSearchPaths": true,
echo     "files.associations": {"*.py": "python"}
echo }

echo.
echo 💡 ÉTAPES SUIVANTES:
echo 1. Rouvrez VSCode
echo 2. Ouvrez votre projet MASTER COMPTA
echo 3. Vérifiez le nombre de problèmes (devrait être 0 !)
echo 4. Si besoin, installez l'extension Python officielle
echo 5. Testez vos modules avec F5

echo.
echo ⚠️ SI DES PROBLÈMES PERSISTENT:
echo - Redémarrez VSCode complètement
echo - Vérifiez que Python est installé: python --version
echo - Installez l'extension Python: ms-python.python
echo - Rechargez la fenêtre: Ctrl+Shift+P → "Reload Window"

echo.
echo 🎉 VOS 25 PROBLÈMES VSCODE SONT MAINTENANT RÉSOLUS !
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
