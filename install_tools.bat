@echo off
echo ========================================
echo INSTALLATION DES OUTILS POUR GESTIMMOB
echo ========================================

echo.
echo 1. Mise a jour de pip...
python -m pip install --upgrade pip

echo.
echo 2. Installation des dependances principales...
python -m pip install -r requirements.txt

echo.
echo 3. Installation des outils de qualite de code...
python -m pip install flake8 pylint mypy black autopep8

echo.
echo 4. Installation des outils de test...
python -m pip install pytest pytest-cov pytest-mock

echo.
echo 5. Installation des outils de securite...
python -m pip install bandit safety

echo.
echo 6. Verification des installations...
python -c "import PySide6; print('PySide6: OK')"
python -c "import sqlite3; print('SQLite3: OK')"
python -c "import cryptography; print('Cryptography: OK')"
python -c "import requests; print('Requests: OK')"

echo.
echo 7. Creation des repertoires necessaires...
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "exports" mkdir exports
if not exist "tests" mkdir tests

echo.
echo 8. Configuration des outils...
python setup_tools.py

echo.
echo ========================================
echo INSTALLATION TERMINEE !
echo ========================================
echo.
echo Pour demarrer GestImmob:
echo   python start_gestimmob.py
echo.
echo Pour valider le code:
echo   python ultimate_validator.py
echo.
echo Pour diagnostiquer:
echo   python diagnostic.py
echo.
pause
