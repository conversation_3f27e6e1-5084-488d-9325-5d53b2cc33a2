from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QHBoxLayout, QMessageBox, QLabel, QComboBox, QCheckBox, QFileDialog, QDialog,
    QFormLayout, QDialogButtonBox, QMainWindow, QMenuBar
)
from PySide6.QtGui import QAction, QColor
from PySide6.QtCore import QTimer, QDateTime, Qt
import logging

class RechercheWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Recherche avancée et synchronisée")
        self.resize(1400, 800)
        main_layout = QVBoxLayout(self)

        # Barre de menu ERP/navigation
        self.menu_bar = QMenuBar(self)
        menu_modules = self.menu_bar.addMenu("Modules")
        action_reforme = QAction("Réforme", self)
        action_fournisseur = QAction("Fournisseurs", self)
        action_inventaire = QAction("Inventaire", self)
        action_erp = QAction("ERP/Comptabilité", self)
        menu_modules.addAction(action_reforme)
        menu_modules.addAction(action_fournisseur)
        menu_modules.addAction(action_inventaire)
        menu_modules.addAction(action_erp)
        self.menu_bar.setNativeMenuBar(False)
        # Correction: Ajouter la barre de menu au layout principal
        main_layout.addWidget(self.menu_bar)

        # Filtres avancés inspirés ERP (Odoo/Dolibarr)
        filter_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Recherche multi-critères (nom, ID, type, etc.)")
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "Tous", "Immobilisation", "Fournisseur", "Inventaire", "Client", "Facture", "Achat", "Vente", "Contrat", "Projet", "Employé"
        ])
        self.date_from = QLineEdit(); self.date_from.setPlaceholderText("Date début (AAAA-MM-JJ)")
        self.date_to = QLineEdit(); self.date_to.setPlaceholderText("Date fin (AAAA-MM-JJ)")
        self.only_anomalies = QCheckBox("Seulement anomalies")
        filter_layout.addWidget(QLabel("Type:"))
        filter_layout.addWidget(self.type_combo)
        filter_layout.addWidget(self.search_input)
        filter_layout.addWidget(self.date_from)
        filter_layout.addWidget(self.date_to)
        filter_layout.addWidget(self.only_anomalies)
        main_layout.addLayout(filter_layout)

        self.search_btn = QPushButton("Rechercher")
        self.reforme_btn = QPushButton("Réformer bien")
        btns_layout = QHBoxLayout()
        btns_layout.addWidget(self.search_btn)
        btns_layout.addWidget(self.reforme_btn)
        main_layout.addLayout(btns_layout)

        # Table enrichie avec entêtes métiers et détails multi-domaine
        self.table = QTableWidget(0, 24)
        self.table.setHorizontalHeaderLabels([
            "Type", "ID", "Nom/Désignation", "Catégorie", "Valeur", "Date", "Emplacement",
            "Statut", "Observation", "Responsable", "Dernière modif.", "Alerte", "Synchronisé", "Source",
            "Code-barres", "Type code-barres", "Photo", "Historique", "Compte bancaire", "NIF/RC",
            "Commission", "Fonction membre", "Avis/Décision", "PV Réforme"
        ])
        main_layout.addWidget(self.table)

        # Alarmes et synchronisation automatique
        self.timer_sync = QTimer()
        self.timer_sync.timeout.connect(self.sauvegarde_auto)
        self.timer_sync.start(180000)  # 3 min

        self.search_btn.clicked.connect(self.rechercher)
        self.reforme_btn.clicked.connect(self.reformer_bien)
        self.changements_detectes = False

        # Détection de modification
        self.table.itemChanged.connect(self.marquer_modification)

        # Navigation modules (exemple d'appel)
        action_reforme.triggered.connect(self.reformer_bien)
        # action_fournisseur.triggered.connect(lambda: ...)  # à compléter selon vos widgets
        # action_inventaire.triggered.connect(lambda: ...)
        # action_erp.triggered.connect(lambda: ...)

    def rechercher(self):
        """Recherche multi-critères avec logique fonctionnelle"""
        try:
            # Récupérer les critères de recherche
            search_text = self.search_input.text().strip().lower()
            type_filter = self.type_combo.currentText()
            date_from = self.date_from.text().strip()
            date_to = self.date_to.text().strip()
            only_anomalies = self.only_anomalies.isChecked()

            # Effacer les résultats précédents
            self.table.setRowCount(0)

            # Données d'exemple enrichies pour la démonstration
            donnees_exemple = [
                {
                    "type": "Fournisseur", "id": "F-001", "nom": "Société Alpha", "categorie": "Matériel",
                    "valeur": "15000", "date": "2024-01-15", "emplacement": "Paris", "statut": "Actif",
                    "observation": "Fournisseur principal", "responsable": "M. Martin", "alerte": "",
                    "synchronise": "Oui", "source": "Fournisseurs.db", "code_barres": "1234567890123",
                    "type_code": "EAN-13", "photo": "alpha.jpg", "historique": "Création 2024",
                    "compte_bancaire": "FR7612345678901234567890123", "nif_rc": "NIF: 123456789",
                    "commission": "", "fonction": "", "avis": "", "pv": ""
                },
                {
                    "type": "Inventaire", "id": "I-002", "nom": "Ordinateur portable", "categorie": "Informatique",
                    "valeur": "-500", "date": "2024-02-01", "emplacement": "Bureau A", "statut": "En stock",
                    "observation": "Valeur négative - anomalie", "responsable": "Mme Durand", "alerte": "⚠️",
                    "synchronise": "Non", "source": "Inventaire.db", "code_barres": "",
                    "type_code": "", "photo": "pc.jpg", "historique": "Achat 2024, Anomalie détectée",
                    "compte_bancaire": "", "nif_rc": "", "commission": "", "fonction": "", "avis": "", "pv": ""
                },
                {
                    "type": "Immobilisation", "id": "IMM-003", "nom": "Véhicule de service", "categorie": "Transport",
                    "valeur": "25000", "date": "2023-12-10", "emplacement": "Garage", "statut": "En service",
                    "observation": "Véhicule principal", "responsable": "M. Bernard", "alerte": "",
                    "synchronise": "Oui", "source": "Immobilisations.db", "code_barres": "9876543210987",
                    "type_code": "Code128", "photo": "vehicule.jpg", "historique": "Achat 2023, Entretien 2024",
                    "compte_bancaire": "", "nif_rc": "", "commission": "", "fonction": "", "avis": "", "pv": ""
                }
            ]

            # Filtrer les données selon les critères
            resultats_filtres = []
            for item in donnees_exemple:
                # Filtre par type
                if type_filter != "Tous" and item["type"] != type_filter:
                    continue

                # Filtre par texte de recherche
                if search_text and search_text not in item["nom"].lower() and search_text not in item["id"].lower():
                    continue

                # Filtre anomalies seulement
                if only_anomalies and not item["alerte"]:
                    continue

                resultats_filtres.append(item)

            # Afficher les résultats
            for item in resultats_filtres:
                row = self.table.rowCount()
                self.table.insertRow(row)

                # Remplir toutes les colonnes
                colonnes = [
                    item["type"], item["id"], item["nom"], item["categorie"], item["valeur"],
                    item["date"], item["emplacement"], item["statut"], item["observation"],
                    item["responsable"], QDateTime.currentDateTime().toString(), item["alerte"],
                    item["synchronise"], item["source"], item["code_barres"], item["type_code"],
                    item["photo"], item["historique"], item["compte_bancaire"], item["nif_rc"],
                    item["commission"], item["fonction"], item["avis"], item["pv"]
                ]

                for col, valeur in enumerate(colonnes):
                    if col < self.table.columnCount():
                        table_item = QTableWidgetItem(str(valeur))

                        # Coloration selon le type et les anomalies
                        if item["alerte"]:
                            table_item.setBackground(QColor("lightcoral"))
                        elif item["type"] == "Fournisseur":
                            table_item.setBackground(QColor("lightblue"))
                        elif item["type"] == "Inventaire":
                            table_item.setBackground(QColor("lightgreen"))
                        elif item["type"] == "Immobilisation":
                            table_item.setBackground(QColor("lightyellow"))

                        self.table.setItem(row, col, table_item)

            # Message de résultat
            nb_resultats = len(resultats_filtres)
            if nb_resultats == 0:
                QMessageBox.information(self, "Recherche", "Aucun résultat trouvé pour ces critères.")
            else:
                # Détecter et signaler les anomalies
                anomalies = [item for item in resultats_filtres if item["alerte"]]
                if anomalies:
                    QMessageBox.warning(self, "Anomalies détectées",
                        f"🔍 Recherche terminée: {nb_resultats} résultat(s)\n"
                        f"⚠️ {len(anomalies)} anomalie(s) détectée(s) !\n\n"
                        f"Vérifiez les lignes en rouge.")
                else:
                    QMessageBox.information(self, "Recherche",
                        f"✅ Recherche terminée: {nb_resultats} résultat(s)\n"
                        f"Aucune anomalie détectée.")

            logging.info(f"Recherche effectuée: {nb_resultats} résultats, critères: {search_text}, {type_filter}")

        except Exception as e:
            logging.error(f"Erreur lors de la recherche: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la recherche: {e}")

    def marquer_modification(self, item):
        self.changements_detectes = True
        item.setBackground(QColor("yellow"))
        # Détection d'anomalie enrichie avec vérifications sécurisées
        try:
            if item.column() == 4:  # Colonne Valeur
                text_value = item.text().strip()
                if text_value:
                    # Vérifier si c'est un nombre valide
                    try:
                        value = float(text_value.replace(',', '.'))
                        if value < 0:
                            item.setBackground(QColor("red"))
                            self.table.setItem(item.row(), 11, QTableWidgetItem("⚠️ Valeur négative"))
                            QMessageBox.warning(self, "Alerte", "Valeur négative détectée.")
                        else:
                            item.setBackground(QColor("lightgreen"))
                            self.table.setItem(item.row(), 11, QTableWidgetItem("✅ Valeur OK"))
                    except ValueError:
                        item.setBackground(QColor("red"))
                        self.table.setItem(item.row(), 11, QTableWidgetItem("⚠️ Valeur invalide"))
                        QMessageBox.warning(self, "Alerte", "Format de valeur invalide.")
            elif item.column() == 14:  # Colonne Code-barres
                if not item.text().strip():
                    item.setBackground(QColor("orange"))
                    self.table.setItem(item.row(), 11, QTableWidgetItem("⚠️ Code-barres manquant"))
                else:
                    item.setBackground(QColor("lightgreen"))
                    self.table.setItem(item.row(), 11, QTableWidgetItem("✅ Code-barres OK"))
            else:
                # Autres colonnes - pas d'alerte spécifique
                if item.row() < self.table.rowCount() and 11 < self.table.columnCount():
                    current_alert = self.table.item(item.row(), 11)
                    if not current_alert or not current_alert.text().startswith("⚠️"):
                        self.table.setItem(item.row(), 11, QTableWidgetItem(""))
        except Exception as e:
            logging.error(f"Erreur lors de la détection d'anomalie: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la validation: {e}")

    def sauvegarde_auto(self):
        if self.changements_detectes:
            # ...logique de sauvegarde synchronisée multi-base...
            logging.info("Sauvegarde automatique et synchronisation effectuée.")
            self.changements_detectes = False

    def reformer_bien(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Réforme", "Sélectionnez une ligne à réformer.")
            return
        row = selected[0].row()
        dialog = QDialog(self)
        dialog.setWindowTitle("Réforme du bien")
        form = QFormLayout(dialog)
        motif = QLineEdit()
        date_reforme = QLineEdit()
        # Correction : vérifier que l'item n'est pas None et est bien un QTableWidgetItem, puis accéder à .text()
        item_resp = self.table.item(row, 9)
        responsable = QLineEdit(item_resp.text() if item_resp is not None and hasattr(item_resp, "text") and item_resp.text() else "")
        item_obs = self.table.item(row, 8)
        observation = QLineEdit(item_obs.text() if item_obs is not None and hasattr(item_obs, "text") and item_obs.text() else "")
        commission = QLineEdit()
        fonction_membre = QLineEdit()
        avis = QLineEdit()
        pv_reforme = QLineEdit()
        form.addRow("Motif de la réforme:", motif)
        form.addRow("Date de réforme:", date_reforme)
        form.addRow("Responsable:", responsable)
        form.addRow("Observation:", observation)
        form.addRow("Commission (noms):", commission)
        form.addRow("Fonction membre:", fonction_membre)
        form.addRow("Avis/Décision:", avis)
        form.addRow("PV de réforme:", pv_reforme)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        if dialog.exec() == 1:
            self.table.setItem(row, 7, QTableWidgetItem("Réformé"))
            self.table.setItem(row, 8, QTableWidgetItem(observation.text()))
            self.table.setItem(row, 11, QTableWidgetItem("Réforme"))
            self.table.setItem(row, 10, QTableWidgetItem(QDateTime.currentDateTime().toString()))
            self.table.setItem(row, 9, QTableWidgetItem(responsable.text()))
            # Ajout d'un détail de réforme dans l'historique avec vérification sécurisée
            try:
                hist_item = self.table.item(row, 17)
                hist = hist_item.text() if hist_item and hasattr(hist_item, 'text') else ""
                hist += f"\nRéforme: {motif.text()} le {date_reforme.text()} par {responsable.text()}"
                self.table.setItem(row, 17, QTableWidgetItem(hist))
            except Exception as e:
                logging.error(f"Erreur lors de la mise à jour de l'historique: {e}")
                # Créer un nouvel historique si problème
                hist = f"Réforme: {motif.text()} le {date_reforme.text()} par {responsable.text()}"
                self.table.setItem(row, 17, QTableWidgetItem(hist))
            self.table.setItem(row, 20, QTableWidgetItem(commission.text()))
            self.table.setItem(row, 21, QTableWidgetItem(fonction_membre.text()))
            self.table.setItem(row, 22, QTableWidgetItem(avis.text()))
            self.table.setItem(row, 23, QTableWidgetItem(pv_reforme.text()))
            QMessageBox.information(self, "Réforme", "Bien réformé avec succès.")
            self.changements_detectes = True

    def closeEvent(self, event):
        if self.changements_detectes:
            self.sauvegarde_auto()
        event.accept()
