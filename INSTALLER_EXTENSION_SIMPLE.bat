@echo off
title 🔧 Installation Extension Robot IA SamNord
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🔧 INSTALLATION EXTENSION ROBOT IA SAMNORD                             ██
echo ██    🤖 INTERFACE IDENTIQUE À AUGMENT AGENT                                 ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🔧 INSTALLATION EXTENSION ROBOT IA SAMNORD
echo ===========================================
echo 👤 SamNord@110577
echo 🔑 NorDine@22
echo 🤖 Interface comme Augment Agent
echo.

echo 📁 COPIE VERS DOSSIER BUREAU...
if not exist "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet" (
    mkdir "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet"
    echo ✅ Dossier projet créé
)

echo 📦 Copie fichiers projet...
xcopy "*.py" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\" /Y >nul 2>&1
xcopy "*.bat" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\" /Y >nul 2>&1
xcopy "*.md" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\" /Y >nul 2>&1
xcopy "modules" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\modules\" /E /I /Y >nul 2>&1
xcopy "vscode_extension" "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\vscode_extension\" /E /I /Y >nul 2>&1

echo ✅ Projet copié sur le bureau

echo.
echo 🔧 INSTALLATION EXTENSION VS CODE...

REM Dossier extensions VS Code
set "VSCODE_EXTENSIONS=%USERPROFILE%\.vscode\extensions"
set "EXTENSION_DIR=%VSCODE_EXTENSIONS%\robot-ia-samnord-1.0.0"

if not exist "%VSCODE_EXTENSIONS%" mkdir "%VSCODE_EXTENSIONS%"
if not exist "%EXTENSION_DIR%" mkdir "%EXTENSION_DIR%"

echo 📁 Copie extension vers VS Code...
xcopy "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet\vscode_extension\*" "%EXTENSION_DIR%\" /E /I /Y >nul 2>&1

if %errorLevel% equ 0 (
    echo ✅ Extension installée dans VS Code
) else (
    echo ⚠️ Erreur installation extension
)

echo.
echo 🚀 LANCEMENT VS CODE AVEC PROJET...
start "" code "%USERPROFILE%\Desktop\RobotIA_SamNord_Projet"

echo ✅ VS Code lancé avec le projet Robot IA

echo.
echo 🤖 LANCEMENT ROBOT IA EN EXTERNE...
start "" cmd /c "cd /d %USERPROFILE%\Desktop\RobotIA_SamNord_Projet && python robot_ia_universel_40_modules.py && pause"

echo ✅ Robot IA lancé en externe

echo.
echo 📋 INSTRUCTIONS UTILISATION:
echo.
echo 🎯 DANS VS CODE:
echo    1. Ctrl+Shift+P puis "Robot IA"
echo    2. Ou cliquer icône 🤖 dans barre latérale
echo    3. Utiliser Ctrl+Shift+C pour chat
echo    4. Interface identique à Augment Agent
echo.
echo 🎯 EN EXTERNE:
echo    1. Fenêtre Robot IA ouverte séparément
echo    2. Tous modules accessibles
echo    3. Fonctionnement autonome
echo.
echo 🔐 VOS IDENTIFIANTS:
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo.

echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         🎉 INSTALLATION TERMINÉE !                                        ██
echo ██         🤖 ROBOT IA DISPONIBLE DANS VS CODE ET EN EXTERNE !              ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🌟 ROBOT IA SAMNORD INSTALLÉ ET LANCÉ !
echo 🎯 INTERFACE COMME AUGMENT AGENT DISPONIBLE !
echo 🚀 PROJET COMPLET SUR LE BUREAU !
echo.
pause
exit /b 0
