#!/usr/bin/env python3
"""
Script de build automatisé pour GestImmob
Crée un exécutable optimisé avec toutes les dépendances
"""

import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List

# Configuration du logging avec encodage UTF-8
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BuildManager:
    """Gestionnaire de build pour GestImmob"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "GestImmob.spec"
        
        # Configuration de build
        self.config: Dict[str, Any] = {
            "app_name": "GestImmob",
            "version": "2.0.0",
            "author": "Votre Entreprise",
            "description": "Logiciel de gestion immobilière professionnel",
            "main_script": "src/main.py",
            "icon": "assets/icon.ico",
            "console": False,
            "onefile": True,
            "optimize": True,
            "strip": True,
            "upx": True,
            "clean": True
        }
        
    def check_dependencies(self) -> bool:
        """Vérifie que toutes les dépendances sont installées"""
        logger.info("Vérification des dépendances...")

        # Mapping des noms de packages pour l'import
        # Packages essentiels (obligatoires)
        essential_packages = {
            "pyinstaller": "PyInstaller",
            "PySide6": "PySide6",
            "cryptography": "cryptography",
        }

        # Packages optionnels (recommandés mais pas obligatoires)
        optional_packages = {
            "SQLAlchemy": "sqlalchemy",
            "pandas": "pandas",
            "xlsxwriter": "xlsxwriter",
            "opencv-python": "cv2",
            "pyzbar": "pyzbar",
            "Pillow": "PIL",
            "reportlab": "reportlab",
            "requests": "requests"
        }

        missing_essential: List[str] = []
        missing_optional: List[str] = []

        # Vérifier les packages essentiels
        logger.info("Vérification des packages essentiels...")
        for package, import_name in essential_packages.items():
            try:
                __import__(import_name)
                logger.info(f"[OK] {package} installé")
            except ImportError:
                missing_essential.append(package)
                logger.error(f"[MANQUANT] {package} manquant (ESSENTIEL)")

        # Vérifier les packages optionnels
        logger.info("Vérification des packages optionnels...")
        for package, import_name in optional_packages.items():
            try:
                __import__(import_name)
                logger.info(f"[OK] {package} installé")
            except ImportError:
                missing_optional.append(package)
                logger.warning(f"[MANQUANT] {package} manquant (optionnel)")

        # Seuls les packages essentiels sont obligatoires
        if missing_essential:
            logger.error(f"Packages essentiels manquants: {', '.join(missing_essential)}")
            logger.info("Exécutez: pip install " + " ".join(missing_essential))
            return False

        if missing_optional:
            logger.warning(f"Packages optionnels manquants: {', '.join(missing_optional)}")
            logger.info("Pour installer tous les packages optionnels: pip install " + " ".join(missing_optional))

        logger.info("Toutes les dépendances essentielles sont installées")
        return True
        
    def clean_build_dirs(self):
        """Nettoie les répertoires de build"""
        logger.info("Nettoyage des répertoires de build...")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                logger.info(f"Supprimé: {dir_path}")
        
        # Supprimer les fichiers .spec existants
        for spec_file in self.project_root.glob("*.spec"):
            spec_file.unlink()
            logger.info(f"Supprimé: {spec_file}")
            
    def create_spec_file(self) -> Path:
        """Crée le fichier .spec pour PyInstaller"""
        logger.info("Création du fichier .spec...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Configuration
app_name = "{self.config['app_name']}"
main_script = "{self.config['main_script']}"
project_root = Path.cwd()

# Données à inclure
datas = []

# Vérifier et ajouter les fichiers de données s'ils existent
data_files = [
    ('config_advanced.json', '.'),
    ('requirements.txt', '.'),
    ('README.md', '.'),
    ('config.json', '.'),
    ('src/config.json', '.'),
]

for src_file, dest_dir in data_files:
    if (project_root / src_file).exists():
        datas.append((src_file, dest_dir))

# Ajouter les modules source
if (project_root / 'src').exists():
    datas.append(('src', 'src'))
if (project_root / 'modules').exists():
    datas.append(('modules', 'modules'))

# Modules cachés (pour éviter les erreurs d'import)
# Seulement les modules qui sont réellement installés
hiddenimports = [
    # PySide6 modules
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtPrintSupport',
    'PySide6.QtSql',

    # Base de données
    'sqlite3',
    'sqlalchemy',
    'sqlalchemy.engine',
    'sqlalchemy.orm',

    # Sécurité
    'cryptography.fernet',
    'cryptography.hazmat',

    # Traitement de données
    'xlsxwriter',

    # Images
    'PIL',
    'PIL.Image',

    # Réseau
    'requests',
    'urllib3',

    # Modules système
    'json',
    'datetime',
    'pathlib',
    'logging',
    'threading',
    'multiprocessing',
    'os',
    'sys',
    'shutil',
    'subprocess',
    'hashlib',

    # Modules du projet
    'src.main',
    'src.config',
    'src.auth.authentication',
    'src.database.db_connection',
    'src.utils.config',
]

# Ajouter les modules optionnels seulement s'ils sont installés
optional_modules = [
    ('pandas', 'pandas'),
    ('reportlab', 'reportlab'),
    ('reportlab', 'reportlab.pdfgen'),
    ('reportlab', 'reportlab.lib'),
    ('cv2', 'cv2'),
    ('pyzbar', 'pyzbar'),
    ('openpyxl', 'openpyxl'),
    ('numpy', 'numpy'),
]

for package, module in optional_modules:
    try:
        __import__(package)
        hiddenimports.append(module)
    except ImportError:
        pass

# Exclusions pour réduire la taille
excludes = [
    'tkinter',
    'matplotlib',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'setuptools',
    'distutils',
]

# Binaires à inclure
binaries = []

a = Analysis(
    [main_script],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=app_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip={self.config['strip']},
    upx={self.config['upx']},
    upx_exclude=[],
    runtime_tmpdir=None,
    console={self.config['console']},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon=None,
)
'''
        
        spec_path = self.project_root / f"{self.config['app_name']}.spec"
        with open(spec_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)
            
        logger.info(f"Fichier .spec créé: {spec_path}")
        return spec_path
        
    def create_version_info(self):
        """Crée le fichier d'informations de version"""
        logger.info("Création du fichier version_info.txt...")
        
        version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2,0,0,0),
    prodvers=(2,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'{self.config["author"]}'),
        StringStruct(u'FileDescription', u'{self.config["description"]}'),
        StringStruct(u'FileVersion', u'{self.config["version"]}'),
        StringStruct(u'InternalName', u'{self.config["app_name"]}'),
        StringStruct(u'LegalCopyright', u'© {datetime.now().year} {self.config["author"]}'),
        StringStruct(u'OriginalFilename', u'{self.config["app_name"]}.exe'),
        StringStruct(u'ProductName', u'{self.config["app_name"]}'),
        StringStruct(u'ProductVersion', u'{self.config["version"]}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
        
        with open('version_info.txt', 'w', encoding='utf-8') as f:
            f.write(version_info)
            
    def optimize_build(self):
        """Optimise les paramètres de build"""
        logger.info("Optimisation des paramètres de build...")
        
        # Variables d'environnement pour optimiser PyInstaller
        env_vars = {
            'PYTHONOPTIMIZE': '2',  # Optimisation maximale
            'PYTHONDONTWRITEBYTECODE': '1',  # Pas de fichiers .pyc
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"Variable d'environnement: {key}={value}")
            
    def verify_main_script(self) -> bool:
        """Vérifie que le script principal existe"""
        main_script_path = self.project_root / self.config["main_script"]

        if not main_script_path.exists():
            logger.error(f"Script principal introuvable: {main_script_path}")

            # Chercher des alternatives
            alternatives = [
                "src/main.py",
                "main.py",
                "gestimmob_complet.py",
                "gestimmob_enterprise_v5.py"
            ]

            for alt in alternatives:
                alt_path = self.project_root / alt
                if alt_path.exists():
                    logger.info(f"Script alternatif trouvé: {alt}")
                    self.config["main_script"] = alt
                    return True

            return False

        return True

    def build_executable(self) -> bool:
        """Construit l'exécutable avec PyInstaller"""
        logger.info("Construction de l'exécutable...")

        try:
            # Vérifier que le script principal existe
            if not self.verify_main_script():
                logger.error("Aucun script principal valide trouvé")
                return False

            # Créer le fichier .spec
            spec_file = self.create_spec_file()

            # Créer le fichier version_info
            self.create_version_info()

            # Optimiser les paramètres
            self.optimize_build()

            # Commande PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                "--log-level=INFO",
                str(spec_file)
            ]

            logger.info(f"Exécution: {' '.join(cmd)}")

            # Exécuter PyInstaller
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=1800  # 30 minutes timeout
            )

            if result.returncode == 0:
                logger.info("Build réussi!")
                if result.stdout:
                    logger.info(f"Sortie PyInstaller:\n{result.stdout}")
                return True
            else:
                logger.error("Échec du build!")
                if result.stderr:
                    logger.error(f"Erreur PyInstaller:\n{result.stderr}")
                if result.stdout:
                    logger.error(f"Sortie PyInstaller:\n{result.stdout}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("Timeout lors du build (30 minutes)")
            return False
        except Exception as e:
            logger.error(f"Erreur lors du build: {e}")
            return False
            
    def post_build_tasks(self):
        """Tâches post-build"""
        logger.info("Exécution des tâches post-build...")

        try:
            # Vérifier que le répertoire dist existe
            if not self.dist_dir.exists():
                logger.error("Le répertoire dist n'existe pas")
                return False

            # Copier les fichiers de configuration
            config_files = [
                "config_advanced.json",
                "requirements.txt",
                "README.md",
                "config.json"
            ]

            for config_file in config_files:
                src = self.project_root / config_file
                if src.exists():
                    try:
                        dst = self.dist_dir / config_file
                        shutil.copy2(src, dst)
                        logger.info(f"Copié: {config_file}")
                    except Exception as e:
                        logger.warning(f"Impossible de copier {config_file}: {e}")

            # Créer les dossiers nécessaires
            folders_to_create = ["data", "logs", "backups", "temp"]

            for folder in folders_to_create:
                try:
                    folder_path = self.dist_dir / folder
                    folder_path.mkdir(exist_ok=True)
                    logger.info(f"Dossier créé: {folder}")
                except Exception as e:
                    logger.warning(f"Impossible de créer le dossier {folder}: {e}")

            # Vérifier que l'exécutable a été créé
            exe_path = self.dist_dir / f"{self.config['app_name']}.exe"
            if not exe_path.exists():
                logger.error("L'exécutable n'a pas été créé")
                return False

            logger.info("Tâches post-build terminées avec succès")
            return True

        except Exception as e:
            logger.error(f"Erreur lors des tâches post-build: {e}")
            return False
        
    def create_installer(self):
        """Crée un installateur (optionnel)"""
        logger.info("Création de l'installateur...")
        
        # Ici, vous pourriez intégrer NSIS, Inno Setup, ou WiX
        # Pour l'instant, on crée juste un script batch simple
        
        batch_content = f'''@echo off
echo Installation de {self.config["app_name"]}...
echo.

REM Créer les dossiers nécessaires
if not exist "%APPDATA%\\{self.config["app_name"]}" mkdir "%APPDATA%\\{self.config["app_name"]}"
if not exist "%APPDATA%\\{self.config["app_name"]}\\data" mkdir "%APPDATA%\\{self.config["app_name"]}\\data"
if not exist "%APPDATA%\\{self.config["app_name"]}\\logs" mkdir "%APPDATA%\\{self.config["app_name"]}\\logs"
if not exist "%APPDATA%\\{self.config["app_name"]}\\backups" mkdir "%APPDATA%\\{self.config["app_name"]}\\backups"

REM Copier les fichiers
copy "{self.config["app_name"]}.exe" "%APPDATA%\\{self.config["app_name"]}\\"
copy "config_advanced.json" "%APPDATA%\\{self.config["app_name"]}\\"

echo Installation terminée!
echo Vous pouvez lancer {self.config["app_name"]} depuis:
echo %APPDATA%\\{self.config["app_name"]}\\{self.config["app_name"]}.exe
pause
'''
        
        installer_path = self.dist_dir / "install.bat"
        with open(installer_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
            
        logger.info(f"Installateur créé: {installer_path}")
        
    def get_build_info(self) -> Dict[str, Any]:
        """Retourne les informations de build"""
        exe_path = self.dist_dir / f"{self.config['app_name']}.exe"
        
        info: Dict[str, Any] = {
            "app_name": self.config["app_name"],
            "version": self.config["version"],
            "build_date": datetime.now().isoformat(),
            "exe_exists": exe_path.exists(),
            "exe_size": exe_path.stat().st_size if exe_path.exists() else 0,
            "exe_path": str(exe_path)
        }
        
        return info
        
    def build(self) -> bool:
        """Processus de build complet"""
        logger.info("=== Début du processus de build ===")
        
        try:
            # Vérifications préliminaires
            if not self.check_dependencies():
                return False
                
            # Nettoyage
            if self.config.get("clean", True):
                self.clean_build_dirs()
                
            # Build
            if not self.build_executable():
                return False
                
            # Tâches post-build
            self.post_build_tasks()
            
            # Créer l'installateur
            self.create_installer()
            
            # Informations finales
            build_info = self.get_build_info()
            logger.info("=== Build terminé avec succès ===")
            logger.info(f"Exécutable: {build_info['exe_path']}")
            logger.info(f"Taille: {build_info['exe_size'] / 1024 / 1024:.1f} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors du build: {e}")
            return False

def test_build_environment():
    """Teste l'environnement de build"""
    logger.info("=== Test de l'environnement de build ===")

    builder = BuildManager()

    # Test 1: Vérification des dépendances
    logger.info("Test 1: Vérification des dépendances")
    if not builder.check_dependencies():
        logger.error("[ECHEC] Test des dépendances échoué")
        return False
    logger.info("[SUCCES] Test des dépendances réussi")

    # Test 2: Vérification du script principal
    logger.info("Test 2: Vérification du script principal")
    if not builder.verify_main_script():
        logger.error("[ECHEC] Test du script principal échoué")
        return False
    logger.info("[SUCCES] Test du script principal réussi")

    # Test 3: Création des répertoires
    logger.info("Test 3: Création des répertoires")
    try:
        builder.dist_dir.mkdir(exist_ok=True)
        builder.build_dir.mkdir(exist_ok=True)
        logger.info("[SUCCES] Test des répertoires réussi")
    except Exception as e:
        logger.error(f"[ECHEC] Test des répertoires échoué: {e}")
        return False

    logger.info("=== Tous les tests réussis ===")
    return True

def main():
    """Fonction principale"""
    builder = BuildManager()

    # Options de ligne de commande
    if len(sys.argv) > 1:
        if sys.argv[1] == "--clean":
            logger.info("Nettoyage des répertoires de build...")
            builder.clean_build_dirs()
            logger.info("Nettoyage terminé")
            return
        elif sys.argv[1] == "--check":
            logger.info("Vérification des dépendances...")
            success = builder.check_dependencies()
            if success:
                logger.info("[SUCCES] Toutes les dépendances sont installées")
            else:
                logger.error("[ECHEC] Des dépendances sont manquantes")
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "--test":
            logger.info("Test de l'environnement de build...")
            success = test_build_environment()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "--help":
            print("""
Usage: python build_executable.py [OPTIONS]

Options:
  --clean    Nettoie les répertoires de build
  --check    Vérifie les dépendances
  --test     Teste l'environnement de build
  --help     Affiche cette aide

Sans option: Lance le build complet
            """)
            return

    # Build complet
    logger.info("Démarrage du build complet...")
    success = builder.build()

    if success:
        logger.info("[SUCCES] Build terminé avec succès!")
    else:
        logger.error("[ECHEC] Build échoué!")

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()



