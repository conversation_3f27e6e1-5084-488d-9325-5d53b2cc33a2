#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour explorer le contenu exact des dossiers COMMUN et DATA2
"""

import os
from pathlib import Path

def explorer_dossiers():
    """Explore le contenu des dossiers COMMUN et DATA2"""
    print("🔍 EXPLORATION DÉTAILLÉE DES DOSSIERS")
    print("=" * 60)
    
    bureau = Path("C:/Users/<USER>/Desktop")
    
    if not bureau.exists():
        print(f"❌ Bureau non trouvé: {bureau}")
        return
    
    print(f"📂 Bureau: {bureau}")
    
    # Explorer dossier COMMUN
    dossier_commun = bureau / "COMMUN"
    print(f"\n📁 DOSSIER COMMUN: {dossier_commun}")
    print("-" * 50)
    
    if dossier_commun.exists():
        print("✅ Dossier COMMUN existe")
        try:
            fichiers = list(dossier_commun.iterdir())
            print(f"📄 {len(fichiers)} fichier(s) trouvé(s):")
            
            for fichier in fichiers:
                if fichier.is_file():
                    taille = fichier.stat().st_size
                    print(f"  📄 {fichier.name} ({taille} octets)")
                    
                    # Analyser le contenu si c'est un fichier texte
                    if fichier.suffix.lower() in ['.json', '.ini', '.xml', '.txt', '.cfg']:
                        analyser_fichier(fichier)
                        
                elif fichier.is_dir():
                    print(f"  📁 {fichier.name}/")
                    
        except PermissionError:
            print("❌ Accès refusé au dossier COMMUN")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    else:
        print("❌ Dossier COMMUN n'existe pas")
    
    # Explorer dossier DATA2
    dossier_data2 = bureau / "DATA2"
    print(f"\n📁 DOSSIER DATA2: {dossier_data2}")
    print("-" * 50)
    
    if dossier_data2.exists():
        print("✅ Dossier DATA2 existe")
        try:
            fichiers = list(dossier_data2.iterdir())
            print(f"📄 {len(fichiers)} fichier(s) trouvé(s):")
            
            for fichier in fichiers:
                if fichier.is_file():
                    taille = fichier.stat().st_size
                    print(f"  📄 {fichier.name} ({taille} octets)")
                    
                    # Analyser le contenu si c'est un fichier texte
                    if fichier.suffix.lower() in ['.json', '.ini', '.xml', '.txt', '.cfg']:
                        analyser_fichier(fichier)
                        
                elif fichier.is_dir():
                    print(f"  📁 {fichier.name}/")
                    
        except PermissionError:
            print("❌ Accès refusé au dossier DATA2")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    else:
        print("❌ Dossier DATA2 n'existe pas")
    
    # Explorer tous les dossiers du bureau pour trouver des configs
    print(f"\n🔍 RECHERCHE GLOBALE SUR LE BUREAU")
    print("-" * 50)
    
    try:
        tous_elements = list(bureau.iterdir())
        dossiers_config = []
        fichiers_config = []
        
        for element in tous_elements:
            if element.is_dir():
                nom = element.name.lower()
                if any(mot in nom for mot in ['config', 'param', 'setting', 'data', 'commun', 'immob']):
                    dossiers_config.append(element)
            elif element.is_file():
                if element.suffix.lower() in ['.json', '.xml', '.ini', '.cfg', '.conf', '.db']:
                    fichiers_config.append(element)
        
        if dossiers_config:
            print(f"📁 Dossiers suspects trouvés:")
            for dossier in dossiers_config:
                print(f"  📁 {dossier.name}")
                explorer_dossier_recursif(dossier)
        
        if fichiers_config:
            print(f"📄 Fichiers de config trouvés:")
            for fichier in fichiers_config:
                print(f"  📄 {fichier.name}")
                analyser_fichier(fichier)
                
    except Exception as e:
        print(f"❌ Erreur exploration bureau: {e}")

def explorer_dossier_recursif(dossier, niveau=1, max_niveau=2):
    """Explore un dossier de manière récursive"""
    if niveau > max_niveau:
        return
    
    indent = "  " * niveau
    
    try:
        for element in dossier.iterdir():
            if element.is_file():
                taille = element.stat().st_size
                print(f"{indent}📄 {element.name} ({taille} octets)")
                
                if element.suffix.lower() in ['.json', '.xml', '.ini', '.cfg', '.conf', '.txt']:
                    analyser_fichier(element, indent + "  ")
                    
            elif element.is_dir() and niveau < max_niveau:
                print(f"{indent}📁 {element.name}/")
                explorer_dossier_recursif(element, niveau + 1, max_niveau)
                
    except PermissionError:
        print(f"{indent}❌ Accès refusé")
    except Exception as e:
        print(f"{indent}❌ Erreur: {e}")

def analyser_fichier(fichier, indent="  "):
    """Analyse le contenu d'un fichier"""
    try:
        print(f"{indent}🔍 Analyse de {fichier.name}:")
        
        with open(fichier, 'r', encoding='utf-8') as f:
            contenu = f.read(1000)  # Lire les premiers 1000 caractères
        
        # Afficher un aperçu du contenu
        lignes = contenu.split('\n')[:5]  # Premières 5 lignes
        for ligne in lignes:
            if ligne.strip():
                print(f"{indent}  {ligne.strip()[:80]}...")
        
        # Rechercher des mots-clés importants
        mots_cles = ['secteur', 'immob', 'config', 'param', 'database', 'table', 'field']
        contenu_lower = contenu.lower()
        mots_trouves = [mot for mot in mots_cles if mot in contenu_lower]
        
        if mots_trouves:
            print(f"{indent}🎯 Mots-clés trouvés: {', '.join(mots_trouves)}")
        
        # Si le fichier semble important, afficher plus de contenu
        if any(mot in fichier.name.lower() for mot in ['config', 'param', 'setting', 'secteur']):
            print(f"{indent}📋 CONTENU COMPLET:")
            print(f"{indent}" + "=" * 40)
            with open(fichier, 'r', encoding='utf-8') as f:
                contenu_complet = f.read()
                for ligne in contenu_complet.split('\n'):
                    print(f"{indent}{ligne}")
            print(f"{indent}" + "=" * 40)
        
    except UnicodeDecodeError:
        try:
            with open(fichier, 'r', encoding='latin-1') as f:
                contenu = f.read(500)
            print(f"{indent}📄 Fichier en latin-1: {contenu[:100]}...")
        except:
            print(f"{indent}❌ Impossible de lire le fichier (encodage)")
    except Exception as e:
        print(f"{indent}❌ Erreur lecture: {e}")

if __name__ == "__main__":
    explorer_dossiers()
