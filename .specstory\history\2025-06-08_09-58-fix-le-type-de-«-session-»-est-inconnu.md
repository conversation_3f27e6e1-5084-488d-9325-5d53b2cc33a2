<!-- Generated by SpecStory -->

# /fix Le type de « session » est inconnu (2025-06-08 10:58:58)

_**User**_

/fix Le type de « session » est inconnu

---

_**Assistant**_

### [user_management.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/auth/user_management.py)

Déclarez explicitement le type de `self.session` dans la classe `UserManager` pour résoudre le problème de type inconnu. Importez `Session` depuis SQLAlchemy.

````python
# ...existing code...
from sqlalchemy.orm import Session  # Ajout de l'import pour le typage

class UserManager:
    def __init__(self):
        self.session: Session = get_db_session()
# ...existing code...
````

---

