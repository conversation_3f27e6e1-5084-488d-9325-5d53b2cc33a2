import os
import sqlite3
from pathlib import Path
from typing import Any, <PERSON>, Tuple, Optional, Dict
from dataclasses import dataclass

@dataclass
class SQLMetadata:
    alias: str = ""
    live_answer: bool = False
    answer_path: Optional[str] = None
    db_type: Optional[str] = None
    constrained: Optional[bool] = None
    aux_tables: Optional[bool] = None
    run_mode: Optional[str] = None

class SQLQuery:
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.metadata = SQLMetadata()
        self.sql_content = ""
        self._parse_file()
    
    def _parse_file(self):
        with open(self.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extraction des métadonnées
        metadata_block = content.split('*/')[0].strip('/*').strip()
        for line in metadata_block.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            if ':' in line:
                key, value = [part.strip() for part in line.split(':', 1)]
                key = key.lower()
                
                if key == 'alias':
                    self.metadata.alias = value
                elif key == 'liveanswer':
                    self.metadata.live_answer = value.upper() == 'TRUE'
                elif key == 'answer':
                    self.metadata.answer_path = value
                elif key == 'type':
                    self.metadata.db_type = value
                elif key == 'constrained':
                    self.metadata.constrained = value.upper() == 'TRUE'
                elif key == 'auxtables':
                    self.metadata.aux_tables = value.upper() == 'TRUE'
                elif key == 'runmode':
                    self.metadata.run_mode = value
        
        # Extraction du SQL
        self.sql_content = content.split('*/')[1].strip()

class SQLManager:
    def __init__(self, sql_folder: str):
        self.sql_folder = Path(sql_folder)
        self.queries: Dict[str, SQLQuery] = {}
        self._load_queries()
    
    def _load_queries(self):
        for sql_file in self.sql_folder.glob('*.SQL'):
            query = SQLQuery(sql_file)
            self.queries[sql_file.stem.upper()] = query
    
    def get_query(self, query_name: str) -> Optional[SQLQuery]:
        return self.queries.get(query_name.upper())
    
    def execute_query(self, query_name: str, connection, params: dict = None):
        query = self.get_query(query_name)
        if not query:
            raise ValueError(f"Query {query_name} not found")
        
        sql = query.sql_content
        if params:
            sql = sql.format(**params)
        
        cursor = connection.cursor()
        cursor.execute(sql)
        
        if query.metadata.live_answer:
            return cursor.fetchall()
        return None

    def get_queries_by_alias(self, alias: str) -> Dict[str, SQLQuery]:
        return {name: q for name, q in self.queries.items() if q.metadata.alias == alias}

# Exemple d'utilisation
if __name__ == "__main__":
    # Configuration
    sql_dir = "chemin/vers/vos/fichiers/sql"
    manager = SQLManager(sql_dir)
    
    # Accès à une requête
    reforme_query = manager.get_query("REFORME")
    print(f"Métadonnées REFORME: {reforme_query.metadata}")
    print(f"SQL REFORME: {reforme_query.sql_content}")
    
    sql_query = manager.get_query("SQL")
    print(f"\nMétadonnées SQL: {sql_query.metadata}")
    print(f"SQL complet: {sql_query.sql_content}")
    
    # Lister toutes les requêtes avec l'alias 'TRAVAIL'
    travail_queries = manager.get_queries_by_alias("TRAVAIL")
    print("\nRequêtes avec alias 'TRAVAIL':")
    for name in travail_queries:
        print(f"- {name}")
