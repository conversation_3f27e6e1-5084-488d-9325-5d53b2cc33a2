<!-- Generated by SpecStory -->

# /fix L’importation « Qt » n’a pas été accédée (2025-06-08 11:10:52)

_**User**_

/fix L’importation « Qt » n’a pas été accédée

---

_**Assistant**_

### [fournisseur.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/fournisseur.py)

Supprimer l'import inutile de `Qt` car il n'est pas utilisé dans ce fichier.

````python
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox
)
from PySide6.QtCore import QTimer
# ...existing code...
````

---

