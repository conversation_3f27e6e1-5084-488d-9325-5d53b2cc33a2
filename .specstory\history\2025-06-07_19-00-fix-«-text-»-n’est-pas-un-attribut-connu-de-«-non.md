<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-07 20:00:32)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [main.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/main.py)

Le problème vient du fait que `item_id` peut être `None`. Il faut donc vérifier que `item_id` n'est pas `None` avant d'appeler `.text()` dessus, sinon une erreur se produit.

Voici la correction à appliquer :

````python
# ...existing code...
        item_id = self.table.item(row, 0)
        if item_id is None:
            QMessageBox.warning(self, "Erreur", "Impossible de trouver l'ID du bien à supprimer.")
            return
        user_id = item_id.text()
# ...existing code...
````

<PERSON><PERSON>, on évite d'appeler `.text()` sur un objet `None` et on affiche un message d'erreur si la cellule est vide.

---

