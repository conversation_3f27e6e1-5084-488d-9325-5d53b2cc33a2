#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'interface graphique GestImmob
Encodage: UTF-8
Auteur: Système de Gestion Immobilière
Version: 1.0.0
"""

import sys
from pathlib import Path

# Ajouter les chemins nécessaires
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))
sys.path.append(str(current_dir / "src"))
sys.path.append(str(current_dir / "modules"))

def test_gui() -> int:
    """Test de l'interface graphique"""
    try:
        from PySide6.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout,
            QHBoxLayout, QLabel, QPushButton, QTableWidget,
            QTextEdit, QTableWidgetItem
        )
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        
        print("[OK] Imports PySide6 réussis")
        
        # Créer l'application
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("[OK] Application Qt créée")
        
        # Créer une fenêtre de test
        class TestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("GestImmob - Test Interface")
                self.setGeometry(100, 100, 800, 600)
                
                # Widget central
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                # Layout principal
                layout = QVBoxLayout(central_widget)
                
                # Titre
                title = QLabel("GestImmob - Interface de Test")
                title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
                title.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(title)
                
                # Boutons de test
                buttons_layout = QHBoxLayout()
                
                btn_test1 = QPushButton("Test Base de Données")
                btn_test1.clicked.connect(self.test_database)
                buttons_layout.addWidget(btn_test1)
                
                btn_test2 = QPushButton("Test Configuration")
                btn_test2.clicked.connect(self.test_config)
                buttons_layout.addWidget(btn_test2)
                
                btn_quit = QPushButton("Quitter")
                btn_quit.clicked.connect(self.close)
                buttons_layout.addWidget(btn_quit)
                
                layout.addLayout(buttons_layout)
                
                # Zone de résultats
                self.results = QTextEdit()
                self.results.setReadOnly(True)
                layout.addWidget(self.results)
                
                # Tableau de test
                self.table = QTableWidget(5, 3)
                self.table.setHorizontalHeaderLabels(["ID", "Nom", "Valeur"])
                layout.addWidget(self.table)
                
                self.log("Interface graphique initialisée avec succès !")
                
            def log(self, message: str) -> None:
                """Ajoute un message aux résultats"""
                self.results.append(f"[{self.get_time()}] {message}")

            def get_time(self) -> str:
                """Retourne l'heure actuelle"""
                from datetime import datetime
                return datetime.now().strftime("%H:%M:%S")
                
            def test_database(self) -> None:
                """Test de la base de données"""
                try:
                    import sqlite3
                    conn = sqlite3.connect(':memory:')
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        CREATE TABLE test_immobilisations (
                            id INTEGER PRIMARY KEY,
                            designation TEXT,
                            valeur REAL
                        )
                    ''')
                    
                    # Insérer des données de test
                    test_data = [
                        (1, "Ordinateur portable", 1200.50),
                        (2, "Bureau", 350.00),
                        (3, "Chaise", 150.75)
                    ]
                    
                    cursor.executemany(
                        'INSERT INTO test_immobilisations VALUES (?, ?, ?)',
                        test_data
                    )
                    
                    # Lire et afficher dans le tableau
                    cursor.execute('SELECT * FROM test_immobilisations')
                    results = cursor.fetchall()
                    
                    self.table.setRowCount(len(results))
                    for row, (id_val, designation, valeur) in enumerate(results):
                        self.table.setItem(row, 0, QTableWidgetItem(str(id_val)))
                        self.table.setItem(row, 1, QTableWidgetItem(designation))
                        self.table.setItem(row, 2, QTableWidgetItem(f"{valeur:.2f}"))
                    
                    conn.close()
                    self.log("[OK] Test base de données réussi - Données affichées dans le tableau")
                    
                except Exception as e:
                    self.log(f"[ERREUR] Erreur base de données: {e}")
                    
            def test_config(self) -> None:
                """Test de la configuration"""
                try:
                    import json
                    config_file = Path("config_advanced.json")
                    
                    if config_file.exists():
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        app_name = config.get("application", {}).get("name", "N/A")
                        version = config.get("application", {}).get("version", "N/A")
                        theme = config.get("ui", {}).get("theme", "N/A")
                        
                        self.log(f"[OK] Configuration chargée:")
                        self.log(f"  Application: {app_name}")
                        self.log(f"  Version: {version}")
                        self.log(f"  Thème: {theme}")
                    else:
                        self.log("⚠ Fichier de configuration non trouvé")
                        
                except Exception as e:
                    self.log(f"[ERREUR] Erreur configuration: {e}")
        
        # Créer et afficher la fenêtre
        window = TestWindow()
        window.show()
        
        print("[OK] Fenêtre de test créée et affichée")
        print("\n[SUCCES] Test de l'interface graphique réussi !")
        print("Fermez la fenêtre pour terminer le test.")
        
        # Lancer la boucle d'événements
        return app.exec()
        
    except Exception as e:
        print(f"[ERREUR] Erreur interface graphique: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(test_gui())
