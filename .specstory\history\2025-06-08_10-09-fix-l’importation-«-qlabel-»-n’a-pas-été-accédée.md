<!-- Generated by SpecStory -->

# /fix L’importation « QLabel » n’a pas été accédée (2025-06-08 11:09:51)

_**User**_

/fix L’importation « QLabel » n’a pas été accédée

---

_**Assistant**_

### [document.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/document.py)

Supprimer l'import inutile de `QLabel` car il n'est pas utilisé dans le fichier.

````python
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox, QFileDialog
)
# ...existing code...
````

---

