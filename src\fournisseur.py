from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout,
    QMessageBox, QLineEdit, QDialog, QFormLayout, QDialogButtonBox, QComboBox
)
from PySide6.QtCore import QTimer
from PySide6.QtGui import QColor, QCloseEvent  # Ajout de QCloseEvent
import logging
import json
import csv

class FournisseurWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Gestion des Fournisseurs")
        self.resize(1100, 600)
        layout = QVBoxLayout(self)
        # Table enrichie inspirée ERP (Odoo/Dolibarr)
        self.table = QTableWidget(0, 12)
        self.table.setHorizontalHeaderLabels([
            "ID", "Nom", "Adresse", "Téléphone", "<PERSON>ail", "<PERSON>", "SIRET", "Contact", "<PERSON><PERSON><PERSON>ie",
            "Statut", "Dernière modif.", "Alerte"
        ])
        layout.addWidget(self.table)
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("Ajouter")
        self.edit_btn = QPushButton("Modifier")
        self.delete_btn = QPushButton("Supprimer")
        self.sync_btn = QPushButton("Synchroniser")
        self.export_btn = QPushButton("Exporter CSV")  # Nouveau bouton
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.sync_btn)
        btn_layout.addWidget(self.export_btn)
        layout.addLayout(btn_layout)

        # Recherche rapide
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Recherche fournisseur par nom...")
        self.search_btn = QPushButton("Rechercher")
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_btn)
        layout.addLayout(search_layout)

        self.add_btn.clicked.connect(self.ajouter_fournisseur)
        self.edit_btn.clicked.connect(self.modifier_fournisseur)
        self.delete_btn.clicked.connect(self.supprimer_fournisseur)
        self.sync_btn.clicked.connect(self.synchroniser)
        self.export_btn.clicked.connect(self.exporter_csv)
        self.search_btn.clicked.connect(self.rechercher_fournisseur)
        self.table.itemChanged.connect(self.alerte_modification)

        self.changements_detectes = False
        self.timer_sync = QTimer()
        self.timer_sync.timeout.connect(self.sauvegarde_auto)
        self.timer_sync.start(180000)  # 3 min

        self.fichier_persistance = "fournisseurs.json"
        self.charger_fournisseurs()

    def charger_fournisseurs(self):
        try:
            with open(self.fichier_persistance, "r", encoding="utf-8") as f:
                fournisseurs = json.load(f)
                for fournisseur in fournisseurs:
                    row = self.table.rowCount()
                    self.table.insertRow(row)
                    for col, value in enumerate(fournisseur):
                        self.table.setItem(row, col, QTableWidgetItem(str(value)))
        except Exception:
            pass  # Pas de fichier ou erreur de lecture

    def sauvegarder_fournisseurs(self):
        fournisseurs: list[list[str]] = []  # Annotation explicite du type
        for row in range(self.table.rowCount()):
            fournisseur: list[str] = []  # Annotation explicite du type
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                fournisseur.append(item.text() if item else "")
            fournisseurs.append(fournisseur)
        with open(self.fichier_persistance, "w", encoding="utf-8") as f:
            json.dump(fournisseurs, f, ensure_ascii=False, indent=2)

    def exporter_csv(self):
        path = "fournisseurs_export.csv"
        with open(path, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            # Correction ici : éviter d'appeler .text() sur None
            writer.writerow([
                (header.text() if header is not None else "")
                for header in [self.table.horizontalHeaderItem(i) for i in range(self.table.columnCount())]
            ])
            for row in range(self.table.rowCount()):
                writer.writerow([
                    (item.text() if item is not None else "")
                    for item in [self.table.item(row, col) for col in range(self.table.columnCount())]
                ])
        QMessageBox.information(self, "Export CSV", f"Export effectué : {path}")

    def rechercher_fournisseur(self):
        nom_recherche = self.search_input.text().strip().lower()
        for row in range(self.table.rowCount()):
            nom = self.table.item(row, 1)
            self.table.setRowHidden(row, False)
            if nom and nom_recherche and nom_recherche not in nom.text().lower():
                self.table.setRowHidden(row, True)

    def ajouter_fournisseur(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajouter un fournisseur")
        form = QFormLayout(dialog)
        nom = QLineEdit()
        adresse = QLineEdit()
        telephone = QLineEdit()
        email = QLineEdit()
        type_f = QComboBox(); type_f.addItems(["Matériel", "Service", "Consultant", "Transport", "Autre"])
        siret = QLineEdit()
        registre_commerce = QLineEdit()
        nif = QLineEdit()
        compte_bancaire = QLineEdit()
        banque = QLineEdit()
        contact = QLineEdit()
        categorie = QComboBox(); categorie.addItems(["Principal", "Secondaire", "Occasionnel"])
        statut = QComboBox(); statut.addItems(["Actif", "Inactif", "En attente"])
        historique_achats = QLineEdit()
        historique_factures = QLineEdit()
        impaye = QLineEdit()
        date_creation = QLineEdit()
        form.addRow("Nom:", nom)
        form.addRow("Adresse:", adresse)
        form.addRow("Téléphone:", telephone)
        form.addRow("Email:", email)
        form.addRow("Type:", type_f)
        form.addRow("SIRET:", siret)
        form.addRow("Registre Commerce:", registre_commerce)
        form.addRow("NIF:", nif)
        form.addRow("Compte Bancaire:", compte_bancaire)
        form.addRow("Banque:", banque)
        form.addRow("Contact:", contact)
        form.addRow("Catégorie:", categorie)
        form.addRow("Statut:", statut)
        form.addRow("Historique Achats:", historique_achats)
        form.addRow("Historique Factures:", historique_factures)
        form.addRow("Impayé:", impaye)
        form.addRow("Date création:", date_creation)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        if dialog.exec() == 1:
            # Validation stricte
            if not nom.text().strip() or not telephone.text().strip():
                QMessageBox.warning(self, "Erreur", "Nom et téléphone obligatoires.")
                return
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(str(row+1)))
            self.table.setItem(row, 1, QTableWidgetItem(nom.text()))
            self.table.setItem(row, 2, QTableWidgetItem(adresse.text()))
            self.table.setItem(row, 3, QTableWidgetItem(telephone.text()))
            self.table.setItem(row, 4, QTableWidgetItem(email.text()))
            self.table.setItem(row, 5, QTableWidgetItem(type_f.currentText()))
            self.table.setItem(row, 6, QTableWidgetItem(siret.text()))
            # Ajouts des nouveaux champs dans les colonnes suivantes
            self.table.setItem(row, 7, QTableWidgetItem(registre_commerce.text()))
            self.table.setItem(row, 8, QTableWidgetItem(nif.text()))
            self.table.setItem(row, 9, QTableWidgetItem(compte_bancaire.text()))
            self.table.setItem(row, 10, QTableWidgetItem(banque.text()))
            self.table.setItem(row, 11, QTableWidgetItem(contact.text()))
            # Pour historique, impayé, date création, etc. (affichage dans une fenêtre dédiée ou colonne supplémentaire si besoin)
            # ...existing code...

    def modifier_fournisseur(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à modifier.")
            return
        row = selected[0].row()
        # Pré-remplir le formulaire
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier fournisseur")
        form = QFormLayout()
        from typing import Optional
        def safe_text(item: Optional[QTableWidgetItem]) -> str:
            if item is not None:
                text_attr = getattr(item, "text", None)
                if callable(text_attr):
                    try:
                        v = text_attr()
                        if v is not None:
                            return str(v)
                    except Exception:
                        return ""
            return ""
        nom = QLineEdit(str(safe_text(self.table.item(row, 1))))
        adresse = QLineEdit(str(safe_text(self.table.item(row, 2))))
        telephone = QLineEdit(str(safe_text(self.table.item(row, 3))))
        email = QLineEdit(str(safe_text(self.table.item(row, 4))))
        type_f = QComboBox(); type_f.addItems(["Matériel", "Service", "Consultant", "Transport", "Autre"])
        type_f.setCurrentText(str(safe_text(self.table.item(row, 5))))
        siret = QLineEdit(str(safe_text(self.table.item(row, 6))))
        contact = QLineEdit(str(safe_text(self.table.item(row, 7))))
        categorie = QComboBox(); categorie.addItems(["Principal", "Secondaire", "Occasionnel"])
        categorie.setCurrentText(str(safe_text(self.table.item(row, 8))))
        statut = QComboBox(); statut.addItems(["Actif", "Inactif", "En attente"])
        statut.setCurrentText(str(safe_text(self.table.item(row, 9))))
        form.addRow("Nom:", nom)
        form.addRow("Adresse:", adresse)
        form.addRow("Téléphone:", telephone)
        form.addRow("Email:", email)
        form.addRow("Type:", type_f)
        form.addRow("SIRET:", siret)
        form.addRow("Contact:", contact)
        form.addRow("Catégorie:", categorie)
        form.addRow("Statut:", statut)
        btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        btn_box.accepted.connect(dialog.accept)
        btn_box.rejected.connect(dialog.reject)
        form.addWidget(btn_box)
        dialog.setLayout(form)
        if dialog.exec() == 1:
            self.table.setItem(row, 1, QTableWidgetItem(nom.text()))
            self.table.setItem(row, 2, QTableWidgetItem(adresse.text()))
            self.table.setItem(row, 3, QTableWidgetItem(telephone.text()))
            self.table.setItem(row, 4, QTableWidgetItem(email.text()))
            self.table.setItem(row, 5, QTableWidgetItem(type_f.currentText()))
            self.table.setItem(row, 6, QTableWidgetItem(siret.text()))
            self.table.setItem(row, 7, QTableWidgetItem(contact.text()))
            self.table.setItem(row, 8, QTableWidgetItem(categorie.currentText()))
            self.table.setItem(row, 9, QTableWidgetItem(statut.currentText()))
            self.table.setItem(row, 10, QTableWidgetItem("Modifié"))
            self.table.setItem(row, 11, QTableWidgetItem(""))
            self.changements_detectes = True

    def supprimer_fournisseur(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "Erreur", "Sélectionnez une ligne à supprimer.")
            return
        row = selected[0].row()
        self.table.removeRow(row)
        self.changements_detectes = True

    def alerte_modification(self, item: QTableWidgetItem):
        # Détection d'anomalie simple : email vide ou téléphone non numérique
        if item.column() == 4 and not item.text():
            item.setBackground(QColor("red"))
            self.table.setItem(item.row(), 11, QTableWidgetItem("⚠️ Email manquant"))
            QMessageBox.warning(self, "Alerte", "Email manquant pour ce fournisseur.")
        elif item.column() == 3 and not item.text().isdigit():
            item.setBackground(QColor("red"))
            self.table.setItem(item.row(), 11, QTableWidgetItem("⚠️ Téléphone invalide"))
            QMessageBox.warning(self, "Alerte", "Téléphone invalide pour ce fournisseur.")
        else:
            item.setBackground(QColor("white"))
            self.table.setItem(item.row(), 11, QTableWidgetItem(""))
        self.changements_detectes = True

    def synchroniser(self):
        # ...logique de synchronisation multi-base ou API...
        QMessageBox.information(self, "Synchronisation", "Synchronisation effectuée avec succès.")
        self.changements_detectes = False

    def sauvegarde_auto(self):
        if self.changements_detectes:
            self.sauvegarder_fournisseurs()
            logging.info("Sauvegarde automatique des fournisseurs.")
            self.changements_detectes = False

    def closeEvent(self, event: QCloseEvent):
        if self.changements_detectes:
            self.sauvegarder_fournisseurs()
        event.accept()
