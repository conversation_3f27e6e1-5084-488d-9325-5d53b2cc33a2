#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Surveillance en temps réel pour éviter les interruptions de travail
"""

import os
import sys
import time
import threading
import subprocess
import psutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

class SurveillanceTempsReel:
    def __init__(self):
        self.surveillance_active = False
        self.processus_surveilles = {}
        self.alertes_actives = []
        self.sauvegardes_auto = []
        self.thread_surveillance = None
        
    def demarrer_surveillance(self):
        """Démarre la surveillance en temps réel"""
        print("🔍 DÉMARRAGE SURVEILLANCE TEMPS RÉEL")
        print("=" * 50)
        
        self.surveillance_active = True
        
        # Démarrer le thread de surveillance
        self.thread_surveillance = threading.Thread(target=self._boucle_surveillance, daemon=True)
        self.thread_surveillance.start()
        
        print("✅ Surveillance démarrée")
        print("📊 Monitoring actif:")
        print("  - Processus Python")
        print("  - Utilisation mémoire")
        print("  - Erreurs de syntaxe")
        print("  - Conflits de modules")
        print("  - Intégrité des fichiers")
        
        return True
    
    def _boucle_surveillance(self):
        """Boucle principale de surveillance"""
        while self.surveillance_active:
            try:
                # Surveillance toutes les 5 secondes
                self._surveiller_processus()
                self._surveiller_memoire()
                self._surveiller_fichiers()
                self._surveiller_syntaxe()
                self._effectuer_sauvegarde_auto()
                
                time.sleep(5)
                
            except Exception as e:
                print(f"⚠️ Erreur surveillance: {e}")
                time.sleep(10)
    
    def _surveiller_processus(self):
        """Surveille les processus Python actifs"""
        try:
            processus_python = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        if any('gestimmob' in arg.lower() or 'main.py' in arg for arg in cmdline):
                            processus_python.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'memory': proc.info['memory_info'].rss if proc.info['memory_info'] else 0,
                                'cmdline': ' '.join(cmdline)
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Détecter les processus en difficulté
            for proc in processus_python:
                # Alerte si utilisation mémoire > 500MB
                if proc['memory'] > 500 * 1024 * 1024:
                    self._generer_alerte('memoire_elevee', f"Processus {proc['pid']} utilise {proc['memory']//1024//1024}MB")
                
                # Stocker pour surveillance continue
                self.processus_surveilles[proc['pid']] = proc
                
        except Exception as e:
            print(f"⚠️ Erreur surveillance processus: {e}")
    
    def _surveiller_memoire(self):
        """Surveille l'utilisation mémoire globale"""
        try:
            memory = psutil.virtual_memory()
            
            # Alerte si utilisation > 85%
            if memory.percent > 85:
                self._generer_alerte('memoire_systeme', f"Utilisation mémoire système: {memory.percent:.1f}%")
            
            # Alerte si mémoire disponible < 1GB
            if memory.available < 1024 * 1024 * 1024:
                self._generer_alerte('memoire_faible', f"Mémoire disponible: {memory.available//1024//1024}MB")
                
        except Exception as e:
            print(f"⚠️ Erreur surveillance mémoire: {e}")
    
    def _surveiller_fichiers(self):
        """Surveille l'intégrité des fichiers critiques"""
        try:
            fichiers_critiques = [
                'src/main.py',
                'src/config.py',
                'src/database/db_connection.py',
                'config/01_application/app_settings.json'
            ]
            
            for fichier in fichiers_critiques:
                if os.path.exists(fichier):
                    # Vérifier la taille (détection de corruption)
                    taille = os.path.getsize(fichier)
                    if taille == 0:
                        self._generer_alerte('fichier_vide', f"Fichier critique vide: {fichier}")
                    
                    # Vérifier les permissions
                    if not os.access(fichier, os.R_OK):
                        self._generer_alerte('permission_lecture', f"Impossible de lire: {fichier}")
                else:
                    self._generer_alerte('fichier_manquant', f"Fichier critique manquant: {fichier}")
                    
        except Exception as e:
            print(f"⚠️ Erreur surveillance fichiers: {e}")
    
    def _surveiller_syntaxe(self):
        """Surveille la syntaxe des fichiers Python modifiés récemment"""
        try:
            # Vérifier les fichiers modifiés dans les 60 dernières secondes
            maintenant = time.time()
            
            for py_file in Path('.').rglob('*.py'):
                if not any(part.startswith('.') for part in py_file.parts):
                    try:
                        mtime = os.path.getmtime(py_file)
                        if maintenant - mtime < 60:  # Modifié dans la dernière minute
                            # Vérifier la syntaxe
                            with open(py_file, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            try:
                                compile(content, str(py_file), 'exec')
                            except SyntaxError as e:
                                self._generer_alerte('erreur_syntaxe', f"Erreur syntaxe dans {py_file}: {e}")
                    
                    except Exception:
                        continue
                        
        except Exception as e:
            print(f"⚠️ Erreur surveillance syntaxe: {e}")
    
    def _effectuer_sauvegarde_auto(self):
        """Effectue des sauvegardes automatiques périodiques"""
        try:
            # Sauvegarde toutes les 10 minutes
            if not hasattr(self, '_derniere_sauvegarde'):
                self._derniere_sauvegarde = 0
            
            maintenant = time.time()
            if maintenant - self._derniere_sauvegarde > 600:  # 10 minutes
                self._creer_sauvegarde_incrementale()
                self._derniere_sauvegarde = maintenant
                
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde auto: {e}")
    
    def _creer_sauvegarde_incrementale(self):
        """Crée une sauvegarde incrémentale"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = Path(f"backup_auto_{timestamp}")
            
            # Sauvegarder seulement les fichiers modifiés récemment
            fichiers_sauvegardes = 0
            
            for py_file in Path('.').rglob('*.py'):
                if not any(part.startswith('.') for part in py_file.parts):
                    try:
                        # Sauvegarder si modifié dans les 30 dernières minutes
                        if time.time() - os.path.getmtime(py_file) < 1800:
                            backup_path = backup_dir / py_file
                            backup_path.parent.mkdir(parents=True, exist_ok=True)
                            
                            import shutil
                            shutil.copy2(py_file, backup_path)
                            fichiers_sauvegardes += 1
                    except Exception:
                        continue
            
            if fichiers_sauvegardes > 0:
                self.sauvegardes_auto.append({
                    'timestamp': timestamp,
                    'path': str(backup_dir),
                    'fichiers': fichiers_sauvegardes
                })
                
                print(f"💾 Sauvegarde auto: {fichiers_sauvegardes} fichier(s) → {backup_dir}")
                
                # Limiter le nombre de sauvegardes (garder les 10 dernières)
                if len(self.sauvegardes_auto) > 10:
                    ancienne = self.sauvegardes_auto.pop(0)
                    try:
                        import shutil
                        shutil.rmtree(ancienne['path'], ignore_errors=True)
                    except:
                        pass
                        
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde incrémentale: {e}")
    
    def _generer_alerte(self, type_alerte: str, message: str):
        """Génère une alerte et prend des mesures correctives"""
        alerte = {
            'timestamp': datetime.now().isoformat(),
            'type': type_alerte,
            'message': message
        }
        
        # Éviter les doublons d'alertes
        if not any(a['type'] == type_alerte and a['message'] == message for a in self.alertes_actives[-5:]):
            self.alertes_actives.append(alerte)
            print(f"🚨 ALERTE: {message}")
            
            # Actions correctives automatiques
            self._action_corrective(type_alerte, message)
    
    def _action_corrective(self, type_alerte: str, message: str):
        """Prend des actions correctives automatiques"""
        try:
            if type_alerte == 'erreur_syntaxe':
                # Restaurer depuis la dernière sauvegarde
                self._restaurer_fichier_depuis_sauvegarde(message)
            
            elif type_alerte == 'memoire_elevee':
                # Suggérer un redémarrage ou optimisation
                print("💡 Suggestion: Redémarrer l'application pour libérer la mémoire")
            
            elif type_alerte == 'fichier_manquant':
                # Tenter de recréer le fichier depuis un template
                self._recreer_fichier_critique(message)
            
            elif type_alerte == 'memoire_systeme':
                # Nettoyer les caches temporaires
                self._nettoyer_caches()
                
        except Exception as e:
            print(f"⚠️ Erreur action corrective: {e}")
    
    def _restaurer_fichier_depuis_sauvegarde(self, message: str):
        """Restaure un fichier depuis la dernière sauvegarde"""
        try:
            # Extraire le nom du fichier du message
            if 'dans ' in message:
                fichier = message.split('dans ')[1].split(':')[0].strip()
                
                # Chercher dans les sauvegardes récentes
                for sauvegarde in reversed(self.sauvegardes_auto[-3:]):
                    backup_file = Path(sauvegarde['path']) / fichier
                    if backup_file.exists():
                        import shutil
                        shutil.copy2(backup_file, fichier)
                        print(f"🔄 Fichier restauré: {fichier}")
                        return True
                        
        except Exception as e:
            print(f"⚠️ Erreur restauration: {e}")
        return False
    
    def _recreer_fichier_critique(self, message: str):
        """Recrée un fichier critique manquant"""
        try:
            if 'main.py' in message:
                self._creer_main_py_minimal()
            elif 'config.py' in message:
                self._creer_config_py_minimal()
                
        except Exception as e:
            print(f"⚠️ Erreur recréation fichier: {e}")
    
    def _creer_main_py_minimal(self):
        """Crée un main.py minimal"""
        content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Point d'entrée principal GestImmob - Version de récupération
"""

import sys
from pathlib import Path

def main():
    """Fonction principale de récupération"""
    print("🔄 Mode de récupération GestImmob")
    print("Le fichier principal a été restauré automatiquement")
    
    # Tenter de charger l'application normale
    try:
        from src.core.app import main as app_main
        return app_main()
    except ImportError:
        print("⚠️ Impossible de charger l'application complète")
        print("Veuillez vérifier l'installation")
        return False

if __name__ == "__main__":
    main()
'''
        
        with open('src/main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("🔄 main.py minimal créé")
    
    def _nettoyer_caches(self):
        """Nettoie les caches temporaires"""
        try:
            # Nettoyer les fichiers __pycache__
            for pycache in Path('.').rglob('__pycache__'):
                import shutil
                shutil.rmtree(pycache, ignore_errors=True)
            
            print("🧹 Caches nettoyés")
            
        except Exception as e:
            print(f"⚠️ Erreur nettoyage: {e}")
    
    def arreter_surveillance(self):
        """Arrête la surveillance"""
        print("\n🛑 ARRÊT DE LA SURVEILLANCE")
        self.surveillance_active = False
        
        if self.thread_surveillance:
            self.thread_surveillance.join(timeout=5)
        
        print("✅ Surveillance arrêtée")
    
    def generer_rapport_surveillance(self):
        """Génère un rapport de surveillance"""
        print(f"\n📊 RAPPORT DE SURVEILLANCE")
        print("=" * 50)
        
        print(f"🔍 Surveillance active: {'✅ Oui' if self.surveillance_active else '❌ Non'}")
        print(f"🚨 Alertes générées: {len(self.alertes_actives)}")
        print(f"💾 Sauvegardes auto: {len(self.sauvegardes_auto)}")
        print(f"🖥️ Processus surveillés: {len(self.processus_surveilles)}")
        
        if self.alertes_actives:
            print(f"\n🚨 DERNIÈRES ALERTES:")
            for alerte in self.alertes_actives[-5:]:
                print(f"  - {alerte['timestamp']}: {alerte['message']}")
        
        if self.sauvegardes_auto:
            print(f"\n💾 SAUVEGARDES AUTOMATIQUES:")
            for sauvegarde in self.sauvegardes_auto[-3:]:
                print(f"  - {sauvegarde['timestamp']}: {sauvegarde['fichiers']} fichier(s)")
        
        return {
            'alertes': len(self.alertes_actives),
            'sauvegardes': len(self.sauvegardes_auto),
            'processus': len(self.processus_surveilles)
        }

def main():
    """Fonction principale de surveillance"""
    print("🔍 SURVEILLANCE TEMPS RÉEL GESTIMMOB")
    print("=" * 80)
    print("Surveillance continue pour éviter les interruptions de travail")
    
    surveillance = SurveillanceTempsReel()
    
    try:
        # Démarrer la surveillance
        surveillance.demarrer_surveillance()
        
        print(f"\n📋 COMMANDES DISPONIBLES:")
        print(f"  - 'status' : Afficher le statut")
        print(f"  - 'rapport' : Générer un rapport")
        print(f"  - 'stop' : Arrêter la surveillance")
        print(f"  - 'quit' : Quitter")
        
        # Boucle interactive
        while surveillance.surveillance_active:
            try:
                commande = input(f"\n> ").strip().lower()
                
                if commande == 'status':
                    print(f"🔍 Surveillance: {'✅ Active' if surveillance.surveillance_active else '❌ Inactive'}")
                    print(f"🚨 Alertes: {len(surveillance.alertes_actives)}")
                    print(f"💾 Sauvegardes: {len(surveillance.sauvegardes_auto)}")
                
                elif commande == 'rapport':
                    surveillance.generer_rapport_surveillance()
                
                elif commande in ['stop', 'quit']:
                    break
                
                elif commande == 'help':
                    print(f"📋 Commandes: status, rapport, stop, quit, help")
                
                else:
                    print(f"❓ Commande inconnue. Tapez 'help' pour l'aide")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
        
        # Arrêter la surveillance
        surveillance.arreter_surveillance()
        
        # Rapport final
        rapport = surveillance.generer_rapport_surveillance()
        
        print(f"\n🎯 SURVEILLANCE TERMINÉE")
        print(f"✅ {rapport['sauvegardes']} sauvegarde(s) automatique(s) créée(s)")
        print(f"🚨 {rapport['alertes']} alerte(s) traitée(s)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur surveillance: {e}")
        surveillance.arreter_surveillance()
        return False

if __name__ == "__main__":
    main()
