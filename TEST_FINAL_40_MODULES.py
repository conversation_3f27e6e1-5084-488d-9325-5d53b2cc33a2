#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST FINAL ROBOT IA - 40 MODULES
Vérification complète système
"""

import os
import sys

def test_robot_ia_40_modules():
    """Test complet Robot IA 40 modules"""
    print("🧪 TEST FINAL ROBOT IA - 40 MODULES")
    print("=" * 45)
    print("👤 SamNord@110577")
    print("🤖 Vérification système complet")
    print()
    
    # Test 1: Vérification fichiers principaux
    print("📁 Test 1: Fichiers principaux...")
    fichiers_principaux = [
        "robot_ia_universel_40_modules.py",
        "LANCER_ROBOT_40_MODULES_FINAL.bat",
        "CREATION_40_MODULES.py",
        "GUIDE_ULTIME_40_MODULES.md"
    ]
    
    fichiers_ok = 0
    for fichier in fichiers_principaux:
        if os.path.exists(fichier):
            print(f"   ✅ {fichier}: OK")
            fichiers_ok += 1
        else:
            print(f"   ❌ {fichier}: MANQUANT")
    
    print(f"   📊 Fichiers principaux: {fichiers_ok}/{len(fichiers_principaux)}")
    
    # Test 2: Vérification dossier modules
    print("\n📂 Test 2: Dossier modules...")
    if os.path.exists("modules"):
        modules_files = [f for f in os.listdir("modules") if f.endswith('.py')]
        print(f"   ✅ Dossier modules: OK")
        print(f"   📊 Modules Python: {len(modules_files)}")
        
        # Affichage quelques modules
        for i, module in enumerate(modules_files[:10]):
            print(f"      {i+1:2d}. {module}")
        if len(modules_files) > 10:
            print(f"      ... et {len(modules_files)-10} autres modules")
    else:
        print("   ❌ Dossier modules: MANQUANT")
    
    # Test 3: Test import application principale
    print("\n🤖 Test 3: Application principale...")
    try:
        if os.path.exists("robot_ia_universel_40_modules.py"):
            with open("robot_ia_universel_40_modules.py", 'r', encoding='utf-8') as f:
                content = f.read()
                if "RobotIAUniversel40Modules" in content:
                    print("   ✅ Classe principale: OK")
                if "modules_40" in content:
                    print("   ✅ Liste 40 modules: OK")
                if "SamNord@110577" in content:
                    print("   ✅ Utilisateur configuré: OK")
        else:
            print("   ❌ Application principale: MANQUANTE")
    except Exception as e:
        print(f"   ❌ Erreur lecture: {e}")
    
    # Test 4: Test modules critiques
    print("\n🔧 Test 4: Modules critiques...")
    modules_critiques = [
        "intelligence_ia_transcendante.py",
        "auto_evolution_autonome.py",
        "tests_automatiques.py",
        "auto_reparation.py"
    ]
    
    modules_critiques_ok = 0
    for module in modules_critiques:
        module_path = f"modules/{module}"
        if os.path.exists(module_path):
            print(f"   ✅ {module}: OK")
            modules_critiques_ok += 1
        else:
            print(f"   ❌ {module}: MANQUANT")
    
    print(f"   📊 Modules critiques: {modules_critiques_ok}/{len(modules_critiques)}")
    
    # Test 5: Résumé final
    print("\n🎯 RÉSUMÉ FINAL:")
    print(f"   📁 Fichiers principaux: {fichiers_ok}/{len(fichiers_principaux)}")
    if os.path.exists("modules"):
        modules_count = len([f for f in os.listdir("modules") if f.endswith('.py')])
        print(f"   📦 Modules Python: {modules_count}")
    print(f"   🔧 Modules critiques: {modules_critiques_ok}/{len(modules_critiques)}")
    
    # Calcul score global
    score_total = 0
    score_max = 0
    
    score_total += fichiers_ok
    score_max += len(fichiers_principaux)
    
    if os.path.exists("modules"):
        score_total += min(40, modules_count)
        score_max += 40
    
    score_total += modules_critiques_ok
    score_max += len(modules_critiques)
    
    score_percent = (score_total / score_max) * 100 if score_max > 0 else 0
    
    print(f"\n📈 SCORE GLOBAL: {score_percent:.1f}%")
    
    if score_percent >= 90:
        print("🌟 EXCELLENT ! Robot IA Transcendant opérationnel !")
    elif score_percent >= 70:
        print("✅ BON ! Robot IA fonctionnel avec quelques modules manquants")
    elif score_percent >= 50:
        print("⚠️ MOYEN ! Installation partielle - Compléter modules")
    else:
        print("❌ CRITIQUE ! Installation incomplète - Relancer création")
    
    print("\n🚀 INSTRUCTIONS LANCEMENT:")
    if os.path.exists("robot_ia_universel_40_modules.py"):
        print("1. Lancer: python robot_ia_universel_40_modules.py")
    if os.path.exists("LANCER_ROBOT_40_MODULES_FINAL.bat"):
        print("2. Ou double-cliquer: LANCER_ROBOT_40_MODULES_FINAL.bat")
    if os.path.exists("CREATION_40_MODULES.py"):
        print("3. Si modules manquants: python CREATION_40_MODULES.py")
    
    print("\n🔐 VOS IDENTIFIANTS:")
    print("👤 Utilisateur: SamNord@110577")
    print("🔑 Mot de passe: NorDine@22")
    
    print("\n🎉 TEST TERMINÉ !")
    return score_percent

if __name__ == "__main__":
    test_robot_ia_40_modules()
    input("\nAppuyez sur Entrée pour continuer...")
