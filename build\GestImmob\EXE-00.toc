('C:\\Users\\<USER>\\Desktop\\logiciel '
 'GestImmob\\gestion-software\\dist\\GestImmob.exe',
 False,
 False,
 False,
 'c:\\Users\\<USER>\\Desktop\\logiciel '
 'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 versioninfo.VSVersionInfo(ffi=versioninfo.FixedFileInfo(filevers=(2, 0, 0, 0), prodvers=(2, 0, 0, 0), mask=0x3f, flags=0x0, OS=0x40004, fileType=1, subtype=0x0, date=(0, 0)), kids=[versioninfo.StringFileInfo([versioninfo.StringTable('040904B0', [versioninfo.StringStruct('CompanyName', 'Votre Entreprise'), versioninfo.StringStruct('FileDescription', 'Logiciel de gestion immobilière professionnel'), versioninfo.StringStruct('FileVersion', '2.0.0'), versioninfo.StringStruct('InternalName', 'GestImmob'), versioninfo.StringStruct('LegalCopyright', '© 2025 Votre Entreprise'), versioninfo.StringStruct('OriginalFilename', 'GestImmob.exe'), versioninfo.StringStruct('ProductName', 'GestImmob'), versioninfo.StringStruct('ProductVersion', '2.0.0')])]), versioninfo.VarFileInfo([versioninfo.VarStruct('Translation', [1033, 1200])])]),
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Desktop\\logiciel '
 'GestImmob\\gestion-software\\build\\GestImmob\\GestImmob.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\main.py',
   'PYSOURCE-2'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlite.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlite.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlmimer.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlmimer.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlibase.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlibase.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlpsql.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlpsql.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqloci.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqloci.dll',
   'BINARY'),
  ('PySide6\\plugins\\sqldrivers\\qsqlodbc.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\sqldrivers\\qsqlodbc.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\greenlet\\_greenlet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('PySide6\\QtSql.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\QtSql.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PySide6\\QtPrintSupport.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('pyodbc.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\pyodbc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Sql.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Sql.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6PrintSupport.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6PrintSupport.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY'),
  ('README.md',
   'C:\\Users\\<USER>\\Desktop\\logiciel GestImmob\\gestion-software\\README.md',
   'DATA'),
  ('config.json',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\config.json',
   'DATA'),
  ('config_advanced.json',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\config_advanced.json',
   'DATA'),
  ('modules\\settings_manager.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\modules\\settings_manager.py',
   'DATA'),
  ('modules\\sql_manager.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\modules\\sql_manager.py',
   'DATA'),
  ('requirements.txt',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\requirements.txt',
   'DATA'),
  ('src\\__pycache__\\config.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\config.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\document.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\document.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\erp_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\erp_module.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\fournisseur.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\fournisseur.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\impression.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\impression.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\inventaire.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\inventaire.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\recherche.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\recherche.cpython-313.pyc',
   'DATA'),
  ('src\\__pycache__\\reforme.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\__pycache__\\reforme.cpython-313.pyc',
   'DATA'),
  ('src\\auth\\__pycache__\\authentication.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\auth\\__pycache__\\authentication.cpython-313.pyc',
   'DATA'),
  ('src\\auth\\authentication.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\auth\\authentication.py',
   'DATA'),
  ('src\\auth\\encryption.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\auth\\encryption.py',
   'DATA'),
  ('src\\auth\\user_management.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\auth\\user_management.py',
   'DATA'),
  ('src\\calculatrice.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\calculatrice.py',
   'DATA'),
  ('src\\config.json',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\config.json',
   'DATA'),
  ('src\\config.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\config.py',
   'DATA'),
  ('src\\database\\__pycache__\\db_connection.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\database\\__pycache__\\db_connection.cpython-313.pyc',
   'DATA'),
  ('src\\database\\db_connection.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\database\\db_connection.py',
   'DATA'),
  ('src\\database\\migrations\\init.sql',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\database\\migrations\\init.sql',
   'DATA'),
  ('src\\database\\models.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\database\\models.py',
   'DATA'),
  ('src\\document.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\document.py',
   'DATA'),
  ('src\\erp_module.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\erp_module.py',
   'DATA'),
  ('src\\fournisseur.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\fournisseur.py',
   'DATA'),
  ('src\\impression.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\impression.py',
   'DATA'),
  ('src\\inventaire.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\inventaire.py',
   'DATA'),
  ('src\\main.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\main.py',
   'DATA'),
  ('src\\main_window.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\main_window.py',
   'DATA'),
  ('src\\materiel_travaux.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\materiel_travaux.py',
   'DATA'),
  ('src\\parc_auto.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\parc_auto.py',
   'DATA'),
  ('src\\recherche.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\recherche.py',
   'DATA'),
  ('src\\reforme.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\reforme.py',
   'DATA'),
  ('src\\suivi_animaux.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\suivi_animaux.py',
   'DATA'),
  ('src\\suivi_travaux.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\suivi_travaux.py',
   'DATA'),
  ('src\\test_main.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\test_main.py',
   'DATA'),
  ('src\\utils\\__pycache__\\config.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\__pycache__\\config.cpython-313.pyc',
   'DATA'),
  ('src\\utils\\__pycache__\\logger.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\__pycache__\\logger.cpython-313.pyc',
   'DATA'),
  ('src\\utils\\config.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\config.py',
   'DATA'),
  ('src\\utils\\logger.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\logger.py',
   'DATA'),
  ('src\\utils\\performance_manager.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\performance_manager.py',
   'DATA'),
  ('src\\utils\\security.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\security.py',
   'DATA'),
  ('src\\utils\\theme_manager.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\utils\\theme_manager.py',
   'DATA'),
  ('src\\views\\admin_view.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\views\\admin_view.py',
   'DATA'),
  ('src\\views\\user_view.py',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\src\\views\\user_view.py',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\build\\GestImmob\\base_library.zip',
   'DATA')],
 [],
 True,
 False,
 **********,
 [('runw.exe',
   'c:\\Users\\<USER>\\Desktop\\logiciel '
   'GestImmob\\gestion-software\\.venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll')
