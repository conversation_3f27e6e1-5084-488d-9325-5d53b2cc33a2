#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour cloner les configurations extraites vers GestImmob
"""

import json
import sqlite3
import os
from datetime import datetime

class CloneurConfigurations:
    def __init__(self):
        self.config_source = "configuration_gestimmob_importee.json"
        self.db_gestimmob = "gestimmob_final.db"
        self.configurations = None
        
    def charger_configurations(self):
        """Charge les configurations extraites"""
        print("📥 CHARGEMENT DES CONFIGURATIONS EXTRAITES")
        print("=" * 60)
        
        if not os.path.exists(self.config_source):
            print(f"❌ Fichier de configuration non trouvé: {self.config_source}")
            return False
        
        try:
            with open(self.config_source, 'r', encoding='utf-8') as f:
                self.configurations = json.load(f)
            
            print(f"✅ Configurations chargées depuis {self.config_source}")
            print(f"📊 {len(self.configurations['secteurs_personnalises'])} secteurs")
            print(f"⚙️ {len(self.configurations['parametres_application'])} groupes de paramètres")
            print(f"👥 {len(self.configurations['utilisateurs'])} utilisateurs")
            print(f"📄 {len(self.configurations['exemples_donnees'])} exemples de données")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement configurations: {e}")
            return False
    
    def cloner_vers_gestimmob(self):
        """Clone les configurations vers GestImmob"""
        print(f"\n🔄 CLONAGE VERS GESTIMMOB")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_gestimmob)
            cursor = conn.cursor()
            
            # 1. Cloner les secteurs personnalisés
            self.cloner_secteurs(cursor)
            
            # 2. Cloner les paramètres
            self.cloner_parametres(cursor)
            
            # 3. Cloner les utilisateurs
            self.cloner_utilisateurs(cursor)
            
            # 4. Cloner les exemples de données
            self.cloner_exemples_donnees(cursor)
            
            # 5. Optimiser la base
            self.optimiser_base(cursor)
            
            conn.commit()
            conn.close()
            
            print(f"\n✅ CLONAGE TERMINÉ AVEC SUCCÈS !")
            return True
            
        except Exception as e:
            print(f"❌ Erreur clonage: {e}")
            return False
    
    def cloner_secteurs(self, cursor):
        """Clone les secteurs personnalisés"""
        print(f"\n🎯 CLONAGE DES SECTEURS")
        print("-" * 40)
        
        secteurs = self.configurations['secteurs_personnalises']
        secteurs_clones = 0
        
        for secteur in secteurs:
            try:
                # Vérifier si le secteur existe déjà
                cursor.execute('''
                    SELECT id FROM secteurs_personnalises WHERE nom = ?
                ''', (secteur['nom'],))
                
                if cursor.fetchone():
                    print(f"⚠️ Secteur existe déjà: {secteur['nom']}")
                    continue
                
                # Insérer le nouveau secteur
                cursor.execute('''
                    INSERT INTO secteurs_personnalises 
                    (nom, description, couleur, icone, is_active)
                    VALUES (?, ?, ?, ?, 1)
                ''', (
                    secteur['nom'],
                    secteur['description'],
                    secteur['couleur'],
                    secteur['icone']
                ))
                
                secteur_id = cursor.lastrowid
                
                # Ajouter les critères si disponibles
                if 'criteres' in secteur and secteur['criteres']:
                    for i, critere in enumerate(secteur['criteres']):
                        cursor.execute('''
                            INSERT INTO criteres_secteur
                            (secteur_id, nom_critere, type_critere, valeurs_possibles, obligatoire, ordre_affichage)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (
                            secteur_id,
                            critere.get('nom', f'Critère {i+1}'),
                            critere.get('type', 'text'),
                            json.dumps(critere.get('valeurs', [])) if critere.get('valeurs') else None,
                            critere.get('obligatoire', 0),
                            i
                        ))
                
                secteurs_clones += 1
                print(f"✅ Secteur cloné: {secteur['nom']}")
                
            except Exception as e:
                print(f"❌ Erreur secteur {secteur['nom']}: {e}")
        
        print(f"📊 {secteurs_clones} secteur(s) cloné(s)")
    
    def cloner_parametres(self, cursor):
        """Clone les paramètres de configuration"""
        print(f"\n⚙️ CLONAGE DES PARAMÈTRES")
        print("-" * 40)
        
        # Créer la table des paramètres si elle n'existe pas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS parametres_clones (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                groupe TEXT NOT NULL,
                nom TEXT NOT NULL,
                valeur TEXT,
                type TEXT DEFAULT 'string',
                description TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(groupe, nom)
            )
        ''')
        
        parametres_clones = 0
        
        for groupe, params in self.configurations['parametres_application'].items():
            for nom, valeur in params.items():
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO parametres_clones
                        (groupe, nom, valeur, description)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        groupe,
                        nom,
                        str(valeur),
                        f"Paramètre cloné depuis {groupe}"
                    ))
                    
                    parametres_clones += 1
                    
                except Exception as e:
                    print(f"❌ Erreur paramètre {nom}: {e}")
        
        print(f"📊 {parametres_clones} paramètre(s) cloné(s)")
    
    def cloner_utilisateurs(self, cursor):
        """Clone les utilisateurs (sans mots de passe sensibles)"""
        print(f"\n👥 CLONAGE DES UTILISATEURS")
        print("-" * 40)
        
        # Créer la table des utilisateurs clonés si elle n'existe pas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS utilisateurs_clones (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_utilisateur TEXT UNIQUE,
                nom_complet TEXT,
                email TEXT,
                role TEXT DEFAULT 'user',
                actif INTEGER DEFAULT 1,
                source_clone TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        utilisateurs_clones = 0
        
        for utilisateur in self.configurations['utilisateurs']:
            try:
                # Extraire les informations utilisateur (sans mot de passe)
                nom_utilisateur = utilisateur.get('username') or utilisateur.get('nom') or utilisateur.get('login') or f"user_{utilisateurs_clones+1}"
                nom_complet = utilisateur.get('nom_complet') or utilisateur.get('full_name') or nom_utilisateur
                email = utilisateur.get('email') or utilisateur.get('mail') or f"{nom_utilisateur}@example.com"
                role = utilisateur.get('role') or utilisateur.get('type') or 'user'
                
                cursor.execute('''
                    INSERT OR REPLACE INTO utilisateurs_clones
                    (nom_utilisateur, nom_complet, email, role, source_clone)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    nom_utilisateur,
                    nom_complet,
                    email,
                    role,
                    "Configuration clonée"
                ))
                
                utilisateurs_clones += 1
                print(f"✅ Utilisateur cloné: {nom_utilisateur}")
                
            except Exception as e:
                print(f"❌ Erreur utilisateur: {e}")
        
        print(f"📊 {utilisateurs_clones} utilisateur(s) cloné(s)")
    
    def cloner_exemples_donnees(self, cursor):
        """Clone les exemples de données d'immobilisations"""
        print(f"\n📄 CLONAGE DES EXEMPLES DE DONNÉES")
        print("-" * 40)
        
        # Créer la table des exemples clonés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS immobilisations_clones (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT,
                valeur REAL,
                secteur TEXT,
                localisation TEXT,
                observation TEXT,
                source_clone TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        exemples_clones = 0
        
        for exemple in self.configurations['exemples_donnees']:
            try:
                # Extraire les informations de l'immobilisation
                designation = exemple.get('designation') or exemple.get('nom') or exemple.get('libelle') or f"Bien {exemples_clones+1}"
                valeur = exemple.get('valeur') or exemple.get('prix') or exemple.get('montant') or 0
                secteur = exemple.get('secteur') or exemple.get('type') or exemple.get('categorie') or 'Général'
                localisation = exemple.get('localisation') or exemple.get('lieu') or exemple.get('emplacement') or 'Non spécifié'
                observation = exemple.get('observation') or exemple.get('description') or exemple.get('remarque') or ''
                
                cursor.execute('''
                    INSERT INTO immobilisations_clones
                    (designation, valeur, secteur, localisation, observation, source_clone)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    designation,
                    float(valeur) if valeur else 0.0,
                    secteur,
                    localisation,
                    observation,
                    "Données clonées"
                ))
                
                exemples_clones += 1
                print(f"✅ Exemple cloné: {designation}")
                
            except Exception as e:
                print(f"❌ Erreur exemple: {e}")
        
        print(f"📊 {exemples_clones} exemple(s) cloné(s)")
    
    def optimiser_base(self, cursor):
        """Optimise la base de données après clonage"""
        print(f"\n📈 OPTIMISATION DE LA BASE")
        print("-" * 40)
        
        try:
            # Créer des index pour améliorer les performances
            index_queries = [
                "CREATE INDEX IF NOT EXISTS idx_secteurs_clones_nom ON secteurs_personnalises(nom)",
                "CREATE INDEX IF NOT EXISTS idx_parametres_clones_groupe ON parametres_clones(groupe)",
                "CREATE INDEX IF NOT EXISTS idx_utilisateurs_clones_nom ON utilisateurs_clones(nom_utilisateur)",
                "CREATE INDEX IF NOT EXISTS idx_immobilisations_clones_secteur ON immobilisations_clones(secteur)"
            ]
            
            for query in index_queries:
                cursor.execute(query)
            
            # Analyser la base pour optimiser les requêtes
            cursor.execute("ANALYZE")
            
            print("✅ Base de données optimisée")
            
        except Exception as e:
            print(f"❌ Erreur optimisation: {e}")
    
    def generer_rapport_clonage(self):
        """Génère un rapport du clonage effectué"""
        print(f"\n📊 RAPPORT DE CLONAGE")
        print("=" * 60)
        
        try:
            conn = sqlite3.connect(self.db_gestimmob)
            cursor = conn.cursor()
            
            # Compter les éléments clonés
            cursor.execute("SELECT COUNT(*) FROM secteurs_personnalises")
            nb_secteurs = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM parametres_clones")
            nb_parametres = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM utilisateurs_clones")
            nb_utilisateurs = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM immobilisations_clones")
            nb_exemples = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ Secteurs dans GestImmob: {nb_secteurs}")
            print(f"✅ Paramètres clonés: {nb_parametres}")
            print(f"✅ Utilisateurs clonés: {nb_utilisateurs}")
            print(f"✅ Exemples clonés: {nb_exemples}")
            
            print(f"\n🎉 CLONAGE RÉUSSI !")
            print(f"Toutes les configurations ont été intégrées dans GestImmob")
            
        except Exception as e:
            print(f"❌ Erreur rapport: {e}")
    
    def creer_script_integration(self):
        """Crée un script pour intégrer les données clonées"""
        script_content = '''
-- Script d'intégration des configurations clonées
-- Exécutez ce script pour finaliser l'intégration

-- Copier les secteurs clonés vers la table principale si nécessaire
INSERT OR IGNORE INTO secteurs_personnalises (nom, description, couleur, icone)
SELECT nom, description, couleur, icone FROM secteurs_personnalises WHERE id > 0;

-- Copier les paramètres vers la table principale
INSERT OR REPLACE INTO parametres (nom, valeur, description)
SELECT nom, valeur, description FROM parametres_clones;

-- Afficher un résumé
SELECT 'Secteurs disponibles' as type, COUNT(*) as nombre FROM secteurs_personnalises
UNION ALL
SELECT 'Paramètres configurés' as type, COUNT(*) as nombre FROM parametres_clones
UNION ALL
SELECT 'Utilisateurs clonés' as type, COUNT(*) as nombre FROM utilisateurs_clones
UNION ALL
SELECT 'Exemples disponibles' as type, COUNT(*) as nombre FROM immobilisations_clones;
'''
        
        with open('integration_finale.sql', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"\n📄 Script d'intégration créé: integration_finale.sql")

def main():
    """Fonction principale"""
    cloneur = CloneurConfigurations()
    
    # Charger les configurations
    if not cloneur.charger_configurations():
        return
    
    # Cloner vers GestImmob
    if cloneur.cloner_vers_gestimmob():
        # Générer le rapport
        cloneur.generer_rapport_clonage()
        
        # Créer le script d'intégration
        cloneur.creer_script_integration()
        
        print(f"\n🎯 CLONAGE TERMINÉ AVEC SUCCÈS !")
        print(f"Les configurations ont été clonées dans {cloneur.db_gestimmob}")
        print(f"GestImmob dispose maintenant de toutes vos configurations personnalisées !")

if __name__ == "__main__":
    main()
