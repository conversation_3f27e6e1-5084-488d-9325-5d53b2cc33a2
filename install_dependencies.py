#!/usr/bin/env python3
"""
Script d'installation automatique des dépendances pour GestImmob
Installe toutes les dépendances nécessaires avec gestion d'erreurs
"""

import subprocess
import sys
import os
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('installation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_python_version():
    """Vérifie que Python 3.8+ est installé"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 ou supérieur requis")
        sys.exit(1)
    logger.info(f"Python {sys.version} détecté - OK")

def upgrade_pip():
    """Met à jour pip vers la dernière version"""
    try:
        logger.info("Mise à jour de pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        logger.info("Pip mis à jour avec succès")
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de la mise à jour de pip: {e}")
        return False
    return True

def install_package(package):
    """Installe un package avec gestion d'erreurs"""
    try:
        logger.info(f"Installation de {package}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            package, "--upgrade", "--no-cache-dir"
        ])
        logger.info(f"{package} installé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de l'installation de {package}: {e}")
        return False

def install_requirements():
    """Installe toutes les dépendances depuis requirements.txt"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        logger.error("Fichier requirements.txt non trouvé")
        return False
    
    try:
        logger.info("Installation des dépendances depuis requirements.txt...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "-r", str(requirements_file), "--upgrade"
        ])
        logger.info("Toutes les dépendances installées avec succès")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de l'installation des dépendances: {e}")
        return False

def install_optional_packages():
    """Installe les packages optionnels pour fonctionnalités avancées"""
    optional_packages = [
        "pyinstaller",  # Pour créer l'exécutable
        "auto-py-to-exe",  # Interface graphique pour PyInstaller
        "nuitka",  # Compilateur Python alternatif
        "cx_Freeze",  # Alternative à PyInstaller
    ]
    
    logger.info("Installation des packages optionnels...")
    for package in optional_packages:
        install_package(package)

def create_virtual_env():
    """Crée un environnement virtuel si nécessaire"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        logger.info("Environnement virtuel existant détecté")
        return True
    
    try:
        logger.info("Création de l'environnement virtuel...")
        subprocess.check_call([sys.executable, "-m", "venv", "venv"])
        logger.info("Environnement virtuel créé avec succès")
        
        # Instructions pour activer l'environnement
        if os.name == 'nt':  # Windows
            activate_script = "venv\\Scripts\\activate.bat"
        else:  # Unix/Linux/macOS
            activate_script = "source venv/bin/activate"
        
        logger.info(f"Pour activer l'environnement virtuel, exécutez: {activate_script}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Erreur lors de la création de l'environnement virtuel: {e}")
        return False

def verify_installation():
    """Vérifie que les packages critiques sont installés"""
    critical_packages = [
        "PySide6",
        "SQLAlchemy", 
        "cryptography",
        "pandas",
        "xlsxwriter"
    ]
    
    logger.info("Vérification de l'installation...")
    failed_packages = []
    
    for package in critical_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            logger.info(f"[OK] {package} installé correctement")
        except ImportError:
            logger.error(f"[ERREUR] {package} non installé ou défaillant")
            failed_packages.append(package)
    
    if failed_packages:
        logger.error(f"Packages défaillants: {', '.join(failed_packages)}")
        return False
    
    logger.info("Tous les packages critiques sont installés correctement")
    return True

def setup_database():
    """Initialise la base de données"""
    try:
        import sqlite3
        db_path = Path("immobilisations.db")
        
        if db_path.exists():
            logger.info("Base de données existante détectée")
            return True
        
        logger.info("Création de la base de données...")
        conn = sqlite3.connect(str(db_path))
        
        # Création des tables de base
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS immobilisations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                designation TEXT NOT NULL,
                valeur REAL NOT NULL,
                annee INTEGER NOT NULL,
                localisation TEXT,
                secteur TEXT,
                observation TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Base de données créée avec succès")
        return True
    except Exception as e:
        logger.error(f"Erreur lors de la création de la base de données: {e}")
        return False

def main():
    """Fonction principale d'installation"""
    logger.info("=== Installation de GestImmob ===")
    
    # Vérifications préliminaires
    check_python_version()
    
    # Mise à jour de pip
    if not upgrade_pip():
        logger.error("Impossible de mettre à jour pip")
        sys.exit(1)
    
    # Installation des dépendances
    if not install_requirements():
        logger.error("Échec de l'installation des dépendances")
        sys.exit(1)
    
    # Installation des packages optionnels
    install_optional_packages()
    
    # Vérification de l'installation
    if not verify_installation():
        logger.error("Vérification échouée")
        sys.exit(1)
    
    # Configuration de la base de données
    if not setup_database():
        logger.error("Échec de la configuration de la base de données")
        sys.exit(1)
    
    logger.info("=== Installation terminée avec succès ===")
    logger.info("Vous pouvez maintenant lancer le logiciel avec: python src/main.py")

if __name__ == "__main__":
    main()
