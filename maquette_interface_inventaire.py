#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MAQUETTE de l'interface d'inventaire - POUR VALIDATION UNIQUEMENT
Ne sera implémentée qu'après votre validation explicite
"""

from PySide6.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QWidget, QVBox<PERSON>ayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QFrame
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QIcon
import sys

class MaquetteInterfaceInventaire(QMainWindow):
    """MAQUETTE SEULEMENT - Interface style inventaire avec fond noir et boutons colorés"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MAQUETTE - Interface Inventaire (VALIDATION REQUISE)")
        self.setGeometry(100, 100, 1000, 700)
        
        # Style général - Cadre noir
        self.setStyleSheet("""
            QMainWindow {
                background-color: #000000;
                border: 2px solid #333333;
            }
        """)
        
        self.setup_maquette_ui()
    
    def setup_maquette_ui(self):
        """Configure la maquette de l'interface"""
        
        # Widget central avec fond blanc
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background-color: #FFFFFF;
                border: 1px solid #CCCCCC;
            }
        """)
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)
        
        # En-tête
        header_label = QLabel("📋 MAQUETTE - Interface Inventaire")
        header_label.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #333333;
                padding: 10px;
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
            }
        """)
        main_layout.addWidget(header_label)
        
        # Tableau principal avec en-têtes d'inventaire
        self.create_tableau_inventaire(main_layout)
        
        # Espace flexible
        main_layout.addStretch()
        
        # Rangée de boutons colorés en bas
        self.create_boutons_colores_bas(main_layout)
    
    def create_tableau_inventaire(self, layout):
        """Crée le tableau avec en-têtes d'inventaire"""
        
        # Tableau
        self.tableau = QTableWidget()
        
        # En-têtes d'inventaire typiques
        entetes = ["Code", "Désignation", "Quantité", "Prix", "Catégorie", "Fournisseur", "Date"]
        self.tableau.setColumnCount(len(entetes))
        self.tableau.setHorizontalHeaderLabels(entetes)
        
        # Style du tableau
        self.tableau.setStyleSheet("""
            QTableWidget {
                background-color: #FFFFFF;
                gridline-color: #CCCCCC;
                border: 1px solid #CCCCCC;
            }
            QHeaderView::section {
                background-color: #E0E0E0;
                color: #333333;
                font-weight: bold;
                border: 1px solid #CCCCCC;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #EEEEEE;
            }
        """)
        
        # Quelques données d'exemple
        donnees_exemple = [
            ["001", "Article 1", "10", "25.50", "Catégorie A", "Fournisseur 1", "01/01/2024"],
            ["002", "Article 2", "5", "15.75", "Catégorie B", "Fournisseur 2", "02/01/2024"],
            ["003", "Article 3", "20", "8.25", "Catégorie A", "Fournisseur 1", "03/01/2024"]
        ]
        
        self.tableau.setRowCount(len(donnees_exemple))
        for i, ligne in enumerate(donnees_exemple):
            for j, valeur in enumerate(ligne):
                item = QTableWidgetItem(str(valeur))
                self.tableau.setItem(i, j, item)
        
        layout.addWidget(self.tableau)
    
    def create_boutons_colores_bas(self, layout):
        """Crée la rangée de boutons colorés en bas"""
        
        # Frame pour les boutons du bas
        boutons_frame = QFrame()
        boutons_frame.setStyleSheet("""
            QFrame {
                background-color: #000000;
                border: 2px solid #333333;
                border-radius: 5px;
            }
        """)
        boutons_frame.setFixedHeight(80)
        
        # Layout horizontal pour les boutons
        boutons_layout = QHBoxLayout(boutons_frame)
        boutons_layout.setContentsMargins(10, 10, 10, 10)
        boutons_layout.setSpacing(10)
        
        # Définition des boutons colorés avec icônes
        boutons_config = [
            {"text": "Ajouter", "color": "#2196F3", "icon": "➕"},  # Bleu
            {"text": "Modifier", "color": "#4CAF50", "icon": "✏️"},  # Vert
            {"text": "Supprimer", "color": "#F44336", "icon": "🗑️"},  # Rouge
            {"text": "Rechercher", "color": "#FF9800", "icon": "🔍"},  # Orange
            {"text": "Imprimer", "color": "#9C27B0", "icon": "🖨️"},  # Violet
            {"text": "Exporter", "color": "#607D8B", "icon": "📤"},  # Gris-bleu
        ]
        
        # Créer chaque bouton coloré
        for config in boutons_config:
            bouton = self.create_bouton_colore(
                config["text"], 
                config["color"], 
                config["icon"]
            )
            boutons_layout.addWidget(bouton)
        
        # Espace flexible à droite
        boutons_layout.addStretch()
        
        layout.addWidget(boutons_frame)
    
    def create_bouton_colore(self, text, color, icon):
        """Crée un bouton coloré avec icône"""
        
        bouton = QPushButton(f"{icon} {text}")
        
        # Style du bouton coloré
        bouton.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 10pt;
                font-weight: bold;
                min-width: 100px;
                max-width: 120px;
                min-height: 40px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
                transform: translateY(0px);
            }}
        """)
        
        # Connecter à une fonction de démonstration
        bouton.clicked.connect(lambda: self.demo_action(text))
        
        return bouton
    
    def darken_color(self, hex_color, factor=0.2):
        """Assombrit une couleur hexadécimale"""
        # Conversion simple pour l'exemple
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
    
    def demo_action(self, action):
        """Action de démonstration pour les boutons"""
        print(f"MAQUETTE - Action simulée: {action}")
        
        # Afficher un message dans la barre de titre
        self.setWindowTitle(f"MAQUETTE - Action: {action} (VALIDATION REQUISE)")

def afficher_maquette():
    """Affiche la maquette pour validation"""
    
    print("🎨 AFFICHAGE DE LA MAQUETTE")
    print("=" * 50)
    print("Cette maquette reproduit le style de votre interface d'inventaire:")
    print("✅ Cadre noir avec fond blanc")
    print("✅ Tableau avec en-têtes d'inventaire")
    print("✅ Boutons colorés en bas avec icônes")
    print("✅ Style sombre et professionnel")
    print()
    print("⚠️ CETTE MAQUETTE EST POUR VALIDATION UNIQUEMENT")
    print("⚠️ RIEN NE SERA IMPLÉMENTÉ SANS VOTRE PERMISSION")
    print()
    print("Veuillez valider ou demander des ajustements...")
    
    app = QApplication(sys.argv)
    
    # Créer et afficher la maquette
    maquette = MaquetteInterfaceInventaire()
    maquette.show()
    
    return app.exec()

if __name__ == "__main__":
    # Afficher la maquette pour validation
    afficher_maquette()
