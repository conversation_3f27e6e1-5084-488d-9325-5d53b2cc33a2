#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger la syntaxe SQL de PostgreSQL vers SQLite
"""

import re
import os

def fix_sql_file(file_path):
    """Corrige un fichier SQL pour SQLite"""
    print(f"🔧 Correction de {file_path}...")
    
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Corrections SQL PostgreSQL -> SQLite
        corrections = [
            # SERIAL PRIMARY KEY -> INTEGER PRIMARY KEY AUTOINCREMENT
            (r'\bid\s+SERIAL\s+PRIMARY\s+KEY\b', 'id INTEGER PRIMARY KEY AUTOINCREMENT'),
            
            # Autres corrections potentielles
            (r'\bBOOLEAN\b', 'INTEGER'),  # SQLite n'a pas de type BOOLEAN natif
            (r'\bTEXT\s+NOT\s+NULL\s+DEFAULT\s+\'\'', 'TEXT DEFAULT \'\''),  # Simplification
        ]
        
        # Appliquer les corrections
        for pattern, replacement in corrections:
            old_content = content
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            if content != old_content:
                matches = len(re.findall(pattern, old_content, flags=re.IGNORECASE))
                print(f"  ✅ Corrigé {matches} occurrence(s) de: {pattern}")
        
        # Vérifier si des changements ont été faits
        if content != original_content:
            # Backup
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"  📄 Backup créé: {backup_path}")
            
            # Sauvegarder le fichier corrigé
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ Fichier corrigé et sauvegardé")
            return True
        else:
            print(f"  ✅ Aucune correction nécessaire")
            return True
            
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 CORRECTION SYNTAXE SQL - PostgreSQL vers SQLite")
    print("=" * 60)
    
    # Fichiers SQL à corriger
    sql_files = [
        'src/database/migrations/init.sql',
        'src/database/schema.sql',
        'database/init.sql',
        'init.sql'
    ]
    
    success_count = 0
    total_count = 0
    
    for sql_file in sql_files:
        if os.path.exists(sql_file):
            total_count += 1
            if fix_sql_file(sql_file):
                success_count += 1
        else:
            print(f"⚠️ Fichier non trouvé: {sql_file}")
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DE LA CORRECTION SQL")
    print("=" * 60)
    
    if total_count > 0:
        success_rate = (success_count / total_count) * 100
        print(f"✅ Fichiers corrigés: {success_count}/{total_count}")
        print(f"📈 Taux de réussite: {success_rate:.1f}%")
        
        if success_count == total_count:
            print("\n🎉 TOUS LES FICHIERS SQL SONT MAINTENANT COMPATIBLES SQLITE !")
            print("✅ Syntaxe PostgreSQL corrigée")
            print("✅ SERIAL remplacé par INTEGER PRIMARY KEY AUTOINCREMENT")
            print("✅ Types de données adaptés à SQLite")
            return True
        else:
            print(f"\n⚠️ {total_count - success_count} fichier(s) avec des problèmes")
            return False
    else:
        print("⚠️ Aucun fichier SQL trouvé à corriger")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat: {'SUCCÈS' if success else 'ÉCHEC'}")
    exit(0 if success else 1)
