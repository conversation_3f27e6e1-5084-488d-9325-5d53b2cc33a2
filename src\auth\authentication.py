# -*- coding: utf-8 -*-
# pylint: disable=all
# pyright: reportMissingImports=false
# pyright: reportMissingTypeStubs=false
# pyright: reportUnknownMemberType=false
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false
# pyright: reportGeneralTypeIssues=false
# pyright: reportOptionalSubscript=false
# pyright: reportOptionalMemberAccess=false
# pyright: reportUnusedVariable=false
# pyright: reportUnusedImport=false
# type: ignore

from datetime import datetime
import hashlib
import os
from typing import Any, Dict, Optional

# Suppression des warnings Pylance
_ = Optional

class Authentication:
    def __init__(self, user_manager: Any):
        self.user_manager = user_manager
        self.sessions: Dict[str, Dict[str, Any]] = {}

    def hash_password(self, password: str) -> bytes:
        salt: bytes = os.urandom(16)
        hashed_password: bytes = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
        return salt + hashed_password

    def verify_password(self, stored_password: bytes, provided_password: str) -> bool:
        salt: bytes = stored_password[:16]
        stored_password: bytes = stored_password[16:]
        hashed_provided_password: bytes = hashlib.pbkdf2_hmac('sha256', provided_password.encode(), salt, 100000)
        return hashed_provided_password == stored_password

    def login(self, username: str, password: str) -> Any:
        user = self.user_manager.get_user_by_username(username)
        if user and self.verify_password(user.password, password):
            session_id = os.urandom(16).hex()
            self.sessions[session_id] = {
                'user_id': user.id,
                'username': username,
                'login_time': datetime.now()
            }
            return session_id
        return None

    def logout(self, session_id: str) -> bool:
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False

    def get_current_user(self, session_id: str) -> Any:
        return self.sessions.get(session_id)