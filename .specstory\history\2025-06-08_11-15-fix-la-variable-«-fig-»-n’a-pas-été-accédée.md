<!-- Generated by SpecStory -->

# /fix La variable « fig » n’a pas été accédée (2025-06-08 12:15:39)

_**User**_

/fix La variable « fig » n’a pas été accédée

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Supprimez la variable inutilisée `fig` dans la méthode `afficher_charts` pour corriger l'avertissement "La variable « fig » n’a pas été accédée". Il suffit d'appeler directement `plt.figure(...)` sans l'assigner à une variable.

````python
# ...existing code...
            if stats_categorie:
                plt.figure(figsize=(8, 4))  # Suppression de l'affectation à fig
                plt.bar(list(stats_categorie.keys()), list(stats_categorie.values()))
                plt.title("Articles par catégorie")
                plt.xlabel("Catégorie")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
            if stats_statut:
                plt.figure(figsize=(8, 4))  # Suppression de l'affectation à fig
                plt.bar(list(stats_statut.keys()), list(stats_statut.values()))
                plt.title("Articles par statut")
                plt.xlabel("Statut")
                plt.ylabel("Nombre")
                plt.tight_layout()
                plt.show()
# ...existing code...
````

---

