@echo off
title 🪟 Robot IA Universel - Windows
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🪟 ROBOT IA UNIVERSEL - WINDOWS                                        ██
echo ██    👤 SamNord@110577 - Compatible Windows 7/8/10/11                       ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🪟 ROBOT IA UNIVERSEL POUR WINDOWS
echo ==================================
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo 💻 Système: Windows
echo.

echo 🔄 Vérification environnement Windows...

REM Vérification version Windows
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo ✅ Version Windows: %VERSION%

REM Vérification Python
python --version >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Python détecté
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo    Version: %%i
) else (
    echo ❌ Python non détecté
    echo 📥 Installation Python requise
    echo 🌐 Téléchargez depuis: https://python.org
    echo.
    set /p install="Voulez-vous télécharger Python maintenant ? (O/N): "
    if /i "!install!"=="O" start https://python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 🚀 LANCEMENT ROBOT IA UNIVERSEL...

REM Tentative exécutable Windows
if exist "executables\RobotIA_SamNord_Windows.exe" (
    echo 🪟 Lancement version exécutable Windows...
    start "" "executables\RobotIA_SamNord_Windows.exe"
    goto SUCCESS
)

REM Tentative Python universel
if exist "robot_ia_universel.py" (
    echo 🐍 Lancement version Python universelle...
    python robot_ia_universel.py
    goto SUCCESS
)

REM Fallback application principale
if exist "robot_autonome_pc.py" (
    echo 🤖 Lancement application principale...
    python robot_autonome_pc.py
    goto SUCCESS
)

echo ❌ Aucune version disponible !
echo 🔧 Vérifiez l'installation
pause
exit /b 1

:SUCCESS
echo.
echo ✅ Robot IA Universel lancé avec succès !
echo 🪟 Optimisé pour Windows
echo 👤 Session SamNord@110577 active
echo.
timeout /t 3 >nul
exit /b 0
