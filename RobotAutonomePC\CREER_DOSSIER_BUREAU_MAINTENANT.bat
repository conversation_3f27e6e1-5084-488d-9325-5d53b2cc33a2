@echo off
title 📁 CRÉATION DOSSIER BUREAU IMMÉDIATE
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    📁 CRÉATION DOSSIER ROBOT IA SUR BUREAU - IMMÉDIATE                    ██
echo ██    🚀 COPIE RAPIDE + RACCOURCIS + LANCEMENT                               ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 CRÉATION IMMÉDIATE DOSSIER BUREAU
echo 📁 Copie tous les fichiers sur bureau
echo 🔗 Création raccourcis
echo 🚀 Lancement automatique
echo.

REM Variables
set "SOURCE=%~dp0"
set "BUREAU=%USERPROFILE%\Desktop"
set "ROBOT_BUREAU=%BUREAU%\RobotIA_SamNord"

echo 📍 EMPLACEMENTS:
echo 📂 Source: %SOURCE%
echo 🖥️ Bureau: %BUREAU%
echo 📁 Destination: %ROBOT_BUREAU%
echo.

echo 🔄 ÉTAPE 1: Nettoyage ancien dossier...
if exist "%ROBOT_BUREAU%" (
    echo 🗑️ Suppression ancien dossier...
    rmdir /s /q "%ROBOT_BUREAU%" >nul 2>&1
)

echo 🔄 ÉTAPE 2: Création nouveau dossier...
mkdir "%ROBOT_BUREAU%" >nul 2>&1
mkdir "%ROBOT_BUREAU%\modules" >nul 2>&1
mkdir "%ROBOT_BUREAU%\config" >nul 2>&1
mkdir "%ROBOT_BUREAU%\data" >nul 2>&1
mkdir "%ROBOT_BUREAU%\logs" >nul 2>&1
mkdir "%ROBOT_BUREAU%\reports" >nul 2>&1
mkdir "%ROBOT_BUREAU%\backups" >nul 2>&1
mkdir "%ROBOT_BUREAU%\executable" >nul 2>&1

if exist "%ROBOT_BUREAU%" (
    echo ✅ Dossier créé avec succès !
) else (
    echo ❌ Erreur création dossier !
    pause
    exit /b 1
)

echo 🔄 ÉTAPE 3: Copie fichiers principaux...
copy "%SOURCE%*.py" "%ROBOT_BUREAU%\" >nul 2>&1
copy "%SOURCE%*.bat" "%ROBOT_BUREAU%\" >nul 2>&1
copy "%SOURCE%*.md" "%ROBOT_BUREAU%\" >nul 2>&1
copy "%SOURCE%*.txt" "%ROBOT_BUREAU%\" >nul 2>&1

echo 🔄 ÉTAPE 4: Copie modules...
if exist "%SOURCE%modules" (
    xcopy "%SOURCE%modules" "%ROBOT_BUREAU%\modules\" /E /I /Q >nul 2>&1
    echo ✅ Modules copiés
) else (
    echo ⚠️ Dossier modules non trouvé
)

echo 🔄 ÉTAPE 5: Copie configuration...
if exist "%SOURCE%config" (
    xcopy "%SOURCE%config" "%ROBOT_BUREAU%\config\" /E /I /Q >nul 2>&1
    echo ✅ Configuration copiée
)

echo 🔄 ÉTAPE 6: Création lanceur principal...
(
echo @echo off
echo title 🤖 ROBOT IA AUTONOME PC - SamNord@110577
echo color 0A
echo chcp 65001 ^>nul
echo.
echo echo ████████████████████████████████████████████████████████████████████████████████
echo echo ██                                                                            ██
echo echo ██    🤖 ROBOT IA AUTONOME PC - LANCEMENT PRINCIPAL                          ██
echo echo ██    👤 UTILISATEUR: SamNord@110577                                         ██
echo echo ██                                                                            ██
echo echo ████████████████████████████████████████████████████████████████████████████████
echo echo.
echo.
echo echo 🚀 LANCEMENT ROBOT IA AUTONOME PC...
echo echo 👤 Utilisateur: SamNord@110577
echo echo 🔐 Authentification automatique...
echo echo.
echo.
echo REM Tentative Python
echo python --version ^>nul 2^>^&1
echo if %%errorLevel%% equ 0 ^(
echo     if exist "robot_autonome_pc.py" ^(
echo         echo 🐍 Lancement version Python...
echo         python robot_autonome_pc.py
echo         goto END
echo     ^)
echo ^)
echo.
echo REM Fallback menu universel
echo if exist "LANCEMENT_FINAL_UNIVERSEL.bat" ^(
echo     echo 📊 Lancement menu universel...
echo     call "LANCEMENT_FINAL_UNIVERSEL.bat"
echo     goto END
echo ^)
echo.
echo REM Fallback connexion sécurisée
echo if exist "CONNEXION_SECURISEE.bat" ^(
echo     echo 🔐 Lancement connexion sécurisée...
echo     call "CONNEXION_SECURISEE.bat"
echo     goto END
echo ^)
echo.
echo echo ❌ Aucune version disponible !
echo echo 🔧 Vérifiez l'installation Python
echo echo 📥 Téléchargez Python depuis python.org
echo pause
echo.
echo :END
echo exit /b 0
) > "%ROBOT_BUREAU%\🚀 LANCER ROBOT IA.bat"

echo ✅ Lanceur principal créé

echo 🔄 ÉTAPE 7: Création raccourci bureau...
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateRobotShortcut.vbs"
echo sLinkFile = "%BUREAU%\🤖 Robot IA - <EMAIL>" >> "%TEMP%\CreateRobotShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateRobotShortcut.vbs"
echo oLink.TargetPath = "%ROBOT_BUREAU%\🚀 LANCER ROBOT IA.bat" >> "%TEMP%\CreateRobotShortcut.vbs"
echo oLink.WorkingDirectory = "%ROBOT_BUREAU%" >> "%TEMP%\CreateRobotShortcut.vbs"
echo oLink.Description = "Robot IA Autonome PC - SamNord@110577" >> "%TEMP%\CreateRobotShortcut.vbs"
echo oLink.IconLocation = "shell32.dll,137" >> "%TEMP%\CreateRobotShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateRobotShortcut.vbs"

cscript "%TEMP%\CreateRobotShortcut.vbs" >nul 2>&1
del "%TEMP%\CreateRobotShortcut.vbs" >nul 2>&1

echo ✅ Raccourci bureau créé

echo 🔄 ÉTAPE 8: Création guide rapide...
(
echo 🤖 ROBOT IA AUTONOME PC - GUIDE RAPIDE SAMNORD@110577
echo =====================================================
echo.
echo 📁 DOSSIER: %ROBOT_BUREAU%
echo 👤 UTILISATEUR: SamNord@110577
echo 🔑 MOT DE PASSE: NorDine@22
echo.
echo 🚀 UTILISATION:
echo 1. Double-cliquer "🤖 Robot IA - SamNord@110577" sur bureau
echo 2. Ou aller dans le dossier et lancer "🚀 LANCER ROBOT IA.bat"
echo.
echo 📦 PRÉREQUIS:
echo - Python installé ^(télécharger sur python.org^)
echo - Dépendances: pip install PySide6 requests psutil
echo.
echo 🎯 FONCTIONNALITÉS:
echo ✅ Intelligence IA Transcendante
echo ✅ Communication Multilingue
echo ✅ Scanner Télécommunications
echo ✅ Cybersécurité Éthique
echo ✅ Tests Automatiques
echo ✅ Auto-Réparation
echo.
echo 🌟 PROFITEZ DE VOTRE ROBOT IA !
) > "%BUREAU%\📋 Guide Robot IA - SamNord.txt"

echo ✅ Guide rapide créé

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         ✅ DOSSIER BUREAU CRÉÉ AVEC SUCCÈS !                              ██
echo ██         📁 VÉRIFIEZ VOTRE BUREAU MAINTENANT !                             ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎉 CRÉATION TERMINÉE !
echo.
echo 📁 DOSSIER CRÉÉ: %ROBOT_BUREAU%
echo 🔗 RACCOURCI CRÉÉ: 🤖 Robot IA - SamNord@110577
echo 📋 GUIDE CRÉÉ: 📋 Guide Robot IA - SamNord
echo.
echo 🖥️ VÉRIFIEZ VOTRE BUREAU - VOUS DEVRIEZ VOIR:
echo ✅ 📁 RobotIA_SamNord ^(dossier^)
echo ✅ 🤖 Robot IA - SamNord@110577 ^(raccourci^)
echo ✅ 📋 Guide Robot IA - SamNord ^(fichier texte^)
echo.
echo 🚀 POUR LANCER LE ROBOT IA:
echo 1. Double-cliquer "🤖 Robot IA - SamNord@110577" sur bureau
echo 2. Ou aller dans dossier RobotIA_SamNord et lancer "🚀 LANCER ROBOT IA.bat"
echo.
echo 📦 SI ERREUR PYTHON:
echo 1. Installer Python depuis python.org
echo 2. Ouvrir cmd et taper: pip install PySide6 requests psutil
echo 3. Relancer le Robot IA
echo.

REM Ouverture automatique bureau
echo 📂 Ouverture bureau pour vérification...
timeout /t 2 >nul
start "" explorer "%BUREAU%"

echo.
echo 🎯 VÉRIFIEZ MAINTENANT VOTRE BUREAU !
echo 👀 Vous devriez voir le dossier et les raccourcis
echo.
pause
exit /b 0
