#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour identifier les problèmes spécifiques dans main.py
"""

import ast
import re
import sys
import os

def analyze_main_py():
    """Analyse détaillée du fichier main.py"""
    print("🔍 Analyse détaillée de main.py...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.splitlines()
        
        issues = []
        
        # 1. Vérifier la syntaxe AST
        try:
            ast.parse(content)
            print("✅ Syntaxe AST correcte")
        except SyntaxError as e:
            issues.append(f"ERREUR SYNTAXE ligne {e.lineno}: {e.msg}")
            print(f"❌ Erreur syntaxe ligne {e.lineno}: {e.msg}")
        
        # 2. Chercher les instructions multiples sur une ligne
        for i, line in enumerate(lines, 1):
            if '; ' in line and not line.strip().startswith('#'):
                # Ignorer les CSS
                if 'color:' not in line and 'background-color:' not in line:
                    issues.append(f"LIGNE {i}: Instructions multiples - {line.strip()[:50]}...")
        
        # 3. Chercher les imports dans les fonctions sans type: ignore
        in_function = False
        function_name = ""
        for i, line in enumerate(lines, 1):
            if line.strip().startswith('def '):
                in_function = True
                function_name = line.strip().split('(')[0].replace('def ', '')
            elif line.strip().startswith('class '):
                in_function = False
            elif in_function and 'import ' in line and not line.strip().startswith('#'):
                if 'type: ignore' not in line:
                    issues.append(f"LIGNE {i}: Import dans fonction {function_name} sans type: ignore - {line.strip()}")
        
        # 4. Chercher les variables non typées
        for i, line in enumerate(lines, 1):
            # Variables avec = mais sans :
            if ' = ' in line and not line.strip().startswith('#'):
                if ': ' not in line.split(' = ')[0] and 'def ' not in line and 'class ' not in line:
                    # Ignorer certains cas valides
                    if not any(x in line for x in ['self.', 'try:', 'except:', 'if ', 'for ', 'while ', 'with ']):
                        if not line.strip().startswith('_'):  # Ignorer les variables privées
                            issues.append(f"LIGNE {i}: Variable non typée - {line.strip()[:50]}...")
        
        # 5. Chercher les méthodes avec des annotations incorrectes
        for i, line in enumerate(lines, 1):
            if 'def ' in line and '->' in line:
                # Vérifier si l'annotation se termine correctement
                if not line.strip().endswith(':'):
                    issues.append(f"LIGNE {i}: Annotation de retour incorrecte - {line.strip()}")
        
        # 6. Chercher les appels de méthodes non définies
        method_calls = re.findall(r'self\.(\w+)\(', content)
        method_definitions = re.findall(r'def (\w+)\(', content)
        
        for method in set(method_calls):
            if method not in method_definitions and method not in ['exec', 'show', 'close', 'accept', 'reject']:
                # Ignorer les méthodes Qt standard
                if not method.startswith(('set', 'get', 'add', 'remove', 'insert', 'clear')):
                    issues.append(f"MÉTHODE: {method}() appelée mais non définie")
        
        # 7. Chercher les imports manquants
        imports_used = re.findall(r'(\w+)\.', content)
        imports_declared = re.findall(r'import (\w+)', content) + re.findall(r'from \w+ import (\w+)', content)
        
        for imp in set(imports_used):
            if imp not in imports_declared and imp not in ['self', 'super', 'str', 'int', 'float', 'len', 'range']:
                if imp[0].isupper():  # Classes probablement importées
                    issues.append(f"IMPORT: {imp} utilisé mais non importé")
        
        # 8. Chercher les paramètres inutilisés
        for i, line in enumerate(lines, 1):
            if 'def ' in line and '(' in line:
                # Extraire les paramètres
                params_part = line.split('(')[1].split(')')[0]
                params = [p.strip().split(':')[0].strip() for p in params_part.split(',') if p.strip()]
                
                # Chercher l'utilisation dans la fonction
                func_start = i
                func_end = func_start
                indent_level = len(line) - len(line.lstrip())
                
                for j in range(func_start, min(func_start + 50, len(lines))):
                    if j > func_start and lines[j].strip() and len(lines[j]) - len(lines[j].lstrip()) <= indent_level:
                        func_end = j
                        break
                
                func_body = '\n'.join(lines[func_start:func_end])
                
                for param in params:
                    if param and param != 'self' and param not in func_body:
                        issues.append(f"LIGNE {i}: Paramètre {param} non utilisé dans {line.split('(')[0].replace('def ', '').strip()}")
        
        print(f"\n📊 TOTAL: {len(issues)} problèmes détectés")
        
        if issues:
            print("\n🔍 DÉTAILS DES PROBLÈMES:")
            for j, issue in enumerate(issues[:12], 1):  # Limiter à 12
                print(f"{j:2d}. {issue}")
        
        return issues
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return [f"ERREUR ANALYSE: {e}"]

def main():
    """Fonction principale"""
    print("🚀 Recherche des Problèmes Spécifiques dans main.py")
    print("=" * 55)
    
    if not os.path.exists('src/main.py'):
        print("❌ Fichier src/main.py non trouvé")
        return 1
    
    issues = analyze_main_py()
    
    print(f"\n📋 RÉSUMÉ:")
    print(f"Problèmes trouvés: {len(issues)}")
    
    if len(issues) == 0:
        print("🎉 Aucun problème détecté!")
        return 0
    elif len(issues) <= 5:
        print("✅ Quelques problèmes mineurs")
        return 0
    else:
        print("⚠️ Plusieurs problèmes à corriger")
        return 1

if __name__ == "__main__":
    sys.exit(main())
