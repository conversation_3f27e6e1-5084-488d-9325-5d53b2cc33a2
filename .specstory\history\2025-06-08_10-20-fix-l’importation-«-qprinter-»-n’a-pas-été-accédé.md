<!-- Generated by SpecStory -->

# /fix L’importation « QPrinter » n’a pas été accédé (2025-06-08 11:20:40)

_**User**_

/fix L’importation « QPrinter » n’a pas été accédée

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Le problème est que l'importation `from PySide6.QtPrintSupport import QPrinter` n'est pas utilisée à l'endroit indiqué. <PERSON>ur corriger cela, il suffit de supprimer cette ligne d'importation inutile si elle n'est pas suivie d'un usage direct de `QPrinter` dans ce bloc.

````python
# ...existing code...
# (Supprimer la ligne suivante si elle apparaît seule et n'est pas suivie d'un usage direct)
# from PySide6.QtPrintSupport import QPrinter
# ...existing code...
````

---

