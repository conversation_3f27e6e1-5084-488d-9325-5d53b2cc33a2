#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST ROBOT IA FINAL
Test complet fonctionnement Robot IA
"""

print("🤖 ROBOT IA TEST FINAL")
print("=" * 25)
print("✅ Python fonctionne")
print("✅ Affichage console OK")
print("✅ Encodage UTF-8 OK")
print()

print("📁 Test modules Robot IA...")
import sys
import os

# Ajout chemin modules
sys.path.append('C:/Users/<USER>/Desktop/RobotIA_SamNord/modules')

modules_test = [
    'intelligence_ia_transcendante',
    'scanner_telecommunications', 
    'cybersecurite_ethique',
    'tracking_telephones',
    'traduction_temps_reel',
    'communication_multilingue',
    'tests_automatiques',
    'auto_reparation'
]

modules_ok = 0
modules_erreur = 0

for module in modules_test:
    try:
        exec(f'import {module}')
        print(f"✅ {module}: OK")
        modules_ok += 1
    except Exception as e:
        print(f"❌ {module}: {str(e)[:50]}")
        modules_erreur += 1

print()
print(f"📊 RÉSULTATS:")
print(f"   ✅ Modules OK: {modules_ok}")
print(f"   ❌ Modules erreur: {modules_erreur}")
print(f"   📈 Taux réussite: {(modules_ok/(modules_ok+modules_erreur)*100):.1f}%")

print()
print("🧪 Test application principale...")
try:
    sys.path.append('C:/Users/<USER>/Desktop/RobotIA_SamNord')
    # Test import sans exécution
    with open('C:/Users/<USER>/Desktop/RobotIA_SamNord/robot_ia_universel.py', 'r', encoding='utf-8') as f:
        content = f.read()
        if 'class UniversalRobotIA' in content:
            print("✅ Application principale: Structure OK")
        else:
            print("❌ Application principale: Structure manquante")
except Exception as e:
    print(f"❌ Application principale: {str(e)[:50]}")

print()
print("🎉 TEST ROBOT IA TERMINÉ !")
print("🚀 Le Robot IA est prêt à être utilisé !")
print()
input("Appuyez sur Entrée pour continuer...")
