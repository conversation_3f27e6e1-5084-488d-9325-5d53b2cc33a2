@echo off
title 🌍 Installation Traduction Temps Réel
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🌍 INSTALLATION TRADUCTION TEMPS RÉEL                                  ██
echo ██    🗣️ Support 100+ langues + Reconnaissance vocale                        ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🌍 INSTALLATION TRADUCTION UNIVERSELLE
echo ======================================
echo 👤 SamNord@110577
echo 🗣️ 100+ langues supportées
echo 🎤 Reconnaissance vocale
echo 🔊 Synthèse vocale
echo.

echo 🔄 Vérification Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python requis !
    echo 📥 Installez Python depuis python.org
    pause
    exit /b 1
)
echo ✅ Python détecté

echo.
echo 📦 INSTALLATION DÉPENDANCES TRADUCTION...
echo.

echo 📦 Installation Google Translate...
pip install googletrans==4.0.0rc1 --quiet
if %errorLevel% equ 0 (echo ✅ Google Translate installé) else (echo ⚠️ Erreur Google Translate)

echo 📦 Installation Speech Recognition...
pip install SpeechRecognition --quiet
if %errorLevel% equ 0 (echo ✅ Speech Recognition installé) else (echo ⚠️ Erreur Speech Recognition)

echo 📦 Installation Text-to-Speech...
pip install pyttsx3 --quiet
if %errorLevel% equ 0 (echo ✅ Text-to-Speech installé) else (echo ⚠️ Erreur Text-to-Speech)

echo 📦 Installation PyAudio (microphone)...
pip install pyaudio --quiet
if %errorLevel% equ 0 (echo ✅ PyAudio installé) else (echo ⚠️ Erreur PyAudio - Essai alternatif...)

REM Tentative installation PyAudio alternative
if %errorLevel% neq 0 (
    echo 🔄 Tentative installation PyAudio alternative...
    pip install pipwin --quiet
    pipwin install pyaudio --quiet
    if %errorLevel% equ 0 (echo ✅ PyAudio installé via pipwin) else (echo ⚠️ PyAudio non installé - Microphone limité)
)

echo 📦 Installation Requests...
pip install requests --quiet
if %errorLevel% equ 0 (echo ✅ Requests installé) else (echo ⚠️ Erreur Requests)

echo 📦 Installation Tkinter (interface)...
REM Tkinter est généralement inclus avec Python
python -c "import tkinter; print('✅ Tkinter disponible')" 2>nul || echo ⚠️ Tkinter non disponible

echo 📦 Installation dépendances optionnelles...
pip install openai --quiet >nul 2>&1
pip install pygame --quiet >nul 2>&1
pip install playsound --quiet >nul 2>&1

echo.
echo ✅ INSTALLATION DÉPENDANCES TERMINÉE !
echo.

echo 🔄 TEST FONCTIONNALITÉS...
echo.

echo 🧪 Test import modules...
python -c "
try:
    from googletrans import Translator
    print('✅ Google Translate: OK')
except ImportError:
    print('❌ Google Translate: ERREUR')

try:
    import speech_recognition as sr
    print('✅ Speech Recognition: OK')
except ImportError:
    print('❌ Speech Recognition: ERREUR')

try:
    import pyttsx3
    print('✅ Text-to-Speech: OK')
except ImportError:
    print('❌ Text-to-Speech: ERREUR')

try:
    import tkinter
    print('✅ Interface graphique: OK')
except ImportError:
    print('❌ Interface graphique: ERREUR')
"

echo.
echo 🧪 Test traduction simple...
python -c "
try:
    from googletrans import Translator
    translator = Translator()
    result = translator.translate('Hello SamNord@110577', dest='fr')
    print(f'✅ Test traduction: {result.text}')
except Exception as e:
    print(f'❌ Erreur test traduction: {e}')
"

echo.
echo 🧪 Test synthèse vocale...
python -c "
try:
    import pyttsx3
    engine = pyttsx3.init()
    print('✅ Synthèse vocale initialisée')
    # Test sans son pour éviter interruption
    engine.setProperty('rate', 150)
    print('✅ Configuration synthèse OK')
except Exception as e:
    print(f'❌ Erreur synthèse vocale: {e}')
"

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██         ✅ INSTALLATION TRADUCTION TERMINÉE !                             ██
echo ██         🌍 ROBOT IA MULTILINGUE OPÉRATIONNEL !                            ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🎉 INSTALLATION TRADUCTION RÉUSSIE !
echo.
echo 🌍 FONCTIONNALITÉS DISPONIBLES:
echo ✅ Traduction 100+ langues
echo ✅ Reconnaissance vocale multilingue
echo ✅ Synthèse vocale naturelle
echo ✅ Interface graphique intuitive
echo ✅ Conversation interactive
echo.
echo 🚀 UTILISATION:
echo 1. Lancer Robot IA Universel
echo 2. Choisir option 5: Traduction Temps Réel
echo 3. Ou option 6: Communication Multilingue
echo.
echo 🎯 LANGUES SUPPORTÉES (exemples):
echo 🇫🇷 Français, 🇬🇧 Anglais, 🇪🇸 Espagnol, 🇩🇪 Allemand
echo 🇮🇹 Italien, 🇵🇹 Portugais, 🇷🇺 Russe, 🇨🇳 Chinois
echo 🇯🇵 Japonais, 🇰🇷 Coréen, 🇸🇦 Arabe, 🇮🇳 Hindi
echo Et 80+ autres langues !
echo.
echo 💡 CONSEILS:
echo - Utilisez un microphone de qualité
echo - Parlez clairement et distinctement
echo - Testez différentes langues
echo - Sauvegardez vos conversations
echo.
echo 🔐 VOS IDENTIFIANTS:
echo 👤 Utilisateur: SamNord@110577
echo 🔑 Mot de passe: NorDine@22
echo.
echo 🌟 PROFITEZ DE VOTRE ROBOT IA MULTILINGUE !
echo.
pause
exit /b 0
