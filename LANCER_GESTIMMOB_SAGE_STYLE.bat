@echo off
title GESTIMMOB STYLE SAGE IMMOBILISATION
color 0B

echo.
echo ========================================================================
echo                    🏢 GESTIMMOB STYLE SAGE IMMOBILISATION 🏢
echo              Interface professionnelle avec TOUS vos critères
echo                        Fond bleu turquoise + style Sage
echo ========================================================================
echo.

echo [%TIME%] Lancement de GestImmob Style Sage...

REM Variables
set "PYTHON_CMD="
set "INTERFACE_PRETE=0"

echo.
echo 🔍 DETECTION PYTHON...

py --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=py"
    for /f "tokens=*" %%i in ('py --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :verifier_interface
)

python --version >nul 2>&1
if not errorlevel 1 (
    set "PYTHON_CMD=python"
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python detecte: %%i
    goto :verifier_interface
)

echo ❌ Python non detecte
goto :end

:verifier_interface
echo.
echo 🔍 VERIFICATION DE L'INTERFACE SAGE...

if exist "gestimmob_sage_style_complet.py" (
    echo ✅ Interface Sage trouvee
    set /a INTERFACE_PRETE=1
) else (
    echo ❌ Interface Sage non trouvee
    goto :end
)

echo.
echo 📦 VERIFICATION DEPENDANCES...

%PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul
if errorlevel 1 (
    echo ❌ PySide6 manquant - Installation...
    %PYTHON_CMD% -m pip install PySide6 --quiet
    if not errorlevel 1 (
        echo ✅ PySide6 installe
    )
) else (
    echo ✅ PySide6 OK
)

%PYTHON_CMD% -c "import sqlite3; print('✅ SQLite3: OK')" 2>nul
if errorlevel 1 (
    echo ❌ SQLite3 manquant
) else (
    echo ✅ SQLite3 OK
)

echo.
echo ========================================================================
echo                    🚀 LANCEMENT INTERFACE SAGE STYLE 🚀
echo ========================================================================
echo.

echo [%TIME%] Demarrage de l'interface style Sage Immobilisation...
echo.

echo 🎯 FONCTIONNALITES DISPONIBLES:
echo   🎨 Interface style Sage Immobilisation
echo   🌊 Fond bleu turquoise professionnel
echo   📋 Menu complet avec tous les modules
echo   🔧 Barre d'outils style Sage
echo   📊 Zone d'informations et statistiques
echo   🧮 Calculatrice scientifique avancee
echo   ⚙️ Parametres administrateur complets
echo   🚗 Module Parc Auto avec TOUS vos criteres
echo   🔢 Codification automatique INV001
echo   💰 Symbole monetaire modifiable
echo.

echo 📋 MODULES AVEC CRITERES PRESERVES:
echo   🏠 Gestion des Biens          🏢 Fournisseurs
echo   📦 Inventaire                🚗 Parc Auto (COMPLET)
echo   🐄 Suivi des Animaux         🔨 Suivi des Travaux
echo   🔧 Outils de Travaux         🧮 Calculatrice (COMPLETE)
echo   📄 Documents                 🖨️ Impression
echo   📚 Archive                   ⚒️ Outillage
echo   🏭 Moyens Generaux           🏨 Hotellerie
echo   🪑 Agencement                🏗️ Batis
echo.

echo 🎨 STYLE SAGE IMMOBILISATION:
echo   🌊 Fond bleu turquoise degrade
echo   📋 Menu professionnel complet
echo   🔧 Barre d'outils integree
echo   📊 Zone d'informations systeme
echo   🎯 Boutons modules style Sage
echo   📈 Statistiques en temps reel
echo.

echo Configuration:
echo   Python: %PYTHON_CMD%
echo   Interface: gestimmob_sage_style_complet.py
echo   Style: Sage Immobilisation + Bleu turquoise
echo   Modules: 16 modules avec criteres complets
echo   Parc Auto: TOUS vos criteres preserves
echo.

echo 🎯 Lancement en cours...
echo.

REM Lancement de l'interface Sage
%PYTHON_CMD% gestimmob_sage_style_complet.py

set "EXIT_CODE=%ERRORLEVEL%"

echo.
echo ========================================================================
echo                              RESULTAT FINAL
echo ========================================================================
echo.

if %EXIT_CODE%==0 (
    echo [%TIME%] ✅ INTERFACE SAGE FERMEE NORMALEMENT
    echo.
    echo 🎉 LANCEMENT REUSSI !
    echo ✅ Interface style Sage Immobilisation parfaite
    echo ✅ Fond bleu turquoise professionnel applique
    echo ✅ TOUS vos criteres du Parc Auto preserves
    echo ✅ Calculatrice scientifique fonctionnelle
    echo ✅ Parametres administrateur complets
    echo.
    echo 🎯 FONCTIONNALITES TESTEES:
    echo   ✅ Menu professionnel style Sage
    echo   ✅ Barre d'outils complete
    echo   ✅ 16 modules organises en grille
    echo   ✅ Module Parc Auto avec 50+ criteres
    echo   ✅ Calculatrice avec fonctions scientifiques
    echo   ✅ Parametres admin avec onglets
    echo   ✅ Style bleu turquoise degrade
    echo.
    echo 💡 Pour relancer: executez a nouveau ce script
    echo    Ou tapez: %PYTHON_CMD% gestimmob_sage_style_complet.py
    echo.
) else (
    echo [%TIME%] ❌ INTERFACE FERMEE AVEC ERREUR (Code: %EXIT_CODE%)
    echo.
    echo 🔧 DIAGNOSTIC:
    
    %PYTHON_CMD% -c "print('✅ Python de base: OK')" 2>nul || echo ❌ Python de base: Probleme
    %PYTHON_CMD% -c "import PySide6; print('✅ PySide6: OK')" 2>nul || echo ❌ PySide6: Probleme
    %PYTHON_CMD% -c "import sqlite3; print('✅ SQLite: OK')" 2>nul || echo ❌ SQLite: Probleme
    
    echo.
    echo 💡 SOLUTIONS:
    echo 1. Relancez ce script
    echo 2. Installez PySide6: pip install PySide6
    echo 3. Verifiez les erreurs ci-dessus
)

:end
echo.
echo ========================================================================
echo                              RESUME
echo ========================================================================
echo.

echo [%TIME%] Script de lancement termine

if %EXIT_CODE%==0 (
    echo.
    echo 🏆 SUCCES TOTAL !
    echo ✅ Interface Sage Immobilisation lancee parfaitement
    echo ✅ TOUS vos criteres du Parc Auto preserves
    echo ✅ Style bleu turquoise professionnel applique
    echo.
    echo 🎯 INTERFACE SAGE STYLE OPERATIONNELLE !
    echo.
    echo 📋 CRITERES PARC AUTO PRESERVES:
    echo   ✅ Type vehicule, marque, modele, immatriculation
    echo   ✅ Chauffeur titulaire et suppleant
    echo   ✅ GPS complet avec fournisseur et coordonnees
    echo   ✅ Entretien, pannes, reparations
    echo   ✅ Amortissement, constat, photos
    echo   ✅ Calcul kilometrage automatique
    echo   ✅ Vidange, pression pneus, carburant
    echo   ✅ Assurance, carte grise, observations
    echo.
    echo 🔧 FONCTIONNALITES AVANCEES:
    echo   ✅ Calculatrice scientifique complete
    echo   ✅ Parametres administrateur avec onglets
    echo   ✅ Menu professionnel style Sage
    echo   ✅ Barre d'outils integree
    echo   ✅ Zone d'informations systeme
    echo   ✅ Statistiques en temps reel
    echo.
) else (
    echo.
    echo ⚠️ PROBLEME DE LANCEMENT
    echo.
    echo 📋 VERIFICATIONS EFFECTUEES:
    echo   - Python: %PYTHON_CMD%
    echo   - Interface: %INTERFACE_PRETE%
    echo   - Dependances: Verifiees
    echo.
    echo 🔧 ACTIONS RECOMMANDEES:
    echo 1. Relancez ce script
    echo 2. Installez manuellement: pip install PySide6
    echo 3. Verifiez les messages d erreur
)

echo.
echo 🏢 GESTIMMOB STYLE SAGE IMMOBILISATION
echo Interface professionnelle avec TOUS vos criteres preserves
echo Fond bleu turquoise + style Sage parfaitement applique
echo.

echo Appuyez sur une touche pour fermer...
pause >nul
