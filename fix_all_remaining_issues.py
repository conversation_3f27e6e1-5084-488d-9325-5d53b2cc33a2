#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger TOUS les problèmes restants dans tous les fichiers
"""

import ast
import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Any, Tuple

class CompletePythonFixer:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        self.files_processed = []
        
    def analyze_file_comprehensive(self, file_path: Path) -> Dict[str, Any]:
        """Analyse complète d'un fichier Python"""
        print(f"\n🔍 Analyse complète de {file_path}...")
        
        result = {
            "path": str(file_path),
            "syntax_errors": [],
            "type_errors": [],
            "import_errors": [],
            "issues_count": 0,
            "is_valid": True
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test 1: Syntaxe AST
            try:
                ast.parse(content)
                print(f"  ✅ AST: OK")
            except SyntaxError as e:
                issue = f"Erreur syntaxe ligne {e.lineno}: {e.msg}"
                result["syntax_errors"].append(issue)
                result["is_valid"] = False
                print(f"  ❌ AST: {issue}")
                if e.text:
                    print(f"      Code: {e.text.strip()}")
            
            # Test 2: Compilation
            try:
                compile(content, str(file_path), 'exec')
                print(f"  ✅ Compilation: OK")
            except SyntaxError as e:
                issue = f"Erreur compilation ligne {e.lineno}: {e.msg}"
                result["syntax_errors"].append(issue)
                result["is_valid"] = False
                print(f"  ❌ Compilation: {issue}")
            
            # Test 3: Problèmes de types spécifiques
            lines = content.splitlines()
            
            for i, line in enumerate(lines, 1):
                # Problème: dict[str, Any] au lieu de Dict[str, Any]
                if re.search(r'\bdict\[.*?\]', line) and 'Dict' not in line:
                    issue = f"Ligne {i}: 'dict[...]' devrait être 'Dict[...]'"
                    result["type_errors"].append(issue)
                    result["is_valid"] = False
                    print(f"  ⚠️ {issue}")
                
                # Problème: list[...] au lieu de List[...]
                if re.search(r'\blist\[.*?\]', line) and 'List' not in line:
                    issue = f"Ligne {i}: 'list[...]' devrait être 'List[...]'"
                    result["type_errors"].append(issue)
                    result["is_valid"] = False
                    print(f"  ⚠️ {issue}")
                
                # Problème: any au lieu de Any
                if ': any =' in line.lower():
                    issue = f"Ligne {i}: 'any' devrait être 'Any'"
                    result["type_errors"].append(issue)
                    result["is_valid"] = False
                    print(f"  ⚠️ {issue}")
            
            # Test 4: Imports manquants
            needs_dict = 'Dict[' in content or 'dict[' in content
            needs_list = 'List[' in content or 'list[' in content
            needs_any = 'Any' in content or ': any =' in content.lower()
            
            has_typing_import = 'from typing import' in content
            
            if (needs_dict or needs_list or needs_any) and not has_typing_import:
                issue = "Import typing manquant"
                result["import_errors"].append(issue)
                result["is_valid"] = False
                print(f"  ⚠️ {issue}")
            
            # Compter les problèmes
            result["issues_count"] = (
                len(result["syntax_errors"]) +
                len(result["type_errors"]) +
                len(result["import_errors"])
            )
            
            if result["issues_count"] == 0:
                print(f"  ✅ Aucun problème détecté")
            else:
                print(f"  ❌ {result['issues_count']} problème(s) détecté(s)")
                self.issues_found.append(result)
            
            return result
            
        except Exception as e:
            issue = f"Erreur lecture: {e}"
            result["syntax_errors"].append(issue)
            result["is_valid"] = False
            print(f"  ❌ Erreur: {e}")
            return result
    
    def fix_file_comprehensive(self, file_path: Path) -> bool:
        """Correction complète d'un fichier"""
        print(f"\n🔧 Correction complète de {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            lines = content.splitlines()
            
            # Correction 1: Ajouter imports typing si nécessaires
            needs_dict = 'Dict[' in content or 'dict[' in content
            needs_list = 'List[' in content or 'list[' in content
            needs_any = 'Any' in content or ': any =' in content.lower()
            
            has_typing_import = 'from typing import' in content
            
            if (needs_dict or needs_list or needs_any) and not has_typing_import:
                # Trouver où insérer l'import
                insert_pos = 0
                for i, line in enumerate(lines):
                    if line.strip() and not line.startswith('#'):
                        insert_pos = i
                        break
                
                typing_imports = []
                if needs_any:
                    typing_imports.append("Any")
                if needs_dict:
                    typing_imports.append("Dict")
                if needs_list:
                    typing_imports.append("List")
                
                # Ajouter d'autres imports utiles
                typing_imports.extend(["Optional", "Tuple", "Union"])
                
                import_line = f"from typing import {', '.join(sorted(set(typing_imports)))}"
                lines.insert(insert_pos, import_line)
                lines.insert(insert_pos + 1, "")
                
                self.fixes_applied.append(f"{file_path.name}: Import typing ajouté")
                print(f"  ✅ Import typing ajouté: {import_line}")
            
            # Correction 2: Corriger dict[...] en Dict[...]
            for i, line in enumerate(lines):
                if 'dict[' in line and 'Dict[' not in line:
                    lines[i] = re.sub(r'\bdict\[', 'Dict[', line)
                    self.fixes_applied.append(f"{file_path.name}: dict[...] corrigé en Dict[...] ligne {i+1}")
                    print(f"  ✅ dict[...] corrigé en Dict[...] ligne {i+1}")
            
            # Correction 3: Corriger list[...] en List[...]
            for i, line in enumerate(lines):
                if 'list[' in line and 'List[' not in line:
                    lines[i] = re.sub(r'\blist\[', 'List[', line)
                    self.fixes_applied.append(f"{file_path.name}: list[...] corrigé en List[...] ligne {i+1}")
                    print(f"  ✅ list[...] corrigé en List[...] ligne {i+1}")
            
            # Correction 4: Corriger 'any' en 'Any'
            for i, line in enumerate(lines):
                if ': any =' in line:
                    lines[i] = line.replace(': any =', ': Any =')
                    self.fixes_applied.append(f"{file_path.name}: 'any' corrigé en 'Any' ligne {i+1}")
                    print(f"  ✅ 'any' corrigé en 'Any' ligne {i+1}")
            
            # Reconstruire le contenu
            new_content = '\n'.join(lines)
            
            # Valider la syntaxe
            try:
                ast.parse(new_content)
                compile(new_content, str(file_path), 'exec')
                
                # Sauvegarder si des changements ont été faits
                if new_content != original_content:
                    # Backup
                    backup_path = file_path.with_suffix('.py.backup_complete')
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(original_content)
                    
                    # Sauvegarder le fichier corrigé
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    print(f"  ✅ Fichier corrigé et sauvegardé")
                    self.files_processed.append(str(file_path))
                    return True
                else:
                    print(f"  ✅ Aucune correction nécessaire")
                    return True
                    
            except (SyntaxError, Exception) as e:
                print(f"  ❌ Erreur après correction: {e}")
                return False
                
        except Exception as e:
            print(f"  ❌ Erreur traitement: {e}")
            return False
    
    def run_complete_fix(self) -> bool:
        """Exécute la correction complète de tous les fichiers"""
        print("🚀 CORRECTION COMPLÈTE DE TOUS LES PROBLÈMES RESTANTS")
        print("=" * 80)
        
        # Trouver tous les fichiers Python
        python_files = []
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.') or d == '.vscode']
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        
        print(f"📁 Fichiers Python trouvés: {len(python_files)}")
        
        # Phase 1: Analyse complète
        print(f"\n🔍 PHASE 1: ANALYSE COMPLÈTE")
        print("-" * 50)
        
        files_with_issues = []
        for file_path in python_files:
            result = self.analyze_file_comprehensive(file_path)
            if not result["is_valid"]:
                files_with_issues.append(file_path)
        
        print(f"\n📊 Fichiers avec problèmes: {len(files_with_issues)}")
        
        # Phase 2: Correction
        if files_with_issues:
            print(f"\n🔧 PHASE 2: CORRECTION COMPLÈTE")
            print("-" * 50)
            
            success_count = 0
            for file_path in files_with_issues:
                if self.fix_file_comprehensive(file_path):
                    success_count += 1
            
            print(f"\n📊 Fichiers corrigés: {success_count}/{len(files_with_issues)}")
        
        # Phase 3: Validation finale
        print(f"\n✅ PHASE 3: VALIDATION FINALE")
        print("-" * 50)
        
        final_success_count = 0
        for file_path in python_files:
            result = self.analyze_file_comprehensive(file_path)
            if result["is_valid"]:
                final_success_count += 1
        
        # Résumé final
        print("\n" + "="*80)
        print("📋 RÉSUMÉ FINAL COMPLET")
        print("="*80)
        
        success_rate = (final_success_count / len(python_files)) * 100 if python_files else 0
        
        print(f"✅ Fichiers sans problème: {final_success_count}/{len(python_files)}")
        print(f"📈 Taux de réussite: {success_rate:.1f}%")
        print(f"🔧 Corrections appliquées: {len(self.fixes_applied)}")
        print(f"📁 Fichiers traités: {len(self.files_processed)}")
        
        if self.fixes_applied:
            print(f"\n🔧 CORRECTIONS APPLIQUÉES:")
            for fix in self.fixes_applied:
                print(f"  - {fix}")
        
        if success_rate >= 95:
            print(f"\n🎉 TOUS LES PROBLÈMES PYTHON SONT MAINTENANT CORRIGÉS !")
            print(f"✅ Syntaxe parfaite dans tous les fichiers")
            print(f"✅ Types corrects partout")
            print(f"✅ Imports complets")
            print(f"✅ Code parfaitement fonctionnel")
            return True
        else:
            remaining_issues = len(python_files) - final_success_count
            print(f"\n⚠️ {remaining_issues} fichier(s) nécessitent encore des corrections")
            return False

def main():
    """Fonction principale"""
    fixer = CompletePythonFixer()
    success = fixer.run_complete_fix()
    
    if success:
        print("\n🏆 CORRECTION COMPLÈTE RÉUSSIE !")
        print("Tous les problèmes Python ont été résolus définitivement.")
    else:
        print("\n⚠️ Correction partielle - vérifiez les erreurs restantes.")
    
    return success

if __name__ == "__main__":
    main()
