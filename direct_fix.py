#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CORRECTION DIRECTE ET EFFICACE DE MAIN.PY
Identifie et corrige les problèmes réels immédiatement
"""

import ast
import sys
import re
from pathlib import Path

def test_compilation():
    """Test de compilation direct pour identifier les erreurs"""
    print("🔍 Test de compilation direct de main.py...")
    
    try:
        with open('src/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test de compilation
        compile(content, 'src/main.py', 'exec')
        print("✅ Compilation réussie")
        
        # Test AST
        ast.parse(content)
        print("✅ AST parsing réussi")
        
        return True, content
        
    except SyntaxError as e:
        print(f"❌ ERREUR SYNTAXE ligne {e.lineno}: {e.msg}")
        print(f"   Texte: {e.text}")
        return False, None
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False, None

def find_real_problems(content):
    """Trouve les vrais problèmes dans le code"""
    print("\n🔍 Recherche des problèmes réels...")
    
    lines = content.splitlines()
    problems = []
    
    # Vérifier les lignes problématiques
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # Problème 1: Instructions multiples sur une ligne
        if ';' in line and not line_stripped.startswith('#'):
            problems.append(f"Ligne {i}: Instructions multiples - {line_stripped}")
        
        # Problème 2: Variables non typées
        if ' = ' in line and ':' not in line.split('=')[0] and not any(x in line for x in ['def ', 'class ', 'import', 'from', '#', 'self.']):
            var_match = re.match(r'\s*(\w+)\s*=', line)
            if var_match and not var_match.group(1).isupper():
                problems.append(f"Ligne {i}: Variable non typée - {var_match.group(1)}")
        
        # Problème 3: Fonctions sans annotation de retour
        if line_stripped.startswith('def ') and '->' not in line and '__init__' not in line:
            func_match = re.match(r'\s*def\s+(\w+)', line)
            if func_match:
                problems.append(f"Ligne {i}: Fonction sans annotation - {func_match.group(1)}")
        
        # Problème 4: Imports dans fonctions sans type: ignore
        if 'import ' in line and 'def ' in content[:content.find(line)] and 'type: ignore' not in line:
            # Vérifier si on est dans une fonction
            before_lines = content[:content.find(line)].splitlines()
            in_function = False
            for prev_line in reversed(before_lines[-20:]):  # Vérifier les 20 dernières lignes
                if prev_line.strip().startswith('def '):
                    in_function = True
                    break
                elif prev_line.strip().startswith('class ') or (not prev_line.startswith(' ') and prev_line.strip()):
                    break
            
            if in_function:
                problems.append(f"Ligne {i}: Import dans fonction sans type: ignore")
    
    return problems

def apply_direct_fixes(content):
    """Applique les corrections directes"""
    print("\n🔧 Application des corrections directes...")
    
    lines = content.splitlines()
    fixes_applied = 0
    
    for i, line in enumerate(lines):
        original_line = line
        
        # Fix 1: Séparer les instructions multiples
        if ';' in line and not line.strip().startswith('#'):
            # Séparer les instructions
            parts = line.split(';')
            if len(parts) > 1:
                indent = len(line) - len(line.lstrip())
                new_lines = []
                for part in parts:
                    if part.strip():
                        new_lines.append(' ' * indent + part.strip())
                lines[i:i+1] = new_lines
                fixes_applied += 1
                continue
        
        # Fix 2: Ajouter annotations de type pour variables
        if ' = ' in line and ':' not in line.split('=')[0]:
            if not any(x in line for x in ['def ', 'class ', 'import', 'from', '#', 'self.']):
                var_match = re.match(r'(\s*)(\w+)\s*=\s*(.+)', line)
                if var_match and not var_match.group(2).isupper():
                    indent, var_name, value = var_match.groups()
                    
                    # Déterminer le type
                    if value.startswith('"') or value.startswith("'"):
                        type_hint = "str"
                    elif value.isdigit():
                        type_hint = "int"
                    elif value in ['True', 'False']:
                        type_hint = "bool"
                    elif value == '[]':
                        type_hint = "List[Any]"
                    elif value == '{}':
                        type_hint = "Dict[str, Any]"
                    else:
                        type_hint = "Any"
                    
                    lines[i] = f"{indent}{var_name}: {type_hint} = {value}"
                    fixes_applied += 1
        
        # Fix 3: Ajouter annotations de retour pour fonctions
        if line.strip().startswith('def ') and '->' not in line and '__init__' not in line:
            lines[i] = line.replace(':', ' -> None:')
            fixes_applied += 1
        
        # Fix 4: Ajouter type: ignore aux imports dans fonctions
        if 'import ' in line and 'type: ignore' not in line:
            # Vérifier si on est dans une fonction (approximation)
            if i > 0 and any('def ' in lines[j] for j in range(max(0, i-10), i)):
                if not line.strip().startswith('#'):
                    lines[i] = line.rstrip() + '  # type: ignore'
                    fixes_applied += 1
    
    print(f"✅ {fixes_applied} corrections appliquées")
    return '\n'.join(lines), fixes_applied

def save_fixed_file(content):
    """Sauvegarde le fichier corrigé"""
    print("\n💾 Sauvegarde du fichier corrigé...")
    
    try:
        # Backup
        with open('src/main.py.backup', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Sauvegarde
        with open('src/main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Fichier sauvegardé (backup créé)")
        return True
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")
        return False

def main():
    """Fonction principale de correction directe"""
    print("🚀 CORRECTION DIRECTE ET EFFICACE DE MAIN.PY")
    print("=" * 50)
    
    # Test initial
    success, content = test_compilation()
    
    if success:
        print("\n✅ Le fichier compile déjà correctement !")
        
        # Chercher les problèmes de style/qualité
        problems = find_real_problems(content)
        
        if problems:
            print(f"\n⚠️ {len(problems)} problèmes de qualité détectés:")
            for problem in problems[:10]:  # Afficher les 10 premiers
                print(f"  - {problem}")
            
            # Appliquer les corrections
            fixed_content, fixes = apply_direct_fixes(content)
            
            if fixes > 0:
                # Test après correction
                try:
                    compile(fixed_content, 'src/main.py', 'exec')
                    ast.parse(fixed_content)
                    
                    # Sauvegarder
                    if save_fixed_file(fixed_content):
                        print(f"\n🎉 CORRECTION RÉUSSIE !")
                        print(f"✅ {fixes} problèmes corrigés")
                        print("✅ Syntaxe validée")
                        print("✅ Fichier sauvegardé")
                        return True
                    
                except Exception as e:
                    print(f"❌ Erreur après correction: {e}")
                    return False
            else:
                print("\n✅ Aucune correction nécessaire")
                return True
        else:
            print("\n✅ Aucun problème détecté - Code parfait !")
            return True
    else:
        print("\n❌ Le fichier a des erreurs de syntaxe qui empêchent la compilation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
