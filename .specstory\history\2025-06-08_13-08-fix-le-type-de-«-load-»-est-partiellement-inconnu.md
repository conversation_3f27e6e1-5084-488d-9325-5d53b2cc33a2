<!-- Generated by SpecStory -->

# /fix Le type de « load » est partiellement inconnu (2025-06-08 14:08:10)

_**User**_

/fix Le type de « load » est partiellement inconnu
  Le type de "load" est "() -> dict[Unknown, Unknown]"

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Déclarez explicitement le type de retour de la méthode `load` pour lever l'ambiguïté de typage.

````python
# ...existing code...
    def load(self) -> Dict[str, Any]:
        try:
            with open(self.filename, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception:
            return {}
# ...existing code...
````

---

