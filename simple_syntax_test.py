#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de syntaxe Python
"""

import ast
import os
import sys

def test_python_syntax(file_path):
    """Test simple de syntaxe Python"""
    print(f"Test de {file_path}...")
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier non trouvé: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test AST
        ast.parse(content)
        print(f"✅ {file_path}: SYNTAXE OK")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path}: ERREUR ligne {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path}: ERREUR: {e}")
        return False

def main():
    """Test principal"""
    print("🔍 TEST SIMPLE DE SYNTAXE PYTHON")
    print("=" * 50)
    
    # Changer vers le bon répertoire
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"Répertoire: {os.getcwd()}")
    
    files = [
        'src/main.py',
        'src/config.py',
        'src/database/db_connection.py',
        'src/database/models.py',
        'src/views/admin_view.py',
        'src/auth/user_management.py'
    ]
    
    results = []
    
    for file_path in files:
        result = test_python_syntax(file_path)
        results.append(result)
    
    # Résumé
    print(f"\n{'='*50}")
    print("RÉSUMÉ")
    print(f"{'='*50}")
    
    valid_count = sum(results)
    total_count = len(results)
    
    print(f"Fichiers testés: {total_count}")
    print(f"Fichiers valides: {valid_count}")
    print(f"Fichiers avec erreurs: {total_count - valid_count}")
    
    if valid_count == total_count:
        print("\n🎉 TOUS LES FICHIERS PYTHON SONT SYNTAXIQUEMENT CORRECTS!")
        
        # Test du fichier SQL
        print(f"\n🔍 TEST DU FICHIER SQL")
        print("-" * 30)
        sql_file = 'src/database/migrations/init.sql'
        if os.path.exists(sql_file):
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            serial_count = sql_content.count('SERIAL PRIMARY KEY')
            autoincrement_count = sql_content.count('INTEGER PRIMARY KEY AUTOINCREMENT')
            
            print(f"SERIAL PRIMARY KEY: {serial_count} occurrences")
            print(f"INTEGER PRIMARY KEY AUTOINCREMENT: {autoincrement_count} occurrences")
            
            if serial_count == 0:
                print("✅ SQL: Toutes les occurrences SERIAL corrigées")
            else:
                print(f"❌ SQL: {serial_count} occurrences SERIAL restantes")
        
        return True
    else:
        print(f"\n🚨 {total_count - valid_count} FICHIER(S) AVEC DES ERREURS")
        return False

if __name__ == "__main__":
    try:
        success = main()
        print(f"\nRésultat final: {'SUCCÈS' if success else 'ÉCHEC'}")
    except Exception as e:
        print(f"Erreur: {e}")
        sys.exit(1)
