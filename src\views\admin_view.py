from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox, QHBoxLayout
from PySide6.QtCore import Qt

class AdminView(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Admin Dashboard")
        self.setGeometry(100, 100, 900, 700)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        title = QLabel("Tableau de bord Administrateur")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Accès rapide à tous les modules
        modules_layout = QHBoxLayout()
        self.btn_inventaire = QPushButton("Inventaire")
        self.btn_fournisseur = QPushButton("Fournisseurs")
        self.btn_recherche = QPushButton("Recherche")
        self.btn_reforme = QPushButton("Réformes")
        self.btn_document = QPushButton("Documents")
        self.btn_impression = QPushButton("Impression")
        self.btn_erp = QPushButton("ERP/Comptabilité")
        self.btn_calculatrice = QPushButton("Calculatrice")
        modules_layout.addWidget(self.btn_inventaire)
        modules_layout.addWidget(self.btn_fournisseur)
        modules_layout.addWidget(self.btn_recherche)
        modules_layout.addWidget(self.btn_reforme)
        modules_layout.addWidget(self.btn_document)
        modules_layout.addWidget(self.btn_impression)
        modules_layout.addWidget(self.btn_erp)
        modules_layout.addWidget(self.btn_calculatrice)
        layout.addLayout(modules_layout)

        # Boutons centralisés pour opérations globales
        ops_layout = QHBoxLayout()
        self.btn_sync = QPushButton("Synchroniser")
        self.btn_save = QPushButton("Sauvegarder")
        self.btn_refresh = QPushButton("Actualiser")
        ops_layout.addWidget(self.btn_sync)
        ops_layout.addWidget(self.btn_save)
        ops_layout.addWidget(self.btn_refresh)
        layout.addLayout(ops_layout)

        self.btn_manage_users = QPushButton("Gestion des utilisateurs")
        self.btn_manage_users.clicked.connect(self.manage_users)
        layout.addWidget(self.btn_manage_users)

        self.btn_view_logs = QPushButton("Voir les logs")
        self.btn_view_logs.clicked.connect(self.view_logs)
        layout.addWidget(self.btn_view_logs)

        self.btn_logout = QPushButton("Déconnexion")
        self.btn_logout.clicked.connect(self.logout)
        layout.addWidget(self.btn_logout)

        self.setLayout(layout)

        # Connexions pour accès modules (à relier à vos widgets/modules)
        self.btn_inventaire.clicked.connect(lambda: self.open_module("inventaire"))
        self.btn_fournisseur.clicked.connect(lambda: self.open_module("fournisseur"))
        self.btn_recherche.clicked.connect(lambda: self.open_module("recherche"))
        self.btn_reforme.clicked.connect(lambda: self.open_module("reforme"))
        self.btn_document.clicked.connect(lambda: self.open_module("document"))
        self.btn_impression.clicked.connect(lambda: self.open_module("impression"))
        self.btn_erp.clicked.connect(lambda: self.open_module("erp"))
        self.btn_calculatrice.clicked.connect(lambda: self.open_module("calculatrice"))
        self.btn_sync.clicked.connect(self.synchroniser)
        self.btn_save.clicked.connect(self.sauvegarder)
        self.btn_refresh.clicked.connect(self.actualiser)

    def open_module(self, module_name):
        QMessageBox.information(self, "Accès module", f"Ouverture du module : {module_name}")

    def synchroniser(self):
        # Appeler ici la méthode de synchronisation globale ou du module courant
        QMessageBox.information(self, "Synchronisation", "Synchronisation effectuée avec succès.")

    def sauvegarder(self):
        # Appeler ici la méthode de sauvegarde globale ou du module courant
        QMessageBox.information(self, "Sauvegarde", "Sauvegarde effectuée avec succès.")

    def actualiser(self):
        # Appeler ici la méthode d'actualisation globale ou du module courant
        QMessageBox.information(self, "Actualisation", "Actualisation effectuée avec succès.")

    def manage_users(self):
        QMessageBox.information(self, "Gestion des utilisateurs", "Fonctionnalité de gestion des utilisateurs à implémenter ici.")

    def view_logs(self):
        QMessageBox.information(self, "Voir les logs", "Fonctionnalité de consultation des logs à implémenter ici.")

    def logout(self):
        QMessageBox.information(self, "Déconnexion", "Vous avez été déconnecté.")
        self.close()