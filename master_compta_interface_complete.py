#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTER COMPTA GÉNÉRAL - Interface Complète
Tous modules fonctionnels • Critères détaillés • ComboBox • Listes
Boutons redimensionnés • Widgets agrandis • Sans référence Office
"""

import sys
import os
import json
import requests
import threading
import time
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFrame, QTabWidget, QSplitter, QTableWidget,
    QTableWidgetItem, QComboBox, QGroupBox, QGridLayout, QMessageBox,
    QMenuBar, QStatusBar, QProgressBar, QTextEdit, QLineEdit, QCheckBox,
    QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit, QListWidget, Q<PERSON>ree<PERSON>idget,
    QTreeWidgetItem, QFormLayout, QScrollArea, QSlider, QDial
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QDate, QTime
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QPainter

class EnhancedWeatherWidget(QWidget):
    """Widget météo agrandi avec plus de contenu"""

    def __init__(self):
        super().__init__()
        self.setup_enhanced_weather()
        self.update_weather()

    def setup_enhanced_weather(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Titre météo agrandi
        title = QLabel("🌤️ MÉTÉO COMPLÈTE 15 JOURS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(0,123,255,0.8);
                border: 3px solid #007bff;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        layout.addWidget(title)

        # Météo actuelle détaillée
        self.current_weather = QLabel("🌤️ Aujourd'hui: 22°C - Ensoleillé\n💨 Vent: 15 km/h Nord-Est\n💧 Humidité: 65%\n🌡️ Ressentie: 24°C\n👁️ Visibilité: 10 km\n📊 Pression: 1013 hPa")
        self.current_weather.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.current_weather.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                background: rgba(0,123,255,0.6);
                border: 2px solid #007bff;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                min-height: 120px;
            }
        """)
        layout.addWidget(self.current_weather)

        # Prévisions détaillées 15 jours
        self.forecast = QLabel("📅 PRÉVISIONS 15 JOURS:\n🌤️ Lun 18/12: 23°C Max, 15°C Min - Nuageux\n☀️ Mar 19/12: 25°C Max, 17°C Min - Ensoleillé\n🌧️ Mer 20/12: 18°C Max, 12°C Min - Pluie\n⛅ Jeu 21/12: 20°C Max, 14°C Min - Nuages\n☀️ Ven 22/12: 26°C Max, 18°C Min - Beau\n🌤️ Sam 23/12: 24°C Max, 16°C Min - Variable\n🌧️ Dim 24/12: 19°C Max, 13°C Min - Averses")
        self.forecast.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.forecast.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                background: rgba(0,123,255,0.4);
                border: 2px solid #007bff;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                min-height: 150px;
            }
        """)
        layout.addWidget(self.forecast)

        # Alertes météo
        self.alerts = QLabel("⚠️ ALERTES:\n🌧️ Risque pluie demain\n💨 Vent fort jeudi\n❄️ Gel possible weekend")
        self.alerts.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(255,152,0,0.6);
                border: 2px solid #ff9800;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.alerts)

    def update_weather(self):
        """Met à jour la météo avec plus de détails"""
        import random
        temp = random.randint(15, 30)
        conditions = ["☀️ Ensoleillé", "⛅ Nuageux", "🌤️ Partiellement nuageux", "🌧️ Pluvieux"]
        condition = random.choice(conditions)

        self.current_weather.setText(f"🌤️ Aujourd'hui: {temp}°C - {condition}\n💨 Vent: {random.randint(5, 25)} km/h Nord-Est\n💧 Humidité: {random.randint(40, 80)}%\n🌡️ Ressentie: {temp + random.randint(-2, 3)}°C\n👁️ Visibilité: {random.randint(8, 15)} km\n📊 Pression: {random.randint(995, 1025)} hPa")

class EnhancedPrayerWidget(QWidget):
    """Widget prières agrandi avec plus de fonctionnalités"""

    def __init__(self):
        super().__init__()
        self.setup_enhanced_prayer()
        self.update_prayer_times()

    def setup_enhanced_prayer(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Titre prières agrandi
        title = QLabel("🕌 HEURES PRIÈRE GPS AVANCÉES")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(76,175,80,0.8);
                border: 3px solid #4caf50;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        layout.addWidget(title)

        # Localisation GPS détaillée
        self.location = QLabel("📍 Localisation: Paris, France\n🌍 Coordonnées: 48.8566°N, 2.3522°E\n🕌 Qibla: 119° Sud-Est\n📡 Précision GPS: ±3 mètres")
        self.location.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.location.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                background: rgba(76,175,80,0.6);
                border: 2px solid #4caf50;
                border-radius: 10px;
                padding: 12px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.location)

        # Heures de prière détaillées
        self.prayer_times = QLabel("🌅 Fajr (Aube): 05:45 - 06:15\n🌞 Dhuhr (Midi): 12:30 - 15:45\n🌇 Asr (Après-midi): 15:45 - 18:15\n🌅 Maghrib (Coucher): 18:15 - 19:45\n🌙 Isha (Nuit): 20:00 - 05:45\n\n⏰ Temps restants:\n🕌 Prochaine: Dhuhr dans 2h 15min\n🔔 Notification: 4 minutes avant")
        self.prayer_times.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.prayer_times.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                background: rgba(76,175,80,0.4);
                border: 2px solid #4caf50;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                min-height: 180px;
            }
        """)
        layout.addWidget(self.prayer_times)

        # Paramètres prières
        self.settings = QLabel("⚙️ PARAMÈTRES:\n📐 Méthode: Ligue Islamique Mondiale\n🌅 Angle Fajr: 18°\n🌙 Angle Isha: 17°\n⏰ Notifications: Activées")
        self.settings.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 10pt;
                background: rgba(255,193,7,0.6);
                border: 2px solid #ffc107;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.settings)

    def update_prayer_times(self):
        """Met à jour les heures avec plus de détails"""
        now = datetime.now()
        # Calculs plus précis des heures de prière
        pass

class EnhancedDateTimeWidget(QWidget):
    """Widget date/heure agrandi avec calendrier"""

    def __init__(self):
        super().__init__()
        self.setup_enhanced_datetime()

        # Timer pour mise à jour
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)

    def setup_enhanced_datetime(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Date actuelle agrandie
        self.date_label = QLabel()
        self.date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.date_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18pt;
                font-weight: bold;
                background: rgba(255,87,34,0.8);
                border: 3px solid #ff5722;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
                min-height: 80px;
            }
        """)
        layout.addWidget(self.date_label)

        # Heure actuelle agrandie
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24pt;
                font-weight: bold;
                background: rgba(255,87,34,0.6);
                border: 3px solid #ff5722;
                border-radius: 12px;
                padding: 25px;
                margin: 5px;
                min-height: 100px;
            }
        """)
        layout.addWidget(self.time_label)

        # Informations supplémentaires
        self.extra_info = QLabel()
        self.extra_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.extra_info.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12pt;
                background: rgba(255,87,34,0.4);
                border: 2px solid #ff5722;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.extra_info)

        self.update_datetime()

    def update_datetime(self):
        """Met à jour avec plus d'informations"""
        now = datetime.now()
        self.date_label.setText(f"📅 {now.strftime('%A %d %B %Y')}")
        self.time_label.setText(f"🕐 {now.strftime('%H:%M:%S')}")

        # Informations supplémentaires
        week_num = now.isocalendar()[1]
        day_of_year = now.timetuple().tm_yday
        self.extra_info.setText(f"📊 Semaine {week_num} • Jour {day_of_year}/365\n🌍 Fuseau: UTC+1 • 🗓️ Trimestre {(now.month-1)//3 + 1}")

class ModuleParcAutoWindow(QWidget):
    """Fenêtre complète module Parc Auto avec tous critères"""

    def __init__(self):
        super().__init__()
        self.setup_parc_auto_window()

    def setup_parc_auto_window(self):
        self.setWindowTitle("🚗 MASTER COMPTA - MODULE PARC AUTO COMPLET")
        self.setGeometry(100, 100, 1400, 900)
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.5 #2a5298, stop:1 #1e3c72);
                color: white;
            }
        """)

        layout = QVBoxLayout(self)

        # Titre module
        title = QLabel("🚗 MODULE PARC AUTO - GESTION COMPLÈTE VÉHICULES")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 22pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4caf50, stop:0.5 #66bb6a, stop:1 #4caf50);
                border: 3px solid #4caf50;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)

        # Zone de critères et filtres
        criteria_frame = QFrame()
        criteria_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
        """)
        criteria_layout = QHBoxLayout(criteria_frame)

        # Critères de recherche
        search_group = QGroupBox("🔍 CRITÈRES DE RECHERCHE")
        search_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #4caf50;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
        """)
        search_layout = QFormLayout(search_group)

        # ComboBox Marque
        self.marque_combo = QComboBox()
        self.marque_combo.addItems(["Toutes", "Peugeot", "Renault", "Citroën", "Ford", "Volkswagen", "BMW", "Mercedes", "Audi"])
        self.marque_combo.setStyleSheet(self.get_combo_style())
        search_layout.addRow("🚗 Marque:", self.marque_combo)

        # ComboBox Statut
        self.statut_combo = QComboBox()
        self.statut_combo.addItems(["Tous", "Actif", "Maintenance", "Vendu", "En panne", "Réservé"])
        self.statut_combo.setStyleSheet(self.get_combo_style())
        search_layout.addRow("📊 Statut:", self.statut_combo)

        # ComboBox Carburant
        self.carburant_combo = QComboBox()
        self.carburant_combo.addItems(["Tous", "Essence", "Diesel", "Hybride", "Électrique", "GPL"])
        self.carburant_combo.setStyleSheet(self.get_combo_style())
        search_layout.addRow("⛽ Carburant:", self.carburant_combo)

        # Année
        self.annee_combo = QComboBox()
        self.annee_combo.addItems(["Toutes"] + [str(year) for year in range(2024, 2010, -1)])
        self.annee_combo.setStyleSheet(self.get_combo_style())
        search_layout.addRow("📅 Année:", self.annee_combo)

        criteria_layout.addWidget(search_group)

        # Filtres avancés
        filters_group = QGroupBox("🎯 FILTRES AVANCÉS")
        filters_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #2196f3;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
        """)
        filters_layout = QFormLayout(filters_group)

        # Kilométrage
        km_layout = QHBoxLayout()
        self.km_min = QSpinBox()
        self.km_min.setRange(0, 500000)
        self.km_min.setSuffix(" km")
        self.km_min.setStyleSheet(self.get_spinbox_style())
        self.km_max = QSpinBox()
        self.km_max.setRange(0, 500000)
        self.km_max.setValue(200000)
        self.km_max.setSuffix(" km")
        self.km_max.setStyleSheet(self.get_spinbox_style())
        km_layout.addWidget(self.km_min)
        km_layout.addWidget(QLabel("à"))
        km_layout.addWidget(self.km_max)
        filters_layout.addRow("📏 Kilométrage:", km_layout)

        # Coût mensuel
        cout_layout = QHBoxLayout()
        self.cout_min = QSpinBox()
        self.cout_min.setRange(0, 2000)
        self.cout_min.setSuffix(" €")
        self.cout_min.setStyleSheet(self.get_spinbox_style())
        self.cout_max = QSpinBox()
        self.cout_max.setRange(0, 2000)
        self.cout_max.setValue(1000)
        self.cout_max.setSuffix(" €")
        self.cout_max.setStyleSheet(self.get_spinbox_style())
        cout_layout.addWidget(self.cout_min)
        cout_layout.addWidget(QLabel("à"))
        cout_layout.addWidget(self.cout_max)
        filters_layout.addRow("💰 Coût/mois:", cout_layout)

        # Assurance
        self.assurance_combo = QComboBox()
        self.assurance_combo.addItems(["Toutes", "Tous risques", "Tiers", "Tiers+", "Temporaire"])
        self.assurance_combo.setStyleSheet(self.get_combo_style())
        filters_layout.addRow("🛡️ Assurance:", self.assurance_combo)

        criteria_layout.addWidget(filters_group)

        # Actions rapides
        actions_group = QGroupBox("⚡ ACTIONS RAPIDES")
        actions_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14pt;
                font-weight: bold;
                border: 2px solid #ff9800;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
        """)
        actions_layout = QVBoxLayout(actions_group)

        # Boutons d'action redimensionnés
        action_buttons = [
            ("🔍 RECHERCHER", "#2196f3", self.rechercher_vehicules),
            ("🔄 ACTUALISER", "#4caf50", self.actualiser_donnees),
            ("📊 STATISTIQUES", "#ff9800", self.afficher_statistiques),
            ("📋 EXPORTER", "#9c27b0", self.exporter_donnees)
        ]

        for text, color, callback in action_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: {color};
                    color: white;
                    font-weight: bold;
                    font-size: 11pt;
                    padding: 10px;
                    border: 2px solid {color};
                    border-radius: 8px;
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    background: rgba(255,255,255,0.2);
                    border: 2px solid white;
                }}
            """)
            actions_layout.addWidget(btn)

        criteria_layout.addWidget(actions_group)
        layout.addWidget(criteria_frame)

        # Tableau des véhicules avec tous les critères
        self.create_vehicles_table(layout)

        # Boutons de gestion redimensionnés
        self.create_management_buttons(layout)

    def get_combo_style(self):
        return """
            QComboBox {
                background: rgba(255,255,255,0.1);
                border: 2px solid #4caf50;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
                min-width: 150px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
            QComboBox QAbstractItemView {
                background: #2a5298;
                color: white;
                border: 2px solid #4caf50;
                selection-background-color: #4caf50;
            }
        """

    def get_spinbox_style(self):
        return """
            QSpinBox {
                background: rgba(255,255,255,0.1);
                border: 2px solid #2196f3;
                border-radius: 6px;
                padding: 8px;
                color: white;
                font-size: 11pt;
                min-width: 100px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background: #2196f3;
                border: none;
                width: 20px;
            }
        """

    def create_vehicles_table(self, layout):
        """Crée le tableau des véhicules avec tous les critères"""
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
        """)
        table_layout = QVBoxLayout(table_frame)

        # Titre tableau
        table_title = QLabel("📊 LISTE COMPLÈTE DES VÉHICULES")
        table_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        table_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(76,175,80,0.6);
                border: 2px solid #4caf50;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        table_layout.addWidget(table_title)

        # Tableau avec tous les critères
        self.vehicles_table = QTableWidget(12, 15)  # Plus de lignes et colonnes
        headers = [
            'Code', 'Marque', 'Modèle', 'Immatriculation', 'Année', 'Kilométrage',
            'Carburant', 'Statut', 'Coût/mois', 'Assurance', 'Dernière Révision',
            'Prochaine Révision', 'Conducteur', 'Observations', 'Actions'
        ]
        self.vehicles_table.setHorizontalHeaderLabels(headers)

        # Style du tableau
        self.vehicles_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #546e7a;
                background: rgba(255,255,255,0.1);
                border: 2px solid #4caf50;
                border-radius: 10px;
                color: white;
                font-size: 11pt;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #388e3c);
                border: 1px solid #2e7d32;
                padding: 12px;
                font-weight: bold;
                color: white;
                font-size: 12pt;
            }
            QTableWidget::item {
                padding: 10px;
                border: 1px solid #546e7a;
                color: white;
            }
            QTableWidget::item:selected {
                background: rgba(76,175,80,0.5);
            }
        """)

        # Données complètes des véhicules
        vehicles_data = [
            ['V001', 'Peugeot', '308', 'AB-123-CD', '2020', '45000', 'Essence', 'Actif', '450€', 'Tous risques', '15/10/2024', '15/04/2025', 'Martin Jean', 'Révision prévue', '🔧'],
            ['V002', 'Renault', 'Clio', 'EF-456-GH', '2019', '62000', 'Diesel', 'Actif', '380€', 'Tiers+', '20/09/2024', '20/03/2025', 'Durand Marie', 'Bon état', '🔧'],
            ['V003', 'Citroën', 'C3', 'IJ-789-KL', '2021', '28000', 'Essence', 'Actif', '420€', 'Tous risques', '05/11/2024', '05/05/2025', 'Leblanc Pierre', 'Garantie', '🔧'],
            ['V004', 'Ford', 'Focus', 'MN-012-OP', '2018', '78000', 'Diesel', 'Maintenance', '520€', 'Tous risques', '10/08/2024', 'En cours', 'Garage Central', 'Réparation moteur', '🔧'],
            ['V005', 'Volkswagen', 'Golf', 'QR-345-ST', '2022', '15000', 'Hybride', 'Actif', '580€', 'Tous risques', '01/12/2024', '01/06/2025', 'Moreau Sophie', 'Véhicule récent', '🔧'],
            ['V006', 'BMW', 'Série 3', 'UV-678-WX', '2020', '55000', 'Diesel', 'Actif', '750€', 'Tous risques', '25/10/2024', '25/04/2025', 'Direction', 'Véhicule direction', '🔧'],
            ['V007', 'Mercedes', 'Classe A', 'YZ-901-AB', '2021', '32000', 'Essence', 'Actif', '680€', 'Tous risques', '12/11/2024', '12/05/2025', 'Bernard Luc', 'Excellent état', '🔧'],
            ['V008', 'Audi', 'A3', 'CD-234-EF', '2019', '68000', 'Diesel', 'Vendu', '0€', 'Résiliée', '15/09/2024', 'N/A', 'N/A', 'Vendu le 15/11/2024', '❌'],
            ['V009', 'Toyota', 'Yaris', 'GH-567-IJ', '2023', '8000', 'Hybride', 'Actif', '520€', 'Tous risques', '30/11/2024', '30/05/2025', 'Petit Anne', 'Véhicule neuf', '🔧'],
            ['V010', 'Nissan', 'Micra', 'KL-890-MN', '2020', '41000', 'Essence', 'Réservé', '400€', 'Tiers+', '18/10/2024', '18/04/2025', 'Réservé', 'En attente affectation', '🔧'],
            ['V011', 'Opel', 'Corsa', 'OP-123-QR', '2017', '89000', 'Diesel', 'En panne', '350€', 'Tiers', '05/07/2024', 'Suspendue', 'Garage', 'Problème transmission', '⚠️'],
            ['V012', 'Fiat', '500', 'ST-456-UV', '2021', '25000', 'Essence', 'Actif', '390€', 'Tous risques', '22/11/2024', '22/05/2025', 'Roux Thomas', 'Parfait état', '🔧']
        ]

        for row, row_data in enumerate(vehicles_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))

                # Couleurs selon critères
                if col == 7:  # Colonne Statut
                    if value == 'Actif':
                        item.setBackground(QColor('#4caf50'))  # Vert
                    elif value == 'Maintenance':
                        item.setBackground(QColor('#ff9800'))  # Orange
                    elif value == 'Vendu':
                        item.setBackground(QColor('#f44336'))  # Rouge
                    elif value == 'Réservé':
                        item.setBackground(QColor('#2196f3'))  # Bleu
                    elif value == 'En panne':
                        item.setBackground(QColor('#9c27b0'))  # Violet
                elif col == 8:  # Colonne Coût
                    if '€' in value and value != '0€':
                        item.setBackground(QColor('#00bcd4'))  # Cyan
                elif col == 9:  # Colonne Assurance
                    if value == 'Tous risques':
                        item.setBackground(QColor('#4caf50'))  # Vert
                    elif value == 'Tiers+':
                        item.setBackground(QColor('#ff9800'))  # Orange
                    elif value == 'Tiers':
                        item.setBackground(QColor('#f44336'))  # Rouge

                self.vehicles_table.setItem(row, col, item)

        self.vehicles_table.resizeColumnsToContents()
        table_layout.addWidget(self.vehicles_table)
        layout.addWidget(table_frame)

    def create_management_buttons(self, layout):
        """Crée les boutons de gestion redimensionnés"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                margin: 5px;
                padding: 15px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)

        # Boutons redimensionnés selon appellations
        management_buttons = [
            ("➕ AJOUTER NOUVEAU VÉHICULE", "#4caf50", 250, self.ajouter_vehicule),
            ("✏️ MODIFIER VÉHICULE SÉLECTIONNÉ", "#2196f3", 280, self.modifier_vehicule),
            ("🗑️ SUPPRIMER VÉHICULE", "#f44336", 200, self.supprimer_vehicule),
            ("📊 GÉNÉRER RAPPORT COMPLET", "#ff9800", 260, self.generer_rapport),
            ("📤 EXPORTER DONNÉES EXCEL", "#9c27b0", 240, self.exporter_excel),
            ("🖨️ IMPRIMER LISTE", "#607d8b", 180, self.imprimer_liste)
        ]

        for text, color, width, callback in management_buttons:
            btn = QPushButton(text)
            btn.setMinimumWidth(width)
            btn.setMinimumHeight(50)
            btn.clicked.connect(callback)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:1 rgba(0,0,0,0.3));
                    color: white;
                    font-weight: bold;
                    font-size: 12pt;
                    padding: 15px;
                    border: 3px solid {color};
                    border-radius: 12px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.3), stop:1 {color});
                    border: 3px solid white;
                    transform: scale(1.05);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0,0,0,0.5), stop:1 {color});
                }}
            """)
            buttons_layout.addWidget(btn)

        layout.addWidget(buttons_frame)

    # Méthodes d'action pour les critères
    def rechercher_vehicules(self):
        """Recherche selon critères sélectionnés"""
        marque = self.marque_combo.currentText()
        statut = self.statut_combo.currentText()
        carburant = self.carburant_combo.currentText()
        annee = self.annee_combo.currentText()

        # Logique de filtrage
        print(f"Recherche: Marque={marque}, Statut={statut}, Carburant={carburant}, Année={annee}")
        # Ici on filtrerait le tableau selon les critères

    def actualiser_donnees(self):
        """Actualise les données du tableau"""
        print("Actualisation des données...")
        # Ici on rechargerait les données depuis la base

    def afficher_statistiques(self):
        """Affiche les statistiques du parc"""
        print("Affichage des statistiques...")
        # Ici on afficherait une fenêtre de statistiques

    def exporter_donnees(self):
        """Exporte les données filtrées"""
        print("Export des données...")
        # Ici on exporterait vers Excel/CSV

    # Méthodes d'action pour la gestion
    def ajouter_vehicule(self):
        """Ouvre la fenêtre d'ajout de véhicule"""
        print("Ouverture fenêtre ajout véhicule...")
        # Ici on ouvrirait un formulaire d'ajout

    def modifier_vehicule(self):
        """Modifie le véhicule sélectionné"""
        print("Modification véhicule sélectionné...")
        # Ici on ouvrirait un formulaire de modification

    def supprimer_vehicule(self):
        """Supprime le véhicule sélectionné"""
        print("Suppression véhicule...")
        # Ici on demanderait confirmation puis suppression

    def generer_rapport(self):
        """Génère un rapport complet"""
        print("Génération rapport complet...")
        # Ici on générerait un rapport PDF

    def exporter_excel(self):
        """Exporte vers Excel"""
        print("Export Excel...")
        # Ici on exporterait vers Excel

    def imprimer_liste(self):
        """Imprime la liste"""
        print("Impression liste...")
        # Ici on lancerait l'impression

class MasterComptaInterfaceComplete(QMainWindow):
    """Interface MASTER COMPTA complète sans référence Office"""

    def __init__(self):
        super().__init__()
        self.setup_master_compta_interface()

    def setup_master_compta_interface(self):
        """Configure l'interface MASTER COMPTA complète"""
        self.setWindowTitle("🏢 MASTER COMPTA GÉNÉRAL - Interface Complète Révolutionnaire")

        # Taille et position
        screen = QApplication.primaryScreen().geometry()
        width = min(1900, int(screen.width() * 0.98))
        height = min(1300, int(screen.height() * 0.98))
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)

        # Style principal BLEU (sans référence Office)
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:0.5 #2a5298, stop:1 #1e3c72);
                color: white;
            }
            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196f3, stop:1 #1976d2);
                border-bottom: 3px solid #0d47a1;
                padding: 10px;
                font-size: 14pt;
                font-weight: bold;
                color: white;
            }
            QMenuBar::item {
                background: transparent;
                padding: 10px 20px;
                border-radius: 8px;
                color: white;
                margin: 2px;
            }
            QMenuBar::item:selected {
                background: rgba(255,255,255,0.3);
                color: white;
            }
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #0d47a1);
                border-top: 3px solid #2196f3;
                color: white;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
            }
        """)

        # Menu MASTER COMPTA (sans référence Office)
        self.create_master_compta_menubar()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Zone supérieure avec widgets agrandis
        self.create_enhanced_widgets_area(main_layout)

        # Zone centrale avec modules MASTER COMPTA
        self.create_master_compta_modules_area(main_layout)

        # Zone inférieure avec outils redimensionnés
        self.create_enhanced_tools_area(main_layout)

        # Barre de statut MASTER COMPTA
        self.create_master_compta_statusbar()

    def create_master_compta_menubar(self):
        """Crée le menu MASTER COMPTA (sans référence Office)"""
        menubar = self.menuBar()

        # Menu Fichier MASTER COMPTA
        file_menu = menubar.addMenu("📁 FICHIERS")
        file_menu.addAction("🆕 Nouveau Dossier", self.nouveau_dossier)
        file_menu.addAction("📂 Ouvrir Dossier", self.ouvrir_dossier)
        file_menu.addAction("💾 Enregistrer Données", self.enregistrer_donnees)
        file_menu.addAction("💾 Enregistrer Sous", self.enregistrer_sous)
        file_menu.addSeparator()
        file_menu.addAction("📊 Importer Données", self.importer_donnees)
        file_menu.addAction("📤 Exporter Données", self.exporter_donnees)
        file_menu.addSeparator()
        file_menu.addAction("🖨️ Imprimer Rapports", self.imprimer_rapports)
        file_menu.addAction("❌ Quitter MASTER COMPTA", self.close)

        # Menu Modules MASTER COMPTA
        modules_menu = menubar.addMenu("🏢 MODULES MASTER COMPTA")
        modules_menu.addAction("🚗 Parc Auto Complet", self.ouvrir_parc_auto_complet)
        modules_menu.addAction("🏢 Immobilier Complet", self.ouvrir_immobilier_complet)
        modules_menu.addAction("👥 Personnel Complet", self.ouvrir_personnel_complet)
        modules_menu.addAction("📊 Comptabilité Générale", self.ouvrir_comptabilite_generale)
        modules_menu.addAction("💰 Finances et Trésorerie", self.ouvrir_finances_tresorerie)
        modules_menu.addAction("📋 Inventaire et Stock", self.ouvrir_inventaire_stock)
        modules_menu.addAction("🏭 Production", self.ouvrir_production)
        modules_menu.addAction("🛒 Achats et Fournisseurs", self.ouvrir_achats_fournisseurs)
        modules_menu.addAction("💼 Ventes et Clients", self.ouvrir_ventes_clients)
        modules_menu.addAction("📈 Contrôle de Gestion", self.ouvrir_controle_gestion)
        modules_menu.addAction("🎯 Projets", self.ouvrir_projets)

        # Menu Outils MASTER COMPTA
        tools_menu = menubar.addMenu("🔧 OUTILS MASTER COMPTA")
        tools_menu.addAction("🗃️ Bases de Données", self.ouvrir_bases_donnees)
        tools_menu.addAction("🤖 IA Ollama Intégrée", self.ouvrir_ia_ollama)
        tools_menu.addAction("🔍 Détection Anomalies", self.detecter_anomalies)
        tools_menu.addAction("💾 Sauvegarde Automatique", self.configurer_sauvegarde)
        tools_menu.addAction("📊 Générateur Rapports", self.generateur_rapports)
        tools_menu.addAction("🔄 Synchronisation", self.synchronisation_donnees)
        tools_menu.addAction("⚡ Optimisation Performance", self.optimiser_performance)

        # Menu Paramètres MASTER COMPTA
        settings_menu = menubar.addMenu("⚙️ PARAMÈTRES MASTER COMPTA")
        settings_menu.addAction("🎨 Interface Utilisateur", self.configurer_interface)
        settings_menu.addAction("🌍 Langues (FR/AR/EN)", self.configurer_langues)
        settings_menu.addAction("🔔 Notifications et Alertes", self.configurer_notifications)
        settings_menu.addAction("💾 Sauvegardes et Backups", self.configurer_backups)
        settings_menu.addAction("🔒 Sécurité et Accès", self.configurer_securite)
        settings_menu.addAction("🏢 Paramètres Société", self.configurer_societe)
        settings_menu.addAction("📊 Préférences Rapports", self.configurer_rapports)

        # Menu Aide MASTER COMPTA
        help_menu = menubar.addMenu("❓ AIDE MASTER COMPTA")
        help_menu.addAction("📖 Documentation Complète", self.afficher_documentation)
        help_menu.addAction("🎥 Tutoriels Vidéo", self.afficher_tutoriels)
        help_menu.addAction("🆘 Support Technique", self.contacter_support)
        help_menu.addAction("🔄 Mises à Jour", self.verifier_mises_a_jour)
        help_menu.addAction("ℹ️ À Propos MASTER COMPTA", self.afficher_a_propos)

    def create_enhanced_widgets_area(self, layout):
        """Crée la zone supérieure avec widgets agrandis"""
        widgets_frame = QFrame()
        widgets_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 3px solid rgba(255,255,255,0.4);
                border-radius: 20px;
                margin: 10px;
                padding: 20px;
            }
        """)

        widgets_layout = QHBoxLayout(widgets_frame)
        widgets_layout.setSpacing(20)

        # Widgets agrandis avec plus de contenu
        self.enhanced_datetime_widget = EnhancedDateTimeWidget()
        self.enhanced_datetime_widget.setMinimumWidth(300)
        widgets_layout.addWidget(self.enhanced_datetime_widget)

        self.enhanced_prayer_widget = EnhancedPrayerWidget()
        self.enhanced_prayer_widget.setMinimumWidth(350)
        widgets_layout.addWidget(self.enhanced_prayer_widget)

        self.enhanced_weather_widget = EnhancedWeatherWidget()
        self.enhanced_weather_widget.setMinimumWidth(400)
        widgets_layout.addWidget(self.enhanced_weather_widget)

        # Widget connexions agrandi
        self.create_enhanced_connections_widget(widgets_layout)

        # Widget mémos agrandi
        self.create_enhanced_memos_widget(widgets_layout)

        layout.addWidget(widgets_frame)

    def create_enhanced_connections_widget(self, layout):
        """Crée le widget connexions agrandi"""
        connections_widget = QWidget()
        connections_widget.setMinimumWidth(250)
        connections_layout = QVBoxLayout(connections_widget)
        connections_layout.setContentsMargins(10, 10, 10, 10)

        # Titre connexions agrandi
        title = QLabel("📡 CONNEXIONS RÉSEAU")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(156,39,176,0.8);
                border: 3px solid #9c27b0;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        connections_layout.addWidget(title)

        # Statuts détaillés
        self.wifi_status = QLabel("📶 WiFi: 🟢 Excellent\n📊 Vitesse: 150 Mbps\n📡 Signal: -45 dBm\n🌐 IP: *************")
        self.wifi_status.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                background: rgba(76,175,80,0.6);
                border: 2px solid #4caf50;
                border-radius: 10px;
                padding: 12px;
                margin: 5px;
                min-height: 80px;
            }
        """)
        connections_layout.addWidget(self.wifi_status)

        self.cellular_status = QLabel("📱 Cellulaire: 🟢 4G+\n📊 Signal: 85%\n📡 Opérateur: Orange\n🌐 Débit: 50 Mbps")
        self.cellular_status.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 11pt;
                background: rgba(33,150,243,0.6);
                border: 2px solid #2196f3;
                border-radius: 10px;
                padding: 12px;
                margin: 5px;
                min-height: 80px;
            }
        """)
        connections_layout.addWidget(self.cellular_status)

        layout.addWidget(connections_widget)

    def create_enhanced_memos_widget(self, layout):
        """Crée le widget mémos agrandi"""
        memos_widget = QWidget()
        memos_widget.setMinimumWidth(300)
        memos_layout = QVBoxLayout(memos_widget)
        memos_layout.setContentsMargins(10, 10, 10, 10)

        # Titre mémos agrandi
        title = QLabel("📝 MÉMOS ET RAPPELS")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16pt;
                font-weight: bold;
                background: rgba(233,30,99,0.8);
                border: 3px solid #e91e63;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        memos_layout.addWidget(title)

        # Zone de saisie agrandie
        self.memo_input = QLineEdit()
        self.memo_input.setPlaceholderText("Ajouter un nouveau mémo...")
        self.memo_input.setStyleSheet("""
            QLineEdit {
                color: white;
                background: rgba(233,30,99,0.6);
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
                font-size: 12pt;
                min-height: 20px;
            }
        """)
        memos_layout.addWidget(self.memo_input)

        # Mémos existants agrandis
        memos = [
            "📋 Réunion équipe lundi 14h",
            "📞 Appeler client Martin urgent",
            "💾 Sauvegarde mensuelle vendredi",
            "📊 Rapport trimestriel à envoyer",
            "🔧 Maintenance serveur weekend",
            "💰 Factures à valider avant 15h"
        ]

        for memo in memos:
            memo_label = QLabel(memo)
            memo_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 11pt;
                    background: rgba(233,30,99,0.4);
                    border: 2px solid #e91e63;
                    border-radius: 6px;
                    padding: 8px;
                    margin: 2px;
                }
            """)
            memos_layout.addWidget(memo_label)

        layout.addWidget(memos_widget)

    def create_master_compta_modules_area(self, layout):
        """Crée la zone centrale avec tous les modules MASTER COMPTA"""
        modules_frame = QFrame()
        modules_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 3px solid rgba(255,255,255,0.4);
                border-radius: 20px;
                margin: 10px;
                padding: 20px;
            }
        """)

        modules_layout = QVBoxLayout(modules_frame)

        # Titre modules MASTER COMPTA
        modules_title = QLabel("🏢 MODULES MASTER COMPTA GÉNÉRAL COMPLETS")
        modules_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        modules_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 28pt;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b35, stop:0.5 #f7931e, stop:1 #ff6b35);
                border: 4px solid #ff6b35;
                border-radius: 20px;
                padding: 25px;
                margin: 15px;
            }
        """)
        modules_layout.addWidget(modules_title)

        # Grille des modules 4x3 (12 modules)
        modules_grid = QGridLayout()
        modules_grid.setSpacing(25)

        # Tous les modules MASTER COMPTA avec boutons redimensionnés
        modules = [
            ("🚗", "PARC AUTO", "Gestion complète véhicules", 280, self.ouvrir_parc_auto_complet),
            ("🏢", "IMMOBILIER", "Gestion biens et locataires", 280, self.ouvrir_immobilier_complet),
            ("👥", "PERSONNEL", "Gestion employés et paie", 280, self.ouvrir_personnel_complet),
            ("📊", "COMPTABILITÉ", "Comptabilité générale", 280, self.ouvrir_comptabilite_generale),
            ("💰", "FINANCES", "Trésorerie et budgets", 280, self.ouvrir_finances_tresorerie),
            ("📋", "INVENTAIRE", "Stock et articles", 280, self.ouvrir_inventaire_stock),
            ("🏭", "PRODUCTION", "Gestion production", 280, self.ouvrir_production),
            ("🛒", "ACHATS", "Fournisseurs et commandes", 280, self.ouvrir_achats_fournisseurs),
            ("💼", "VENTES", "Clients et facturation", 280, self.ouvrir_ventes_clients),
            ("📈", "CONTRÔLE", "Contrôle de gestion", 280, self.ouvrir_controle_gestion),
            ("🎯", "PROJETS", "Gestion de projets", 280, self.ouvrir_projets),
            ("🗃️", "BASES", "Système bases données", 280, self.ouvrir_bases_donnees)
        ]

        for i, (icon, title, desc, width, callback) in enumerate(modules):
            row = i // 4
            col = i % 4

            module_btn = QPushButton()
            module_btn.setMinimumSize(width, 140)
            module_btn.clicked.connect(callback)

            # Couleurs différentes pour chaque module
            colors = [
                "#4caf50", "#2196f3", "#ff9800", "#9c27b0",
                "#f44336", "#00bcd4", "#795548", "#607d8b",
                "#3f51b5", "#e91e63", "#ff5722", "#009688"
            ]
            color = colors[i % len(colors)]

            module_btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:0.5 rgba(255,255,255,0.3), stop:1 {color});
                    color: white;
                    border: 4px solid {color};
                    border-radius: 20px;
                    font-size: 16pt;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255,255,255,0.4), stop:0.5 {color}, stop:1 rgba(255,255,255,0.4));
                    border: 4px solid white;
                    transform: scale(1.08);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(0,0,0,0.4), stop:0.5 {color}, stop:1 rgba(0,0,0,0.4));
                }}
            """)

            module_btn.setText(f"{icon}\n{title}\n{desc}")

            modules_grid.addWidget(module_btn, row, col)

        modules_layout.addLayout(modules_grid)
        layout.addWidget(modules_frame)

    def create_enhanced_tools_area(self, layout):
        """Crée la zone inférieure avec outils redimensionnés"""
        tools_frame = QFrame()
        tools_frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 3px solid rgba(255,255,255,0.4);
                border-radius: 20px;
                margin: 10px;
                padding: 20px;
            }
        """)

        tools_layout = QHBoxLayout(tools_frame)
        tools_layout.setSpacing(20)

        # Outils redimensionnés selon appellations
        tools = [
            ("📋", "COPIER DONNÉES", 180, self.copier_donnees),
            ("📄", "COLLER DONNÉES", 180, self.coller_donnees),
            ("✂️", "COUPER DONNÉES", 180, self.couper_donnees),
            ("↩️", "ANNULER ACTION", 180, self.annuler_action),
            ("↪️", "RÉTABLIR ACTION", 180, self.retablir_action),
            ("🔍", "RECHERCHER GLOBAL", 200, self.rechercher_global),
            ("💾", "SAUVEGARDER TOUT", 200, self.sauvegarder_tout),
            ("🖨️", "IMPRIMER RAPPORTS", 200, self.imprimer_rapports),
            ("🔄", "SYNCHRONISER", 180, self.synchroniser_donnees),
            ("⚡", "OPTIMISER", 150, self.optimiser_systeme)
        ]

        for icon, text, width, callback in tools:
            tool_btn = QPushButton(f"{icon}\n{text}")
            tool_btn.setMinimumSize(width, 80)
            tool_btn.clicked.connect(callback)
            tool_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #37474f, stop:1 #263238);
                    color: white;
                    border: 3px solid #546e7a;
                    border-radius: 15px;
                    font-size: 12pt;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #546e7a, stop:1 #37474f);
                    border: 3px solid #90a4ae;
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #263238, stop:1 #37474f);
                }
            """)
            tools_layout.addWidget(tool_btn)

        layout.addWidget(tools_frame)

    def create_master_compta_statusbar(self):
        """Crée la barre de statut MASTER COMPTA"""
        statusbar = self.statusBar()

        # Message principal MASTER COMPTA
        statusbar.showMessage("🏢 MASTER COMPTA GÉNÉRAL - Interface Complète Révolutionnaire • Tous modules fonctionnels • Widgets agrandis • Critères détaillés")

        # Widgets permanents
        # Heure actuelle
        self.status_time = QLabel()
        self.status_time.setStyleSheet("color: white; font-weight: bold; padding: 8px; font-size: 12pt;")
        statusbar.addPermanentWidget(self.status_time)

        # Statut système
        self.status_system = QLabel("🟢 Système Optimal")
        self.status_system.setStyleSheet("color: #4caf50; font-weight: bold; padding: 8px; font-size: 12pt;")
        statusbar.addPermanentWidget(self.status_system)

        # Mode MASTER COMPTA
        self.status_mode = QLabel("MASTER COMPTA GÉNÉRAL")
        self.status_mode.setStyleSheet("color: #ff9800; font-weight: bold; padding: 8px; font-size: 12pt;")
        statusbar.addPermanentWidget(self.status_mode)

        # Timer pour mise à jour
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_statusbar)
        self.status_timer.start(1000)

    def update_statusbar(self):
        """Met à jour la barre de statut"""
        now = datetime.now()
        self.status_time.setText(f"🕐 {now.strftime('%H:%M:%S')}")

    # Méthodes d'ouverture des modules complets
    def ouvrir_parc_auto_complet(self):
        """Ouvre le module Parc Auto complet"""
        self.parc_auto_window = ModuleParcAutoWindow()
        self.parc_auto_window.show()

    def ouvrir_immobilier_complet(self):
        """Ouvre le module Immobilier complet"""
        print("🏢 Ouverture module Immobilier complet...")
        # Ici on ouvrirait la fenêtre Immobilier complète

    def ouvrir_personnel_complet(self):
        """Ouvre le module Personnel complet"""
        print("👥 Ouverture module Personnel complet...")
        # Ici on ouvrirait la fenêtre Personnel complète

    def ouvrir_comptabilite_generale(self):
        """Ouvre le module Comptabilité générale"""
        print("📊 Ouverture module Comptabilité générale...")
        # Ici on ouvrirait la fenêtre Comptabilité

    def ouvrir_finances_tresorerie(self):
        """Ouvre le module Finances et Trésorerie"""
        print("💰 Ouverture module Finances et Trésorerie...")
        # Ici on ouvrirait la fenêtre Finances

    def ouvrir_inventaire_stock(self):
        """Ouvre le module Inventaire et Stock"""
        print("📋 Ouverture module Inventaire et Stock...")
        # Ici on ouvrirait la fenêtre Inventaire

    def ouvrir_production(self):
        """Ouvre le module Production"""
        print("🏭 Ouverture module Production...")
        # Ici on ouvrirait la fenêtre Production

    def ouvrir_achats_fournisseurs(self):
        """Ouvre le module Achats et Fournisseurs"""
        print("🛒 Ouverture module Achats et Fournisseurs...")
        # Ici on ouvrirait la fenêtre Achats

    def ouvrir_ventes_clients(self):
        """Ouvre le module Ventes et Clients"""
        print("💼 Ouverture module Ventes et Clients...")
        # Ici on ouvrirait la fenêtre Ventes

    def ouvrir_controle_gestion(self):
        """Ouvre le module Contrôle de Gestion"""
        print("📈 Ouverture module Contrôle de Gestion...")
        # Ici on ouvrirait la fenêtre Contrôle

    def ouvrir_projets(self):
        """Ouvre le module Projets"""
        print("🎯 Ouverture module Projets...")
        # Ici on ouvrirait la fenêtre Projets

    def ouvrir_bases_donnees(self):
        """Ouvre le système de bases de données"""
        print("🗃️ Ouverture système bases de données...")
        # Ici on ouvrirait le système de BDD

    def ouvrir_ia_ollama(self):
        """Ouvre l'IA Ollama intégrée"""
        print("🤖 Ouverture IA Ollama intégrée...")
        # Ici on ouvrirait l'interface IA

    # Méthodes des menus (stubs pour l'instant)
    def nouveau_dossier(self): print("🆕 Nouveau dossier")
    def ouvrir_dossier(self): print("📂 Ouvrir dossier")
    def enregistrer_donnees(self): print("💾 Enregistrer données")
    def enregistrer_sous(self): print("💾 Enregistrer sous")
    def importer_donnees(self): print("📊 Importer données")
    def exporter_donnees(self): print("📤 Exporter données")
    def imprimer_rapports(self): print("🖨️ Imprimer rapports")
    def detecter_anomalies(self): print("🔍 Détecter anomalies")
    def configurer_sauvegarde(self): print("💾 Configurer sauvegarde")
    def generateur_rapports(self): print("📊 Générateur rapports")
    def synchronisation_donnees(self): print("🔄 Synchronisation")
    def optimiser_performance(self): print("⚡ Optimiser performance")
    def configurer_interface(self): print("🎨 Configurer interface")
    def configurer_langues(self): print("🌍 Configurer langues")
    def configurer_notifications(self): print("🔔 Configurer notifications")
    def configurer_backups(self): print("💾 Configurer backups")
    def configurer_securite(self): print("🔒 Configurer sécurité")
    def configurer_societe(self): print("🏢 Configurer société")
    def configurer_rapports(self): print("📊 Configurer rapports")
    def afficher_documentation(self): print("📖 Documentation")
    def afficher_tutoriels(self): print("🎥 Tutoriels")
    def contacter_support(self): print("🆘 Support")
    def verifier_mises_a_jour(self): print("🔄 Mises à jour")
    def afficher_a_propos(self): print("ℹ️ À propos MASTER COMPTA")

    # Méthodes des outils
    def copier_donnees(self): print("📋 Copier données")
    def coller_donnees(self): print("📄 Coller données")
    def couper_donnees(self): print("✂️ Couper données")
    def annuler_action(self): print("↩️ Annuler action")
    def retablir_action(self): print("↪️ Rétablir action")
    def rechercher_global(self): print("🔍 Rechercher global")
    def sauvegarder_tout(self): print("💾 Sauvegarder tout")
    def synchroniser_donnees(self): print("🔄 Synchroniser")
    def optimiser_systeme(self): print("⚡ Optimiser système")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Configuration MASTER COMPTA
    app.setApplicationName("MASTER COMPTA GÉNÉRAL")
    app.setApplicationVersion("Interface Complète Révolutionnaire")
    app.setOrganizationName("MASTER COMPTA")

    window = MasterComptaInterfaceComplete()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()