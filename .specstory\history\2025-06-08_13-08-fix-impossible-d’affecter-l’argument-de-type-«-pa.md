<!-- Generated by SpecStory -->

# /fix Impossible d’affecter l’argument de type « Pa (2025-06-08 14:08:51)

_**User**_

/fix Impossible d’affecter l’argument de type « Path » au paramètre « file_path » de type « str » dans la fonction « __init__ »
  « Path » n’est pas assignable à « str »

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Le problème vient du fait que `sql_file` est un objet `Path`, alors que le constructeur de `SQLQuery` attend un `str`. Il suffit de convertir `sql_file` en chaîne de caractères avec `str(sql_file)`.

````python
    def _load_queries(self):
        for sql_file in self.sql_folder.glob('*.SQL'):
            query = SQLQuery(str(sql_file))  # Correction ici
            self.queries[sql_file.stem.upper()] = query
# ...existing code...
````

---

