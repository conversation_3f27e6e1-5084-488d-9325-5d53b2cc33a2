#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 SCRIPT DE CRÉATION EXE POUR ROBOT AUTONOME
Crée un fichier .exe installable pour Windows
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def installer_pyinstaller():
    """Installe PyInstaller pour créer l'EXE"""
    print("📦 Installation de PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installé avec succès !")
        return True
    except subprocess.CalledProcessError:
        print("❌ Erreur lors de l'installation de PyInstaller")
        return False

def creer_fichier_spec():
    """Crée le fichier .spec pour PyInstaller"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['robot_autonome_pc.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'json',
        'datetime',
        'threading',
        'time',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RobotAutonomePC',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='robot_icon.ico'
)
'''
    
    with open('robot_autonome.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec créé !")

def creer_icone_robot():
    """Crée un fichier icône pour le robot"""
    # Création d'un fichier icône simple (simulation)
    icone_content = """
🤖 ICÔNE ROBOT AUTONOME
(Fichier icône simulé - remplacez par une vraie icône .ico)
    """
    
    with open('robot_icon.txt', 'w', encoding='utf-8') as f:
        f.write(icone_content)
    
    print("✅ Icône robot créée (remplacez robot_icon.ico par une vraie icône)")

def creer_installateur():
    """Crée le script d'installation"""
    installateur_content = '''
@echo off
echo 🤖 INSTALLATION ROBOT AUTONOME PC
echo ================================

echo 📦 Création du dossier d'installation...
if not exist "C:\\RobotAutonomePC" mkdir "C:\\RobotAutonomePC"

echo 📋 Copie des fichiers...
copy "RobotAutonomePC.exe" "C:\\RobotAutonomePC\\"
copy "README.txt" "C:\\RobotAutonomePC\\"

echo 🔗 Création du raccourci bureau...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\Robot Autonome PC.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\RobotAutonomePC\\RobotAutonomePC.exe" >> CreateShortcut.vbs
echo oLink.Description = "Robot IA Autonome pour PC" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo 📋 Création entrée menu démarrer...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot Autonome PC" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot Autonome PC"
copy "%USERPROFILE%\\Desktop\\Robot Autonome PC.lnk" "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot Autonome PC\\"

echo ✅ INSTALLATION TERMINÉE !
echo 🚀 Robot Autonome PC installé avec succès !
echo 📍 Emplacement: C:\\RobotAutonomePC\\
echo 🖥️ Raccourci bureau créé
echo 📋 Entrée menu démarrer ajoutée

echo.
echo 🤖 LANCEMENT DU ROBOT...
start "" "C:\\RobotAutonomePC\\RobotAutonomePC.exe"

pause
    '''
    
    with open('installer_robot.bat', 'w', encoding='utf-8') as f:
        f.write(installateur_content)
    
    print("✅ Script d'installation créé !")

def creer_readme():
    """Crée le fichier README"""
    readme_content = '''
🤖 ROBOT AUTONOME PC - README
============================

🚀 BIENVENUE DANS VOTRE ROBOT IA AUTONOME !

📋 DESCRIPTION:
Robot IA ultra-intelligent pour votre PC avec capacités:
• Communication vocale bidirectionnelle
• Surveillance et optimisation automatique
• Apprentissage continu et auto-amélioration
• Gestion intelligente des fichiers
• Automatisation des tâches
• Interface intuitive et moderne

⚙️ CONFIGURATION REQUISE:
• Windows 10/11 (64-bit)
• 4 GB RAM minimum (8 GB recommandé)
• 500 MB espace disque libre
• Microphone pour communication vocale
• Haut-parleurs pour réponses vocales

🚀 INSTALLATION:
1. Exécutez installer_robot.bat en tant qu'administrateur
2. Suivez les instructions à l'écran
3. Le robot sera installé dans C:\\RobotAutonomePC\\
4. Un raccourci sera créé sur le bureau

💬 UTILISATION:
• Lancez le robot depuis le raccourci bureau
• Parlez ou écrivez vos commandes
• Le robot comprend le langage naturel
• Explorez les différents onglets pour toutes les fonctions

🎯 COMMANDES EXEMPLES:
• "Analyser mon PC"
• "Optimiser les performances"
• "Surveiller le système"
• "Générer un rapport"
• "Automatiser les tâches"

🤖 SUPPORT:
Votre Robot IA Autonome s'améliore constamment !
Il apprend de vos habitudes et optimise ses performances.

🚀 PROFITEZ DE VOTRE ASSISTANT IA PERSONNEL !
    '''
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ README créé !")

def creer_desinstallateur():
    """Crée le script de désinstallation"""
    desinstallateur_content = '''
@echo off
echo 🗑️ DÉSINSTALLATION ROBOT AUTONOME PC
echo ===================================

echo ⚠️ Êtes-vous sûr de vouloir désinstaller Robot Autonome PC ?
pause

echo 🔄 Arrêt du robot...
taskkill /f /im "RobotAutonomePC.exe" 2>nul

echo 🗑️ Suppression des fichiers...
if exist "C:\\RobotAutonomePC" rmdir /s /q "C:\\RobotAutonomePC"

echo 🔗 Suppression des raccourcis...
if exist "%USERPROFILE%\\Desktop\\Robot Autonome PC.lnk" del "%USERPROFILE%\\Desktop\\Robot Autonome PC.lnk"
if exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot Autonome PC" rmdir /s /q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Robot Autonome PC"

echo ✅ DÉSINSTALLATION TERMINÉE !
echo 🤖 Robot Autonome PC a été supprimé de votre système.

pause
    '''
    
    with open('uninstall_robot.bat', 'w', encoding='utf-8') as f:
        f.write(desinstallateur_content)
    
    print("✅ Script de désinstallation créé !")

def compiler_exe():
    """Compile le robot en fichier EXE"""
    print("🔨 Compilation en cours...")
    
    try:
        # Compilation avec PyInstaller
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed", 
            "--name=RobotAutonomePC",
            "--distpath=dist",
            "--workpath=build",
            "robot_autonome_pc.py"
        ]
        
        subprocess.check_call(cmd)
        print("✅ Compilation réussie !")
        
        # Copie de l'EXE dans le dossier principal
        if os.path.exists("dist/RobotAutonomePC.exe"):
            shutil.copy("dist/RobotAutonomePC.exe", ".")
            print("✅ EXE copié dans le dossier principal !")
            return True
        else:
            print("❌ Fichier EXE non trouvé")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur de compilation: {e}")
        return False

def creer_package_installation():
    """Crée le package d'installation complet"""
    print("📦 Création du package d'installation...")
    
    # Création du dossier de distribution
    dist_folder = "RobotAutonomePC_Installation"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    os.makedirs(dist_folder)
    
    # Copie des fichiers nécessaires
    files_to_copy = [
        "RobotAutonomePC.exe",
        "installer_robot.bat", 
        "uninstall_robot.bat",
        "README.txt"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy(file, dist_folder)
            print(f"✅ {file} copié")
        else:
            print(f"⚠️ {file} non trouvé")
    
    print(f"✅ Package d'installation créé dans {dist_folder}/")

def main():
    """Fonction principale de création EXE"""
    print("🤖 CRÉATION APPLICATION EXE ROBOT AUTONOME")
    print("=" * 50)
    
    # Vérification que le fichier source existe
    if not os.path.exists("robot_autonome_pc.py"):
        print("❌ Fichier robot_autonome_pc.py non trouvé !")
        print("📋 Assurez-vous que le fichier source est présent.")
        return
    
    # Étapes de création
    steps = [
        ("📦 Installation PyInstaller", installer_pyinstaller),
        ("📄 Création fichier .spec", creer_fichier_spec),
        ("🎨 Création icône", creer_icone_robot),
        ("📋 Création README", creer_readme),
        ("⚙️ Création installateur", creer_installateur),
        ("🗑️ Création désinstallateur", creer_desinstallateur),
        ("🔨 Compilation EXE", compiler_exe),
        ("📦 Création package", creer_package_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            result = step_func()
            if result is False:
                print(f"❌ Échec: {step_name}")
                break
        except Exception as e:
            print(f"❌ Erreur {step_name}: {e}")
            break
    
    print("\n" + "=" * 50)
    print("🚀 CRÉATION EXE TERMINÉE !")
    print("📁 Fichiers créés:")
    print("   • RobotAutonomePC.exe (Application principale)")
    print("   • installer_robot.bat (Script d'installation)")
    print("   • uninstall_robot.bat (Script de désinstallation)")
    print("   • README.txt (Documentation)")
    print("   • RobotAutonomePC_Installation/ (Package complet)")
    print("\n🎯 INSTRUCTIONS:")
    print("1. Distribuez le dossier RobotAutonomePC_Installation/")
    print("2. L'utilisateur exécute installer_robot.bat")
    print("3. Le robot sera installé et prêt à utiliser !")
    print("\n🤖 VOTRE ROBOT AUTONOME EST PRÊT POUR INSTALLATION !")

if __name__ == "__main__":
    main()
