#!/usr/bin/env python3
"""
Script de vérification de l'état de GestImmob
"""

import os
import sqlite3
from pathlib import Path
from datetime import datetime

def verifier_gestimmob():
    """Vérifie l'état de GestImmob"""
    print("🔍 Vérification de l'état de GestImmob...")
    print("=" * 50)
    
    # Vérifier les fichiers
    fichiers_requis = [
        "gestimmob_simple.py",
        "lancer_gestimmob.bat",
        "config_advanced.json",
        "requirements_minimal.txt"
    ]
    
    print("📁 Fichiers principaux:")
    for fichier in fichiers_requis:
        if Path(fichier).exists():
            taille = Path(fichier).stat().st_size
            print(f"  ✅ {fichier} ({taille:,} bytes)")
        else:
            print(f"  [ECHEC] {fichier} - MANQUANT")
    
    # Vérifier la base de données
    print("\n💾 Base de données:")
    db_path = "gestimmob.db"
    if Path(db_path).exists():
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Vérifier les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"  ✅ Base de données créée avec {len(tables)} tables")
            
            # Vérifier les données
            cursor.execute("SELECT COUNT(*) FROM immobilisations")
            nb_biens = cursor.fetchone()[0]
            print(f"  [STATS] {nb_biens} bien(s) enregistré(s)")
            
            cursor.execute("SELECT COUNT(*) FROM users")
            nb_users = cursor.fetchone()[0]
            print(f"  👥 {nb_users} utilisateur(s) configuré(s)")
            
            conn.close()
            
        except Exception as e:
            print(f"  [ECHEC] Erreur base de données: {e}")
    else:
        print(f"  [ATTENTION] Base de données non créée (sera créée au premier lancement)")
    
    # Vérifier les modules Python
    print("\n🐍 Modules Python:")
    modules_requis = [
        ("PySide6", "Interface graphique"),
        ("sqlite3", "Base de données"),
        ("cryptography", "Sécurité"),
        ("xlsxwriter", "Export Excel")
    ]
    
    for module, description in modules_requis:
        try:
            __import__(module)
            print(f"  ✅ {module} - {description}")
        except ImportError:
            print(f"  [ECHEC] {module} - MANQUANT ({description})")
    
    # Vérifier les processus
    print("\n🔄 État de l'application:")
    try:
        import psutil
        gestimmob_running = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('gestimmob_simple.py' in cmd for cmd in proc.info['cmdline']):
                    print(f"  ✅ GestImmob en cours d'exécution (PID: {proc.info['pid']})")
                    gestimmob_running = True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if not gestimmob_running:
            print("  [ATTENTION] GestImmob n'est pas en cours d'exécution")
            
    except ImportError:
        print("  ℹ️ Module psutil non disponible (vérification des processus impossible)")
    
    # Résumé
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ:")
    print("  • Application: GestImmob v2.0.0")
    print("  • Type: Logiciel de gestion immobilière")
    print("  • Interface: PySide6 (moderne)")
    print("  • Base de données: SQLite")
    print("  • Sécurité: Chiffrement intégré")
    print("  • Export: Excel, CSV, PDF")
    
    print("\n[LANCE] COMMANDES DE LANCEMENT:")
    print("  • Lancement simple: python gestimmob_simple.py")
    print("  • Lancement Windows: lancer_gestimmob.bat")
    print("  • Tests complets: python run_tests.py")
    
    print("\n✅ GestImmob est prêt à l'utilisation !")
    
    return True

if __name__ == "__main__":
    verifier_gestimmob()
