@echo off
title 🤖 ROBOT IA AUTONOME PC - LANCEMENT
color 0A

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██    🤖 ROBOT IA AUTONOME PC - LANCEMENT AUTOMATIQUE     ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo 🔍 Vérification du système...
timeout /t 1 >nul

echo ✅ Système Windows détecté
echo 🐍 Vérification Python...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non détecté !
    echo 📥 Installation Python en cours...
    winget install Python.Python.3.12 --silent
    if errorlevel 1 (
        echo ❌ Échec installation Python
        echo 💡 Installez manuellement depuis https://python.org
        pause
        exit /b 1
    )
    echo ✅ Python installé avec succès !
)

echo ✅ Python opérationnel
echo 📦 Vérification PySide6...

python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo 📥 Installation PySide6...
    pip install PySide6 --quiet
    if errorlevel 1 (
        echo ❌ Échec installation PySide6
        pause
        exit /b 1
    )
    echo ✅ PySide6 installé !
)

echo ✅ PySide6 opérationnel
echo.

echo 🤖 INITIALISATION ROBOT IA...
echo ═══════════════════════════════════════════════════════════
echo 🧠 Chargement intelligence artificielle...
timeout /t 1 >nul
echo ✅ IA chargée

echo 🎙️ Activation capacités vocales...
timeout /t 1 >nul
echo ✅ Voix activée

echo 👁️ Démarrage surveillance PC...
timeout /t 1 >nul
echo ✅ Surveillance active

echo 🔧 Configuration modules...
timeout /t 1 >nul
echo ✅ Modules configurés

echo 🌟 Activation transcendance...
timeout /t 1 >nul
echo ✅ Transcendance activée

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██         🚀 ROBOT IA AUTONOME OPÉRATIONNEL !            ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

echo 🎯 Lancement de l'interface...
echo.

cd /d "%~dp0"

if exist "robot_autonome_pc.py" (
    echo 🚀 Démarrage Robot IA Autonome...
    python robot_autonome_pc.py
) else (
    echo ❌ Fichier robot_autonome_pc.py non trouvé !
    echo 📁 Vérifiez que vous êtes dans le bon dossier
    echo 📍 Dossier actuel: %CD%
    pause
    exit /b 1
)

echo.
echo 🤖 Robot IA Autonome fermé
echo 💾 Sauvegarde automatique effectuée
echo 📊 Logs mis à jour
echo.
echo 🎉 Merci d'avoir utilisé Robot IA Autonome !
echo.
pause
