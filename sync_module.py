#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de synchronisation multi-langages pour GestImmob
Synchronise les données entre Python, JavaScript, PowerShell et SQL
"""

import json
import sqlite3
import subprocess
import threading
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

class MultiLanguageSync:
    """Gestionnaire de synchronisation multi-langages"""

    def __init__(self, db_path: str = "gestimmob.db"):
        self.db_path = Path(db_path)
        self.sync_config = {
            "python_modules": [],
            "javascript_files": [],
            "powershell_scripts": [],
            "sql_scripts": [],
            "last_sync": None,
            "sync_interval": 30  # secondes
        }
        self.is_syncing = False
        self.sync_thread = None

    def start_sync(self):
        """Démarre la synchronisation automatique"""
        if not self.is_syncing:
            self.is_syncing = True
            self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
            self.sync_thread.start()
            logger.info("Synchronisation démarrée")

    def stop_sync(self):
        """Arrête la synchronisation"""
        self.is_syncing = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        logger.info("Synchronisation arrêtée")

    def _sync_loop(self):
        """Boucle de synchronisation"""
        while self.is_syncing:
            try:
                self.sync_all()
                time.sleep(self.sync_config["sync_interval"])
            except Exception as e:
                logger.error(f"Erreur synchronisation: {e}")
                time.sleep(5)

    def sync_all(self):
        """Synchronise tous les composants"""
        logger.debug("Synchronisation en cours...")

        # Synchroniser Python
        self._sync_python_modules()

        # Synchroniser JavaScript
        self._sync_javascript()

        # Synchroniser PowerShell
        self._sync_powershell()

        # Synchroniser SQL
        self._sync_sql()

        # Mettre à jour la configuration
        self.sync_config["last_sync"] = time.time()
        self._save_sync_config()

    def _sync_python_modules(self):
        """Synchronise les modules Python"""
        try:
            # Vérifier les modules Python
            python_files = list(Path.cwd().glob("*.py"))
            for py_file in python_files:
                if py_file.name not in self.sync_config["python_modules"]:
                    self.sync_config["python_modules"].append(py_file.name)
                    logger.debug(f"Module Python ajouté: {py_file.name}")
        except Exception as e:
            logger.error(f"Erreur sync Python: {e}")

    def _sync_javascript(self):
        """Synchronise les fichiers JavaScript"""
        try:
            # Créer/mettre à jour les fichiers JavaScript si nécessaire
            js_dir = Path("web") / "js"
            js_dir.mkdir(parents=True, exist_ok=True)

            # Fichier de synchronisation JavaScript
            sync_js = js_dir / "sync.js"
            if not sync_js.exists():
                self._create_sync_js(sync_js)
        except Exception as e:
            logger.error(f"Erreur sync JavaScript: {e}")

    def _sync_powershell(self):
        """Synchronise les scripts PowerShell"""
        try:
            # Créer/mettre à jour les scripts PowerShell
            ps_dir = Path("scripts") / "powershell"
            ps_dir.mkdir(parents=True, exist_ok=True)

            sync_ps = ps_dir / "sync.ps1"
            if not sync_ps.exists():
                self._create_sync_ps(sync_ps)
        except Exception as e:
            logger.error(f"Erreur sync PowerShell: {e}")

    def _sync_sql(self):
        """Synchronise les scripts SQL"""
        try:
            # Vérifier la base de données
            if self.db_path.exists():
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    # Créer la table de synchronisation si elle n'existe pas
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS sync_log (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                            component TEXT NOT NULL,
                            action TEXT NOT NULL,
                            status TEXT NOT NULL,
                            details TEXT
                        )
                    """)

                    # Enregistrer la synchronisation
                    cursor.execute("""
                        INSERT INTO sync_log (component, action, status, details)
                        VALUES (?, ?, ?, ?)
                    """, ("multi_language_sync", "sync_all", "success", json.dumps(self.sync_config)))

                    conn.commit()
        except Exception as e:
            logger.error(f"Erreur sync SQL: {e}")

    def _create_sync_js(self, file_path: Path):
        """Crée le fichier de synchronisation JavaScript"""
        js_content = """
// Synchronisation JavaScript pour GestImmob
class GestImmobSync {
    constructor() {
        this.syncInterval = 30000; // 30 secondes
        this.isRunning = false;
    }

    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.syncLoop();
            console.log('Synchronisation JavaScript démarrée');
        }
    }

    stop() {
        this.isRunning = false;
        console.log('Synchronisation JavaScript arrêtée');
    }

    async syncLoop() {
        while (this.isRunning) {
            try {
                await this.syncData();
                await this.sleep(this.syncInterval);
            } catch (error) {
                console.error('Erreur synchronisation:', error);
                await this.sleep(5000);
            }
        }
    }

    async syncData() {
        // Synchroniser avec l'API Python
        try {
            const response = await fetch('/api/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    timestamp: Date.now(),
                    component: 'javascript'
                })
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Synchronisation réussie:', data);
            }
        } catch (error) {
            console.error('Erreur API sync:', error);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialiser la synchronisation
const gestImmobSync = new GestImmobSync();
gestImmobSync.start();
"""

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(js_content)

        logger.info(f"Fichier JavaScript créé: {file_path}")

    def _create_sync_ps(self, file_path: Path):
        """Crée le script de synchronisation PowerShell"""
        ps_content = """# Synchronisation PowerShell pour GestImmob
# Surveille les fichiers et synchronise avec Python

param(
    [int]$SyncInterval = 30,
    [string]$ProjectPath = ".",
    [switch]$Verbose
)

function Write-SyncLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "sync.log" -Value $logMessage
}

function Start-GestImmobSync {
    Write-SyncLog "Démarrage de la synchronisation PowerShell"

    $watcher = New-Object System.IO.FileSystemWatcher
    $watcher.Path = $ProjectPath
    $watcher.Filter = "*.py"
    $watcher.IncludeSubdirectories = $true
    $watcher.EnableRaisingEvents = $true

    # Événement de modification de fichier
    $action = {
        $path = $Event.SourceEventArgs.FullPath
        $changeType = $Event.SourceEventArgs.ChangeType
        Write-SyncLog "Fichier modifié: $path ($changeType)"

        # Appeler le script Python de synchronisation
        try {
            python -c "from sync_module import MultiLanguageSync; sync = MultiLanguageSync(); sync.sync_all()"
            Write-SyncLog "Synchronisation Python déclenchée avec succès"
        } catch {
            Write-SyncLog "Erreur lors de la synchronisation Python: $_" "ERROR"
        }
    }

    Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
    Register-ObjectEvent -InputObject $watcher -EventName "Created" -Action $action
    Register-ObjectEvent -InputObject $watcher -EventName "Deleted" -Action $action

    Write-SyncLog "Surveillance des fichiers activée"

    # Boucle de synchronisation périodique
    while ($true) {
        Start-Sleep -Seconds $SyncInterval
        Write-SyncLog "Synchronisation périodique"

        try {
            python -c "from sync_module import MultiLanguageSync; sync = MultiLanguageSync(); sync.sync_all()"
        } catch {
            Write-SyncLog "Erreur synchronisation périodique: $_" "ERROR"
        }
    }
}

# Démarrer la synchronisation
Start-GestImmobSync
"""

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(ps_content)

        logger.info(f"Script PowerShell créé: {file_path}")

    def _save_sync_config(self):
        """Sauvegarde la configuration de synchronisation"""
        config_file = Path("sync_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.sync_config, f, indent=2, ensure_ascii=False)

# Instance globale
sync_manager = MultiLanguageSync()
