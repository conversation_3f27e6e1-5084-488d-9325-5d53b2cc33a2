#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vérification exhaustive de TOUS les codes Python du projet
"""

import ast
import os
import sys
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Any

def analyser_fichier_python(file_path: str) -> Dict[str, Any]:
    """Analyse complète d'un fichier Python"""
    print(f"\n{'='*80}")
    print(f"ANALYSE COMPLÈTE: {file_path}")
    print(f"{'='*80}")
    
    if not os.path.exists(file_path):
        return {
            "path": file_path,
            "exists": False,
            "errors": ["Fichier non trouvé"],
            "valid": False
        }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 Fichier: {len(content)} caractères, {len(content.splitlines())} lignes")
        
        result = {
            "path": file_path,
            "exists": True,
            "size": len(content),
            "lines": len(content.splitlines()),
            "errors": [],
            "warnings": [],
            "imports": [],
            "classes": [],
            "functions": [],
            "valid": True
        }
        
        lines = content.splitlines()
        
        # Test 1: Syntaxe AST
        print(f"\n🔍 TEST AST:")
        try:
            tree = ast.parse(content, filename=file_path)
            print(f"✅ AST: OK")
            
            # Extraire les informations du code
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        result["imports"].append(f"import {alias.name}")
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        result["imports"].append(f"from {module} import {alias.name}")
                elif isinstance(node, ast.ClassDef):
                    result["classes"].append(node.name)
                elif isinstance(node, ast.FunctionDef):
                    result["functions"].append(node.name)
            
        except SyntaxError as e:
            error = f"ERREUR AST ligne {e.lineno}: {e.msg}"
            print(f"❌ {error}")
            result["errors"].append(error)
            result["valid"] = False
            
            # Contexte de l'erreur
            if e.lineno and e.lineno <= len(lines):
                print(f"\n📍 CONTEXTE:")
                start = max(0, e.lineno - 3)
                end = min(len(lines), e.lineno + 3)
                for i in range(start, end):
                    marker = " >>> " if i == e.lineno - 1 else "     "
                    print(f"{marker}{i+1:4d}: {lines[i]}")
        
        # Test 2: Compilation
        print(f"\n🔍 TEST COMPILATION:")
        try:
            compile(content, file_path, 'exec')
            print(f"✅ Compilation: OK")
        except SyntaxError as e:
            error = f"ERREUR COMPILATION ligne {e.lineno}: {e.msg}"
            print(f"❌ {error}")
            result["errors"].append(error)
            result["valid"] = False
        
        # Test 3: Problèmes spécifiques
        print(f"\n🔍 ANALYSE SPÉCIFIQUE:")
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Problème: dict[...] au lieu de Dict[...]
            if 'dict[' in line and 'Dict[' not in line and not line_stripped.startswith('#'):
                warning = f"Ligne {i}: 'dict[...]' devrait être 'Dict[...]'"
                result["warnings"].append(warning)
                print(f"⚠️ {warning}")
            
            # Problème: list[...] au lieu de List[...]
            if 'list[' in line and 'List[' not in line and not line_stripped.startswith('#'):
                warning = f"Ligne {i}: 'list[...]' devrait être 'List[...]'"
                result["warnings"].append(warning)
                print(f"⚠️ {warning}")
            
            # Problème: any au lieu de Any
            if ': any =' in line.lower() and not line_stripped.startswith('#'):
                warning = f"Ligne {i}: 'any' devrait être 'Any'"
                result["warnings"].append(warning)
                print(f"⚠️ {warning}")
            
            # Problème: Imports manquants
            if ('Dict[' in content or 'List[' in content or 'Any' in content) and 'from typing import' not in content:
                if not any('Import typing manquant' in w for w in result["warnings"]):
                    warning = "Import typing manquant"
                    result["warnings"].append(warning)
                    print(f"⚠️ {warning}")
        
        # Statistiques
        print(f"\n📊 STATISTIQUES:")
        print(f"   Imports: {len(result['imports'])}")
        print(f"   Classes: {len(result['classes'])}")
        print(f"   Fonctions: {len(result['functions'])}")
        print(f"   Erreurs: {len(result['errors'])}")
        print(f"   Avertissements: {len(result['warnings'])}")
        
        if result["errors"]:
            print(f"\n❌ FICHIER AVEC ERREURS: {len(result['errors'])} erreur(s)")
            result["valid"] = False
        elif result["warnings"]:
            print(f"\n⚠️ FICHIER AVEC AVERTISSEMENTS: {len(result['warnings'])} avertissement(s)")
        else:
            print(f"\n✅ FICHIER PARFAIT")
        
        return result
        
    except Exception as e:
        error = f"ERREUR INATTENDUE: {type(e).__name__}: {e}"
        print(f"❌ {error}")
        return {
            "path": file_path,
            "exists": True,
            "errors": [error],
            "valid": False
        }

def trouver_tous_fichiers_python() -> List[str]:
    """Trouve tous les fichiers Python du projet"""
    fichiers_python = []
    
    # Fichiers dans le répertoire racine
    for file in Path('.').glob('*.py'):
        fichiers_python.append(str(file))
    
    # Fichiers dans src/
    for file in Path('src').rglob('*.py'):
        fichiers_python.append(str(file))
    
    # Fichiers dans modules/
    if Path('modules').exists():
        for file in Path('modules').rglob('*.py'):
            fichiers_python.append(str(file))
    
    return sorted(fichiers_python)

def main():
    """Vérification exhaustive de tous les codes"""
    print("🔍 VÉRIFICATION EXHAUSTIVE DE TOUS LES CODES PYTHON")
    print("=" * 80)
    print(f"Python: {sys.version}")
    print(f"Répertoire: {os.getcwd()}")
    
    # Trouver tous les fichiers Python
    fichiers_python = trouver_tous_fichiers_python()
    
    print(f"\n📁 FICHIERS PYTHON TROUVÉS: {len(fichiers_python)}")
    for fichier in fichiers_python[:10]:  # Afficher les 10 premiers
        print(f"   - {fichier}")
    if len(fichiers_python) > 10:
        print(f"   ... et {len(fichiers_python) - 10} autres")
    
    # Analyser chaque fichier
    resultats = []
    
    for fichier in fichiers_python:
        resultat = analyser_fichier_python(fichier)
        resultats.append(resultat)
    
    # Résumé final
    print(f"\n{'='*80}")
    print("📊 RÉSUMÉ FINAL EXHAUSTIF")
    print(f"{'='*80}")
    
    fichiers_valides = sum(1 for r in resultats if r.get("valid", False))
    fichiers_avec_erreurs = sum(1 for r in resultats if r.get("errors") and r.get("valid", True) == False)
    fichiers_avec_warnings = sum(1 for r in resultats if r.get("warnings") and r.get("valid", True) == True)
    
    total_erreurs = sum(len(r.get("errors", [])) for r in resultats)
    total_warnings = sum(len(r.get("warnings", [])) for r in resultats)
    
    print(f"📁 Total fichiers analysés: {len(resultats)}")
    print(f"✅ Fichiers parfaits: {fichiers_valides}")
    print(f"❌ Fichiers avec erreurs: {fichiers_avec_erreurs}")
    print(f"⚠️ Fichiers avec avertissements: {fichiers_avec_warnings}")
    print(f"🚨 Total erreurs: {total_erreurs}")
    print(f"⚠️ Total avertissements: {total_warnings}")
    
    # Détail des problèmes
    if total_erreurs > 0:
        print(f"\n🚨 FICHIERS AVEC ERREURS:")
        for resultat in resultats:
            if resultat.get("errors"):
                print(f"\n❌ {resultat['path']}:")
                for error in resultat["errors"][:3]:  # Limiter à 3 erreurs
                    print(f"   - {error}")
                if len(resultat["errors"]) > 3:
                    print(f"   ... et {len(resultat['errors']) - 3} autres erreurs")
    
    if total_warnings > 0:
        print(f"\n⚠️ FICHIERS AVEC AVERTISSEMENTS:")
        for resultat in resultats:
            if resultat.get("warnings") and resultat.get("valid", True):
                print(f"\n⚠️ {resultat['path']}:")
                for warning in resultat["warnings"][:2]:  # Limiter à 2 avertissements
                    print(f"   - {warning}")
                if len(resultat["warnings"]) > 2:
                    print(f"   ... et {len(resultat['warnings']) - 2} autres avertissements")
    
    # Statistiques globales
    total_imports = sum(len(r.get("imports", [])) for r in resultats)
    total_classes = sum(len(r.get("classes", [])) for r in resultats)
    total_functions = sum(len(r.get("functions", [])) for r in resultats)
    
    print(f"\n📊 STATISTIQUES GLOBALES:")
    print(f"   Total imports: {total_imports}")
    print(f"   Total classes: {total_classes}")
    print(f"   Total fonctions: {total_functions}")
    
    # Conclusion
    if total_erreurs == 0:
        print(f"\n🎉 TOUS LES FICHIERS PYTHON SONT SYNTAXIQUEMENT CORRECTS!")
        if total_warnings > 0:
            print(f"⚠️ {total_warnings} avertissement(s) à corriger pour une qualité parfaite")
        else:
            print(f"✅ QUALITÉ PARFAITE - Aucun problème détecté")
        return True
    else:
        print(f"\n🚨 {fichiers_avec_erreurs} FICHIER(S) AVEC DES ERREURS CRITIQUES")
        print(f"❌ {total_erreurs} erreur(s) empêchent l'exécution")
        print(f"🔧 Corrections nécessaires avant le lancement")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nRésultat final: {'SUCCÈS COMPLET' if success else 'CORRECTIONS NÉCESSAIRES'}")
    sys.exit(0 if success else 1)
