{"name": "robot-ia-dev-stable", "displayName": "🛠️ Robot IA Développement", "description": "Robot IA Développement Ultra-Stable - Interface Complète", "version": "3.0.0", "publisher": "Sam<PERSON>ord", "engines": {"vscode": "^1.60.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Debuggers", "Programming Languages"], "keywords": ["ai", "robot", "development", "coding", "assistant", "stable"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": [{"command": "robotIA.openDevPanel", "title": "🛠️ Panneau Développement", "category": "Robot IA"}, {"command": "robotIA.codeReview", "title": "🔍 Review Code", "category": "Robot IA"}, {"command": "robotIA.generateCode", "title": "⚡ Générer Code", "category": "Robot IA"}, {"command": "robotIA.debugAssist", "title": "🐛 Assistant Debug", "category": "Robot IA"}, {"command": "robotIA.refactorCode", "title": "🔧 Refactoriser", "category": "Robot IA"}, {"command": "robotIA.addComments", "title": "📝 Ajouter Commentaires", "category": "Robot IA"}, {"command": "robotIA.createTests", "title": "🧪 C<PERSON>er Tests", "category": "Robot IA"}, {"command": "robotIA.projectAnalysis", "title": "📊 Analyser Projet", "category": "Robot IA"}], "keybindings": [{"command": "robotIA.openDevPanel", "key": "ctrl+shift+d"}, {"command": "robotIA.codeReview", "key": "ctrl+alt+r"}, {"command": "robotIA.generateCode", "key": "ctrl+alt+g"}], "menus": {"editor/context": [{"submenu": "robotIA.contextMenu", "when": "editorTextFocus", "group": "robotIA@1"}], "robotIA.contextMenu": [{"command": "robotIA.codeReview", "when": "editorHasSelection"}, {"command": "robotIA.refactorCode", "when": "editorHasSelection"}, {"command": "robotIA.addComments", "when": "editorHasSelection"}, {"command": "robotIA.createTests", "when": "editorHasSelection"}]}, "submenus": [{"id": "robotIA.contextMenu", "label": "🤖 Robot IA"}], "viewsContainers": {"activitybar": [{"id": "robotIA", "title": "🛠️ Robot IA Dev", "icon": "$(tools)"}]}, "views": {"robotIA": [{"id": "robotIA.devPanel", "name": "💻 Développement", "type": "webview"}]}}}