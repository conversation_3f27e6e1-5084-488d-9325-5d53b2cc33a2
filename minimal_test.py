#!/usr/bin/env python3
"""Test minimal pour identifier les problèmes exacts"""

print("=== TEST MINIMAL PYTHON ===")

# Test 1: Import ast
try:
    import ast
    print("✅ Import ast: OK")
except Exception as e:
    print(f"❌ Import ast: {e}")
    exit(1)

# Test 2: Lecture main.py
try:
    with open('src/main.py', 'r', encoding='utf-8') as f:
        main_content = f.read()
    print("✅ Lecture main.py: OK")
except Exception as e:
    print(f"❌ Lecture main.py: {e}")
    exit(1)

# Test 3: AST main.py
try:
    ast.parse(main_content)
    print("✅ AST main.py: OK")
except SyntaxError as e:
    print(f"❌ AST main.py: Erreur syntaxe ligne {e.lineno}: {e.msg}")
    if e.text:
        print(f"   Code: {e.text.strip()}")
    exit(1)
except Exception as e:
    print(f"❌ AST main.py: {e}")
    exit(1)

# Test 4: Compilation main.py
try:
    compile(main_content, 'main.py', 'exec')
    print("✅ Compilation main.py: OK")
except SyntaxError as e:
    print(f"❌ Compilation main.py: Erreur syntaxe ligne {e.lineno}: {e.msg}")
    exit(1)
except Exception as e:
    print(f"❌ Compilation main.py: {e}")
    exit(1)

# Test 5: Lecture user_management.py
try:
    with open('src/auth/user_management.py', 'r', encoding='utf-8') as f:
        user_content = f.read()
    print("✅ Lecture user_management.py: OK")
except Exception as e:
    print(f"❌ Lecture user_management.py: {e}")
    exit(1)

# Test 6: AST user_management.py
try:
    ast.parse(user_content)
    print("✅ AST user_management.py: OK")
except SyntaxError as e:
    print(f"❌ AST user_management.py: Erreur syntaxe ligne {e.lineno}: {e.msg}")
    if e.text:
        print(f"   Code: {e.text.strip()}")
    exit(1)
except Exception as e:
    print(f"❌ AST user_management.py: {e}")
    exit(1)

# Test 7: Compilation user_management.py
try:
    compile(user_content, 'user_management.py', 'exec')
    print("✅ Compilation user_management.py: OK")
except SyntaxError as e:
    print(f"❌ Compilation user_management.py: Erreur syntaxe ligne {e.lineno}: {e.msg}")
    exit(1)
except Exception as e:
    print(f"❌ Compilation user_management.py: {e}")
    exit(1)

print("\n🎉 TOUS LES TESTS SONT RÉUSSIS !")
print("✅ Aucun problème de syntaxe détecté")
print("✅ Tous les fichiers compilent correctement")
