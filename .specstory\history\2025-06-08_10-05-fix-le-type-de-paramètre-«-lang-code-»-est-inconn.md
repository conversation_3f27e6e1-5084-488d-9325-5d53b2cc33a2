<!-- Generated by SpecStory -->

# /fix Le type de paramètre « lang_code » est inconn (2025-06-08 11:05:08)

_**User**_

/fix Le type de paramètre « lang_code » est inconnu, L'annotation de type est manquante pour le paramètre "lang_code"

---

_**Assistant**_

### [calculatrice.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/calculatrice.py)

Ajouter une annotation de type explicite pour le paramètre `lang_code` dans la méthode `set_language`.

````python
# ...existing code...
    def set_language(self, lang_code: str):
        """
        Changer la langue de l'interface (ex : 'fr', 'en', 'ar', etc.)
        """
        # Peut être enrichi avec des fichiers de traduction ou des modèles IA multilingues
        if lang_code == "en":
            self.setWindowTitle("AI Multifunction Calculator")
        elif lang_code == "ar":
            self.setWindowTitle("حاسبة الذكاء الاصطناعي متعددة الوظائف")
        else:
            self.setWindowTitle("Calculatrice IA Multifonction")
# ...existing code...
````

---

