<!-- Generated by SpecStory -->

# /fix « text » n’est pas un attribut connu de « Non (2025-06-08 11:42:39)

_**User**_

/fix « text » n’est pas un attribut connu de « None »

---

_**Assistant**_

### [inventaire.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/inventaire.py)

Corriger l'accès à `.text()` sur un objet potentiellement `None` dans l'export CSV, en utilisant une variable temporaire pour éviter d'appeler `.text()` sur `None`.

````python
# ...existing code...
                for row in range(self.table.rowCount()):
                    writer.writerow([
                        self.table.item(row, col).text() if self.table.item(row, col) else ""
                        for col in range(self.table.columnCount())
                    ])
# ...existing code...
````

devient :

````python
# ...existing code...
                for row in range(self.table.rowCount()):
                    writer.writerow([
                        (item.text() if item is not None else "")
                        for item in [self.table.item(row, col) for col in range(self.table.columnCount())]
                    ])
# ...existing code...
````

---

