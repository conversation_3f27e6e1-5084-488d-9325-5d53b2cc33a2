<!-- Generated by SpecStory -->

# /fix L’importation « QTextEdit » n’a pas été accéd (2025-06-08 11:15:24)

_**User**_

/fix L’importation « QTextEdit » n’a pas été accédée

---

_**Assistant**_

### [impression.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/src/impression.py)

Supprimer l'import inutile de `QTextEdit` qui n'est pas utilisé dans le code.

````python
from PySide6.QtWidgets import QWidget, QVBoxLayout, QPushButton, QLabel, QFileDialog, QComboBox, QTableWidget, QTableWidgetItem, QMessageBox, QHBoxLayout, QCheckBox
# ...existing code...
````

---

