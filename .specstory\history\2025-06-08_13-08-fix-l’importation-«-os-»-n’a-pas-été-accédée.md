<!-- Generated by SpecStory -->

# /fix L’importation « os » n’a pas été accédée (2025-06-08 14:08:26)

_**User**_

/fix L’importation « os » n’a pas été accédée

---

_**Assistant**_

### [settings_manager.py](file:///c%3A/Users/<USER>/Desktop/logiciel%20GestImmob/gestion-software/modules/settings_manager.py)

Supprimer l'import inutile de `os` qui n'est pas utilisé dans ce module.

````python
# --- Module SQLQuery/SQLManager simplifié pour gestion rapide des fichiers SQL ---
# import os  # Suppression de l'import inutile
from pathlib import Path
# ...existing code...
````

---

